<!--
 * @Author: nameZ
 * @data: Do not edit
 * @Description:
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-03 17:35:31
 * @FilePath: \qbmbtsweb\index.html
-->
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>跨域管控中心</title>
  <script src="/js/Cesium/Cesium.js"></script>
  <script src="/js/Cesium/Cesium.js"></script>
  <!-- <script src="/public/js/evwebgis-debug.js" type="module"></script> -->
  <script src="/js/Cesium/CesiumEMExtensions.js"></script>
  <script src="/js/vis/moment.min.js" type="text/javascript"></script>
  <!-- <script src="/js/vis/zh-cn.js" type="text/javascript"></script> -->
  <script src="/js/vis/vis-timeline-graph2d.min.js" type="text/javascript"></script>
  <script type="text/javascript" src="/js/vis/vis-network.min.js"></script>

  <link rel="stylesheet" href="/js/vis/vis-timeline-graph2d.min.css" type="text/css" />

  <script type="module">
    import { default as ol } from "/public/js/Cesium/ThirdParty/ol/ol";
    import { fromLonLat } from 'ol/proj';
    import "/public/js/vis/zh-cn.js"
    window.ol = ol;
    window.fromLonLat = fromLonLat
    moment.locale("zh-cn");
    moment.lang("zh-cn");
  </script>
  <!-- <script src="/public/js/Cesium/CesiumEMMPExtensions.js"></script>
  <script src="/public/js/Cesium/CesiumEMBBExtensions.js"></script>
  <script src="/public/js/Cesium/CesiumEMVBExtensions.js"></script>
  <script src="/public/js/Cesium/CesiumEMTSExtensions.js"></script>
  <script src="/public/js/Cesium/CesiumEMTMExtensions.js"></script>
  <script src="/public/js/Cesium/CesiumEMSQExtensions.js"></script>
  <script src="/public/js/Cesium/CesiumEMMTExtensions.js"></script> -->
  <!-- <script src="/public/js/Cesium/CesiumExternal.js"></script>
  <script src="/public/js/Cesium/CesiumVideoFusion.js"></script> -->

  <!-- <script src="/js/Cesium/Cesium.js"></script> -->
  <script src="/js/Cesium/CesiumExternal.js"></script>
  <script src="/js/Cesium/CesiumEMGEExtensions.js"></script>
  <!-- <script src="/js/Cesium/CesiumVideoFusionEditor.js"></script> -->
  <script src="/js/Cesium/CesiumVideoFusion.js"></script>
  <!-- <script src="/js/Cesium/CesiumEMExtensions.js"></script> -->

  <script src="/js/config.js"></script>
  <!-- <link rel="stylesheet" href="/public/css/base.css" /> -->
  <link rel="stylesheet" href="/public/css/base.less" />
  <link rel="stylesheet" href="/public/css/evwebgis-global.css" />
  <script type="text/javascript" src="/js/satellite.min.js"></script>

  <link rel="stylesheet" href="/public/js/Cesium/Widgets/widgets.css" />
  <link rel="stylesheet" href="/public/js/Cesium/ThirdParty/ol/ol.css" />
</head>
<style>
  * {
    padding: 0;
    margin: 0;
    font-size: 14px;
  }

  ::-webkit-scrollbar {
    height: 8px;
    width: 8px;
    background: transparent;
  }
</style>

<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>
