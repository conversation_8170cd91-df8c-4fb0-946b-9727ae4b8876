import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
export default defineConfig({
  pluginOptions: {
    "style-resources-loader": {
      preProcessor: "less",
      patterns: [
        // 这个是加上自己的路径,不能使用(如下:alias)中配置的别名路径
        path.resolve(__dirname, "./src/assets/style/theme/style.less"),
      ],
    },
  },
  plugins: [vue()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"), //别名  需要在tsconfig.json中配置,否则无法正常调用
      // "@assets": path.resolve(__dirname, "src/assets"),
      // "@components": path.resolve(__dirname, "src/components"),
      // "@utils": path.resolve(__dirname, "src/utils"),
      // "@views": path.resolve(__dirname, "src/views"),
      // "@stores": path.resolve(__dirname, "src/stores"),
      // "@styles": path.resolve(__dirname, "src/styles"),
      // "@types": path.resolve(__dirname, "src/types"),
    },
  },
  server: {
    fs: {
      strict: false, //  支持引用除入口目录的文件
    },
    host: "0.0.0.0",
    proxy: {
      "/api": "http://**************:9999", // 反向代理
    },
  },
});
