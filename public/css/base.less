@import "../../src/assets/style/theme/style.less";

:root {
  /* 表单背景颜色 */
  --el-fill-color-blank: transparent !important;
  --el-border-color-lighter: rgb(0, 58, 102) !important;
  --el-color-primary-light-9: rgb(0, 58, 102, 0.4) !important;
  --el-border-color-light: rgb(0, 58, 102) !important;
  --el-border-color-hover: rgb(0, 174, 255) !important;
  --el-border-color-base: rgb(0, 58, 102) !important;
  --el-border-color: rgb(0, 58, 102) !important;
  --el-bg-color-overlay: #021942 !important;
  --el-fill-color-light: rgba(0, 174, 255, 0.4) !important;
  --el-disabled-bg-color: transparent !important;
  /* 表单文字背景颜色 */
  --el-text-color-regular: rgb(0, 0, 0) !important;
  --el-menu-hover-bg-color: transparent !important;
}

@font_color: #d9dfe8d9;
@placeholder_color: #909399;

* {
  color: @font_color;
}

// @buttonColor: var(--buttonColor, rgba(33, 76, 119)); // 按钮色(蓝色)
/*按钮样式*/
.el-button {
  // background-color: rgba(33, 76, 119, 1) !important;
  background-color: rgba(33, 76, 119, 0.1) !important;
  border-color: rgba(33, 76, 119, 1) !important;
  color: #fff !important;
}

.el-button:focus,
.el-button:hover {
  background-color: rgb(45, 102, 160) !important;
  // color: rgba(@fontColor, 1) !important;
}

/*表单外边框样式*/
// .el-date-editor.el-input__wrapper{
//   box-shadow: 0 0 0 1px rgba(@buttonColor,1) inset !important;
// }
.el-tree {
  --el-tree-node-hover-bg-color: #285472 !important;
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #285472 !important;
}

.el-tree-node__content {
  margin-top: 4%;
}

/* 通用表格样式 custom-table start  START  ---------------------------------- */
.el-table {
  .cell {
    /* color: #285472; */
    font-weight: 500;
    color: @font_color !important;
    padding: 0 4px !important;

  }
}

.el-table .el-input__wrapper {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.el-table__header {
  background-color: transparent;

  .cell {
    // color: #285472; 
    // color: @fontColor;
    // color: @fontColor !important;
    font-weight: bolder;
    font-size: 15px;
  }

  .el-table__cell {
    height: 44px;
    // background-color: #f3f6f9 !important;
    background: transparent !important;
  }
}

.custom-table {
  background-color: #031527a6 !important;
  border: 0px #254c74 solid !important;
  border-left: 0px !important;
}

.custom-table tr {
  background-color: transparent !important;
}

.custom-table th.el-table__cell {
  background-color: transparent !important;
  // background-color: rgba(@themeColor, 0.3) !important;
  // color: #111 !important;
  // color: @fontColor !important;
  font-weight: 600;
}

.custom-table td.el-table__cell:not(:first-child),
.custom-table th.el-table__cell.is-leaf:not(:first-child) {
  // border-bottom: none !important;
  border-left: 1px #2b3b47 solid !important;
  // border-bottom: 0px #254c74 solid !important;
  // border-collapse: collapse;

}

.custom-table td.el-table__cell,
.custom-table th.el-table__cell.is-leaf {
  // border-bottom: none !important;
  // border-left: 1px #254c74 solid !important;
  border-bottom: 1px #2b3b47 solid !important;
  // border-collapse: collapse;

}

// .custom-table .el-table__body td.el-table__cell {
//   color: rgba(@fontColor, 1) !important;
// }
.custom-table .el-table__body tr:hover>td.el-table__cell {
  background-color: rgba(@themeColor, 0.3) !important;
}

.custom-table .el-table__body tr.current-row>td.el-table__cell {
  background-color: rgba(@themeColor, 0.5) !important;
}

.custom-table .el-table__inner-wrapper::after,
.custom-table .el-table__inner-wrapper::before {
  display: none !important;
}

.dragTableSty {
  padding: 5px;
  box-sizing: border-box;
  color: #ffffff;
  height: calc(100% - 30px);
  overflow: auto;
}

.dragTableSty table {
  width: 100%;
  font-size: 14px;
  border-collapse: collapse;
}

.dragTableSty table thead {
  color: #0eaffd;
  background-color: rgba(@bgColor, 0.3) !important;
}

.dragTableSty table tbody {
  text-align: center;
}

.dragTableSty table tr {
  line-height: 28px;
}

.dragTableSty2 {
  padding: 8px;
  box-sizing: border-box;
  color: #ffffff;
  height: calc(100% - 40px);
  overflow: auto;
}

.dragTableSty2 table {
  width: 100%;
  font-size: 18px;
  border-collapse: collapse;
}

.dragTableSty2 table thead {
  color: #0eaffd;
  background-color: rgba(@bgColor, 0.3) !important;
}

.dragTableSty2 table tbody {
  text-align: center;
}

.dragTableSty2 table tr {
  line-height: 40px;
}

.custom-table .el-table__body-wrapper tr td.el-table-fixed-column--right {
  background: transparent !important;
}

.el-checkbox__inner {
  border-color: rgba(@themeColor, 1) !important;
  background-color: transparent;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: rgba(@themeColor, 1) !important;
}

.el-checkbox__input.is-checked+.el-checkbox__label {
  // color: rgba(@themeColor, 1) !important;
  color: rgba(@themeColor, 1) !important;
}

.el-checkbox__label {
  color: @font_color;
}

.el-checkbox__input.is-disabled .el-checkbox__inner {
  background-color: rgba(@themeColor, .4) !important;
}

.el-radio__inner {
  border-color: rgba(@themeColor, 1) !important;
}

.el-radio__input.is-checked .el-radio__inner {
  background: rgba(@themeColor, 1) !important;
}

.el-radio__input.is-checked+.el-radio__label {
  color: rgba(@themeColor, 1) !important;
}

.el-radio__input.is-disabled .el-radio__inner {
  background-color: rgba(@themeColor, .4) !important;
}

.inputModule .el-input-group__append {
  background-color: #1b2f8894 !important;
  color: #ffffff;
}

input::-webkit-input-placeholder {
  color: #909399 !important;
}

input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}

input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.el-input.is-disabled .el-input__wrapper {
  box-shadow: 0 0 0 1px rgba(@themeColor, .3) inset !important;
}

.el-textarea {
  --el-input-border-color: rgba(@themeColor, .3) !important;
  --el-input-focus-border-color: rgba(@themeColor, .3) !important;
}

.el-textarea__inner {
  color: @font_color !important;
}

.el-textarea__inner:hover {
  box-shadow: 0 0 0 1px rgba(@themeColor, 1) inset !important;
}

.el-color-picker__mask {
  background-color: rgba(@themeColor, .1) !important;
}

.el-scrollbar {
  --el-scrollbar-bg-color: rgba(@themeColor, 1) !important;
  --el-scrollbar-hover-bg-color: rgba(@themeColor, 1) !important;
}

.el-scrollbar__view {
  height: 100%;
  width: 100%;
}

::-webkit-scrollbar-thumb {
  background: rgba(@themeColor, .8) !important;
}

.demo-color-block .el-color-picker__trigger {
  border: 1px solid rgba(@themeColor, .3) !important;
}

/* 通用表格样式 custom-table end*/

/* 分页器通用样式 START *******************************   */

.el-pagination button:disabled {
  background-color: transparent;
}

.el-pagination .btn-next,
.el-pagination .btn-prev {
  background-color: transparent !important;
}

.el-pagination__total {
  color: @font_color !important;
}

.el-pagination__jump {
  color: @font_color !important;
}

.el-pagination__editor.el-input .el-input__inner {
  color: @font_color !important;
}

.el-pagination .el-select__placeholder {
  // :deep() {
  color: @font_color !important;
  // }

  // :deep(.el-select__placeholder.is-transparent) {
  //   color: #909399 !important;
  // }
}



.el-tabs--border-card {
  background: #031527a6 !important;

  .el-tabs__header {
    background: transparent !important;

    .is-active {
      background-color: #031527a6 !important;
    }
  }

}

.el-divider__text {
  background-color: transparent !important;
  color: @font_color !important;
}

.el-tabs__header {
  margin: 0 !important;
}

.el-tabs__nav-wrap {
  margin-bottom: -2px !important;
}

.elPager {
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  // background-color: #eee;
  // color: #333;

}

.elPager .el-pager li {
  color: @font_color !important;
}

.elPager .el-pager li:hover {
  color: rgba(14, 173, 253, 1) !important;
}

.elPager .el-pager li.is-active {
  color: rgba(14, 173, 253, 1) !important;
}

.el-pagination button {
  color: rgba(14, 173, 253, 1) !important;
}

.el-pagination button:hover {
  color: #409eff !important;
}

.elPager .el-pager li.is-active {
  color: rgba(14, 173, 253, 1) !important;
}

/* 分页器通用样式 END *******************************   */

/* 表单通用样式 START *******************************   */

.custom-form {
  /* height: 100%; */
  overflow: auto;
}

.custom-form::-webkit-scrollbar {
  width: 6px;
}

.custom-form::-webkit-scrollbar-thumb {
  background: #0e59f1;
  border-radius: 5px;
}

.custom-form::-webkit-scrollbar-thumb:hover {
  background: #2266ec;
}

/* 输入框 */
.custom-form .el-form-item__label {
  /* color: #179ae0 !important; */
  color: #ffffff !important;
}

.custom-form .el-select {
  width: 100%;
}

.custom-form .el-cascader {
  width: 100%;
}

.custom-form .el-form-item__content {
  color: #ffffff !important;
}

/*  修改浏览器 默认额记住背景色 */
input:-webkit-autofill {
  -webkit-text-fill-color: #ffffff !important;
  background-color: transparent !important;
  background-image: none;
  caret-color: #ffffff;
  transition: background-color 500000s ease-in-out 0s;
}

/* 下拉框 */

/* 日期控件修改 start */
.el-range-separator {
  color: #ffffff !important;
}

.el-date-table td.in-range .el-date-table-cell {
  background-color: #546072 !important;
}

.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
  color: #d9dfe8d9 !important;
}

.el-range-input {
  color: @font_color !important;
}

// .el-date-editor .el-range-input{
//   color: ;
// }
/* 表单通用样式 END *******************************   */

/* 消息提示确认框 START *******************************   */
.el-message-box {
  // background-color: #032a50;
  background-color: #062646cf !important
}

.el-message-box__header {
  background-color: #006db2a1 !important;
}

.el-message-box__close {
  color: #ffffff !important;
}

/* 消息提示确认框 END *******************************   */

/* tabs标签页 START *******************************   */
.tabsClass .el-tabs__item {
  color: @font_color !important;
}

.tabsClass .el-tabs__item:hover {
  color: #00d0ff;
}

.tabsClass .is-active {
  color: #00d0ff !important;
  font-weight: bold;
}

.tabsClass .el-form-item__label {
  // color: #0eaffd;
}

/* tabs标签页 END *******************************   */

/* menu标签页 START *******************************   */

.el-menu--horizontal>.el-menu-item.is-active,
.el-menu-item.is-active {
  background-image: @bg;
  background-size: cover;
  background-position: center center;
  color: @font_color !important
}

.el-menu-item:hover {
  background-color: #092a4a !important;
}

/* menu标签页 END *******************************   */

/* input标签页 START *******************************   */
.el-input__wrapper {
  box-shadow: 0 0 0 1px rgba(@themeColor, .3) inset !important;
}

.el-input__inner {
  color: @font_color !important;
}

.el-input__wrapper:hover {
  box-shadow: 0 0 0 1px rgba(@themeColor, 1) inset !important;
}

/* input标签页 END *******************************   */

/* select标签页 START *******************************   */
.el-select__wrapper {
  box-shadow: 0 0 0 1px rgba(@themeColor, .3) inset !important;
  min-height: 32px !important;
}

.el-select__wrapper:hover {
  box-shadow: 0 0 0 1px rgba(@themeColor, 1) inset !important;
}

.el-select-dropdown__item {
  color: #fff !important;
}

.el-select-dropdown__item.is-selected {
  color: #fff !important;
}

.el-select__input {
  color: @font_color !important;
}

// .el-select-dropdown__item.is-hovering {
//   background-color: rgba(@themeColor, .3) !important;
// }
// .el-select-dropdown__item.is-selected {
//   color: rgba(@themeColor, 1) !important;
// }
// .el-select__placeholder{
//   color:rgba(@fontColor, 1) !important
// }
.el-select__placeholder {
  color: @font_color !important;
}

.el-select__placeholder.is-transparent span {
  color: @placeholder_color !important;
}

.el-tag.el-tag--info {
  --el-tag-bg-color: rgba(@overlayBg, 1) !important;
  --el-tag-text-color: rgba(@fontColor, 1) !important;
}

.selectHeight .el-select__wrapper {
  padding-top: 4px !important;
  padding-bottom: 4px !important;

}

/* select标签页 END *******************************   */

/* popper标签页 START *******************************   */
.el-popper.is-light {
  background: #021942 !important;
  border: 1px solid #2c659f !important;
  color: @font_color;

}

/* popper标签页 END *******************************   */

/* picker 标签页 START *******************************   */
.el-range-separator {
  color: #ffffff !important;
}

.el-date-picker__header-label {
  color: @font_color !important;

  &:hover {
    color: #0eadfd !important;
  }
}

.el-date-table th {
  border-bottom: solid 1px rgba(@themeColor, .3) !important;
  color: @font_color !important;
}

.el-picker__popper {
  td {
    .cell {
      color: @font_color !important;
    }
  }
}

.el-date-range-picker__content.is-left {
  border-right: 1px solid rgba(@themeColor, .3) !important;
}

.el-picker-panel__footer {
  border-top: 1px solid rgba(@themeColor, .3) !important;
}

.el-date-table td.in-range .el-date-table-cell {
  background-color: #546072 !important;
}

// .el-date-editor .el-range-input {
//   color: rgba(@fontColor, 1) !important;
// }

.el-picker-panel {
  background: rgba(@overlayBg, 1) !important;
}

.el-picker-panel__icon-btn {
  color: rgba(@fontColor, .8) !important;
}

.el-picker-panel__footer {
  background: rgba(@overlayBg, 1) !important;
}

.el-date-table td.end-date .el-date-table-cell__text,
.el-date-table td.start-date .el-date-table-cell__text {
  background-color: rgba(@themeColor, 1) !important;
}

.el-date-table td.today .el-date-table-cell__text {
  color: rgba(@themeColor, 1) !important;
}

.el-date-table td.available:hover {
  color: rgba(@themeColor, 1) !important;
}

.el-date-table td.in-range .el-date-table-cell {
  background-color: rgba(@themeColor, .3) !important;
}

.el-picker-panel .el-time-panel {
  // background-color: rgba(@overlayBg, 1) !important;
  border: solid 1px rgba(@themeColor, 1) !important;
}

// .el-time-spinner__item.is-active:not(.is-disabled) {
//   color: rgba(@themeColor, 1) !important;
// }
.el-time-spinner__item {
  --el-text-color-regular: @font_color;
  --el-text-color-primary: #316ada;
}

.el-time-panel__btn {
  color: rgba(@themeColor, 1) !important;
}

.el-popper__arrow {
  display: none;
}

.el-popper.is-light .el-popper__arrow::before {
  border: 1px solid #0eadfd !important;
  background: #021942 !important;
}

.el-popper.is-dark .el-popper__arrow::before {
  border: 1px solid #0eadfd !important;
  background: #021942 !important;
}

/* picker 标签页 END *******************************   */


#app {
  height: 100vh;
  overflow: hidden;
  // background-image: @app_bg !important;
  background-image: url(/images/new/icon/背景.png);
  // background-image: url(@app_bg);
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
}


/* el-dialog  START*/
.el-overlay {
  background-color: #031527df;
  z-index: 2000 !important;
}

.el-dialog {
  border-radius: 6px !important;
  background: #022141bf !important;
  border: 1px solid #2c659f4f;
  --el-dialog-margin-top: 15px;

  .el-form-item__label {
    color: #006db2;
    font-weight: 600;
  }

  .el-button:first-of-type {
    background-color: #006db2;
  }
}

.el-dialog .el-dialog__header {
  border-radius: 6px 6px 0 0;
  background-color: transparent;
  border-bottom: 1px solid #2c659f4f;
  margin: 0;

  .el-dialog__title {
    color: #fff;
    font-size: 19px;
    font-weight: bold;
  }

  .el-dialog__headerbtn {
    display: none;
  }
}

.dialog_body {
  max-height: 60vh;
  overflow: auto;
  padding-right: 20px;
}

/* el-dialog  END*/

/* 消息提示弹窗 START*/
.el-message {
  z-index: 9999 !important;
}

.el-message-box__header {
  background-color: #006db2;
  padding-left: 10px;

  .el-message-box__title {
    font-size: 18px;
    font-weight: bolder;
    // color: #285472;
    color: #fff
  }

  .el-message-box__headerbtn .el-message-box__close {
    color: #fff;
  }
}

.el-message-box__content {
  padding-left: 25px;

  .el-message-box__message {
    color: #285472;
    font-weight: 600;
  }
}

.el-message-box {
  border: 0px solid gainsboro !important;
}

/* 消息提示弹窗 END*/

// 时间选择器 START
.el-picker__popper .el-picker-panel__body {
  background-color: #022141bf;
}

.el-picker__popper .el-picker-panel__body .el-time-panel__content {
  background-color: transparent;
}

.el-picker__popper .el-picker-panel__body .el-time-panel__btn {
  color: #fff !important;
}

// 时间选择器 END

// 多选级联 tag颜色
.el-cascader__tags .el-tag {
  background: #214c77 !important;
}

// user defined
.list_icon {
  display: inline-block;
  width: 32px;
  height: 32px;
  border-radius: 10px;
  background: url("/images/new/icon/列表.png") no-repeat 0 0/100% 100%;
  margin-right: 10px;
}

.resource_icon {
  display: inline-block;
  width: 32px;
  height: 32px;
  border-radius: 10px;
  background: url("/images/header/资源.png") no-repeat 0 0/100% 100%;
  margin-right: 10px;
}

.timeLine_icon {
  display: inline-block;
  width: 32px;
  height: 32px;
  border-radius: 10px;
  background: url("/images/header/态势.png") no-repeat 0 0/100% 100%;
  margin-right: 10px;
}

.EMail_icon {
  display: inline-block;
  width: 32px;
  height: 32px;
  border-radius: 10px;
  background: url("/images/header/Email.png") no-repeat 0 0/100% 100%;
  margin-right: 10px;
}

.user_icon {
  width: 32px;
  height: 32px;
  border-radius: 10px;
  background: url("/images/header/用户.png") no-repeat 0 0/100% 100%;
  margin-right: 10px;
}

.small_list_icon {
  cursor: pointer;
  display: inline-block;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background: url("/images/new/icon/任务推演_30.png") no-repeat 0 0/100% 100%;
  margin-right: 10px;
}

.system_config_icon {
  cursor: pointer;
  display: inline-block;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background: url("/images/header/设置.png") no-repeat 0 0/100% 100%;
  margin-right: 10px;
}

//地基图标
.dj_icon {
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  width: 50px;
  height: 50px;
  border-radius: 4px;
  border: 1px solid #255788;
  // background: url("/images/new/icon/地基.png") no-repeat 0 0/100% 100%;
  background: url("/public/dmz1.png") no-repeat 0 0/100% 100%;

  &:hover {
    border: 1px solid #197adb;
  }

}

//空基图标
.kj_icon {
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  width: 50px;
  height: 50px;
  border-radius: 4px;
  border: 1px solid #255788;
  // background: url("/images/new/icon/无人机.png") no-repeat 0 0/100% 100%;
  background: url("/public/fj.png") no-repeat 0 0/100% 100%;


  &:hover {
    border: 1px solid #197adb;
  }
}

//天基图标
.tj_icon {
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  width: 50px;
  height: 50px;
  border-radius: 10px;
  border: 1px solid #255788;
  // background: url("/images/new/icon/卫星.png") no-repeat 0 0/100% 100%;
  background: url("/public/wx.png") no-repeat 0 0/100% 100%;


  &:hover {
    border: 1px solid #197adb;
  }
}

.el-tree-node__children {
  background-color: #031527a6 !important;
}

// 进度条
.el-progress-bar__outer {
  border: 1px solid #2b3b47 !important;
  background: #031527a6 !important;
  overflow: visible !important;
}

.el-progress-bar__innerText {
  position: relative;
}

// .el-progress-bar__inner {
//   width: 100% !important;
// }

//透明按钮
.transparentBtn {
  background: transparent !important;

}

.el-dropdown {
  color: @font_color !important;
}

.el-dropdown-menu__item {
  color: @font_color !important;

}

//用户字体颜色
.userColor {
  color: @font_color !important;
}

.user_subTitle {
  color: @font_color !important;
  font-size: 16px;
  ;
}

.title-font {
  font-size: 18px;
  font-weight: bolder;
  color: #c6cdd6;
}

.el-step__title.is-process {
  // color: #;
  --el-text-color-primary: #5a83d7
}

.el-step__icon {
  // background: #092a4a;
  --el-bg-color: #092a4a;
  width: 32px !important;
  height: 32px !important;
}

.is-process .el-step__icon {
  border-color: #2f86ff !important;
  background: #2f86ff;
}

.el-step__head.is-success {
  --el-color-success: #03a2a7;
}

.el-step__title.is-success {
  --el-color-success: #03a2a7;
}

.is-success .el-step__line {
  background-color: #03a2a7 !important;
  opacity: 1;

}

.el-step__line {
  --el-text-color-placeholder: #a8abb2;
  opacity: 0.3;
}

.el-step__head.is-wait {
  --el-text-color-placeholder: #a8abb299
}

.is-wait .el-step__icon-inner {
  font-weight: 100
}

.el-step__line {
  top: 14px;
}

.is-success .el-step__icon-inner path {
  color: #03a2a7;
}

.el-radio-group {
  border-radius: 5px;
}

.el-radio-group .is-active .el-radio-button__inner {
  background-color: #003a66 !important;
}

.el-radio-button__inner {
  // border: 0 !important;
  color: @font_color !important;
}

.vis-text {
  color: @font_color !important;
}

.is-left {
  text-align: left !important;
}

.is-right {
  text-align: right !important;
}

.vis-item {
  // background: #2f557b !important;
  background: #2f557b;
  border-color: #9c9c9c;
  border-width: 2px;

}

.vis_guihuaTask {
  background: #2f557b !important;
}

.vis_shijiTask {
  background: #7c623eB2 !important;
}

.vis-foreground {
  border: 0 !important;
}

.vis-timeline {
  border-color: #003a66 !important;
}

.vis-panel {
  border: 0px solid #003a66;
}

.vis-grid {
  border-color: #003a66 !important;

}

.vis-panel.vis-center {
  border: 1px solid #003a66 !important;
}

.vis-panel.vis-bottom {
  border: 1px solid #003a66 !important;

}

.vis-panel.vis-left {
  border-color: #003a66 !important;
}

.vis-labelset .vis-label {
  border-bottom: 1px solid #003a66 !important;
}

.vis-foreground .vis-group {
  border-bottom: 1px solid #003a66 !important;

}

.vis-custom-time {
  background-color: #6e94ff;
}


/*****************针对地球标牌*********************/
.infoDiv {
  height: auto !important;
  min-height: 110px !important;
  width: 220px !important;
  background: #113c5bd9 !important;
}

.contentContainer {
}

.contentLabel {
  min-width: 50px !important;

}


.evgis-overlay-container {
  height: auto !important;
  min-height: 110px !important;
  width: 220px !important;
  background: #113c5bd9 !important;
}

.evgis-overlay-title {
  background-color: transparent !important;
}

.evgis-overlay-content {
  height: auto !important;

}

/*************自定义消息***********************/

// :nth-child(n)


.customMessage {
  position: fixed;
  // top: 8%; //calc(0.08 + n * 0.03);
  left: 50%;
  transform: translateX(-50%);
  min-width: 300px;
  max-width: 50vw;
  min-height: 40px;
  z-index: 9999;
  display: flex;
  border-radius: 5px;

  .msg-close {
    position: absolute;
    top: 0px;
    right: 10px;
  }

  .msg-iconBox {
    width: 35px;
    display: flex;
    justify-content: center;
    align-items: center;

    .msg-icon {
      width: 20px;
      height: 20px;
    }
  }

  .msg-text {
    width: calc(100% - 35px);

    .msg-title {
      height: 25px;
      line-height: 25px;
      font-size: 15px;
      font-weight: bold;
    }

    .msg-body {
      min-height: 25px;
      line-height: 25px;

    }
  }
}

.errorMessage {
  background: rgba(244, 78, 78, 0.6);
  border: 2px solid #972e2e;

  .msg-icon-icon {
    background: url(/images/icon/w.png) 0 0 /100% 100% no-repeat;
  }
}

.infoMessage {
  background: rgba(13, 59, 102, 0.6);
  border: 2px solid #1f86c7;

  .msg-icon-icon {
    background: url(/images/icon/x.png) 0 0 /100% 100% no-repeat;
  }
}

.warningMessage {
  background: rgba(169, 91, 44, 0.6);
  border: 2px solid #a95b2c;

  .msg-icon-icon {
    background: url(/images/icon/g.png) 0 0 /100% 100% no-repeat;
  }
}

.successMessage {
  background: rgba(29, 173, 164, 0.6);
  border: 2px solid #1dada4;

  .msg-icon-icon {
    background: url(/images/icon/r.png) 0 0 /100% 100% no-repeat;
  }
}

.forMessage(@n, @i: 0) when (@i<=@n) {
  .customMessage:nth-child(@{i}) {
    top: calc(2% + @i * 6%)
  }

  .forMessage(@n, (@i+1))
}

@media screen {
  .forMessage(20);
}

/***********************自定义消息****************************/