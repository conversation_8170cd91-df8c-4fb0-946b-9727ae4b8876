.ol-popup {
  background-color: white;
  border-radius: 3px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  padding: 8px;
  position: absolute;
  width: 200px;
  z-index: 999;
}

.ol-popup:after,
.ol-popup:before {
  bottom: 100%;
  left: 50%;
  border: solid transparent;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.ol-popup:after {
  border-color: rgba(255, 255, 255, 0);
  border-bottom-color: white;
  border-width: 10px;
  margin-left: -10px;
}

.ol-popup:before {
  border-color: rgba(0, 0, 0, 0);
  border-bottom-color: rgba(0, 0, 0, 0.2);
  border-width: 11px;
  margin-left: -11px;
}

.ol-popup-closer {
  text-decoration: none;
  position: absolute;
  top: 2px;
  right: 8px;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  color: #333;
  opacity: 0.2;
}

.ol-popup-closer:hover {
  opacity: 0.5;
  cursor: pointer;
}

/* 背景遮罩层 */
.evgis-overlay-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}

/* 弹出框容器 */
.evgis-overlay-container {
  position: absolute;
  min-width: 280px;
  z-index: 1001;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 5px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  overflow: auto;
}

/* 弹出框标题栏 */
.evgis-overlay-title {
  padding: 10px;
  font-size: 18px;
  font-weight: bold;
  cursor: all-scroll;
  color: #fff;
  background-color: #2884d8;
  border-bottom: 1px solid #eee;
}

/* 弹出框关闭按钮 */
.evgis-overlay-close {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
  font-size: 20px;
  color: #000;
}

/* 弹出框内容 */
.evgis-overlay-content {
  padding: 20px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #383838;
  max-width: 300px; /* 设置最大宽度 */
  overflow: auto; /* 添加滚动条 */
  float: left; /* 左浮动 */
}
@keyframes flashAlarmer {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}
.alarmer-box2d {
  width: 64px;
  height: 64px;
  pointer-events: none;
  z-index: 997;
  transform: translate(-32px, -32px);
}
.alarmer-box2d .alarmer2d {
  width: 64px;
  height: 64px;
  background-color: rgba(255, 99, 71, 0.6);
  pointer-events: none;
  z-index: 997;
  border-radius: 32px;
  animation: flashAlarmer 1s ease-in-out infinite;
}
@keyframes flashPointAlarmer {
  0% {
    opacity: 0.6;
    transform: scale(0.5);
  }
  30% {
    opacity: 0.6;
  }
  100% {
    opacity: 0;
    transform: scale(1.4);
  }
}
.alarmer-point-box {
  width: 20px;
  height: 20px;
  pointer-events: none;
  z-index: 997;
  position: relative;
  border-radius: 50%;
  transform: translate(22px, -23px);
}
.alarmer-point-box .alarmer::after,
.alarmer-point-box .alarmer::before {
  content: '';
  width: 20px;
  height: 20px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -10px;
  margin-top: -10px;
  border-radius: 50%;
  animation: flashPointAlarmer 1.5s ease-out 0s infinite;
}
.alarmer-point-box .alarmer::after {
  background-color: #0c87e2;
}
.alarmer-point-box .alarmer::before {
  background-color: rgba(0, 168, 253, 0.2);
}

/* ------------标牌------------- */
/* 滚动条宽度 */
.infoDiv {
  position: absolute;
  top: 10px;
  left: 294px;
  background: rgb(0 128 218 / 85%);
  padding: 10px;
  height: 300px;
  width: 250px;
  /* border: 1px solid rgba(255, 255, 255, 0.9); */
  cursor: pointer;
  color: #fff;
  border-radius: 10px;
}
.infoDiv ::-webkit-scrollbar {
  width: 20px;
}
.contentContainer {
  position: relative;
  border-top: 1px solid;
  overflow-y: auto;
  /* height:200px !important; */
  padding: 10px;
}

.contentDiv {
  display: flex;
  margin-bottom: 8px;
}

.labelTitle {
  margin: 0 0 5px 0;
}
.customInfo {
position: absolute;
bottom: 10px;
cursor: pointer;
color: #007cb4;
right: 10px;
}
.contentLabel {
  min-width: 105px;
}

.contentValue {
  margin-left: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.closeBtn {
  position: absolute;
  top: 10px;
  right: 10px;
}

/*----------------- 时间轴样式 ----------------------*/
.timeLine_Element {
  width: 80%;
  height: 90px;
  background: rgba(255, 255, 255, 0.6);
  position: absolute;
  bottom: 10px;
  left: 9%;
  border-radius: 10px;
  padding: 0 40px;
}
.timeLine_Container {
  height: 100%;
}
.timeLine_Slider {
  width: 100%;
  height: 20px;
  display: flex;
  flex-direction: column-reverse;
}
.slider_Strip {
  width: 100%;
  height: 5px;
  border: 1px solid #fff;
  border-radius: 5px;
  background-color: #2884d8;
  cursor: pointer;
}
.slider_Button_Wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 36px;
  width: 36px;
  position: absolute;
  top: -2px;
  left: 0;
  transform: translateX(80%);
  background-color: transparent;
  text-align: center;
  line-height: normal;
  /* transition: all 1s linear; */
}
.slider_TimeBox {
  display: none;
  position: absolute;
  top: -28px;
  width: 175px;
  height: 30px;
  line-height: 30px;
  color: #fff;
  background: #000;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.slider_TimeBox::after {
  content: '';
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  border-style: solid;
  border-width: 6px;
  border-color: #000 transparent transparent transparent;
}
.slider_Button {
  width: 10px;
  height: 10px;
  background: #2884d8;
  border: 2px solid #fff;
  border-radius: 50%;
  transition: 0.2s;
  cursor: grab;
}
.timeLine_ControlBtn {
  width: 100%;
  height: 70px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.timeLine_ControlBtn_btnList {
  width: 210px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.timeLine_ControlBtn_btnList_speedBox {
  width: 50px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  background: #2884d8;
  color: #fff;
  border-radius: 8px;
  user-select: none;
  margin-bottom: 5px;
}
.speedBox_playSpeed {
  display: flex;
  justify-content: space-around;
  font-size: 20px;
  cursor: pointer;
}
.speedBox_playSpeed_speed {
  margin-left: 2px;
}
.timeLine_ControlBtn_Btn {
  cursor: pointer;
}
.timeLine_TimeList {
  color: #007cb4;
  user-select: none;
}
.timeLine_TimeList_StartTime {
  position: absolute;
  top: 40px;
}
.timeLine_TimeList_EndTime {
  position: absolute;
  top: 40px;
  right: 40px;
}
