:export {
  name: "less";
  themeColor: var(--theme<PERSON><PERSON>r, rgba(14, 173, 253));
  bgColor: var(--bgColor, #ffffff);
  fontColor: var(--fontColor, #000000);
  buttonColor: var(--buttonColor, rgba(64, 158, 255));
  tree01: var(--tree01, url('/images/bgBorder/tree-bg_01.png'));
  tree02: var(--tree02, url('/images/bgBorder/tree-bg_02.png'));
  tree03: var(--tree03, url('/images/bgBorder/tree-bg_03.png'));
  qb_03: var(--qb_03, url('/images/bgBorder/qb_03.png'));
  qb_04: var(--qb_04, url('/images/bgBorder/qb_04.png'));
  qb_05: var(--qb_05, url('/images/bgBorder/qb_05.png'));
  qb_06: var(--qb_06, url('/images/bgBorder/qb_06.png'));
  DK_06: var(--DK_06, url('/images/bgBorder/模型库DK_06.png'));
  DK_08: var(--DK_08, url('/images/bgBorder/模型库DK_08.png'));
  DK_09: var(--DK_09, url('/images/bgBorder/模型库DK_09.png'));
  DK_10: var(--DK_10, url('/images/bgBorder/模型库DK_10.png'));
  DK_11: var(--DK_11, url('/images/bgBorder/模型库DK_11.png'));
  DK_12: var(--DK_12, url('/images/bgBorder/模型库DK_12.png'));
  line_06: var(--line_06, url('/images/bgBorder/line_06.png'));
  line_07: var(--line_07, url('/images/bgBorder/line_07.png'));
  line_03: var(--line_03, url('/images/bgBorder/line_03.png'));
  small_03: var(--small_03, url('/images/bgBorder/forms-small_03.png'));
  small_04: var(--small_04, url('/images/bgBorder/forms-small_04.png'));
  small_05: var(--small_05, url('/images/bgBorder/forms-small_05.png'));
  small_06: var(--small_06, url('/images/bgBorder/forms-small_06.png'));
  small_08: var(--small_08, url('/images/bgBorder/forms-small_08.png'));
  small_09: var(--small_09, url('/images/bgBorder/forms-small_09.png'));
  small_10: var(--small_10, url('/images/bgBorder/forms-small_10.png'));
  small_11: var(--small_11, url('/images/bgBorder/forms-small_11.png'));
  small_12: var(--small_12, url('/images/bgBorder/forms-small_12.png'));
  small_13: var(--small_13, url('/images/bgBorder/forms-small_13.png'));
  small_14: var(--small_14, url('/images/bgBorder/forms-small_14.png'));
  top_bg: var(--top_bg, url('/images/header/模型库-top-bg.png'));
  bg_mr: var(--bg_mr, url('/images/header/一级菜单按钮-bg-默认.png'));
  bg: var(--bg, url('/images/header/一级菜单按钮-bg.png'));
  app_bg: var(--app_bg, url(""));
  current_time: var(--current_time, url('/images/header/current-time.png'));
  data_reduction: var(--data_reduction, url('/images/header/data-reduction.png'));
  play_back: var(--play_back, url('/images/header/play-back.png'));
  system_settings: var(--system_settings, url('/images/header/system-settings.png'));
  popupTitleBg: var(--popupTitleBg, url('/images/bgBorder/装饰一级bt_03.png'));
  borderColor: var(--borderColor, rgba(255, 255, 255));
  custom_header_close: var(--custom_header_close, url("/images/icon/close.png"));
  custom_header_delete: var(--custom_header_delete, url("/images/icon/sc.png"));
  custom_header_Edit: var(--custom_header_Edit, url("/images/icon/bianji.png"));
  custom_header_look: var(--custom_header_look, url("/images/icon/ck.png"));
  custom_header_wgz: var(--custom_header_wgz, url("/images/icon/wgz.png"));
  custom_header_zdgz: var(--custom_header_zdgz, url("/images/icon/zdgz.png"));
  overlayBg: var(--overlayBg, rgba(2, 25, 66));
}

:root {
  /* 表单背景颜色 */
  --el-fill-color-blank: transparent !important;
  --el-border-color-lighter: #003a66 !important;
  --el-color-primary-light-9: #003a66 !important;
  --el-border-color-light: #003a66 !important;
  --el-border-color-hover: #00aeff !important;
  --el-border-color-base: #003a66 !important;
  --el-border-color: #003a66 !important;
  --el-bg-color-overlay: #021942 !important;
  --el-fill-color-light: rgba(0, 174, 255, 0.4) !important;
  --el-disabled-bg-color: transparent !important;
  /* 表单文字背景颜色 */
  --el-text-color-regular: #000000 !important;
  --el-menu-hover-bg-color: transparent !important;
}

/*按钮样式*/
.el-button {
  background-color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
  border-color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
  color: #fff !important;
}

.el-button:focus,
.el-button:hover {
  background-color: #006eff !important;
}

/*表单外边框样式*/
.el-tree {
  --el-tree-node-hover-bg-color: #81d4f9 !important;
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #81d4f9 !important;
}

.el-tree-node__content {
  margin-top: 4%;
}

/* 通用表格样式 custom-table start  START  ---------------------------------- */
.el-table .cell {
  /* color: #285472; */
  font-weight: 500;
}

.el-table__header {
  background-color: #b1a7a700;
}

.el-table__header .cell {
  color: #285472;
  font-weight: bolder;
  font-size: 15px;
}

.el-table__header .el-table__cell {
  height: 44px;
  background-color: #f3f6f9 !important;
}

.custom-table {
  background-color: transparent !important;
}

.custom-table tr {
  background-color: transparent !important;
}

.custom-table th.el-table__cell {
  background-color: #f3f6f9 !important;
  color: #111 !important;
  font-weight: 600;
}

.custom-table td.el-table__cell,
.custom-table th.el-table__cell.is-leaf {
  border-bottom: none !important;
}

.custom-table .el-table__body tr:hover>td.el-table__cell {
  background-color: rgba(var(--themeColor, rgba(14, 173, 253)), 0.3) !important;
}

.custom-table .el-table__body tr.current-row>td.el-table__cell {
  background-color: rgba(var(--themeColor, rgba(14, 173, 253)), 0.5) !important;
}

.custom-table .el-table__inner-wrapper::after,
.custom-table .el-table__inner-wrapper::before {
  display: none !important;
}

.dragTableSty {
  padding: 5px;
  box-sizing: border-box;
  color: #ffffff;
  height: calc(100% - 30px);
  overflow: auto;
}

.dragTableSty table {
  width: 100%;
  font-size: 14px;
  border-collapse: collapse;
}

.dragTableSty table thead {
  color: #0eaffd;
  background-color: rgba(var(--bgColor, #ffffff), 0.3) !important;
}

.dragTableSty table tbody {
  text-align: center;
}

.dragTableSty table tr {
  line-height: 28px;
}

.dragTableSty2 {
  padding: 8px;
  box-sizing: border-box;
  color: #ffffff;
  height: calc(100% - 40px);
  overflow: auto;
}

.dragTableSty2 table {
  width: 100%;
  font-size: 18px;
  border-collapse: collapse;
}

.dragTableSty2 table thead {
  color: #0eaffd;
  background-color: rgba(var(--bgColor, #ffffff), 0.3) !important;
}

.dragTableSty2 table tbody {
  text-align: center;
}

.dragTableSty2 table tr {
  line-height: 40px;
}

.custom-table .el-table__body-wrapper tr td.el-table-fixed-column--right {
  background: transparent !important;
}

.el-checkbox__inner {
  border-color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
  background-color: transparent;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-checkbox__input.is-checked+.el-checkbox__label {
  color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-checkbox__input.is-disabled .el-checkbox__inner {
  background-color: rgba(var(--themeColor, rgba(14, 173, 253)), 0.4) !important;
}

.el-radio__inner {
  border-color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-radio__input.is-checked .el-radio__inner {
  background: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-radio__input.is-checked+.el-radio__label {
  color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-radio__input.is-disabled .el-radio__inner {
  background-color: rgba(var(--themeColor, rgba(14, 173, 253)), 0.4) !important;
}

.inputModule .el-input-group__append {
  background-color: #1b2f8894 !important;
  color: #ffffff;
}

input::-webkit-input-placeholder {
  color: #909399 !important;
}

input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}

input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.el-input.is-disabled .el-input__wrapper {
  box-shadow: 0 0 0 1px rgba(var(--themeColor, rgba(14, 173, 253)), 0.3) inset !important;
}

.el-textarea {
  --el-input-border-color: rgba(var(--themeColor, rgba(14, 173, 253)), 0.3) !important;
  --el-input-focus-border-color: rgba(var(--themeColor, rgba(14, 173, 253)), 0.3) !important;
}

.el-textarea__inner:hover {
  box-shadow: 0 0 0 1px rgba(var(--themeColor, rgba(14, 173, 253)), 1) inset !important;
}

.el-color-picker__mask {
  background-color: rgba(var(--themeColor, rgba(14, 173, 253)), 0.1) !important;
}

.el-scrollbar {
  --el-scrollbar-bg-color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
  --el-scrollbar-hover-bg-color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

::-webkit-scrollbar-thumb {
  background: rgba(var(--themeColor, rgba(14, 173, 253)), 0.8) !important;
}

.demo-color-block .el-color-picker__trigger {
  border: 1px solid rgba(var(--themeColor, rgba(14, 173, 253)), 0.3) !important;
}

/* 通用表格样式 custom-table end*/
/* 分页器通用样式 START *******************************   */
.el-pagination button:disabled {
  background-color: transparent;
}

.el-pagination .btn-next,
.el-pagination .btn-prev {
  background-color: transparent !important;
}

.el-pagination__total {
  color: #333 !important;
}

.el-pagination__jump {
  color: #333 !important;
}

.el-pagination__editor.el-input .el-input__inner {
  color: #333 !important;
}

.el-tabs__header {
  margin: 0 !important;
}

.el-tabs__nav-wrap {
  margin-bottom: -2px !important;
}

.elPager {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #eee;
  color: #333;
}

.elPager .el-pager li {
  color: rgba(var(--fontColor, #000000), 1) !important;
}

.elPager .el-pager li:hover {
  color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.elPager .el-pager li.is-active {
  color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-pagination button {
  color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-pagination button:hover {
  color: #409eff !important;
}

.elPager .el-pager li.is-active {
  color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

/* 分页器通用样式 END *******************************   */
/* 表单通用样式 START *******************************   */
.custom-form {
  /* height: 100%; */
  overflow: auto;
}

.custom-form::-webkit-scrollbar {
  width: 6px;
}

.custom-form::-webkit-scrollbar-thumb {
  background: #0e59f1;
  border-radius: 5px;
}

.custom-form::-webkit-scrollbar-thumb:hover {
  background: #2266ec;
}

/* 输入框 */
.custom-form .el-form-item__label {
  /* color: #179ae0 !important; */
  color: #ffffff !important;
}

.custom-form .el-select {
  width: 100%;
}

.custom-form .el-cascader {
  width: 100%;
}

.custom-form .el-form-item__content {
  color: #ffffff !important;
}

/*  修改浏览器 默认额记住背景色 */
input:-webkit-autofill {
  -webkit-text-fill-color: #ffffff !important;
  background-color: transparent !important;
  background-image: none;
  caret-color: #ffffff;
  transition: background-color 500000s ease-in-out 0s;
}

/* 下拉框 */
/* 日期控件修改 start */
.el-range-separator {
  color: #ffffff !important;
}

.el-date-table td.in-range .el-date-table-cell {
  background-color: #546072 !important;
}

/* 表单通用样式 END *******************************   */
/* 消息提示确认框 START *******************************   */
.elMessage .el-message-box {
  background-color: #032a50;
}

.elMessage .el-message-box__title {
  color: #00d5f7;
}

.elMessage .el-message-box__close {
  color: #ffffff !important;
}

/* 消息提示确认框 END *******************************   */
/* tabs标签页 START *******************************   */
.tabsClass .el-tabs__item {
  color: #4c93b5 !important;
}

.tabsClass .el-tabs__item:hover {
  color: #00d0ff;
}

.tabsClass .is-active {
  color: #00d0ff !important;
  font-weight: bold;
}

.tabsClass .el-form-item__label {
  color: #0eaffd;
}

/* tabs标签页 END *******************************   */
/* menu标签页 START *******************************   */
.el-menu--horizontal>.el-menu-item.is-active,
.el-menu-item.is-active {
  background-image: var(--bg, url('/images/header/一级菜单按钮-bg.png'));
  background-size: cover;
  background-position: center center;
  color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-menu-item:hover {
  background-color: none !important;
}

/* menu标签页 END *******************************   */
/* input标签页 START *******************************   */
.el-input__wrapper {
  box-shadow: 0 0 0 1px rgba(var(--themeColor, rgba(14, 173, 253)), 0.3) inset !important;
}

.el-input__wrapper:hover {
  box-shadow: 0 0 0 1px rgba(var(--themeColor, rgba(14, 173, 253)), 1) inset !important;
}

/* input标签页 END *******************************   */
/* select标签页 START *******************************   */
.el-select__wrapper {
  box-shadow: 0 0 0 1px rgba(var(--themeColor, rgba(14, 173, 253)), 0.3) inset !important;
  min-height: 32px !important;
}

.el-select__wrapper:hover {
  box-shadow: 0 0 0 1px rgba(var(--themeColor, rgba(14, 173, 253)), 1) inset !important;
}

.el-select-dropdown__item {
  color: #fff !important;
}

.el-select-dropdown__item.is-selected {
  color: #000 !important;
}

.el-select__placeholder.is-transparent {
  color: #909399 !important;
}

.el-tag.el-tag--info {
  --el-tag-bg-color: rgba(var(--overlayBg, rgba(2, 25, 66)), 1) !important;
  --el-tag-text-color: rgba(var(--fontColor, #000000), 1) !important;
}

/* select标签页 END *******************************   */
/* popper标签页 START *******************************   */
.el-popper.is-light {
  background: rgba(var(--overlayBg, rgba(2, 25, 66)), 1) !important;
  border: 1px solid rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

/* popper标签页 END *******************************   */
/* picker 标签页 START *******************************   */
.el-range-separator {
  color: #ffffff !important;
}

.el-date-table th {
  border-bottom: solid 1px rgba(var(--themeColor, rgba(14, 173, 253)), 0.3) !important;
}

.el-date-range-picker__content.is-left {
  border-right: 1px solid rgba(var(--themeColor, rgba(14, 173, 253)), 0.3) !important;
}

.el-picker-panel__footer {
  border-top: 1px solid rgba(var(--themeColor, rgba(14, 173, 253)), 0.3) !important;
}

.el-date-table td.in-range .el-date-table-cell {
  background-color: #546072 !important;
}

.el-date-editor .el-range-input {
  color: rgba(var(--fontColor, #000000), 1) !important;
}

.el-picker-panel {
  background: rgba(var(--overlayBg, rgba(2, 25, 66)), 1) !important;
}

.el-picker-panel__icon-btn {
  color: rgba(var(--fontColor, #000000), 0.8) !important;
}

.el-picker-panel__footer {
  background: rgba(var(--overlayBg, rgba(2, 25, 66)), 1) !important;
}

.el-date-table td.end-date .el-date-table-cell__text,
.el-date-table td.start-date .el-date-table-cell__text {
  background-color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-date-table td.today .el-date-table-cell__text {
  color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-date-table td.available:hover {
  color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-date-table td.in-range .el-date-table-cell {
  background-color: rgba(var(--themeColor, rgba(14, 173, 253)), 0.3) !important;
}

.el-picker-panel .el-time-panel {
  background-color: rgba(var(--overlayBg, rgba(2, 25, 66)), 1) !important;
  border: solid 1px rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-time-spinner__item.is-active:not(.is-disabled) {
  color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-time-panel__btn {
  color: rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
}

.el-popper.is-light .el-popper__arrow::before {
  border: 1px solid rgba(var(--themeColor, rgba(14, 173, 253)), 1) !important;
  background: rgba(var(--overlayBg, rgba(2, 25, 66)), 1) !important;
}

/* picker 标签页 END *******************************   */
#app {
  height: 100vh;
  overflow: hidden;
  background-image: var(--app_bg, url(""));
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
}

/* el-dialog  START*/
.el-dialog {
  border-radius: 6px !important;
}

.el-dialog .el-form-item__label {
  color: #285472;
  font-weight: 600;
}

.el-dialog .el-button:first-of-type {
  background-color: #006db2;
}

.el-dialog .el-dialog__header {
  border-radius: 6px 6px 0 0;
  background-color: #37a0ea;
  margin: 0;
}

.el-dialog .el-dialog__header .el-dialog__title {
  color: #fff;
  font-size: 19px;
  font-weight: bold;
}

.el-dialog .el-dialog__header .el-dialog__headerbtn {
  display: none;
}

/* el-dialog  END*/
/* 消息提示弹窗 START*/
.el-message-box__header {
  background-color: #006db2;
  padding-left: 10px;
}

.el-message-box__header .el-message-box__title {
  font-size: 18px;
  font-weight: bolder;
  color: #fff;
}

.el-message-box__header .el-message-box__headerbtn .el-message-box__close {
  color: #fff;
}

.el-message-box__content {
  padding-left: 25px;
}

.el-message-box__content .el-message-box__message {
  color: #285472;
  font-weight: 600;
}

.el-message-box {
  border: 0px solid gainsboro !important;
}

/* 消息提示弹窗 END*/