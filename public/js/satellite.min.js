/*!
 * satellite-js v4.1.3
 * (c) 2013 Shashwat Kandadai and UCSC
 * https://github.com/shashwatak/satellite-js
 * License: MIT
 */

!function(o,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(o="undefined"!=typeof globalThis?globalThis:o||self).satellite=t()}(this,(function(){"use strict";var o=Math.PI,t=2*o,e=o/180,s=180/o,n=1440,a=398600.5,d=6378.137,r=60/Math.sqrt(650944.**********),i=d*r/60,c=1/r,h=.00108262998905,m=-253215306e-14,l=-161098761e-14,p=m/h,x=2/3;function g(o,t){for(var e=[31,o%4==0?29:28,31,30,31,30,31,31,30,31,30,31],s=Math.floor(t),n=1,a=0;s>a+e[n-1]&&n<12;)a+=e[n-1],n+=1;var d=n,r=s-a,i=24*(t-s),c=Math.floor(i);i=60*(i-c);var h=Math.floor(i);return{mon:d,day:r,hr:c,minute:h,sec:60*(i-h)}}function M(o,t,e,s,n,a){var d=arguments.length>6&&void 0!==arguments[6]?arguments[6]:0;return 367*o-Math.floor(7*(o+Math.floor((t+9)/12))*.25)+Math.floor(275*t/9)+e+1721013.5+((d/6e4+a/60+n)/60+s)/24}function f(o,t,e,s,n,a,d){if(o instanceof Date){var r=o;return M(r.getUTCFullYear(),r.getUTCMonth()+1,r.getUTCDate(),r.getUTCHours(),r.getUTCMinutes(),r.getUTCSeconds(),r.getUTCMilliseconds())}return M(o,t,e,s,n,a,d)}function u(e,s){var n,a,d,r,i,c,h,m,l,p,x,g,M,f,u,z,v,y,b=e.e3,q=e.ee2,w=e.peo,T=e.pgho,j=e.pho,E=e.pinco,F=e.plo,A=e.se2,C=e.se3,L=e.sgh2,S=e.sgh3,U=e.sgh4,D=e.sh2,I=e.sh3,R=e.si2,O=e.si3,_=e.sl2,k=e.sl3,P=e.sl4,Z=e.t,G=e.xgh2,H=e.xgh3,Y=e.xgh4,$=e.xh2,B=e.xh3,J=e.xi2,K=e.xi3,N=e.xl2,Q=e.xl3,V=e.xl4,W=e.zmol,X=e.zmos,oo=s.init,to=s.opsmode,eo=s.ep,so=s.inclp,no=s.nodep,ao=s.argpp,ro=s.mp;y=X+119459e-10*Z,"y"===oo&&(y=X),v=y+.0335*Math.sin(y);var io=A*(h=.5*(f=Math.sin(v))*f-.25)+C*(m=-.5*f*Math.cos(v)),co=R*h+O*m,ho=_*h+k*m+P*f,mo=L*h+S*m+U*f,lo=D*h+I*m;return y=W+.00015835218*Z,"y"===oo&&(y=W),v=y+.1098*Math.sin(y),l=io+(q*(h=.5*(f=Math.sin(v))*f-.25)+b*(m=-.5*f*Math.cos(v))),g=co+(J*h+K*m),M=ho+(N*h+Q*m+V*f),p=mo+(G*h+H*m+Y*f),x=lo+($*h+B*m),"n"===oo&&(M-=F,p-=T,x-=j,so+=g-=E,eo+=l-=w,r=Math.sin(so),d=Math.cos(so),so>=.2?(ao+=p-=d*(x/=r),no+=x,ro+=M):(n=r*(c=Math.sin(no)),a=r*(i=Math.cos(no)),n+=x*i+g*d*c,a+=-x*c+g*d*i,(no%=t)<0&&"a"===to&&(no+=t),u=ro+ao+d*no,u+=M+p-g*no*r,z=no,(no=Math.atan2(n,a))<0&&"a"===to&&(no+=t),Math.abs(z-no)>o&&(no<z?no+=t:no-=t),ao=u-(ro+=M)-d*no)),{ep:eo,inclp:so,nodep:no,argpp:ao,mp:ro}}function z(o){var s=(o-2451545)/36525,n=-62e-7*s*s*s+.093104*s*s+3164400184.812866*s+67310.54841;return(n=n*e/240%t)<0&&(n+=t),n}function v(){return(arguments.length<=0?void 0:arguments[0])instanceof Date||arguments.length>1?z(f.apply(void 0,arguments)):z.apply(void 0,arguments)}function y(e,s){var n,a,c,m,l,g,M,f,z,v,y,b,q,w,T,j,E,F,A,C,L,S,U,D,I,R;e.t=s,e.error=0;var O=e.mo+e.mdot*e.t,_=e.argpo+e.argpdot*e.t,k=e.nodeo+e.nodedot*e.t;f=_,C=O;var P=e.t*e.t;if(S=k+e.nodecf*P,j=1-e.cc1*e.t,E=e.bstar*e.cc4*e.t,F=e.t2cof*P,1!==e.isimp){g=e.omgcof*e.t;var Z=1+e.eta*Math.cos(O);C=O+(T=g+e.xmcof*(Z*Z*Z-e.delmo)),f=_-T,b=(y=P*e.t)*e.t,j=j-e.d2*P-e.d3*y-e.d4*b,E+=e.bstar*e.cc5*(Math.sin(C)-e.sinmao),F=F+e.t3cof*y+b*(e.t4cof+e.t*e.t5cof)}L=e.no;var G=e.ecco;if(A=e.inclo,"d"===e.method){q=e.t;var H=function(o){var e,s,n,a,d,r,i,c,h=o.irez,m=o.d2201,l=o.d2211,p=o.d3210,x=o.d3222,g=o.d4410,M=o.d4422,f=o.d5220,u=o.d5232,z=o.d5421,v=o.d5433,y=o.dedt,b=o.del1,q=o.del2,w=o.del3,T=o.didt,j=o.dmdt,E=o.dnodt,F=o.domdt,A=o.argpo,C=o.argpdot,L=o.t,S=o.tc,U=o.gsto,D=o.xfact,I=o.xlamo,R=o.no,O=o.atime,_=o.em,k=o.argpm,P=o.inclm,Z=o.xli,G=o.mm,H=o.xni,Y=o.nodem,$=o.nm,B=.13130908,J=2.8843198,K=.37448087,N=5.7686396,Q=.95240898,V=1.8014998,W=1.050833,X=4.4108898,oo=259200,to=0,eo=0,so=(U+.0043752690880113*S)%t;if(_+=y*L,P+=T*L,k+=F*L,Y+=E*L,G+=j*L,0!==h){(0===O||L*O<=0||Math.abs(L)<Math.abs(O))&&(O=0,H=R,Z=I),e=L>0?720:-720;for(var no=381;381===no;)2!==h?(i=b*Math.sin(Z-B)+q*Math.sin(2*(Z-J))+w*Math.sin(3*(Z-K)),d=H+D,r=b*Math.cos(Z-B)+2*q*Math.cos(2*(Z-J))+3*w*Math.cos(3*(Z-K)),r*=d):(n=(c=A+C*O)+c,s=Z+Z,i=m*Math.sin(n+Z-N)+l*Math.sin(Z-N)+p*Math.sin(c+Z-Q)+x*Math.sin(-c+Z-Q)+g*Math.sin(n+s-V)+M*Math.sin(s-V)+f*Math.sin(c+Z-W)+u*Math.sin(-c+Z-W)+z*Math.sin(c+s-X)+v*Math.sin(-c+s-X),d=H+D,r=m*Math.cos(n+Z-N)+l*Math.cos(Z-N)+p*Math.cos(c+Z-Q)+x*Math.cos(-c+Z-Q)+f*Math.cos(c+Z-W)+u*Math.cos(-c+Z-W)+2*g*Math.cos(n+s-V)+M*Math.cos(s-V)+z*Math.cos(c+s-X)+v*Math.cos(-c+s-X),r*=d),Math.abs(L-O)>=720?no=381:(eo=L-O,no=0),381===no&&(Z+=d*e+i*oo,H+=i*e+r*oo,O+=e);$=H+i*eo+r*eo*eo*.5,a=Z+d*eo+i*eo*eo*.5,1!==h?(G=a-2*Y+2*so,to=$-R):(G=a-Y-k+so,to=$-R),$=R+to}return{atime:O,em:_,argpm:k,inclm:P,xli:Z,mm:G,xni:H,nodem:Y,dndt:to,nm:$}}({irez:e.irez,d2201:e.d2201,d2211:e.d2211,d3210:e.d3210,d3222:e.d3222,d4410:e.d4410,d4422:e.d4422,d5220:e.d5220,d5232:e.d5232,d5421:e.d5421,d5433:e.d5433,dedt:e.dedt,del1:e.del1,del2:e.del2,del3:e.del3,didt:e.didt,dmdt:e.dmdt,dnodt:e.dnodt,domdt:e.domdt,argpo:e.argpo,argpdot:e.argpdot,t:e.t,tc:q,gsto:e.gsto,xfact:e.xfact,xlamo:e.xlamo,no:e.no,atime:e.atime,em:G,argpm:f,inclm:A,xli:e.xli,mm:C,xni:e.xni,nodem:S,nm:L});G=H.em,f=H.argpm,A=H.inclm,C=H.mm,S=H.nodem,L=H.nm}if(L<=0)return e.error=2,[!1,!1];var Y=Math.pow(r/L,x)*j*j;if(L=r/Math.pow(Y,1.5),(G-=E)>=1||G<-.001)return e.error=1,[!1,!1];G<1e-6&&(G=1e-6),D=(C+=e.no*F)+f+S;var $=G;if(U=A,z=f%=t,R=S%=t,I=C=((D%=t)-f-S)%t,m=Math.sin(A),c=Math.cos(A),"d"===e.method){var B=u(e,{inclo:e.inclo,init:"n",ep:$,inclp:U,nodep:R,argpp:z,mp:I,opsmode:e.operationmode});if($=B.ep,R=B.nodep,z=B.argpp,I=B.mp,(U=B.inclp)<0&&(U=-U,R+=o,z-=o),$<0||$>1)return e.error=3,[!1,!1]}"d"===e.method&&(m=Math.sin(U),c=Math.cos(U),e.aycof=-.5*p*m,Math.abs(c+1)>15e-13?e.xlcof=-.25*p*m*(3+5*c)/(1+c):e.xlcof=-.25*p*m*(3+5*c)/15e-13);var J=$*Math.cos(z);T=1/(Y*(1-$*$));var K=$*Math.sin(z)+T*e.aycof,N=(I+z+R+T*e.xlcof*J-R)%t;M=N,w=9999.9;for(var Q=1;Math.abs(w)>=1e-12&&Q<=10;)a=Math.sin(M),w=(N-K*(n=Math.cos(M))+J*a-M)/(w=1-n*J-a*K),Math.abs(w)>=.95&&(w=w>0?.95:-.95),M+=w,Q+=1;var V=J*n+K*a,W=J*a-K*n,X=J*J+K*K,oo=Y*(1-X);if(oo<0)return e.error=4,[!1,!1];var to=Y*(1-V),eo=Math.sqrt(Y)*W/to,so=Math.sqrt(oo)/to,no=Math.sqrt(1-X),ao=Y/to*(a-K-J*(T=W/(1+no))),ro=Y/to*(n-J+K*T);v=Math.atan2(ao,ro);var io=(ro+ro)*ao,co=1-2*ao*ao,ho=.5*h*(T=1/oo),mo=ho*T;"d"===e.method&&(l=c*c,e.con41=3*l-1,e.x1mth2=1-l,e.x7thm1=7*l-1);var lo=to*(1-1.5*mo*no*e.con41)+.5*ho*e.x1mth2*co;if(lo<1)return e.error=6,{position:!1,velocity:!1};v-=.25*mo*e.x7thm1*io;var po=R+1.5*mo*c*io,xo=U+1.5*mo*c*m*co,go=eo-L*ho*e.x1mth2*io/r,Mo=so+L*ho*(e.x1mth2*co+1.5*e.con41)/r,fo=Math.sin(v),uo=Math.cos(v),zo=Math.sin(po),vo=Math.cos(po),yo=Math.sin(xo),bo=Math.cos(xo),qo=-zo*bo,wo=vo*bo,To=qo*fo+vo*uo,jo=wo*fo+zo*uo,Eo=yo*fo;return{position:{x:lo*To*d,y:lo*jo*d,z:lo*Eo*d},velocity:{x:(go*To+Mo*(qo*uo-vo*fo))*i,y:(go*jo+Mo*(wo*uo-zo*fo))*i,z:(go*Eo+Mo*(yo*uo))*i}}}function b(e,s){var n,a,i,c,m,g,M,f,z,b,q,w,T,j,E,F,A,C,L,S,U,D,I,R,O,_,k,P,Z,G,H,Y,$,B,J,K,N,Q,V,W,X,oo,to,eo,so,no,ao,ro,io,co,ho,mo,lo=s.opsmode,po=s.satn,xo=s.epoch,go=s.xbstar,Mo=s.xecco,fo=s.xargpo,uo=s.xinclo,zo=s.xmo,vo=s.xno,yo=s.xnodeo;e.isimp=0,e.method="n",e.aycof=0,e.con41=0,e.cc1=0,e.cc4=0,e.cc5=0,e.d2=0,e.d3=0,e.d4=0,e.delmo=0,e.eta=0,e.argpdot=0,e.omgcof=0,e.sinmao=0,e.t=0,e.t2cof=0,e.t3cof=0,e.t4cof=0,e.t5cof=0,e.x1mth2=0,e.x7thm1=0,e.mdot=0,e.nodedot=0,e.xlcof=0,e.xmcof=0,e.nodecf=0,e.irez=0,e.d2201=0,e.d2211=0,e.d3210=0,e.d3222=0,e.d4410=0,e.d4422=0,e.d5220=0,e.d5232=0,e.d5421=0,e.d5433=0,e.dedt=0,e.del1=0,e.del2=0,e.del3=0,e.didt=0,e.dmdt=0,e.dnodt=0,e.domdt=0,e.e3=0,e.ee2=0,e.peo=0,e.pgho=0,e.pho=0,e.pinco=0,e.plo=0,e.se2=0,e.se3=0,e.sgh2=0,e.sgh3=0,e.sgh4=0,e.sh2=0,e.sh3=0,e.si2=0,e.si3=0,e.sl2=0,e.sl3=0,e.sl4=0,e.gsto=0,e.xfact=0,e.xgh2=0,e.xgh3=0,e.xgh4=0,e.xh2=0,e.xh3=0,e.xi2=0,e.xi3=0,e.xl2=0,e.xl3=0,e.xl4=0,e.xlamo=0,e.zmol=0,e.zmos=0,e.atime=0,e.xli=0,e.xni=0,e.bstar=go,e.ecco=Mo,e.argpo=fo,e.inclo=uo,e.mo=zo,e.no=vo,e.nodeo=yo,e.operationmode=lo;e.init="y",e.t=0;var bo=function(o){var e=o.ecco,s=o.epoch,n=o.inclo,a=o.opsmode,d=o.no,i=e*e,c=1-i,m=Math.sqrt(c),l=Math.cos(n),p=l*l,g=Math.pow(r/d,x),M=.75*h*(3*p-1)/(m*c),f=M/(g*g),u=g*(1-f*f-f*(1/3+134*f*f/81));d/=1+(f=M/(u*u));var z,y=Math.pow(r/d,x),b=Math.sin(n),q=y*c,w=1-5*p,T=-w-p-p,j=1/y,E=q*q,F=y*(1-e);if("a"===a){var A=s-7305,C=Math.floor(A+1e-8),L=.017202791694070362;(z=(1.7321343856509375+L*C+(L+t)*(A-C)+A*A*5075514194322695e-30)%t)<0&&(z+=t)}else z=v(s+2433281.5);return{no:d,method:"n",ainv:j,ao:y,con41:T,con42:w,cosio:l,cosio2:p,eccsq:i,omeosq:c,posq:E,rp:F,rteosq:m,sinio:b,gsto:z}}({satn:po,ecco:e.ecco,epoch:xo,inclo:e.inclo,no:e.no,method:e.method,opsmode:e.operationmode}),qo=bo.ao,wo=bo.con42,To=bo.cosio,jo=bo.cosio2,Eo=bo.eccsq,Fo=bo.omeosq,Ao=bo.posq,Co=bo.rp,Lo=bo.rteosq,So=bo.sinio;if(e.no=bo.no,e.con41=bo.con41,e.gsto=bo.gsto,e.error=0,Fo>=0||e.no>=0){if(e.isimp=0,Co<220/d+1&&(e.isimp=1),R=1.0122292763545218,C=1.8802768006108976e-9,(E=(Co-1)*d)<156){R=E-78,E<98&&(R=20);var Uo=(120-R)/d;C=Uo*Uo*Uo*Uo,R=R/d+1}F=1/Ao,oo=1/(qo-R),e.eta=qo*e.ecco*oo,w=e.eta*e.eta,q=e.ecco*e.eta,A=Math.abs(1-w),c=(M=(g=C*Math.pow(oo,4))/Math.pow(A,3.5))*e.no*(qo*(1+1.5*w+q*(4+w))+.375*h*oo/A*e.con41*(8+3*w*(8+w))),e.cc1=e.bstar*c,m=0,e.ecco>1e-4&&(m=-2*g*oo*p*e.no*So/e.ecco),e.x1mth2=1-jo,e.cc4=2*e.no*M*qo*Fo*(e.eta*(2+.5*w)+e.ecco*(.5+2*w)-h*oo/(qo*A)*(-3*e.con41*(1-2*q+w*(1.5-.5*q))+.75*e.x1mth2*(2*w-q*(1+w))*Math.cos(2*e.argpo))),e.cc5=2*M*qo*Fo*(1+2.75*(w+q)+q*w),f=jo*jo,W=.5*(V=1.5*h*F*e.no)*h*F,X=-.46875*l*F*F*e.no,e.mdot=e.no+.5*V*Lo*e.con41+.0625*W*Lo*(13-78*jo+137*f),e.argpdot=-.5*V*wo+.0625*W*(7-114*jo+395*f)+X*(3-36*jo+49*f),eo=-V*To,e.nodedot=eo+(.5*W*(4-19*jo)+2*X*(3-7*jo))*To,to=e.argpdot+e.nodedot,e.omgcof=e.bstar*m*Math.cos(e.argpo),e.xmcof=0,e.ecco>1e-4&&(e.xmcof=-x*g*e.bstar/q),e.nodecf=3.5*Fo*eo*e.cc1,e.t2cof=1.5*e.cc1,Math.abs(To+1)>15e-13?e.xlcof=-.25*p*So*(3+5*To)/(1+To):e.xlcof=-.25*p*So*(3+5*To)/15e-13,e.aycof=-.5*p*So;var Do=1+e.eta*Math.cos(e.mo);if(e.delmo=Do*Do*Do,e.sinmao=Math.sin(e.mo),e.x7thm1=7*jo-1,2*o/e.no>=225){e.method="d",e.isimp=1,0,T=e.inclo;var Io=function(o){var e,s,n,a,d,r,i,c,h,m,l,p,x,g,M,f,u,z,v,y,b,q,w,T,j,E,F,A,C,L,S,U,D,I,R,O,_,k,P,Z,G,H,Y,$,B,J,K,N,Q,V,W,X,oo,to,eo,so,no,ao,ro,io,co,ho,mo,lo=o.epoch,po=o.ep,xo=o.argpp,go=o.tc,Mo=o.inclp,fo=o.nodep,uo=.01675,zo=.0549,vo=o.np,yo=po,bo=Math.sin(fo),qo=Math.cos(fo),wo=Math.sin(xo),To=Math.cos(xo),jo=Math.sin(Mo),Eo=Math.cos(Mo),Fo=yo*yo,Ao=1-Fo,Co=Math.sqrt(Ao),Lo=lo+18261.5+go/1440,So=(4.523602-.00092422029*Lo)%t,Uo=Math.sin(So),Do=Math.cos(So),Io=.91375164-.03568096*Do,Ro=Math.sqrt(1-Io*Io),Oo=.089683511*Uo/Ro,_o=Math.sqrt(1-Oo*Oo),ko=5.8351514+.001944368*Lo,Po=.39785416*Uo/Ro,Zo=_o*Do+.91744867*Oo*Uo;Po=Math.atan2(Po,Zo),Po+=ko-So;var Go=Math.cos(Po),Ho=Math.sin(Po);y=.1945905,b=-.98088458,T=.91744867,j=.39785416,q=qo,w=bo,l=29864797e-13;for(var Yo=1/vo,$o=0;$o<2;)eo=-6*(e=y*q+b*T*w)*(d=-jo*(i=-y*w+b*T*q)+Eo*(c=b*j))+Fo*(-24*(p=e*To+(s=Eo*i+jo*c)*wo)*(z=d*To)-6*(g=-e*wo+s*To)*(f=d*wo)),so=-6*(e*(r=-jo*(h=b*w+y*T*q)+Eo*(m=y*j))+(n=-b*q+y*T*w)*d)+Fo*(-24*((x=n*To+(a=Eo*h+jo*m)*wo)*z+p*(v=r*To))+-6*(g*(u=r*wo)+(M=-n*wo+a*To)*f)),no=-6*n*r+Fo*(-24*x*v-6*M*u),ao=6*s*d+Fo*(24*p*f-6*g*z),ro=6*(a*d+s*r)+Fo*(24*(x*f+p*u)-6*(M*z+g*v)),io=6*a*r+Fo*(24*x*u-6*M*v),X=(X=3*(e*e+s*s)+(co=12*p*p-3*g*g)*Fo)+X+Ao*co,oo=(oo=6*(e*n+s*a)+(ho=24*p*x-6*g*M)*Fo)+oo+Ao*ho,to=(to=3*(n*n+a*a)+(mo=12*x*x-3*M*M)*Fo)+to+Ao*mo,J=-.5*(K=l*Yo)/Co,B=-15*yo*(N=K*Co),Q=p*g+x*M,V=x*g+p*M,W=x*M-p*g,1===($o+=1)&&(E=B,F=J,A=K,C=N,L=Q,S=V,U=W,D=X,I=oo,R=to,O=eo,_=so,k=no,P=ao,Z=ro,G=io,H=co,Y=ho,$=mo,y=Go,b=Ho,T=Io,j=Ro,q=_o*qo+Oo*bo,w=bo*_o-qo*Oo,l=4.7968065e-7);return{snodm:bo,cnodm:qo,sinim:jo,cosim:Eo,sinomm:wo,cosomm:To,day:Lo,e3:2*B*W,ee2:2*B*V,em:yo,emsq:Fo,gam:ko,peo:0,pgho:0,pho:0,pinco:0,plo:0,rtemsq:Co,se2:2*E*S,se3:2*E*U,sgh2:2*C*Y,sgh3:2*C*($-H),sgh4:-18*C*uo,sh2:-2*F*Z,sh3:-2*F*(G-P),si2:2*F*_,si3:2*F*(k-O),sl2:-2*A*I,sl3:-2*A*(R-D),sl4:-2*A*(-21-9*Fo)*uo,s1:B,s2:J,s3:K,s4:N,s5:Q,s6:V,s7:W,ss1:E,ss2:F,ss3:A,ss4:C,ss5:L,ss6:S,ss7:U,sz1:D,sz2:I,sz3:R,sz11:O,sz12:_,sz13:k,sz21:P,sz22:Z,sz23:G,sz31:H,sz32:Y,sz33:$,xgh2:2*N*ho,xgh3:2*N*(mo-co),xgh4:-18*N*zo,xh2:-2*J*ro,xh3:-2*J*(io-ao),xi2:2*J*so,xi3:2*J*(no-eo),xl2:-2*K*oo,xl3:-2*K*(to-X),xl4:-2*K*(-21-9*Fo)*zo,nm:vo,z1:X,z2:oo,z3:to,z11:eo,z12:so,z13:no,z21:ao,z22:ro,z23:io,z31:co,z32:ho,z33:mo,zmol:(.2299715*Lo-ko+4.7199672)%t,zmos:(6.2565837+.017201977*Lo)%t}}({epoch:xo,ep:e.ecco,argpp:e.argpo,tc:0,inclp:e.inclo,nodep:e.nodeo,np:e.no,e3:e.e3,ee2:e.ee2,peo:e.peo,pgho:e.pgho,pho:e.pho,pinco:e.pinco,plo:e.plo,se2:e.se2,se3:e.se3,sgh2:e.sgh2,sgh3:e.sgh3,sgh4:e.sgh4,sh2:e.sh2,sh3:e.sh3,si2:e.si2,si3:e.si3,sl2:e.sl2,sl3:e.sl3,sl4:e.sl4,xgh2:e.xgh2,xgh3:e.xgh3,xgh4:e.xgh4,xh2:e.xh2,xh3:e.xh3,xi2:e.xi2,xi3:e.xi3,xl2:e.xl2,xl3:e.xl3,xl4:e.xl4,zmol:e.zmol,zmos:e.zmos});e.e3=Io.e3,e.ee2=Io.ee2,e.peo=Io.peo,e.pgho=Io.pgho,e.pho=Io.pho,e.pinco=Io.pinco,e.plo=Io.plo,e.se2=Io.se2,e.se3=Io.se3,e.sgh2=Io.sgh2,e.sgh3=Io.sgh3,e.sgh4=Io.sgh4,e.sh2=Io.sh2,e.sh3=Io.sh3,e.si2=Io.si2,e.si3=Io.si3,e.sl2=Io.sl2,e.sl3=Io.sl3,e.sl4=Io.sl4,a=Io.sinim,n=Io.cosim,z=Io.em,b=Io.emsq,L=Io.s1,S=Io.s2,U=Io.s3,D=Io.s4,I=Io.s5,O=Io.ss1,_=Io.ss2,k=Io.ss3,P=Io.ss4,Z=Io.ss5,G=Io.sz1,H=Io.sz3,Y=Io.sz11,$=Io.sz13,B=Io.sz21,J=Io.sz23,K=Io.sz31,N=Io.sz33,e.xgh2=Io.xgh2,e.xgh3=Io.xgh3,e.xgh4=Io.xgh4,e.xh2=Io.xh2,e.xh3=Io.xh3,e.xi2=Io.xi2,e.xi3=Io.xi3,e.xl2=Io.xl2,e.xl3=Io.xl3,e.xl4=Io.xl4,e.zmol=Io.zmol,e.zmos=Io.zmos,j=Io.nm,so=Io.z1,no=Io.z3,ao=Io.z11,ro=Io.z13,io=Io.z21,co=Io.z23,ho=Io.z31,mo=Io.z33;var Ro=u(e,{inclo:T,init:e.init,ep:e.ecco,inclp:e.inclo,nodep:e.nodeo,argpp:e.argpo,mp:e.mo,opsmode:e.operationmode});e.ecco=Ro.ep,e.inclo=Ro.inclp,e.nodeo=Ro.nodep,e.argpo=Ro.argpp,e.mo=Ro.mp,0,0,0;var Oo=function(e){var s,n,a,d,i,c,h,m,l,p,g,M,f,u,z,v,y,b=e.cosim,q=e.argpo,w=e.s1,T=e.s2,j=e.s3,E=e.s4,F=e.s5,A=e.sinim,C=e.ss1,L=e.ss2,S=e.ss3,U=e.ss4,D=e.ss5,I=e.sz1,R=e.sz3,O=e.sz11,_=e.sz13,k=e.sz21,P=e.sz23,Z=e.sz31,G=e.sz33,H=e.t,Y=e.tc,$=e.gsto,B=e.mo,J=e.mdot,K=e.no,N=e.nodeo,Q=e.nodedot,V=e.xpidot,W=e.z1,X=e.z3,oo=e.z11,to=e.z13,eo=e.z21,so=e.z23,no=e.z31,ao=e.z33,ro=e.ecco,io=e.eccsq,co=e.emsq,ho=e.em,mo=e.argpm,lo=e.inclm,po=e.mm,xo=e.nm,go=e.nodem,Mo=e.irez,fo=e.atime,uo=e.d2201,zo=e.d2211,vo=e.d3210,yo=e.d3222,bo=e.d4410,qo=e.d4422,wo=e.d5220,To=e.d5232,jo=e.d5421,Eo=e.d5433,Fo=e.dedt,Ao=e.didt,Co=e.dmdt,Lo=e.dnodt,So=e.domdt,Uo=e.del1,Do=e.del2,Io=e.del3,Ro=e.xfact,Oo=e.xlamo,_o=e.xli,ko=e.xni,Po=.0043752690880113,Zo=.00015835218,Go=119459e-10;Mo=0,xo<.0052359877&&xo>.0034906585&&(Mo=1),xo>=.00826&&xo<=.00924&&ho>=.5&&(Mo=2);var Ho=-Go*L*(k+P);(lo<.052359877||lo>o-.052359877)&&(Ho=0),0!==A&&(Ho/=A);var Yo=-Zo*T*(eo+so);(lo<.052359877||lo>o-.052359877)&&(Yo=0),So=U*Go*(Z+G-6)-b*Ho+E*Zo*(no+ao-6),Lo=Ho,0!==A&&(So-=b/A*Yo,Lo+=Yo/A);var $o=($+Y*Po)%t;if(ho+=(Fo=C*Go*D+w*Zo*F)*H,lo+=(Ao=L*Go*(O+_)+T*Zo*(oo+to))*H,mo+=So*H,go+=Lo*H,po+=(Co=-Go*S*(I+R-14-6*co)-Zo*j*(W+X-14-6*co))*H,0!==Mo){if(z=Math.pow(xo/r,x),2===Mo){var Bo=ho,Jo=co;y=(ho=ro)*(co=io),ho<=.65?(a=3.616-13.247*ho+16.29*co,d=117.39*ho-19.302-228.419*co+156.591*y,i=109.7927*ho-18.9068-214.6334*co+146.5816*y,c=242.694*ho-41.122-471.094*co+313.953*y,h=841.88*ho-146.407-1629.014*co+1083.435*y,m=3017.977*ho-532.114-5740.032*co+3708.276*y):(a=331.819*ho-72.099-508.738*co+266.724*y,d=1582.851*ho-346.844-2415.925*co+1246.113*y,i=1554.908*ho-342.585-2366.899*co+1215.972*y,c=4758.686*ho-1052.797-7193.992*co+3651.957*y,h=16178.11*ho-3581.69-24462.77*co+12422.52*y,m=ho>.715?29936.92*ho-5149.66-54087.36*co+31324.56*y:1464.74-4664.75*ho+3763.64*co),ho<.7?(g=4988.61*ho-919.2277-9064.77*co+5542.21*y,l=4568.6173*ho-822.71072-8491.4146*co+5337.524*y,p=4690.25*ho-853.666-8624.77*co+5341.4*y):(g=161616.52*ho-37995.78-229838.2*co+109377.94*y,l=218913.95*ho-51752.104-309468.16*co+146349.42*y,p=170470.89*ho-40023.88-242699.48*co+115605.82*y),uo=(f=17891679e-13*(u=xo*xo*3*(z*z)))*(s=.75*(1+2*b+(v=b*b)))*(-.306-.44*(ho-.64)),zo=f*(1.5*(M=A*A))*a,vo=(f=3.7393792e-7*(u*=z))*(1.875*A*(1-2*b-3*v))*d,yo=f*(-1.875*A*(1+2*b-3*v))*i,bo=(f=2*(u*=z)*7.3636953e-9)*(35*M*s)*c,qo=f*(39.375*M*M)*h,wo=(f=1.1428639e-7*(u*=z))*(9.84375*A*(M*(1-2*b-5*v)+.33333333*(4*b-2+6*v)))*m,To=f*(A*(4.92187512*M*(-2-4*b+10*v)+6.56250012*(1+2*b-3*v)))*p,jo=(f=2*u*2.1765803e-9)*(29.53125*A*(2-8*b+v*(8*b-12+10*v)))*l,Eo=f*(29.53125*A*(-2-8*b+v*(12+8*b-10*v)))*g,Oo=(B+N+N-($o+$o))%t,Ro=J+Co+2*(Q+Lo-Po)-K,ho=Bo,co=Jo}1===Mo&&(n=1+b,Do=2*(Uo=3*xo*xo*z*z)*(s=.75*(1+b)*(1+b))*(1+co*(.8125*co-2.5))*17891679e-13,Io=3*Uo*(n*=1.875*n*n)*(1+co*(6.60937*co-6))*2.2123015e-7*z,Uo=Uo*(.9375*A*A*(1+3*b)-.75*(1+b))*(d=1+2*co)*21460748e-13*z,Oo=(B+N+q-$o)%t,Ro=J+V+Co+So+Lo-(K+Po)),_o=Oo,ko=K,fo=0,xo=K+0}return{em:ho,argpm:mo,inclm:lo,mm:po,nm:xo,nodem:go,irez:Mo,atime:fo,d2201:uo,d2211:zo,d3210:vo,d3222:yo,d4410:bo,d4422:qo,d5220:wo,d5232:To,d5421:jo,d5433:Eo,dedt:Fo,didt:Ao,dmdt:Co,dndt:0,dnodt:Lo,domdt:So,del1:Uo,del2:Do,del3:Io,xfact:Ro,xlamo:Oo,xli:_o,xni:ko}}({cosim:n,emsq:b,argpo:e.argpo,s1:L,s2:S,s3:U,s4:D,s5:I,sinim:a,ss1:O,ss2:_,ss3:k,ss4:P,ss5:Z,sz1:G,sz3:H,sz11:Y,sz13:$,sz21:B,sz23:J,sz31:K,sz33:N,t:e.t,tc:0,gsto:e.gsto,mo:e.mo,mdot:e.mdot,no:e.no,nodeo:e.nodeo,nodedot:e.nodedot,xpidot:to,z1:so,z3:no,z11:ao,z13:ro,z21:io,z23:co,z31:ho,z33:mo,ecco:e.ecco,eccsq:Eo,em:z,argpm:0,inclm:T,mm:0,nm:j,nodem:0,irez:e.irez,atime:e.atime,d2201:e.d2201,d2211:e.d2211,d3210:e.d3210,d3222:e.d3222,d4410:e.d4410,d4422:e.d4422,d5220:e.d5220,d5232:e.d5232,d5421:e.d5421,d5433:e.d5433,dedt:e.dedt,didt:e.didt,dmdt:e.dmdt,dnodt:e.dnodt,domdt:e.domdt,del1:e.del1,del2:e.del2,del3:e.del3,xfact:e.xfact,xlamo:e.xlamo,xli:e.xli,xni:e.xni});e.irez=Oo.irez,e.atime=Oo.atime,e.d2201=Oo.d2201,e.d2211=Oo.d2211,e.d3210=Oo.d3210,e.d3222=Oo.d3222,e.d4410=Oo.d4410,e.d4422=Oo.d4422,e.d5220=Oo.d5220,e.d5232=Oo.d5232,e.d5421=Oo.d5421,e.d5433=Oo.d5433,e.dedt=Oo.dedt,e.didt=Oo.didt,e.dmdt=Oo.dmdt,e.dnodt=Oo.dnodt,e.domdt=Oo.domdt,e.del1=Oo.del1,e.del2=Oo.del2,e.del3=Oo.del3,e.xfact=Oo.xfact,e.xlamo=Oo.xlamo,e.xli=Oo.xli,e.xni=Oo.xni}1!==e.isimp&&(i=e.cc1*e.cc1,e.d2=4*qo*oo*i,Q=e.d2*oo*e.cc1/3,e.d3=(17*qo+R)*Q,e.d4=.5*Q*qo*oo*(221*qo+31*R)*e.cc1,e.t3cof=e.d2+2*i,e.t4cof=.25*(3*e.d3+e.cc1*(12*e.d2+10*i)),e.t5cof=.2*(3*e.d4+12*e.cc1*e.d3+6*e.d2*e.d2+15*i*(2*e.d2+i)))}y(e,0),e.init="n"}function q(o){return function(o){if(Array.isArray(o))return w(o)}(o)||function(o){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(o))return Array.from(o)}(o)||function(o,t){if(!o)return;if("string"==typeof o)return w(o,t);var e=Object.prototype.toString.call(o).slice(8,-1);"Object"===e&&o.constructor&&(e=o.constructor.name);if("Map"===e||"Set"===e)return Array.from(o);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return w(o,t)}(o)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(o,t){(null==t||t>o.length)&&(t=o.length);for(var e=0,s=new Array(t);e<t;e++)s[e]=o[e];return s}function T(o){return o*s}function j(o){return o*e}function E(o){var t=o.longitude,e=o.latitude,s=o.height,n=6378.137,a=(n-6356.7523142)/n,d=2*a-a*a,r=n/Math.sqrt(1-d*(Math.sin(e)*Math.sin(e)));return{x:(r+s)*Math.cos(e)*Math.cos(t),y:(r+s)*Math.cos(e)*Math.sin(t),z:(r*(1-d)+s)*Math.sin(e)}}return{constants:Object.freeze({__proto__:null,pi:o,twoPi:t,deg2rad:e,rad2deg:s,minutesPerDay:n,mu:a,earthRadius:d,xke:r,vkmpersec:i,tumin:c,j2:h,j3:m,j4:l,j3oj2:p,x2o3:x}),propagate:function(){for(var o=arguments.length,t=new Array(o),e=0;e<o;e++)t[e]=arguments[e];var s=t[0],a=Array.prototype.slice.call(t,1),d=f.apply(void 0,q(a)),r=(d-s.jdsatepoch)*n;return y(s,r)},sgp4:y,twoline2satrec:function(t,s){var n=1440/(2*o),a=0,d={error:0};d.satnum=t.substring(2,7),d.epochyr=parseInt(t.substring(18,20),10),d.epochdays=parseFloat(t.substring(20,32)),d.ndot=parseFloat(t.substring(33,43)),d.nddot=parseFloat(".".concat(parseInt(t.substring(44,50),10),"E").concat(t.substring(50,52))),d.bstar=parseFloat("".concat(t.substring(53,54),".").concat(parseInt(t.substring(54,59),10),"E").concat(t.substring(59,61))),d.inclo=parseFloat(s.substring(8,16)),d.nodeo=parseFloat(s.substring(17,25)),d.ecco=parseFloat(".".concat(s.substring(26,33))),d.argpo=parseFloat(s.substring(34,42)),d.mo=parseFloat(s.substring(43,51)),d.no=parseFloat(s.substring(52,63)),d.no/=n,d.a=Math.pow(d.no*c,-2/3),d.ndot/=1440*n,d.nddot/=1440*n*1440,d.inclo*=e,d.nodeo*=e,d.argpo*=e,d.mo*=e,d.alta=d.a*(1+d.ecco)-1,d.altp=d.a*(1-d.ecco)-1;var r=g(a=d.epochyr<57?d.epochyr+2e3:d.epochyr+1900,d.epochdays),i=r.mon,h=r.day,m=r.hr,l=r.minute,p=r.sec;return d.jdsatepoch=f(a,i,h,m,l,p),b(d,{opsmode:"i",satn:d.satnum,epoch:d.jdsatepoch-2433281.5,xbstar:d.bstar,xecco:d.ecco,xargpo:d.argpo,xinclo:d.inclo,xmo:d.mo,xno:d.no,xnodeo:d.nodeo}),d},gstime:v,jday:f,invjday:function(o,t){var e=o-2415019.5,s=e/365.25,n=1900+Math.floor(s),a=Math.floor(.25*(n-1901)),d=e-(365*(n-1900)+a)+1e-11;d<1&&(d=e-(365*((n-=1)-1900)+(a=Math.floor(.25*(n-1901)))));var r=g(n,d),i=r.mon,c=r.day,h=r.hr,m=r.minute,l=r.sec-864e-9;return t?[n,i,c,h,m,Math.floor(l)]:new Date(Date.UTC(n,i-1,c,h,m,Math.floor(l)))},dopplerFactor:function(o,t,e){var s=7292115e-11,n={x:t.x-o.x,y:t.y-o.y,z:t.z-o.z};n.w=Math.sqrt(Math.pow(n.x,2)+Math.pow(n.y,2)+Math.pow(n.z,2));var a=e.x+s*o.y,d=e.y-s*o.x,r=e.z,i=(n.x*a+n.y*d+n.z*r)/n.w;return 1+i/299792.458*(i>=0?1:-1)},radiansToDegrees:T,degreesToRadians:j,degreesLat:function(t){if(t<-o/2||t>o/2)throw new RangeError("Latitude radians must be in range [-pi/2; pi/2].");return T(t)},degreesLong:function(t){if(t<-o||t>o)throw new RangeError("Longitude radians must be in range [-pi; pi].");return T(t)},radiansLat:function(o){if(o<-90||o>90)throw new RangeError("Latitude degrees must be in range [-90; 90].");return j(o)},radiansLong:function(o){if(o<-180||o>180)throw new RangeError("Longitude degrees must be in range [-180; 180].");return j(o)},geodeticToEcf:E,eciToGeodetic:function(e,s){for(var n=6378.137,a=Math.sqrt(e.x*e.x+e.y*e.y),d=(n-6356.7523142)/n,r=2*d-d*d,i=Math.atan2(e.y,e.x)-s;i<-o;)i+=t;for(;i>o;)i-=t;for(var c,h=0,m=Math.atan2(e.z,Math.sqrt(e.x*e.x+e.y*e.y));h<20;)c=1/Math.sqrt(1-r*(Math.sin(m)*Math.sin(m))),m=Math.atan2(e.z+n*c*r*Math.sin(m),a),h+=1;return{longitude:i,latitude:m,height:a/Math.cos(m)-n*c}},eciToEcf:function(o,t){return{x:o.x*Math.cos(t)+o.y*Math.sin(t),y:o.x*-Math.sin(t)+o.y*Math.cos(t),z:o.z}},ecfToEci:function(o,t){return{x:o.x*Math.cos(t)-o.y*Math.sin(t),y:o.x*Math.sin(t)+o.y*Math.cos(t),z:o.z}},ecfToLookAngles:function(t,e){var s,n,a,d,r,i,c=function(o,t){var e=o.longitude,s=o.latitude,n=E(o),a=t.x-n.x,d=t.y-n.y,r=t.z-n.z;return{topS:Math.sin(s)*Math.cos(e)*a+Math.sin(s)*Math.sin(e)*d-Math.cos(s)*r,topE:-Math.sin(e)*a+Math.cos(e)*d,topZ:Math.cos(s)*Math.cos(e)*a+Math.cos(s)*Math.sin(e)*d+Math.sin(s)*r}}(t,e);return n=(s=c).topS,a=s.topE,d=s.topZ,r=Math.sqrt(n*n+a*a+d*d),i=Math.asin(d/r),{azimuth:Math.atan2(-a,n)+o,elevation:i,rangeSat:r}}}}));
