/**
 * @date        2024-02-29
 *
 * @description 时间轴组件
 *              初始化时间轴（播放-暂停-加速-减速-重置）
 *              补充功能：拖拽（仅限于暂停时）
 *                       点击时间轴（跳转时间-仅限于暂停时）
 *                       显示当前时间 currentTime
 *                       更新时间轴
 *                       销毁时间轴
 *
 * @example     引用：import xxx from 'xxxx';
 *              使用：xxx.initTimeLine({
 *                      speedList: [1, 2, 4, 8, 16],
 *                      timeList: ['2024-02-25 11:05:20', '2024-02-25 11:06:40'],
 *                      callBack: function (res) {
 *                      },
 *                    })
 */

/* 使用的变量 */
let isDragging = false;
let initialX = 0; //滑块初始位置（用于拖动）
let offsetX = 0; //X轴的偏移（用于拖动）
let refuseStatus = null; //重置按钮状态
let draggerStatus = null; //拖动状态

//时间
let timerId = Math.random(); // 滑块移动定时器ID
let currentPosition = 0; // 当前滑块位置
let timeList = []; //时间列表
let speedNow = 1; //当前倍速
let speedList = []; //倍速列表
let selectedIndexGo = 0; // 倍速默认选中第一位
let sliderWidth = 0; //时间轴的宽度
let currentSecond = 0; // 当前秒数
let totalTimeInSeconds = 0; //总秒数
let timeImitateId = null; //模拟时间定时器ID
let lastTime = null; //上一次的时间

//dom
let dragBtnWrapper = null; //滑块外部
let dragBtn = null; //拖动的滑块
let sliderStrip = null; //时间轴
let playBtn = null; //播放按钮
let pauseBtn = null; //暂停按钮
let timeBox = null; //时间盒子
let speedShow = null; //速度显示
let timeLine_TimeList = null; //时间显示
let startTime = null; //开始时间
let endTime = null; //结束时间
let speedBox = null; //速度背景
let svgIcon = null; //svg

//其他变量
let recordNowPosition = null;

/**
 * @description 初始化时间轴
 * @param {Object} params 参数集合
 * @param {String} params.domId 容器Id（必传）
 * @param {Array} params.speedList 倍速集合（最小为1,默认也为1）
 * @param {Array} params.timeList 携带参数集合（包含开始时间,结束时间）
 * @param {Object} params.otherList 其他参数集合
 * @param {Array} params.callBack 回调函数（返回当前运行时间）
 */
function initTimeLine(params) {
  //判断是否传入容器Id
  if (!params.domId || params.domId == '') {
    console.error('未找到容器');
    return;
  }

  //判断是否存在 不重复创建
  if (document.querySelector('.timeLine_Element')) return;

  //生成时间轴的容器div
  let domWrapper = document.getElementById(params.domId);
  //整体容器
  let timeLineElement;
  timeLineElement = document.createElement('div');
  timeLineElement.className = 'timeLine_Element';
  //用div生成时间轴里各种按钮及需要显示的东西
  timeLineElement.innerHTML = `
      <div class="timeLine_Container">
        <div class="timeLine_Slider">
          <div id="sliderStrip" class="slider_Strip">
            <div id="dragBtnWrapper" class="slider_Button_Wrapper">
              <div id="timeBox" class="slider_TimeBox">${
                params.timeList[0]
              }</div>
              <div id="dragBtn" class="slider_Button"></div>
            </div>
          </div>
        </div>
        <div class="timeLine_ControlBtn">
          <div class="timeLine_ControlBtn_btnList">
            <div class="timeLine_ControlBtn_btnList_speedBox">
              <span class="speedBox_playSpeed">
                <span>x</span>
                <span class="speedBox_playSpeed_speed">${
                  (params && params.speedList[0]) || 1
                }</span>
              </span>
            </div>
            <span id="slowBtn" class="timeLine_ControlBtn_Btn timeLine_ControlBtn_Btn_Slow"></span>
            <span id="playBtn" class="timeLine_ControlBtn_Btn timeLine_ControlBtn_Btn_Play"></span>
            <span id="pauseBtn" class="timeLine_ControlBtn_Btn timeLine_ControlBtn_Btn_Pause"></span>
            <span id="fastBtn" class="timeLine_ControlBtn_Btn timeLine_ControlBtn_Btn_Fast"></span>
            <span id="refuseBtn" class="timeLine_ControlBtn_Btn timeLine_ControlBtn_Btn_Refuse"></span>
          </div>
        </div>
        <div class="timeLine_TimeList">
         <span class="timeLine_TimeList_StartTime">${params.timeList[0]}</span>
         <span class="timeLine_TimeList_EndTime">${params.timeList[1]}</span>
        </div>
      </div>
    `;
  if (domWrapper) domWrapper.appendChild(timeLineElement);

  //生成svg图标
  createSvg();

  //所有dom赋值
  playBtn = document.getElementById('playBtn');
  pauseBtn = document.getElementById('pauseBtn');
  dragBtn = document.getElementById('dragBtn');
  dragBtnWrapper = document.getElementById('dragBtnWrapper');
  sliderStrip = document.querySelector('.slider_Strip');
  timeBox = document.getElementById('timeBox');
  speedShow = document.querySelector('.speedBox_playSpeed_speed');
  timeLine_TimeList = document.querySelector('.timeLine_TimeList');
  startTime = document.querySelector('.timeLine_TimeList_StartTime');
  endTime = document.querySelector('.timeLine_TimeList_EndTime');
  speedBox = document.querySelector('.timeLine_ControlBtn_btnList_speedBox');
  svgIcon = document.querySelectorAll('.svgIcon');

  //设置初始样式
  playBtn.style.display = 'none';
  pauseBtn.style.display = 'block';
  if (params.otherList.themeColor && params.otherList.themeColor != '') {
    sliderStrip.style.backgroundColor = params.otherList.themeColor;
    dragBtn.style.backgroundColor = params.otherList.themeColor;
    timeLine_TimeList.style.color = params.otherList.themeColor;
    speedBox.style.backgroundColor = params.otherList.themeColor;
    svgIcon.forEach((item) => {
      item.querySelectorAll('path').forEach(function (path) {
        path.setAttribute('fill', params.otherList.themeColor);
      });
    });
  }

  //注册所有需要的点击事件
  addAllEventListener();

  //参数赋值
  timeList = params.timeList;
  speedList = params.speedList || [1, 2, 4, 8];
  totalTimeInSeconds = timeCalculation(timeList[0], timeList[1]); // 总秒数
  sliderWidth = sliderStrip.offsetWidth - 14;

  //当位置发生变化时 返回当前时间
  setInterval(() => {
    if (dragBtnWrapper.offsetLeft != recordNowPosition) {
      recordNowPosition = dragBtnWrapper.offsetLeft;
      params.callBack({
        nowSecond: currentSecond, //当前秒数
        lastDate: calculateTimeWithinRange(timeList[0], lastTime),
        nowDate: calculateTimeWithinRange(timeList[0], currentSecond), //当前时间（yyyy-mm-dd hh:mm:ss）
        isDragger: draggerStatus, //拖拽
        refuse: refuseStatus ? refuseStatus : null, //重置
      });
    }
  }, 1000);
}

/**
 *@description 生成SVG图片
 */
function createSvg() {
  //添加svg图标
  let slow = document.querySelector('.timeLine_ControlBtn_Btn_Slow');
  let play = document.querySelector('.timeLine_ControlBtn_Btn_Play');
  let pause = document.querySelector('.timeLine_ControlBtn_Btn_Pause');
  let fast = document.querySelector('.timeLine_ControlBtn_Btn_Fast');
  let refuse = document.querySelector('.timeLine_ControlBtn_Btn_Refuse');
  slow.innerHTML = `<svg t="1708412949027" class="svgIcon" viewBox="0 0 1032 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1449" width="32" height="32"><path d="M237.422506 505.535174L546.739559 164.817539l4.830859-5.399196-5.257111-4.688775L483.085889 97.469682l-5.257111-4.830859-4.688775 5.257112L107.557652 500.704315l-4.404606 4.830859 4.404606 4.830859L473.140003 913.174414l4.830859 5.257111 5.257111-4.830859 63.085334-57.259886 5.257111-4.830859-4.830859-5.257111-309.317053-340.717636z" fill="#1296db" p-id="1450"></path><path d="M891.293465 846.25281L581.976412 505.535174l309.174969-340.85972 4.830858-5.257111-5.257111-4.688775-63.227417-57.259886-5.115027-4.830859-4.830859 5.257112-365.582351 402.80838-4.404607 4.830859 4.404607 4.688775 365.582351 402.808381 4.830859 5.257111 5.257111-4.688775 63.085333-57.40197 5.257111-4.688775-4.688774-5.257111z" fill="#1296db" p-id="1451"></path></svg>`;
  play.innerHTML = `<svg t="1708408783018" class="svgIcon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13675" width="32" height="32"><path d="M512 1024C228.266667 1024 0 795.733333 0 512S228.266667 0 512 0s512 228.266667 512 512-228.266667 512-512 512z m0-42.666667c260.266667 0 469.333333-209.066667 469.333333-469.333333S772.266667 42.666667 512 42.666667 42.666667 251.733333 42.666667 512s209.066667 469.333333 469.333333 469.333333z m-106.666667-682.666666c12.8 0 21.333333 8.533333 21.333334 21.333333v384c0 12.8-8.533333 21.333333-21.333334 21.333333s-21.333333-8.533333-21.333333-21.333333V320c0-12.8 8.533333-21.333333 21.333333-21.333333z m213.333334 0c12.8 0 21.333333 8.533333 21.333333 21.333333v384c0 12.8-8.533333 21.333333-21.333333 21.333333s-21.333333-8.533333-21.333334-21.333333V320c0-12.8 8.533333-21.333333 21.333334-21.333333z" fill="#1296db" fill-opacity=".9" p-id="13676"></path></svg>`;
  pause.innerHTML = `<svg t="1708408767293" class="svgIcon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12666" width="32" height="32"><path d="M512 960A448 448 0 1 0 512 64a448 448 0 0 0 0 896z m0 64A512 512 0 1 1 512 0a512 512 0 0 1 0 1024z" fill="#1296db" p-id="12667"></path><path d="M448 422.656v217.088L621.632 531.2 448 422.656z m14.336 283.584A51.2 51.2 0 0 1 384 662.848V399.552a51.2 51.2 0 0 1 78.336-43.392l210.56 131.648a51.2 51.2 0 0 1 0 86.784l-210.56 131.648z" fill="#1296db" p-id="12668"></path></svg>`;
  fast.innerHTML = `<svg t="1708412973776" class="svgIcon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7511" width="28" height="28"><path d="M561.777778 491.804444L109.226667 39.253333a14.506667 14.506667 0 0 0-20.195556 0l-48.071111 48.355556a14.222222 14.222222 0 0 0 0 20.195555L435.2 502.044444a14.222222 14.222222 0 0 1 0 19.911112L40.96 916.195556a14.222222 14.222222 0 0 0 0 20.195555l48.071111 48.355556a14.506667 14.506667 0 0 0 20.195556 0l452.551111-452.551111a28.444444 28.444444 0 0 0 0-40.391112z" fill="#1296db" p-id="7512"></path><path d="M979.057778 491.804444L526.506667 39.253333a14.506667 14.506667 0 0 0-20.195556 0l-48.355555 48.355556a14.506667 14.506667 0 0 0 0 20.195555L853.333333 502.044444a13.653333 13.653333 0 0 1 0 19.911112L457.955556 916.195556a14.506667 14.506667 0 0 0 0 20.195555l48.355555 48.355556a14.506667 14.506667 0 0 0 20.195556 0l452.551111-452.551111a28.444444 28.444444 0 0 0 0-40.391112z" fill="#1296db" p-id="7513"></path></svg>`;
  refuse.innerHTML = `<svg t="1708938971867" class="svgIcon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4221" width="30" height="30"><path d="M960 416V192l-73.056 73.056a447.712 447.712 0 0 0-373.6-201.088C265.92 63.968 65.312 264.544 65.312 512S265.92 960.032 513.344 960.032a448.064 448.064 0 0 0 415.232-279.488 38.368 38.368 0 1 0-71.136-28.896 371.36 371.36 0 0 1-344.096 231.584C308.32 883.232 142.112 717.024 142.112 512S308.32 140.768 513.344 140.768c132.448 0 251.936 70.08 318.016 179.84L736 416h224z" p-id="4222" fill="#1296db"></path></svg>`;
}

/**
 *@description 将时间换算成秒数
 */
function timeCalculation(dateOne, dateTwo) {
  let date1 = new Date(dateOne);
  let date2 = new Date(dateTwo);

  let s1 = date1.getTime();
  let s2 = date2.getTime();

  return (s2 - s1) / 1000; //总秒数
}

/**
 *@description 处理时间（将秒数换算成时分秒-时间格式为HH:MM:SS）
 */
function timeConversion(totalS) {
  let hours = Math.floor(totalS / 3600);
  let minutes = Math.floor((totalS % 3600) / 60);
  let seconds = totalS % 60;

  // 将时、分、秒格式化为两位数的字符串
  let hoursStr = String(hours).padStart(2, '0');
  let minutesStr = String(minutes).padStart(2, '0');
  let secondsStr = String(seconds).padStart(2, '0');

  return hoursStr + ':' + minutesStr + ':' + secondsStr;
}

/**
 *@description 将秒数转换为时间段内的时间格式
 */
function calculateTimeWithinRange(startTime, second) {
  let start = new Date(startTime);
  let date = new Date(start.getTime() + second * 1000);

  let year = date.getFullYear();
  let month = String(date.getMonth() + 1).padStart(2, '0');
  let day = String(date.getDate()).padStart(2, '0');
  let hours = String(date.getHours()).padStart(2, '0');
  let minutes = String(date.getMinutes()).padStart(2, '0');
  let seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 *@description 时间更新（根据总秒数来模拟时间的流动）
 */

function timeUpdate() {
  lastTime = currentSecond;
  currentSecond = currentSecond + speedNow * 1
  timeBox.innerHTML = calculateTimeWithinRange(timeList[0], currentSecond);
  if (currentSecond >= totalTimeInSeconds) {
    clearInterval(timeImitateId);
  }
}

/**
 *@description 更新滑块的位置
 */
function updateSliderPosition() {
  const distancePerDivision = sliderWidth / totalTimeInSeconds; // 每格的移动距离(时间轴长度为1524)
  currentPosition = currentPosition + distancePerDivision * speedNow;
  dragBtnWrapper.style.left = `${currentPosition}px`;
  refuseStatus = null;

  if (
    currentPosition / sliderWidth > 0.99 &&
    currentSecond != totalTimeInSeconds
  ) {
    timeBox.innerHTML = calculateTimeWithinRange(
      timeList[0],
      totalTimeInSeconds
    );
  }
  if (Number(dragBtnWrapper.style.left.slice(0, -2)) >= sliderWidth) {
    dragBtnWrapper.style.left = `${sliderWidth}px`;
    clearInterval(timerId);
    clearInterval(timeImitateId);
    playBtn.style.display = 'none';
    pauseBtn.style.display = 'block';
    return;
  }
}

/**
 * @description 注册点击事件（暂停-播放,快进-快退,拖动）
 */
function addAllEventListener() {
  //播放-暂停
  document
    .getElementById('playBtn')
    .addEventListener('click', handlePauseClick);
  document
    .getElementById('pauseBtn')
    .addEventListener('click', handlePlayClick);

  //快进-快退
  document.getElementById('slowBtn').addEventListener('click', handleSlowClick);
  document.getElementById('fastBtn').addEventListener('click', handleFastClick);

  //重置
  document
    .getElementById('refuseBtn')
    .addEventListener('click', handleRefuseClick);

  //拖动
  document
    .getElementById('dragBtn')
    .addEventListener('mousedown', handleMouseDown);
  document.getElementById('dragBtn').addEventListener('mouseup', handleMouseUp);
  document
    .getElementById('dragBtn')
    .addEventListener('mouseover', handleMouseOver);
  document
    .getElementById('dragBtn')
    .addEventListener('mouseout', handleMouseOut);

  //点击时间轴
  document
    .getElementById('sliderStrip')
    .addEventListener('click', handleSliderClick);
}

/**
 * @description 播放点击事件
 */
function handlePlayClick() {
  //如果滑块的位置位于末端 时间轴重新开始
  if (Number(dragBtnWrapper.style.left.slice(0, -2)) >= sliderWidth) {
    dragBtnWrapper.style.left = '0';
    currentPosition = 0;
    currentSecond = 0;
    timeBox.innerHTML = timeList[0];
  }
  clearInterval(timerId);
  clearInterval(timeImitateId);

  //变化按钮 重置滑块移动定时器
  playBtn.style.display = 'block';
  pauseBtn.style.display = 'none';
  timerId = setInterval(updateSliderPosition, 1000);
  //重置时间显示框定时器
  timeImitateId = setInterval(timeUpdate, 1000);

  draggerStatus = null;
}

/**
 * @description 暂停点击事件
 */
function handlePauseClick() {
  playBtn.style.display = 'none';
  pauseBtn.style.display = 'block';

  clearInterval(timerId);
  //清除时间流逝定时器
  clearInterval(timeImitateId);

  draggerStatus = null;
}

/**
 * @description 快退点击事件
 */
function handleSlowClick() {
  if (selectedIndexGo > 0) {
    selectedIndexGo--; // 往回退一步
    speedNow = speedList[selectedIndexGo]; // 返回当前选中的元素
    speedShow.innerHTML = speedNow;

    clearInterval(timerId);
    clearInterval(timeImitateId);
    if (playBtn.style.display == 'block') {
      timerId = setInterval(updateSliderPosition, 1000);
      timeImitateId = setInterval(timeUpdate, 1000);
    }
  } else {
    return null; // 已经选中了第一位，无法再往后退
  }
}

/**
 * @description 快进点击事件
 */
function handleFastClick() {
  if (selectedIndexGo < speedList.length - 1) {
    selectedIndexGo++; // 选中下一位
    speedNow = speedList[selectedIndexGo]; // 返回当前选中的元素
    speedShow.innerHTML = speedNow;

    clearInterval(timerId);
    clearInterval(timeImitateId);
    if (playBtn.style.display == 'block') {
      timerId = setInterval(updateSliderPosition, 1000);
      timeImitateId = setInterval(timeUpdate, 1000);
    }
  } else {
    return null; // 已经选中了最后一位，返回 null
  }
}

/**
 * @description 重置时间轴事件
 */
function handleRefuseClick() {
  //将按钮重置 并将滑块归位
  playBtn.style.display = 'none';
  pauseBtn.style.display = 'block';
  dragBtnWrapper.style.left = '0';
  currentPosition = 0;
  clearInterval(timerId);

  //清除时间流逝定时器
  clearInterval(timeImitateId);
  currentSecond = 0;
  timeBox.innerHTML = timeList[0];
  speedShow.innerHTML = 1;
  selectedIndexGo = 0;
  refuseStatus = true;
  speedNow = 1;
}

/**
 * @description 点击时间轴事件
 */
function handleSliderClick(event) {
  if (event.clientX < 214 || event.clientX > 1749) return;
  //先将按钮重置-清除定时器 再将滑块定位到点击的位置
  playBtn.style.display = 'none';
  pauseBtn.style.display = 'block';
  clearInterval(timerId);
  dragBtnWrapper.style.left = event.clientX - 220 + 'px';
  currentPosition = event.clientX - 220;

  //根据当前距离左侧判断当前是多少秒 赋值给时间显示框
  clearInterval(timeImitateId);
  let percentNow = dragBtnWrapper.offsetLeft / sliderWidth;
  if (percentNow < 0) percentNow = 0;
  let nowShowDateNow = Math.floor(totalTimeInSeconds * percentNow);
  timeBox.innerHTML = calculateTimeWithinRange(timeList[0], nowShowDateNow);
  // timeBox.innerHTML = timeConversion(nowShowDateNow);
  currentSecond = nowShowDateNow;

  draggerStatus = true;
}

/**
 * @description 鼠标按下事件(准备拖动时间轴按钮)
 */
function handleMouseDown(event) {
  //首先判断是否处于播放模式
  if (playBtn.style.display == 'block') {
    playBtn.style.display = 'none';
    pauseBtn.style.display = 'block';
  }
  //按下鼠标切换样式
  dragBtn.style.cursor = 'grabbing';
  event.preventDefault();
  isDragging = true;
  initialX = event.clientX;
  offsetX = dragBtnWrapper.offsetLeft;
  // 添加事件监听器到document上，确保即使超出按钮区域也能继续拖动
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
}

/**
 * @description 鼠标移动事件
 */
function handleMouseMove(event) {
  if (isDragging) {
    const diffX = event.clientX - initialX;
    const newX = offsetX + diffX;

    const minPos = 0;
    const maxPos = sliderStrip.offsetWidth - dragBtn.offsetWidth;

    let clampedX = Math.max(minPos, Math.min(newX, maxPos));

    dragBtnWrapper.style.left = clampedX + 'px';
    currentPosition = dragBtnWrapper.offsetLeft;
    clearInterval(timerId);

    //根据当前距离左侧判断当前是多少秒 赋值给时间显示框以及当前秒数
    let percentNow = clampedX / (sliderStrip.offsetWidth - 14);
    if (percentNow < 0) percentNow = 0;
    let nowShowDateNow = Math.floor(totalTimeInSeconds * percentNow);
    // timeBox.innerHTML = timeConversion(nowShowDateNow);
    timeBox.innerHTML = calculateTimeWithinRange(timeList[0], nowShowDateNow);
    currentSecond = nowShowDateNow;

    draggerStatus = isDragging;
  }
  //清除时间流逝定时器
  clearInterval(timeImitateId);
}

/**
 * @description 处理鼠标释放事件
 */
function handleMouseUp() {
  dragBtn.style.cursor = 'grab';
  isDragging = false;

  // 在鼠标松开时移除移动事件监听器
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
}

/**
 * @description 处理鼠标移入事件
 */
function handleMouseOver() {
  timeBox.style.display = 'block';
}

/**
 * @description 处理鼠标移出事件
 */
function handleMouseOut() {
  timeBox.style.display = 'none';
}

/**
 * @description 更新时间轴内的显示参数
 * @param {Object} params 参数集合
 * @param {String} params.themeColor 主题颜色
 * @param {Array} params.timeList 时间显示
 */
function updateTimeLineParams(params) {
  //修改主题颜色
  if (sliderStrip && dragBtn && timeLine_TimeList && speedBox && svgIcon) {
    sliderStrip.style.backgroundColor = params.themeColor;
    dragBtn.style.backgroundColor = params.themeColor;
    timeLine_TimeList.style.color = params.themeColor;
    speedBox.style.backgroundColor = params.themeColor;
    svgIcon.forEach((item) => {
      item.querySelectorAll('path').forEach(function (path) {
        path.setAttribute('fill', params.themeColor);
      });
    });
  }

  //修改时间显示
  if (startTime && endTime && params.timeList) {
    startTime.innerHTML = params.timeList[0];
    endTime.innerHTML = params.timeList[1];
    timeList = params.timeList;
    speedList = params.speedList || [1, 2, 4, 8];
    totalTimeInSeconds = timeCalculation(
      params.timeList[0],
      params.timeList[1]
    );
  }
}

/**
 * @description 销毁时间轴
 */
function closeTimeLine() {
  let dom = document.querySelector('.timeLine_Element');
  if (!dom) {
    console.warn('未找到相关时间轴元素');
    return;
  }

  currentPosition = 0;
  currentSecond = 0;
  draggerStatus = null;
  timeBox.innerHTML = '00:00:00';
  dom.remove();
}

export default {
  initTimeLine, //初始化时间轴
  updateTimeLineParams, //修改时间轴参数
  closeTimeLine, //关闭时间轴
};
