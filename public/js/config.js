/*
 * @Author: JRX <EMAIL>
 * @Date: 2024-03-26 16:55:34
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-31 10:00:10
 * @FilePath: \wrxtzhglrj\public\js\config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 请求配置
var requestConfig = {
  url: "", // 请求地址
  timeout: 6000, // 请求超时时间
};
// let link = "*************";
let link = "**********";
// let link = "**************";
// let link = "*************";

// 地图服务地址
// let MAPServerIp = "http://**************:8088";
// let MAPServerIp = "http://*************:8088";
// let MAPServerIp = "http://*************:8088";
// let MAPServerIp = "http://**************:8088";
// let MAPServerIp = "http://*************:8088";
let MAPServerIp = "http://**********:8088";

// let MAPServerIp = "http://**************:8088";
// 后端地址 Api
let AdminServerApi = "http://" + link + ":8090/wrpt";
// ws://**************:8090/wrpt/ws/fullViewTs/{clientId}
// let AdminServerApi='/api'
// 后端地址 ws
let AdminServerWS = "ws://" + link + ":8090/wrpt/ws/global";
// 态势推演
// let TSServerWS = "ws://" + link + ":8090/wrpt/ws/fullViewTs";
let TSServerWS = "ws://" + link + ":8090/wrpt/ws/panoramic/situation";

const FlyToHeightConfig = {
  // ysd
  '1839127664938557442': 6000,
  '1839127664938557442-position': [112.888765,9.54814],
  // th
  '1838888513471299585': 1000000,
  '1838888513471299585-position': [119.924753,24.161484]
}
// 态势呈现时间倍数， 默认10s
const apanoramaTimeConfig = 10
// 影像
let ImagerLayers = [
  {
    value: "quanqiu",
    name: "quanqiu",
    serverIP: MAPServerIp,
    layerType: "WGS84",
    dataSource:0,// WebMercator
    dataSourceType: "",
    serverSource: 1,
    tileRowSize: 5, //开始层级一列有多少个瓦片
    tileColSize: 10, //开始层级一行有多少个瓦片
    tileStartZ: -4, //开始层级
    index: -1,
    imageType: "Image",
    tileStartPosion: "LeftBottom",
    show: true,
     url:MAPServerIp+"/earthview/rest/services/tileserver/wmts?serviceName=quanqiu&service=wmts&request=GetTile&version=1.0.0&style=default&layer=quanqiu&tileMatrix={tilematrix}&tileRow={tilerow}&tileCol={tilecol}&FORMAT=image/png&tileMatrixset=EPSG:4326&token=undefined",

  },
  {
    value: "威海远遥港DAT",
    name: "威海远遥港DAT",
    serverIP: MAPServerIp,
    layerType: "WGS84",
    dataSource:1,// WebMercator
    dataSourceType: "RiverMap",
    serverSource: 1,
    tileRowSize: 1, //开始层级一列有多少个瓦片
    tileColSize: 2, //开始层级一行有多少个瓦片
    tileStartZ: 0, //开始层级
    index: 1,
    imageType: "Image",
    tileStartPosion: "LeftTop",
    show: true,
     url:MAPServerIp+"/earthview/rest/services/tileserver/wmts?serviceName=%E5%A8%81%E6%B5%B7%E8%BF%9C%E9%81%A5%E6%B8%AFDAT&service=wmts&request=GetTile&version=1.0.0&style=default&layer=%E5%A8%81%E6%B5%B7%E8%BF%9C%E9%81%A5%E6%B8%AFDAT&tileMatrix={tilematrix}&tileRow={tilerow}&tileCol={tilecol}&FORMAT=image/png&tileMatrixset=EPSG:4326&token=undefined",

  },
  // {
  //   layerValue: "quanqiu",
  //   layerName: "quanqiu",
  //   serverIP: MAPServerIp,
  //   tileType: "WGS84",
  //   dataSource:0,
  //   dataSourceType: "RiverMap",
  //   // WebMercator
  //   serverSource: 0,
  //   tileRowSize: 1, //开始层级一列有多少个瓦片
  //   tileColSize: 2, //开始层级一行有多少个瓦片
  //   tileStartZ: 0, //开始层级
  //   tileStartPosion: "LeftTop",
  //   tileStartPosion: "LeftTop",
  //   index:-1,
  //   show: true,
  //    url:"",
  //    imageType: "Image",
  // },

  
];
// 矢量
let VectorLayers = [
  // 矢量服务
  {
    value: "webMap",
    name: "webMap",
    url: MAPServerIp,
    index: 1,
  },
  // {
  //   // value: 'BoundaryLineMap_Mercator_Web',
  //   // name: 'BoundaryLineMap_Mercator_Web',
  //   value: 'BoundaryLineMap_Mercator_Web',
  //   name: 'BoundaryLineMap_Mercator_Web',
  //   url: GISServerIp,
  //   index: 6,
  // },
  // {
  //   value: 'wenchang-SL',
  //   name: 'wenchang-SL',
  //   url: GISServerIp,
  //   index: 6,
  // },
  // {
  //   value: 'SL',
  //   name: 'SL',
  //   url: GISServerIp,
  //   index: 7,
  // },
  // {
  //   value: 'EVWebMap',
  //   name: '国界线',
  //   url:
  //     GISServerIp +
  //     '/earthview/services/EVWebMap/RasterService/WMTS?tilematrix={tilematrix}&layer=EVWebMap&style=default&tilerow={tilerow}&tilecol={tilecol}&tilematrixset=OGC_WebMercator&format=image%2Fjpg&service=WMTS&version=1.0.0&request=GetTile',
  // },
];
// 高程
let GCLayers = [
  // {
  //   value: 'ChinaMectorDem30',
  //   name: 'ChinaMectorDem30',
  //   url: GISServerIp,
  // },
];
