{"asset": {"generator": "FBX2glTF", "version": "2.0"}, "scene": 0, "buffers": [{"byteLength": 99852, "uri": "buffer.bin"}], "bufferViews": [{"buffer": 0, "byteLength": 15864, "byteOffset": 0, "target": 34963}, {"buffer": 0, "byteLength": 30312, "byteOffset": 15864, "target": 34962}, {"buffer": 0, "byteLength": 30312, "byteOffset": 46176, "target": 34962}, {"buffer": 0, "byteLength": 20208, "byteOffset": 76488, "target": 34962}, {"buffer": 0, "byteLength": 660, "byteOffset": 96696, "target": 34963}, {"buffer": 0, "byteLength": 936, "byteOffset": 97356, "target": 34962}, {"buffer": 0, "byteLength": 936, "byteOffset": 98292, "target": 34962}, {"buffer": 0, "byteLength": 624, "byteOffset": 99228, "target": 34962}], "scenes": [{"name": "Root Scene", "nodes": [0]}], "accessors": [{"componentType": 5123, "type": "SCALAR", "count": 7932, "bufferView": 0, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 2526, "bufferView": 1, "byteOffset": 0, "min": [-0.241063877940178, -0.112448610365391, -0.374718397855759], "max": [0.241063892841339, 0.1181256249547, 0.371406316757202]}, {"componentType": 5126, "type": "VEC3", "count": 2526, "bufferView": 2, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 2526, "bufferView": 3, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 330, "bufferView": 4, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 78, "bufferView": 5, "byteOffset": 0, "min": [-0.0187379661947489, -0.0163817256689072, 0.0537712387740612], "max": [0.0187380518764257, 0.0158343408256769, 0.226978868246078]}, {"componentType": 5126, "type": "VEC3", "count": 78, "bufferView": 6, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 78, "bufferView": 7, "byteOffset": 0}], "images": [{"name": "F-16.fbm/lowpoly_GRP-Albedo.jpeg", "uri": "F-16.fbm/lowpoly_GRP-Albedo.jpeg"}], "samplers": [{}], "textures": [{"name": "file2", "sampler": 0, "source": 0}], "materials": [{"name": "lambert2", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<unknown>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 0, "texCoord": 0}, "baseColorFactor": [1.0, 1.0, 1.0, 1.0], "metallicFactor": 0.200000002980232, "roughnessFactor": 0.800000011920929}}, {"name": "blinn1", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<unknown>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorFactor": [0.502923965454102, 0.502923965454102, 0.502923965454102, 0.116959035396576], "metallicFactor": 0.200000002980232, "roughnessFactor": 0.800000011920929}}], "meshes": [{"name": "f16_lowpoly:plane_to_uv6:polySurface85", "primitives": [{"material": 0, "mode": 4, "attributes": {"NORMAL": 2, "POSITION": 1, "TEXCOORD_0": 3}, "indices": 0}, {"material": 1, "mode": 4, "attributes": {"NORMAL": 6, "POSITION": 5, "TEXCOORD_0": 7}, "indices": 4}]}], "nodes": [{"name": "RootNode", "translation": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "children": [1]}, {"name": "f16_lowpoly:plane_to_uv6:polySurface85", "translation": [-1.49011611938477e-08, 1.74328684806824, -0.00165598408784717], "scale": [15.4569406509399, 15.4569406509399, 15.4569406509399], "mesh": 0}]}