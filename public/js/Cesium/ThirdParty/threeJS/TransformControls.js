import o from"./three.js";import et from"./TransformControlsPlane.js";import nt from"./TransformControlsGizmo.js";function G(I,r){o.Object3D.call(this),r=r!==void 0?r:document,this.visible=!1;var j=new nt;this.add(j);var x=new et;this.add(x);var a=this;i("camera",I),i("object",void 0),i("enabled",!0),i("axis",null),i("mode","translate"),i("translationSnap",null),i("rotationSnap",null),i("space","world"),i("size",1),i("dragging",!1),i("showX",!0),i("showY",!0),i("showZ",!0);var W={type:"change"},O={type:"mouseDown"},T={type:"mouseUp",mode:a.mode},J={type:"objectChange"},y=new o.Raycaster,u=new o.Vector3,p=new o.Vector3,Y=new o.Quaternion,A={X:new o.Vector3(1,0,0),Y:new o.Vector3(0,1,0),Z:new o.Vector3(0,0,1)},d=new o.Vector3,l=new o.Vector3,c=new o.Vector3,f=new o.Vector3,F=new o.Vector3,D=new o.Vector3,h=0,Q=new o.Vector3,q=new o.Quaternion,K=new o.Vector3,$=new o.Vector3,H=new o.Quaternion,Z=new o.Quaternion,C=new o.Vector3,E=new o.Vector3,U=new o.Quaternion,N=new o.Vector3,M=new o.Vector3,z=new o.Quaternion,L=new o.Quaternion,tt=new o.Vector3,b=new o.Vector3,_=new o.Vector3,w=new o.Quaternion,R=new o.Vector3;i("worldPosition",M),i("worldPositionStart",E),i("worldQuaternion",z),i("worldQuaternionStart",U),i("cameraPosition",Q),i("cameraQuaternion",q),i("pointStart",d),i("pointEnd",l),i("rotationAxis",f),i("rotationAngle",h),i("eye",b),r.addEventListener("mousedown",P,!1),r.addEventListener("touchstart",P,!1),r.addEventListener("mousemove",V,!1),r.addEventListener("touchmove",V,!1),r.addEventListener("touchmove",S,!1),document.addEventListener("mouseup",v,!1),r.addEventListener("touchend",v,!1),r.addEventListener("touchcancel",v,!1),r.addEventListener("touchleave",v,!1),this.dispose=function(){r.removeEventListener("mousedown",P),r.removeEventListener("touchstart",P),r.removeEventListener("mousemove",V),document.removeEventListener("mousemove",S),r.removeEventListener("touchmove",V),r.removeEventListener("touchmove",S),document.removeEventListener("mouseup",v),r.removeEventListener("touchend",v),r.removeEventListener("touchcancel",v),r.removeEventListener("touchleave",v),this.traverse(function(t){t.geometry&&t.geometry.dispose(),t.material&&t.material.dispose()})},this.attach=function(t){this.object=t,this.visible=!0},this.detach=function(){this.object=void 0,this.visible=!1,this.axis=null};function i(t,e){var s=e;Object.defineProperty(a,t,{get:function(){return s!==void 0?s:e},set:function(n){s!==n&&(s=n,x[t]=n,j[t]=n,a.dispatchEvent({type:t+"-changed",value:n}),a.dispatchEvent(W))}}),a[t]=e,x[t]=e,j[t]=e}this.updateMatrixWorld=function(){this.object!==void 0&&(this.object.updateMatrixWorld(),this.object.parent.matrixWorld.decompose($,H,C),this.object.matrixWorld.decompose(M,z,tt),Z.copy(H).inverse(),L.copy(z).inverse()),this.camera.updateMatrixWorld(),this.camera.matrixWorld.decompose(Q,q,K),this.camera instanceof o.PerspectiveCamera?b.copy(Q).sub(M).normalize():this.camera instanceof o.OrthographicCamera&&b.copy(Q).normalize(),o.Object3D.prototype.updateMatrixWorld.call(this)},this.pointerHover=function(t){if(!(this.object===void 0||this.dragging===!0||t.button!==void 0&&t.button!==0)){y.setFromCamera(t,this.camera);var e=y.intersectObjects(j.picker[this.mode].children,!0)[0]||!1;e?this.axis=e.object.name:this.axis=null}},this.pointerDown=function(t){if(!(this.object===void 0||this.dragging===!0||t.button!==void 0&&t.button!==0)&&(t.button===0||t.button===void 0)&&this.axis!==null){y.setFromCamera(t,this.camera);var e=y.intersectObjects([x],!0)[0]||!1;if(e){var s=this.space;if(this.mode==="scale"?s="local":(this.axis==="E"||this.axis==="XYZE"||this.axis==="XYZ")&&(s="world"),s==="local"&&this.mode==="rotate"){var n=this.rotationSnap;this.axis==="X"&&n&&(this.object.rotation.x=Math.round(this.object.rotation.x/n)*n),this.axis==="Y"&&n&&(this.object.rotation.y=Math.round(this.object.rotation.y/n)*n),this.axis==="Z"&&n&&(this.object.rotation.z=Math.round(this.object.rotation.z/n)*n)}this.object.updateMatrixWorld(),this.object.parent.updateMatrixWorld(),_.copy(this.object.position),w.copy(this.object.quaternion),R.copy(this.object.scale),this.object.matrixWorld.decompose(E,U,N),d.copy(e.point).sub(E)}this.dragging=!0,O.mode=this.mode,this.dispatchEvent(O)}},this.pointerMove=function(t){var e=this.axis,s=this.mode,n=this.object,m=this.space;if(s==="scale"?m="local":(e==="E"||e==="XYZE"||e==="XYZ")&&(m="world"),!(n===void 0||e===null||this.dragging===!1||t.button!==void 0&&t.button!==0)){y.setFromCamera(t,this.camera);var k=y.intersectObjects([x],!0)[0]||!1;if(k!==!1){if(l.copy(k.point).sub(E),s==="translate")c.copy(l).sub(d),m==="local"&&e!=="XYZ"&&c.applyQuaternion(L),e.indexOf("X")===-1&&(c.x=0),e.indexOf("Y")===-1&&(c.y=0),e.indexOf("Z")===-1&&(c.z=0),m==="local"&&e!=="XYZ"?c.applyQuaternion(w).divide(C):c.applyQuaternion(Z).divide(C),n.position.copy(c).add(_),this.translationSnap&&(m==="local"&&(n.position.applyQuaternion(Y.copy(w).inverse()),e.search("X")!==-1&&(n.position.x=Math.round(n.position.x/this.translationSnap)*this.translationSnap),e.search("Y")!==-1&&(n.position.y=Math.round(n.position.y/this.translationSnap)*this.translationSnap),e.search("Z")!==-1&&(n.position.z=Math.round(n.position.z/this.translationSnap)*this.translationSnap),n.position.applyQuaternion(w)),m==="world"&&(n.parent&&n.position.add(u.setFromMatrixPosition(n.parent.matrixWorld)),e.search("X")!==-1&&(n.position.x=Math.round(n.position.x/this.translationSnap)*this.translationSnap),e.search("Y")!==-1&&(n.position.y=Math.round(n.position.y/this.translationSnap)*this.translationSnap),e.search("Z")!==-1&&(n.position.z=Math.round(n.position.z/this.translationSnap)*this.translationSnap),n.parent&&n.position.sub(u.setFromMatrixPosition(n.parent.matrixWorld))));else if(s==="scale"){if(e.search("XYZ")!==-1){var X=l.length()/d.length();l.dot(d)<0&&(X*=-1),p.set(X,X,X)}else u.copy(d),p.copy(l),u.applyQuaternion(L),p.applyQuaternion(L),p.divide(u),e.search("X")===-1&&(p.x=1),e.search("Y")===-1&&(p.y=1),e.search("Z")===-1&&(p.z=1);n.scale.copy(R).multiply(p)}else if(s==="rotate"){c.copy(l).sub(d);var B=20/M.distanceTo(u.setFromMatrixPosition(this.camera.matrixWorld));e==="E"?(f.copy(b),h=l.angleTo(d),F.copy(d).normalize(),D.copy(l).normalize(),h*=D.cross(F).dot(b)<0?1:-1):e==="XYZE"?(f.copy(c).cross(b).normalize(),h=c.dot(u.copy(f).cross(this.eye))*B):(e==="X"||e==="Y"||e==="Z")&&(f.copy(A[e]),u.copy(A[e]),m==="local"&&u.applyQuaternion(z),h=c.dot(u.cross(b).normalize())*B),this.rotationSnap&&(h=Math.round(h/this.rotationSnap)*this.rotationSnap),this.rotationAngle=h,m==="local"&&e!=="E"&&e!=="XYZE"?(n.quaternion.copy(w),n.quaternion.multiply(Y.setFromAxisAngle(f,h)).normalize()):(f.applyQuaternion(Z),n.quaternion.copy(Y.setFromAxisAngle(f,h)),n.quaternion.multiply(w).normalize())}this.dispatchEvent(W),this.dispatchEvent(J)}}},this.pointerUp=function(t){t.button!==void 0&&t.button!==0||(this.dragging&&this.axis!==null&&(T.mode=this.mode,this.dispatchEvent(T)),this.dragging=!1,t.button===void 0&&(this.axis=null))};function g(t){var e=t.changedTouches?t.changedTouches[0]:t,s=r.getBoundingClientRect();return{x:(e.clientX-s.left)/s.width*2-1,y:-(e.clientY-s.top)/s.height*2+1,button:t.button}}function V(t){a.enabled&&a.pointerHover(g(t))}function P(t){a.enabled&&(document.addEventListener("mousemove",S,!1),a.pointerHover(g(t)),a.pointerDown(g(t)))}function S(t){a.enabled&&a.pointerMove(g(t))}function v(t){a.enabled&&(document.removeEventListener("mousemove",S,!1),a.pointerUp(g(t)))}this.getMode=function(){return a.mode},this.setMode=function(t){a.mode=t},this.setTranslationSnap=function(t){a.translationSnap=t},this.setRotationSnap=function(t){a.rotationSnap=t},this.setSize=function(t){a.size=t},this.setSpace=function(t){a.space=t},this.update=function(){console.warn("THREE.TransformControls: update function has been depricated.")}}G.prototype=Object.assign(Object.create(o.Object3D.prototype),{constructor:o.TransformControls,isTransformControls:!0});export default G;
