import y from"./three.js";const Gt=0,we=1,ve=2,an=0,ln=1,jt=2,Bt=1.25,Zt=1,Z=6*4+4+4,Tt=65535,un=Math.pow(2,-24),Kt=Symbol("SKIP_GENERATION");function Ae(i){return i.index?i.index.count:i.attributes.position.count}function tt(i){return Ae(i)/3}function Be(i,e=ArrayBuffer){return i>65535?new Uint32Array(new e(4*i)):new Uint16Array(new e(2*i))}function fn(i,e){if(!i.index){const t=i.attributes.position.count,n=e.useSharedArrayBuffer?SharedArrayBuffer:ArrayBuffer,s=Be(t,n);i.setIndex(new y.BufferAttribute(s,1));for(let a=0;a<t;a++)s[a]=a}}function Te(i){const e=tt(i),t=i.drawRange,n=t.start/3,s=(t.start+t.count)/3,a=Math.max(0,n),o=Math.min(e,s)-a;return[{offset:Math.floor(a),count:Math.floor(o)}]}function Ie(i){if(!i.groups||!i.groups.length)return Te(i);const e=[],t=new Set,n=i.drawRange,s=n.start/3,a=(n.start+n.count)/3;for(const r of i.groups){const c=r.start/3,d=(r.start+r.count)/3;t.add(Math.max(s,c)),t.add(Math.min(a,d))}const o=Array.from(t.values()).sort((r,c)=>r-c);for(let r=0;r<o.length-1;r++){const c=o[r],d=o[r+1];e.push({offset:Math.floor(c),count:Math.floor(d-c)})}return e}function dn(i){if(i.groups.length===0)return!1;const e=tt(i),t=Ie(i).sort((a,o)=>a.offset-o.offset),n=t[t.length-1];n.count=Math.min(e-n.offset,n.count);let s=0;return t.forEach(({count:a})=>s+=a),e!==s}function N(i,e,t){return t.min.x=e[i],t.min.y=e[i+1],t.min.z=e[i+2],t.max.x=e[i+3],t.max.y=e[i+4],t.max.z=e[i+5],t}function pn(i){i[0]=i[1]=i[2]=1/0,i[3]=i[4]=i[5]=-1/0}function Me(i){let e=-1,t=-1/0;for(let n=0;n<3;n++){const s=i[n+3]-i[n];s>t&&(t=s,e=n)}return e}function Pe(i,e){e.set(i)}function Se(i,e,t){let n,s;for(let a=0;a<3;a++){const o=a+3;n=i[a],s=e[a],t[a]=n<s?n:s,n=i[o],s=e[o],t[o]=n>s?n:s}}function It(i,e,t){for(let n=0;n<3;n++){const s=e[i+2*n],a=e[i+2*n+1],o=s-a,r=s+a;o<t[n]&&(t[n]=o),r>t[n+3]&&(t[n+3]=r)}}function ht(i){const e=i[3]-i[0],t=i[4]-i[1],n=i[5]-i[2];return 2*(e*t+t*n+n*e)}function Jt(i,e,t,n,s=null){let a=1/0,o=1/0,r=1/0,c=-1/0,d=-1/0,f=-1/0,u=1/0,l=1/0,p=1/0,m=-1/0,A=-1/0,g=-1/0;const h=s!==null;for(let x=e*6,b=(e+t)*6;x<b;x+=6){const w=i[x+0],v=i[x+1],B=w-v,I=w+v;B<a&&(a=B),I>c&&(c=I),h&&w<u&&(u=w),h&&w>m&&(m=w);const T=i[x+2],M=i[x+3],S=T-M,V=T+M;S<o&&(o=S),V>d&&(d=V),h&&T<l&&(l=T),h&&T>A&&(A=T);const P=i[x+4],F=i[x+5],_=P-F,D=P+F;_<r&&(r=_),D>f&&(f=D),h&&P<p&&(p=P),h&&P>g&&(g=P)}n[0]=a,n[1]=o,n[2]=r,n[3]=c,n[4]=d,n[5]=f,h&&(s[0]=u,s[1]=l,s[2]=p,s[3]=m,s[4]=A,s[5]=g)}function hn(i,e,t,n){let s=1/0,a=1/0,o=1/0,r=-1/0,c=-1/0,d=-1/0;for(let f=e*6,u=(e+t)*6;f<u;f+=6){const l=i[f+0];l<s&&(s=l),l>r&&(r=l);const p=i[f+2];p<a&&(a=p),p>c&&(c=p);const m=i[f+4];m<o&&(o=m),m>d&&(d=m)}n[0]=s,n[1]=a,n[2]=o,n[3]=r,n[4]=c,n[5]=d}function yn(i,e){pn(e);const t=i.attributes.position,n=i.index?i.index.array:null,s=tt(i),a=new Float32Array(s*6),o=t.normalized,r=t.array,c=t.offset||0;let d=3;t.isInterleavedBufferAttribute&&(d=t.data.stride);const f=["getX","getY","getZ"];for(let u=0;u<s;u++){const l=u*3,p=u*6;let m=l+0,A=l+1,g=l+2;n&&(m=n[m],A=n[A],g=n[g]),o||(m=m*d+c,A=A*d+c,g=g*d+c);for(let h=0;h<3;h++){let x,b,w;o?(x=t[f[h]](m),b=t[f[h]](A),w=t[f[h]](g)):(x=r[m+h],b=r[A+h],w=r[g+h]);let v=x;b<v&&(v=b),w<v&&(v=w);let B=x;b>B&&(B=b),w>B&&(B=w);const I=(B-v)/2,T=h*2;a[p+T+0]=v+I,a[p+T+1]=I+(Math.abs(v)+I)*un,v<e[h]&&(e[h]=v),B>e[h+3]&&(e[h+3]=B)}}return a}const O=32,xn=(i,e)=>i.candidate-e.candidate,X=new Array(O).fill().map(()=>({count:0,bounds:new Float32Array(6),rightCacheBounds:new Float32Array(6),leftCacheBounds:new Float32Array(6),candidate:0})),Mt=new Float32Array(6);function mn(i,e,t,n,s,a){let o=-1,r=0;if(a===Gt)o=Me(e),o!==-1&&(r=(e[o]+e[o+3])/2);else if(a===we)o=Me(i),o!==-1&&(r=bn(t,n,s,o));else if(a===ve){const c=ht(i);let d=Bt*s;const f=n*6,u=(n+s)*6;for(let l=0;l<3;l++){const p=e[l],g=(e[l+3]-p)/O;if(s<O/4){const h=[...X];h.length=s;let x=0;for(let w=f;w<u;w+=6,x++){const v=h[x];v.candidate=t[w+2*l],v.count=0;const{bounds:B,leftCacheBounds:I,rightCacheBounds:T}=v;for(let M=0;M<3;M++)T[M]=1/0,T[M+3]=-1/0,I[M]=1/0,I[M+3]=-1/0,B[M]=1/0,B[M+3]=-1/0;It(w,t,B)}h.sort(xn);let b=s;for(let w=0;w<b;w++){const v=h[w];for(;w+1<b&&h[w+1].candidate===v.candidate;)h.splice(w+1,1),b--}for(let w=f;w<u;w+=6){const v=t[w+2*l];for(let B=0;B<b;B++){const I=h[B];v>=I.candidate?It(w,t,I.rightCacheBounds):(It(w,t,I.leftCacheBounds),I.count++)}}for(let w=0;w<b;w++){const v=h[w],B=v.count,I=s-v.count,T=v.leftCacheBounds,M=v.rightCacheBounds;let S=0;B!==0&&(S=ht(T)/c);let V=0;I!==0&&(V=ht(M)/c);const P=Zt+Bt*(S*B+V*I);P<d&&(o=l,d=P,r=v.candidate)}}else{for(let b=0;b<O;b++){const w=X[b];w.count=0,w.candidate=p+g+b*g;const v=w.bounds;for(let B=0;B<3;B++)v[B]=1/0,v[B+3]=-1/0}for(let b=f;b<u;b+=6){let B=~~((t[b+2*l]-p)/g);B>=O&&(B=O-1);const I=X[B];I.count++,It(b,t,I.bounds)}const h=X[O-1];Pe(h.bounds,h.rightCacheBounds);for(let b=O-2;b>=0;b--){const w=X[b],v=X[b+1];Se(w.bounds,v.rightCacheBounds,w.rightCacheBounds)}let x=0;for(let b=0;b<O-1;b++){const w=X[b],v=w.count,B=w.bounds,T=X[b+1].rightCacheBounds;v!==0&&(x===0?Pe(B,Mt):Se(B,Mt,Mt)),x+=v;let M=0,S=0;x!==0&&(M=ht(Mt)/c);const V=s-x;V!==0&&(S=ht(T)/c);const P=Zt+Bt*(M*x+S*V);P<d&&(o=l,d=P,r=w.candidate)}}}}else console.warn(`MeshBVH: Invalid build strategy value ${a} used.`);return{axis:o,pos:r}}function bn(i,e,t,n){let s=0;for(let a=e,o=e+t;a<o;a++)s+=i[a*6+n*2];return s/t}class Pt{constructor(){}}function gn(i,e,t,n,s,a){let o=n,r=n+s-1;const c=a.pos,d=a.axis*2;for(;;){for(;o<=r&&t[o*6+d]<c;)o++;for(;o<=r&&t[r*6+d]>=c;)r--;if(o<r){for(let f=0;f<3;f++){let u=e[o*3+f];e[o*3+f]=e[r*3+f],e[r*3+f]=u}for(let f=0;f<6;f++){let u=t[o*6+f];t[o*6+f]=t[r*6+f],t[r*6+f]=u}o++,r--}else return o}}function wn(i,e,t,n,s,a){let o=n,r=n+s-1;const c=a.pos,d=a.axis*2;for(;;){for(;o<=r&&t[o*6+d]<c;)o++;for(;o<=r&&t[r*6+d]>=c;)r--;if(o<r){let f=i[o];i[o]=i[r],i[r]=f;for(let u=0;u<6;u++){let l=t[o*6+u];t[o*6+u]=t[r*6+u],t[r*6+u]=l}o++,r--}else return o}}function vn(i,e){const t=(i.index?i.index.count:i.attributes.position.count)/3,n=t>2**16,s=n?4:2,a=e?new SharedArrayBuffer(t*s):new ArrayBuffer(t*s),o=n?new Uint32Array(a):new Uint16Array(a);for(let r=0,c=o.length;r<c;r++)o[r]=r;return o}function An(i,e){const t=i.geometry,n=t.index?t.index.array:null,s=e.maxDepth,a=e.verbose,o=e.maxLeafTris,r=e.strategy,c=e.onProgress,d=tt(t),f=i._indirectBuffer;let u=!1;const l=new Float32Array(6),p=new Float32Array(6),m=yn(t,l),A=e.indirect?wn:gn,g=[],h=e.indirect?Te(t):Ie(t);if(h.length===1){const w=h[0],v=new Pt;v.boundingData=l,hn(m,w.offset,w.count,p),b(v,w.offset,w.count,p),g.push(v)}else for(let w of h){const v=new Pt;v.boundingData=new Float32Array(6),Jt(m,w.offset,w.count,v.boundingData,p),b(v,w.offset,w.count,p),g.push(v)}return g;function x(w){c&&c(w/d)}function b(w,v,B,I=null,T=0){if(!u&&T>=s&&(u=!0,a&&(console.warn(`MeshBVH: Max depth of ${s} reached when generating BVH. Consider increasing maxDepth.`),console.warn(t))),B<=o||T>=s)return x(v+B),w.offset=v,w.count=B,w;const M=mn(w.boundingData,I,m,v,B,r);if(M.axis===-1)return x(v+B),w.offset=v,w.count=B,w;const S=A(f,n,m,v,B,M);if(S===v||S===v+B)x(v+B),w.offset=v,w.count=B;else{w.splitAxis=M.axis;const V=new Pt,P=v,F=S-v;w.left=V,V.boundingData=new Float32Array(6),Jt(m,P,F,V.boundingData,p),b(V,P,F,p,T+1);const _=new Pt,D=S,R=B-F;w.right=_,_.boundingData=new Float32Array(6),Jt(m,D,R,_.boundingData,p),b(_,D,R,p,T+1)}return w}}function Bn(i,e){const t=i.geometry;e.indirect&&(i._indirectBuffer=vn(t,e.useSharedArrayBuffer),dn(t)&&!e.verbose&&console.warn('MeshBVH: Provided geometry contains groups that do not fully span the vertex contents while using the "indirect" option. BVH may incorrectly report intersections on unrendered portions of the geometry.')),i._indirectBuffer||fn(t,e);const n=An(i,e);let s,a,o;const r=[],c=e.useSharedArrayBuffer?SharedArrayBuffer:ArrayBuffer;for(let u=0;u<n.length;u++){const l=n[u];let p=d(l);const m=new c(Z*p);s=new Float32Array(m),a=new Uint32Array(m),o=new Uint16Array(m),f(0,l),r.push(m)}i._roots=r;return;function d(u){return u.count?1:1+d(u.left)+d(u.right)}function f(u,l){const p=u/4,m=u/2,A=!!l.count,g=l.boundingData;for(let h=0;h<6;h++)s[p+h]=g[h];if(A){const h=l.offset,x=l.count;return a[p+6]=h,o[m+14]=x,o[m+15]=Tt,u+Z}else{const h=l.left,x=l.right,b=l.splitAxis;let w;if(w=f(u+Z,h),w/4>Math.pow(2,32))throw new Error("MeshBVH: Cannot store child pointer greater than 32 bits.");return a[p+6]=w/4,w=f(w,x),a[p+7]=b,w}}}class q{constructor(){this.min=1/0,this.max=-1/0}setFromPointsField(e,t){let n=1/0,s=-1/0;for(let a=0,o=e.length;a<o;a++){const c=e[a][t];n=c<n?c:n,s=c>s?c:s}this.min=n,this.max=s}setFromPoints(e,t){let n=1/0,s=-1/0;for(let a=0,o=t.length;a<o;a++){const r=t[a],c=e.dot(r);n=c<n?c:n,s=c>s?c:s}this.min=n,this.max=s}isSeparated(e){return this.min>e.max||e.min>this.max}}q.prototype.setFromBox=function(){const i=new y.Vector3;return function(t,n){const s=n.min,a=n.max;let o=1/0,r=-1/0;for(let c=0;c<=1;c++)for(let d=0;d<=1;d++)for(let f=0;f<=1;f++){i.x=s.x*c+a.x*(1-c),i.y=s.y*d+a.y*(1-d),i.z=s.z*f+a.z*(1-f);const u=t.dot(i);o=Math.min(u,o),r=Math.max(u,r)}this.min=o,this.max=r}}();const Ci=function(){const i=new q;return function(t,n){const s=t.points,a=t.satAxes,o=t.satBounds,r=n.points,c=n.satAxes,d=n.satBounds;for(let f=0;f<3;f++){const u=o[f],l=a[f];if(i.setFromPoints(l,r),u.isSeparated(i))return!1}for(let f=0;f<3;f++){const u=d[f],l=c[f];if(i.setFromPoints(l,s),u.isSeparated(i))return!1}}}(),Tn=function(){const i=new y.Vector3,e=new y.Vector3,t=new y.Vector3;return function(s,a,o){const r=s.start,c=i,d=a.start,f=e;t.subVectors(r,d),i.subVectors(s.end,s.start),e.subVectors(a.end,a.start);const u=t.dot(f),l=f.dot(c),p=f.dot(f),m=t.dot(c),g=c.dot(c)*p-l*l;let h,x;g!==0?h=(u*l-m*p)/g:h=0,x=(u+h*l)/p,o.x=h,o.y=x}}(),Qt=function(){const i=new y.Vector2,e=new y.Vector3,t=new y.Vector3;return function(s,a,o,r){Tn(s,a,i);let c=i.x,d=i.y;if(c>=0&&c<=1&&d>=0&&d<=1){s.at(c,o),a.at(d,r);return}else if(c>=0&&c<=1){d<0?a.at(0,r):a.at(1,r),s.closestPointToPoint(r,!0,o);return}else if(d>=0&&d<=1){c<0?s.at(0,o):s.at(1,o),a.closestPointToPoint(o,!0,r);return}else{let f;c<0?f=s.start:f=s.end;let u;d<0?u=a.start:u=a.end;const l=e,p=t;if(s.closestPointToPoint(u,!0,e),a.closestPointToPoint(f,!0,t),l.distanceToSquared(u)<=p.distanceToSquared(f)){o.copy(l),r.copy(u);return}else{o.copy(f),r.copy(p);return}}}}(),In=function(){const i=new y.Vector3,e=new y.Vector3,t=new y.Plane,n=new y.Line3;return function(a,o){const{radius:r,center:c}=a,{a:d,b:f,c:u}=o;if(n.start=d,n.end=f,n.closestPointToPoint(c,!0,i).distanceTo(c)<=r||(n.start=d,n.end=u,n.closestPointToPoint(c,!0,i).distanceTo(c)<=r)||(n.start=f,n.end=u,n.closestPointToPoint(c,!0,i).distanceTo(c)<=r))return!0;const A=o.getPlane(t);if(Math.abs(A.distanceToPoint(c))<=r){const h=A.projectPoint(c,e);if(o.containsPoint(h))return!0}return!1}}(),Mn=1e-15;function te(i){return Math.abs(i)<Mn}class L extends y.Triangle{constructor(...e){super(...e),this.isExtendedTriangle=!0,this.satAxes=new Array(4).fill().map(()=>new y.Vector3),this.satBounds=new Array(4).fill().map(()=>new q),this.points=[this.a,this.b,this.c],this.sphere=new y.Sphere,this.plane=new y.Plane,this.needsUpdate=!0}intersectsSphere(e){return In(e,this)}update(){const e=this.a,t=this.b,n=this.c,s=this.points,a=this.satAxes,o=this.satBounds,r=a[0],c=o[0];this.getNormal(r),c.setFromPoints(r,s);const d=a[1],f=o[1];d.subVectors(e,t),f.setFromPoints(d,s);const u=a[2],l=o[2];u.subVectors(t,n),l.setFromPoints(u,s);const p=a[3],m=o[3];p.subVectors(n,e),m.setFromPoints(p,s),this.sphere.setFromPoints(this.points),this.plane.setFromNormalAndCoplanarPoint(r,e),this.needsUpdate=!1}}L.prototype.closestPointToSegment=function(){const i=new y.Vector3,e=new y.Vector3,t=new y.Line3;return function(s,a=null,o=null){const{start:r,end:c}=s,d=this.points;let f,u=1/0;for(let l=0;l<3;l++){const p=(l+1)%3;t.start.copy(d[l]),t.end.copy(d[p]),Qt(t,s,i,e),f=i.distanceToSquared(e),f<u&&(u=f,a&&a.copy(i),o&&o.copy(e))}return this.closestPointToPoint(r,i),f=r.distanceToSquared(i),f<u&&(u=f,a&&a.copy(i),o&&o.copy(r)),this.closestPointToPoint(c,i),f=c.distanceToSquared(i),f<u&&(u=f,a&&a.copy(i),o&&o.copy(c)),Math.sqrt(u)}}(),L.prototype.intersectsTriangle=function(){const i=new L,e=new Array(3),t=new Array(3),n=new q,s=new q,a=new y.Vector3,o=new y.Vector3,r=new y.Vector3,c=new y.Vector3,d=new y.Vector3,f=new y.Line3,u=new y.Line3,l=new y.Line3,p=new y.Vector3;function m(A,g,h){const x=A.points;let b=0,w=-1;for(let v=0;v<3;v++){const{start:B,end:I}=f;B.copy(x[v]),I.copy(x[(v+1)%3]),f.delta(o);const T=te(g.distanceToPoint(B));if(te(g.normal.dot(o))&&T){h.copy(f),b=2;break}const M=g.intersectLine(f,p);if(!M&&T&&p.copy(B),(M||T)&&!te(p.distanceTo(I))){if(b<=1)(b===1?h.start:h.end).copy(p),T&&(w=b);else if(b>=2){(w===1?h.start:h.end).copy(p),b=2;break}if(b++,b===2&&w===-1)break}}return b}return function(g,h=null,x=!1){this.needsUpdate&&this.update(),g.isExtendedTriangle?g.needsUpdate&&g.update():(i.copy(g),i.update(),g=i);const b=this.plane,w=g.plane;if(Math.abs(b.normal.dot(w.normal))>1-1e-10){const v=this.satBounds,B=this.satAxes;t[0]=g.a,t[1]=g.b,t[2]=g.c;for(let M=0;M<4;M++){const S=v[M],V=B[M];if(n.setFromPoints(V,t),S.isSeparated(n))return!1}const I=g.satBounds,T=g.satAxes;e[0]=this.a,e[1]=this.b,e[2]=this.c;for(let M=0;M<4;M++){const S=I[M],V=T[M];if(n.setFromPoints(V,e),S.isSeparated(n))return!1}for(let M=0;M<4;M++){const S=B[M];for(let V=0;V<4;V++){const P=T[V];if(a.crossVectors(S,P),n.setFromPoints(a,e),s.setFromPoints(a,t),n.isSeparated(s))return!1}}return h&&(x||console.warn("ExtendedTriangle.intersectsTriangle: Triangles are coplanar which does not support an output edge. Setting edge to 0, 0, 0."),h.start.set(0,0,0),h.end.set(0,0,0)),!0}else{const v=m(this,w,u);if(v===1&&g.containsPoint(u.end))return h&&(h.start.copy(u.end),h.end.copy(u.end)),!0;if(v!==2)return!1;const B=m(g,b,l);if(B===1&&this.containsPoint(l.end))return h&&(h.start.copy(l.end),h.end.copy(l.end)),!0;if(B!==2)return!1;if(u.delta(r),l.delta(c),r.dot(c)<0){let F=l.start;l.start=l.end,l.end=F}const I=u.start.dot(r),T=u.end.dot(r),M=l.start.dot(r),S=l.end.dot(r),V=T<M,P=I<S;return I!==S&&M!==T&&V===P?!1:(h&&(d.subVectors(u.start,l.start),d.dot(r)>0?h.start.copy(u.start):h.start.copy(l.start),d.subVectors(u.end,l.end),d.dot(r)<0?h.end.copy(u.end):h.end.copy(l.end)),!0)}}}(),L.prototype.distanceToPoint=function(){const i=new y.Vector3;return function(t){return this.closestPointToPoint(t,i),t.distanceTo(i)}}(),L.prototype.distanceToTriangle=function(){const i=new y.Vector3,e=new y.Vector3,t=["a","b","c"],n=new y.Line3,s=new y.Line3;return function(o,r=null,c=null){const d=r||c?n:null;if(this.intersectsTriangle(o,d))return(r||c)&&(r&&d.getCenter(r),c&&d.getCenter(c)),0;let f=1/0;for(let u=0;u<3;u++){let l;const p=t[u],m=o[p];this.closestPointToPoint(m,i),l=m.distanceToSquared(i),l<f&&(f=l,r&&r.copy(i),c&&c.copy(m));const A=this[p];o.closestPointToPoint(A,i),l=A.distanceToSquared(i),l<f&&(f=l,r&&r.copy(A),c&&c.copy(i))}for(let u=0;u<3;u++){const l=t[u],p=t[(u+1)%3];n.set(this[l],this[p]);for(let m=0;m<3;m++){const A=t[m],g=t[(m+1)%3];s.set(o[A],o[g]),Qt(n,s,i,e);const h=i.distanceToSquared(e);h<f&&(f=h,r&&r.copy(i),c&&c.copy(e))}}return Math.sqrt(f)}}();class U{constructor(e,t,n){this.isOrientedBox=!0,this.min=new y.Vector3,this.max=new y.Vector3,this.matrix=new y.Matrix4,this.invMatrix=new y.Matrix4,this.points=new Array(8).fill().map(()=>new y.Vector3),this.satAxes=new Array(3).fill().map(()=>new y.Vector3),this.satBounds=new Array(3).fill().map(()=>new q),this.alignedSatBounds=new Array(3).fill().map(()=>new q),this.needsUpdate=!1,e&&this.min.copy(e),t&&this.max.copy(t),n&&this.matrix.copy(n)}set(e,t,n){this.min.copy(e),this.max.copy(t),this.matrix.copy(n),this.needsUpdate=!0}copy(e){this.min.copy(e.min),this.max.copy(e.max),this.matrix.copy(e.matrix),this.needsUpdate=!0}}U.prototype.update=function(){return function(){const e=this.matrix,t=this.min,n=this.max,s=this.points;for(let d=0;d<=1;d++)for(let f=0;f<=1;f++)for(let u=0;u<=1;u++){const l=1*d|2*f|4*u,p=s[l];p.x=d?n.x:t.x,p.y=f?n.y:t.y,p.z=u?n.z:t.z,p.applyMatrix4(e)}const a=this.satBounds,o=this.satAxes,r=s[0];for(let d=0;d<3;d++){const f=o[d],u=a[d],l=1<<d,p=s[l];f.subVectors(r,p),u.setFromPoints(f,s)}const c=this.alignedSatBounds;c[0].setFromPointsField(s,"x"),c[1].setFromPointsField(s,"y"),c[2].setFromPointsField(s,"z"),this.invMatrix.copy(this.matrix).invert(),this.needsUpdate=!1}}(),U.prototype.intersectsBox=function(){const i=new q;return function(t){this.needsUpdate&&this.update();const n=t.min,s=t.max,a=this.satBounds,o=this.satAxes,r=this.alignedSatBounds;if(i.min=n.x,i.max=s.x,r[0].isSeparated(i)||(i.min=n.y,i.max=s.y,r[1].isSeparated(i))||(i.min=n.z,i.max=s.z,r[2].isSeparated(i)))return!1;for(let c=0;c<3;c++){const d=o[c],f=a[c];if(i.setFromBox(d,t),f.isSeparated(i))return!1}return!0}}(),U.prototype.intersectsTriangle=function(){const i=new L,e=new Array(3),t=new q,n=new q,s=new y.Vector3;return function(o){this.needsUpdate&&this.update(),o.isExtendedTriangle?o.needsUpdate&&o.update():(i.copy(o),i.update(),o=i);const r=this.satBounds,c=this.satAxes;e[0]=o.a,e[1]=o.b,e[2]=o.c;for(let l=0;l<3;l++){const p=r[l],m=c[l];if(t.setFromPoints(m,e),p.isSeparated(t))return!1}const d=o.satBounds,f=o.satAxes,u=this.points;for(let l=0;l<3;l++){const p=d[l],m=f[l];if(t.setFromPoints(m,u),p.isSeparated(t))return!1}for(let l=0;l<3;l++){const p=c[l];for(let m=0;m<4;m++){const A=f[m];if(s.crossVectors(p,A),t.setFromPoints(s,e),n.setFromPoints(s,u),t.isSeparated(n))return!1}}return!0}}(),U.prototype.closestPointToPoint=function(){return function(e,t){return this.needsUpdate&&this.update(),t.copy(e).applyMatrix4(this.invMatrix).clamp(this.min,this.max).applyMatrix4(this.matrix),t}}(),U.prototype.distanceToPoint=function(){const i=new y.Vector3;return function(t){return this.closestPointToPoint(t,i),t.distanceTo(i)}}(),U.prototype.distanceToBox=function(){const i=["x","y","z"],e=new Array(12).fill().map(()=>new y.Line3),t=new Array(12).fill().map(()=>new y.Line3),n=new y.Vector3,s=new y.Vector3;return function(o,r=0,c=null,d=null){if(this.needsUpdate&&this.update(),this.intersectsBox(o))return(c||d)&&(o.getCenter(s),this.closestPointToPoint(s,n),o.closestPointToPoint(n,s),c&&c.copy(n),d&&d.copy(s)),0;const f=r*r,u=o.min,l=o.max,p=this.points;let m=1/0;for(let g=0;g<8;g++){const h=p[g];s.copy(h).clamp(u,l);const x=h.distanceToSquared(s);if(x<m&&(m=x,c&&c.copy(h),d&&d.copy(s),x<f))return Math.sqrt(x)}let A=0;for(let g=0;g<3;g++)for(let h=0;h<=1;h++)for(let x=0;x<=1;x++){const b=(g+1)%3,w=(g+2)%3,v=h<<b|x<<w,B=1<<g|h<<b|x<<w,I=p[v],T=p[B];e[A].set(I,T);const S=i[g],V=i[b],P=i[w],F=t[A],_=F.start,D=F.end;_[S]=u[S],_[V]=h?u[V]:l[V],_[P]=x?u[P]:l[V],D[S]=l[S],D[V]=h?u[V]:l[V],D[P]=x?u[P]:l[V],A++}for(let g=0;g<=1;g++)for(let h=0;h<=1;h++)for(let x=0;x<=1;x++){s.x=g?l.x:u.x,s.y=h?l.y:u.y,s.z=x?l.z:u.z,this.closestPointToPoint(s,n);const b=s.distanceToSquared(n);if(b<m&&(m=b,c&&c.copy(n),d&&d.copy(s),b<f))return Math.sqrt(b)}for(let g=0;g<12;g++){const h=e[g];for(let x=0;x<12;x++){const b=t[x];Qt(h,b,n,s);const w=n.distanceToSquared(s);if(w<m&&(m=w,c&&c.copy(n),d&&d.copy(s),w<f))return Math.sqrt(w)}}return Math.sqrt(m)}}();class Ve{constructor(e){this._getNewPrimitive=e,this._primitives=[]}getPrimitive(){const e=this._primitives;return e.length===0?this._getNewPrimitive():e.pop()}releasePrimitive(e){this._primitives.push(e)}}class Pn extends Ve{constructor(){super(()=>new L)}}const C=new Pn;function k(i,e){return e[i+15]===65535}function H(i,e){return e[i+6]}function W(i,e){return e[i+14]}function Y(i){return i+8}function $(i,e){return e[i+6]}function ee(i,e){return e[i+7]}function Ri(i){return i}class Sn{constructor(){this.float32Array=null,this.uint16Array=null,this.uint32Array=null;const e=[];let t=null;this.setBuffer=n=>{t&&e.push(t),t=n,this.float32Array=new Float32Array(n),this.uint16Array=new Uint16Array(n),this.uint32Array=new Uint32Array(n)},this.clearBuffer=()=>{t=null,this.float32Array=null,this.uint16Array=null,this.uint32Array=null,e.length!==0&&this.setBuffer(e.pop())}}}const z=new Sn;let G,et;const nt=[],St=new Ve(()=>new y.Box3);function Vn(i,e,t,n,s,a){G=St.getPrimitive(),et=St.getPrimitive(),nt.push(G,et),z.setBuffer(i._roots[e]);const o=ne(0,i.geometry,t,n,s,a);z.clearBuffer(),St.releasePrimitive(G),St.releasePrimitive(et),nt.pop(),nt.pop();const r=nt.length;return r>0&&(et=nt[r-1],G=nt[r-2]),o}function ne(i,e,t,n,s=null,a=0,o=0){const{float32Array:r,uint16Array:c,uint32Array:d}=z;let f=i*2;if(k(f,c)){const l=H(i,d),p=W(f,c);return N(i,r,G),n(l,p,!1,o,a+i,G)}else{let S=function(P){const{uint16Array:F,uint32Array:_}=z;let D=P*2;for(;!k(D,F);)P=Y(P),D=P*2;return H(P,_)},V=function(P){const{uint16Array:F,uint32Array:_}=z;let D=P*2;for(;!k(D,F);)P=$(P,_),D=P*2;return H(P,_)+W(D,F)};const l=Y(i),p=$(i,d);let m=l,A=p,g,h,x,b;if(s&&(x=G,b=et,N(m,r,x),N(A,r,b),g=s(x),h=s(b),h<g)){m=p,A=l;const P=g;g=h,h=P,x=b}x||(x=G,N(m,r,x));const w=k(m*2,c),v=t(x,w,g,o+1,a+m);let B;if(v===jt){const P=S(m),_=V(m)-P;B=n(P,_,!0,o+1,a+m,x)}else B=v&&ne(m,e,t,n,s,a,o+1);if(B)return!0;b=et,N(A,r,b);const I=k(A*2,c),T=t(b,I,h,o+1,a+A);let M;if(T===jt){const P=S(A),_=V(A)-P;M=n(P,_,!0,o+1,a+A,b)}else M=T&&ne(A,e,t,n,s,a,o+1);return!!M}}const yt=new y.Vector3,ie=new y.Vector3;function Fn(i,e,t={},n=0,s=1/0){const a=n*n,o=s*s;let r=1/0,c=null;if(i.shapecast({boundsTraverseOrder:f=>(yt.copy(e).clamp(f.min,f.max),yt.distanceToSquared(e)),intersectsBounds:(f,u,l)=>l<r&&l<o,intersectsTriangle:(f,u)=>{f.closestPointToPoint(e,yt);const l=e.distanceToSquared(yt);return l<r&&(ie.copy(yt),r=l,c=u),l<a}}),r===1/0)return null;const d=Math.sqrt(r);return t.point?t.point.copy(ie):t.point=ie.clone(),t.distance=d,t.faceIndex=c,t}const it=new y.Vector3,st=new y.Vector3,ot=new y.Vector3,Vt=new y.Vector2,Ft=new y.Vector2,_t=new y.Vector2,Fe=new y.Vector3,_e=new y.Vector3,De=new y.Vector3,Dt=new y.Vector3;function _n(i,e,t,n,s,a){let o;return a===y.BackSide?o=i.intersectTriangle(n,t,e,!0,s):o=i.intersectTriangle(e,t,n,a!==y.DoubleSide,s),o===null?null:{distance:i.origin.distanceTo(s),point:s.clone()}}function Dn(i,e,t,n,s,a,o,r,c){it.fromBufferAttribute(e,a),st.fromBufferAttribute(e,o),ot.fromBufferAttribute(e,r);const d=_n(i,it,st,ot,Dt,c);if(d){n&&(Vt.fromBufferAttribute(n,a),Ft.fromBufferAttribute(n,o),_t.fromBufferAttribute(n,r),d.uv=y.Triangle.getInterpolation(Dt,it,st,ot,Vt,Ft,_t,new y.Vector2)),s&&(Vt.fromBufferAttribute(s,a),Ft.fromBufferAttribute(s,o),_t.fromBufferAttribute(s,r),d.uv1=y.Triangle.getInterpolation(Dt,it,st,ot,Vt,Ft,_t,new y.Vector2)),t&&(Fe.fromBufferAttribute(t,a),_e.fromBufferAttribute(t,o),De.fromBufferAttribute(t,r),d.normal=y.Triangle.getInterpolation(Dt,it,st,ot,Fe,_e,De,new y.Vector3),d.normal.dot(i.direction)>0&&d.normal.multiplyScalar(-1));const f={a,b:o,c:r,normal:new y.Vector3,materialIndex:0};y.Triangle.getNormal(it,st,ot,f.normal),d.face=f,d.faceIndex=a}return d}function zt(i,e,t,n,s){const a=n*3;let o=a+0,r=a+1,c=a+2;const d=i.index;i.index&&(o=d.getX(o),r=d.getX(r),c=d.getX(c));const{position:f,normal:u,uv:l,uv1:p}=i.attributes,m=Dn(t,f,u,l,p,o,r,c,e);return m?(m.faceIndex=n,s&&s.push(m),m):null}function E(i,e,t,n){const s=i.a,a=i.b,o=i.c;let r=e,c=e+1,d=e+2;t&&(r=t.getX(r),c=t.getX(c),d=t.getX(d)),s.x=n.getX(r),s.y=n.getY(r),s.z=n.getZ(r),a.x=n.getX(c),a.y=n.getY(c),a.z=n.getZ(c),o.x=n.getX(d),o.y=n.getY(d),o.z=n.getZ(d)}const Et=new y.Vector3,Ct=new y.Vector3,Rt=new y.Vector3,ze=new y.Vector2,Ee=new y.Vector2,Ce=new y.Vector2;function zn(i,e,t,n){const s=e.getIndex().array,a=e.getAttribute("position"),o=e.getAttribute("uv"),r=s[t*3],c=s[t*3+1],d=s[t*3+2];Et.fromBufferAttribute(a,r),Ct.fromBufferAttribute(a,c),Rt.fromBufferAttribute(a,d);let f=0;const u=e.groups,l=t*3;for(let m=0,A=u.length;m<A;m++){const g=u[m],{start:h,count:x}=g;if(l>=h&&l<h+x){f=g.materialIndex;break}}let p=null;return o&&(ze.fromBufferAttribute(o,r),Ee.fromBufferAttribute(o,c),Ce.fromBufferAttribute(o,d),n&&n.uv?p=n.uv:p=new y.Vector2,y.Triangle.getInterpolation(i,Et,Ct,Rt,ze,Ee,Ce,p)),n?(n.face||(n.face={}),n.face.a=r,n.face.b=c,n.face.c=d,n.face.materialIndex=f,n.face.normal||(n.face.normal=new y.Vector3),y.Triangle.getNormal(Et,Ct,Rt,n.face.normal),p&&(n.uv=p),n):{face:{a:r,b:c,c:d,materialIndex:f,normal:y.Triangle.getNormal(Et,Ct,Rt,new y.Vector3)},uv:p}}function En(i,e,t,n,s,a){const{geometry:o,_indirectBuffer:r}=i;for(let c=n,d=n+s;c<d;c++)zt(o,e,t,c,a)}function Cn(i,e,t,n,s){const{geometry:a,_indirectBuffer:o}=i;let r=1/0,c=null;for(let d=n,f=n+s;d<f;d++){let u;u=zt(a,e,t,d),u&&u.distance<r&&(c=u,r=u.distance)}return c}function Rn(i,e,t,n,s,a,o){const{geometry:r}=t,{index:c}=r,d=r.attributes.position;for(let f=i,u=e+i;f<u;f++){let l;if(l=f,E(o,l*3,c,d),o.needsUpdate=!0,n(o,l,s,a))return!0}return!1}function Nn(i,e=null){e&&Array.isArray(e)&&(e=new Set(e));const t=i.geometry,n=t.index?t.index.array:null,s=t.attributes.position;let a,o,r,c,d=0;const f=i._roots;for(let l=0,p=f.length;l<p;l++)a=f[l],o=new Uint32Array(a),r=new Uint16Array(a),c=new Float32Array(a),u(0,d),d+=a.byteLength;function u(l,p,m=!1){const A=l*2;if(r[A+15]===Tt){const h=o[l+6],x=r[A+14];let b=1/0,w=1/0,v=1/0,B=-1/0,I=-1/0,T=-1/0;for(let M=3*h,S=3*(h+x);M<S;M++){let V=n[M];const P=s.getX(V),F=s.getY(V),_=s.getZ(V);P<b&&(b=P),P>B&&(B=P),F<w&&(w=F),F>I&&(I=F),_<v&&(v=_),_>T&&(T=_)}return c[l+0]!==b||c[l+1]!==w||c[l+2]!==v||c[l+3]!==B||c[l+4]!==I||c[l+5]!==T?(c[l+0]=b,c[l+1]=w,c[l+2]=v,c[l+3]=B,c[l+4]=I,c[l+5]=T,!0):!1}else{const h=l+8,x=o[l+6],b=h+p,w=x+p;let v=m,B=!1,I=!1;e?v||(B=e.has(b),I=e.has(w),v=!B&&!I):(B=!0,I=!0);const T=v||B,M=v||I;let S=!1;T&&(S=u(h,p,v));let V=!1;M&&(V=u(x,p,v));const P=S||V;if(P)for(let F=0;F<3;F++){const _=h+F,D=x+F,R=c[_],ft=c[_+3],dt=c[D],pt=c[D+3];c[l+F]=R<dt?R:dt,c[l+F+3]=ft>pt?ft:pt}return P}}}const Re=new y.Box3;function j(i,e,t,n){return N(i,e,Re),t.intersectBox(Re,n)}function Un(i,e,t,n,s,a){const{geometry:o,_indirectBuffer:r}=i;for(let c=n,d=n+s;c<d;c++){let f=r?r[c]:c;zt(o,e,t,f,a)}}function Ln(i,e,t,n,s){const{geometry:a,_indirectBuffer:o}=i;let r=1/0,c=null;for(let d=n,f=n+s;d<f;d++){let u;u=zt(a,e,t,o?o[d]:d),u&&u.distance<r&&(c=u,r=u.distance)}return c}function kn(i,e,t,n,s,a,o){const{geometry:r}=t,{index:c}=r,d=r.attributes.position;for(let f=i,u=e+i;f<u;f++){let l;if(l=t.resolveTriangleIndex(f),E(o,l*3,c,d),o.needsUpdate=!0,n(o,l,s,a))return!0}return!1}const Ne=new y.Vector3;function qn(i,e,t,n,s){z.setBuffer(i._roots[e]),se(0,i,t,n,s),z.clearBuffer()}function se(i,e,t,n,s){const{float32Array:a,uint16Array:o,uint32Array:r}=z,c=i*2;if(k(c,o)){const f=H(i,r),u=W(c,o);En(e,t,n,f,u,s)}else{const f=Y(i);j(f,a,n,Ne)&&se(f,e,t,n,s);const u=$(i,r);j(u,a,n,Ne)&&se(u,e,t,n,s)}}const Ue=new y.Vector3,Hn=["x","y","z"];function On(i,e,t,n){z.setBuffer(i._roots[e]);const s=oe(0,i,t,n);return z.clearBuffer(),s}function oe(i,e,t,n){const{float32Array:s,uint16Array:a,uint32Array:o}=z;let r=i*2;if(k(r,a)){const d=H(i,o),f=W(r,a);return Cn(e,t,n,d,f)}else{const d=ee(i,o),f=Hn[d],l=n.direction[f]>=0;let p,m;l?(p=Y(i),m=$(i,o)):(p=$(i,o),m=Y(i));const g=j(p,s,n,Ue)?oe(p,e,t,n):null;if(g){const b=g.point[f];if(l?b<=s[m+d]:b>=s[m+d+3])return g}const x=j(m,s,n,Ue)?oe(m,e,t,n):null;return g&&x?g.distance<=x.distance?g:x:g||x||null}}const Nt=new y.Box3,rt=new L,ct=new L,xt=new y.Matrix4,Le=new U,Ut=new U;function Wn(i,e,t,n){z.setBuffer(i._roots[e]);const s=re(0,i,t,n);return z.clearBuffer(),s}function re(i,e,t,n,s=null){const{float32Array:a,uint16Array:o,uint32Array:r}=z;let c=i*2;if(s===null&&(t.boundingBox||t.computeBoundingBox(),Le.set(t.boundingBox.min,t.boundingBox.max,n),s=Le),k(c,o)){const f=e.geometry,u=f.index,l=f.attributes.position,p=t.index,m=t.attributes.position,A=H(i,r),g=W(c,o);if(xt.copy(n).invert(),t.boundsTree)return N(i,a,Ut),Ut.matrix.copy(xt),Ut.needsUpdate=!0,t.boundsTree.shapecast({intersectsBounds:x=>Ut.intersectsBox(x),intersectsTriangle:x=>{x.a.applyMatrix4(n),x.b.applyMatrix4(n),x.c.applyMatrix4(n),x.needsUpdate=!0;for(let b=A*3,w=(g+A)*3;b<w;b+=3)if(E(ct,b,u,l),ct.needsUpdate=!0,x.intersectsTriangle(ct))return!0;return!1}});for(let h=A*3,x=(g+A)*3;h<x;h+=3){E(rt,h,u,l),rt.a.applyMatrix4(xt),rt.b.applyMatrix4(xt),rt.c.applyMatrix4(xt),rt.needsUpdate=!0;for(let b=0,w=p.count;b<w;b+=3)if(E(ct,b,p,m),ct.needsUpdate=!0,rt.intersectsTriangle(ct))return!0}}else{const f=i+8,u=r[i+6];return N(f,a,Nt),!!(s.intersectsBox(Nt)&&re(f,e,t,n,s)||(N(u,a,Nt),s.intersectsBox(Nt)&&re(u,e,t,n,s)))}}const Lt=new y.Matrix4,ce=new U,mt=new U,$n=new y.Vector3,Xn=new y.Vector3,Yn=new y.Vector3,Gn=new y.Vector3;function jn(i,e,t,n={},s={},a=0,o=1/0){e.boundingBox||e.computeBoundingBox(),ce.set(e.boundingBox.min,e.boundingBox.max,t),ce.needsUpdate=!0;const r=i.geometry,c=r.attributes.position,d=r.index,f=e.attributes.position,u=e.index,l=C.getPrimitive(),p=C.getPrimitive();let m=$n,A=Xn,g=null,h=null;s&&(g=Yn,h=Gn);let x=1/0,b=null,w=null;return Lt.copy(t).invert(),mt.matrix.copy(Lt),i.shapecast({boundsTraverseOrder:v=>ce.distanceToBox(v),intersectsBounds:(v,B,I)=>I<x&&I<o?(B&&(mt.min.copy(v.min),mt.max.copy(v.max),mt.needsUpdate=!0),!0):!1,intersectsRange:(v,B)=>{if(e.boundsTree)return e.boundsTree.shapecast({boundsTraverseOrder:T=>mt.distanceToBox(T),intersectsBounds:(T,M,S)=>S<x&&S<o,intersectsRange:(T,M)=>{for(let S=T,V=T+M;S<V;S++){E(p,3*S,u,f),p.a.applyMatrix4(t),p.b.applyMatrix4(t),p.c.applyMatrix4(t),p.needsUpdate=!0;for(let P=v,F=v+B;P<F;P++){E(l,3*P,d,c),l.needsUpdate=!0;const _=l.distanceToTriangle(p,m,g);if(_<x&&(A.copy(m),h&&h.copy(g),x=_,b=P,w=S),_<a)return!0}}}});{const I=tt(e);for(let T=0,M=I;T<M;T++){E(p,3*T,u,f),p.a.applyMatrix4(t),p.b.applyMatrix4(t),p.c.applyMatrix4(t),p.needsUpdate=!0;for(let S=v,V=v+B;S<V;S++){E(l,3*S,d,c),l.needsUpdate=!0;const P=l.distanceToTriangle(p,m,g);if(P<x&&(A.copy(m),h&&h.copy(g),x=P,b=S,w=T),P<a)return!0}}}}}),C.releasePrimitive(l),C.releasePrimitive(p),x===1/0?null:(n.point?n.point.copy(A):n.point=A.clone(),n.distance=x,n.faceIndex=b,s&&(s.point?s.point.copy(h):s.point=h.clone(),s.point.applyMatrix4(Lt),A.applyMatrix4(Lt),s.distance=A.sub(s.point).length(),s.faceIndex=w),n)}const ke=new y.Matrix4,ae=new y.Box3,le=new y.Box3;function Zn(i,e,t,n){let{intersectsRanges:s,intersectsTriangles:a}=n;const o=i.geometry.index,r=i.geometry.attributes.position,c=e.geometry.index,d=e.geometry.attributes.position;ke.copy(t).invert();const f=C.getPrimitive(),u=C.getPrimitive();if(a){const p=(m,A,g,h,x,b,w,v)=>{for(let B=g,I=g+h;B<I;B++){E(u,B*3,c,d),u.a.applyMatrix4(t),u.b.applyMatrix4(t),u.c.applyMatrix4(t),u.needsUpdate=!0;for(let T=m,M=m+A;T<M;T++)if(E(f,T*3,o,r),f.needsUpdate=!0,a(f,u,T,B,x,b,w,v))return!0}return!1};if(s){const m=s;s=function(A,g,h,x,b,w,v,B){return m(A,g,h,x,b,w,v,B)?!0:p(A,g,h,x,b,w,v,B)}}else s=p}e.getBoundingBox(le),le.applyMatrix4(t);const l=i.shapecast({intersectsBounds:p=>le.intersectsBox(p),intersectsRange:(p,m,A,g,h,x)=>(ae.copy(x),ae.applyMatrix4(ke),e.shapecast({intersectsBounds:b=>ae.intersectsBox(b),intersectsRange:(b,w,v,B,I)=>s(p,m,b,w,g,h,B,I)}))});return C.releasePrimitive(f),C.releasePrimitive(u),l}function Kn(i,e=null){e&&Array.isArray(e)&&(e=new Set(e));const t=i.geometry,n=t.index?t.index.array:null,s=t.attributes.position;let a,o,r,c,d=0;const f=i._roots;for(let l=0,p=f.length;l<p;l++)a=f[l],o=new Uint32Array(a),r=new Uint16Array(a),c=new Float32Array(a),u(0,d),d+=a.byteLength;function u(l,p,m=!1){const A=l*2;if(r[A+15]===Tt){const h=o[l+6],x=r[A+14];let b=1/0,w=1/0,v=1/0,B=-1/0,I=-1/0,T=-1/0;for(let M=h,S=h+x;M<S;M++){const V=3*i.resolveTriangleIndex(M);for(let P=0;P<3;P++){let F=V+P;F=n?n[F]:F;const _=s.getX(F),D=s.getY(F),R=s.getZ(F);_<b&&(b=_),_>B&&(B=_),D<w&&(w=D),D>I&&(I=D),R<v&&(v=R),R>T&&(T=R)}}return c[l+0]!==b||c[l+1]!==w||c[l+2]!==v||c[l+3]!==B||c[l+4]!==I||c[l+5]!==T?(c[l+0]=b,c[l+1]=w,c[l+2]=v,c[l+3]=B,c[l+4]=I,c[l+5]=T,!0):!1}else{const h=l+8,x=o[l+6],b=h+p,w=x+p;let v=m,B=!1,I=!1;e?v||(B=e.has(b),I=e.has(w),v=!B&&!I):(B=!0,I=!0);const T=v||B,M=v||I;let S=!1;T&&(S=u(h,p,v));let V=!1;M&&(V=u(x,p,v));const P=S||V;if(P)for(let F=0;F<3;F++){const _=h+F,D=x+F,R=c[_],ft=c[_+3],dt=c[D],pt=c[D+3];c[l+F]=R<dt?R:dt,c[l+F+3]=ft>pt?ft:pt}return P}}}const qe=new y.Vector3;function Jn(i,e,t,n,s){z.setBuffer(i._roots[e]),ue(0,i,t,n,s),z.clearBuffer()}function ue(i,e,t,n,s){const{float32Array:a,uint16Array:o,uint32Array:r}=z,c=i*2;if(k(c,o)){const f=H(i,r),u=W(c,o);Un(e,t,n,f,u,s)}else{const f=Y(i);j(f,a,n,qe)&&ue(f,e,t,n,s);const u=$(i,r);j(u,a,n,qe)&&ue(u,e,t,n,s)}}const He=new y.Vector3,Qn=["x","y","z"];function ti(i,e,t,n){z.setBuffer(i._roots[e]);const s=fe(0,i,t,n);return z.clearBuffer(),s}function fe(i,e,t,n){const{float32Array:s,uint16Array:a,uint32Array:o}=z;let r=i*2;if(k(r,a)){const d=H(i,o),f=W(r,a);return Ln(e,t,n,d,f)}else{const d=ee(i,o),f=Qn[d],l=n.direction[f]>=0;let p,m;l?(p=Y(i),m=$(i,o)):(p=$(i,o),m=Y(i));const g=j(p,s,n,He)?fe(p,e,t,n):null;if(g){const b=g.point[f];if(l?b<=s[m+d]:b>=s[m+d+3])return g}const x=j(m,s,n,He)?fe(m,e,t,n):null;return g&&x?g.distance<=x.distance?g:x:g||x||null}}const kt=new y.Box3,at=new L,lt=new L,bt=new y.Matrix4,Oe=new U,qt=new U;function ei(i,e,t,n){z.setBuffer(i._roots[e]);const s=de(0,i,t,n);return z.clearBuffer(),s}function de(i,e,t,n,s=null){const{float32Array:a,uint16Array:o,uint32Array:r}=z;let c=i*2;if(s===null&&(t.boundingBox||t.computeBoundingBox(),Oe.set(t.boundingBox.min,t.boundingBox.max,n),s=Oe),k(c,o)){const f=e.geometry,u=f.index,l=f.attributes.position,p=t.index,m=t.attributes.position,A=H(i,r),g=W(c,o);if(bt.copy(n).invert(),t.boundsTree)return N(i,a,qt),qt.matrix.copy(bt),qt.needsUpdate=!0,t.boundsTree.shapecast({intersectsBounds:x=>qt.intersectsBox(x),intersectsTriangle:x=>{x.a.applyMatrix4(n),x.b.applyMatrix4(n),x.c.applyMatrix4(n),x.needsUpdate=!0;for(let b=A,w=g+A;b<w;b++)if(E(lt,3*e.resolveTriangleIndex(b),u,l),lt.needsUpdate=!0,x.intersectsTriangle(lt))return!0;return!1}});for(let h=A,x=g+A;h<x;h++){const b=e.resolveTriangleIndex(h);E(at,3*b,u,l),at.a.applyMatrix4(bt),at.b.applyMatrix4(bt),at.c.applyMatrix4(bt),at.needsUpdate=!0;for(let w=0,v=p.count;w<v;w+=3)if(E(lt,w,p,m),lt.needsUpdate=!0,at.intersectsTriangle(lt))return!0}}else{const f=i+8,u=r[i+6];return N(f,a,kt),!!(s.intersectsBox(kt)&&de(f,e,t,n,s)||(N(u,a,kt),s.intersectsBox(kt)&&de(u,e,t,n,s)))}}const Ht=new y.Matrix4,pe=new U,gt=new U,ni=new y.Vector3,ii=new y.Vector3,si=new y.Vector3,oi=new y.Vector3;function ri(i,e,t,n={},s={},a=0,o=1/0){e.boundingBox||e.computeBoundingBox(),pe.set(e.boundingBox.min,e.boundingBox.max,t),pe.needsUpdate=!0;const r=i.geometry,c=r.attributes.position,d=r.index,f=e.attributes.position,u=e.index,l=C.getPrimitive(),p=C.getPrimitive();let m=ni,A=ii,g=null,h=null;s&&(g=si,h=oi);let x=1/0,b=null,w=null;return Ht.copy(t).invert(),gt.matrix.copy(Ht),i.shapecast({boundsTraverseOrder:v=>pe.distanceToBox(v),intersectsBounds:(v,B,I)=>I<x&&I<o?(B&&(gt.min.copy(v.min),gt.max.copy(v.max),gt.needsUpdate=!0),!0):!1,intersectsRange:(v,B)=>{if(e.boundsTree){const I=e.boundsTree;return I.shapecast({boundsTraverseOrder:T=>gt.distanceToBox(T),intersectsBounds:(T,M,S)=>S<x&&S<o,intersectsRange:(T,M)=>{for(let S=T,V=T+M;S<V;S++){const P=I.resolveTriangleIndex(S);E(p,3*P,u,f),p.a.applyMatrix4(t),p.b.applyMatrix4(t),p.c.applyMatrix4(t),p.needsUpdate=!0;for(let F=v,_=v+B;F<_;F++){const D=i.resolveTriangleIndex(F);E(l,3*D,d,c),l.needsUpdate=!0;const R=l.distanceToTriangle(p,m,g);if(R<x&&(A.copy(m),h&&h.copy(g),x=R,b=F,w=S),R<a)return!0}}}})}else{const I=tt(e);for(let T=0,M=I;T<M;T++){E(p,3*T,u,f),p.a.applyMatrix4(t),p.b.applyMatrix4(t),p.c.applyMatrix4(t),p.needsUpdate=!0;for(let S=v,V=v+B;S<V;S++){const P=i.resolveTriangleIndex(S);E(l,3*P,d,c),l.needsUpdate=!0;const F=l.distanceToTriangle(p,m,g);if(F<x&&(A.copy(m),h&&h.copy(g),x=F,b=S,w=T),F<a)return!0}}}}}),C.releasePrimitive(l),C.releasePrimitive(p),x===1/0?null:(n.point?n.point.copy(A):n.point=A.clone(),n.distance=x,n.faceIndex=b,s&&(s.point?s.point.copy(h):s.point=h.clone(),s.point.applyMatrix4(Ht),A.applyMatrix4(Ht),s.distance=A.sub(s.point).length(),s.faceIndex=w),n)}const We=new y.Matrix4,he=new y.Box3,ye=new y.Box3;function ci(i,e,t,n){let{intersectsRanges:s,intersectsTriangles:a}=n;const o=i.geometry.index,r=i.geometry.attributes.position,c=e.geometry.index,d=e.geometry.attributes.position;We.copy(t).invert();const f=C.getPrimitive(),u=C.getPrimitive();if(a){const p=(m,A,g,h,x,b,w,v)=>{for(let B=g,I=g+h;B<I;B++){const T=e.resolveTriangleIndex(B);E(u,T*3,c,d),u.a.applyMatrix4(t),u.b.applyMatrix4(t),u.c.applyMatrix4(t),u.needsUpdate=!0;for(let M=m,S=m+A;M<S;M++){const V=i.resolveTriangleIndex(M);if(E(f,V*3,o,r),f.needsUpdate=!0,a(f,u,M,B,x,b,w,v))return!0}}return!1};if(s){const m=s;s=function(A,g,h,x,b,w,v,B){return m(A,g,h,x,b,w,v,B)?!0:p(A,g,h,x,b,w,v,B)}}else s=p}e.getBoundingBox(ye),ye.applyMatrix4(t);const l=i.shapecast({intersectsBounds:p=>ye.intersectsBox(p),intersectsRange:(p,m,A,g,h,x)=>(he.copy(x),he.applyMatrix4(We),e.shapecast({intersectsBounds:b=>he.intersectsBox(b),intersectsRange:(b,w,v,B,I)=>s(p,m,b,w,g,h,B,I)}))});return C.releasePrimitive(f),C.releasePrimitive(u),l}function $e(){return typeof SharedArrayBuffer<"u"}const Ot=new U,Xe=new y.Box3;class Yt{static serialize(e,t={}){t={cloneBuffers:!0,...t};const n=e.geometry,s=e._roots,a=e._indirectBuffer,o=n.getIndex();let r;return t.cloneBuffers?r={roots:s.map(c=>c.slice()),index:o.array.slice(),indirectBuffer:a?a.slice():null}:r={roots:s,index:o.array,indirectBuffer:a},r}static deserialize(e,t,n={}){n={setIndex:!0,indirect:!!e.indirectBuffer,...n};const{index:s,roots:a,indirectBuffer:o}=e,r=new Yt(t,{...n,[Kt]:!0});if(r._roots=a,r._indirectBuffer=o||null,n.setIndex){const c=t.getIndex();if(c===null){const d=new y.BufferAttribute(e.index,1,!1);t.setIndex(d)}else c.array!==s&&(c.array.set(s),c.needsUpdate=!0)}return r}get indirect(){return!!this._indirectBuffer}constructor(e,t={}){if(e.isBufferGeometry){if(e.index&&e.index.isInterleavedBufferAttribute)throw new Error("MeshBVH: InterleavedBufferAttribute is not supported for the index attribute.")}else throw new Error("MeshBVH: Only BufferGeometries are supported.");if(t=Object.assign({strategy:Gt,maxDepth:40,maxLeafTris:10,verbose:!0,useSharedArrayBuffer:!1,setBoundingBox:!0,onProgress:null,indirect:!1,[Kt]:!1},t),t.useSharedArrayBuffer&&!$e())throw new Error("MeshBVH: SharedArrayBuffer is not available.");this.geometry=e,this._roots=null,this._indirectBuffer=null,t[Kt]||(Bn(this,t),!e.boundingBox&&t.setBoundingBox&&(e.boundingBox=this.getBoundingBox(new y.Box3)));const{_indirectBuffer:n}=this;this.resolveTriangleIndex=t.indirect?s=>n[s]:s=>s}refit(e=null){return(this.indirect?Kn:Nn)(this,e)}traverse(e,t=0){const n=this._roots[t],s=new Uint32Array(n),a=new Uint16Array(n);o(0);function o(r,c=0){const d=r*2,f=a[d+15]===Tt;if(f){const u=s[r+6],l=a[d+14];e(c,f,new Float32Array(n,r*4,6),u,l)}else{const u=r+Z/4,l=s[r+6],p=s[r+7];e(c,f,new Float32Array(n,r*4,6),p)||(o(u,c+1),o(l,c+1))}}}raycast(e,t=y.FrontSide){const n=this._roots,s=this.geometry,a=[],o=t.isMaterial,r=Array.isArray(t),c=s.groups,d=o?t.side:t,f=this.indirect?Jn:qn;for(let u=0,l=n.length;u<l;u++){const p=r?t[c[u].materialIndex].side:d,m=a.length;if(f(this,u,p,e,a),r){const A=c[u].materialIndex;for(let g=m,h=a.length;g<h;g++)a[g].face.materialIndex=A}}return a}raycastFirst(e,t=y.FrontSide){const n=this._roots,s=this.geometry,a=t.isMaterial,o=Array.isArray(t);let r=null;const c=s.groups,d=a?t.side:t,f=this.indirect?ti:On;for(let u=0,l=n.length;u<l;u++){const p=o?t[c[u].materialIndex].side:d,m=f(this,u,p,e);m!=null&&(r==null||m.distance<r.distance)&&(r=m,o&&(m.face.materialIndex=c[u].materialIndex))}return r}intersectsGeometry(e,t){let n=!1;const s=this._roots,a=this.indirect?ei:Wn;for(let o=0,r=s.length;o<r&&(n=a(this,o,e,t),!n);o++);return n}shapecast(e){const t=C.getPrimitive(),n=this.indirect?kn:Rn;let{boundsTraverseOrder:s,intersectsBounds:a,intersectsRange:o,intersectsTriangle:r}=e;if(o&&r){const u=o;o=(l,p,m,A,g)=>u(l,p,m,A,g)?!0:n(l,p,this,r,m,A,t)}else o||(r?o=(u,l,p,m)=>n(u,l,this,r,p,m,t):o=(u,l,p)=>p);let c=!1,d=0;const f=this._roots;for(let u=0,l=f.length;u<l;u++){const p=f[u];if(c=Vn(this,u,a,o,s,d),c)break;d+=p.byteLength}return C.releasePrimitive(t),c}bvhcast(e,t,n){return(this.indirect?ci:Zn)(this,e,t,n)}intersectsBox(e,t){return Ot.set(e.min,e.max,t),Ot.needsUpdate=!0,this.shapecast({intersectsBounds:n=>Ot.intersectsBox(n),intersectsTriangle:n=>Ot.intersectsTriangle(n)})}intersectsSphere(e){return this.shapecast({intersectsBounds:t=>e.intersectsBox(t),intersectsTriangle:t=>t.intersectsSphere(e)})}closestPointToGeometry(e,t,n={},s={},a=0,o=1/0){return(this.indirect?ri:jn)(this,e,t,n,s,a,o)}closestPointToPoint(e,t={},n=0,s=1/0){return Fn(this,e,t,n,s)}getBoundingBox(e){return e.makeEmpty(),this._roots.forEach(n=>{N(0,new Float32Array(n),Xe),e.union(Xe)}),e}}const Ye=new y.Box3;class ai extends y.Object3D{get isMesh(){return!this.displayEdges}get isLineSegments(){return this.displayEdges}get isLine(){return this.displayEdges}constructor(e,t,n=10,s=0){super(),this.material=t,this.geometry=new y.BufferGeometry,this.name="MeshBVHRootVisualizer",this.depth=n,this.displayParents=!1,this.mesh=e,this.displayEdges=!0,this._group=s}raycast(){}update(){const e=this.geometry,t=this.mesh.geometry.boundsTree,n=this._group;if(e.dispose(),this.visible=!1,t){const s=this.depth-1,a=this.displayParents;let o=0;t.traverse((l,p)=>{if(l===s||p)return o++,!0;a&&o++},n);let r=0;const c=new Float32Array(8*3*o);t.traverse((l,p,m)=>{const A=l===s||p;if(A||a){N(0,m,Ye);const{min:g,max:h}=Ye;for(let x=-1;x<=1;x+=2){const b=x<0?g.x:h.x;for(let w=-1;w<=1;w+=2){const v=w<0?g.y:h.y;for(let B=-1;B<=1;B+=2){const I=B<0?g.z:h.z;c[r+0]=b,c[r+1]=v,c[r+2]=I,r+=3}}}return A}},n);let d,f;this.displayEdges?f=new Uint8Array([0,4,1,5,2,6,3,7,0,2,1,3,4,6,5,7,0,1,2,3,4,5,6,7]):f=new Uint8Array([0,1,2,2,1,3,4,6,5,6,7,5,1,4,5,0,4,1,2,3,6,3,7,6,0,2,4,2,6,4,1,5,3,3,5,7]),c.length>65535?d=new Uint32Array(f.length*o):d=new Uint16Array(f.length*o);const u=f.length;for(let l=0;l<o;l++){const p=l*8,m=l*u;for(let A=0;A<u;A++)d[m+A]=p+f[A]}e.setIndex(new y.BufferAttribute(d,1,!1)),e.setAttribute("position",new y.BufferAttribute(c,3,!1)),this.visible=!0}}}class ge extends y.Group{get color(){return this.edgeMaterial.color}get opacity(){return this.edgeMaterial.opacity}set opacity(e){this.edgeMaterial.opacity=e,this.meshMaterial.opacity=e}constructor(e,t=10){super(),this.name="MeshBVHVisualizer",this.depth=t,this.mesh=e,this.displayParents=!1,this.displayEdges=!0,this._roots=[];const n=new y.LineBasicMaterial({color:65416,transparent:!0,opacity:.3,depthWrite:!1}),s=new y.MeshBasicMaterial({color:65416,transparent:!0,opacity:.3,depthWrite:!1});s.color=n.color,this.edgeMaterial=n,this.meshMaterial=s,this.update()}update(){const e=this.mesh.geometry.boundsTree,t=e?e._roots.length:0;for(;this._roots.length>t;){const n=this._roots.pop();n.geometry.dispose(),this.remove(n)}for(let n=0;n<t;n++){if(n>=this._roots.length){const a=new ai(this.mesh,this.edgeMaterial,this.depth,n);this.add(a),this._roots.push(a)}const s=this._roots[n];s.depth=this.depth,s.mesh=this.mesh,s.displayParents=this.displayParents,s.displayEdges=this.displayEdges,s.material=this.displayEdges?this.edgeMaterial:this.meshMaterial,s.update()}}updateMatrixWorld(...e){this.position.copy(this.mesh.position),this.rotation.copy(this.mesh.rotation),this.scale.copy(this.mesh.scale),super.updateMatrixWorld(...e)}copy(e){this.depth=e.depth,this.mesh=e.mesh}clone(){return new ge(this.mesh,this.depth)}dispose(){this.edgeMaterial.dispose(),this.meshMaterial.dispose();const e=this.children;for(let t=0,n=e.length;t<n;t++)e[t].geometry.dispose()}}const wt=new y.Box3,Ge=new y.Box3,ut=new y.Vector3;function je(i){switch(typeof i){case"number":return 8;case"string":return i.length*2;case"boolean":return 4;default:return 0}}function li(i){return/(Uint|Int|Float)(8|16|32)Array/.test(i.constructor.name)}function ui(i,e){const t={nodeCount:0,leafNodeCount:0,depth:{min:1/0,max:-1/0},tris:{min:1/0,max:-1/0},splits:[0,0,0],surfaceAreaScore:0};return i.traverse((n,s,a,o,r)=>{const c=a[3]-a[0],d=a[1+3]-a[1],f=a[2+3]-a[2],u=2*(c*d+d*f+f*c);t.nodeCount++,s?(t.leafNodeCount++,t.depth.min=Math.min(n,t.depth.min),t.depth.max=Math.max(n,t.depth.max),t.tris.min=Math.min(r,t.tris.min),t.tris.max=Math.max(r,t.tris.max),t.surfaceAreaScore+=u*Bt*r):(t.splits[o]++,t.surfaceAreaScore+=u*Zt)},e),t.tris.min===1/0&&(t.tris.min=0,t.tris.max=0),t.depth.min===1/0&&(t.depth.min=0,t.depth.max=0),t}function fi(i){return i._roots.map((e,t)=>ui(i,t))}function di(i){const e=new Set,t=[i];let n=0;for(;t.length;){const s=t.pop();if(!e.has(s)){e.add(s);for(let a in s){if(!s.hasOwnProperty(a))continue;n+=je(a);const o=s[a];o&&(typeof o=="object"||typeof o=="function")?li(o)||$e()&&o instanceof SharedArrayBuffer||o instanceof ArrayBuffer?n+=o.byteLength:t.push(o):n+=je(o)}}}return n}function pi(i){const e=i.geometry,t=[],n=e.index,s=e.getAttribute("position");let a=!0;return i.traverse((o,r,c,d,f)=>{const u={depth:o,isLeaf:r,boundingData:c,offset:d,count:f};t[o]=u,N(0,c,wt);const l=t[o-1];if(r)for(let p=d*3,m=(d+f)*3;p<m;p+=3){const A=n.getX(p),g=n.getX(p+1),h=n.getX(p+2);let x;ut.fromBufferAttribute(s,A),x=wt.containsPoint(ut),ut.fromBufferAttribute(s,g),x=x&&wt.containsPoint(ut),ut.fromBufferAttribute(s,h),x=x&&wt.containsPoint(ut),console.assert(x,"Leaf bounds does not fully contain triangle."),a=a&&x}if(l){N(0,c,Ge);const p=Ge.containsBox(wt);console.assert(p,"Parent bounds does not fully contain child."),a=a&&p}}),a}function hi(i){const e=[];return i.traverse((t,n,s,a,o)=>{const r={bounds:N(0,s,new y.Box3)};n?(r.count=o,r.offset=a):(r.left=null,r.right=null),e[t]=r;const c=e[t-1];c&&(c.left===null?c.left=r:c.right=r)}),e[0]}function Ze(i,e,t){return i===null||(i.point.applyMatrix4(e.matrixWorld),i.distance=i.point.distanceTo(t.ray.origin),i.object=e,i.distance<t.near||i.distance>t.far)?null:i}const xe=new y.Ray,Ke=new y.Matrix4,yi=y.Mesh.prototype.raycast;function xi(i,e){if(this.geometry.boundsTree){if(this.material===void 0)return;Ke.copy(this.matrixWorld).invert(),xe.copy(i.ray).applyMatrix4(Ke);const t=this.geometry.boundsTree;if(i.firstHitOnly===!0){const n=Ze(t.raycastFirst(xe,this.material),this,i);n&&e.push(n)}else{const n=t.raycast(xe,this.material);for(let s=0,a=n.length;s<a;s++){const o=Ze(n[s],this,i);o&&e.push(o)}}}else yi.call(this,i,e)}function mi(i){return this.boundsTree=new Yt(this,i),this.boundsTree}function bi(){this.boundsTree=null}function gi(i){switch(i){case 1:return"R";case 2:return"RG";case 3:return"RGBA";case 4:return"RGBA"}throw new Error}function wi(i){switch(i){case 1:return y.RedFormat;case 2:return y.RGFormat;case 3:return y.RGBAFormat;case 4:return y.RGBAFormat}}function Je(i){switch(i){case 1:return y.RedIntegerFormat;case 2:return y.RGIntegerFormat;case 3:return y.RGBAIntegerFormat;case 4:return y.RGBAIntegerFormat}}class Wt extends y.DataTexture{constructor(){super(),this.minFilter=y.NearestFilter,this.magFilter=y.NearestFilter,this.generateMipmaps=!1,this.overrideItemSize=null,this._forcedType=null}updateFrom(e){const t=this.overrideItemSize,n=e.itemSize,s=e.count;if(t!==null){if(n*s%t!==0)throw new Error("VertexAttributeTexture: overrideItemSize must divide evenly into buffer length.");e.itemSize=t,e.count=s*n/t}const a=e.itemSize,o=e.count,r=e.normalized,c=e.array.constructor,d=c.BYTES_PER_ELEMENT;let f=this._forcedType,u=a;if(f===null)switch(c){case Float32Array:f=y.FloatType;break;case Uint8Array:case Uint16Array:case Uint32Array:f=y.UnsignedIntType;break;case Int8Array:case Int16Array:case Int32Array:f=y.IntType;break}let l,p,m,A,g=gi(a);switch(f){case y.FloatType:m=1,p=wi(a),r&&d===1?(A=c,g+="8",c===Uint8Array?l=y.UnsignedByteType:(l=y.ByteType,g+="_SNORM")):(A=Float32Array,g+="32F",l=y.FloatType);break;case y.IntType:g+=d*8+"I",m=r?Math.pow(2,c.BYTES_PER_ELEMENT*8-1):1,p=Je(a),d===1?(A=Int8Array,l=y.ByteType):d===2?(A=Int16Array,l=y.ShortType):(A=Int32Array,l=y.IntType);break;case y.UnsignedIntType:g+=d*8+"UI",m=r?Math.pow(2,c.BYTES_PER_ELEMENT*8-1):1,p=Je(a),d===1?(A=Uint8Array,l=y.UnsignedByteType):d===2?(A=Uint16Array,l=y.UnsignedShortType):(A=Uint32Array,l=y.UnsignedIntType);break}u===3&&(p===y.RGBAFormat||p===y.RGBAIntegerFormat)&&(u=4);const h=Math.ceil(Math.sqrt(o)),x=u*h*h,b=new A(x),w=e.normalized;e.normalized=!1;for(let v=0;v<o;v++){const B=u*v;b[B]=e.getX(v)/m,a>=2&&(b[B+1]=e.getY(v)/m),a>=3&&(b[B+2]=e.getZ(v)/m,u===4&&(b[B+3]=1)),a>=4&&(b[B+3]=e.getW(v)/m)}e.normalized=w,this.internalFormat=g,this.format=p,this.type=l,this.image.width=h,this.image.height=h,this.image.data=b,this.needsUpdate=!0,this.dispose(),e.itemSize=n,e.count=s}}class Qe extends Wt{constructor(){super(),this._forcedType=y.UnsignedIntType}}class vi extends Wt{constructor(){super(),this._forcedType=y.IntType}}class tn extends Wt{constructor(){super(),this._forcedType=y.FloatType}}class Ai{constructor(){this.index=new Qe,this.position=new tn,this.bvhBounds=new y.DataTexture,this.bvhContents=new y.DataTexture,this._cachedIndexAttr=null,this.index.overrideItemSize=3}updateFrom(e){const{geometry:t}=e;if(Ti(e,this.bvhBounds,this.bvhContents),this.position.updateFrom(t.attributes.position),e.indirect){const n=e._indirectBuffer;if(this._cachedIndexAttr===null||this._cachedIndexAttr.count!==n.length)if(t.index)this._cachedIndexAttr=t.index.clone();else{const s=Be(Ae(t));this._cachedIndexAttr=new y.BufferAttribute(s,1,!1)}Bi(t,n,this._cachedIndexAttr),this.index.updateFrom(this._cachedIndexAttr)}else this.index.updateFrom(t.index)}dispose(){const{index:e,position:t,bvhBounds:n,bvhContents:s}=this;e&&e.dispose(),t&&t.dispose(),n&&n.dispose(),s&&s.dispose()}}function Bi(i,e,t){const n=t.array,s=i.index?i.index.array:null;for(let a=0,o=e.length;a<o;a++){const r=3*a,c=3*e[a];for(let d=0;d<3;d++)n[r+d]=s?s[c+d]:c+d}}function Ti(i,e,t){const n=i._roots;if(n.length!==1)throw new Error("MeshBVHUniformStruct: Multi-root BVHs not supported.");const s=n[0],a=new Uint16Array(s),o=new Uint32Array(s),r=new Float32Array(s),c=s.byteLength/Z,d=2*Math.ceil(Math.sqrt(c/2)),f=new Float32Array(4*d*d),u=Math.ceil(Math.sqrt(c)),l=new Uint32Array(2*u*u);for(let p=0;p<c;p++){const m=p*Z/4,A=m*2,g=m;for(let h=0;h<3;h++)f[8*p+0+h]=r[g+0+h],f[8*p+4+h]=r[g+3+h];if(k(A,a)){const h=W(A,a),x=H(m,o),b=4294901760|h;l[p*2+0]=b,l[p*2+1]=x}else{const h=4*$(m,o)/Z,x=ee(m,o);l[p*2+0]=x,l[p*2+1]=h}}e.image.data=f,e.image.width=d,e.image.height=d,e.format=y.RGBAFormat,e.type=y.FloatType,e.internalFormat="RGBA32F",e.minFilter=y.NearestFilter,e.magFilter=y.NearestFilter,e.generateMipmaps=!1,e.needsUpdate=!0,e.dispose(),t.image.data=l,t.image.width=u,t.image.height=u,t.format=y.RGIntegerFormat,t.type=y.UnsignedIntType,t.internalFormat="RG32UI",t.minFilter=y.NearestFilter,t.magFilter=y.NearestFilter,t.generateMipmaps=!1,t.needsUpdate=!0,t.dispose()}const Ii=`
#ifndef TRI_INTERSECT_EPSILON
#define TRI_INTERSECT_EPSILON 1e-5
#endif

#ifndef INFINITY
#define INFINITY 1e20
#endif

struct BVH {

	usampler2D index;
	sampler2D position;

	sampler2D bvhBounds;
	usampler2D bvhContents;

};
`,Mi=`

// Utilities
uvec4 uTexelFetch1D( usampler2D tex, uint index ) {

	uint width = uint( textureSize( tex, 0 ).x );
	uvec2 uv;
	uv.x = index % width;
	uv.y = index / width;

	return texelFetch( tex, ivec2( uv ), 0 );

}

ivec4 iTexelFetch1D( isampler2D tex, uint index ) {

	uint width = uint( textureSize( tex, 0 ).x );
	uvec2 uv;
	uv.x = index % width;
	uv.y = index / width;

	return texelFetch( tex, ivec2( uv ), 0 );

}

vec4 texelFetch1D( sampler2D tex, uint index ) {

	uint width = uint( textureSize( tex, 0 ).x );
	uvec2 uv;
	uv.x = index % width;
	uv.y = index / width;

	return texelFetch( tex, ivec2( uv ), 0 );

}

vec4 textureSampleBarycoord( sampler2D tex, vec3 barycoord, uvec3 faceIndices ) {

	return
		barycoord.x * texelFetch1D( tex, faceIndices.x ) +
		barycoord.y * texelFetch1D( tex, faceIndices.y ) +
		barycoord.z * texelFetch1D( tex, faceIndices.z );

}

void ndcToCameraRay(
	vec2 coord, mat4 cameraWorld, mat4 invProjectionMatrix,
	out vec3 rayOrigin, out vec3 rayDirection
) {

	// get camera look direction and near plane for camera clipping
	vec4 lookDirection = cameraWorld * vec4( 0.0, 0.0, - 1.0, 0.0 );
	vec4 nearVector = invProjectionMatrix * vec4( 0.0, 0.0, - 1.0, 1.0 );
	float near = abs( nearVector.z / nearVector.w );

	// get the camera direction and position from camera matrices
	vec4 origin = cameraWorld * vec4( 0.0, 0.0, 0.0, 1.0 );
	vec4 direction = invProjectionMatrix * vec4( coord, 0.5, 1.0 );
	direction /= direction.w;
	direction = cameraWorld * direction - origin;

	// slide the origin along the ray until it sits at the near clip plane position
	origin.xyz += direction.xyz * near / dot( direction, lookDirection );

	rayOrigin = origin.xyz;
	rayDirection = direction.xyz;

}

// Raycasting
float intersectsBounds( vec3 rayOrigin, vec3 rayDirection, vec3 boundsMin, vec3 boundsMax ) {

	// https://www.reddit.com/r/opengl/comments/8ntzz5/fast_glsl_ray_box_intersection/
	// https://tavianator.com/2011/ray_box.html
	vec3 invDir = 1.0 / rayDirection;

	// find intersection distances for each plane
	vec3 tMinPlane = invDir * ( boundsMin - rayOrigin );
	vec3 tMaxPlane = invDir * ( boundsMax - rayOrigin );

	// get the min and max distances from each intersection
	vec3 tMinHit = min( tMaxPlane, tMinPlane );
	vec3 tMaxHit = max( tMaxPlane, tMinPlane );

	// get the furthest hit distance
	vec2 t = max( tMinHit.xx, tMinHit.yz );
	float t0 = max( t.x, t.y );

	// get the minimum hit distance
	t = min( tMaxHit.xx, tMaxHit.yz );
	float t1 = min( t.x, t.y );

	// set distance to 0.0 if the ray starts inside the box
	float dist = max( t0, 0.0 );

	return t1 >= dist ? dist : INFINITY;

}

bool intersectsTriangle(
	vec3 rayOrigin, vec3 rayDirection, vec3 a, vec3 b, vec3 c,
	out vec3 barycoord, out vec3 norm, out float dist, out float side
) {

	// https://stackoverflow.com/questions/42740765/intersection-between-line-and-triangle-in-3d
	vec3 edge1 = b - a;
	vec3 edge2 = c - a;
	norm = cross( edge1, edge2 );

	float det = - dot( rayDirection, norm );
	float invdet = 1.0 / det;

	vec3 AO = rayOrigin - a;
	vec3 DAO = cross( AO, rayDirection );

	vec4 uvt;
	uvt.x = dot( edge2, DAO ) * invdet;
	uvt.y = - dot( edge1, DAO ) * invdet;
	uvt.z = dot( AO, norm ) * invdet;
	uvt.w = 1.0 - uvt.x - uvt.y;

	// set the hit information
	barycoord = uvt.wxy; // arranged in A, B, C order
	dist = uvt.z;
	side = sign( det );
	norm = side * normalize( norm );

	// add an epsilon to avoid misses between triangles
	uvt += vec4( TRI_INTERSECT_EPSILON );

	return all( greaterThanEqual( uvt, vec4( 0.0 ) ) );

}

bool intersectTriangles(
	BVH bvh, vec3 rayOrigin, vec3 rayDirection, uint offset, uint count,
	inout float minDistance,

	// output variables
	out uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,
	out float side, out float dist
) {

	bool found = false;
	vec3 localBarycoord, localNormal;
	float localDist, localSide;
	for ( uint i = offset, l = offset + count; i < l; i ++ ) {

		uvec3 indices = uTexelFetch1D( bvh.index, i ).xyz;
		vec3 a = texelFetch1D( bvh.position, indices.x ).rgb;
		vec3 b = texelFetch1D( bvh.position, indices.y ).rgb;
		vec3 c = texelFetch1D( bvh.position, indices.z ).rgb;

		if (
			intersectsTriangle( rayOrigin, rayDirection, a, b, c, localBarycoord, localNormal, localDist, localSide )
			&& localDist < minDistance
		) {

			found = true;
			minDistance = localDist;

			faceIndices = uvec4( indices.xyz, i );
			faceNormal = localNormal;

			side = localSide;
			barycoord = localBarycoord;
			dist = localDist;

		}

	}

	return found;

}

float intersectsBVHNodeBounds( vec3 rayOrigin, vec3 rayDirection, BVH bvh, uint currNodeIndex ) {

	vec3 boundsMin = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 0u ).xyz;
	vec3 boundsMax = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 1u ).xyz;
	return intersectsBounds( rayOrigin, rayDirection, boundsMin, boundsMax );

}

bool bvhIntersectFirstHit(
	BVH bvh, vec3 rayOrigin, vec3 rayDirection,

	// output variables
	out uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,
	out float side, out float dist
) {

	// stack needs to be twice as long as the deepest tree we expect because
	// we push both the left and right child onto the stack every traversal
	int ptr = 0;
	uint stack[ 60 ];
	stack[ 0 ] = 0u;

	float triangleDistance = 1e20;
	bool found = false;
	while ( ptr > - 1 && ptr < 60 ) {

		uint currNodeIndex = stack[ ptr ];
		ptr --;

		// check if we intersect the current bounds
		float boundsHitDistance = intersectsBVHNodeBounds( rayOrigin, rayDirection, bvh, currNodeIndex );
		if ( boundsHitDistance == INFINITY || boundsHitDistance > triangleDistance ) {

			continue;

		}

		uvec2 boundsInfo = uTexelFetch1D( bvh.bvhContents, currNodeIndex ).xy;
		bool isLeaf = bool( boundsInfo.x & 0xffff0000u );

		if ( isLeaf ) {

			uint count = boundsInfo.x & 0x0000ffffu;
			uint offset = boundsInfo.y;

			found = intersectTriangles(
				bvh, rayOrigin, rayDirection, offset, count, triangleDistance,
				faceIndices, faceNormal, barycoord, side, dist
			) || found;

		} else {

			uint leftIndex = currNodeIndex + 1u;
			uint splitAxis = boundsInfo.x & 0x0000ffffu;
			uint rightIndex = boundsInfo.y;

			bool leftToRight = rayDirection[ splitAxis ] >= 0.0;
			uint c1 = leftToRight ? leftIndex : rightIndex;
			uint c2 = leftToRight ? rightIndex : leftIndex;

			// set c2 in the stack so we traverse it later. We need to keep track of a pointer in
			// the stack while we traverse. The second pointer added is the one that will be
			// traversed first
			ptr ++;
			stack[ ptr ] = c2;

			ptr ++;
			stack[ ptr ] = c1;

		}

	}

	return found;

}
`,Pi=`

float dot2( in vec3 v ) {

	return dot( v, v );

}


// https://www.shadertoy.com/view/ttfGWl
vec3 closestPointToTriangle( vec3 p, vec3 v0, vec3 v1, vec3 v2, out vec3 barycoord ) {

    vec3 v10 = v1 - v0;
    vec3 v21 = v2 - v1;
    vec3 v02 = v0 - v2;

	vec3 p0 = p - v0;
	vec3 p1 = p - v1;
	vec3 p2 = p - v2;

    vec3 nor = cross( v10, v02 );

    // method 2, in barycentric space
    vec3  q = cross( nor, p0 );
    float d = 1.0 / dot2( nor );
    float u = d * dot( q, v02 );
    float v = d * dot( q, v10 );
    float w = 1.0 - u - v;

	if( u < 0.0 ) {

		w = clamp( dot( p2, v02 ) / dot2( v02 ), 0.0, 1.0 );
		u = 0.0;
		v = 1.0 - w;

	} else if( v < 0.0 ) {

		u = clamp( dot( p0, v10 ) / dot2( v10 ), 0.0, 1.0 );
		v = 0.0;
		w = 1.0 - u;

	} else if( w < 0.0 ) {

		v = clamp( dot( p1, v21 ) / dot2( v21 ), 0.0, 1.0 );
		w = 0.0;
		u = 1.0-v;

	}

	barycoord = vec3( u, v, w );
    return u * v1 + v * v2 + w * v0;

}

float distanceToTriangles(
	BVH bvh, vec3 point, uint offset, uint count, float closestDistanceSquared,

	out uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord, out float side, out vec3 outPoint
) {

	bool found = false;
	uvec3 localIndices;
	vec3 localBarycoord;
	vec3 localNormal;
	for ( uint i = offset, l = offset + count; i < l; i ++ ) {

		uvec3 indices = uTexelFetch1D( bvh.index, i ).xyz;
		vec3 a = texelFetch1D( bvh.position, indices.x ).rgb;
		vec3 b = texelFetch1D( bvh.position, indices.y ).rgb;
		vec3 c = texelFetch1D( bvh.position, indices.z ).rgb;

		// get the closest point and barycoord
		vec3 closestPoint = closestPointToTriangle( point, a, b, c, localBarycoord );
		vec3 delta = point - closestPoint;
		float sqDist = dot2( delta );
		if ( sqDist < closestDistanceSquared ) {

			// set the output results
			closestDistanceSquared = sqDist;
			faceIndices = uvec4( indices.xyz, i );
			faceNormal = normalize( cross( a - b, b - c ) );
			barycoord = localBarycoord;
			outPoint = closestPoint;
			side = sign( dot( faceNormal, delta ) );

		}

	}

	return closestDistanceSquared;

}

float distanceSqToBounds( vec3 point, vec3 boundsMin, vec3 boundsMax ) {

	vec3 clampedPoint = clamp( point, boundsMin, boundsMax );
	vec3 delta = point - clampedPoint;
	return dot( delta, delta );

}

float distanceSqToBVHNodeBoundsPoint( vec3 point, BVH bvh, uint currNodeIndex ) {

	vec3 boundsMin = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 0u ).xyz;
	vec3 boundsMax = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 1u ).xyz;
	return distanceSqToBounds( point, boundsMin, boundsMax );

}

float bvhClosestPointToPoint(
	BVH bvh, vec3 point,

	// output variables
	out uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,
	out float side, out vec3 outPoint
 ) {

	// stack needs to be twice as long as the deepest tree we expect because
	// we push both the left and right child onto the stack every traversal
	int ptr = 0;
	uint stack[ 60 ];
	stack[ 0 ] = 0u;
	float closestDistanceSquared = pow( 100000.0, 2.0 );
	bool found = false;
	while ( ptr > - 1 && ptr < 60 ) {

		uint currNodeIndex = stack[ ptr ];
		ptr --;

		// check if we intersect the current bounds
		float boundsHitDistance = distanceSqToBVHNodeBoundsPoint( point, bvh, currNodeIndex );
		if ( boundsHitDistance > closestDistanceSquared ) {

			continue;

		}

		uvec2 boundsInfo = uTexelFetch1D( bvh.bvhContents, currNodeIndex ).xy;
		bool isLeaf = bool( boundsInfo.x & 0xffff0000u );
		if ( isLeaf ) {

			uint count = boundsInfo.x & 0x0000ffffu;
			uint offset = boundsInfo.y;
			closestDistanceSquared = distanceToTriangles(
				bvh, point, offset, count, closestDistanceSquared,

				// outputs
				faceIndices, faceNormal, barycoord, side, outPoint
			);

		} else {

			uint leftIndex = currNodeIndex + 1u;
			uint splitAxis = boundsInfo.x & 0x0000ffffu;
			uint rightIndex = boundsInfo.y;
			bool leftToRight = distanceSqToBVHNodeBoundsPoint( point, bvh, leftIndex ) < distanceSqToBVHNodeBoundsPoint( point, bvh, rightIndex );//rayDirection[ splitAxis ] >= 0.0;
			uint c1 = leftToRight ? leftIndex : rightIndex;
			uint c2 = leftToRight ? rightIndex : leftIndex;

			// set c2 in the stack so we traverse it later. We need to keep track of a pointer in
			// the stack while we traverse. The second pointer added is the one that will be
			// traversed first
			ptr ++;
			stack[ ptr ] = c2;
			ptr ++;
			stack[ ptr ] = c1;

		}

	}

	return sqrt( closestDistanceSquared );

}
`,K=new y.Vector3,J=new y.Vector3,Q=new y.Vector3,en=new y.Vector4,$t=new y.Vector3,me=new y.Vector3,nn=new y.Vector4,sn=new y.Vector4,Xt=new y.Matrix4,on=new y.Matrix4;function vt(i,e){if(!i&&!e)return;const t=i.count===e.count,n=i.normalized===e.normalized,s=i.array.constructor===e.array.constructor,a=i.itemSize===e.itemSize;if(!t||!n||!s||!a)throw new Error}function At(i,e=null){const t=i.array.constructor,n=i.normalized,s=i.itemSize,a=e===null?i.count:e;return new y.BufferAttribute(new t(s*a),s,n)}function rn(i,e,t=0){if(i.isInterleavedBufferAttribute){const n=i.itemSize;for(let s=0,a=i.count;s<a;s++){const o=s+t;e.setX(o,i.getX(s)),n>=2&&e.setY(o,i.getY(s)),n>=3&&e.setZ(o,i.getZ(s)),n>=4&&e.setW(o,i.getW(s))}}else{const n=e.array,s=n.constructor,a=n.BYTES_PER_ELEMENT*i.itemSize*t;new s(n.buffer,a,i.array.length).set(i.array)}}function Si(i,e,t){const n=i.elements,s=e.elements;for(let a=0,o=s.length;a<o;a++)n[a]+=s[a]*t}function cn(i,e,t){const n=i.skeleton,s=i.geometry,a=n.bones,o=n.boneInverses;nn.fromBufferAttribute(s.attributes.skinIndex,e),sn.fromBufferAttribute(s.attributes.skinWeight,e),Xt.elements.fill(0);for(let r=0;r<4;r++){const c=sn.getComponent(r);if(c!==0){const d=nn.getComponent(r);on.multiplyMatrices(a[d].matrixWorld,o[d]),Si(Xt,on,c)}}return Xt.multiply(i.bindMatrix).premultiply(i.bindMatrixInverse),t.transformDirection(Xt),t}function be(i,e,t,n,s){$t.set(0,0,0);for(let a=0,o=i.length;a<o;a++){const r=e[a],c=i[a];r!==0&&(me.fromBufferAttribute(c,n),t?$t.addScaledVector(me,r):$t.addScaledVector(me.sub(s),r))}s.add($t)}function Vi(i,e={useGroups:!1,updateIndex:!1,skipAttributes:[]},t=new y.BufferGeometry){const n=i[0].index!==null,{useGroups:s=!1,updateIndex:a=!1,skipAttributes:o=[]}=e,r=new Set(Object.keys(i[0].attributes)),c={};let d=0;t.clearGroups();for(let f=0;f<i.length;++f){const u=i[f];let l=0;if(n!==(u.index!==null))throw new Error("StaticGeometryGenerator: All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.");for(const p in u.attributes){if(!r.has(p))throw new Error('StaticGeometryGenerator: All geometries must have compatible attributes; make sure "'+p+'" attribute exists among all geometries, or in none of them.');c[p]===void 0&&(c[p]=[]),c[p].push(u.attributes[p]),l++}if(l!==r.size)throw new Error("StaticGeometryGenerator: Make sure all geometries have the same number of attributes.");if(s){let p;if(n)p=u.index.count;else if(u.attributes.position!==void 0)p=u.attributes.position.count;else throw new Error("StaticGeometryGenerator: The geometry must have either an index or a position attribute");t.addGroup(d,p,f),d+=p}}if(n){let f=!1;if(!t.index){let u=0;for(let l=0;l<i.length;++l)u+=i[l].index.count;t.setIndex(new y.BufferAttribute(new Uint32Array(u),1,!1)),f=!0}if(a||f){const u=t.index;let l=0,p=0;for(let m=0;m<i.length;++m){const A=i[m],g=A.index;if(o[m]!==!0)for(let h=0;h<g.count;++h)u.setX(l,g.getX(h)+p),l++;p+=A.attributes.position.count}}}for(const f in c){const u=c[f];if(!(f in t.attributes)){let m=0;for(const A in u)m+=u[A].count;t.setAttribute(f,At(c[f][0],m))}const l=t.attributes[f];let p=0;for(let m=0,A=u.length;m<A;m++){const g=u[m];o[m]!==!0&&rn(g,l,p),p+=g.count}}return t}function Fi(i,e){if(i===null||e===null)return i===e;if(i.length!==e.length)return!1;for(let t=0,n=i.length;t<n;t++)if(i[t]!==e[t])return!1;return!0}class _i{constructor(e){this.matrixWorld=new y.Matrix4,this.geometryHash=null,this.boneMatrices=null,this.primitiveCount=-1,this.mesh=e,this.update()}update(){const e=this.mesh,t=e.geometry,n=e.skeleton,s=(t.index?t.index.count:t.attributes.position.count)/3;if(this.matrixWorld.copy(e.matrixWorld),this.geometryHash=t.attributes.position.version,this.primitiveCount=s,n){n.boneTexture||n.computeBoneTexture(),n.update();const a=n.boneMatrices;!this.boneMatrices||this.boneMatrices.length!==a.length?this.boneMatrices=a.slice():this.boneMatrices.set(a)}else this.boneMatrices=null}didChange(){const e=this.mesh,t=e.geometry,n=(t.index?t.index.count:t.attributes.position.count)/3;return!(this.matrixWorld.equals(e.matrixWorld)&&this.geometryHash===t.attributes.position.version&&Fi(e.skeleton&&e.skeleton.boneMatrices||null,this.boneMatrices)&&this.primitiveCount===n)}}class Di{constructor(e){Array.isArray(e)||(e=[e]);const t=[];e.forEach(n=>{n.traverseVisible(s=>{s.isMesh&&t.push(s)})}),this.meshes=t,this.useGroups=!0,this.applyWorldTransforms=!0,this.attributes=["position","normal","color","tangent","uv","uv2"],this._intermediateGeometry=new Array(t.length).fill().map(()=>new y.BufferGeometry),this._diffMap=new WeakMap}getMaterials(){const e=[];return this.meshes.forEach(t=>{Array.isArray(t.material)?e.push(...t.material):e.push(t.material)}),e}generate(e=new y.BufferGeometry){let t=[];const{meshes:n,useGroups:s,_intermediateGeometry:a,_diffMap:o}=this;for(let r=0,c=n.length;r<c;r++){const d=n[r],f=a[r],u=o.get(d);!u||u.didChange(d)?(this._convertToStaticGeometry(d,f),t.push(!1),u?u.update():o.set(d,new _i(d))):t.push(!0)}Vi(a,{useGroups:s,skipAttributes:t},e);for(const r in e.attributes)e.attributes[r].needsUpdate=!0;return e}_convertToStaticGeometry(e,t=new y.BufferGeometry){const n=e.geometry,s=this.applyWorldTransforms,a=this.attributes.includes("normal"),o=this.attributes.includes("tangent"),r=n.attributes,c=t.attributes;t.index||(t.index=n.index),c.position||t.setAttribute("position",At(r.position)),a&&!c.normal&&r.normal&&t.setAttribute("normal",At(r.normal)),o&&!c.tangent&&r.tangent&&t.setAttribute("tangent",At(r.tangent)),vt(n.index,t.index),vt(r.position,c.position),a&&vt(r.normal,c.normal),o&&vt(r.tangent,c.tangent);const d=r.position,f=a?r.normal:null,u=o?r.tangent:null,l=n.morphAttributes.position,p=n.morphAttributes.normal,m=n.morphAttributes.tangent,A=n.morphTargetsRelative,g=e.morphTargetInfluences,h=new y.Matrix3;h.getNormalMatrix(e.matrixWorld);for(let x=0,b=r.position.count;x<b;x++)K.fromBufferAttribute(d,x),f&&J.fromBufferAttribute(f,x),u&&(en.fromBufferAttribute(u,x),Q.fromBufferAttribute(u,x)),g&&(l&&be(l,g,A,x,K),p&&be(p,g,A,x,J),m&&be(m,g,A,x,Q)),e.isSkinnedMesh&&(e.applyBoneTransform(x,K),f&&cn(e,x,J),u&&cn(e,x,Q)),s&&K.applyMatrix4(e.matrixWorld),c.position.setXYZ(x,K.x,K.y,K.z),f&&(s&&J.applyNormalMatrix(h),c.normal.setXYZ(x,J.x,J.y,J.z)),u&&(s&&Q.transformDirection(e.matrixWorld),c.tangent.setXYZW(x,Q.x,Q.y,Q.z,en.w));for(const x in this.attributes){const b=this.attributes[x];b==="position"||b==="tangent"||b==="normal"||!(b in r)||(c[b]||t.setAttribute(b,At(r[b])),vt(r[b],c[b]),rn(r[b],c[b]))}return t}}let zi={AVERAGE:we,CENTER:Gt,CONTAINED:jt,ExtendedTriangle:L,FloatVertexAttributeTexture:tn,INTERSECTED:ln,IntVertexAttributeTexture:vi,MeshBVH:Yt,MeshBVHUniformStruct:Ai,MeshBVHVisualizer:ge,NOT_INTERSECTED:an,OrientedBox:U,SAH:ve,StaticGeometryGenerator:Di,UIntVertexAttributeTexture:Qe,VertexAttributeTexture:Wt,acceleratedRaycast:xi,computeBoundsTree:mi,disposeBoundsTree:bi,estimateMemoryInBytes:di,getBVHExtremes:fi,getJSONStructure:hi,getTriangleHitPointInfo:zn,shaderDistanceFunction:Pi,shaderIntersectFunction:Mi,shaderStructs:Ii,validateBounds:pi};export default zi;
