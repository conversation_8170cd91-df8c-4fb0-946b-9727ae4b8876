import d from"./three.js";import jt from"./three-mesh-bvh.js";const Ft=1e-6,ke=Ft*.5,Xt=Math.pow(10,-Math.log10(Ft)),Ne=ke*Xt;function k(n){return~~(n*Xt+Ne)}function Oe(n){return`${k(n.x)},${k(n.y)}`}function at(n){return`${k(n.x)},${k(n.y)},${k(n.z)}`}function De(n){return`${k(n.x)},${k(n.y)},${k(n.z)},${k(n.w)}`}function Rn(n){return`${at(n.origin)}-${at(n.direction)}`}function Ut(n,t,e){e.direction.subVectors(t,n).normalize();const r=n.dot(e.direction);return e.origin.copy(n).addScaledVector(e.direction,-r),e}function Wt(){return typeof SharedArrayBuffer<"u"}function $e(n){if(n.buffer instanceof SharedArrayBuffer)return n;const t=n.constructor,e=n.buffer,r=new SharedArrayBuffer(e.byteLength),o=new Uint8Array(e);return new Uint8Array(r).set(o,0),new t(r)}function He(n,t=ArrayBuffer){return n>65535?new Uint32Array(new t(4*n)):new Uint16Array(new t(2*n))}function Le(n,t){if(!n.index){const e=n.attributes.position.count,r=t.useSharedArrayBuffer?SharedArrayBuffer:ArrayBuffer,o=He(e,r);n.setIndex(new d.BufferAttribute(o,1));for(let s=0;s<e;s++)o[s]=s}}function je(n){return n.index?n.index.count:n.attributes.position.count}function rt(n){return je(n)/3}const Fe=1e-8,Xe=new d.Vector3;function Ue(n){return~~(n/3)}function We(n){return n%3}function It(n,t){return n.start-t.start}function qt(n,t){return Xe.subVectors(t,n.origin).dot(n.direction)}function kn(n){n=[...n].sort(It);for(let t=0,e=n.length;t<e-1;t++){const r=n[t],o=n[t+1];if(o.start<r.end&&Math.abs(o.start-r.end)>1e-5)return!0}return!1}function Nn(n){let t=0;return n.forEach(({start:e,end:r})=>t+=r-e),t}function qe(n,t,e,r=Fe){n.sort(It),t.sort(It);for(let c=0;c<n.length;c++){const i=n[c];for(let l=0;l<t.length;l++){const a=t[l];if(!(a.start>i.end)){if(i.end<a.start||a.end<i.start)continue;if(i.start<=a.start&&i.end>=a.end)s(a.end,i.end)||n.splice(c+1,0,{start:a.end,end:i.end,index:i.index}),i.end=a.start,a.start=0,a.end=0;else if(i.start>=a.start&&i.end<=a.end)s(i.end,a.end)||t.splice(l+1,0,{start:i.end,end:a.end,index:a.index}),a.end=i.start,i.start=0,i.end=0;else if(i.start<=a.start&&i.end<=a.end){const f=i.end;i.end=a.start,a.start=f}else if(i.start>=a.start&&i.end>=a.end){const f=a.end;a.end=i.start,i.start=f}else throw new Error}if(e.has(i.index)||e.set(i.index,[]),e.has(a.index)||e.set(a.index,[]),e.get(i.index).push(a.index),e.get(a.index).push(i.index),u(a)&&(t.splice(l,1),l--),u(i)){n.splice(c,1),c--;break}}}o(n),o(t);function o(c){for(let i=0;i<c.length;i++)u(c[i])&&(c.splice(i,1),i--)}function s(c,i){return Math.abs(i-c)<r}function u(c){return Math.abs(c.end-c.start)<r}}const Kt=1e-5,Yt=1e-4;class Ke{constructor(){this._rays=[]}addRay(t){this._rays.push(t)}findClosestRay(t){const e=this._rays,r=t.clone();r.direction.multiplyScalar(-1);let o=1/0,s=null;for(let i=0,l=e.length;i<l;i++){const a=e[i];if(u(a,t)&&u(a,r))continue;const f=c(a,t),h=c(a,r),g=Math.min(f,h);g<o&&(o=g,s=a)}return s;function u(i,l){const a=i.origin.distanceTo(l.origin)>Kt;return i.direction.angleTo(l.direction)>Yt||a}function c(i,l){const a=i.origin.distanceTo(l.origin),f=i.direction.angleTo(l.direction);return a/Kt+f/Yt}}}const Et=new d.Vector3,Vt=new d.Vector3,lt=new d.Ray;function Ye(n,t,e){const r=n.attributes,o=n.index,s=r.position,u=new Map,c=new Map,i=Array.from(t),l=new Ke;for(let a=0,f=i.length;a<f;a++){const h=i[a],g=Ue(h),m=We(h);let b=3*g+m,A=3*g+(m+1)%3;o&&(b=o.getX(b),A=o.getX(A)),Et.fromBufferAttribute(s,b),Vt.fromBufferAttribute(s,A),Ut(Et,Vt,lt);let p,x=l.findClosestRay(lt);x===null&&(x=lt.clone(),l.addRay(x)),c.has(x)||c.set(x,{forward:[],reverse:[],ray:x}),p=c.get(x);let y=qt(x,Et),w=qt(x,Vt);y>w&&([y,w]=[w,y]),lt.direction.dot(x.direction)<0?p.reverse.push({start:y,end:w,index:h}):p.forward.push({start:y,end:w,index:h})}return c.forEach(({forward:a,reverse:f},h)=>{qe(a,f,u,e),a.length===0&&f.length===0&&c.delete(h)}),{disjointConnectivityMap:u,fragmentMap:c}}const Ze=new d.Vector2,Mt=new d.Vector3,Je=new d.Vector4,vt=["","",""];class Zt{constructor(t=null){this.data=null,this.disjointConnections=null,this.unmatchedDisjointEdges=null,this.unmatchedEdges=-1,this.matchedEdges=-1,this.useDrawRange=!0,this.useAllAttributes=!1,this.matchDisjointEdges=!1,this.degenerateEpsilon=1e-8,t&&this.updateFrom(t)}getSiblingTriangleIndex(t,e){const r=this.data[t*3+e];return r===-1?-1:~~(r/3)}getSiblingEdgeIndex(t,e){const r=this.data[t*3+e];return r===-1?-1:r%3}getDisjointSiblingTriangleIndices(t,e){const r=t*3+e,o=this.disjointConnections.get(r);return o?o.map(s=>~~(s/3)):[]}getDisjointSiblingEdgeIndices(t,e){const r=t*3+e,o=this.disjointConnections.get(r);return o?o.map(s=>s%3):[]}isFullyConnected(){return this.unmatchedEdges===0}updateFrom(t){const{useAllAttributes:e,useDrawRange:r,matchDisjointEdges:o,degenerateEpsilon:s}=this,u=e?y:x,c=new Map,{attributes:i}=t,l=e?Object.keys(i):null,a=t.index,f=i.position;let h=rt(t);const g=h;let m=0;r&&(m=t.drawRange.start,t.drawRange.count!==1/0&&(h=~~(t.drawRange.count/3)));let b=this.data;(!b||b.length<3*g)&&(b=new Int32Array(3*g)),b.fill(-1);let A=0,p=new Set;for(let w=m,T=h*3+m;w<T;w+=3){const M=w;for(let S=0;S<3;S++){let _=M+S;a&&(_=a.getX(_)),vt[S]=u(_)}for(let S=0;S<3;S++){const _=(S+1)%3,I=vt[S],X=vt[_],B=`${X}_${I}`;if(c.has(B)){const R=M+S,E=c.get(B);b[R]=E,b[E]=R,c.delete(B),A+=2,p.delete(E)}else{const R=`${I}_${X}`,E=M+S;c.set(R,E),p.add(E)}}}if(o){const{fragmentMap:w,disjointConnectivityMap:T}=Ye(t,p,s);p.clear(),w.forEach(({forward:M,reverse:S})=>{M.forEach(({index:_})=>p.add(_)),S.forEach(({index:_})=>p.add(_))}),this.unmatchedDisjointEdges=w,this.disjointConnections=T,A=h*3-p.size}this.matchedEdges=A,this.unmatchedEdges=p.size,this.data=b;function x(w){return Mt.fromBufferAttribute(f,w),at(Mt)}function y(w){let T="";for(let M=0,S=l.length;M<S;M++){const _=i[l[M]];let I;switch(_.itemSize){case 1:I=k(_.getX(w));break;case 2:I=Oe(Ze.fromBufferAttribute(_,w));break;case 3:I=at(Mt.fromBufferAttribute(_,w));break;case 4:I=De(Je.fromBufferAttribute(_,w));break}T!==""&&(T+="|"),T+=I}return T}}}class ut extends d.Mesh{constructor(...t){super(...t),this.isBrush=!0,this._previousMatrix=new d.Matrix4,this._previousMatrix.elements.fill(0)}markUpdated(){this._previousMatrix.copy(this.matrix)}isDirty(){const{matrix:t,_previousMatrix:e}=this,r=t.elements,o=e.elements;for(let s=0;s<16;s++)if(r[s]!==o[s])return!0;return!1}prepareGeometry(){const t=this.geometry,e=t.attributes,r=Wt();if(r)for(const o in e){const s=e[o];if(s.isInterleavedBufferAttribute)throw new Error("Brush: InterleavedBufferAttributes are not supported.");s.array=$e(s.array)}if(t.boundsTree||(Le(t,{useSharedArrayBuffer:r}),t.boundsTree=new jt.MeshBVH(t,{maxLeafTris:3,indirect:!0,useSharedArrayBuffer:r})),t.halfEdges||(t.halfEdges=new Zt(t)),!t.groupIndices){const o=rt(t),s=new Uint16Array(o),u=t.groups;for(let c=0,i=u.length;c<i;c++){const{start:l,count:a}=u[c];for(let f=l/3,h=(l+a)/3;f<h;f++)s[f]=c}t.groupIndices=s}}disposeCacheData(){const{geometry:t}=this;t.halfEdges=null,t.boundsTree=null,t.groupIndices=null}}const O=1e-14,ot=1e-10,Qe=1e-10,D=new d.Line3,V=new d.Line3,$=new d.Vector3,Bt=new d.Vector3,Jt=new d.Vector3,dt=new d.Plane,Ct=new jt.ExtendedTriangle,On=new d.Vector3,zt=new d.Vector3,Qt=new d.Vector3,te=new d.Vector3;function Q(n){zt.subVectors(n.b,n.a),Qt.subVectors(n.c,n.a),te.subVectors(n.c,n.b);const t=zt.angleTo(Qt),e=zt.angleTo(te)-Math.PI,r=Math.PI-t-e;return Math.abs(t)<O||Math.abs(e)<O||Math.abs(r)<O||n.a.distanceToSquared(n.b)<O||n.a.distanceToSquared(n.c)<O||n.b.distanceToSquared(n.c)<O}class tn{constructor(){this._pool=[],this._index=0}getTriangle(){return this._index>=this._pool.length&&this._pool.push(new d.Triangle),this._pool[this._index++]}clear(){this._index=0}reset(){this._pool.length=0,this._index=0}}class ee{constructor(){this.trianglePool=new tn,this.triangles=[],this.normal=new d.Vector3,this.coplanarTriangleUsed=!1}initialize(t){this.reset();const{triangles:e,trianglePool:r,normal:o}=this;if(Array.isArray(t))for(let s=0,u=t.length;s<u;s++){const c=t[s];if(s===0)c.getNormal(o);else if(Math.abs(1-c.getNormal($).dot(o))>O)throw new Error("THREE.Triangle Splitter: Cannot initialize with triangles that have different normals.");const i=r.getTriangle();i.copy(c),e.push(i)}else{t.getNormal(o);const s=r.getTriangle();s.copy(t),e.push(s)}}splitByTriangle(t){const{normal:e,triangles:r}=this;if(t.getNormal(Bt).normalize(),Math.abs(1-Math.abs(Bt.dot(e)))<Qe){this.coplanarTriangleUsed===!1&&(this.coplanarTriangleUsed=!0);for(let s=0,u=r.length;s<u;s++){const c=r[s];c.coplanarCount=0}const o=[t.a,t.b,t.c];for(let s=0;s<3;s++){const u=(s+1)%3,c=o[s],i=o[u];$.subVectors(i,c).normalize(),Jt.crossVectors(Bt,$),dt.setFromNormalAndCoplanarPoint(Jt,c),this.splitByPlane(dt,t)}}else t.getPlane(dt),this.splitByPlane(dt,t)}splitByPlane(t,e){const{triangles:r,trianglePool:o}=this;Ct.copy(e),Ct.needsUpdate=!0;for(let s=0,u=r.length;s<u;s++){const c=r[s];if(!Ct.intersectsTriangle(c,D,!0))continue;const{a:i,b:l,c:a}=c;let f=0,h=-1,g=!1,m=[],b=[];const A=[i,l,a];for(let p=0;p<3;p++){const x=(p+1)%3;D.start.copy(A[p]),D.end.copy(A[x]);const y=t.distanceToPoint(D.start),w=t.distanceToPoint(D.end);if(Math.abs(y)<ot&&Math.abs(w)<ot){g=!0;break}if(Math.abs(y)<ot)continue;y>0?m.push(p):b.push(p);let T=!!t.intersectLine(D,$);!T&&Math.abs(w)<ot&&($.copy(D.end),T=!0),T&&!($.distanceTo(D.start)<O)&&($.distanceTo(D.end)<O&&(h=p),f===0?V.start.copy($):V.end.copy($),f++)}if(!g&&f===2&&V.distance()>ot)if(h!==-1){h=(h+1)%3;let p=0;p===h&&(p=(p+1)%3);let x=p+1;x===h&&(x=(x+1)%3);const y=o.getTriangle();y.a.copy(A[x]),y.b.copy(V.end),y.c.copy(V.start),Q(y)||r.push(y),c.a.copy(A[p]),c.b.copy(V.start),c.c.copy(V.end),Q(c)&&(r.splice(s,1),s--,u--)}else{const p=m.length>=2?b[0]:m[0];if(p===0){let M=V.start;V.start=V.end,V.end=M}const x=(p+1)%3,y=(p+2)%3,w=o.getTriangle(),T=o.getTriangle();A[x].distanceToSquared(V.start)<A[y].distanceToSquared(V.end)?(w.a.copy(A[x]),w.b.copy(V.start),w.c.copy(V.end),T.a.copy(A[x]),T.b.copy(A[y]),T.c.copy(V.start)):(w.a.copy(A[y]),w.b.copy(V.start),w.c.copy(V.end),T.a.copy(A[x]),T.b.copy(A[y]),T.c.copy(V.end)),c.a.copy(A[p]),c.b.copy(V.end),c.c.copy(V.start),Q(w)||r.push(w),Q(T)||r.push(T),Q(c)&&(r.splice(s,1),s--,u--)}else f===3&&console.warn("TriangleClipper: Coplanar clip not handled")}}reset(){this.triangles.length=0,this.trianglePool.clear(),this.coplanarTriangleUsed=!1}}function en(n){return n=~~n,n+4-n%4}class ne{constructor(t,e=500){this.expansionFactor=1.5,this.type=t,this.length=0,this.array=null,this.setSize(e)}setType(t){if(this.length!==0)throw new Error("TypeBackedArray: Cannot change the type while there is used data in the buffer.");const e=this.array.buffer;this.array=new t(e),this.type=t}setSize(t){if(this.array&&t===this.array.length)return;const e=this.type,r=Wt()?SharedArrayBuffer:ArrayBuffer,o=new e(new r(en(t*e.BYTES_PER_ELEMENT)));this.array&&o.set(this.array,0),this.array=o}expand(){const{array:t,expansionFactor:e}=this;this.setSize(t.length*e)}push(...t){let{array:e,length:r}=this;r+t.length>e.length&&(this.expand(),e=this.array);for(let o=0,s=t.length;o<s;o++)e[r+o]=t[o];this.length+=t.length}clear(){this.length=0}}class nn{constructor(){this.groupAttributes=[{}],this.groupCount=0}getType(t){return this.groupAttributes[0][t].type}getItemSize(t){return this.groupAttributes[0][t].itemSize}getNormalized(t){return this.groupAttributes[0][t].normalized}getCount(t){if(this.groupCount<=t)return 0;const e=this.getGroupAttrArray("position",t);return e.length/e.itemSize}getTotalLength(t){const{groupCount:e,groupAttributes:r}=this;let o=0;for(let s=0;s<e;s++){const u=r[s];o+=u[t].length}return o}getGroupAttrSet(t=0){const{groupAttributes:e}=this;if(e[t])return this.groupCount=Math.max(this.groupCount,t+1),e[t];const r=e[0];for(this.groupCount=Math.max(this.groupCount,t+1);t>=e.length;){const o={};e.push(o);for(const s in r){const u=r[s],c=new ne(u.type);c.itemSize=u.itemSize,c.normalized=u.normalized,o[s]=c}}return e[t]}getGroupAttrArray(t,e=0){const{groupAttributes:r}=this;if(!r[0][t])throw new Error(`TypedAttributeData: Attribute with "${t}" has not been initialized`);return this.getGroupAttrSet(e)[t]}initializeArray(t,e,r,o){const{groupAttributes:s}=this,c=s[0][t];if(c){if(c.type!==e)for(let i=0,l=s.length;i<l;i++){const a=s[i][t];a.setType(e),a.itemSize=r,a.normalized=o}}else for(let i=0,l=s.length;i<l;i++){const a=new ne(e);a.itemSize=r,a.normalized=o,s[i][t]=a}}clear(){this.groupCount=0;const{groupAttributes:t}=this;t.forEach(e=>{for(const r in e)e[r].clear()})}delete(t){this.groupAttributes.forEach(e=>{delete e[t]})}reset(){this.groupAttributes=[],this.groupCount=0}}class re{constructor(){this.intersectionSet={},this.ids=[]}add(t,e){const{intersectionSet:r,ids:o}=this;r[t]||(r[t]=[],o.push(t)),r[t].push(e)}}const Pt=0,oe=1,se=2,ie=3,ce=4,Gt=5,Rt=6,z=new d.Ray,ae=new d.Matrix4,v=new d.Triangle,H=new d.Vector3,le=new d.Vector4,ue=new d.Vector4,de=new d.Vector4,kt=new d.Vector4,ft=new d.Vector4,ht=new d.Vector4,fe=new d.Line3,Nt=new d.Vector3,Ot=1e-8,rn=1e-15,W=-1,q=1,gt=-2,pt=2,st=0,K=1,Dt=2;let mt=null;function he(n){mt=n}function ge(n,t){n.getMidpoint(z.origin),n.getNormal(z.direction);const e=t.raycastFirst(z,d.DoubleSide);return!!(e&&z.direction.dot(e.face.normal)>0)?W:q}function on(n,t){function e(){return Math.random()-.5}n.getNormal(Nt),z.direction.copy(Nt),n.getMidpoint(z.origin);const r=3;let o=0,s=1/0;for(let u=0;u<r;u++){z.direction.x+=e()*Ot,z.direction.y+=e()*Ot,z.direction.z+=e()*Ot,z.direction.multiplyScalar(-1);const c=t.raycastFirst(z,d.DoubleSide);if(!!(c&&z.direction.dot(c.face.normal)>0)&&o++,c!==null&&(s=Math.min(s,c.distance)),s<=rn)return c.face.normal.dot(Nt)>0?pt:gt;if(o/r>.5||(u-o+1)/r>.5)break}return o/r>.5?W:q}function sn(n,t){const e=new re,r=new re;return ae.copy(n.matrixWorld).invert().multiply(t.matrixWorld),n.geometry.boundsTree.bvhcast(t.geometry.boundsTree,ae,{intersectsTriangles(o,s,u,c){if(o.intersectsTriangle(s,fe,!0)){let i=n.geometry.boundsTree.resolveTriangleIndex(u),l=t.geometry.boundsTree.resolveTriangleIndex(c);e.add(i,l),r.add(l,i),mt&&(mt.addEdge(fe),mt.addIntersectingTriangles(u,o,c,s))}return!1}}),{aIntersections:e,bIntersections:r}}function cn(n,t,e,r,o,s,u=!1){const c=e.attributes,i=e.index,l=n*3,a=i.getX(l+0),f=i.getX(l+1),h=i.getX(l+2);for(const g in s){const m=c[g],b=s[g];if(!(g in c))throw new Error(`CSG Operations: Attribute ${g} not available on geometry.`);const A=m.itemSize;g==="position"?(v.a.fromBufferAttribute(m,a).applyMatrix4(r),v.b.fromBufferAttribute(m,f).applyMatrix4(r),v.c.fromBufferAttribute(m,h).applyMatrix4(r),$t(v.a,v.b,v.c,t,3,b,u)):g==="normal"?(v.a.fromBufferAttribute(m,a).applyNormalMatrix(o),v.b.fromBufferAttribute(m,f).applyNormalMatrix(o),v.c.fromBufferAttribute(m,h).applyNormalMatrix(o),u&&(v.a.multiplyScalar(-1),v.b.multiplyScalar(-1),v.c.multiplyScalar(-1)),$t(v.a,v.b,v.c,t,3,b,u,!0)):(le.fromBufferAttribute(m,a),ue.fromBufferAttribute(m,f),de.fromBufferAttribute(m,h),$t(le,ue,de,t,A,b,u))}}function an(n,t,e,r,o,s,u,c=!1){Ht(n,r,o,s,u,c),Ht(c?e:t,r,o,s,u,c),Ht(c?t:e,r,o,s,u,c)}function pe(n,t,e=!1){switch(n){case Pt:if(t===q||t===pt&&!e)return K;break;case oe:if(e){if(t===W)return st}else if(t===q||t===gt)return K;break;case se:if(e){if(t===q||t===gt)return K}else if(t===W)return st;break;case ce:if(t===W)return st;if(t===q)return K;break;case ie:if(t===W||t===pt&&!e)return K;break;case Gt:if(!e&&(t===q||t===gt))return K;break;case Rt:if(!e&&(t===W||t===pt))return K;break;default:throw new Error(`Unrecognized CSG operation enum "${n}".`)}return Dt}function $t(n,t,e,r,o,s,u=!1,c=!1){const i=l=>{s.push(l.x),o>1&&s.push(l.y),o>2&&s.push(l.z),o>3&&s.push(l.w)};kt.set(0,0,0,0).addScaledVector(n,r.a.x).addScaledVector(t,r.a.y).addScaledVector(e,r.a.z),ft.set(0,0,0,0).addScaledVector(n,r.b.x).addScaledVector(t,r.b.y).addScaledVector(e,r.b.z),ht.set(0,0,0,0).addScaledVector(n,r.c.x).addScaledVector(t,r.c.y).addScaledVector(e,r.c.z),c&&(kt.normalize(),ft.normalize(),ht.normalize()),i(kt),u?(i(ht),i(ft)):(i(ft),i(ht))}function Ht(n,t,e,r,o,s=!1){for(const u in o){const c=t[u],i=o[u];if(!(u in t))throw new Error(`CSG Operations: Attribute ${u} no available on geometry.`);const l=c.itemSize;u==="position"?(H.fromBufferAttribute(c,n).applyMatrix4(e),i.push(H.x,H.y,H.z)):u==="normal"?(H.fromBufferAttribute(c,n).applyNormalMatrix(r),s&&H.multiplyScalar(-1),i.push(H.x,H.y,H.z)):(i.push(c.getX(n)),l>1&&i.push(c.getY(n)),l>2&&i.push(c.getZ(n)),l>3&&i.push(c.getW(n)))}}class ln{constructor(t){this.triangle=new d.Triangle().copy(t),this.intersects={}}addTriangle(t,e){this.intersects[t]=new d.Triangle().copy(e)}getIntersectArray(){const t=[],{intersects:e}=this;for(const r in e)t.push(e[r]);return t}}class me{constructor(){this.data={}}addTriangleIntersection(t,e,r,o){const{data:s}=this;s[t]||(s[t]=new ln(e)),s[t].addTriangle(r,o)}getTrianglesAsArray(t=null){const{data:e}=this,r=[];if(t!==null)t in e&&r.push(e[t].triangle);else for(const o in e)r.push(e[o].triangle);return r}getTriangleIndices(){return Object.keys(this.data).map(t=>parseInt(t))}getIntersectionIndices(t){const{data:e}=this;return e[t]?Object.keys(e[t].intersects).map(r=>parseInt(r)):[]}getIntersectionsAsArray(t=null,e=null){const{data:r}=this,o=new Set,s=[],u=c=>{if(r[c])if(e!==null)r[c].intersects[e]&&s.push(r[c].intersects[e]);else{const i=r[c].intersects;for(const l in i)o.has(l)||(o.add(l),s.push(i[l]))}};if(t!==null)u(t);else for(const c in r)u(c);return s}reset(){this.data={}}}class un{constructor(){this.enabled=!1,this.triangleIntersectsA=new me,this.triangleIntersectsB=new me,this.intersectionEdges=[]}addIntersectingTriangles(t,e,r,o){const{triangleIntersectsA:s,triangleIntersectsB:u}=this;s.addTriangleIntersection(t,e,r,o),u.addTriangleIntersection(r,o,t,e)}addEdge(t){this.intersectionEdges.push(t.clone())}reset(){this.triangleIntersectsA.reset(),this.triangleIntersectsB.reset(),this.intersectionEdges=[]}init(){this.enabled&&(this.reset(),he(this))}complete(){this.enabled&&he(null)}}const j=new d.Matrix4,yt=new d.Matrix3,Y=new d.Triangle,xt=new d.Triangle,bt=new d.Triangle,wt=new d.Triangle,P=[],Z=[];function dn(n){for(const t of n)return t}function fn(n,t,e,r,o,s={}){const{useGroups:u=!0}=s,{aIntersections:c,bIntersections:i}=sn(n,t),l=[];let a=null,f;return f=u?0:-1,ye(n,t,c,e,!1,r,o,f),xe(n,t,c,e,!1,o,f),e.findIndex(g=>g!==Rt&&g!==Gt)!==-1&&(f=u?n.geometry.groups.length||1:-1,ye(t,n,i,e,!0,r,o,f),xe(t,n,i,e,!0,o,f)),P.length=0,Z.length=0,{groups:l,materials:a}}function ye(n,t,e,r,o,s,u,c=0){const i=n.matrixWorld.determinant()<0;j.copy(t.matrixWorld).invert().multiply(n.matrixWorld),yt.getNormalMatrix(n.matrixWorld).multiplyScalar(i?-1:1);const l=n.geometry.groupIndices,a=n.geometry.index,f=n.geometry.attributes.position,h=t.geometry.boundsTree,g=t.geometry.index,m=t.geometry.attributes.position,b=e.ids,A=e.intersectionSet;for(let p=0,x=b.length;p<x;p++){const y=b[p],w=c===-1?0:l[y]+c,T=3*y,M=a.getX(T+0),S=a.getX(T+1),_=a.getX(T+2);Y.a.fromBufferAttribute(f,M).applyMatrix4(j),Y.b.fromBufferAttribute(f,S).applyMatrix4(j),Y.c.fromBufferAttribute(f,_).applyMatrix4(j),s.reset(),s.initialize(Y);const I=A[y];for(let B=0,R=I.length;B<R;B++){const E=3*I[B],nt=g.getX(E+0),C=g.getX(E+1),U=g.getX(E+2);xt.a.fromBufferAttribute(m,nt),xt.b.fromBufferAttribute(m,C),xt.c.fromBufferAttribute(m,U),s.splitByTriangle(xt)}const X=s.triangles;for(let B=0,R=X.length;B<R;B++){const E=X[B],nt=s.coplanarTriangleUsed?on(E,h):ge(E,h);P.length=0,Z.length=0;for(let C=0,U=r.length;C<U;C++){const J=pe(r[C],nt,o);J!==Dt&&(Z.push(J),P.push(u[C].getGroupAttrSet(w)))}if(P.length!==0){Y.getBarycoord(E.a,wt.a),Y.getBarycoord(E.b,wt.b),Y.getBarycoord(E.c,wt.c);for(let C=0,U=P.length;C<U;C++){const J=P[C],Re=Z[C]===st;cn(y,wt,n.geometry,n.matrixWorld,yt,J,i!==Re)}}}}return b.length}function xe(n,t,e,r,o,s,u=0){const c=n.matrixWorld.determinant()<0;j.copy(t.matrixWorld).invert().multiply(n.matrixWorld),yt.getNormalMatrix(n.matrixWorld).multiplyScalar(c?-1:1);const i=t.geometry.boundsTree,l=n.geometry.groupIndices,a=n.geometry.index,f=n.geometry.attributes,h=f.position,g=[],m=n.geometry.halfEdges,b=new Set,A=rt(n.geometry);for(let p=0,x=A;p<x;p++)p in e.intersectionSet||b.add(p);for(;b.size>0;){const p=dn(b);b.delete(p),g.push(p);const x=3*p,y=a.getX(x+0),w=a.getX(x+1),T=a.getX(x+2);bt.a.fromBufferAttribute(h,y).applyMatrix4(j),bt.b.fromBufferAttribute(h,w).applyMatrix4(j),bt.c.fromBufferAttribute(h,T).applyMatrix4(j);const M=ge(bt,i);Z.length=0,P.length=0;for(let S=0,_=r.length;S<_;S++){const I=pe(r[S],M,o);I!==Dt&&(Z.push(I),P.push(s[S]))}for(;g.length>0;){const S=g.pop();for(let _=0;_<3;_++){const I=m.getSiblingTriangleIndex(S,_);I!==-1&&b.has(I)&&(g.push(I),b.delete(I))}if(P.length!==0){const _=3*S,I=a.getX(_+0),X=a.getX(_+1),B=a.getX(_+2),R=u===-1?0:l[S]+u;for(let E=0,nt=P.length;E<nt;E++){const C=Z[E],U=P[E].getGroupAttrSet(R),J=C===st;an(I,X,B,f,n.matrixWorld,yt,U,J!==c)}}}}}function hn(n){for(let t=0;t<n.length-1;t++){const e=n[t],r=n[t+1];if(e.materialIndex===r.materialIndex){const o=e.start,s=r.start+r.count;r.start=o,r.count=s-o,n.splice(t,1),t--}}}function gn(n,t,e,r){const o=n.attributes;for(let s=0,u=r.length;s<u;s++){const c=r[s],i=o[c];e.initializeArray(c,i.array.constructor,i.itemSize,i.normalized)}for(const s in e.attributes)r.includes(s)||e.delete(s);for(const s in t.attributes)r.includes(s)||(t.deleteAttribute(s),t.dispose());e.clear()}function pn(n,t,e){let r=!1,o=-1;const s=n.attributes,u=t.groupAttributes[0];for(const i in u){const l=t.getTotalLength(i),a=t.getType(i),f=t.getItemSize(i),h=t.getNormalized(i);let g=s[i];(!g||g.array.length<l)&&(g=new d.BufferAttribute(new a(l),f,h),n.setAttribute(i,g),r=!0);let m=0;for(let b=0,A=Math.min(e.length,t.groupCount);b<A;b++){const p=e[b].index,{array:x,type:y,length:w}=t.groupAttributes[p][i],T=new y(x.buffer,0,w);g.array.set(T,m),m+=T.length}g.needsUpdate=!0,o=l/g.itemSize}if(n.index){const i=n.index.array;if(i.length<o)n.index=null,r=!0;else for(let l=0,a=i.length;l<a;l++)i[l]=l}let c=0;n.clearGroups();for(let i=0,l=Math.min(e.length,t.groupCount);i<l;i++){const{index:a,materialIndex:f}=e[i],h=t.getCount(a);h!==0&&(n.addGroup(c,h,f),c+=h)}n.setDrawRange(0,o),n.boundsTree=null,r&&n.dispose()}function be(n,t){let e=t;return Array.isArray(t)||(e=[],n.forEach(r=>{e[r.materialIndex]=t})),e}class mn{constructor(){this.triangleSplitter=new ee,this.attributeData=[],this.attributes=["position","uv","normal"],this.useGroups=!0,this.consolidateGroups=!0,this.debug=new un}getGroupRanges(t){return!this.useGroups||t.groups.length===0?[{start:0,count:1/0,materialIndex:0}]:t.groups.map(e=>({...e}))}evaluate(t,e,r,o=new ut){let s=!0;if(Array.isArray(r)||(r=[r]),Array.isArray(o)||(o=[o],s=!1),o.length!==r.length)throw new Error("Evaluator: operations and target array passed as different sizes.");t.prepareGeometry(),e.prepareGeometry();const{triangleSplitter:u,attributeData:c,attributes:i,useGroups:l,consolidateGroups:a,debug:f}=this;for(;c.length<o.length;)c.push(new nn);o.forEach((p,x)=>{gn(t.geometry,p.geometry,c[x],i)}),f.init(),fn(t,e,r,u,c,{useGroups:l}),f.complete();const h=this.getGroupRanges(t.geometry),g=be(h,t.material),m=this.getGroupRanges(e.geometry),b=be(m,e.material);m.forEach(p=>p.materialIndex+=g.length);let A=[...h,...m].map((p,x)=>({...p,index:x}));if(l){const p=[...g,...b];a&&(A=A.map(y=>{const w=p[y.materialIndex];return y.materialIndex=p.indexOf(w),y}).sort((y,w)=>y.materialIndex-w.materialIndex));const x=[];for(let y=0,w=p.length;y<w;y++){let T=!1;for(let M=0,S=A.length;M<S;M++){const _=A[M];_.materialIndex===y&&(T=!0,_.materialIndex=x.length)}T&&x.push(p[y])}o.forEach(y=>{y.material=x})}else A=[{start:0,count:1/0,index:0,materialIndex:0}],o.forEach(p=>{p.material=g[0]});return o.forEach((p,x)=>{const y=p.geometry;pn(y,c[x],A),a&&hn(y.groups)}),s?o:o[0]}evaluateHierarchy(t,e=new ut){t.updateMatrixWorld(!0);const r=(s,u)=>{const c=s.children;for(let i=0,l=c.length;i<l;i++){const a=c[i];a.isOperationGroup?r(a,u):u(a)}},o=s=>{const u=s.children;let c=!1;for(let l=0,a=u.length;l<a;l++){const f=u[l];c=o(f)||c}const i=s.isDirty();if(i&&s.markUpdated(),c&&!s.isOperationGroup){let l;return r(s,a=>{l?l=this.evaluate(l,a,a.operation):l=this.evaluate(s,a,a.operation)}),s._cachedGeometry=l.geometry,s._cachedMaterials=l.material,!0}else return c||i};return o(t),e.geometry=t._cachedGeometry,e.material=t._cachedMaterials,e}reset(){this.triangleSplitter.reset()}}class yn extends ut{constructor(...t){super(...t),this.isOperation=!0,this.operation=Pt,this._cachedGeometry=new d.BufferGeometry,this._cachedMaterials=null,this._previousOperation=null}markUpdated(){super.markUpdated(),this._previousOperation=this.operation}isDirty(){return this.operation!==this._previousOperation||super.isDirty()}insertBefore(t){const e=this.parent,r=e.children.indexOf(this);e.children.splice(r,0,t)}insertAfter(t){const e=this.parent,r=e.children.indexOf(this);e.children.splice(r+1,0,t)}}class xn extends d.Group{constructor(){super(),this.isOperationGroup=!0,this._previousMatrix=new d.Matrix4}markUpdated(){this._previousMatrix.copy(this.matrix)}isDirty(){const{matrix:t,_previousMatrix:e}=this,r=t.elements,o=e.elements;for(let s=0;s<16;s++)if(r[s]!==o[s])return!0;return!1}}function bn(n){if(!/varying\s+vec3\s+wPosition/.test(n.vertexShader))return n.vertexShader=`
			varying vec3 wPosition;
			${n.vertexShader}
		`.replace(/#include <displacementmap_vertex>/,t=>`${t}
				wPosition = (modelMatrix * vec4( transformed, 1.0 )).xyz;
				`),n.fragmentShader=`
		varying vec3 wPosition;
		${n.fragmentShader}
		`,n}function wn(n){return n.uniforms={...n.uniforms,checkerboardColor:{value:new d.Color(1118481)}},bn(n),n.defines={CSG_GRID:1},n.fragmentShader=n.fragmentShader.replace(/#include <common>/,t=>`
			${t}

			uniform vec3 checkerboardColor;
			float getCheckerboard( vec2 p, float scale ) {

				p /= scale;
				p += vec2( 0.5 );

				vec2 line = mod( p, 2.0 ) - vec2( 1.0 );
				line = abs( line );

				vec2 pWidth = fwidth( line );
				vec2 value = smoothstep( 0.5 - pWidth / 2.0, 0.5 + pWidth / 2.0, line );
				float result = value.x * value.y + ( 1.0 - value.x ) * ( 1.0 - value.y );

				return result;

			}

			float getGrid( vec2 p, float scale, float thickness ) {

				p /= 0.5 * scale;

				vec2 stride = mod( p, 2.0 ) - vec2( 1.0 );
				stride = abs( stride );

				vec2 pWidth = fwidth( p );
				vec2 line = smoothstep( 1.0 - pWidth / 2.0, 1.0 + pWidth / 2.0, stride + thickness * pWidth );

				return max( line.x, line.y );

			}

			vec3 getFaceColor( vec2 p, vec3 color ) {

				float checkLarge = getCheckerboard( p, 1.0 );
				float checkSmall = abs( getCheckerboard( p, 0.1 ) );
				float lines = getGrid( p, 10.0, 1.0 );

				vec3 checkColor = mix(
					vec3( 0.7 ) * color,
					vec3( 1.0 ) * color,
					checkSmall * 0.4 + checkLarge * 0.6
				);

				vec3 gridColor = vec3( 1.0 );

				return mix( checkColor, gridColor, lines );

			}

			float angleBetween( vec3 a, vec3 b ) {

				return acos( abs( dot( a, b ) ) );

			}

			vec3 planeProject( vec3 norm, vec3 other ) {

				float d = dot( norm, other );
				return normalize( other - norm * d );

			}

			vec3 getBlendFactors( vec3 norm ) {

				vec3 xVec = vec3( 1.0, 0.0, 0.0 );
				vec3 yVec = vec3( 0.0, 1.0, 0.0 );
				vec3 zVec = vec3( 0.0, 0.0, 1.0 );

				vec3 projX = planeProject( xVec, norm );
				vec3 projY = planeProject( yVec, norm );
				vec3 projZ = planeProject( zVec, norm );

				float xAngle = max(
					angleBetween( xVec, projY ),
					angleBetween( xVec, projZ )
				);

				float yAngle = max(
					angleBetween( yVec, projX ),
					angleBetween( yVec, projZ )
				);

				float zAngle = max(
					angleBetween( zVec, projX ),
					angleBetween( zVec, projY )
				);

				return vec3( xAngle, yAngle, zAngle ) / ( 0.5 * PI );

			}
		`).replace(/#include <normal_fragment_maps>/,t=>`${t}
				#if CSG_GRID
				{

					vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );

					float yCont = abs( dot( vec3( 0.0, 1.0, 0.0 ), worldNormal ) );
					float zCont = abs( dot( vec3( 0.0, 0.0, 1.0 ), worldNormal ) );
					float xCont = abs( dot( vec3( 1.0, 0.0, 0.0 ), worldNormal ) );

					vec3 factors = getBlendFactors( worldNormal );
					factors = smoothstep( vec3( 0.475 ), vec3( 0.525 ), vec3( 1.0 ) - factors );

					float weight = factors.x + factors.y + factors.z;
					factors /= weight;

					vec3 color =
						getFaceColor( wPosition.yz, diffuseColor.rgb ) * factors.x +
						getFaceColor( wPosition.xz, diffuseColor.rgb ) * factors.y +
						getFaceColor( wPosition.xy, diffuseColor.rgb ) * factors.z;

					diffuseColor.rgb = color;

				}
				#endif
				`),n}class An extends d.MeshPhongMaterial{get enableGrid(){return!!this._enableGrid}set enableGrid(t){this._enableGrid!==t&&(this._enableGrid=t,this.needsUpdate=!0)}constructor(...t){super(...t),this.enableGrid=!0}onBeforeCompile(t){wn(t),t.defines.CSG_GRID=Number(this.enableGrid)}customProgramCacheKey(){return this.enableGrid.toString()}}function we(...n){function t(e){return`new THREE.THREE.Vector3( ${e.x}, ${e.y}, ${e.z} )`}return n.map(e=>`
new THREE.THREE.Triangle(
	${t(e.a)},
	${t(e.b)},
	${t(e.c)},
)
		`.trim())}function Tn(...n){console.log(we(...n).join(`,
`))}function _n(n){const t=n.attributes.position,e=new Float32Array(t.count*3),r=new d.Color;for(let o=0,s=e.length;o<s;o+=9)r.setHSL(Math.random(),d.MathUtils.lerp(.5,1,Math.random()),d.MathUtils.lerp(.5,.75,Math.random())),e[o+0]=r.r,e[o+1]=r.g,e[o+2]=r.b,e[o+3]=r.r,e[o+4]=r.g,e[o+5]=r.b,e[o+6]=r.r,e[o+7]=r.g,e[o+8]=r.b;n.setAttribute("color",new d.BufferAttribute(e,3))}class Sn extends d.Group{get color(){return this._mesh.material.color}get side(){return this._mesh.material.side}set side(t){this._mesh.material.side=t}constructor(t=[]){super();const e=new d.BufferGeometry,r=new d.BufferGeometry;this._mesh=new d.Mesh(e,new d.MeshPhongMaterial({flatShading:!0,transparent:!0,opacity:.25,depthWrite:!1})),this._lines=new d.LineSegments(r,new d.LineBasicMaterial),this._mesh.material.color=this._lines.material.color,this._lines.frustumCulled=!1,this._mesh.frustumCulled=!1,this.add(this._lines,this._mesh),this.setTriangles(t)}setTriangles(t){const e=new Float32Array(9*t.length),r=new Float32Array(6*3*t.length);for(let o=0,s=t.length;o<s;o++){const u=9*o,c=18*o,i=t[o];i.a.toArray(e,u+0),i.b.toArray(e,u+3),i.c.toArray(e,u+6),i.a.toArray(r,c+0),i.b.toArray(r,c+3),i.b.toArray(r,c+6),i.c.toArray(r,c+9),i.c.toArray(r,c+12),i.a.toArray(r,c+15)}this._mesh.geometry.dispose(),this._mesh.geometry.setAttribute("position",new d.BufferAttribute(e,3)),this._lines.geometry.dispose(),this._lines.geometry.setAttribute("position",new d.BufferAttribute(r,3))}}class Ae extends d.LineSegments{get color(){return this.material.color}constructor(t=[]){super(),this.frustumCulled=!1,this.setEdges(t)}setEdges(t){const{geometry:e}=this,r=t.flatMap(o=>[o.start,o.end]);e.dispose(),e.setFromPoints(r)}}const Te=new d.Matrix4;class In extends d.InstancedMesh{get color(){return this.material.color}constructor(t=1e3,e=[]){super(new d.SphereGeometry(.025),new d.MeshBasicMaterial,t),this.frustumCulled=!1,this.setPoints(e)}setPoints(t){for(let e=0,r=t.length;e<r;e++){const o=t[e];Te.makeTranslation(o.x,o.y,o.z),this.setMatrixAt(e,Te)}this.count=t.length}}const F=["a","b","c"],L=new d.Triangle,it=new d.Triangle,tt=new d.Vector3,ct=new d.Vector3,_e=new d.Vector3,Se=new d.Vector3,At=new d.Vector3,Tt=new d.Vector3,Ie=new d.Vector3,Ee=new d.Vector3,et=new d.Vector3,Ve=new d.Vector3,Me=new d.Vector3,ve=new d.Vector3,Be=new d.Plane,Ce=new d.Plane,_t=new d.Vector3,G=new d.Ray,Lt=new d.Line3;function St(n,t,e){const r=3*t;let o=r+0,s=r+1,u=r+2;const c=n.index,i=n.attributes.position;return c&&(o=c.getX(o),s=c.getX(s),u=c.getX(u)),e.a.fromBufferAttribute(i,o),e.b.fromBufferAttribute(i,s),e.c.fromBufferAttribute(i,u),e}function En(n,t,e,r,o){const s=(t+1)%3,u=n[F[t]],c=n[F[s]],i=(r+1)%3,l=e[F[r]],a=e[F[i]];Ut(u,c,G);let f=et.subVectors(u,G.origin).dot(G.direction),h=et.subVectors(c,G.origin).dot(G.direction);f>h&&([f,h]=[h,f]);let g=et.subVectors(l,G.origin).dot(G.direction),m=et.subVectors(a,G.origin).dot(G.direction);g>m&&([g,m]=[m,g]);const b=Math.max(f,g),A=Math.min(h,m);G.at(b,o.start),G.at(A,o.end)}class Vn extends Ae{constructor(t=null,e=null){super(),this.straightEdges=!1,this.displayDisconnectedEdges=!1,t&&e&&this.setHalfEdges(t,e)}setHalfEdges(t,e){const{straightEdges:r,displayDisconnectedEdges:o}=this,s=[],u=t.drawRange.start;let c=rt(t);if(t.drawRange.count!==1/0&&(c=~~(t.drawRange.count/3)),o)if(e.unmatchedDisjointEdges)e.unmatchedDisjointEdges.forEach(({forward:l,reverse:a,ray:f})=>{[...l,...a].forEach(({start:h,end:g})=>{const m=new d.Line3;f.at(h,m.start),f.at(g,m.end),s.push(m)})});else for(let l=u;l<c;l++){St(t,l,L);for(let a=0;a<3;a++)if(e.getSiblingTriangleIndex(l,a)===-1){const h=(a+1)%3,g=L[F[a]],m=L[F[h]],b=new d.Line3;b.start.copy(g),b.end.copy(m),s.push(b)}}else for(let l=u;l<c;l++){St(t,l,L);for(let a=0;a<3;a++){const f=e.getSiblingTriangleIndex(l,a);if(f===-1)continue;St(t,f,it);const h=(a+1)%3,g=L[F[a]],m=L[F[h]];_t.lerpVectors(g,m,.5),i(L,it,_t)}if(e.disjointConnections)for(let a=0;a<3;a++){const f=e.getDisjointSiblingTriangleIndices(l,a),h=e.getDisjointSiblingEdgeIndices(l,a);for(let g=0;g<f.length;g++){const m=f[g],b=h[g];St(t,m,it),En(L,a,it,b,Lt),_t.lerpVectors(Lt.start,Lt.end,.5),i(L,it,_t)}}}super.setEdges(s);function i(l,a,f){l.getMidpoint(tt),a.getMidpoint(ct),l.getPlane(Be),a.getPlane(Ce);const h=new d.Line3;h.start.copy(tt),r?(Be.projectPoint(ct,_e),Ce.projectPoint(tt,Se),At.subVectors(_e,tt),Tt.subVectors(Se,ct),Ie.subVectors(f,tt),Ee.subVectors(f,ct),At.dot(Ie)<0&&At.multiplyScalar(-1),Tt.dot(Ee)<0&&Tt.multiplyScalar(-1),et.addVectors(tt,At),Ve.addVectors(ct,Tt),l.closestPointToPoint(et,Me),a.closestPointToPoint(Ve,ve),h.end.lerpVectors(Me,ve,.5)):h.end.copy(f),s.push(h)}}}const N=new d.Triangle,ze=new d.Vector3,Pe=new d.Vector3;function Mn(n){let t,e;n.isBufferGeometry?(t=n,e=null):(t=n.geometry,e=Math.abs(n.matrixWorld.determinant()-1)<1e-15?null:n.matrixWorld);const r=t.index,o=t.attributes.position,s=t.drawRange,u=Math.min(rt(t),s.count/3);N.setFromAttributeAndIndices(o,0,1,2),Ge(N,e),N.getNormal(ze),N.getMidpoint(Pe).add(ze);let c=0;const i=s.start/3;for(let l=i,a=i+u;l<a;l++){let f=3*l+0,h=3*l+1,g=3*l+2;r&&(f=r.getX(f),h=r.getX(h),g=r.getX(g)),N.setFromAttributeAndIndices(o,f,h,g),Ge(N,e),Bn(N,Pe),c+=vn(N.a,N.b,N.c)}return Math.abs(c)}function vn(n,t,e){const r=e.x*t.y*n.z,o=t.x*e.y*n.z,s=e.x*n.y*t.z,u=n.x*e.y*t.z,c=t.x*n.y*e.z,i=n.x*t.y*e.z;return 1/6*(-r+o+s-u-c+i)}function Bn(n,t){n.a.sub(t),n.b.sub(t),n.c.sub(t)}function Ge(n,t=null){t!==null&&(n.a.applyMatrix4(t),n.b.applyMatrix4(t),n.c.applyMatrix4(t))}let Cn={ADDITION:Pt,Brush:ut,DIFFERENCE:ce,EdgesHelper:Ae,Evaluator:mn,GridMaterial:An,HOLLOW_INTERSECTION:Rt,HOLLOW_SUBTRACTION:Gt,HalfEdgeHelper:Vn,HalfEdgeMap:Zt,INTERSECTION:ie,Operation:yn,OperationGroup:xn,PointsHelper:In,REVERSE_SUBTRACTION:se,SUBTRACTION:oe,TriangleSetHelper:Sn,TriangleSplitter:ee,computeMeshVolume:Mn,generateRandomTriangleColors:_n,getTriangleDefinitions:we,isTriDegenerate:Q,logTriangleDefinitions:Tn};export default Cn;
