import u from"./three.js";function Y(e){this.manager=e!==void 0?e:u.<PERSON><PERSON><PERSON><PERSON>oadingManager,this.dracoLoader=null,this.ddsLoader=null}Y.prototype={constructor:Y,crossOrigin:"anonymous",load:function(e,n,t,s){var r=this,a;this.resourcePath!==void 0?a=this.resourcePath:this.path!==void 0?a=this.path:a=u.LoaderUtils.extractUrlBase(e),r.manager.itemStart(e);var i=function(l){s?s(l):console.error(l),r.manager.itemError(e),r.manager.itemEnd(e)},o=new u.FileLoader(r.manager);o.setPath(this.path),o.setResponseType("arraybuffer"),r.crossOrigin==="use-credentials"&&o.setWithCredentials(!0),o.load(e,function(l){try{r.parse(l,a,function(p){n(p),r.manager.itemEnd(e)},i)}catch(p){i(p)}},t,i)},setCrossOrigin:function(e){return this.crossOrigin=e,this},setPath:function(e){return this.path=e,this},setResourcePath:function(e){return this.resourcePath=e,this},setDRACOLoader:function(e){return this.dracoLoader=e,this},setDDSLoader:function(e){return this.ddsLoader=e,this},parse:function(e,n,t,s){var r,a={};if(typeof e=="string")r=e;else{var i=u.LoaderUtils.decodeText(new Uint8Array(e,0,4));if(i===ne){try{a[T.KHR_BINARY_GLTF]=new Me(e)}catch(d){s&&s(d);return}r=a[T.KHR_BINARY_GLTF].content}else r=u.LoaderUtils.decodeText(new Uint8Array(e))}var o=JSON.parse(r);if(o.asset===void 0||o.asset.version[0]<2){s&&s(new Error("THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported. Use LegacyGLTFLoader instead."));return}if(o.extensionsUsed)for(var l=0;l<o.extensionsUsed.length;++l){var p=o.extensionsUsed[l],c=o.extensionsRequired||[];switch(p){case T.KHR_LIGHTS_PUNCTUAL:a[p]=new ee(o);break;case T.KHR_MATERIALS_UNLIT:a[p]=new W;break;case T.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS:a[p]=new Ee;break;case T.KHR_DRACO_MESH_COMPRESSION:a[p]=new se(o,this.dracoLoader);break;case T.MSFT_TEXTURE_DDS:a[T.MSFT_TEXTURE_DDS]=new Se(this.ddsLoader);break;case T.KHR_TEXTURE_TRANSFORM:a[T.KHR_TEXTURE_TRANSFORM]=new te;break;default:c.indexOf(p)>=0&&console.warn('THREE.GLTFLoader: Unknown extension "'+p+'".')}}var f=new R(o,a,{path:n||this.resourcePath||"",crossOrigin:this.crossOrigin,manager:this.manager});f.parse(t,s)}};function Te(){var e={};return{get:function(n){return e[n]},add:function(n,t){e[n]=t},remove:function(n){delete e[n]},removeAll:function(){e={}}}}var T={KHR_BINARY_GLTF:"KHR_binary_glTF",KHR_DRACO_MESH_COMPRESSION:"KHR_draco_mesh_compression",KHR_LIGHTS_PUNCTUAL:"KHR_lights_punctual",KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS:"KHR_materials_pbrSpecularGlossiness",KHR_MATERIALS_UNLIT:"KHR_materials_unlit",KHR_TEXTURE_TRANSFORM:"KHR_texture_transform",MSFT_TEXTURE_DDS:"MSFT_texture_dds"};function Se(e){if(!e)throw new Error("THREE.GLTFLoader: Attempting to load .dds texture without importing THREE.DDSLoader");this.name=T.MSFT_TEXTURE_DDS,this.ddsLoader=e}function ee(e){this.name=T.KHR_LIGHTS_PUNCTUAL;var n=e.extensions&&e.extensions[T.KHR_LIGHTS_PUNCTUAL]||{};this.lightDefs=n.lights||[]}ee.prototype.loadLight=function(e){var n=this.lightDefs[e],t,s=new u.Color(16777215);n.color!==void 0&&s.fromArray(n.color);var r=n.range!==void 0?n.range:0;switch(n.type){case"directional":t=new u.DirectionalLight(s),t.target.position.set(0,0,-1),t.add(t.target);break;case"point":t=new u.PointLight(s),t.distance=r;break;case"spot":t=new u.SpotLight(s),t.distance=r,n.spot=n.spot||{},n.spot.innerConeAngle=n.spot.innerConeAngle!==void 0?n.spot.innerConeAngle:0,n.spot.outerConeAngle=n.spot.outerConeAngle!==void 0?n.spot.outerConeAngle:Math.PI/4,t.angle=n.spot.outerConeAngle,t.penumbra=1-n.spot.innerConeAngle/n.spot.outerConeAngle,t.target.position.set(0,0,-1),t.add(t.target);break;default:throw new Error('THREE.GLTFLoader: Unexpected light type, "'+n.type+'".')}return t.position.set(0,0,0),t.decay=2,n.intensity!==void 0&&(t.intensity=n.intensity),t.name=n.name||"light_"+e,Promise.resolve(t)};function W(){this.name=T.KHR_MATERIALS_UNLIT}W.prototype.getMaterialType=function(){return u.MeshBasicMaterial},W.prototype.extendParams=function(e,n,t){var s=[];e.color=new u.Color(1,1,1),e.opacity=1;var r=n.pbrMetallicRoughness;if(r){if(Array.isArray(r.baseColorFactor)){var a=r.baseColorFactor;e.color.fromArray(a),e.opacity=a[3]}r.baseColorTexture!==void 0&&s.push(t.assignTexture(e,"map",r.baseColorTexture))}return Promise.all(s)};var ne="glTF",B=12,re={JSON:1313821514,BIN:5130562};function Me(e){this.name=T.KHR_BINARY_GLTF,this.content=null,this.body=null;var n=new DataView(e,0,B);if(this.header={magic:u.LoaderUtils.decodeText(new Uint8Array(e.slice(0,4))),version:n.getUint32(4,!0),length:n.getUint32(8,!0)},this.header.magic!==ne)throw new Error("THREE.GLTFLoader: Unsupported glTF-Binary header.");if(this.header.version<2)throw new Error("THREE.GLTFLoader: Legacy binary file detected. Use LegacyGLTFLoader instead.");for(var t=new DataView(e,B),s=0;s<t.byteLength;){var r=t.getUint32(s,!0);s+=4;var a=t.getUint32(s,!0);if(s+=4,a===re.JSON){var i=new Uint8Array(e,B+s,r);this.content=u.LoaderUtils.decodeText(i)}else if(a===re.BIN){var o=B+s;this.body=e.slice(o,o+r)}s+=r}if(this.content===null)throw new Error("THREE.GLTFLoader: JSON content not found.")}function se(e,n){if(!n)throw new Error("THREE.GLTFLoader: No DRACOLoader instance provided.");this.name=T.KHR_DRACO_MESH_COMPRESSION,this.json=e,this.dracoLoader=n}se.prototype.decodePrimitive=function(e,n){var t=this.json,s=this.dracoLoader,r=e.extensions[this.name].bufferView,a=e.extensions[this.name].attributes,i={},o={},l={};for(var p in a){var c=Z[p]||p.toLowerCase();i[c]=a[p]}for(p in e.attributes){var c=Z[p]||p.toLowerCase();if(a[p]!==void 0){var f=t.accessors[e.attributes[p]],d=z[f.componentType];l[c]=d,o[c]=f.normalized===!0}}return n.getDependency("bufferView",r).then(function(h){return new Promise(function(v){s.decodeDracoFile(h,function(g){for(var S in g.attributes){var E=g.attributes[S],L=o[S];L!==void 0&&(E.normalized=L)}v(g)},i,l)})})};function te(){this.name=T.KHR_TEXTURE_TRANSFORM}te.prototype.extendTexture=function(e,n){return e=e.clone(),n.offset!==void 0&&e.offset.fromArray(n.offset),n.rotation!==void 0&&(e.rotation=n.rotation),n.scale!==void 0&&e.repeat.fromArray(n.scale),n.texCoord!==void 0&&console.warn('THREE.GLTFLoader: Custom UV sets in "'+this.name+'" extension not yet supported.'),e.needsUpdate=!0,e};function Ee(){return{name:T.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS,specularGlossinessParams:["color","map","lightMap","lightMapIntensity","aoMap","aoMapIntensity","emissive","emissiveIntensity","emissiveMap","bumpMap","bumpScale","normalMap","displacementMap","displacementScale","displacementBias","specularMap","specular","glossinessMap","glossiness","alphaMap","envMap","envMapIntensity","refractionRatio"],getMaterialType:function(){return u.ShaderMaterial},extendParams:function(e,n,t){var s=n.extensions[this.name],r=u.ShaderLib.standard,a=u.UniformsUtils.clone(r.uniforms),i=["#ifdef USE_SPECULARMAP","	uniform sampler2D specularMap;","#endif"].join(`
`),o=["#ifdef USE_GLOSSINESSMAP","	uniform sampler2D glossinessMap;","#endif"].join(`
`),l=["vec3 specularFactor = specular;","#ifdef USE_SPECULARMAP","	vec4 texelSpecular = texture2D( specularMap, vUv );","	texelSpecular = sRGBToLinear( texelSpecular );","	// reads channel RGB, compatible with a glTF Specular-Glossiness (RGBA) texture","	specularFactor *= texelSpecular.rgb;","#endif"].join(`
`),p=["float glossinessFactor = glossiness;","#ifdef USE_GLOSSINESSMAP","	vec4 texelGlossiness = texture2D( glossinessMap, vUv );","	// reads channel A, compatible with a glTF Specular-Glossiness (RGBA) texture","	glossinessFactor *= texelGlossiness.a;","#endif"].join(`
`),c=["PhysicalMaterial material;","material.diffuseColor = diffuseColor.rgb;","material.specularRoughness = clamp( 1.0 - glossinessFactor, 0.04, 1.0 );","material.specularColor = specularFactor.rgb;"].join(`
`),f=r.fragmentShader.replace("uniform float roughness;","uniform vec3 specular;").replace("uniform float metalness;","uniform float glossiness;").replace("#include <roughnessmap_pars_fragment>",i).replace("#include <metalnessmap_pars_fragment>",o).replace("#include <roughnessmap_fragment>",l).replace("#include <metalnessmap_fragment>",p).replace("#include <lights_physical_fragment>",c);delete a.roughness,delete a.metalness,delete a.roughnessMap,delete a.metalnessMap,a.specular={value:new u.Color().setHex(1118481)},a.glossiness={value:.5},a.specularMap={value:null},a.glossinessMap={value:null},e.vertexShader=r.vertexShader,e.fragmentShader=f,e.uniforms=a,e.defines={STANDARD:""},e.color=new u.Color(1,1,1),e.opacity=1;var d=[];if(Array.isArray(s.diffuseFactor)){var h=s.diffuseFactor;e.color.fromArray(h),e.opacity=h[3]}if(s.diffuseTexture!==void 0&&d.push(t.assignTexture(e,"map",s.diffuseTexture)),e.emissive=new u.Color(0,0,0),e.glossiness=s.glossinessFactor!==void 0?s.glossinessFactor:1,e.specular=new u.Color(1,1,1),Array.isArray(s.specularFactor)&&e.specular.fromArray(s.specularFactor),s.specularGlossinessTexture!==void 0){var v=s.specularGlossinessTexture;d.push(t.assignTexture(e,"glossinessMap",v)),d.push(t.assignTexture(e,"specularMap",v))}return Promise.all(d)},createMaterial:function(e){var n=new u.ShaderMaterial({defines:e.defines,vertexShader:e.vertexShader,fragmentShader:e.fragmentShader,uniforms:e.uniforms,fog:!0,lights:!0,opacity:e.opacity,transparent:e.transparent});return n.isGLTFSpecularGlossinessMaterial=!0,n.color=e.color,n.map=e.map===void 0?null:e.map,n.lightMap=null,n.lightMapIntensity=1,n.aoMap=e.aoMap===void 0?null:e.aoMap,n.aoMapIntensity=1,n.emissive=e.emissive,n.emissiveIntensity=1,n.emissiveMap=e.emissiveMap===void 0?null:e.emissiveMap,n.bumpMap=e.bumpMap===void 0?null:e.bumpMap,n.bumpScale=1,n.normalMap=e.normalMap===void 0?null:e.normalMap,e.normalScale&&(n.normalScale=e.normalScale),n.displacementMap=null,n.displacementScale=1,n.displacementBias=0,n.specularMap=e.specularMap===void 0?null:e.specularMap,n.specular=e.specular,n.glossinessMap=e.glossinessMap===void 0?null:e.glossinessMap,n.glossiness=e.glossiness,n.alphaMap=null,n.envMap=e.envMap===void 0?null:e.envMap,n.envMapIntensity=1,n.refractionRatio=.98,n.extensions.derivatives=!0,n},cloneMaterial:function(e){var n=e.clone();n.isGLTFSpecularGlossinessMaterial=!0;for(var t=this.specularGlossinessParams,s=0,r=t.length;s<r;s++){var a=e[t[s]];n[t[s]]=a&&a.isColor?a.clone():a}return n},refreshUniforms:function(e,n,t,s,r){if(r.isGLTFSpecularGlossinessMaterial===!0){var a=r.uniforms,i=r.defines;a.opacity.value=r.opacity,a.diffuse.value.copy(r.color),a.emissive.value.copy(r.emissive).multiplyScalar(r.emissiveIntensity),a.map.value=r.map,a.specularMap.value=r.specularMap,a.alphaMap.value=r.alphaMap,a.lightMap.value=r.lightMap,a.lightMapIntensity.value=r.lightMapIntensity,a.aoMap.value=r.aoMap,a.aoMapIntensity.value=r.aoMapIntensity;var o;r.map?o=r.map:r.specularMap?o=r.specularMap:r.displacementMap?o=r.displacementMap:r.normalMap?o=r.normalMap:r.bumpMap?o=r.bumpMap:r.glossinessMap?o=r.glossinessMap:r.alphaMap?o=r.alphaMap:r.emissiveMap&&(o=r.emissiveMap),o!==void 0&&(o.isWebGLRenderTarget&&(o=o.texture),o.matrixAutoUpdate===!0&&o.updateMatrix(),a.uvTransform.value.copy(o.matrix)),r.envMap&&(a.envMap.value=r.envMap,a.envMapIntensity.value=r.envMapIntensity,a.flipEnvMap.value=r.envMap.isCubeTexture?-1:1,a.reflectivity.value=r.reflectivity,a.refractionRatio.value=r.refractionRatio,a.maxMipLevel.value=e.properties.get(r.envMap).__maxMipLevel),a.specular.value.copy(r.specular),a.glossiness.value=r.glossiness,a.glossinessMap.value=r.glossinessMap,a.emissiveMap.value=r.emissiveMap,a.bumpMap.value=r.bumpMap,a.normalMap.value=r.normalMap,a.displacementMap.value=r.displacementMap,a.displacementScale.value=r.displacementScale,a.displacementBias.value=r.displacementBias,a.glossinessMap.value!==null&&i.USE_GLOSSINESSMAP===void 0&&(i.USE_GLOSSINESSMAP="",i.USE_ROUGHNESSMAP=""),a.glossinessMap.value===null&&i.USE_GLOSSINESSMAP!==void 0&&(delete i.USE_GLOSSINESSMAP,delete i.USE_ROUGHNESSMAP)}}}}function y(e,n,t,s){u.Interpolant.call(this,e,n,t,s)}y.prototype=Object.create(u.Interpolant.prototype),y.prototype.constructor=y,y.prototype.copySampleValue_=function(e){for(var n=this.resultBuffer,t=this.sampleValues,s=this.valueSize,r=e*s*3+s,a=0;a!==s;a++)n[a]=t[r+a];return n},y.prototype.beforeStart_=y.prototype.copySampleValue_,y.prototype.afterEnd_=y.prototype.copySampleValue_,y.prototype.interpolate_=function(e,n,t,s){for(var r=this.resultBuffer,a=this.sampleValues,i=this.valueSize,o=i*2,l=i*3,p=s-n,c=(t-n)/p,f=c*c,d=f*c,h=e*l,v=h-l,g=-2*d+3*f,S=d-f,E=1-g,L=S-f+c,_=0;_!==i;_++){var M=a[v+_+i],w=a[v+_+o]*p,A=a[h+_+i],m=a[h+_]*p;r[_]=E*M+L*w+g*A+S*m}return r};var b={FLOAT:5126,FLOAT_MAT3:35675,FLOAT_MAT4:35676,FLOAT_VEC2:35664,FLOAT_VEC3:35665,FLOAT_VEC4:35666,LINEAR:9729,REPEAT:10497,SAMPLER_2D:35678,POINTS:0,LINES:1,LINE_LOOP:2,LINE_STRIP:3,TRIANGLES:4,TRIANGLE_STRIP:5,TRIANGLE_FAN:6,UNSIGNED_BYTE:5121,UNSIGNED_SHORT:5123},z={5120:Int8Array,5121:Uint8Array,5122:Int16Array,5123:Uint16Array,5125:Uint32Array,5126:Float32Array},ae={9728:u.NearestFilter,9729:u.LinearFilter,9984:u.NearestMipMapNearestFilter,9985:u.LinearMipMapNearestFilter,9986:u.NearestMipMapLinearFilter,9987:u.LinearMipMapLinearFilter},ie={33071:u.ClampToEdgeWrapping,33648:u.MirroredRepeatWrapping,10497:u.RepeatWrapping},oe={SCALAR:1,VEC2:2,VEC3:3,VEC4:4,MAT2:4,MAT3:9,MAT4:16},Z={POSITION:"position",NORMAL:"normal",TANGENT:"tangent",TEXCOORD_0:"uv",TEXCOORD_1:"uv2",COLOR_0:"color",WEIGHTS_0:"skinWeight",JOINTS_0:"skinIndex"},U={scale:"scale",translation:"position",rotation:"quaternion",weights:"morphTargetInfluences"},Le={CUBICSPLINE:void 0,LINEAR:u.InterpolateLinear,STEP:u.InterpolateDiscrete},J={OPAQUE:"OPAQUE",MASK:"MASK",BLEND:"BLEND"},ue={"image/png":u.RGBAFormat,"image/jpeg":u.RGBFormat};function le(e,n){return typeof e!="string"||e===""?"":/^(https?:)?\/\//i.test(e)||/^data:.*,.*$/i.test(e)||/^blob:.*$/i.test(e)?e:n+e}var Q;function Re(){return Q=Q||new u.MeshStandardMaterial({color:16777215,emissive:0,metalness:1,roughness:1,transparent:!1,depthTest:!0,side:u.FrontSide}),Q}function k(e,n,t){for(var s in t.extensions)e[s]===void 0&&(n.userData.gltfExtensions=n.userData.gltfExtensions||{},n.userData.gltfExtensions[s]=t.extensions[s])}function O(e,n){n.extras!==void 0&&(typeof n.extras=="object"?Object.assign(e.userData,n.extras):console.warn("THREE.GLTFLoader: Ignoring primitive type .extras, "+n.extras))}function me(e,n,t){for(var s=!1,r=!1,a=0,i=n.length;a<i;a++){var o=n[a];if(o.POSITION!==void 0&&(s=!0),o.NORMAL!==void 0&&(r=!0),s&&r)break}if(!s&&!r)return Promise.resolve(e);for(var l=[],p=[],a=0,i=n.length;a<i;a++){var o=n[a];if(s){var c=o.POSITION!==void 0?t.getDependency("accessor",o.POSITION):e.attributes.position;l.push(c)}if(r){var c=o.NORMAL!==void 0?t.getDependency("accessor",o.NORMAL):e.attributes.normal;p.push(c)}}return Promise.all([Promise.all(l),Promise.all(p)]).then(function(f){for(var d=f[0],h=f[1],v=0,g=d.length;v<g;v++)e.attributes.position!==d[v]&&(d[v]=ce(d[v]));for(var v=0,g=h.length;v<g;v++)e.attributes.normal!==h[v]&&(h[v]=ce(h[v]));for(var v=0,g=n.length;v<g;v++){var S=n[v],E="morphTarget"+v;if(s&&S.POSITION!==void 0){var L=d[v];L.name=E;for(var _=e.attributes.position,M=0,w=L.count;M<w;M++)L.setXYZ(M,L.getX(M)+_.getX(M),L.getY(M)+_.getY(M),L.getZ(M)+_.getZ(M))}if(r&&S.NORMAL!==void 0){var A=h[v];A.name=E;for(var m=e.attributes.normal,M=0,w=A.count;M<w;M++)A.setXYZ(M,A.getX(M)+m.getX(M),A.getY(M)+m.getY(M),A.getZ(M)+m.getZ(M))}}return s&&(e.morphAttributes.position=d),r&&(e.morphAttributes.normal=h),e})}function _e(e,n){if(e.updateMorphTargets(),n.weights!==void 0)for(var t=0,s=n.weights.length;t<s;t++)e.morphTargetInfluences[t]=n.weights[t];if(n.extras&&Array.isArray(n.extras.targetNames)){var r=n.extras.targetNames;if(e.morphTargetInfluences.length===r.length){e.morphTargetDictionary={};for(var t=0,s=r.length;t<s;t++)e.morphTargetDictionary[r[t]]=t}else console.warn("THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.")}}function Ae(e){var n=e.extensions&&e.extensions[T.KHR_DRACO_MESH_COMPRESSION],t;return n?t="draco:"+n.bufferView+":"+n.indices+":"+fe(n.attributes):t=e.indices+":"+fe(e.attributes)+":"+e.mode,t}function fe(e){for(var n="",t=Object.keys(e).sort(),s=0,r=t.length;s<r;s++)n+=t[s]+":"+e[t[s]]+";";return n}function ce(e){if(e.isInterleavedBufferAttribute){for(var n=e.count,t=e.itemSize,s=e.array.slice(0,n*t),r=0,a=0;r<n;++r)s[a++]=e.getX(r),t>=2&&(s[a++]=e.getY(r)),t>=3&&(s[a++]=e.getZ(r)),t>=4&&(s[a++]=e.getW(r));return new u.BufferAttribute(s,t,e.normalized)}return e.clone()}function R(e,n,t){this.json=e||{},this.extensions=n||{},this.options=t||{},this.cache=new Te,this.primitiveCache={},this.textureLoader=new u.TextureLoader(this.options.manager),this.textureLoader.setCrossOrigin(this.options.crossOrigin),this.fileLoader=new u.FileLoader(this.options.manager),this.fileLoader.setResponseType("arraybuffer"),this.options.crossOrigin==="use-credentials"&&this.fileLoader.setWithCredentials(!0)}R.prototype.parse=function(e,n){var t=this,s=this.json,r=this.extensions;this.cache.removeAll(),this.markDefs(),Promise.all([this.getDependencies("scene"),this.getDependencies("animation"),this.getDependencies("camera")]).then(function(a){var i={scene:a[0][s.scene||0],scenes:a[0],animations:a[1],cameras:a[2],asset:s.asset,parser:t,userData:{}};k(r,i,s),O(i,s),e(i)}).catch(n)},R.prototype.markDefs=function(){for(var e=this.json.nodes||[],n=this.json.skins||[],t=this.json.meshes||[],s={},r={},a=0,i=n.length;a<i;a++)for(var o=n[a].joints,l=0,p=o.length;l<p;l++)e[o[l]].isBone=!0;for(var c=0,f=e.length;c<f;c++){var d=e[c];d.mesh!==void 0&&(s[d.mesh]===void 0&&(s[d.mesh]=r[d.mesh]=0),s[d.mesh]++,d.skin!==void 0&&(t[d.mesh].isSkinnedMesh=!0))}this.json.meshReferences=s,this.json.meshUses=r},R.prototype.getDependency=function(e,n){var t=e+":"+n,s=this.cache.get(t);if(!s){switch(e){case"scene":s=this.loadScene(n);break;case"node":s=this.loadNode(n);break;case"mesh":s=this.loadMesh(n);break;case"accessor":s=this.loadAccessor(n);break;case"bufferView":s=this.loadBufferView(n);break;case"buffer":s=this.loadBuffer(n);break;case"material":s=this.loadMaterial(n);break;case"texture":s=this.loadTexture(n);break;case"skin":s=this.loadSkin(n);break;case"animation":s=this.loadAnimation(n);break;case"camera":s=this.loadCamera(n);break;case"light":s=this.extensions[T.KHR_LIGHTS_PUNCTUAL].loadLight(n);break;default:throw new Error("Unknown type: "+e)}this.cache.add(t,s)}return s},R.prototype.getDependencies=function(e){var n=this.cache.get(e);if(!n){var t=this,s=this.json[e+(e==="mesh"?"es":"s")]||[];n=Promise.all(s.map(function(r,a){return t.getDependency(e,a)})),this.cache.add(e,n)}return n},R.prototype.loadBuffer=function(e){var n=this.json.buffers[e],t=this.fileLoader;if(n.type&&n.type!=="arraybuffer")throw new Error("THREE.GLTFLoader: "+n.type+" buffer type is not supported.");if(n.uri===void 0&&e===0)return Promise.resolve(this.extensions[T.KHR_BINARY_GLTF].body);var s=this.options;return new Promise(function(r,a){t.load(le(n.uri,s.path),r,void 0,function(){a(new Error('THREE.GLTFLoader: Failed to load buffer "'+n.uri+'".'))})})},R.prototype.loadBufferView=function(e){var n=this.json.bufferViews[e];return this.getDependency("buffer",n.buffer).then(function(t){var s=n.byteLength||0,r=n.byteOffset||0;return t.slice(r,r+s)})},R.prototype.loadAccessor=function(e){var n=this,t=this.json,s=this.json.accessors[e];if(s.bufferView===void 0&&s.sparse===void 0)return Promise.resolve(null);var r=[];return s.bufferView!==void 0?r.push(this.getDependency("bufferView",s.bufferView)):r.push(null),s.sparse!==void 0&&(r.push(this.getDependency("bufferView",s.sparse.indices.bufferView)),r.push(this.getDependency("bufferView",s.sparse.values.bufferView))),Promise.all(r).then(function(a){var i=a[0],o=oe[s.type],l=z[s.componentType],p=l.BYTES_PER_ELEMENT,c=p*o,f=s.byteOffset||0,d=s.bufferView!==void 0?t.bufferViews[s.bufferView].byteStride:void 0,h=s.normalized===!0,v,g;if(d&&d!==c){var S="InterleavedBuffer:"+s.bufferView+":"+s.componentType,E=n.cache.get(S);E||(v=new l(i),E=new u.InterleavedBuffer(v,d/p),n.cache.add(S,E)),g=new u.InterleavedBufferAttribute(E,o,f/p,h)}else i===null?v=new l(s.count*o):v=new l(i,f,s.count*o),g=new u.BufferAttribute(v,o,h);if(s.sparse!==void 0){var L=oe.SCALAR,_=z[s.sparse.indices.componentType],M=s.sparse.indices.byteOffset||0,w=s.sparse.values.byteOffset||0,A=new _(a[1],M,s.sparse.count*L),m=new l(a[2],w,s.sparse.count*o);i!==null&&g.setArray(g.array.slice());for(var I=0,x=A.length;I<x;I++){var G=A[I];if(g.setX(G,m[I*o]),o>=2&&g.setY(G,m[I*o+1]),o>=3&&g.setZ(G,m[I*o+2]),o>=4&&g.setW(G,m[I*o+3]),o>=5)throw new Error("THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.")}}return g})},R.prototype.loadTexture=function(e){var n=this,t=this.json,s=this.options,r=this.textureLoader,a=window.URL||window.webkitURL,i=t.textures[e],o=i.extensions||{},l;o[T.MSFT_TEXTURE_DDS]?l=t.images[o[T.MSFT_TEXTURE_DDS].source]:l=t.images[i.source];var p=l.uri,c=!1;return l.bufferView!==void 0&&(p=n.getDependency("bufferView",l.bufferView).then(function(f){c=!0;var d=new Blob([f],{type:l.mimeType});return p=a.createObjectURL(d),p})),Promise.resolve(p).then(function(f){var d=r.manager.getHandler(f);return d||(d=o[T.MSFT_TEXTURE_DDS]?n.extensions[T.MSFT_TEXTURE_DDS].ddsLoader:r),new Promise(function(h,v){d.load(le(f,s.path),h,void 0,v)})}).then(function(f){c===!0&&a.revokeObjectURL(p),f.flipY=!1,i.name!==void 0&&(f.name=i.name),l.mimeType in ue&&(f.format=ue[l.mimeType]);var d=t.samplers||{},h=d[i.sampler]||{};return f.magFilter=ae[h.magFilter]||u.LinearFilter,f.minFilter=ae[h.minFilter]||u.LinearMipMapLinearFilter,f.wrapS=ie[h.wrapS]||u.RepeatWrapping,f.wrapT=ie[h.wrapT]||u.RepeatWrapping,f})},R.prototype.assignTexture=function(e,n,t){var s=this;return this.getDependency("texture",t.index).then(function(r){if(!r.isCompressedTexture)switch(n){case"aoMap":case"emissiveMap":case"metalnessMap":case"normalMap":case"roughnessMap":r.format=u.RGBFormat;break}if(s.extensions[T.KHR_TEXTURE_TRANSFORM]){var a=t.extensions!==void 0?t.extensions[T.KHR_TEXTURE_TRANSFORM]:void 0;a&&(r=s.extensions[T.KHR_TEXTURE_TRANSFORM].extendTexture(r,a))}e[n]=r})},R.prototype.assignFinalMaterial=function(e){var n=e.geometry,t=e.material,s=this.extensions,r=n.attributes.tangent!==void 0,a=n.attributes.color!==void 0,i=n.attributes.normal===void 0,o=e.isSkinnedMesh===!0,l=Object.keys(n.morphAttributes).length>0,p=l&&n.morphAttributes.normal!==void 0;if(e.isPoints){var c="PointsMaterial:"+t.uuid,f=this.cache.get(c);f||(f=new u.PointsMaterial,u.Material.prototype.copy.call(f,t),f.color.copy(t.color),f.map=t.map,f.lights=!1,this.cache.add(c,f)),t=f}else if(e.isLine){var c="LineBasicMaterial:"+t.uuid,d=this.cache.get(c);d||(d=new u.LineBasicMaterial,u.Material.prototype.copy.call(d,t),d.color.copy(t.color),d.lights=!1,this.cache.add(c,d)),t=d}if(r||a||i||o||l){var c="ClonedMaterial:"+t.uuid+":";t.isGLTFSpecularGlossinessMaterial&&(c+="specular-glossiness:"),o&&(c+="skinning:"),r&&(c+="vertex-tangents:"),a&&(c+="vertex-colors:"),i&&(c+="flat-shading:"),l&&(c+="morph-targets:"),p&&(c+="morph-normals:");var h=this.cache.get(c);h||(h=t.isGLTFSpecularGlossinessMaterial?s[T.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS].cloneMaterial(t):t.clone(),o&&(h.skinning=!0),r&&(h.vertexTangents=!0),a&&(h.vertexColors=u.VertexColors),i&&(h.flatShading=!0),l&&(h.morphTargets=!0),p&&(h.morphNormals=!0),this.cache.add(c,h)),t=h}t.aoMap&&n.attributes.uv2===void 0&&n.attributes.uv!==void 0&&(console.log("THREE.GLTFLoader: Duplicating UVs to support aoMap."),n.addAttribute("uv2",new u.BufferAttribute(n.attributes.uv.array,2))),t.isGLTFSpecularGlossinessMaterial&&(e.onBeforeRender=s[T.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS].refreshUniforms),e.material=t},R.prototype.loadMaterial=function(e){var n=this,t=this.json,s=this.extensions,r=t.materials[e],a,i={},o=r.extensions||{},l=[];if(o[T.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS]){var p=s[T.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS];a=p.getMaterialType(),l.push(p.extendParams(i,r,n))}else if(o[T.KHR_MATERIALS_UNLIT]){var c=s[T.KHR_MATERIALS_UNLIT];a=c.getMaterialType(),l.push(c.extendParams(i,r,n))}else{a=u.MeshStandardMaterial;var f=r.pbrMetallicRoughness||{};if(i.color=new u.Color(1,1,1),i.opacity=1,Array.isArray(f.baseColorFactor)){var d=f.baseColorFactor;i.color.fromArray(d),i.opacity=d[3]}f.baseColorTexture!==void 0&&l.push(n.assignTexture(i,"map",f.baseColorTexture)),i.metalness=f.metallicFactor!==void 0?f.metallicFactor:1,i.roughness=f.roughnessFactor!==void 0?f.roughnessFactor:1,f.metallicRoughnessTexture!==void 0&&(l.push(n.assignTexture(i,"metalnessMap",f.metallicRoughnessTexture)),l.push(n.assignTexture(i,"roughnessMap",f.metallicRoughnessTexture)))}r.doubleSided===!0&&(i.side=u.DoubleSide);var h=r.alphaMode||J.OPAQUE;return h===J.BLEND?i.transparent=!0:(i.transparent=!1,h===J.MASK&&(i.alphaTest=r.alphaCutoff!==void 0?r.alphaCutoff:.5)),r.normalTexture!==void 0&&a!==u.MeshBasicMaterial&&(l.push(n.assignTexture(i,"normalMap",r.normalTexture)),i.normalScale=new u.Vector2(1,1),r.normalTexture.scale!==void 0&&i.normalScale.set(r.normalTexture.scale,r.normalTexture.scale)),r.occlusionTexture!==void 0&&a!==u.MeshBasicMaterial&&(l.push(n.assignTexture(i,"aoMap",r.occlusionTexture)),r.occlusionTexture.strength!==void 0&&(i.aoMapIntensity=r.occlusionTexture.strength)),r.emissiveFactor!==void 0&&a!==u.MeshBasicMaterial&&(i.emissive=new u.Color().fromArray(r.emissiveFactor)),r.emissiveTexture!==void 0&&a!==u.MeshBasicMaterial&&l.push(n.assignTexture(i,"emissiveMap",r.emissiveTexture)),Promise.all(l).then(function(){var v;return a===u.ShaderMaterial?v=s[T.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS].createMaterial(i):v=new a(i),r.name!==void 0&&(v.name=r.name),v.map&&(v.map.encoding=u.sRGBEncoding),v.emissiveMap&&(v.emissiveMap.encoding=u.sRGBEncoding),v.specularMap&&(v.specularMap.encoding=u.sRGBEncoding),O(v,r),r.extensions&&k(s,v,r),v})};function pe(e,n,t){var s=n.attributes,r=[];function a(p,c){return t.getDependency("accessor",p).then(function(f){e.addAttribute(c,f)})}for(var i in s){var o=Z[i]||i.toLowerCase();o in e.attributes||r.push(a(s[i],o))}if(n.indices!==void 0&&!e.index){var l=t.getDependency("accessor",n.indices).then(function(p){e.setIndex(p)});r.push(l)}return O(e,n),Promise.all(r).then(function(){return n.targets!==void 0?me(e,n.targets,t):e})}R.prototype.loadGeometries=function(e){var n=this,t=this.extensions,s=this.primitiveCache;function r(d){return t[T.KHR_DRACO_MESH_COMPRESSION].decodePrimitive(d,n).then(function(h){return pe(h,d,n)})}for(var a=[],i=0,o=e.length;i<o;i++){var l=e[i],p=Ae(l),c=s[p];if(c)a.push(c.promise);else{var f;l.extensions&&l.extensions[T.KHR_DRACO_MESH_COMPRESSION]?f=r(l):f=pe(new u.BufferGeometry,l,n),s[p]={primitive:l,promise:f},a.push(f)}}return Promise.all(a)},R.prototype.loadMesh=function(e){for(var n=this,t=this.json,s=t.meshes[e],r=s.primitives,a=[],i=0,o=r.length;i<o;i++){var l=r[i].material===void 0?Re():this.getDependency("material",r[i].material);a.push(l)}return Promise.all(a).then(function(p){return n.loadGeometries(r).then(function(c){for(var f=[],d=0,h=c.length;d<h;d++){var v=c[d],g=r[d],S,E=p[d];if(g.mode===b.TRIANGLES||g.mode===b.TRIANGLE_STRIP||g.mode===b.TRIANGLE_FAN||g.mode===void 0)S=s.isSkinnedMesh===!0?new u.SkinnedMesh(v,E):new u.Mesh(v,E),S.isSkinnedMesh===!0&&!S.geometry.attributes.skinWeight.normalized&&S.normalizeSkinWeights(),g.mode===b.TRIANGLE_STRIP?S.drawMode=u.TriangleStripDrawMode:g.mode===b.TRIANGLE_FAN&&(S.drawMode=u.TriangleFanDrawMode);else if(g.mode===b.LINES)S=new u.LineSegments(v,E);else if(g.mode===b.LINE_STRIP)S=new u.Line(v,E);else if(g.mode===b.LINE_LOOP)S=new u.LineLoop(v,E);else if(g.mode===b.POINTS)S=new u.Points(v,E);else throw new Error("THREE.GLTFLoader: Primitive mode unsupported: "+g.mode);Object.keys(S.geometry.morphAttributes).length>0&&_e(S,s),S.name=s.name||"mesh_"+e,c.length>1&&(S.name+="_"+d),O(S,s),n.assignFinalMaterial(S),f.push(S)}if(f.length===1)return f[0];for(var L=new u.Group,d=0,h=f.length;d<h;d++)L.add(f[d]);return L})})},R.prototype.loadCamera=function(e){var n,t=this.json.cameras[e],s=t[t.type];if(!s){console.warn("THREE.GLTFLoader: Missing camera parameters.");return}return t.type==="perspective"?n=new u.PerspectiveCamera(u.Math.radToDeg(s.yfov),s.aspectRatio||1,s.znear||1,s.zfar||2e6):t.type==="orthographic"&&(n=new u.OrthographicCamera(s.xmag/-2,s.xmag/2,s.ymag/2,s.ymag/-2,s.znear,s.zfar)),t.name!==void 0&&(n.name=t.name),O(n,t),Promise.resolve(n)},R.prototype.loadSkin=function(e){var n=this.json.skins[e],t={joints:n.joints};return n.inverseBindMatrices===void 0?Promise.resolve(t):this.getDependency("accessor",n.inverseBindMatrices).then(function(s){return t.inverseBindMatrices=s,t})},R.prototype.loadAnimation=function(e){for(var n=this.json,t=n.animations[e],s=[],r=[],a=[],i=[],o=[],l=0,p=t.channels.length;l<p;l++){var c=t.channels[l],f=t.samplers[c.sampler],d=c.target,h=d.node!==void 0?d.node:d.id,v=t.parameters!==void 0?t.parameters[f.input]:f.input,g=t.parameters!==void 0?t.parameters[f.output]:f.output;s.push(this.getDependency("node",h)),r.push(this.getDependency("accessor",v)),a.push(this.getDependency("accessor",g)),i.push(f),o.push(d)}return Promise.all([Promise.all(s),Promise.all(r),Promise.all(a),Promise.all(i),Promise.all(o)]).then(function(S){for(var E=S[0],L=S[1],_=S[2],M=S[3],w=S[4],A=[],m=0,I=E.length;m<I;m++){var x=E[m],G=L[m],q=_[m],K=M[m],V=w[m];if(x!==void 0){x.updateMatrix(),x.matrixAutoUpdate=!0;var H;switch(U[V.path]){case U.weights:H=u.NumberKeyframeTrack;break;case U.rotation:H=u.QuaternionKeyframeTrack;break;case U.position:case U.scale:default:H=u.VectorKeyframeTrack;break}var de=x.name?x.name:x.uuid,ve=K.interpolation!==void 0?Le[K.interpolation]:u.InterpolateLinear,D=[];U[V.path]===U.weights?x.traverse(function(C){C.isMesh===!0&&C.morphTargetInfluences&&D.push(C.name?C.name:C.uuid)}):D.push(de);var F=q.array;if(q.normalized){var N;if(F.constructor===Int8Array)N=1/127;else if(F.constructor===Uint8Array)N=1/255;else if(F.constructor==Int16Array)N=1/32767;else if(F.constructor===Uint16Array)N=1/65535;else throw new Error("THREE.GLTFLoader: Unsupported output accessor component type.");for(var $=new Float32Array(F.length),P=0,j=F.length;P<j;P++)$[P]=F[P]*N;F=$}for(var P=0,j=D.length;P<j;P++){var X=new H(D[P]+"."+U[V.path],G.array,F,ve);K.interpolation==="CUBICSPLINE"&&(X.createInterpolant=function(ge){return new y(this.times,this.values,this.getValueSize()/3,ge)},X.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline=!0),A.push(X)}}}var he=t.name!==void 0?t.name:"animation_"+e;return new u.AnimationClip(he,void 0,A)})},R.prototype.loadNode=function(e){var n=this.json,t=this.extensions,s=this,r=n.meshReferences,a=n.meshUses,i=n.nodes[e];return function(){return i.isBone===!0?Promise.resolve(new u.Bone):i.mesh!==void 0?s.getDependency("mesh",i.mesh).then(function(o){var l;if(r[i.mesh]>1){var p=a[i.mesh]++;l=o.clone(),l.name+="_instance_"+p,l.onBeforeRender=o.onBeforeRender;for(var c=0,f=l.children.length;c<f;c++)l.children[c].name+="_instance_"+p,l.children[c].onBeforeRender=o.children[c].onBeforeRender}else l=o;return i.weights!==void 0&&l.traverse(function(d){if(d.isMesh)for(var h=0,v=i.weights.length;h<v;h++)d.morphTargetInfluences[h]=i.weights[h]}),l}):i.camera!==void 0?s.getDependency("camera",i.camera):i.extensions&&i.extensions[T.KHR_LIGHTS_PUNCTUAL]&&i.extensions[T.KHR_LIGHTS_PUNCTUAL].light!==void 0?s.getDependency("light",i.extensions[T.KHR_LIGHTS_PUNCTUAL].light):Promise.resolve(new u.Object3D)}().then(function(o){if(i.name!==void 0&&(o.userData.name=i.name,o.name=u.PropertyBinding.sanitizeNodeName(i.name)),O(o,i),i.extensions&&k(t,o,i),i.matrix!==void 0){var l=new u.Matrix4;l.fromArray(i.matrix),o.applyMatrix(l)}else i.translation!==void 0&&o.position.fromArray(i.translation),i.rotation!==void 0&&o.quaternion.fromArray(i.rotation),i.scale!==void 0&&o.scale.fromArray(i.scale);return o})},R.prototype.loadScene=function(){function e(n,t,s,r){var a=s.nodes[n];return r.getDependency("node",n).then(function(i){if(a.skin===void 0)return i;var o;return r.getDependency("skin",a.skin).then(function(l){o=l;for(var p=[],c=0,f=o.joints.length;c<f;c++)p.push(r.getDependency("node",o.joints[c]));return Promise.all(p)}).then(function(l){for(var p=i.isGroup===!0?i.children:[i],c=0,f=p.length;c<f;c++){for(var d=p[c],h=[],v=[],g=0,S=l.length;g<S;g++){var E=l[g];if(E){h.push(E);var L=new u.Matrix4;o.inverseBindMatrices!==void 0&&L.fromArray(o.inverseBindMatrices.array,g*16),v.push(L)}else console.warn('THREE.GLTFLoader: Joint "%s" could not be found.',o.joints[g])}d.bind(new u.Skeleton(h,v),d.matrixWorld)}return i})}).then(function(i){t.add(i);var o=[];if(a.children)for(var l=a.children,p=0,c=l.length;p<c;p++){var f=l[p];o.push(e(f,i,s,r))}return Promise.all(o)})}return function(t){var s=this.json,r=this.extensions,a=this.json.scenes[t],i=this,o=new u.Scene;a.name!==void 0&&(o.name=a.name),O(o,a),a.extensions&&k(r,o,a);for(var l=a.nodes||[],p=[],c=0,f=l.length;c<f;c++)p.push(e(l[c],o,s,i));return Promise.all(p).then(function(){return o})}}();export default Y;
