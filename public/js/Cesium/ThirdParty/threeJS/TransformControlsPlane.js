import e from"./three.js";function n(){"use strict";e.Mesh.call(this,new e.PlaneBufferGeometry(1e5,1e5,2,2),new e.MeshBasicMaterial({visible:!1,wireframe:!0,side:e.DoubleSide,transparent:!0,opacity:.1})),this.type="TransformControlsPlane";var s=new e.Vector3(1,0,0),a=new e.Vector3(0,1,0),o=new e.Vector3(0,0,1),p=new e.Vector3,t=new e.Vector3,r=new e.Vector3,l=new e.Matrix4,c=new e.Quaternion;this.updateMatrixWorld=function(){var i=this.space;switch(this.position.copy(this.worldPosition),this.mode==="scale"&&(i="local"),s.set(1,0,0).applyQuaternion(i==="local"?this.worldQuaternion:c),a.set(0,1,0).applyQuaternion(i==="local"?this.worldQuaternion:c),o.set(0,0,1).applyQuaternion(i==="local"?this.worldQuaternion:c),r.copy(a),this.mode){case"translate":case"scale":switch(this.axis){case"X":r.copy(this.eye).cross(s),t.copy(s).cross(r);break;case"Y":r.copy(this.eye).cross(a),t.copy(a).cross(r);break;case"Z":r.copy(this.eye).cross(o),t.copy(o).cross(r);break;case"XY":t.copy(o);break;case"YZ":t.copy(s);break;case"XZ":r.copy(o),t.copy(a);break;case"XYZ":case"E":t.set(0,0,0);break}break;case"rotate":default:t.set(0,0,0)}t.length()===0?this.quaternion.copy(this.cameraQuaternion):(l.lookAt(p.set(0,0,0),t,r),this.quaternion.setFromRotationMatrix(l)),e.Object3D.prototype.updateMatrixWorld.call(this)}}n.prototype=Object.assign(Object.create(e.Mesh.prototype),{constructor:n,isTransformControlsPlane:!0});export default n;
