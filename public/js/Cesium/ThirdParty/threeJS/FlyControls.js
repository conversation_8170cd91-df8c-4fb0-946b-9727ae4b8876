import s from"./three.js";const S={type:"change"};function a(n,l){this.object=n,this.domElement=l,this.movementSpeed=1,this.rollSpeed=.005,this.dragToLook=!1,this.autoForward=!1;const t=this,r=1e-6,h=new s.Quaternion,c=new s.Vector3;this.tmpQuaternion=new s.Quaternion,this.status=0,this.moveState={up:0,down:0,left:0,right:0,forward:0,back:0,pitchUp:0,pitchDown:0,yawLeft:0,yawRight:0,rollLeft:0,rollRight:0},this.moveVector=new s.Vector3(0,0,0),this.rotationVector=new s.Vector3(0,0,0),this.keydown=function(e){if(!e.altKey){switch(e.code){case"ShiftLeft":case"ShiftRight":this.movementSpeedMultiplier=.1;break;case"KeyW":this.moveState.forward=1;break;case"KeyS":this.moveState.back=1;break;case"KeyA":this.moveState.left=1;break;case"KeyD":this.moveState.right=1;break;case"KeyR":this.moveState.up=1;break;case"KeyF":this.moveState.down=1;break;case"ArrowUp":this.moveState.pitchUp=1;break;case"ArrowDown":this.moveState.pitchDown=1;break;case"ArrowLeft":this.moveState.yawLeft=1;break;case"ArrowRight":this.moveState.yawRight=1;break;case"KeyQ":this.moveState.rollLeft=1;break;case"KeyE":this.moveState.rollRight=1;break}this.updateMovementVector(),this.updateRotationVector()}},this.keyup=function(e){switch(e.code){case"ShiftLeft":case"ShiftRight":this.movementSpeedMultiplier=1;break;case"KeyW":this.moveState.forward=0;break;case"KeyS":this.moveState.back=0;break;case"KeyA":this.moveState.left=0;break;case"KeyD":this.moveState.right=0;break;case"KeyR":this.moveState.up=0;break;case"KeyF":this.moveState.down=0;break;case"ArrowUp":this.moveState.pitchUp=0;break;case"ArrowDown":this.moveState.pitchDown=0;break;case"ArrowLeft":this.moveState.yawLeft=0;break;case"ArrowRight":this.moveState.yawRight=0;break;case"KeyQ":this.moveState.rollLeft=0;break;case"KeyE":this.moveState.rollRight=0;break}this.updateMovementVector(),this.updateRotationVector()},this.pointerdown=function(e){if(this.dragToLook)this.status++;else{switch(e.button){case 0:this.moveState.forward=1;break;case 2:this.moveState.back=1;break}this.updateMovementVector()}},this.pointermove=function(e){if(!this.dragToLook||this.status>0){const o=this.getContainerDimensions(),i=o.size[0]/2,f=o.size[1]/2;this.moveState.yawLeft=-(e.pageX-o.offset[0]-i)/i,this.moveState.pitchDown=(e.pageY-o.offset[1]-f)/f,this.updateRotationVector()}},this.pointerup=function(e){if(this.dragToLook)this.status--,this.moveState.yawLeft=this.moveState.pitchDown=0;else{switch(e.button){case 0:this.moveState.forward=0;break;case 2:this.moveState.back=0;break}this.updateMovementVector()}this.updateRotationVector()},this.update=function(e){const o=e*t.movementSpeed,i=e*t.rollSpeed;t.object.translateX(t.moveVector.x*o),t.object.translateY(t.moveVector.y*o),t.object.translateZ(t.moveVector.z*o),t.tmpQuaternion.set(t.rotationVector.x*i,t.rotationVector.y*i,t.rotationVector.z*i,1).normalize(),t.object.quaternion.multiply(t.tmpQuaternion),(c.distanceToSquared(t.object.position)>r||8*(1-h.dot(t.object.quaternion))>r)&&(t.dispatchEvent(S),h.copy(t.object.quaternion),c.copy(t.object.position))},this.updateMovementVector=function(){const e=this.moveState.forward||this.autoForward&&!this.moveState.back?1:0;this.moveVector.x=-this.moveState.left+this.moveState.right,this.moveVector.y=-this.moveState.down+this.moveState.up,this.moveVector.z=-e+this.moveState.back},this.updateRotationVector=function(){this.rotationVector.x=-this.moveState.pitchDown+this.moveState.pitchUp,this.rotationVector.y=-this.moveState.yawRight+this.moveState.yawLeft,this.rotationVector.z=-this.moveState.rollRight+this.moveState.rollLeft},this.getContainerDimensions=function(){return this.domElement!=document?{size:[this.domElement.offsetWidth,this.domElement.offsetHeight],offset:[this.domElement.offsetLeft,this.domElement.offsetTop]}:{size:[window.innerWidth,window.innerHeight],offset:[0,0]}},this.dispose=function(){this.domElement.removeEventListener("contextmenu",w),this.domElement.removeEventListener("pointerdown",d),this.domElement.removeEventListener("pointermove",m),this.domElement.removeEventListener("pointerup",p),window.removeEventListener("keydown",v),window.removeEventListener("keyup",u)};const m=this.pointermove.bind(this),d=this.pointerdown.bind(this),p=this.pointerup.bind(this),v=this.keydown.bind(this),u=this.keyup.bind(this);this.domElement.addEventListener("contextmenu",w),this.domElement.addEventListener("pointerdown",d),this.domElement.addEventListener("pointermove",m),this.domElement.addEventListener("pointerup",p),window.addEventListener("keydown",v),window.addEventListener("keyup",u),this.updateMovementVector(),this.updateRotationVector()}function w(n){n.preventDefault()}a.prototype=Object.create(s.EventDispatcher.prototype),a.prototype.constructor=a;export default a;
