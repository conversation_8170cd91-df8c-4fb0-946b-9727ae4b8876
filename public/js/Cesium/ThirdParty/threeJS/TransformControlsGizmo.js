import e from"./three.js";function S(){"use strict";e.Object3D.call(this),this.type="TransformControlsGizmo";var y=new e.MeshBasicMaterial({depthTest:!1,depthWrite:!1,transparent:!0,side:e.DoubleSide,fog:!1}),M=new e.LineBasicMaterial({depthTest:!1,depthWrite:!1,transparent:!0,linewidth:1,fog:!1}),s=y.clone();s.opacity=.15;var c=y.clone();c.opacity=.33;var G=y.clone();G.color.set(16711680);var Y=y.clone();Y.color.set(65280);var g=y.clone();g.color.set(255);var p=y.clone();p.opacity=.25;var O=p.clone();O.color.set(16776960);var F=p.clone();F.color.set(65535);var V=p.clone();V.color.set(16711935);var K=y.clone();K.color.set(16776960);var T=M.clone();T.color.set(16711680);var z=M.clone();z.color.set(65280);var A=M.clone();A.color.set(255);var Z=M.clone();Z.color.set(65535);var I=M.clone();I.color.set(16711935);var P=M.clone();P.color.set(16776960);var W=M.clone();W.color.set(7895160);var b=P.clone();b.opacity=.25;var x=new e.CylinderBufferGeometry(0,.05,.2,12,1,!1),u=new e.BoxBufferGeometry(.125,.125,.125),i=new e.BufferGeometry;i.addAttribute("position",new e.Float32BufferAttribute([0,0,0,1,0,0],3));var X=function(l,r){for(var n=new e.BufferGeometry,f=[],t=0;t<=64*r;++t)f.push(0,Math.cos(t/32*Math.PI)*l,Math.sin(t/32*Math.PI)*l);return n.addAttribute("position",new e.Float32BufferAttribute(f,3)),n},N=function(){var l=new e.BufferGeometry;return l.addAttribute("position",new e.Float32BufferAttribute([0,0,0,1,1,1],3)),l},U={X:[[new e.Mesh(x,G),[1,0,0],[0,0,-Math.PI/2],null,"fwd"],[new e.Mesh(x,G),[1,0,0],[0,0,Math.PI/2],null,"bwd"],[new e.Line(i,T)]],Y:[[new e.Mesh(x,Y),[0,1,0],null,null,"fwd"],[new e.Mesh(x,Y),[0,1,0],[Math.PI,0,0],null,"bwd"],[new e.Line(i,z),null,[0,0,Math.PI/2]]],Z:[[new e.Mesh(x,g),[0,0,1],[Math.PI/2,0,0],null,"fwd"],[new e.Mesh(x,g),[0,0,1],[-Math.PI/2,0,0],null,"bwd"],[new e.Line(i,A),null,[0,-Math.PI/2,0]]],XYZ:[[new e.Mesh(new e.OctahedronBufferGeometry(.1,0),p),[0,0,0],[0,0,0]]],XY:[[new e.Mesh(new e.PlaneBufferGeometry(.295,.295),O),[.15,.15,0]],[new e.Line(i,P),[.18,.3,0],null,[.125,1,1]],[new e.Line(i,P),[.3,.18,0],[0,0,Math.PI/2],[.125,1,1]]],YZ:[[new e.Mesh(new e.PlaneBufferGeometry(.295,.295),F),[0,.15,.15],[0,Math.PI/2,0]],[new e.Line(i,Z),[0,.18,.3],[0,0,Math.PI/2],[.125,1,1]],[new e.Line(i,Z),[0,.3,.18],[0,-Math.PI/2,0],[.125,1,1]]],XZ:[[new e.Mesh(new e.PlaneBufferGeometry(.295,.295),V),[.15,0,.15],[-Math.PI/2,0,0]],[new e.Line(i,I),[.18,0,.3],null,[.125,1,1]],[new e.Line(i,I),[.3,0,.18],[0,-Math.PI/2,0],[.125,1,1]]]},$={X:[[new e.Mesh(new e.CylinderBufferGeometry(.2,0,1,4,1,!1),s),[.6,0,0],[0,0,-Math.PI/2]]],Y:[[new e.Mesh(new e.CylinderBufferGeometry(.2,0,1,4,1,!1),s),[0,.6,0]]],Z:[[new e.Mesh(new e.CylinderBufferGeometry(.2,0,1,4,1,!1),s),[0,0,.6],[Math.PI/2,0,0]]],XYZ:[[new e.Mesh(new e.OctahedronBufferGeometry(.2,0),s)]],XY:[[new e.Mesh(new e.PlaneBufferGeometry(.4,.4),s),[.2,.2,0]]],YZ:[[new e.Mesh(new e.PlaneBufferGeometry(.4,.4),s),[0,.2,.2],[0,Math.PI/2,0]]],XZ:[[new e.Mesh(new e.PlaneBufferGeometry(.4,.4),s),[.2,0,.2],[-Math.PI/2,0,0]]]},D={START:[[new e.Mesh(new e.OctahedronBufferGeometry(.01,2),c),null,null,null,"helper"]],END:[[new e.Mesh(new e.OctahedronBufferGeometry(.01,2),c),null,null,null,"helper"]],DELTA:[[new e.Line(N(),c),null,null,null,"helper"]],X:[[new e.Line(i,c.clone()),[-1e3,0,0],null,[1e6,1,1],"helper"]],Y:[[new e.Line(i,c.clone()),[0,-1e3,0],[0,0,Math.PI/2],[1e6,1,1],"helper"]],Z:[[new e.Line(i,c.clone()),[0,0,-1e3],[0,-Math.PI/2,0],[1e6,1,1],"helper"]]},H={X:[[new e.Line(X(1,.5),T)],[new e.Mesh(new e.OctahedronBufferGeometry(.04,0),G),[0,0,.99],null,[1,3,1]]],Y:[[new e.Line(X(1,.5),z),null,[0,0,-Math.PI/2]],[new e.Mesh(new e.OctahedronBufferGeometry(.04,0),Y),[0,0,.99],null,[3,1,1]]],Z:[[new e.Line(X(1,.5),A),null,[0,Math.PI/2,0]],[new e.Mesh(new e.OctahedronBufferGeometry(.04,0),g),[.99,0,0],null,[1,3,1]]],E:[[new e.Line(X(1.25,1),b),null,[0,Math.PI/2,0]],[new e.Mesh(new e.CylinderBufferGeometry(.03,0,.15,4,1,!1),b),[1.17,0,0],[0,0,-Math.PI/2],[1,1,.001]],[new e.Mesh(new e.CylinderBufferGeometry(.03,0,.15,4,1,!1),b),[-1.17,0,0],[0,0,Math.PI/2],[1,1,.001]],[new e.Mesh(new e.CylinderBufferGeometry(.03,0,.15,4,1,!1),b),[0,-1.17,0],[Math.PI,0,0],[1,1,.001]],[new e.Mesh(new e.CylinderBufferGeometry(.03,0,.15,4,1,!1),b),[0,1.17,0],[0,0,0],[1,1,.001]]],XYZE:[[new e.Line(X(1,1),W),null,[0,Math.PI/2,0]]]},_={AXIS:[[new e.Line(i,c.clone()),[-1e3,0,0],null,[1e6,1,1],"helper"]]},j={X:[[new e.Mesh(new e.TorusBufferGeometry(1,.1,4,24),s),[0,0,0],[0,-Math.PI/2,-Math.PI/2]]],Y:[[new e.Mesh(new e.TorusBufferGeometry(1,.1,4,24),s),[0,0,0],[Math.PI/2,0,0]]],Z:[[new e.Mesh(new e.TorusBufferGeometry(1,.1,4,24),s),[0,0,0],[0,0,-Math.PI/2]]],E:[[new e.Mesh(new e.TorusBufferGeometry(1.25,.1,2,24),s)]],XYZE:[[new e.Mesh(new e.SphereBufferGeometry(.7,10,8),s)]]},ee={X:[[new e.Mesh(u,G),[.8,0,0],[0,0,-Math.PI/2]],[new e.Line(i,T),null,null,[.8,1,1]]],Y:[[new e.Mesh(u,Y),[0,.8,0]],[new e.Line(i,z),null,[0,0,Math.PI/2],[.8,1,1]]],Z:[[new e.Mesh(u,g),[0,0,.8],[Math.PI/2,0,0]],[new e.Line(i,A),null,[0,-Math.PI/2,0],[.8,1,1]]],XY:[[new e.Mesh(u,O),[.85,.85,0],null,[2,2,.2]],[new e.Line(i,P),[.855,.98,0],null,[.125,1,1]],[new e.Line(i,P),[.98,.855,0],[0,0,Math.PI/2],[.125,1,1]]],YZ:[[new e.Mesh(u,F),[0,.85,.85],null,[.2,2,2]],[new e.Line(i,Z),[0,.855,.98],[0,0,Math.PI/2],[.125,1,1]],[new e.Line(i,Z),[0,.98,.855],[0,-Math.PI/2,0],[.125,1,1]]],XZ:[[new e.Mesh(u,V),[.85,0,.85],null,[2,.2,2]],[new e.Line(i,I),[.855,0,.98],null,[.125,1,1]],[new e.Line(i,I),[.98,0,.855],[0,-Math.PI/2,0],[.125,1,1]]],XYZX:[[new e.Mesh(new e.BoxBufferGeometry(.125,.125,.125),p),[1.1,0,0]]],XYZY:[[new e.Mesh(new e.BoxBufferGeometry(.125,.125,.125),p),[0,1.1,0]]],XYZZ:[[new e.Mesh(new e.BoxBufferGeometry(.125,.125,.125),p),[0,0,1.1]]]},te={X:[[new e.Mesh(new e.CylinderBufferGeometry(.2,0,.8,4,1,!1),s),[.5,0,0],[0,0,-Math.PI/2]]],Y:[[new e.Mesh(new e.CylinderBufferGeometry(.2,0,.8,4,1,!1),s),[0,.5,0]]],Z:[[new e.Mesh(new e.CylinderBufferGeometry(.2,0,.8,4,1,!1),s),[0,0,.5],[Math.PI/2,0,0]]],XY:[[new e.Mesh(u,s),[.85,.85,0],null,[3,3,.2]]],YZ:[[new e.Mesh(u,s),[0,.85,.85],null,[.2,3,3]]],XZ:[[new e.Mesh(u,s),[.85,0,.85],null,[3,.2,3]]],XYZX:[[new e.Mesh(new e.BoxBufferGeometry(.2,.2,.2),s),[1.1,0,0]]],XYZY:[[new e.Mesh(new e.BoxBufferGeometry(.2,.2,.2),s),[0,1.1,0]]],XYZZ:[[new e.Mesh(new e.BoxBufferGeometry(.2,.2,.2),s),[0,0,1.1]]]},ie={X:[[new e.Line(i,c.clone()),[-1e3,0,0],null,[1e6,1,1],"helper"]],Y:[[new e.Line(i,c.clone()),[0,-1e3,0],[0,0,Math.PI/2],[1e6,1,1],"helper"]],Z:[[new e.Line(i,c.clone()),[0,0,-1e3],[0,-Math.PI/2,0],[1e6,1,1],"helper"]]},w=function(l){var r=new e.Object3D;for(var n in l)for(var f=l[n].length;f--;){var t=l[n][f][0].clone(),B=l[n][f][1],m=l[n][f][2],h=l[n][f][3],C=l[n][f][4];t.name=n,t.tag=C,B&&t.position.set(B[0],B[1],B[2]),m&&t.rotation.set(m[0],m[1],m[2]),h&&t.scale.set(h[0],h[1],h[2]),t.updateMatrix();var q=t.geometry.clone();q.applyMatrix(t.matrix),t.geometry=q,t.renderOrder=1/0,t.position.set(0,0,0),t.rotation.set(0,0,0),t.scale.set(1,1,1),r.add(t)}return r},E=new e.Vector3(0,0,0),d=new e.Euler,o=new e.Vector3(0,1,0),R=new e.Vector3(0,0,0),J=new e.Matrix4,a=new e.Quaternion,k=new e.Quaternion,re=new e.Quaternion,L=new e.Vector3(1,0,0),v=new e.Vector3(0,1,0),Q=new e.Vector3(0,0,1);this.gizmo={},this.picker={},this.helper={},this.add(this.gizmo.translate=w(U)),this.add(this.gizmo.rotate=w(H)),this.add(this.gizmo.scale=w(ee)),this.add(this.picker.translate=w($)),this.add(this.picker.rotate=w(j)),this.add(this.picker.scale=w(te)),this.add(this.helper.translate=w(D)),this.add(this.helper.rotate=w(_)),this.add(this.helper.scale=w(ie)),this.picker.translate.visible=!1,this.picker.rotate.visible=!1,this.picker.scale.visible=!1,this.updateMatrixWorld=function(){var l=this.space;this.mode==="scale"&&(l="local");var r=l==="local"?this.worldQuaternion:re;this.gizmo.translate.visible=this.mode==="translate",this.gizmo.rotate.visible=this.mode==="rotate",this.gizmo.scale.visible=this.mode==="scale",this.helper.translate.visible=this.mode==="translate",this.helper.rotate.visible=this.mode==="rotate",this.helper.scale.visible=this.mode==="scale";var n=[];n=n.concat(this.picker[this.mode].children),n=n.concat(this.gizmo[this.mode].children),n=n.concat(this.helper[this.mode].children);for(var f=0;f<n.length;f++){var t=n[f];t.visible=!0,t.rotation.set(0,0,0),t.position.copy(this.worldPosition);var B=this.worldPosition.distanceTo(this.cameraPosition);if(t.scale.set(1,1,1).multiplyScalar(B*this.size/7),t.tag==="helper"){t.visible=!1,t.name==="AXIS"?(t.position.copy(this.worldPositionStart),t.visible=!!this.axis,this.axis==="X"&&(a.setFromEuler(d.set(0,0,0)),t.quaternion.copy(r).multiply(a),Math.abs(o.copy(L).applyQuaternion(r).dot(this.eye))>.9&&(t.visible=!1)),this.axis==="Y"&&(a.setFromEuler(d.set(0,0,Math.PI/2)),t.quaternion.copy(r).multiply(a),Math.abs(o.copy(v).applyQuaternion(r).dot(this.eye))>.9&&(t.visible=!1)),this.axis==="Z"&&(a.setFromEuler(d.set(0,Math.PI/2,0)),t.quaternion.copy(r).multiply(a),Math.abs(o.copy(Q).applyQuaternion(r).dot(this.eye))>.9&&(t.visible=!1)),this.axis==="XYZE"&&(a.setFromEuler(d.set(0,Math.PI/2,0)),o.copy(this.rotationAxis),t.quaternion.setFromRotationMatrix(J.lookAt(R,o,v)),t.quaternion.multiply(a),t.visible=this.dragging),this.axis==="E"&&(t.visible=!1)):t.name==="START"?(t.position.copy(this.worldPositionStart),t.visible=this.dragging):t.name==="END"?(t.position.copy(this.worldPosition),t.visible=this.dragging):t.name==="DELTA"?(t.position.copy(this.worldPositionStart),t.quaternion.copy(this.worldQuaternionStart),E.set(1e-10,1e-10,1e-10).add(this.worldPositionStart).sub(this.worldPosition).multiplyScalar(-1),E.applyQuaternion(this.worldQuaternionStart.clone().inverse()),t.scale.copy(E),t.visible=this.dragging):(t.quaternion.copy(r),this.dragging?t.position.copy(this.worldPositionStart):t.position.copy(this.worldPosition),this.axis&&(t.visible=this.axis.search(t.name)!==-1));continue}if(t.quaternion.copy(r),this.mode==="translate"||this.mode==="scale"){var m=.99,h=.2,C=0;(t.name==="X"||t.name==="XYZX")&&Math.abs(o.copy(L).applyQuaternion(r).dot(this.eye))>m&&(t.scale.set(1e-10,1e-10,1e-10),t.visible=!1),(t.name==="Y"||t.name==="XYZY")&&Math.abs(o.copy(v).applyQuaternion(r).dot(this.eye))>m&&(t.scale.set(1e-10,1e-10,1e-10),t.visible=!1),(t.name==="Z"||t.name==="XYZZ")&&Math.abs(o.copy(Q).applyQuaternion(r).dot(this.eye))>m&&(t.scale.set(1e-10,1e-10,1e-10),t.visible=!1),t.name==="XY"&&Math.abs(o.copy(Q).applyQuaternion(r).dot(this.eye))<h&&(t.scale.set(1e-10,1e-10,1e-10),t.visible=!1),t.name==="YZ"&&Math.abs(o.copy(L).applyQuaternion(r).dot(this.eye))<h&&(t.scale.set(1e-10,1e-10,1e-10),t.visible=!1),t.name==="XZ"&&Math.abs(o.copy(v).applyQuaternion(r).dot(this.eye))<h&&(t.scale.set(1e-10,1e-10,1e-10),t.visible=!1),t.name.search("X")!==-1&&(o.copy(L).applyQuaternion(r).dot(this.eye)<C?t.tag==="fwd"?t.visible=!1:t.scale.x*=-1:t.tag==="bwd"&&(t.visible=!1)),t.name.search("Y")!==-1&&(o.copy(v).applyQuaternion(r).dot(this.eye)<C?t.tag==="fwd"?t.visible=!1:t.scale.y*=-1:t.tag==="bwd"&&(t.visible=!1)),t.name.search("Z")!==-1&&(o.copy(Q).applyQuaternion(r).dot(this.eye)<C?t.tag==="fwd"?t.visible=!1:t.scale.z*=-1:t.tag==="bwd"&&(t.visible=!1))}else this.mode==="rotate"&&(k.copy(r),o.copy(this.eye).applyQuaternion(a.copy(r).inverse()),t.name.search("E")!==-1&&t.quaternion.setFromRotationMatrix(J.lookAt(this.eye,R,v)),t.name==="X"&&(a.setFromAxisAngle(L,Math.atan2(-o.y,o.z)),a.multiplyQuaternions(k,a),t.quaternion.copy(a)),t.name==="Y"&&(a.setFromAxisAngle(v,Math.atan2(o.x,o.z)),a.multiplyQuaternions(k,a),t.quaternion.copy(a)),t.name==="Z"&&(a.setFromAxisAngle(Q,Math.atan2(o.y,o.x)),a.multiplyQuaternions(k,a),t.quaternion.copy(a)));t.visible=t.visible&&(t.name.indexOf("X")===-1||this.showX),t.visible=t.visible&&(t.name.indexOf("Y")===-1||this.showY),t.visible=t.visible&&(t.name.indexOf("Z")===-1||this.showZ),t.visible=t.visible&&(t.name.indexOf("E")===-1||this.showX&&this.showY&&this.showZ),t.material._opacity=t.material._opacity||t.material.opacity,t.material._color=t.material._color||t.material.color.clone(),t.material.color.copy(t.material._color),t.material.opacity=t.material._opacity,this.enabled?this.axis&&(t.name===this.axis?(t.material.opacity=1,t.material.color.lerp(new e.Color(1,1,1),.5)):this.axis.split("").some(function(q){return t.name===q})?(t.material.opacity=1,t.material.color.lerp(new e.Color(1,1,1),.5)):(t.material.opacity*=.25,t.material.color.lerp(new e.Color(1,1,1),.5))):(t.material.opacity*=.5,t.material.color.lerp(new e.Color(1,1,1),.5))}e.Object3D.prototype.updateMatrixWorld.call(this)}}S.prototype=Object.assign(Object.create(e.Object3D.prototype),{constructor:S,isTransformControlsGizmo:!0});export default S;
