import n from"./three.js";function M(s,H){this.object=s,this.domElement=H!==void 0?H:document,this.enabled=!0,this.target=new n.Vector3,this.minDistance=0,this.maxDistance=1/0,this.minZoom=0,this.maxZoom=1/0,this.minPolarAngle=0,this.maxPolarAngle=Math.PI,this.minAzimuthAngle=-1/0,this.maxAzimuthAngle=1/0,this.enableDamping=!1,this.dampingFactor=.25,this.enableZoom=!0,this.zoomSpeed=1,this.enableRotate=!0,this.rotateSpeed=1,this.enablePan=!0,this.panSpeed=1,this.screenSpacePanning=!1,this.keyPanSpeed=7,this.autoRotate=!1,this.autoRotateSpeed=2,this.enableKeys=!0,this.keys={LEFT:37,UP:38,RIGHT:39,BOTTOM:40},this.mouseButtons={LEFT:n.MOUSE.LEFT,MIDDLE:n.MOUSE.MIDDLE,RIGHT:n.MOUSE.RIGHT},this.target0=this.target.clone(),this.position0=this.object.position.clone(),this.zoom0=this.object.zoom,this.getPolarAngle=function(){return i.phi},this.getAzimuthalAngle=function(){return i.theta},this.saveState=function(){e.target0.copy(e.target),e.position0.copy(e.object.position),e.zoom0=e.object.zoom},this.reset=function(){e.target.copy(e.target0),e.object.position.copy(e.position0),e.object.zoom=e.zoom0,e.object.updateProjectionMatrix(),e.dispatchEvent(U),e.update(),r=a.NONE},this.update=function(){var t=new n.Vector3,o=new n.Quaternion().setFromUnitVectors(s.up,new n.Vector3(0,1,0)),c=o.clone().invert(),l=new n.Vector3,u=new n.Quaternion;return function(){var O=e.object.position;return t.copy(O).sub(e.target),t.applyQuaternion(o),i.setFromVector3(t),e.autoRotate&&r===a.NONE&&A(W()),i.theta+=m.theta,i.phi+=m.phi,i.theta=Math.max(e.minAzimuthAngle,Math.min(e.maxAzimuthAngle,i.theta)),i.phi=Math.max(e.minPolarAngle,Math.min(e.maxPolarAngle,i.phi)),i.makeSafe(),i.radius*=R,i.radius=Math.max(e.minDistance,Math.min(e.maxDistance,i.radius)),e.target.add(P),t.setFromSpherical(i),t.applyQuaternion(c),O.copy(e.target).add(t),e.object.lookAt(e.target),e.enableDamping===!0?(m.theta*=1-e.dampingFactor,m.phi*=1-e.dampingFactor,P.multiplyScalar(1-e.dampingFactor)):(m.set(0,0,0),P.set(0,0,0)),R=1,j||l.distanceToSquared(e.object.position)>x||8*(1-u.dot(e.object.quaternion))>x?(e.dispatchEvent(U),l.copy(e.object.position),u.copy(e.object.quaternion),j=!1,!0):!1}}(),this.dispose=function(){e.domElement.removeEventListener("contextmenu",B,!1),e.domElement.removeEventListener("mousedown",F,!1),e.domElement.removeEventListener("wheel",X,!1),e.domElement.removeEventListener("touchstart",_,!1),e.domElement.removeEventListener("touchend",q,!1),e.domElement.removeEventListener("touchmove",G,!1),document.removeEventListener("mousemove",N,!1),document.removeEventListener("mouseup",k,!1),window.removeEventListener("keydown",K,!1)};var e=this,U={type:"change"},L={type:"start"},S={type:"end"},a={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_DOLLY_PAN:4},r=a.NONE,x=1e-6,i=new n.Spherical,m=new n.Spherical,R=1,P=new n.Vector3,j=!1,d=new n.Vector2,h=new n.Vector2,p=new n.Vector2,f=new n.Vector2,b=new n.Vector2,E=new n.Vector2,g=new n.Vector2,y=new n.Vector2,T=new n.Vector2;function W(){return 2*Math.PI/60/60*e.autoRotateSpeed}function D(){return Math.pow(.95,e.zoomSpeed)}function A(t){m.theta+=t}function V(t){m.phi+=t}var I=function(){var t=new n.Vector3;return function(c,l){t.setFromMatrixColumn(l,0),t.multiplyScalar(-c),P.add(t)}}(),Z=function(){var t=new n.Vector3;return function(c,l){e.screenSpacePanning===!0?t.setFromMatrixColumn(l,1):(t.setFromMatrixColumn(l,0),t.crossVectors(e.object.up,t)),t.multiplyScalar(c),P.add(t)}}(),v=function(){var t=new n.Vector3;return function(c,l){var u=e.domElement===document?e.domElement.body:e.domElement;if(e.object.isPerspectiveCamera){var w=e.object.position;t.copy(w).sub(e.target);var O=t.length();O*=Math.tan(e.object.fov/2*Math.PI/180),I(2*c*O/u.clientHeight,e.object.matrix),Z(2*l*O/u.clientHeight,e.object.matrix)}else e.object.isOrthographicCamera?(I(c*(e.object.right-e.object.left)/e.object.zoom/u.clientWidth,e.object.matrix),Z(l*(e.object.top-e.object.bottom)/e.object.zoom/u.clientHeight,e.object.matrix)):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."),e.enablePan=!1)}}();function C(t){e.object.isPerspectiveCamera?R/=t:e.object.isOrthographicCamera?(e.object.zoom=Math.max(e.minZoom,Math.min(e.maxZoom,e.object.zoom*t)),e.object.updateProjectionMatrix(),j=!0):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),e.enableZoom=!1)}function z(t){e.object.isPerspectiveCamera?R*=t:e.object.isOrthographicCamera?(e.object.zoom=Math.max(e.minZoom,Math.min(e.maxZoom,e.object.zoom/t)),e.object.updateProjectionMatrix(),j=!0):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),e.enableZoom=!1)}function Q(t){d.set(t.clientX,t.clientY)}function J(t){g.set(t.clientX,t.clientY)}function Y(t){f.set(t.clientX,t.clientY)}function $(t){h.set(t.clientX,t.clientY),p.subVectors(h,d).multiplyScalar(e.rotateSpeed);var o=e.domElement===document?e.domElement.body:e.domElement;A(2*Math.PI*p.x/o.clientHeight),V(2*Math.PI*p.y/o.clientHeight),d.copy(h),e.update()}function ee(t){y.set(t.clientX,t.clientY),T.subVectors(y,g),T.y>0?C(D()):T.y<0&&z(D()),g.copy(y),e.update()}function te(t){b.set(t.clientX,t.clientY),E.subVectors(b,f).multiplyScalar(e.panSpeed),v(E.x,E.y),f.copy(b),e.update()}function ce(t){}function ne(t){t.deltaY<0?z(D()):t.deltaY>0&&C(D()),e.update()}function oe(t){var o=!1;switch(t.keyCode){case e.keys.UP:v(0,e.keyPanSpeed),o=!0;break;case e.keys.BOTTOM:v(0,-e.keyPanSpeed),o=!0;break;case e.keys.LEFT:v(e.keyPanSpeed,0),o=!0;break;case e.keys.RIGHT:v(-e.keyPanSpeed,0),o=!0;break}o&&(t.preventDefault(),e.update())}function ae(t){d.set(t.touches[0].pageX,t.touches[0].pageY)}function re(t){if(e.enableZoom){var o=t.touches[0].pageX-t.touches[1].pageX,c=t.touches[0].pageY-t.touches[1].pageY,l=Math.sqrt(o*o+c*c);g.set(0,l)}if(e.enablePan){var u=.5*(t.touches[0].pageX+t.touches[1].pageX),w=.5*(t.touches[0].pageY+t.touches[1].pageY);f.set(u,w)}}function se(t){h.set(t.touches[0].pageX,t.touches[0].pageY),p.subVectors(h,d).multiplyScalar(e.rotateSpeed);var o=e.domElement===document?e.domElement.body:e.domElement;A(2*Math.PI*p.x/o.clientHeight),V(2*Math.PI*p.y/o.clientHeight),d.copy(h),e.update()}function ie(t){if(e.enableZoom){var o=t.touches[0].pageX-t.touches[1].pageX,c=t.touches[0].pageY-t.touches[1].pageY,l=Math.sqrt(o*o+c*c);y.set(0,l),T.set(0,Math.pow(y.y/g.y,e.zoomSpeed)),C(T.y),g.copy(y)}if(e.enablePan){var u=.5*(t.touches[0].pageX+t.touches[1].pageX),w=.5*(t.touches[0].pageY+t.touches[1].pageY);b.set(u,w),E.subVectors(b,f).multiplyScalar(e.panSpeed),v(E.x,E.y),f.copy(b)}e.update()}function le(t){}function F(t){if(e.enabled!==!1){switch(t.preventDefault(),e.domElement.focus?e.domElement.focus():window.focus(),t.button){case e.mouseButtons.RIGHT:if(t.ctrlKey||t.metaKey||t.shiftKey){if(e.enablePan===!1)return;Y(t),r=a.PAN}else{if(e.enableRotate===!1)return;Q(t),r=a.ROTATE}break;case e.mouseButtons.MIDDLE:if(e.enableZoom===!1)return;J(t),r=a.DOLLY;break;case e.mouseButtons.LEFT:if(e.enablePan===!1)return;Y(t),r=a.PAN;break}r!==a.NONE&&(document.addEventListener("mousemove",N,!1),document.addEventListener("mouseup",k,!1),e.dispatchEvent(L))}}function N(t){if(e.enabled!==!1)switch(t.preventDefault(),r){case a.ROTATE:if(e.enableRotate===!1)return;$(t);break;case a.DOLLY:if(e.enableZoom===!1)return;ee(t);break;case a.PAN:if(e.enablePan===!1)return;te(t);break}}function k(t){e.enabled!==!1&&(document.removeEventListener("mousemove",N,!1),document.removeEventListener("mouseup",k,!1),e.dispatchEvent(S),r=a.NONE)}function X(t){e.enabled===!1||e.enableZoom===!1||r!==a.NONE&&r!==a.ROTATE||(t.preventDefault(),t.stopPropagation(),e.dispatchEvent(L),ne(t),e.dispatchEvent(S))}function K(t){e.enabled===!1||e.enableKeys===!1||e.enablePan===!1||oe(t)}function _(t){if(e.enabled!==!1){switch(t.preventDefault(),t.touches.length){case 1:if(e.enableRotate===!1)return;ae(t),r=a.TOUCH_ROTATE;break;case 2:if(e.enableZoom===!1&&e.enablePan===!1)return;re(t),r=a.TOUCH_DOLLY_PAN;break;default:r=a.NONE}r!==a.NONE&&e.dispatchEvent(L)}}function G(t){if(e.enabled!==!1)switch(t.preventDefault(),t.stopPropagation(),t.touches.length){case 1:if(e.enableRotate===!1||r!==a.TOUCH_ROTATE)return;se(t);break;case 2:if(e.enableZoom===!1&&e.enablePan===!1||r!==a.TOUCH_DOLLY_PAN)return;ie(t);break;default:r=a.NONE}}function q(t){e.enabled!==!1&&(e.dispatchEvent(S),r=a.NONE)}function B(t){e.enabled!==!1&&t.preventDefault()}e.domElement.addEventListener("contextmenu",B,!1),e.domElement.addEventListener("mousedown",F,!1),e.domElement.addEventListener("wheel",X,!1),e.domElement.addEventListener("touchstart",_,!1),e.domElement.addEventListener("touchend",q,!1),e.domElement.addEventListener("touchmove",G,!1),window.addEventListener("keydown",K,!1),this.update()}M.prototype=Object.create(n.EventDispatcher.prototype),M.prototype.constructor=M,Object.defineProperties(M.prototype,{center:{get:function(){return console.warn("THREE.OrbitControls: .center has been renamed to .target"),this.target}},noZoom:{get:function(){return console.warn("THREE.OrbitControls: .noZoom has been deprecated. Use .enableZoom instead."),!this.enableZoom},set:function(s){console.warn("THREE.OrbitControls: .noZoom has been deprecated. Use .enableZoom instead."),this.enableZoom=!s}},noRotate:{get:function(){return console.warn("THREE.OrbitControls: .noRotate has been deprecated. Use .enableRotate instead."),!this.enableRotate},set:function(s){console.warn("THREE.OrbitControls: .noRotate has been deprecated. Use .enableRotate instead."),this.enableRotate=!s}},noPan:{get:function(){return console.warn("THREE.OrbitControls: .noPan has been deprecated. Use .enablePan instead."),!this.enablePan},set:function(s){console.warn("THREE.OrbitControls: .noPan has been deprecated. Use .enablePan instead."),this.enablePan=!s}},noKeys:{get:function(){return console.warn("THREE.OrbitControls: .noKeys has been deprecated. Use .enableKeys instead."),!this.enableKeys},set:function(s){console.warn("THREE.OrbitControls: .noKeys has been deprecated. Use .enableKeys instead."),this.enableKeys=!s}},staticMoving:{get:function(){return console.warn("THREE.OrbitControls: .staticMoving has been deprecated. Use .enableDamping instead."),!this.enableDamping},set:function(s){console.warn("THREE.OrbitControls: .staticMoving has been deprecated. Use .enableDamping instead."),this.enableDamping=!s}},dynamicDampingFactor:{get:function(){return console.warn("THREE.OrbitControls: .dynamicDampingFactor has been renamed. Use .dampingFactor instead."),this.dampingFactor},set:function(s){console.warn("THREE.OrbitControls: .dynamicDampingFactor has been renamed. Use .dampingFactor instead."),this.dampingFactor=s}}});export default M;
