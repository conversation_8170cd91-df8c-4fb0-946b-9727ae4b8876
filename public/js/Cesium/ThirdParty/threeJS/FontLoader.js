import g from"./three.js";class T extends g.<PERSON>ader{constructor(t){super(t)}load(t,e,o,h){const i=this,s=new g.<PERSON>oader(this.manager);s.setPath(this.path),s.setRequestHeader(this.requestHeader),s.setWithCredentials(this.withCredentials),s.load(t,function(c){const a=i.parse(JSON.parse(c));e&&e(a)},o,h)}parse(t){return new F(t)}}class F{constructor(t){this.isFont=!0,this.type="Font",this.data=t}generateShapes(t,e=100){const o=[],h=k(t,e,this.data);for(let i=0,s=h.length;i<s;i++)o.push(...h[i].toShapes());return o}}function k(d,t,e){const o=Array.from(d),h=t/e.resolution,i=(e.boundingBox.yMax-e.boundingBox.yMin+e.underlineThickness)*h,s=[];let c=0,a=0;for(let p=0;p<o.length;p++){const u=o[p];if(u===`
`)c=0,a-=i;else{const l=w(u,h,c,a,e);c+=l.offsetX,s.push(l.path)}}return s}function w(d,t,e,o,h){const i=h.glyphs[d]||h.glyphs["?"];if(!i){console.error('THREE.Font: character "'+d+'" does not exists in font family '+h.familyName+".");return}const s=new g.ShapePath;let c,a,p,u,l,y,x,m;if(i.o){const n=i._cachedOutline||(i._cachedOutline=i.o.split(" "));for(let r=0,b=n.length;r<b;)switch(n[r++]){case"m":c=n[r++]*t+e,a=n[r++]*t+o,s.moveTo(c,a);break;case"l":c=n[r++]*t+e,a=n[r++]*t+o,s.lineTo(c,a);break;case"q":p=n[r++]*t+e,u=n[r++]*t+o,l=n[r++]*t+e,y=n[r++]*t+o,s.quadraticCurveTo(l,y,p,u);break;case"b":p=n[r++]*t+e,u=n[r++]*t+o,l=n[r++]*t+e,y=n[r++]*t+o,x=n[r++]*t+e,m=n[r++]*t+o,s.bezierCurveTo(l,y,x,m,p,u);break}}return{offsetX:i.ha*t,path:s}}export default T;
