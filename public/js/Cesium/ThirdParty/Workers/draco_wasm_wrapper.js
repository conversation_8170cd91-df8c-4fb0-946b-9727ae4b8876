var $jscomp=$jscomp||{};$jscomp.scope={},$jscomp.ASSUME_ES5=!1,$jscomp.ASSUME_NO_NATIVE_MAP=!1,$jscomp.ASSUME_NO_NATIVE_SET=!1,$jscomp.defineProperty=$jscomp.ASSUME_ES5||typeof Object.defineProperties=="function"?Object.defineProperty:function(u,i,f){u!=Array.prototype&&u!=Object.prototype&&(u[i]=f.value)},$jscomp.getGlobal=function(u){return typeof window<"u"&&window===u?u:typeof global<"u"&&global!=null?global:u},$jscomp.global=$jscomp.getGlobal(this),$jscomp.polyfill=function(u,i,f,B){if(i){for(f=$jscomp.global,u=u.split("."),B=0;B<u.length-1;B++){var d=u[B];d in f||(f[d]={}),f=f[d]}u=u[u.length-1],B=f[u],i=i(B),i!=B&&i!=null&&$jscomp.defineProperty(f,u,{configurable:!0,writable:!0,value:i})}},$jscomp.polyfill("Math.imul",function(u){return u||function(i,f){i=Number(i),f=Number(f);var B=i&65535,d=f&65535;return B*d+((i>>>16&65535)*d+B*(f>>>16&65535)<<16>>>0)|0}},"es6","es3"),$jscomp.polyfill("Math.clz32",function(u){return u||function(i){if(i=Number(i)>>>0,i===0)return 32;var f=0;return!(i&4294901760)&&(i<<=16,f+=16),!(i&4278190080)&&(i<<=8,f+=8),!(i&4026531840)&&(i<<=4,f+=4),!(i&3221225472)&&(i<<=2,f+=2),!(i&2147483648)&&f++,f}},"es6","es3"),$jscomp.polyfill("Math.trunc",function(u){return u||function(i){if(i=Number(i),isNaN(i)||i===1/0||i===-1/0||i===0)return i;var f=Math.floor(Math.abs(i));return 0>i?-f:f}},"es6","es3"),$jscomp.SYMBOL_PREFIX="jscomp_symbol_",$jscomp.initSymbol=function(){$jscomp.initSymbol=function(){},$jscomp.global.Symbol||($jscomp.global.Symbol=$jscomp.Symbol)},$jscomp.symbolCounter_=0,$jscomp.Symbol=function(u){return $jscomp.SYMBOL_PREFIX+(u||"")+$jscomp.symbolCounter_++},$jscomp.initSymbolIterator=function(){$jscomp.initSymbol();var u=$jscomp.global.Symbol.iterator;u||(u=$jscomp.global.Symbol.iterator=$jscomp.global.Symbol("iterator")),typeof Array.prototype[u]!="function"&&$jscomp.defineProperty(Array.prototype,u,{configurable:!0,writable:!0,value:function(){return $jscomp.arrayIterator(this)}}),$jscomp.initSymbolIterator=function(){}},$jscomp.arrayIterator=function(u){var i=0;return $jscomp.iteratorPrototype(function(){return i<u.length?{done:!1,value:u[i++]}:{done:!0}})},$jscomp.iteratorPrototype=function(u){return $jscomp.initSymbolIterator(),u={next:u},u[$jscomp.global.Symbol.iterator]=function(){return this},u},$jscomp.makeIterator=function(u){$jscomp.initSymbolIterator();var i=u[Symbol.iterator];return i?i.call(u):$jscomp.arrayIterator(u)},$jscomp.FORCE_POLYFILL_PROMISE=!1,$jscomp.polyfill("Promise",function(u){function i(){this.batch_=null}function f(_){return _ instanceof d?_:new d(function(p,b){p(_)})}if(u&&!$jscomp.FORCE_POLYFILL_PROMISE)return u;i.prototype.asyncExecute=function(_){return this.batch_==null&&(this.batch_=[],this.asyncExecuteBatch_()),this.batch_.push(_),this},i.prototype.asyncExecuteBatch_=function(){var _=this;this.asyncExecuteFunction(function(){_.executeBatch_()})};var B=$jscomp.global.setTimeout;i.prototype.asyncExecuteFunction=function(_){B(_,0)},i.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var _=this.batch_;this.batch_=[];for(var p=0;p<_.length;++p){var b=_[p];delete _[p];try{b()}catch(v){this.asyncThrow_(v)}}}this.batch_=null},i.prototype.asyncThrow_=function(_){this.asyncExecuteFunction(function(){throw _})};var d=function(_){this.state_=0,this.result_=void 0,this.onSettledCallbacks_=[];var p=this.createResolveAndReject_();try{_(p.resolve,p.reject)}catch(b){p.reject(b)}};d.prototype.createResolveAndReject_=function(){function _(v){return function(z){b||(b=!0,v.call(p,z))}}var p=this,b=!1;return{resolve:_(this.resolveTo_),reject:_(this.reject_)}},d.prototype.resolveTo_=function(_){if(_===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(_ instanceof d)this.settleSameAsPromise_(_);else{t:switch(typeof _){case"object":var p=_!=null;break t;case"function":p=!0;break t;default:p=!1}p?this.resolveToNonPromiseObj_(_):this.fulfill_(_)}},d.prototype.resolveToNonPromiseObj_=function(_){var p=void 0;try{p=_.then}catch(b){this.reject_(b);return}typeof p=="function"?this.settleSameAsThenable_(p,_):this.fulfill_(_)},d.prototype.reject_=function(_){this.settle_(2,_)},d.prototype.fulfill_=function(_){this.settle_(1,_)},d.prototype.settle_=function(_,p){if(this.state_!=0)throw Error("Cannot settle("+_+", "+p|"): Promise already settled in state"+this.state_);this.state_=_,this.result_=p,this.executeOnSettledCallbacks_()},d.prototype.executeOnSettledCallbacks_=function(){if(this.onSettledCallbacks_!=null){for(var _=this.onSettledCallbacks_,p=0;p<_.length;++p)_[p].call(),_[p]=null;this.onSettledCallbacks_=null}};var gt=new i;return d.prototype.settleSameAsPromise_=function(_){var p=this.createResolveAndReject_();_.callWhenSettled_(p.resolve,p.reject)},d.prototype.settleSameAsThenable_=function(_,p){var b=this.createResolveAndReject_();try{_.call(p,b.resolve,b.reject)}catch(v){b.reject(v)}},d.prototype.then=function(_,p){function b(N,j){return typeof N=="function"?function(ct){try{v(N(ct))}catch(lt){z(lt)}}:j}var v,z,ot=new d(function(N,j){v=N,z=j});return this.callWhenSettled_(b(_,v),b(p,z)),ot},d.prototype.catch=function(_){return this.then(void 0,_)},d.prototype.callWhenSettled_=function(_,p){function b(){switch(v.state_){case 1:_(v.result_);break;case 2:p(v.result_);break;default:throw Error("Unexpected state: "+v.state_)}}var v=this;this.onSettledCallbacks_==null?gt.asyncExecute(b):this.onSettledCallbacks_.push(function(){gt.asyncExecute(b)})},d.resolve=f,d.reject=function(_){return new d(function(p,b){b(_)})},d.race=function(_){return new d(function(p,b){for(var v=$jscomp.makeIterator(_),z=v.next();!z.done;z=v.next())f(z.value).callWhenSettled_(p,b)})},d.all=function(_){var p=$jscomp.makeIterator(_),b=p.next();return b.done?f([]):new d(function(v,z){function ot(ct){return function(lt){N[ct]=lt,j--,j==0&&v(N)}}var N=[],j=0;do N.push(void 0),j++,f(b.value).callWhenSettled_(ot(N.length-1),z),b=p.next();while(!b.done)})},d},"es6","es3");var DracoDecoderModule=function(u){function i(e,r){e||it("Assertion failed: "+r)}function f(e,r){if(r===0||!e)return"";for(var n=0,o,c=0;o=et[e+c>>0],n|=o,!(o==0&&!r||(c++,r&&c==r)););if(r||(r=c),o="",128>n){for(;0<r;)n=String.fromCharCode.apply(String,et.subarray(e,e+Math.min(r,1024))),o=o?o+n:n,e+=1024,r-=1024;return o}return t.UTF8ToString(e)}function B(e){return e.replace(/__Z[\w\d_]+/g,function(r){return r===r?r:r+" ["+r+"]"})}function d(){t:{var e=Error();if(!e.stack){try{throw Error(0)}catch(r){e=r}if(!e.stack){e="(no stack trace available)";break t}}e=e.stack.toString()}return t.extraStackTrace&&(e+=`
`+t.extraStackTrace()),B(e)}function gt(e,r){return 0<e%r&&(e+=r-e%r),e}function _(){t.HEAP8=dt=new Int8Array(F),t.HEAP16=Ct=new Int16Array(F),t.HEAP32=M=new Int32Array(F),t.HEAPU8=et=new Uint8Array(F),t.HEAPU16=Wt=new Uint16Array(F),t.HEAPU32=qt=new Uint32Array(F),t.HEAPF32=Yt=new Float32Array(F),t.HEAPF64=Xt=new Float64Array(F)}function p(){var e=t.usingWasm?jt:Qt,r=2147483648-e;if(M[mt>>2]>r)return!1;var n=G;for(G=Math.max(G,pe);G<M[mt>>2];)G=536870912>=G?gt(2*G,e):Math.min(gt((3*G+2147483648)/4,e),r);return e=t.reallocBuffer(G),!e||e.byteLength!=G?(G=n,!1):(t.buffer=F=e,_(),!0)}function b(e){for(;0<e.length;){var r=e.shift();if(typeof r=="function")r();else{var n=r.func;typeof n=="number"?r.arg===void 0?t.dynCall_v(n):t.dynCall_vi(n,r.arg):n(r.arg===void 0?null:r.arg)}}}function v(e){ht++,t.monitorRunDependencies&&t.monitorRunDependencies(ht)}function z(e){ht--,t.monitorRunDependencies&&t.monitorRunDependencies(ht),ht==0&&(Lt!==null&&(clearInterval(Lt),Lt=null),Dt&&(e=Dt,Dt=null,e()))}function ot(){return!!ot.uncaught_exception}function N(){var e=P.last;if(!e)return y.setTempRet0(0),0|0;var r=P.infos[e],n=r.type;if(!n)return y.setTempRet0(0),e|0;var o=Array.prototype.slice.call(arguments);t.___cxa_is_pointer_type(n),N.buffer||(N.buffer=ne(4)),M[N.buffer>>2]=e,e=N.buffer;for(var c=0;c<o.length;c++)if(o[c]&&t.___cxa_can_catch(o[c],n,e))return e=M[e>>2],r.adjusted=e,y.setTempRet0(o[c]),e|0;return e=M[e>>2],y.setTempRet0(n),e|0}function j(e,r){D.varargs=r;try{var n=D.get(),o=D.get(),c=D.get();for(e=0,j.buffer||(j.buffers=[null,[],[]],j.printChar=function(st,m){var h=j.buffers[st];if(i(h),m===0||m===10){st=st===1?t.print:t.printErr;t:{for(var U=m=0;h[U];)++U;if(16<U-m&&h.subarray&&Ht)m=Ht.decode(h.subarray(m,U));else for(U="";;){var A=h[m++];if(!A){m=U;break t}if(A&128){var tt=h[m++]&63;if((A&224)==192)U+=String.fromCharCode((A&31)<<6|tt);else{var At=h[m++]&63;if((A&240)==224)A=(A&15)<<12|tt<<6|At;else{var O=h[m++]&63;if((A&248)==240)A=(A&7)<<18|tt<<12|At<<6|O;else{var X=h[m++]&63;if((A&252)==248)A=(A&3)<<24|tt<<18|At<<12|O<<6|X;else{var Gt=h[m++]&63;A=(A&1)<<30|tt<<24|At<<18|O<<12|X<<6|Gt}}}65536>A?U+=String.fromCharCode(A):(A-=65536,U+=String.fromCharCode(55296|A>>10,56320|A&1023))}}else U+=String.fromCharCode(A)}}st(m),h.length=0}else h.push(m)}),r=0;r<c;r++){for(var s=M[o+8*r>>2],S=M[o+(8*r+4)>>2],Z=0;Z<S;Z++)j.printChar(n,et[s+Z]);e+=S}return e}catch(st){return typeof FS<"u"&&st instanceof FS.ErrnoError||it(st),-st.errno}}function ct(e,r){ct.seen||(ct.seen={}),e in ct.seen||(t.dynCall_v(r),ct.seen[e]=1)}function lt(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function Ot(e){function r(){if(!t.calledRun&&(t.calledRun=!0,!Et)){if(ee||(ee=!0,b(Zt)),b(Jt),t.onRuntimeInitialized&&t.onRuntimeInitialized(),t.postRun)for(typeof t.postRun=="function"&&(t.postRun=[t.postRun]);t.postRun.length;)te.unshift(t.postRun.shift());b(te)}}if(ue===null&&(ue=Date.now()),!(0<ht)){if(t.preRun)for(typeof t.preRun=="function"&&(t.preRun=[t.preRun]);t.preRun.length;)Kt.unshift(t.preRun.shift());b(Kt),0<ht||t.calledRun||(t.setStatus?(t.setStatus("Running..."),setTimeout(function(){setTimeout(function(){t.setStatus("")},1),r()},1)):r())}}function it(e){t.onAbort&&t.onAbort(e),e!==void 0?(t.print(e),t.printErr(e),e=JSON.stringify(e)):e="",Et=!0;var r="abort("+e+") at "+d()+`
If this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.`;throw ae&&ae.forEach(function(n){r=n(r,e)}),r}function g(){}function E(e){return(e||g).__cache__}function _t(e,r){var n=E(r),o=n[e];return o||(o=Object.create((r||g).prototype),o.ptr=e,n[e]=o)}function ut(e){if(typeof e=="string"){for(var r=0,n=0;n<e.length;++n){var o=e.charCodeAt(n);55296<=o&&57343>=o&&(o=65536+((o&1023)<<10)|e.charCodeAt(++n)&1023),127>=o?++r:r=2047>=o?r+2:65535>=o?r+3:2097151>=o?r+4:67108863>=o?r+5:r+6}if(r=Array(r+1),n=0,o=r.length,0<o){o=n+o-1;for(var c=0;c<e.length;++c){var s=e.charCodeAt(c);if(55296<=s&&57343>=s&&(s=65536+((s&1023)<<10)|e.charCodeAt(++c)&1023),127>=s){if(n>=o)break;r[n++]=s}else{if(2047>=s){if(n+1>=o)break;r[n++]=192|s>>6}else{if(65535>=s){if(n+2>=o)break;r[n++]=224|s>>12}else{if(2097151>=s){if(n+3>=o)break;r[n++]=240|s>>18}else{if(67108863>=s){if(n+4>=o)break;r[n++]=248|s>>24}else{if(n+5>=o)break;r[n++]=252|s>>30,r[n++]=128|s>>24&63}r[n++]=128|s>>18&63}r[n++]=128|s>>12&63}r[n++]=128|s>>6&63}r[n++]=128|s&63}}r[n]=0}e=l.alloc(r,dt),l.copy(r,dt,e)}return e}function w(){throw"cannot construct a Status, no constructor in IDL"}function L(){this.ptr=yr(),E(L)[this.ptr]=this}function k(){this.ptr=rn(),E(k)[this.ptr]=this}function x(){this.ptr=Tr(),E(x)[this.ptr]=this}function V(){this.ptr=dr(),E(V)[this.ptr]=this}function H(){this.ptr=ce(),E(H)[this.ptr]=this}function T(){this.ptr=Wr(),E(T)[this.ptr]=this}function K(){this.ptr=Te(),E(K)[this.ptr]=this}function R(){this.ptr=fe(),E(R)[this.ptr]=this}function Q(){this.ptr=pr(),E(Q)[this.ptr]=this}function I(){this.ptr=Lr(),E(I)[this.ptr]=this}function W(){this.ptr=er(),E(W)[this.ptr]=this}function q(){this.ptr=Ze(),E(q)[this.ptr]=this}function at(){this.ptr=Dr(),E(at)[this.ptr]=this}function J(){this.ptr=Ee(),E(J)[this.ptr]=this}function a(){this.ptr=Ge(),E(a)[this.ptr]=this}function C(){this.ptr=Rr(),E(C)[this.ptr]=this}function yt(){throw"cannot construct a VoidPtr, no constructor in IDL"}function Y(){this.ptr=ir(),E(Y)[this.ptr]=this}function $(){this.ptr=Vr(),E($)[this.ptr]=this}var t=u=u||{},xt=!1,Vt=!1;t.onRuntimeInitialized=function(){xt=!0,Vt&&typeof t.onModuleLoaded=="function"&&t.onModuleLoaded(t)},t.onModuleParsed=function(){Vt=!0,xt&&typeof t.onModuleLoaded=="function"&&t.onModuleLoaded(t)},t.isVersionSupported=function(e){return typeof e!="string"?!1:(e=e.split("."),2>e.length||3<e.length?!1:e[0]==1&&0<=e[1]&&3>=e[1]?!0:!(e[0]!=0||10<e[1]))},t||(t=(typeof u<"u"?u:null)||{});var vt={},ft;for(ft in t)t.hasOwnProperty(ft)&&(vt[ft]=t[ft]);var Tt=!1,bt=!1,It=!1,St=!1;if(t.ENVIRONMENT)if(t.ENVIRONMENT==="WEB")Tt=!0;else if(t.ENVIRONMENT==="WORKER")bt=!0;else if(t.ENVIRONMENT==="NODE")It=!0;else if(t.ENVIRONMENT==="SHELL")St=!0;else throw Error("The provided Module['ENVIRONMENT'] value is not valid. It must be one of: WEB|WORKER|NODE|SHELL.");else Tt=typeof window=="object",bt=typeof importScripts=="function",It=typeof process=="object"&&typeof require=="function"&&!Tt&&!bt,St=!Tt&&!It&&!bt;if(It){t.print||(t.print=console.log),t.printErr||(t.printErr=console.warn);var wt,Pt;t.read=function(e,r){return wt||(wt=require("fs")),Pt||(Pt=require("path")),e=Pt.normalize(e),e=wt.readFileSync(e),r?e:e.toString()},t.readBinary=function(e){return e=t.read(e,!0),e.buffer||(e=new Uint8Array(e)),i(e.buffer),e},t.thisProgram||(t.thisProgram=1<process.argv.length?process.argv[1].replace(/\\/g,"/"):"unknown-program"),t.arguments=process.argv.slice(2),process.on("uncaughtException",function(e){if(!(e instanceof lt))throw e}),t.inspect=function(){return"[Emscripten Module object]"}}else if(St)t.print||(t.print=print),typeof printErr<"u"&&(t.printErr=printErr),t.read=typeof read<"u"?function(e){return read(e)}:function(){throw"no read() available"},t.readBinary=function(e){return typeof readbuffer=="function"?new Uint8Array(readbuffer(e)):(e=read(e,"binary"),i(typeof e=="object"),e)},typeof scriptArgs<"u"?t.arguments=scriptArgs:typeof arguments<"u"&&(t.arguments=arguments),typeof quit=="function"&&(t.quit=function(e,r){quit(e)});else if(Tt||bt)t.read=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},bt&&(t.readBinary=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),t.readAsync=function(e,r,n){var o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="arraybuffer",o.onload=function(){o.status==200||o.status==0&&o.response?r(o.response):n()},o.onerror=n,o.send(null)},typeof arguments<"u"&&(t.arguments=arguments),typeof console<"u"?(t.print||(t.print=function(e){console.log(e)}),t.printErr||(t.printErr=function(e){console.warn(e)})):t.print||(t.print=function(e){}),typeof t.setWindowTitle>"u"&&(t.setWindowTitle=function(e){document.title=e});else throw Error("Unknown runtime environment. Where are we?");t.print||(t.print=function(){}),t.printErr||(t.printErr=t.print),t.arguments||(t.arguments=[]),t.thisProgram||(t.thisProgram="./this.program"),t.quit||(t.quit=function(e,r){throw r}),t.print=t.print,t.printErr=t.printErr,t.preRun=[],t.postRun=[];for(ft in vt)vt.hasOwnProperty(ft)&&(t[ft]=vt[ft]);vt=void 0;var y={setTempRet0:function(e){return tempRet0=e},getTempRet0:function(){return tempRet0},stackSave:function(){return rt},stackRestore:function(e){rt=e},getNativeTypeSize:function(e){switch(e){case"i1":case"i8":return 1;case"i16":return 2;case"i32":return 4;case"i64":return 8;case"float":return 4;case"double":return 8;default:return e[e.length-1]==="*"?y.QUANTUM_SIZE:e[0]==="i"?(e=parseInt(e.substr(1)),i(e%8===0),e/8):0}},getNativeFieldSize:function(e){return Math.max(y.getNativeTypeSize(e),y.QUANTUM_SIZE)},STACK_ALIGN:16,prepVararg:function(e,r){return r==="double"||r==="i64"?e&7&&(i((e&7)===4),e+=4):i((e&3)===0),e},getAlignSize:function(e,r,n){return n||e!="i64"&&e!="double"?e?Math.min(r||(e?y.getNativeFieldSize(e):0),y.QUANTUM_SIZE):Math.min(r,8):8},dynCall:function(e,r,n){return n&&n.length?t["dynCall_"+e].apply(null,[r].concat(n)):t["dynCall_"+e].call(null,r)},functionPointers:[],addFunction:function(e){for(var r=0;r<y.functionPointers.length;r++)if(!y.functionPointers[r])return y.functionPointers[r]=e,2*(1+r);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."},removeFunction:function(e){y.functionPointers[(e-2)/2]=null},warnOnce:function(e){y.warnOnce.shown||(y.warnOnce.shown={}),y.warnOnce.shown[e]||(y.warnOnce.shown[e]=1,t.printErr(e))},funcWrappers:{},getFuncWrapper:function(e,r){if(e){i(r),y.funcWrappers[r]||(y.funcWrappers[r]={});var n=y.funcWrappers[r];return n[e]||(n[e]=r.length===1?function(){return y.dynCall(r,e)}:r.length===2?function(o){return y.dynCall(r,e,[o])}:function(){return y.dynCall(r,e,Array.prototype.slice.call(arguments))}),n[e]}},getCompilerSetting:function(e){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work"},stackAlloc:function(e){var r=rt;return rt=rt+e|0,rt=rt+15&-16,r},staticAlloc:function(e){var r=pt;return pt=pt+e|0,pt=pt+15&-16,r},dynamicAlloc:function(e){var r=M[mt>>2];return e=(r+e+15|0)&-16,M[mt>>2]=e,e>=G&&!p()?(M[mt>>2]=r,0):r},alignMemory:function(e,r){return Math.ceil(e/(r||16))*(r||16)},makeBigInt:function(e,r,n){return n?+(e>>>0)+4294967296*+(r>>>0):+(e>>>0)+4294967296*+(r|0)},GLOBAL_BASE:1024,QUANTUM_SIZE:4,__dummy__:0},Et=0,Ht=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;typeof TextDecoder<"u"&&new TextDecoder("utf-16le");var jt=65536,Qt=16777216,pe=16777216,dt,et,Ct,Wt,M,qt,Yt,Xt,pt,Nt,rt,Rt,Ft,mt,Ut=pt=Nt=rt=Rt=Ft=mt=0;t.reallocBuffer||(t.reallocBuffer=function(e){try{if(ArrayBuffer.transfer)var r=ArrayBuffer.transfer(F,e);else{var n=dt;r=new ArrayBuffer(e),new Int8Array(r).set(n)}}catch{return!1}return wn(r)?r:!1});try{var Bt=Function.prototype.call.bind(Object.getOwnPropertyDescriptor(ArrayBuffer.prototype,"byteLength").get);Bt(new ArrayBuffer(4))}catch{Bt=function(r){return r.byteLength}}var zt=t.TOTAL_STACK||5242880,G=t.TOTAL_MEMORY||16777216;if(G<zt&&t.printErr("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+G+"! (TOTAL_STACK="+zt+")"),t.buffer)var F=t.buffer;else typeof WebAssembly=="object"&&typeof WebAssembly.Memory=="function"?(t.wasmMemory=new WebAssembly.Memory({initial:G/jt}),F=t.wasmMemory.buffer):F=new ArrayBuffer(G);if(_(),M[0]=1668509029,Ct[1]=25459,et[2]!==115||et[3]!==99)throw"Runtime error: expected the system to be little-endian!";t.HEAP=void 0,t.buffer=F,t.HEAP8=dt,t.HEAP16=Ct,t.HEAP32=M,t.HEAPU8=et,t.HEAPU16=Wt,t.HEAPU32=qt,t.HEAPF32=Yt,t.HEAPF64=Xt;var Kt=[],Zt=[],Jt=[],$t=[],te=[],ee=!1;i(Math.imul&&Math.fround&&Math.clz32&&Math.trunc,"this is a legacy browser, build with LEGACY_VM_SUPPORT");var ht=0,Lt=null,Dt=null;t.preloadedImages={},t.preloadedAudios={};var nt=null;(function(){function e(){try{if(t.wasmBinary)return new Uint8Array(t.wasmBinary);if(t.readBinary)return t.readBinary(o);throw"on the web, we need the wasm binary to be preloaded and set on Module['wasmBinary']. emcc.py will do that for you when generating HTML (but not JS)"}catch(m){it(m)}}function r(){return t.wasmBinary||!Tt&&!bt||typeof fetch!="function"?new Promise(function(m,h){m(e())}):fetch(o,{credentials:"same-origin"}).then(function(m){if(!m.ok)throw"failed to load wasm binary file at '"+o+"'";return m.arrayBuffer()}).catch(function(){return e()})}function n(m,h,U){function A(O,X){if(S=O.exports,S.memory){O=S.memory,X=t.buffer,O.byteLength<X.byteLength&&t.printErr("the new buffer in mergeMemory is smaller than the previous one. in native wasm, we should grow memory here"),X=new Int8Array(X);var Gt=new Int8Array(O);nt||X.set(Gt.subarray(t.STATIC_BASE,t.STATIC_BASE+t.STATIC_BUMP),t.STATIC_BASE),Gt.set(X),t.buffer=F=O,_()}t.asm=S,t.usingWasm=!0,z("wasm-instantiate")}function tt(O){A(O.instance,O.module)}function At(O){r().then(function(X){return WebAssembly.instantiate(X,s)}).then(O).catch(function(X){t.printErr("failed to asynchronously prepare wasm: "+X),it(X)})}if(typeof WebAssembly!="object")return t.printErr("no native wasm support detected"),!1;if(!(t.wasmMemory instanceof WebAssembly.Memory))return t.printErr("no native wasm Memory in use"),!1;if(h.memory=t.wasmMemory,s.global={NaN:NaN,Infinity:1/0},s["global.Math"]=m.Math,s.env=h,v("wasm-instantiate"),t.instantiateWasm)try{return t.instantiateWasm(s,A)}catch(O){return t.printErr("Module.instantiateWasm callback failed with error: "+O),!1}return t.wasmBinary||typeof WebAssembly.instantiateStreaming!="function"||o.indexOf("data:")===0||typeof fetch!="function"?At(tt):WebAssembly.instantiateStreaming(fetch(o,{credentials:"same-origin"}),s).then(tt).catch(function(O){t.printErr("wasm streaming compile failed: "+O),t.printErr("falling back to ArrayBuffer instantiation"),At(tt)}),{}}var o="draco_decoder.wasm",c="draco_decoder.temp.asm.js";typeof t.locateFile=="function"&&(t.locateFile("draco_decoder.wast"),o=t.locateFile(o),c=t.locateFile(c));var s={global:null,env:null,asm2wasm:{"f64-rem":function(m,h){return m%h},debugger:function(){debugger}},parent:t},S=null;t.asmPreload=t.asm;var Z=t.reallocBuffer;t.reallocBuffer=function(m){if(st==="asmjs")var h=Z(m);else t:{m=gt(m,t.usingWasm?jt:Qt);var U=t.buffer.byteLength;if(t.usingWasm)try{h=t.wasmMemory.grow((m-U)/65536)!==-1?t.buffer=t.wasmMemory.buffer:null;break t}catch{h=null;break t}h=void 0}return h};var st="";t.asm=function(m,h,U){if(!h.table){var A=t.wasmTableSize;A===void 0&&(A=1024);var tt=t.wasmMaxTableSize;h.table=typeof WebAssembly=="object"&&typeof WebAssembly.Table=="function"?tt!==void 0?new WebAssembly.Table({initial:A,maximum:tt,element:"anyfunc"}):new WebAssembly.Table({initial:A,element:"anyfunc"}):Array(A),t.wasmTable=h.table}return h.memoryBase||(h.memoryBase=t.STATIC_BASE),h.tableBase||(h.tableBase=0),(m=n(m,h,U))||it("no binaryen method succeeded. consider enabling more options, like interpreting, if you want that: https://github.com/kripken/emscripten/wiki/WebAssembly#binaryen-methods"),m}})(),Ut=y.GLOBAL_BASE,pt=Ut+19104,Zt.push(),nt=null,t.STATIC_BASE=Ut,t.STATIC_BUMP=19104;var se=pt;pt+=16;var P={last:0,caught:[],infos:{},deAdjust:function(e){if(!e||P.infos[e])return e;for(var r in P.infos)if(P.infos[r].adjusted===e)return r;return e},addRef:function(e){e&&P.infos[e].refcount++},decRef:function(e){if(e){var r=P.infos[e];i(0<r.refcount),r.refcount--,r.refcount!==0||r.rethrown||(r.destructor&&t.dynCall_vi(r.destructor,e),delete P.infos[e],___cxa_free_exception(e))}},clearRef:function(e){e&&(P.infos[e].refcount=0)}},D={varargs:0,get:function(e){return D.varargs+=4,M[D.varargs-4>>2]},getStr:function(){return f(D.get())},get64:function(){var e=D.get(),r=D.get();return 0<=e?i(r===0):i(r===-1),e},getZero:function(){i(D.get()===0)}},Mt={},kt=1;$t.push(function(){var e=t._fflush;if(e&&e(0),e=j.printChar){var r=j.buffers;r[1].length&&e(1,10),r[2].length&&e(2,10)}}),mt=y.staticAlloc(4),Nt=rt=y.alignMemory(pt),Rt=Nt+zt,Ft=y.alignMemory(Rt),M[mt>>2]=Ft,t.wasmTableSize=492,t.wasmMaxTableSize=492,t.asmGlobalArg={Math,Int8Array,Int16Array,Int32Array,Uint8Array,Uint16Array,Uint32Array,Float32Array,Float64Array,NaN:NaN,Infinity:1/0,byteLength:Bt},t.asmLibraryArg={abort:it,assert:i,enlargeMemory:p,getTotalMemory:function(){return G},abortOnCannotGrowMemory:function(){it("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+G+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")},invoke_ii:function(e,r){try{return t.dynCall_ii(e,r)}catch(n){if(typeof n!="number"&&n!=="longjmp")throw n;t.setThrew(1,0)}},invoke_iii:function(e,r,n){try{return t.dynCall_iii(e,r,n)}catch(o){if(typeof o!="number"&&o!=="longjmp")throw o;t.setThrew(1,0)}},invoke_iiii:function(e,r,n,o){try{return t.dynCall_iiii(e,r,n,o)}catch(c){if(typeof c!="number"&&c!=="longjmp")throw c;t.setThrew(1,0)}},invoke_iiiiiii:function(e,r,n,o,c,s,S){try{return t.dynCall_iiiiiii(e,r,n,o,c,s,S)}catch(Z){if(typeof Z!="number"&&Z!=="longjmp")throw Z;t.setThrew(1,0)}},invoke_v:function(e){try{t.dynCall_v(e)}catch(r){if(typeof r!="number"&&r!=="longjmp")throw r;t.setThrew(1,0)}},invoke_vi:function(e,r){try{t.dynCall_vi(e,r)}catch(n){if(typeof n!="number"&&n!=="longjmp")throw n;t.setThrew(1,0)}},invoke_vii:function(e,r,n){try{t.dynCall_vii(e,r,n)}catch(o){if(typeof o!="number"&&o!=="longjmp")throw o;t.setThrew(1,0)}},invoke_viii:function(e,r,n,o){try{t.dynCall_viii(e,r,n,o)}catch(c){if(typeof c!="number"&&c!=="longjmp")throw c;t.setThrew(1,0)}},invoke_viiii:function(e,r,n,o,c){try{t.dynCall_viiii(e,r,n,o,c)}catch(s){if(typeof s!="number"&&s!=="longjmp")throw s;t.setThrew(1,0)}},invoke_viiiii:function(e,r,n,o,c,s){try{t.dynCall_viiiii(e,r,n,o,c,s)}catch(S){if(typeof S!="number"&&S!=="longjmp")throw S;t.setThrew(1,0)}},invoke_viiiiii:function(e,r,n,o,c,s,S){try{t.dynCall_viiiiii(e,r,n,o,c,s,S)}catch(Z){if(typeof Z!="number"&&Z!=="longjmp")throw Z;t.setThrew(1,0)}},__ZSt18uncaught_exceptionv:ot,___cxa_allocate_exception:function(e){return ne(e)},___cxa_begin_catch:function(e){var r=P.infos[e];return r&&!r.caught&&(r.caught=!0,ot.uncaught_exception--),r&&(r.rethrown=!1),P.caught.push(e),P.addRef(P.deAdjust(e)),e},___cxa_find_matching_catch:N,___cxa_pure_virtual:function(){throw Et=!0,"Pure virtual function called!"},___cxa_throw:function(e,r,n){throw P.infos[e]={ptr:e,adjusted:e,type:r,destructor:n,refcount:0,caught:!1,rethrown:!1},P.last=e,"uncaught_exception"in ot?ot.uncaught_exception++:ot.uncaught_exception=1,e+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch."},___gxx_personality_v0:function(){},___resumeException:function(e){throw P.last||(P.last=e),e+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch."},___setErrNo:function(e){return t.___errno_location&&(M[t.___errno_location()>>2]=e),e},___syscall140:function(e,r){D.varargs=r;try{var n=D.getStreamFromFD();D.get();var o=D.get(),c=D.get(),s=D.get();return FS.llseek(n,o,s),M[c>>2]=n.position,n.getdents&&o===0&&s===0&&(n.getdents=null),0}catch(S){return typeof FS<"u"&&S instanceof FS.ErrnoError||it(S),-S.errno}},___syscall146:j,___syscall54:function(e,r){return D.varargs=r,0},___syscall6:function(e,r){D.varargs=r;try{var n=D.getStreamFromFD();return FS.close(n),0}catch(o){return typeof FS<"u"&&o instanceof FS.ErrnoError||it(o),-o.errno}},_abort:function(){t.abort()},_emscripten_memcpy_big:function(e,r,n){return et.set(et.subarray(r,r+n),e),e},_pthread_getspecific:function(e){return Mt[e]||0},_pthread_key_create:function(e,r){return e==0?22:(M[e>>2]=kt,Mt[kt]=0,kt++,0)},_pthread_once:ct,_pthread_setspecific:function(e,r){return e in Mt?(Mt[e]=r,0):22},DYNAMICTOP_PTR:mt,tempDoublePtr:se,ABORT:Et,STACKTOP:rt,STACK_MAX:Rt};var re=t.asm(t.asmGlobalArg,t.asmLibraryArg,F);t.asm=re,t.___cxa_can_catch=function(){return t.asm.___cxa_can_catch.apply(null,arguments)},t.___cxa_is_pointer_type=function(){return t.asm.___cxa_is_pointer_type.apply(null,arguments)};var ce=t._emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0=function(){return t.asm._emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0.apply(null,arguments)},le=t._emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1=function(){return t.asm._emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1.apply(null,arguments)},ye=t._emscripten_bind_AttributeOctahedronTransform___destroy___0=function(){return t.asm._emscripten_bind_AttributeOctahedronTransform___destroy___0.apply(null,arguments)},me=t._emscripten_bind_AttributeOctahedronTransform_quantization_bits_0=function(){return t.asm._emscripten_bind_AttributeOctahedronTransform_quantization_bits_0.apply(null,arguments)},fe=t._emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0=function(){return t.asm._emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0.apply(null,arguments)},be=t._emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1=function(){return t.asm._emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1.apply(null,arguments)},de=t._emscripten_bind_AttributeQuantizationTransform___destroy___0=function(){return t.asm._emscripten_bind_AttributeQuantizationTransform___destroy___0.apply(null,arguments)},he=t._emscripten_bind_AttributeQuantizationTransform_min_value_1=function(){return t.asm._emscripten_bind_AttributeQuantizationTransform_min_value_1.apply(null,arguments)},Ae=t._emscripten_bind_AttributeQuantizationTransform_quantization_bits_0=function(){return t.asm._emscripten_bind_AttributeQuantizationTransform_quantization_bits_0.apply(null,arguments)},ge=t._emscripten_bind_AttributeQuantizationTransform_range_0=function(){return t.asm._emscripten_bind_AttributeQuantizationTransform_range_0.apply(null,arguments)},Te=t._emscripten_bind_AttributeTransformData_AttributeTransformData_0=function(){return t.asm._emscripten_bind_AttributeTransformData_AttributeTransformData_0.apply(null,arguments)},Ie=t._emscripten_bind_AttributeTransformData___destroy___0=function(){return t.asm._emscripten_bind_AttributeTransformData___destroy___0.apply(null,arguments)},ve=t._emscripten_bind_AttributeTransformData_transform_type_0=function(){return t.asm._emscripten_bind_AttributeTransformData_transform_type_0.apply(null,arguments)},Ee=t._emscripten_bind_DecoderBuffer_DecoderBuffer_0=function(){return t.asm._emscripten_bind_DecoderBuffer_DecoderBuffer_0.apply(null,arguments)},De=t._emscripten_bind_DecoderBuffer_Init_2=function(){return t.asm._emscripten_bind_DecoderBuffer_Init_2.apply(null,arguments)},Se=t._emscripten_bind_DecoderBuffer___destroy___0=function(){return t.asm._emscripten_bind_DecoderBuffer___destroy___0.apply(null,arguments)},Re=t._emscripten_bind_Decoder_DecodeBufferToMesh_2=function(){return t.asm._emscripten_bind_Decoder_DecodeBufferToMesh_2.apply(null,arguments)},Me=t._emscripten_bind_Decoder_DecodeBufferToPointCloud_2=function(){return t.asm._emscripten_bind_Decoder_DecodeBufferToPointCloud_2.apply(null,arguments)},Ge=t._emscripten_bind_Decoder_Decoder_0=function(){return t.asm._emscripten_bind_Decoder_Decoder_0.apply(null,arguments)},Oe=t._emscripten_bind_Decoder_GetAttributeByUniqueId_2=function(){return t.asm._emscripten_bind_Decoder_GetAttributeByUniqueId_2.apply(null,arguments)},we=t._emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3=function(){return t.asm._emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3.apply(null,arguments)},Pe=t._emscripten_bind_Decoder_GetAttributeFloat_3=function(){return t.asm._emscripten_bind_Decoder_GetAttributeFloat_3.apply(null,arguments)},je=t._emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3=function(){return t.asm._emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3.apply(null,arguments)},Ce=t._emscripten_bind_Decoder_GetAttributeIdByName_2=function(){return t.asm._emscripten_bind_Decoder_GetAttributeIdByName_2.apply(null,arguments)},Ne=t._emscripten_bind_Decoder_GetAttributeId_2=function(){return t.asm._emscripten_bind_Decoder_GetAttributeId_2.apply(null,arguments)},Fe=t._emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3=function(){return t.asm._emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3.apply(null,arguments)},Ue=t._emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3=function(){return t.asm._emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3.apply(null,arguments)},Be=t._emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3=function(){return t.asm._emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3.apply(null,arguments)},ze=t._emscripten_bind_Decoder_GetAttributeIntForAllPoints_3=function(){return t.asm._emscripten_bind_Decoder_GetAttributeIntForAllPoints_3.apply(null,arguments)},Le=t._emscripten_bind_Decoder_GetAttributeMetadata_2=function(){return t.asm._emscripten_bind_Decoder_GetAttributeMetadata_2.apply(null,arguments)},ke=t._emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3=function(){return t.asm._emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3.apply(null,arguments)},xe=t._emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3=function(){return t.asm._emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3.apply(null,arguments)},Ve=t._emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3=function(){return t.asm._emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3.apply(null,arguments)},He=t._emscripten_bind_Decoder_GetAttribute_2=function(){return t.asm._emscripten_bind_Decoder_GetAttribute_2.apply(null,arguments)},Qe=t._emscripten_bind_Decoder_GetEncodedGeometryType_1=function(){return t.asm._emscripten_bind_Decoder_GetEncodedGeometryType_1.apply(null,arguments)},We=t._emscripten_bind_Decoder_GetFaceFromMesh_3=function(){return t.asm._emscripten_bind_Decoder_GetFaceFromMesh_3.apply(null,arguments)},qe=t._emscripten_bind_Decoder_GetMetadata_1=function(){return t.asm._emscripten_bind_Decoder_GetMetadata_1.apply(null,arguments)},Ye=t._emscripten_bind_Decoder_GetTriangleStripsFromMesh_2=function(){return t.asm._emscripten_bind_Decoder_GetTriangleStripsFromMesh_2.apply(null,arguments)},Xe=t._emscripten_bind_Decoder_SkipAttributeTransform_1=function(){return t.asm._emscripten_bind_Decoder_SkipAttributeTransform_1.apply(null,arguments)},Ke=t._emscripten_bind_Decoder___destroy___0=function(){return t.asm._emscripten_bind_Decoder___destroy___0.apply(null,arguments)},Ze=t._emscripten_bind_DracoFloat32Array_DracoFloat32Array_0=function(){return t.asm._emscripten_bind_DracoFloat32Array_DracoFloat32Array_0.apply(null,arguments)},Je=t._emscripten_bind_DracoFloat32Array_GetValue_1=function(){return t.asm._emscripten_bind_DracoFloat32Array_GetValue_1.apply(null,arguments)},$e=t._emscripten_bind_DracoFloat32Array___destroy___0=function(){return t.asm._emscripten_bind_DracoFloat32Array___destroy___0.apply(null,arguments)},tr=t._emscripten_bind_DracoFloat32Array_size_0=function(){return t.asm._emscripten_bind_DracoFloat32Array_size_0.apply(null,arguments)},er=t._emscripten_bind_DracoInt16Array_DracoInt16Array_0=function(){return t.asm._emscripten_bind_DracoInt16Array_DracoInt16Array_0.apply(null,arguments)},rr=t._emscripten_bind_DracoInt16Array_GetValue_1=function(){return t.asm._emscripten_bind_DracoInt16Array_GetValue_1.apply(null,arguments)},nr=t._emscripten_bind_DracoInt16Array___destroy___0=function(){return t.asm._emscripten_bind_DracoInt16Array___destroy___0.apply(null,arguments)},or=t._emscripten_bind_DracoInt16Array_size_0=function(){return t.asm._emscripten_bind_DracoInt16Array_size_0.apply(null,arguments)},ir=t._emscripten_bind_DracoInt32Array_DracoInt32Array_0=function(){return t.asm._emscripten_bind_DracoInt32Array_DracoInt32Array_0.apply(null,arguments)},_r=t._emscripten_bind_DracoInt32Array_GetValue_1=function(){return t.asm._emscripten_bind_DracoInt32Array_GetValue_1.apply(null,arguments)},ur=t._emscripten_bind_DracoInt32Array___destroy___0=function(){return t.asm._emscripten_bind_DracoInt32Array___destroy___0.apply(null,arguments)},ar=t._emscripten_bind_DracoInt32Array_size_0=function(){return t.asm._emscripten_bind_DracoInt32Array_size_0.apply(null,arguments)},pr=t._emscripten_bind_DracoInt8Array_DracoInt8Array_0=function(){return t.asm._emscripten_bind_DracoInt8Array_DracoInt8Array_0.apply(null,arguments)},sr=t._emscripten_bind_DracoInt8Array_GetValue_1=function(){return t.asm._emscripten_bind_DracoInt8Array_GetValue_1.apply(null,arguments)},cr=t._emscripten_bind_DracoInt8Array___destroy___0=function(){return t.asm._emscripten_bind_DracoInt8Array___destroy___0.apply(null,arguments)},lr=t._emscripten_bind_DracoInt8Array_size_0=function(){return t.asm._emscripten_bind_DracoInt8Array_size_0.apply(null,arguments)},yr=t._emscripten_bind_DracoUInt16Array_DracoUInt16Array_0=function(){return t.asm._emscripten_bind_DracoUInt16Array_DracoUInt16Array_0.apply(null,arguments)},mr=t._emscripten_bind_DracoUInt16Array_GetValue_1=function(){return t.asm._emscripten_bind_DracoUInt16Array_GetValue_1.apply(null,arguments)},fr=t._emscripten_bind_DracoUInt16Array___destroy___0=function(){return t.asm._emscripten_bind_DracoUInt16Array___destroy___0.apply(null,arguments)},br=t._emscripten_bind_DracoUInt16Array_size_0=function(){return t.asm._emscripten_bind_DracoUInt16Array_size_0.apply(null,arguments)},dr=t._emscripten_bind_DracoUInt32Array_DracoUInt32Array_0=function(){return t.asm._emscripten_bind_DracoUInt32Array_DracoUInt32Array_0.apply(null,arguments)},hr=t._emscripten_bind_DracoUInt32Array_GetValue_1=function(){return t.asm._emscripten_bind_DracoUInt32Array_GetValue_1.apply(null,arguments)},Ar=t._emscripten_bind_DracoUInt32Array___destroy___0=function(){return t.asm._emscripten_bind_DracoUInt32Array___destroy___0.apply(null,arguments)},gr=t._emscripten_bind_DracoUInt32Array_size_0=function(){return t.asm._emscripten_bind_DracoUInt32Array_size_0.apply(null,arguments)},Tr=t._emscripten_bind_DracoUInt8Array_DracoUInt8Array_0=function(){return t.asm._emscripten_bind_DracoUInt8Array_DracoUInt8Array_0.apply(null,arguments)},Ir=t._emscripten_bind_DracoUInt8Array_GetValue_1=function(){return t.asm._emscripten_bind_DracoUInt8Array_GetValue_1.apply(null,arguments)},vr=t._emscripten_bind_DracoUInt8Array___destroy___0=function(){return t.asm._emscripten_bind_DracoUInt8Array___destroy___0.apply(null,arguments)},Er=t._emscripten_bind_DracoUInt8Array_size_0=function(){return t.asm._emscripten_bind_DracoUInt8Array_size_0.apply(null,arguments)},Dr=t._emscripten_bind_GeometryAttribute_GeometryAttribute_0=function(){return t.asm._emscripten_bind_GeometryAttribute_GeometryAttribute_0.apply(null,arguments)},Sr=t._emscripten_bind_GeometryAttribute___destroy___0=function(){return t.asm._emscripten_bind_GeometryAttribute___destroy___0.apply(null,arguments)},Rr=t._emscripten_bind_Mesh_Mesh_0=function(){return t.asm._emscripten_bind_Mesh_Mesh_0.apply(null,arguments)},Mr=t._emscripten_bind_Mesh___destroy___0=function(){return t.asm._emscripten_bind_Mesh___destroy___0.apply(null,arguments)},Gr=t._emscripten_bind_Mesh_num_attributes_0=function(){return t.asm._emscripten_bind_Mesh_num_attributes_0.apply(null,arguments)},Or=t._emscripten_bind_Mesh_num_faces_0=function(){return t.asm._emscripten_bind_Mesh_num_faces_0.apply(null,arguments)},wr=t._emscripten_bind_Mesh_num_points_0=function(){return t.asm._emscripten_bind_Mesh_num_points_0.apply(null,arguments)},Pr=t._emscripten_bind_MetadataQuerier_GetDoubleEntry_2=function(){return t.asm._emscripten_bind_MetadataQuerier_GetDoubleEntry_2.apply(null,arguments)},jr=t._emscripten_bind_MetadataQuerier_GetEntryName_2=function(){return t.asm._emscripten_bind_MetadataQuerier_GetEntryName_2.apply(null,arguments)},Cr=t._emscripten_bind_MetadataQuerier_GetIntEntry_2=function(){return t.asm._emscripten_bind_MetadataQuerier_GetIntEntry_2.apply(null,arguments)},Nr=t._emscripten_bind_MetadataQuerier_GetStringEntry_2=function(){return t.asm._emscripten_bind_MetadataQuerier_GetStringEntry_2.apply(null,arguments)},Fr=t._emscripten_bind_MetadataQuerier_HasDoubleEntry_2=function(){return t.asm._emscripten_bind_MetadataQuerier_HasDoubleEntry_2.apply(null,arguments)},Ur=t._emscripten_bind_MetadataQuerier_HasEntry_2=function(){return t.asm._emscripten_bind_MetadataQuerier_HasEntry_2.apply(null,arguments)},Br=t._emscripten_bind_MetadataQuerier_HasIntEntry_2=function(){return t.asm._emscripten_bind_MetadataQuerier_HasIntEntry_2.apply(null,arguments)},zr=t._emscripten_bind_MetadataQuerier_HasStringEntry_2=function(){return t.asm._emscripten_bind_MetadataQuerier_HasStringEntry_2.apply(null,arguments)},Lr=t._emscripten_bind_MetadataQuerier_MetadataQuerier_0=function(){return t.asm._emscripten_bind_MetadataQuerier_MetadataQuerier_0.apply(null,arguments)},kr=t._emscripten_bind_MetadataQuerier_NumEntries_1=function(){return t.asm._emscripten_bind_MetadataQuerier_NumEntries_1.apply(null,arguments)},xr=t._emscripten_bind_MetadataQuerier___destroy___0=function(){return t.asm._emscripten_bind_MetadataQuerier___destroy___0.apply(null,arguments)},Vr=t._emscripten_bind_Metadata_Metadata_0=function(){return t.asm._emscripten_bind_Metadata_Metadata_0.apply(null,arguments)},Hr=t._emscripten_bind_Metadata___destroy___0=function(){return t.asm._emscripten_bind_Metadata___destroy___0.apply(null,arguments)},Qr=t._emscripten_bind_PointAttribute_GetAttributeTransformData_0=function(){return t.asm._emscripten_bind_PointAttribute_GetAttributeTransformData_0.apply(null,arguments)},Wr=t._emscripten_bind_PointAttribute_PointAttribute_0=function(){return t.asm._emscripten_bind_PointAttribute_PointAttribute_0.apply(null,arguments)},qr=t._emscripten_bind_PointAttribute___destroy___0=function(){return t.asm._emscripten_bind_PointAttribute___destroy___0.apply(null,arguments)},Yr=t._emscripten_bind_PointAttribute_attribute_type_0=function(){return t.asm._emscripten_bind_PointAttribute_attribute_type_0.apply(null,arguments)},Xr=t._emscripten_bind_PointAttribute_byte_offset_0=function(){return t.asm._emscripten_bind_PointAttribute_byte_offset_0.apply(null,arguments)},Kr=t._emscripten_bind_PointAttribute_byte_stride_0=function(){return t.asm._emscripten_bind_PointAttribute_byte_stride_0.apply(null,arguments)},Zr=t._emscripten_bind_PointAttribute_data_type_0=function(){return t.asm._emscripten_bind_PointAttribute_data_type_0.apply(null,arguments)},Jr=t._emscripten_bind_PointAttribute_normalized_0=function(){return t.asm._emscripten_bind_PointAttribute_normalized_0.apply(null,arguments)},$r=t._emscripten_bind_PointAttribute_num_components_0=function(){return t.asm._emscripten_bind_PointAttribute_num_components_0.apply(null,arguments)},tn=t._emscripten_bind_PointAttribute_size_0=function(){return t.asm._emscripten_bind_PointAttribute_size_0.apply(null,arguments)},en=t._emscripten_bind_PointAttribute_unique_id_0=function(){return t.asm._emscripten_bind_PointAttribute_unique_id_0.apply(null,arguments)},rn=t._emscripten_bind_PointCloud_PointCloud_0=function(){return t.asm._emscripten_bind_PointCloud_PointCloud_0.apply(null,arguments)},nn=t._emscripten_bind_PointCloud___destroy___0=function(){return t.asm._emscripten_bind_PointCloud___destroy___0.apply(null,arguments)},on=t._emscripten_bind_PointCloud_num_attributes_0=function(){return t.asm._emscripten_bind_PointCloud_num_attributes_0.apply(null,arguments)},_n=t._emscripten_bind_PointCloud_num_points_0=function(){return t.asm._emscripten_bind_PointCloud_num_points_0.apply(null,arguments)},un=t._emscripten_bind_Status___destroy___0=function(){return t.asm._emscripten_bind_Status___destroy___0.apply(null,arguments)},an=t._emscripten_bind_Status_code_0=function(){return t.asm._emscripten_bind_Status_code_0.apply(null,arguments)},pn=t._emscripten_bind_Status_error_msg_0=function(){return t.asm._emscripten_bind_Status_error_msg_0.apply(null,arguments)},sn=t._emscripten_bind_Status_ok_0=function(){return t.asm._emscripten_bind_Status_ok_0.apply(null,arguments)},cn=t._emscripten_bind_VoidPtr___destroy___0=function(){return t.asm._emscripten_bind_VoidPtr___destroy___0.apply(null,arguments)},ln=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM=function(){return t.asm._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM.apply(null,arguments)},yn=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM=function(){return t.asm._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM.apply(null,arguments)},mn=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM=function(){return t.asm._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM.apply(null,arguments)},fn=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM=function(){return t.asm._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM.apply(null,arguments)},bn=t._emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE=function(){return t.asm._emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE.apply(null,arguments)},dn=t._emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD=function(){return t.asm._emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD.apply(null,arguments)},hn=t._emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH=function(){return t.asm._emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH.apply(null,arguments)},An=t._emscripten_enum_draco_GeometryAttribute_Type_COLOR=function(){return t.asm._emscripten_enum_draco_GeometryAttribute_Type_COLOR.apply(null,arguments)},gn=t._emscripten_enum_draco_GeometryAttribute_Type_GENERIC=function(){return t.asm._emscripten_enum_draco_GeometryAttribute_Type_GENERIC.apply(null,arguments)},Tn=t._emscripten_enum_draco_GeometryAttribute_Type_INVALID=function(){return t.asm._emscripten_enum_draco_GeometryAttribute_Type_INVALID.apply(null,arguments)},In=t._emscripten_enum_draco_GeometryAttribute_Type_NORMAL=function(){return t.asm._emscripten_enum_draco_GeometryAttribute_Type_NORMAL.apply(null,arguments)},vn=t._emscripten_enum_draco_GeometryAttribute_Type_POSITION=function(){return t.asm._emscripten_enum_draco_GeometryAttribute_Type_POSITION.apply(null,arguments)},En=t._emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD=function(){return t.asm._emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD.apply(null,arguments)},Dn=t._emscripten_enum_draco_StatusCode_ERROR=function(){return t.asm._emscripten_enum_draco_StatusCode_ERROR.apply(null,arguments)},Sn=t._emscripten_enum_draco_StatusCode_INVALID_PARAMETER=function(){return t.asm._emscripten_enum_draco_StatusCode_INVALID_PARAMETER.apply(null,arguments)},Rn=t._emscripten_enum_draco_StatusCode_IO_ERROR=function(){return t.asm._emscripten_enum_draco_StatusCode_IO_ERROR.apply(null,arguments)},Mn=t._emscripten_enum_draco_StatusCode_OK=function(){return t.asm._emscripten_enum_draco_StatusCode_OK.apply(null,arguments)},Gn=t._emscripten_enum_draco_StatusCode_UNKNOWN_VERSION=function(){return t.asm._emscripten_enum_draco_StatusCode_UNKNOWN_VERSION.apply(null,arguments)},On=t._emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION=function(){return t.asm._emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION.apply(null,arguments)};t._emscripten_get_global_libc=function(){return t.asm._emscripten_get_global_libc.apply(null,arguments)};var wn=t._emscripten_replace_memory=function(){return t.asm._emscripten_replace_memory.apply(null,arguments)};t._free=function(){return t.asm._free.apply(null,arguments)},t._llvm_bswap_i32=function(){return t.asm._llvm_bswap_i32.apply(null,arguments)};var ne=t._malloc=function(){return t.asm._malloc.apply(null,arguments)};if(t._memcpy=function(){return t.asm._memcpy.apply(null,arguments)},t._memmove=function(){return t.asm._memmove.apply(null,arguments)},t._memset=function(){return t.asm._memset.apply(null,arguments)},t._sbrk=function(){return t.asm._sbrk.apply(null,arguments)},t.establishStackSpace=function(){return t.asm.establishStackSpace.apply(null,arguments)},t.getTempRet0=function(){return t.asm.getTempRet0.apply(null,arguments)},t.runPostSets=function(){return t.asm.runPostSets.apply(null,arguments)},t.setTempRet0=function(){return t.asm.setTempRet0.apply(null,arguments)},t.setThrew=function(){return t.asm.setThrew.apply(null,arguments)},t.stackAlloc=function(){return t.asm.stackAlloc.apply(null,arguments)},t.stackRestore=function(){return t.asm.stackRestore.apply(null,arguments)},t.stackSave=function(){return t.asm.stackSave.apply(null,arguments)},t.dynCall_ii=function(){return t.asm.dynCall_ii.apply(null,arguments)},t.dynCall_iii=function(){return t.asm.dynCall_iii.apply(null,arguments)},t.dynCall_iiii=function(){return t.asm.dynCall_iiii.apply(null,arguments)},t.dynCall_iiiiiii=function(){return t.asm.dynCall_iiiiiii.apply(null,arguments)},t.dynCall_v=function(){return t.asm.dynCall_v.apply(null,arguments)},t.dynCall_vi=function(){return t.asm.dynCall_vi.apply(null,arguments)},t.dynCall_vii=function(){return t.asm.dynCall_vii.apply(null,arguments)},t.dynCall_viii=function(){return t.asm.dynCall_viii.apply(null,arguments)},t.dynCall_viiii=function(){return t.asm.dynCall_viiii.apply(null,arguments)},t.dynCall_viiiii=function(){return t.asm.dynCall_viiiii.apply(null,arguments)},t.dynCall_viiiiii=function(){return t.asm.dynCall_viiiiii.apply(null,arguments)},y.stackAlloc=t.stackAlloc,y.stackSave=t.stackSave,y.stackRestore=t.stackRestore,y.establishStackSpace=t.establishStackSpace,y.setTempRet0=t.setTempRet0,y.getTempRet0=t.getTempRet0,t.asm=re,nt)if(typeof t.locateFile=="function"?nt=t.locateFile(nt):t.memoryInitializerPrefixURL&&(nt=t.memoryInitializerPrefixURL+nt),It||St){var Pn=t.readBinary(nt);et.set(Pn,y.GLOBAL_BASE)}else{var oe=function(){t.readAsync(nt,ie,function(){throw"could not load memory initializer "+nt})};v("memory initializer");var ie=function(e){e.byteLength&&(e=new Uint8Array(e)),et.set(e,y.GLOBAL_BASE),t.memoryInitializerRequest&&delete t.memoryInitializerRequest.response,z("memory initializer")};if(t.memoryInitializerRequest){var _e=function(){var e=t.memoryInitializerRequest,r=e.response;e.status!==200&&e.status!==0?(console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+e.status+", retrying "+nt),oe()):ie(r)};t.memoryInitializerRequest.response?setTimeout(_e,0):t.memoryInitializerRequest.addEventListener("load",_e)}else oe()}t.then=function(e){if(t.calledRun)e(t);else{var r=t.onRuntimeInitialized;t.onRuntimeInitialized=function(){r&&r(),e(t)}}return t},lt.prototype=Error(),lt.prototype.constructor=lt;var ue=null;Dt=function e(){t.calledRun||Ot(),t.calledRun||(Dt=e)},t.run=Ot,t.exit=function(e,r){(!r||!t.noExitRuntime)&&(!t.noExitRuntime&&(Et=!0,rt=void 0,b($t),t.onExit)&&t.onExit(e),It&&process.exit(e),t.quit(e,new lt(e)))};var ae=[];if(t.abort=it,t.preInit)for(typeof t.preInit=="function"&&(t.preInit=[t.preInit]);0<t.preInit.length;)t.preInit.pop()();Ot(),g.prototype=Object.create(g.prototype),g.prototype.constructor=g,g.prototype.__class__=g,g.__cache__={},t.WrapperObject=g,t.getCache=E,t.wrapPointer=_t,t.castObject=function(e,r){return _t(e.ptr,r)},t.NULL=_t(0),t.destroy=function(e){if(!e.__destroy__)throw"Error: Cannot destroy object. (Did you create it yourself?)";e.__destroy__(),delete E(e.__class__)[e.ptr]},t.compare=function(e,r){return e.ptr===r.ptr},t.getPointer=function(e){return e.ptr},t.getClass=function(e){return e.__class__};var l={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(l.needed){for(var e=0;e<l.temps.length;e++)t._free(l.temps[e]);l.temps.length=0,t._free(l.buffer),l.buffer=0,l.size+=l.needed,l.needed=0}l.buffer||(l.size+=128,l.buffer=t._malloc(l.size),i(l.buffer)),l.pos=0},alloc:function(e,r){return i(l.buffer),e=e.length*r.BYTES_PER_ELEMENT,e=e+7&-8,l.pos+e>=l.size?(i(0<e),l.needed+=e,r=t._malloc(e),l.temps.push(r)):(r=l.buffer+l.pos,l.pos+=e),r},copy:function(e,r,n){switch(r.BYTES_PER_ELEMENT){case 2:n>>=1;break;case 4:n>>=2;break;case 8:n>>=3}for(var o=0;o<e.length;o++)r[n+o]=e[o]}};return w.prototype=Object.create(g.prototype),w.prototype.constructor=w,w.prototype.__class__=w,w.__cache__={},t.Status=w,w.prototype.code=w.prototype.code=function(){return an(this.ptr)},w.prototype.ok=w.prototype.ok=function(){return!!sn(this.ptr)},w.prototype.error_msg=w.prototype.error_msg=function(){return f(pn(this.ptr))},w.prototype.__destroy__=w.prototype.__destroy__=function(){un(this.ptr)},L.prototype=Object.create(g.prototype),L.prototype.constructor=L,L.prototype.__class__=L,L.__cache__={},t.DracoUInt16Array=L,L.prototype.GetValue=L.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),mr(r,e)},L.prototype.size=L.prototype.size=function(){return br(this.ptr)},L.prototype.__destroy__=L.prototype.__destroy__=function(){fr(this.ptr)},k.prototype=Object.create(g.prototype),k.prototype.constructor=k,k.prototype.__class__=k,k.__cache__={},t.PointCloud=k,k.prototype.num_attributes=k.prototype.num_attributes=function(){return on(this.ptr)},k.prototype.num_points=k.prototype.num_points=function(){return _n(this.ptr)},k.prototype.__destroy__=k.prototype.__destroy__=function(){nn(this.ptr)},x.prototype=Object.create(g.prototype),x.prototype.constructor=x,x.prototype.__class__=x,x.__cache__={},t.DracoUInt8Array=x,x.prototype.GetValue=x.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),Ir(r,e)},x.prototype.size=x.prototype.size=function(){return Er(this.ptr)},x.prototype.__destroy__=x.prototype.__destroy__=function(){vr(this.ptr)},V.prototype=Object.create(g.prototype),V.prototype.constructor=V,V.prototype.__class__=V,V.__cache__={},t.DracoUInt32Array=V,V.prototype.GetValue=V.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),hr(r,e)},V.prototype.size=V.prototype.size=function(){return gr(this.ptr)},V.prototype.__destroy__=V.prototype.__destroy__=function(){Ar(this.ptr)},H.prototype=Object.create(g.prototype),H.prototype.constructor=H,H.prototype.__class__=H,H.__cache__={},t.AttributeOctahedronTransform=H,H.prototype.InitFromAttribute=H.prototype.InitFromAttribute=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),!!le(r,e)},H.prototype.quantization_bits=H.prototype.quantization_bits=function(){return me(this.ptr)},H.prototype.__destroy__=H.prototype.__destroy__=function(){ye(this.ptr)},T.prototype=Object.create(g.prototype),T.prototype.constructor=T,T.prototype.__class__=T,T.__cache__={},t.PointAttribute=T,T.prototype.size=T.prototype.size=function(){return tn(this.ptr)},T.prototype.GetAttributeTransformData=T.prototype.GetAttributeTransformData=function(){return _t(Qr(this.ptr),K)},T.prototype.attribute_type=T.prototype.attribute_type=function(){return Yr(this.ptr)},T.prototype.data_type=T.prototype.data_type=function(){return Zr(this.ptr)},T.prototype.num_components=T.prototype.num_components=function(){return $r(this.ptr)},T.prototype.normalized=T.prototype.normalized=function(){return!!Jr(this.ptr)},T.prototype.byte_stride=T.prototype.byte_stride=function(){return Kr(this.ptr)},T.prototype.byte_offset=T.prototype.byte_offset=function(){return Xr(this.ptr)},T.prototype.unique_id=T.prototype.unique_id=function(){return en(this.ptr)},T.prototype.__destroy__=T.prototype.__destroy__=function(){qr(this.ptr)},K.prototype=Object.create(g.prototype),K.prototype.constructor=K,K.prototype.__class__=K,K.__cache__={},t.AttributeTransformData=K,K.prototype.transform_type=K.prototype.transform_type=function(){return ve(this.ptr)},K.prototype.__destroy__=K.prototype.__destroy__=function(){Ie(this.ptr)},R.prototype=Object.create(g.prototype),R.prototype.constructor=R,R.prototype.__class__=R,R.__cache__={},t.AttributeQuantizationTransform=R,R.prototype.InitFromAttribute=R.prototype.InitFromAttribute=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),!!be(r,e)},R.prototype.quantization_bits=R.prototype.quantization_bits=function(){return Ae(this.ptr)},R.prototype.min_value=R.prototype.min_value=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),he(r,e)},R.prototype.range=R.prototype.range=function(){return ge(this.ptr)},R.prototype.__destroy__=R.prototype.__destroy__=function(){de(this.ptr)},Q.prototype=Object.create(g.prototype),Q.prototype.constructor=Q,Q.prototype.__class__=Q,Q.__cache__={},t.DracoInt8Array=Q,Q.prototype.GetValue=Q.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),sr(r,e)},Q.prototype.size=Q.prototype.size=function(){return lr(this.ptr)},Q.prototype.__destroy__=Q.prototype.__destroy__=function(){cr(this.ptr)},I.prototype=Object.create(g.prototype),I.prototype.constructor=I,I.prototype.__class__=I,I.__cache__={},t.MetadataQuerier=I,I.prototype.HasEntry=I.prototype.HasEntry=function(e,r){var n=this.ptr;return l.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:ut(r),!!Ur(n,e,r)},I.prototype.HasIntEntry=I.prototype.HasIntEntry=function(e,r){var n=this.ptr;return l.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:ut(r),!!Br(n,e,r)},I.prototype.GetIntEntry=I.prototype.GetIntEntry=function(e,r){var n=this.ptr;return l.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:ut(r),Cr(n,e,r)},I.prototype.HasDoubleEntry=I.prototype.HasDoubleEntry=function(e,r){var n=this.ptr;return l.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:ut(r),!!Fr(n,e,r)},I.prototype.GetDoubleEntry=I.prototype.GetDoubleEntry=function(e,r){var n=this.ptr;return l.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:ut(r),Pr(n,e,r)},I.prototype.HasStringEntry=I.prototype.HasStringEntry=function(e,r){var n=this.ptr;return l.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:ut(r),!!zr(n,e,r)},I.prototype.GetStringEntry=I.prototype.GetStringEntry=function(e,r){var n=this.ptr;return l.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:ut(r),f(Nr(n,e,r))},I.prototype.NumEntries=I.prototype.NumEntries=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),kr(r,e)},I.prototype.GetEntryName=I.prototype.GetEntryName=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),f(jr(n,e,r))},I.prototype.__destroy__=I.prototype.__destroy__=function(){xr(this.ptr)},W.prototype=Object.create(g.prototype),W.prototype.constructor=W,W.prototype.__class__=W,W.__cache__={},t.DracoInt16Array=W,W.prototype.GetValue=W.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),rr(r,e)},W.prototype.size=W.prototype.size=function(){return or(this.ptr)},W.prototype.__destroy__=W.prototype.__destroy__=function(){nr(this.ptr)},q.prototype=Object.create(g.prototype),q.prototype.constructor=q,q.prototype.__class__=q,q.__cache__={},t.DracoFloat32Array=q,q.prototype.GetValue=q.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),Je(r,e)},q.prototype.size=q.prototype.size=function(){return tr(this.ptr)},q.prototype.__destroy__=q.prototype.__destroy__=function(){$e(this.ptr)},at.prototype=Object.create(g.prototype),at.prototype.constructor=at,at.prototype.__class__=at,at.__cache__={},t.GeometryAttribute=at,at.prototype.__destroy__=at.prototype.__destroy__=function(){Sr(this.ptr)},J.prototype=Object.create(g.prototype),J.prototype.constructor=J,J.prototype.__class__=J,J.__cache__={},t.DecoderBuffer=J,J.prototype.Init=J.prototype.Init=function(e,r){var n=this.ptr;if(l.prepare(),typeof e=="object"&&typeof e=="object"){var o=l.alloc(e,dt);l.copy(e,dt,o),e=o}r&&typeof r=="object"&&(r=r.ptr),De(n,e,r)},J.prototype.__destroy__=J.prototype.__destroy__=function(){Se(this.ptr)},a.prototype=Object.create(g.prototype),a.prototype.constructor=a,a.prototype.__class__=a,a.__cache__={},t.Decoder=a,a.prototype.GetEncodedGeometryType=a.prototype.GetEncodedGeometryType=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),Qe(r,e)},a.prototype.DecodeBufferToPointCloud=a.prototype.DecodeBufferToPointCloud=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),_t(Me(n,e,r),w)},a.prototype.DecodeBufferToMesh=a.prototype.DecodeBufferToMesh=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),_t(Re(n,e,r),w)},a.prototype.GetAttributeId=a.prototype.GetAttributeId=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),Ne(n,e,r)},a.prototype.GetAttributeIdByName=a.prototype.GetAttributeIdByName=function(e,r){var n=this.ptr;return l.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:ut(r),Ce(n,e,r)},a.prototype.GetAttributeIdByMetadataEntry=a.prototype.GetAttributeIdByMetadataEntry=function(e,r,n){var o=this.ptr;return l.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:ut(r),n=n&&typeof n=="object"?n.ptr:ut(n),je(o,e,r,n)},a.prototype.GetAttribute=a.prototype.GetAttribute=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),_t(He(n,e,r),T)},a.prototype.GetAttributeByUniqueId=a.prototype.GetAttributeByUniqueId=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),_t(Oe(n,e,r),T)},a.prototype.GetMetadata=a.prototype.GetMetadata=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),_t(qe(r,e),$)},a.prototype.GetAttributeMetadata=a.prototype.GetAttributeMetadata=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),_t(Le(n,e,r),$)},a.prototype.GetFaceFromMesh=a.prototype.GetFaceFromMesh=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!We(o,e,r,n)},a.prototype.GetTriangleStripsFromMesh=a.prototype.GetTriangleStripsFromMesh=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),Ye(n,e,r)},a.prototype.GetAttributeFloat=a.prototype.GetAttributeFloat=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Pe(o,e,r,n)},a.prototype.GetAttributeFloatForAllPoints=a.prototype.GetAttributeFloatForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!we(o,e,r,n)},a.prototype.GetAttributeIntForAllPoints=a.prototype.GetAttributeIntForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!ze(o,e,r,n)},a.prototype.GetAttributeInt8ForAllPoints=a.prototype.GetAttributeInt8ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Be(o,e,r,n)},a.prototype.GetAttributeUInt8ForAllPoints=a.prototype.GetAttributeUInt8ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Ve(o,e,r,n)},a.prototype.GetAttributeInt16ForAllPoints=a.prototype.GetAttributeInt16ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Fe(o,e,r,n)},a.prototype.GetAttributeUInt16ForAllPoints=a.prototype.GetAttributeUInt16ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!ke(o,e,r,n)},a.prototype.GetAttributeInt32ForAllPoints=a.prototype.GetAttributeInt32ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Ue(o,e,r,n)},a.prototype.GetAttributeUInt32ForAllPoints=a.prototype.GetAttributeUInt32ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!xe(o,e,r,n)},a.prototype.SkipAttributeTransform=a.prototype.SkipAttributeTransform=function(e){var r=this.ptr;e&&typeof e=="object"&&(e=e.ptr),Xe(r,e)},a.prototype.__destroy__=a.prototype.__destroy__=function(){Ke(this.ptr)},C.prototype=Object.create(g.prototype),C.prototype.constructor=C,C.prototype.__class__=C,C.__cache__={},t.Mesh=C,C.prototype.num_faces=C.prototype.num_faces=function(){return Or(this.ptr)},C.prototype.num_attributes=C.prototype.num_attributes=function(){return Gr(this.ptr)},C.prototype.num_points=C.prototype.num_points=function(){return wr(this.ptr)},C.prototype.__destroy__=C.prototype.__destroy__=function(){Mr(this.ptr)},yt.prototype=Object.create(g.prototype),yt.prototype.constructor=yt,yt.prototype.__class__=yt,yt.__cache__={},t.VoidPtr=yt,yt.prototype.__destroy__=yt.prototype.__destroy__=function(){cn(this.ptr)},Y.prototype=Object.create(g.prototype),Y.prototype.constructor=Y,Y.prototype.__class__=Y,Y.__cache__={},t.DracoInt32Array=Y,Y.prototype.GetValue=Y.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),_r(r,e)},Y.prototype.size=Y.prototype.size=function(){return ar(this.ptr)},Y.prototype.__destroy__=Y.prototype.__destroy__=function(){ur(this.ptr)},$.prototype=Object.create(g.prototype),$.prototype.constructor=$,$.prototype.__class__=$,$.__cache__={},t.Metadata=$,$.prototype.__destroy__=$.prototype.__destroy__=function(){Hr(this.ptr)},function(){function e(){t.OK=Mn(),t.ERROR=Dn(),t.IO_ERROR=Rn(),t.INVALID_PARAMETER=Sn(),t.UNSUPPORTED_VERSION=On(),t.UNKNOWN_VERSION=Gn(),t.INVALID_GEOMETRY_TYPE=bn(),t.POINT_CLOUD=dn(),t.TRIANGULAR_MESH=hn(),t.ATTRIBUTE_INVALID_TRANSFORM=ln(),t.ATTRIBUTE_NO_TRANSFORM=yn(),t.ATTRIBUTE_QUANTIZATION_TRANSFORM=fn(),t.ATTRIBUTE_OCTAHEDRON_TRANSFORM=mn(),t.INVALID=Tn(),t.POSITION=vn(),t.NORMAL=In(),t.COLOR=An(),t.TEX_COORD=En(),t.GENERIC=gn()}t.calledRun?e():Jt.unshift(e)}(),typeof t.onModuleParsed=="function"&&t.onModuleParsed(),u};typeof module=="object"&&module.exports&&(module.exports=DracoDecoderModule);
