/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.105.2
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

var CesiumEMVBExtensions=(()=>{var Ti=Object.create;var st=Object.defineProperty;var Mi=Object.getOwnPropertyDescriptor;var Li=Object.getOwnPropertyNames;var _i=Object.getPrototypeOf,Di=Object.prototype.hasOwnProperty;var aA=(A,e)=>()=>(e||A((e={exports:{}}).exports,e),e.exports),Vi=(A,e)=>{for(var t in e)st(A,t,{get:e[t],enumerable:!0})},On=(A,e,t,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of Li(e))!Di.call(A,n)&&n!==t&&st(A,n,{get:()=>e[n],enumerable:!(r=Mi(e,n))||r.enumerable});return A};var Sn=(A,e,t)=>(t=A!=null?Ti(_i(A)):{},On(e||!A||!A.__esModule?st(t,"default",{value:A,enumerable:!0}):t,A)),Pi=A=>On(st({},"__esModule",{value:!0}),A);var Qr=aA(FA=>{"use strict";var Tn=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",Gi=Tn+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040",Mn="["+Tn+"]["+Gi+"]*",Ji=new RegExp("^"+Mn+"$"),Wi=function(A,e){let t=[],r=e.exec(A);for(;r;){let n=[];n.startIndex=e.lastIndex-r[0].length;let s=r.length;for(let o=0;o<s;o++)n.push(r[o]);t.push(n),r=e.exec(A)}return t},ki=function(A){let e=Ji.exec(A);return!(e===null||typeof e>"u")};FA.isExist=function(A){return typeof A<"u"};FA.isEmptyObject=function(A){return Object.keys(A).length===0};FA.merge=function(A,e,t){if(e){let r=Object.keys(e),n=r.length;for(let s=0;s<n;s++)t==="strict"?A[r[s]]=[e[r[s]]]:A[r[s]]=e[r[s]]}};FA.getValue=function(A){return FA.isExist(A)?A:""};FA.isName=ki;FA.getAllMatches=Wi;FA.nameRegexp=Mn});var wr=aA(Pn=>{"use strict";var gr=Qr(),qi={allowBooleanAttributes:!1,unpairedTags:[]};Pn.validate=function(A,e){e=Object.assign({},qi,e);let t=[],r=!1,n=!1;A[0]==="\uFEFF"&&(A=A.substr(1));for(let s=0;s<A.length;s++)if(A[s]==="<"&&A[s+1]==="?"){if(s+=2,s=_n(A,s),s.err)return s}else if(A[s]==="<"){let o=s;if(s++,A[s]==="!"){s=Dn(A,s);continue}else{let c=!1;A[s]==="/"&&(c=!0,s++);let i="";for(;s<A.length&&A[s]!==">"&&A[s]!==" "&&A[s]!=="	"&&A[s]!==`
`&&A[s]!=="\r";s++)i+=A[s];if(i=i.trim(),i[i.length-1]==="/"&&(i=i.substring(0,i.length-1),s--),!rc(i)){let f;return i.trim().length===0?f="Invalid space after '<'.":f="Tag '"+i+"' is an invalid name.",z("InvalidTag",f,nA(A,s))}let a=Zi(A,s);if(a===!1)return z("InvalidAttr","Attributes for '"+i+"' have open quote.",nA(A,s));let B=a.value;if(s=a.index,B[B.length-1]==="/"){let f=s-B.length;B=B.substring(0,B.length-1);let d=Vn(B,e);if(d===!0)r=!0;else return z(d.err.code,d.err.msg,nA(A,f+d.err.line))}else if(c)if(a.tagClosed){if(B.trim().length>0)return z("InvalidTag","Closing tag '"+i+"' can't have attributes or invalid starting.",nA(A,o));{let f=t.pop();if(i!==f.tagName){let d=nA(A,f.tagStartPos);return z("InvalidTag","Expected closing tag '"+f.tagName+"' (opened in line "+d.line+", col "+d.col+") instead of closing tag '"+i+"'.",nA(A,o))}t.length==0&&(n=!0)}}else return z("InvalidTag","Closing tag '"+i+"' doesn't have proper closing.",nA(A,s));else{let f=Vn(B,e);if(f!==!0)return z(f.err.code,f.err.msg,nA(A,s-B.length+f.err.line));if(n===!0)return z("InvalidXml","Multiple possible root nodes found.",nA(A,s));e.unpairedTags.indexOf(i)!==-1||t.push({tagName:i,tagStartPos:o}),r=!0}for(s++;s<A.length;s++)if(A[s]==="<")if(A[s+1]==="!"){s++,s=Dn(A,s);continue}else if(A[s+1]==="?"){if(s=_n(A,++s),s.err)return s}else break;else if(A[s]==="&"){let f=ec(A,s);if(f==-1)return z("InvalidChar","char '&' is not expected.",nA(A,s));s=f}else if(n===!0&&!Ln(A[s]))return z("InvalidXml","Extra text at the end",nA(A,s));A[s]==="<"&&s--}}else{if(Ln(A[s]))continue;return z("InvalidChar","char '"+A[s]+"' is not expected.",nA(A,s))}if(r){if(t.length==1)return z("InvalidTag","Unclosed tag '"+t[0].tagName+"'.",nA(A,t[0].tagStartPos));if(t.length>0)return z("InvalidXml","Invalid '"+JSON.stringify(t.map(s=>s.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1})}else return z("InvalidXml","Start tag expected.",1);return!0};function Ln(A){return A===" "||A==="	"||A===`
`||A==="\r"}function _n(A,e){let t=e;for(;e<A.length;e++)if(A[e]=="?"||A[e]==" "){let r=A.substr(t,e-t);if(e>5&&r==="xml")return z("InvalidXml","XML declaration allowed only at the start of the document.",nA(A,e));if(A[e]=="?"&&A[e+1]==">"){e++;break}else continue}return e}function Dn(A,e){if(A.length>e+5&&A[e+1]==="-"&&A[e+2]==="-"){for(e+=3;e<A.length;e++)if(A[e]==="-"&&A[e+1]==="-"&&A[e+2]===">"){e+=2;break}}else if(A.length>e+8&&A[e+1]==="D"&&A[e+2]==="O"&&A[e+3]==="C"&&A[e+4]==="T"&&A[e+5]==="Y"&&A[e+6]==="P"&&A[e+7]==="E"){let t=1;for(e+=8;e<A.length;e++)if(A[e]==="<")t++;else if(A[e]===">"&&(t--,t===0))break}else if(A.length>e+9&&A[e+1]==="["&&A[e+2]==="C"&&A[e+3]==="D"&&A[e+4]==="A"&&A[e+5]==="T"&&A[e+6]==="A"&&A[e+7]==="["){for(e+=8;e<A.length;e++)if(A[e]==="]"&&A[e+1]==="]"&&A[e+2]===">"){e+=2;break}}return e}var ji='"',$i="'";function Zi(A,e){let t="",r="",n=!1;for(;e<A.length;e++){if(A[e]===ji||A[e]===$i)r===""?r=A[e]:r!==A[e]||(r="");else if(A[e]===">"&&r===""){n=!0;break}t+=A[e]}return r!==""?!1:{value:t,index:e,tagClosed:n}}var Yi=new RegExp(`(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['"])(([\\s\\S])*?)\\5)?`,"g");function Vn(A,e){let t=gr.getAllMatches(A,Yi),r={};for(let n=0;n<t.length;n++){if(t[n][1].length===0)return z("InvalidAttr","Attribute '"+t[n][2]+"' has no space in starting.",Fe(t[n]));if(t[n][3]!==void 0&&t[n][4]===void 0)return z("InvalidAttr","Attribute '"+t[n][2]+"' is without value.",Fe(t[n]));if(t[n][3]===void 0&&!e.allowBooleanAttributes)return z("InvalidAttr","boolean attribute '"+t[n][2]+"' is not allowed.",Fe(t[n]));let s=t[n][2];if(!tc(s))return z("InvalidAttr","Attribute '"+s+"' is an invalid name.",Fe(t[n]));if(!r.hasOwnProperty(s))r[s]=1;else return z("InvalidAttr","Attribute '"+s+"' is repeated.",Fe(t[n]))}return!0}function Ac(A,e){let t=/\d/;for(A[e]==="x"&&(e++,t=/[\da-fA-F]/);e<A.length;e++){if(A[e]===";")return e;if(!A[e].match(t))break}return-1}function ec(A,e){if(e++,A[e]===";")return-1;if(A[e]==="#")return e++,Ac(A,e);let t=0;for(;e<A.length;e++,t++)if(!(A[e].match(/\w/)&&t<20)){if(A[e]===";")break;return-1}return e}function z(A,e,t){return{err:{code:A,msg:e,line:t.line||t,col:t.col}}}function tc(A){return gr.isName(A)}function rc(A){return gr.isName(A)}function nA(A,e){let t=A.substring(0,e).split(/\r?\n/);return{line:t.length,col:t[t.length-1].length+1}}function Fe(A){return A.startIndex+A[1].length}});var Xn=aA(Cr=>{var zn={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(A,e){return e},attributeValueProcessor:function(A,e){return e},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1},nc=function(A){return Object.assign({},zn,A)};Cr.buildOptions=nc;Cr.defaultOptions=zn});var Gn=aA((Vd,vn)=>{"use strict";var Ur=class{constructor(e){this.tagname=e,this.child=[],this[":@"]={}}add(e,t){e==="__proto__"&&(e="#__proto__"),this.child.push({[e]:t})}addChild(e){e.tagname==="__proto__"&&(e.tagname="#__proto__"),e[":@"]&&Object.keys(e[":@"]).length>0?this.child.push({[e.tagname]:e.child,[":@"]:e[":@"]}):this.child.push({[e.tagname]:e.child})}};vn.exports=Ur});var Wn=aA((Pd,Jn)=>{function sc(A,e){let t={};if(A[e+3]==="O"&&A[e+4]==="C"&&A[e+5]==="T"&&A[e+6]==="Y"&&A[e+7]==="P"&&A[e+8]==="E"){e=e+9;let r=1,n=!1,s=!1,o=!1,c="";for(;e<A.length;e++)if(A[e]==="<"&&!o){if(n&&A[e+1]==="!"&&A[e+2]==="E"&&A[e+3]==="N"&&A[e+4]==="T"&&A[e+5]==="I"&&A[e+6]==="T"&&A[e+7]==="Y")e+=7,s=!0;else if(n&&A[e+1]==="!"&&A[e+2]==="E"&&A[e+3]==="L"&&A[e+4]==="E"&&A[e+5]==="M"&&A[e+6]==="E"&&A[e+7]==="N"&&A[e+8]==="T")e+=8;else if(n&&A[e+1]==="!"&&A[e+2]==="A"&&A[e+3]==="T"&&A[e+4]==="T"&&A[e+5]==="L"&&A[e+6]==="I"&&A[e+7]==="S"&&A[e+8]==="T")e+=8;else if(n&&A[e+1]==="!"&&A[e+2]==="N"&&A[e+3]==="O"&&A[e+4]==="T"&&A[e+5]==="A"&&A[e+6]==="T"&&A[e+7]==="I"&&A[e+8]==="O"&&A[e+9]==="N")e+=9;else if(A[e+1]==="!"&&A[e+2]==="-"&&A[e+3]==="-")o=!0;else throw new Error("Invalid DOCTYPE");r++,c=""}else if(A[e]===">"){if(o?A[e-1]==="-"&&A[e-2]==="-"&&(o=!1,r--):(s&&(ic(c,t),s=!1),r--),r===0)break}else A[e]==="["?n=!0:c+=A[e];if(r!==0)throw new Error("Unclosed DOCTYPE")}else throw new Error("Invalid Tag instead of DOCTYPE");return{entities:t,i:e}}var oc=RegExp(`^\\s([a-zA-z0-0]+)[ 	](['"])([^&]+)\\2`);function ic(A,e){let t=oc.exec(A);t&&(e[t[1]]={regx:RegExp(`&${t[1]};`,"g"),val:t[3]})}Jn.exports=sc});var qn=aA((zd,kn)=>{var cc=/^[-+]?0x[a-fA-F0-9]+$/,ac=/^([\-\+])?(0*)(\.[0-9]+([eE]\-?[0-9]+)?|[0-9]+(\.[0-9]+([eE]\-?[0-9]+)?)?)$/;!Number.parseInt&&window.parseInt&&(Number.parseInt=window.parseInt);!Number.parseFloat&&window.parseFloat&&(Number.parseFloat=window.parseFloat);var Bc={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};function uc(A,e={}){if(e=Object.assign({},Bc,e),!A||typeof A!="string")return A;let t=A.trim();if(e.skipLike!==void 0&&e.skipLike.test(t))return A;if(e.hex&&cc.test(t))return Number.parseInt(t,16);{let r=ac.exec(t);if(r){let n=r[1],s=r[2],o=lc(r[3]),c=r[4]||r[6];if(!e.leadingZeros&&s.length>0&&n&&t[2]!==".")return A;if(!e.leadingZeros&&s.length>0&&!n&&t[1]!==".")return A;{let i=Number(t),a=""+i;return a.search(/[eE]/)!==-1||c?e.eNotation?i:A:t.indexOf(".")!==-1?a==="0"&&o===""||a===o||n&&a==="-"+o?i:A:s?o===a||n+o===a?i:A:t===a||t===n+a?i:A}}else return A}}function lc(A){return A&&A.indexOf(".")!==-1&&(A=A.replace(/0+$/,""),A==="."?A="0":A[0]==="."?A="0"+A:A[A.length-1]==="."&&(A=A.substr(0,A.length-1))),A}kn.exports=uc});var $n=aA((vd,jn)=>{"use strict";var mr=Qr(),me=Gn(),fc=Wn(),dc=qn(),Xd="<((!\\[CDATA\\[([\\s\\S]*?)(]]>))|((NAME:)?(NAME))([^>]*)>|((\\/)(NAME)\\s*>))([^<]*)".replace(/NAME/g,mr.nameRegexp),Er=class{constructor(e){this.options=e,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"\xA2"},pound:{regex:/&(pound|#163);/g,val:"\xA3"},yen:{regex:/&(yen|#165);/g,val:"\xA5"},euro:{regex:/&(euro|#8364);/g,val:"\u20AC"},copyright:{regex:/&(copy|#169);/g,val:"\xA9"},reg:{regex:/&(reg|#174);/g,val:"\xAE"},inr:{regex:/&(inr|#8377);/g,val:"\u20B9"}},this.addExternalEntities=hc,this.parseXml=Uc,this.parseTextData=Qc,this.resolveNameSpace=gc,this.buildAttributesMap=Cc,this.isItStopNode=Fc,this.replaceEntitiesValue=Ec,this.readStopNodeData=Hc,this.saveTextToParentTag=pc}};function hc(A){let e=Object.keys(A);for(let t=0;t<e.length;t++){let r=e[t];this.lastEntities[r]={regex:new RegExp("&"+r+";","g"),val:A[r]}}}function Qc(A,e,t,r,n,s,o){if(A!==void 0&&(this.options.trimValues&&!r&&(A=A.trim()),A.length>0)){o||(A=this.replaceEntitiesValue(A));let c=this.options.tagValueProcessor(e,A,t,n,s);return c==null?A:typeof c!=typeof A||c!==A?c:this.options.trimValues?Fr(A,this.options.parseTagValue,this.options.numberParseOptions):A.trim()===A?Fr(A,this.options.parseTagValue,this.options.numberParseOptions):A}}function gc(A){if(this.options.removeNSPrefix){let e=A.split(":"),t=A.charAt(0)==="/"?"/":"";if(e[0]==="xmlns")return"";e.length===2&&(A=t+e[1])}return A}var wc=new RegExp(`([^\\s=]+)\\s*(=\\s*(['"])([\\s\\S]*?)\\3)?`,"gm");function Cc(A,e){if(!this.options.ignoreAttributes&&typeof A=="string"){let t=mr.getAllMatches(A,wc),r=t.length,n={};for(let s=0;s<r;s++){let o=this.resolveNameSpace(t[s][1]),c=t[s][4],i=this.options.attributeNamePrefix+o;if(o.length)if(this.options.transformAttributeName&&(i=this.options.transformAttributeName(i)),i==="__proto__"&&(i="#__proto__"),c!==void 0){this.options.trimValues&&(c=c.trim()),c=this.replaceEntitiesValue(c);let a=this.options.attributeValueProcessor(o,c,e);a==null?n[i]=c:typeof a!=typeof c||a!==c?n[i]=a:n[i]=Fr(c,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(n[i]=!0)}if(!Object.keys(n).length)return;if(this.options.attributesGroupName){let s={};return s[this.options.attributesGroupName]=n,s}return n}}var Uc=function(A){A=A.replace(/\r\n?/g,`
`);let e=new me("!xml"),t=e,r="",n="";for(let s=0;s<A.length;s++)if(A[s]==="<")if(A[s+1]==="/"){let c=qA(A,">",s,"Closing Tag is not closed."),i=A.substring(s+2,c).trim();if(this.options.removeNSPrefix){let a=i.indexOf(":");a!==-1&&(i=i.substr(a+1))}this.options.transformTagName&&(i=this.options.transformTagName(i)),t&&(r=this.saveTextToParentTag(r,t,n)),n=n.substr(0,n.lastIndexOf(".")),t=this.tagsNodeStack.pop(),r="",s=c}else if(A[s+1]==="?"){let c=pr(A,s,!1,"?>");if(!c)throw new Error("Pi Tag is not closed.");if(r=this.saveTextToParentTag(r,t,n),!(this.options.ignoreDeclaration&&c.tagName==="?xml"||this.options.ignorePiTags)){let i=new me(c.tagName);i.add(this.options.textNodeName,""),c.tagName!==c.tagExp&&c.attrExpPresent&&(i[":@"]=this.buildAttributesMap(c.tagExp,n)),t.addChild(i)}s=c.closeIndex+1}else if(A.substr(s+1,3)==="!--"){let c=qA(A,"-->",s+4,"Comment is not closed.");if(this.options.commentPropName){let i=A.substring(s+4,c-2);r=this.saveTextToParentTag(r,t,n),t.add(this.options.commentPropName,[{[this.options.textNodeName]:i}])}s=c}else if(A.substr(s+1,2)==="!D"){let c=fc(A,s);this.docTypeEntities=c.entities,s=c.i}else if(A.substr(s+1,2)==="!["){let c=qA(A,"]]>",s,"CDATA is not closed.")-2,i=A.substring(s+9,c);if(r=this.saveTextToParentTag(r,t,n),this.options.cdataPropName)t.add(this.options.cdataPropName,[{[this.options.textNodeName]:i}]);else{let a=this.parseTextData(i,t.tagname,n,!0,!1,!0);a==null&&(a=""),t.add(this.options.textNodeName,a)}s=c+2}else{let c=pr(A,s,this.options.removeNSPrefix),i=c.tagName,a=c.tagExp,B=c.attrExpPresent,f=c.closeIndex;this.options.transformTagName&&(i=this.options.transformTagName(i)),t&&r&&t.tagname!=="!xml"&&(r=this.saveTextToParentTag(r,t,n,!1)),i!==e.tagname&&(n+=n?"."+i:i);let d=t;if(d&&this.options.unpairedTags.indexOf(d.tagname)!==-1&&(t=this.tagsNodeStack.pop()),this.isItStopNode(this.options.stopNodes,n,i)){let l="";if(a.length>0&&a.lastIndexOf("/")===a.length-1)s=c.closeIndex;else if(this.options.unpairedTags.indexOf(i)!==-1)s=c.closeIndex;else{let h=this.readStopNodeData(A,i,f+1);if(!h)throw new Error(`Unexpected end of ${i}`);s=h.i,l=h.tagContent}let u=new me(i);i!==a&&B&&(u[":@"]=this.buildAttributesMap(a,n)),l&&(l=this.parseTextData(l,i,n,!0,B,!0,!0)),n=n.substr(0,n.lastIndexOf(".")),u.add(this.options.textNodeName,l),t.addChild(u)}else{if(a.length>0&&a.lastIndexOf("/")===a.length-1){i[i.length-1]==="/"?(i=i.substr(0,i.length-1),a=i):a=a.substr(0,a.length-1),this.options.transformTagName&&(i=this.options.transformTagName(i));let l=new me(i);i!==a&&B&&(l[":@"]=this.buildAttributesMap(a,n)),n=n.substr(0,n.lastIndexOf(".")),t.addChild(l)}else{let l=new me(i);this.tagsNodeStack.push(t),i!==a&&B&&(l[":@"]=this.buildAttributesMap(a,n)),t.addChild(l),t=l}r="",s=f}}else r+=A[s];return e.child},Ec=function(A){if(this.options.processEntities){for(let e in this.docTypeEntities){let t=this.docTypeEntities[e];A=A.replace(t.regx,t.val)}for(let e in this.lastEntities){let t=this.lastEntities[e];A=A.replace(t.regex,t.val)}if(this.options.htmlEntities)for(let e in this.htmlEntities){let t=this.htmlEntities[e];A=A.replace(t.regex,t.val)}A=A.replace(this.ampEntity.regex,this.ampEntity.val)}return A};function pc(A,e,t,r){return A&&(r===void 0&&(r=Object.keys(e.child).length===0),A=this.parseTextData(A,e.tagname,t,!1,e[":@"]?Object.keys(e[":@"]).length!==0:!1,r),A!==void 0&&A!==""&&e.add(this.options.textNodeName,A),A=""),A}function Fc(A,e,t){let r="*."+t;for(let n in A){let s=A[n];if(r===s||e===s)return!0}return!1}function mc(A,e,t=">"){let r,n="";for(let s=e;s<A.length;s++){let o=A[s];if(r)o===r&&(r="");else if(o==='"'||o==="'")r=o;else if(o===t[0])if(t[1]){if(A[s+1]===t[1])return{data:n,index:s}}else return{data:n,index:s};else o==="	"&&(o=" ");n+=o}}function qA(A,e,t,r){let n=A.indexOf(e,t);if(n===-1)throw new Error(r);return n+e.length-1}function pr(A,e,t,r=">"){let n=mc(A,e+1,r);if(!n)return;let s=n.data,o=n.index,c=s.search(/\s/),i=s,a=!0;if(c!==-1&&(i=s.substr(0,c).replace(/\s\s*$/,""),s=s.substr(c+1)),t){let B=i.indexOf(":");B!==-1&&(i=i.substr(B+1),a=i!==n.data.substr(B+1))}return{tagName:i,tagExp:s,closeIndex:o,attrExpPresent:a}}function Hc(A,e,t){let r=t,n=1;for(;t<A.length;t++)if(A[t]==="<")if(A[t+1]==="/"){let s=qA(A,">",t,`${e} is not closed`);if(A.substring(t+2,s).trim()===e&&(n--,n===0))return{tagContent:A.substring(r,t),i:s};t=s}else if(A[t+1]==="?")t=qA(A,"?>",t+1,"StopNode is not closed.");else if(A.substr(t+1,3)==="!--")t=qA(A,"-->",t+3,"StopNode is not closed.");else if(A.substr(t+1,2)==="![")t=qA(A,"]]>",t,"StopNode is not closed.")-2;else{let s=pr(A,t,">");s&&((s&&s.tagName)===e&&s.tagExp[s.tagExp.length-1]!=="/"&&n++,t=s.closeIndex)}}function Fr(A,e,t){if(e&&typeof A=="string"){let r=A.trim();return r==="true"?!0:r==="false"?!1:dc(A,t)}else return mr.isExist(A)?A:""}jn.exports=Er});var As=aA(Yn=>{"use strict";function Nc(A,e){return Zn(A,e)}function Zn(A,e,t){let r,n={};for(let s=0;s<A.length;s++){let o=A[s],c=Kc(o),i="";if(t===void 0?i=c:i=t+"."+c,c===e.textNodeName)r===void 0?r=o[c]:r+=""+o[c];else{if(c===void 0)continue;if(o[c]){let a=Zn(o[c],e,i),B=Rc(a,e);o[":@"]?Ic(a,o[":@"],i,e):Object.keys(a).length===1&&a[e.textNodeName]!==void 0&&!e.alwaysCreateTextNode?a=a[e.textNodeName]:Object.keys(a).length===0&&(e.alwaysCreateTextNode?a[e.textNodeName]="":a=""),n[c]!==void 0&&n.hasOwnProperty(c)?(Array.isArray(n[c])||(n[c]=[n[c]]),n[c].push(a)):e.isArray(c,i,B)?n[c]=[a]:n[c]=a}}}return typeof r=="string"?r.length>0&&(n[e.textNodeName]=r):r!==void 0&&(n[e.textNodeName]=r),n}function Kc(A){let e=Object.keys(A);for(let t=0;t<e.length;t++){let r=e[t];if(r!==":@")return r}}function Ic(A,e,t,r){if(e){let n=Object.keys(e),s=n.length;for(let o=0;o<s;o++){let c=n[o];r.isArray(c,t+"."+c,!0,!0)?A[c]=[e[c]]:A[c]=e[c]}}}function Rc(A,e){let t=Object.keys(A).length;return!!(t===0||t===1&&A[e.textNodeName])}Yn.prettify=Nc});var ts=aA((Jd,es)=>{var{buildOptions:bc}=Xn(),xc=$n(),{prettify:Oc}=As(),Sc=wr(),Hr=class{constructor(e){this.externalEntities={},this.options=bc(e)}parse(e,t){if(typeof e!="string")if(e.toString)e=e.toString();else throw new Error("XML data is accepted in String or Bytes[] form.");if(t){t===!0&&(t={});let s=Sc.validate(e,t);if(s!==!0)throw Error(`${s.err.msg}:${s.err.line}:${s.err.col}`)}let r=new xc(this.options);r.addExternalEntities(this.externalEntities);let n=r.parseXml(e);return this.options.preserveOrder||n===void 0?n:Oc(n,this.options)}addEntity(e,t){if(t.indexOf("&")!==-1)throw new Error("Entity value can't have '&'");if(e.indexOf("&")!==-1||e.indexOf(";")!==-1)throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if(t==="&")throw new Error("An entity with value '&' is not permitted");this.externalEntities[e]=t}};es.exports=Hr});var is=aA((Wd,os)=>{var yc=`
`;function Tc(A,e){let t="";return e.format&&e.indentBy.length>0&&(t=yc),ns(A,e,"",t)}function ns(A,e,t,r){let n="",s=!1;for(let o=0;o<A.length;o++){let c=A[o],i=Mc(c),a="";if(t.length===0?a=i:a=`${t}.${i}`,i===e.textNodeName){let u=c[i];Lc(a,e)||(u=e.tagValueProcessor(i,u),u=ss(u,e)),s&&(n+=r),n+=u,s=!1;continue}else if(i===e.cdataPropName){s&&(n+=r),n+=`<![CDATA[${c[i][0][e.textNodeName]}]]>`,s=!1;continue}else if(i===e.commentPropName){n+=r+`<!--${c[i][0][e.textNodeName]}-->`,s=!0;continue}else if(i[0]==="?"){let u=rs(c[":@"],e),h=i==="?xml"?"":r,U=c[i][0][e.textNodeName];U=U.length!==0?" "+U:"",n+=h+`<${i}${U}${u}?>`,s=!0;continue}let B=r;B!==""&&(B+=e.indentBy);let f=rs(c[":@"],e),d=r+`<${i}${f}`,l=ns(c[i],e,a,B);e.unpairedTags.indexOf(i)!==-1?e.suppressUnpairedNode?n+=d+">":n+=d+"/>":(!l||l.length===0)&&e.suppressEmptyNode?n+=d+"/>":l&&l.endsWith(">")?n+=d+`>${l}${r}</${i}>`:(n+=d+">",l&&r!==""&&(l.includes("/>")||l.includes("</"))?n+=r+e.indentBy+l+r:n+=l,n+=`</${i}>`),s=!0}return n}function Mc(A){let e=Object.keys(A);for(let t=0;t<e.length;t++){let r=e[t];if(r!==":@")return r}}function rs(A,e){let t="";if(A&&!e.ignoreAttributes)for(let r in A){let n=e.attributeValueProcessor(r,A[r]);n=ss(n,e),n===!0&&e.suppressBooleanAttributes?t+=` ${r.substr(e.attributeNamePrefix.length)}`:t+=` ${r.substr(e.attributeNamePrefix.length)}="${n}"`}return t}function Lc(A,e){A=A.substr(0,A.length-e.textNodeName.length-1);let t=A.substr(A.lastIndexOf(".")+1);for(let r in e.stopNodes)if(e.stopNodes[r]===A||e.stopNodes[r]==="*."+t)return!0;return!1}function ss(A,e){if(A&&A.length>0&&e.processEntities)for(let t=0;t<e.entities.length;t++){let r=e.entities[t];A=A.replace(r.regex,r.val)}return A}os.exports=Tc});var as=aA((kd,cs)=>{"use strict";var _c=is(),Dc={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(A,e){return e},attributeValueProcessor:function(A,e){return e},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:new RegExp("&","g"),val:"&amp;"},{regex:new RegExp(">","g"),val:"&gt;"},{regex:new RegExp("<","g"),val:"&lt;"},{regex:new RegExp("'","g"),val:"&apos;"},{regex:new RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[]};function OA(A){this.options=Object.assign({},Dc,A),this.options.ignoreAttributes||this.options.attributesGroupName?this.isAttribute=function(){return!1}:(this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=zc),this.processTextOrObjNode=Vc,this.options.format?(this.indentate=Pc,this.tagEndChar=`>
`,this.newLine=`
`):(this.indentate=function(){return""},this.tagEndChar=">",this.newLine="")}OA.prototype.build=function(A){return this.options.preserveOrder?_c(A,this.options):(Array.isArray(A)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1&&(A={[this.options.arrayNodeName]:A}),this.j2x(A,0).val)};OA.prototype.j2x=function(A,e){let t="",r="";for(let n in A)if(!(typeof A[n]>"u"))if(A[n]===null)n[0]==="?"?r+=this.indentate(e)+"<"+n+"?"+this.tagEndChar:r+=this.indentate(e)+"<"+n+"/"+this.tagEndChar;else if(A[n]instanceof Date)r+=this.buildTextValNode(A[n],n,"",e);else if(typeof A[n]!="object"){let s=this.isAttribute(n);if(s)t+=this.buildAttrPairStr(s,""+A[n]);else if(n===this.options.textNodeName){let o=this.options.tagValueProcessor(n,""+A[n]);r+=this.replaceEntitiesValue(o)}else r+=this.buildTextValNode(A[n],n,"",e)}else if(Array.isArray(A[n])){let s=A[n].length;for(let o=0;o<s;o++){let c=A[n][o];typeof c>"u"||(c===null?n[0]==="?"?r+=this.indentate(e)+"<"+n+"?"+this.tagEndChar:r+=this.indentate(e)+"<"+n+"/"+this.tagEndChar:typeof c=="object"?r+=this.processTextOrObjNode(c,n,e):r+=this.buildTextValNode(c,n,"",e))}}else if(this.options.attributesGroupName&&n===this.options.attributesGroupName){let s=Object.keys(A[n]),o=s.length;for(let c=0;c<o;c++)t+=this.buildAttrPairStr(s[c],""+A[n][s[c]])}else r+=this.processTextOrObjNode(A[n],n,e);return{attrStr:t,val:r}};OA.prototype.buildAttrPairStr=function(A,e){return e=this.options.attributeValueProcessor(A,""+e),e=this.replaceEntitiesValue(e),this.options.suppressBooleanAttributes&&e==="true"?" "+A:" "+A+'="'+e+'"'};function Vc(A,e,t){let r=this.j2x(A,t+1);return A[this.options.textNodeName]!==void 0&&Object.keys(A).length===1?this.buildTextValNode(A[this.options.textNodeName],e,r.attrStr,t):this.buildObjectNode(r.val,e,r.attrStr,t)}OA.prototype.buildObjectNode=function(A,e,t,r){if(A==="")return e[0]==="?"?this.indentate(r)+"<"+e+t+"?"+this.tagEndChar:this.indentate(r)+"<"+e+t+this.closeTag(e)+this.tagEndChar;{let n="</"+e+this.tagEndChar,s="";return e[0]==="?"&&(s="?",n=""),t&&A.indexOf("<")===-1?this.indentate(r)+"<"+e+t+s+">"+A+n:this.options.commentPropName!==!1&&e===this.options.commentPropName&&s.length===0?this.indentate(r)+`<!--${A}-->`+this.newLine:this.indentate(r)+"<"+e+t+s+this.tagEndChar+A+this.indentate(r)+n}};OA.prototype.closeTag=function(A){let e="";return this.options.unpairedTags.indexOf(A)!==-1?this.options.suppressUnpairedNode||(e="/"):this.options.suppressEmptyNode?e="/":e=`></${A}`,e};OA.prototype.buildTextValNode=function(A,e,t,r){if(this.options.cdataPropName!==!1&&e===this.options.cdataPropName)return this.indentate(r)+`<![CDATA[${A}]]>`+this.newLine;if(this.options.commentPropName!==!1&&e===this.options.commentPropName)return this.indentate(r)+`<!--${A}-->`+this.newLine;if(e[0]==="?")return this.indentate(r)+"<"+e+t+"?"+this.tagEndChar;{let n=this.options.tagValueProcessor(e,A);return n=this.replaceEntitiesValue(n),n===""?this.indentate(r)+"<"+e+t+this.closeTag(e)+this.tagEndChar:this.indentate(r)+"<"+e+t+">"+n+"</"+e+this.tagEndChar}};OA.prototype.replaceEntitiesValue=function(A){if(A&&A.length>0&&this.options.processEntities)for(let e=0;e<this.options.entities.length;e++){let t=this.options.entities[e];A=A.replace(t.regex,t.val)}return A};function Pc(A){return this.options.indentBy.repeat(A)}function zc(A){return A.startsWith(this.options.attributeNamePrefix)?A.substr(this.attrPrefixLen):!1}cs.exports=OA});var Nr=aA((qd,Bs)=>{"use strict";var Xc=wr(),vc=ts(),Gc=as();Bs.exports={XMLParser:vc,XMLValidator:Xc,XMLBuilder:Gc}});var xd={};Vi(xd,{EMExtensionsMixin:()=>yn,EV_CameraManager:()=>ot,EV_GroupManager:()=>it,EV_ViewPointManager:()=>yi});var _={_maximumCombinedTextureImageUnits:0,_maximumCubeMapSize:0,_maximumFragmentUniformVectors:0,_maximumTextureImageUnits:0,_maximumRenderbufferSize:0,_maximumTextureSize:0,_maximumVaryingVectors:0,_maximumVertexAttributes:0,_maximumVertexTextureImageUnits:0,_maximumVertexUniformVectors:0,_minimumAliasedLineWidth:0,_maximumAliasedLineWidth:0,_minimumAliasedPointSize:0,_maximumAliasedPointSize:0,_maximumViewportWidth:0,_maximumViewportHeight:0,_maximumTextureFilterAnisotropy:0,_maximumDrawBuffers:0,_maximumColorAttachments:0,_maximumSamples:0,_highpFloatSupported:!1,_highpIntSupported:!1};Object.defineProperties(_,{maximumCombinedTextureImageUnits:{get:function(){return _._maximumCombinedTextureImageUnits}},maximumCubeMapSize:{get:function(){return _._maximumCubeMapSize}},maximumFragmentUniformVectors:{get:function(){return _._maximumFragmentUniformVectors}},maximumTextureImageUnits:{get:function(){return _._maximumTextureImageUnits}},maximumRenderbufferSize:{get:function(){return _._maximumRenderbufferSize}},maximumTextureSize:{get:function(){return _._maximumTextureSize}},maximumVaryingVectors:{get:function(){return _._maximumVaryingVectors}},maximumVertexAttributes:{get:function(){return _._maximumVertexAttributes}},maximumVertexTextureImageUnits:{get:function(){return _._maximumVertexTextureImageUnits}},maximumVertexUniformVectors:{get:function(){return _._maximumVertexUniformVectors}},minimumAliasedLineWidth:{get:function(){return _._minimumAliasedLineWidth}},maximumAliasedLineWidth:{get:function(){return _._maximumAliasedLineWidth}},minimumAliasedPointSize:{get:function(){return _._minimumAliasedPointSize}},maximumAliasedPointSize:{get:function(){return _._maximumAliasedPointSize}},maximumViewportWidth:{get:function(){return _._maximumViewportWidth}},maximumViewportHeight:{get:function(){return _._maximumViewportHeight}},maximumTextureFilterAnisotropy:{get:function(){return _._maximumTextureFilterAnisotropy}},maximumDrawBuffers:{get:function(){return _._maximumDrawBuffers}},maximumColorAttachments:{get:function(){return _._maximumColorAttachments}},maximumSamples:{get:function(){return _._maximumSamples}},highpFloatSupported:{get:function(){return _._highpFloatSupported}},highpIntSupported:{get:function(){return _._highpIntSupported}}});var D=_;function zi(A,e){D._maximumCombinedTextureImageUnits=Cesium.ContextLimits._maximumCombinedTextureImageUnits,D._maximumCubeMapSize=Cesium.ContextLimits._maximumCubeMapSize,D._maximumFragmentUniformVectors=Cesium.ContextLimits._maximumFragmentUniformVectors,D._maximumTextureImageUnits=Cesium.ContextLimits._maximumTextureImageUnits,D._maximumRenderbufferSize=Cesium.ContextLimits._maximumRenderbufferSize,D._maximumTextureSize=Cesium.ContextLimits._maximumTextureSize,D._maximumVertexAttributes=Cesium.ContextLimits._maximumVertexAttributes,D._maximumVertexTextureImageUnits=Cesium.ContextLimits._maximumVertexTextureImageUnits,D._maximumVertexUniformVectors=Cesium.ContextLimits._maximumVertexUniformVectors,D._minimumAliasedLineWidth=Cesium.ContextLimits._minimumAliasedLineWidth,D._maximumAliasedLineWidth=Cesium.ContextLimits._maximumAliasedLineWidth,D._minimumAliasedPointSize=Cesium.ContextLimits._minimumAliasedPointSize,D._maximumAliasedPointSize=Cesium.ContextLimits._maximumAliasedPointSize,D._maximumViewportWidth=Cesium.ContextLimits._maximumViewportWidth,D._maximumViewportHeight=Cesium.ContextLimits._maximumViewportHeight,D._highpFloatSupported=Cesium.ContextLimits._highpFloatSupported,D._highpIntSupported=Cesium.ContextLimits._highpIntSupported,D._maximumTextureFilterAnisotropy=Cesium.ContextLimits._maximumTextureFilterAnisotropy,D._maximumDrawBuffers=Cesium.ContextLimits._maximumDrawBuffers,D._maximumColorAttachments=Cesium.ContextLimits._maximumColorAttachments}var yn=zi;var hr=class{constructor(e,t){this.viewer=e,this.olMap=t}cameraJump(e,t){let r=Xi(e,t),n=Cesium.Cartesian3.fromDegrees(r.CamLon,r.CamLat,r.CamAltitude),s=Cesium.Math.toRadians(r.CamHeading),o=Cesium.Math.toRadians(r.CamTilt-90),c=Cesium.Math.toRadians(r.CamRoll),{heading:i,pitch:a,roll:B,position:f}=this.viewer.camera;Cesium.Math.equalsEpsilon(a,o,Cesium.Math.EPSILON7)&&Cesium.Math.equalsEpsilon(i,s,Cesium.Math.EPSILON9)&&Cesium.Math.equalsEpsilon(B,c,Cesium.Math.EPSILON9)&&Cesium.Math.equalsEpsilon(f.x,n.x,Cesium.Math.EPSILON12)&&Cesium.Math.equalsEpsilon(f.y,n.y,Cesium.Math.EPSILON12)&&Cesium.Math.equalsEpsilon(f.z,n.z,Cesium.Math.EPSILON12)||this.viewer.camera.flyTo({destination:n,orientation:{heading:s,pitch:o,roll:c},duration:6})}setView(e,t){let r=vi(this.olMap,e,t);this.olMap.getView().setZoom(r.scale),this.olMap.getView().setCenter(r.coordinate)}setClickEvent(){this.viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(()=>{this.viewer.camera.cancelFlight()},Cesium.ScreenSpaceEventType.LEFT_CLICK),this.viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(()=>{this.viewer.camera.cancelFlight()},Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(()=>{this.viewer.camera.cancelFlight()},Cesium.ScreenSpaceEventType.MIDDLE_DOWN)}destoryClickEvent(){this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.MIDDLE_DOWN)}};function Xi(A,e){let t={};if(A.hasOwnProperty("ViewPoint")){let B=A.ViewPoint;B instanceof Array?t=B.find(f=>f.guid==e):t=B}else t=A;let r={},{CamLon:n,CamLat:s,CamAltitude:o,CamHeading:c,CamTilt:i,CamRoll:a}=t;return s=parseFloat(s),n=parseFloat(n),o=parseFloat(o),c=parseFloat(c),i=parseFloat(i),a=parseFloat(a),r={CamLat:s,CamLon:n,CamAltitude:o,CamHeading:c,CamTilt:i,CamRoll:a},r}function vi(A,e,t){let r={};if(e.hasOwnProperty("ViewPoint")){let B=e.ViewPoint;B instanceof Array?r=B.find(f=>f.guid==t):r=B}else r=e;let{CenterX:n,CenterY:s,CurrentScale:o}=r.Layer2D,c=[parseFloat(n),parseFloat(s)],i=parseFloat(o),a=A.getView().getProjection();return a.code_==="EPSG:3857"?(i=A.getView().getZoomForResolution(i/(96*39.37001*11e4)*111319),c=ol.proj.transform(c,"EPSG:4326",a)):i=A.getView().getZoomForResolution(i/(96*39.37001*11e4)),{coordinate:c,scale:i}}var ot=hr;var us=Sn(Nr(),1),Kr=class{handleGroup(e){let t={ViewPointProjectList:{Project:[]}},r=e.map(s=>{let o={"@link_foldername":"","@project_name":"","@project_id":"","@createTime":"","@modifyTime":"","@isExpanded":""};return o["@link_foldername"]=s.link_foldername,o["@project_name"]=s.project_name,o["@project_id"]=s.project_id,o["@createTime"]=s.createTime,o["@modifyTime"]=s.modifyTime,o["@isExpanded"]=s.isExpanded,o});return t.ViewPointProjectList.Project=r,Jc(t)}};function Jc(A,e="@"){let t={ignoreAttributes:!1,attributeNamePrefix:e,format:!0};return new us.XMLBuilder(t).build(A)}var it=Kr;/*!
 * html2canvas 1.0.0-rc.5 <https://html2canvas.hertzen.com>
 * Copyright (c) 2020 Niklas von Hertzen <https://hertzen.com>
 * Released under MIT License
 *//*! *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
    ***************************************************************************** */var Pr=function(A,e){return Pr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(let n in r)r.hasOwnProperty(n)&&(t[n]=r[n])},Pr(A,e)};function bA(A,e){Pr(A,e);function t(){this.constructor=A}A.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}var Ht=function(){return Ht=Object.assign||function(e){for(let t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(let s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e},Ht.apply(this,arguments)};function cA(A,e,t,r){return new(t||(t=Promise))(function(n,s){function o(a){try{i(r.next(a))}catch(B){s(B)}}function c(a){try{i(r.throw(a))}catch(B){s(B)}}function i(a){a.done?n(a.value):new t(function(B){B(a.value)}).then(o,c)}i((r=r.apply(A,e||[])).next())})}function oA(A,e){let t,r,n,s,o={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]};return s={next:c(0),throw:c(1),return:c(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function c(a){return function(B){return i([a,B])}}function i(a){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,r&&(n=a[0]&2?r.return:a[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,a[1])).done)return n;switch(r=0,n&&(a=[a[0]&2,n.value]),a[0]){case 0:case 1:n=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,r=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(n=o.trys,!(n=n.length>0&&n[n.length-1])&&(a[0]===6||a[0]===2)){o=0;continue}if(a[0]===3&&(!n||a[1]>n[0]&&a[1]<n[3])){o.label=a[1];break}if(a[0]===6&&o.label<n[1]){o.label=n[1],n=a;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(a);break}n[2]&&o.ops.pop(),o.trys.pop();continue}a=e.call(A,o)}catch(B){a=[6,B],r=0}finally{t=n=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}}var XA=function(){function A(e,t,r,n){this.left=e,this.top=t,this.width=r,this.height=n}return A.prototype.add=function(e,t,r,n){return new A(this.left+e,this.top+t,this.width+r,this.height+n)},A.fromClientRect=function(e){return new A(e.left,e.top,e.width,e.height)},A}(),sn=function(A){return XA.fromClientRect(A.getBoundingClientRect())},Wc=function(A){let e=A.body,t=A.documentElement;if(!e||!t)throw new Error("Unable to get document size");let r=Math.max(Math.max(e.scrollWidth,t.scrollWidth),Math.max(e.offsetWidth,t.offsetWidth),Math.max(e.clientWidth,t.clientWidth)),n=Math.max(Math.max(e.scrollHeight,t.scrollHeight),Math.max(e.offsetHeight,t.offsetHeight),Math.max(e.clientHeight,t.clientHeight));return new XA(0,0,r,n)},_t=function(A){let e=[],t=0,r=A.length;for(;t<r;){let n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){let s=A.charCodeAt(t++);(s&64512)===56320?e.push(((n&1023)<<10)+(s&1023)+65536):(e.push(n),t--)}else e.push(n)}return e},X=function(){let A=[];for(let s=0;s<arguments.length;s++)A[s]=arguments[s];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);let e=A.length;if(!e)return"";let t=[],r=-1,n="";for(;++r<e;){let s=A[r];s<=65535?t.push(s):(s-=65536,t.push((s>>10)+55296,s%1024+56320)),(r+1===e||t.length>16384)&&(n+=String.fromCharCode.apply(String,t),t.length=0)}return n},ls="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",xe=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let A=0;A<ls.length;A++)xe[ls.charCodeAt(A)]=A;var kc=function(A){let e=A.length*.75,t,r=0,n,s,o,c,i=A.length;A[A.length-1]==="="&&(e--,A[A.length-2]==="="&&e--);let a=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(e):new Array(e),B=Array.isArray(a)?a:new Uint8Array(a);for(t=0;t<i;t+=4)n=xe[A.charCodeAt(t)],s=xe[A.charCodeAt(t+1)],o=xe[A.charCodeAt(t+2)],c=xe[A.charCodeAt(t+3)],B[r++]=n<<2|s>>4,B[r++]=(s&15)<<4|o>>2,B[r++]=(o&3)<<6|c&63;return a},qc=function(A){let e=A.length,t=[];for(let r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t},jc=function(A){let e=A.length,t=[];for(let r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t},Ae=5,on=6+5,Ir=2,$c=on-Ae,js=65536>>Ae,Zc=1<<Ae,Rr=Zc-1,Yc=1024>>Ae,Aa=js+Yc,ea=Aa,ta=32,ra=ea+ta,na=65536>>on,sa=1<<$c,oa=sa-1,fs=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},ia=function(A,e,t){return A.slice?A.slice(e,t):new Uint32Array(Array.prototype.slice.call(A,e,t))},ca=function(A){let e=kc(A),t=Array.isArray(e)?jc(e):new Uint32Array(e),r=Array.isArray(e)?qc(e):new Uint16Array(e),n=24,s=fs(r,n/2,t[4]/2),o=t[5]===2?fs(r,(n+t[4])/2):ia(t,Math.ceil((n+t[4])/4));return new aa(t[0],t[1],t[2],t[3],s,o)},aa=function(){function A(e,t,r,n,s,o){this.initialValue=e,this.errorValue=t,this.highStart=r,this.highValueIndex=n,this.index=s,this.data=o}return A.prototype.get=function(e){let t;if(e>=0){if(e<55296||e>56319&&e<=65535)return t=this.index[e>>Ae],t=(t<<Ir)+(e&Rr),this.data[t];if(e<=65535)return t=this.index[js+(e-55296>>Ae)],t=(t<<Ir)+(e&Rr),this.data[t];if(e<this.highStart)return t=ra-na+(e>>on),t=this.index[t],t+=e>>Ae&oa,t=this.index[t],t=(t<<Ir)+(e&Rr),this.data[t];if(e<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),Ba="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",ds=50,ua=1,$s=2,Zs=3,la=4,fa=5,hs=7,Ys=8,Qs=9,_A=10,Ao=11,gs=12,zr=13,da=14,Oe=15,Xr=16,ct=17,He=18,ws=19,br=20,vr=21,Ne=22,xr=23,te=24,sA=25,Se=26,ye=27,re=28,ha=29,$A=30,Qa=31,Ke=32,Ie=33,Gr=34,Jr=35,Wr=36,ZA=37,kr=38,Ft=39,mt=40,Or=41,eo=42,ga=43,to="!",R="\xD7",at="\xF7",ro=ca(Ba),gA=[$A,Wr],qr=[ua,$s,Zs,fa],no=[_A,Ys],Cs=[ye,Se],wa=qr.concat(no),Us=[kr,Ft,mt,Gr,Jr],Ca=[Oe,zr],Ua=function(A,e){e===void 0&&(e="strict");let t=[],r=[],n=[];return A.forEach(function(s,o){let c=ro.get(s);if(c>ds?(n.push(!0),c-=ds):n.push(!1),["normal","auto","loose"].indexOf(e)!==-1&&[8208,8211,12316,12448].indexOf(s)!==-1)return r.push(o),t.push(Xr);if(c===la||c===Ao){if(o===0)return r.push(o),t.push($A);let i=t[o-1];return wa.indexOf(i)===-1?(r.push(r[o-1]),t.push(i)):(r.push(o),t.push($A))}if(r.push(o),c===Qa)return t.push(e==="strict"?vr:ZA);if(c===eo||c===ha)return t.push($A);if(c===ga)return s>=131072&&s<=196605||s>=196608&&s<=262141?t.push(ZA):t.push($A);t.push(c)}),[r,t,n]},Sr=function(A,e,t,r){let n=r[t];if(Array.isArray(A)?A.indexOf(n)!==-1:A===n){let c=t;for(;c<=r.length;){c++;let i=r[c];if(i===e)return!0;if(i!==_A)break}}if(n===_A)for(var s=t;s>0;){s--;let c=r[s];if(Array.isArray(A)?A.indexOf(c)!==-1:A===c){let i=t;for(;i<=r.length;){i++;var o=r[i];if(o===e)return!0;if(o!==_A)break}}if(c!==_A)break}return!1},Es=function(A,e){let t=A;for(;t>=0;){let r=e[t];if(r===_A)t--;else return r}return 0},Ea=function(A,e,t,r,n){if(t[r]===0)return R;let s=r-1;if(Array.isArray(n)&&n[s]===!0)return R;let o=s-1,c=s+1,i=e[s],a=o>=0?e[o]:0,B=e[c];if(i===$s&&B===Zs)return R;if(qr.indexOf(i)!==-1)return to;if(qr.indexOf(B)!==-1||no.indexOf(B)!==-1)return R;if(Es(s,e)===Ys)return at;if(ro.get(A[s])===Ao&&(B===ZA||B===Ke||B===Ie)||i===hs||B===hs||i===Qs||[_A,zr,Oe].indexOf(i)===-1&&B===Qs||[ct,He,ws,te,re].indexOf(B)!==-1||Es(s,e)===Ne||Sr(xr,Ne,s,e)||Sr([ct,He],vr,s,e)||Sr(gs,gs,s,e))return R;if(i===_A)return at;if(i===xr||B===xr)return R;if(B===Xr||i===Xr)return at;if([zr,Oe,vr].indexOf(B)!==-1||i===da||a===Wr&&Ca.indexOf(i)!==-1||i===re&&B===Wr||B===br&&gA.concat(br,ws,sA,ZA,Ke,Ie).indexOf(i)!==-1||gA.indexOf(B)!==-1&&i===sA||gA.indexOf(i)!==-1&&B===sA||i===ye&&[ZA,Ke,Ie].indexOf(B)!==-1||[ZA,Ke,Ie].indexOf(i)!==-1&&B===Se||gA.indexOf(i)!==-1&&Cs.indexOf(B)!==-1||Cs.indexOf(i)!==-1&&gA.indexOf(B)!==-1||[ye,Se].indexOf(i)!==-1&&(B===sA||[Ne,Oe].indexOf(B)!==-1&&e[c+1]===sA)||[Ne,Oe].indexOf(i)!==-1&&B===sA||i===sA&&[sA,re,te].indexOf(B)!==-1)return R;if([sA,re,te,ct,He].indexOf(B)!==-1){let l=s;for(;l>=0;){let u=e[l];if(u===sA)return R;if([re,te].indexOf(u)!==-1)l--;else break}}if([ye,Se].indexOf(B)!==-1)for(var f=[ct,He].indexOf(i)!==-1?o:s;f>=0;){var d=e[f];if(d===sA)return R;if([re,te].indexOf(d)!==-1)f--;else break}if(kr===i&&[kr,Ft,Gr,Jr].indexOf(B)!==-1||[Ft,Gr].indexOf(i)!==-1&&[Ft,mt].indexOf(B)!==-1||[mt,Jr].indexOf(i)!==-1&&B===mt||Us.indexOf(i)!==-1&&[br,Se].indexOf(B)!==-1||Us.indexOf(B)!==-1&&i===ye||gA.indexOf(i)!==-1&&gA.indexOf(B)!==-1||i===te&&gA.indexOf(B)!==-1||gA.concat(sA).indexOf(i)!==-1&&B===Ne||gA.concat(sA).indexOf(B)!==-1&&i===He)return R;if(i===Or&&B===Or){let l=t[s],u=1;for(;l>0&&(l--,e[l]===Or);)u++;if(u%2!==0)return R}return i===Ke&&B===Ie?R:at},pa=function(A,e){e||(e={lineBreak:"normal",wordBreak:"normal"});let t=Ua(A,e.lineBreak),r=t[0],n=t[2],s=t[1];(e.wordBreak==="break-all"||e.wordBreak==="break-word")&&(s=s.map(function(c){return[sA,$A,eo].indexOf(c)!==-1?ZA:c}));let o=e.wordBreak==="keep-all"?n.map(function(c,i){return c&&A[i]>=19968&&A[i]<=40959}):void 0;return[r,s,o]},Fa=function(){function A(e,t,r,n){this.codePoints=e,this.required=t===to,this.start=r,this.end=n}return A.prototype.slice=function(){return X.apply(void 0,this.codePoints.slice(this.start,this.end))},A}(),ma=function(A,e){let t=_t(A),r=pa(t,e),n=r[0],s=r[1],o=r[2],c=t.length,i=0,a=0;return{next:function(){if(a>=c)return{done:!0,value:null};let B=R;for(;a<c&&(B=Ea(t,s,n,++a,o))===R;);if(B!==R||a===c){let f=new Fa(t,B,i,a);return i=a,{value:f,done:!1}}return{done:!0,value:null}}}},w;(function(A){A[A.STRING_TOKEN=0]="STRING_TOKEN",A[A.BAD_STRING_TOKEN=1]="BAD_STRING_TOKEN",A[A.LEFT_PARENTHESIS_TOKEN=2]="LEFT_PARENTHESIS_TOKEN",A[A.RIGHT_PARENTHESIS_TOKEN=3]="RIGHT_PARENTHESIS_TOKEN",A[A.COMMA_TOKEN=4]="COMMA_TOKEN",A[A.HASH_TOKEN=5]="HASH_TOKEN",A[A.DELIM_TOKEN=6]="DELIM_TOKEN",A[A.AT_KEYWORD_TOKEN=7]="AT_KEYWORD_TOKEN",A[A.PREFIX_MATCH_TOKEN=8]="PREFIX_MATCH_TOKEN",A[A.DASH_MATCH_TOKEN=9]="DASH_MATCH_TOKEN",A[A.INCLUDE_MATCH_TOKEN=10]="INCLUDE_MATCH_TOKEN",A[A.LEFT_CURLY_BRACKET_TOKEN=11]="LEFT_CURLY_BRACKET_TOKEN",A[A.RIGHT_CURLY_BRACKET_TOKEN=12]="RIGHT_CURLY_BRACKET_TOKEN",A[A.SUFFIX_MATCH_TOKEN=13]="SUFFIX_MATCH_TOKEN",A[A.SUBSTRING_MATCH_TOKEN=14]="SUBSTRING_MATCH_TOKEN",A[A.DIMENSION_TOKEN=15]="DIMENSION_TOKEN",A[A.PERCENTAGE_TOKEN=16]="PERCENTAGE_TOKEN",A[A.NUMBER_TOKEN=17]="NUMBER_TOKEN",A[A.FUNCTION=18]="FUNCTION",A[A.FUNCTION_TOKEN=19]="FUNCTION_TOKEN",A[A.IDENT_TOKEN=20]="IDENT_TOKEN",A[A.COLUMN_TOKEN=21]="COLUMN_TOKEN",A[A.URL_TOKEN=22]="URL_TOKEN",A[A.BAD_URL_TOKEN=23]="BAD_URL_TOKEN",A[A.CDC_TOKEN=24]="CDC_TOKEN",A[A.CDO_TOKEN=25]="CDO_TOKEN",A[A.COLON_TOKEN=26]="COLON_TOKEN",A[A.SEMICOLON_TOKEN=27]="SEMICOLON_TOKEN",A[A.LEFT_SQUARE_BRACKET_TOKEN=28]="LEFT_SQUARE_BRACKET_TOKEN",A[A.RIGHT_SQUARE_BRACKET_TOKEN=29]="RIGHT_SQUARE_BRACKET_TOKEN",A[A.UNICODE_RANGE_TOKEN=30]="UNICODE_RANGE_TOKEN",A[A.WHITESPACE_TOKEN=31]="WHITESPACE_TOKEN",A[A.EOF_TOKEN=32]="EOF_TOKEN"})(w||(w={}));var Ha=1,Na=2,Ge=4,ps=8,Nt=10,Fs=47,Le=92,Ka=9,Ia=32,Bt=34,Re=61,Ra=35,ba=36,xa=37,ut=39,lt=40,be=41,Oa=95,iA=45,Sa=33,ya=60,Ta=62,Ma=64,La=91,_a=93,Da=61,Va=123,ft=63,Pa=125,ms=124,za=126,Xa=128,Hs=65533,yr=42,YA=43,va=44,Ga=58,Ja=59,Pe=46,Wa=0,ka=8,qa=11,ja=14,$a=31,Za=127,wA=-1,so=48,oo=97,io=101,Ya=102,AB=117,eB=122,co=65,ao=69,Bo=70,tB=85,rB=90,Y=function(A){return A>=so&&A<=57},nB=function(A){return A>=55296&&A<=57343},ne=function(A){return Y(A)||A>=co&&A<=Bo||A>=oo&&A<=Ya},sB=function(A){return A>=oo&&A<=eB},oB=function(A){return A>=co&&A<=rB},iB=function(A){return sB(A)||oB(A)},cB=function(A){return A>=Xa},dt=function(A){return A===Nt||A===Ka||A===Ia},Kt=function(A){return iB(A)||cB(A)||A===Oa},Ns=function(A){return Kt(A)||Y(A)||A===iA},aB=function(A){return A>=Wa&&A<=ka||A===qa||A>=ja&&A<=$a||A===Za},TA=function(A,e){return A!==Le?!1:e!==Nt},ht=function(A,e,t){return A===iA?Kt(e)||TA(e,t):Kt(A)?!0:!!(A===Le&&TA(A,e))},Tr=function(A,e,t){return A===YA||A===iA?Y(e)?!0:e===Pe&&Y(t):Y(A===Pe?e:A)},BB=function(A){let e=0,t=1;(A[e]===YA||A[e]===iA)&&(A[e]===iA&&(t=-1),e++);let r=[];for(;Y(A[e]);)r.push(A[e++]);let n=r.length?parseInt(X.apply(void 0,r),10):0;A[e]===Pe&&e++;let s=[];for(;Y(A[e]);)s.push(A[e++]);let o=s.length,c=o?parseInt(X.apply(void 0,s),10):0;(A[e]===ao||A[e]===io)&&e++;let i=1;(A[e]===YA||A[e]===iA)&&(A[e]===iA&&(i=-1),e++);let a=[];for(;Y(A[e]);)a.push(A[e++]);let B=a.length?parseInt(X.apply(void 0,a),10):0;return t*(n+c*Math.pow(10,-o))*Math.pow(10,i*B)},uB={type:w.LEFT_PARENTHESIS_TOKEN},lB={type:w.RIGHT_PARENTHESIS_TOKEN},fB={type:w.COMMA_TOKEN},dB={type:w.SUFFIX_MATCH_TOKEN},hB={type:w.PREFIX_MATCH_TOKEN},QB={type:w.COLUMN_TOKEN},gB={type:w.DASH_MATCH_TOKEN},wB={type:w.INCLUDE_MATCH_TOKEN},CB={type:w.LEFT_CURLY_BRACKET_TOKEN},UB={type:w.RIGHT_CURLY_BRACKET_TOKEN},EB={type:w.SUBSTRING_MATCH_TOKEN},Qt={type:w.BAD_URL_TOKEN},pB={type:w.BAD_STRING_TOKEN},FB={type:w.CDO_TOKEN},mB={type:w.CDC_TOKEN},HB={type:w.COLON_TOKEN},NB={type:w.SEMICOLON_TOKEN},KB={type:w.LEFT_SQUARE_BRACKET_TOKEN},IB={type:w.RIGHT_SQUARE_BRACKET_TOKEN},RB={type:w.WHITESPACE_TOKEN},jr={type:w.EOF_TOKEN},uo=function(){function A(){this._value=[]}return A.prototype.write=function(e){this._value=this._value.concat(_t(e))},A.prototype.read=function(){let e=[],t=this.consumeToken();for(;t!==jr;)e.push(t),t=this.consumeToken();return e},A.prototype.consumeToken=function(){let e=this.consumeCodePoint();switch(e){case Bt:return this.consumeStringToken(Bt);case Ra:var t=this.peekCodePoint(0),r=this.peekCodePoint(1),n=this.peekCodePoint(2);if(Ns(t)||TA(r,n)){let u=ht(t,r,n)?Na:Ha,h=this.consumeName();return{type:w.HASH_TOKEN,value:h,flags:u}}break;case ba:if(this.peekCodePoint(0)===Re)return this.consumeCodePoint(),dB;break;case ut:return this.consumeStringToken(ut);case lt:return uB;case be:return lB;case yr:if(this.peekCodePoint(0)===Re)return this.consumeCodePoint(),EB;break;case YA:if(Tr(e,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(e),this.consumeNumericToken();break;case va:return fB;case iA:var s=e,o=this.peekCodePoint(0),c=this.peekCodePoint(1);if(Tr(s,o,c))return this.reconsumeCodePoint(e),this.consumeNumericToken();if(ht(s,o,c))return this.reconsumeCodePoint(e),this.consumeIdentLikeToken();if(o===iA&&c===Ta)return this.consumeCodePoint(),this.consumeCodePoint(),mB;break;case Pe:if(Tr(e,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(e),this.consumeNumericToken();break;case Fs:if(this.peekCodePoint(0)===yr)for(this.consumeCodePoint();;){let u=this.consumeCodePoint();if(u===yr&&(u=this.consumeCodePoint(),u===Fs))return this.consumeToken();if(u===wA)return this.consumeToken()}break;case Ga:return HB;case Ja:return NB;case ya:if(this.peekCodePoint(0)===Sa&&this.peekCodePoint(1)===iA&&this.peekCodePoint(2)===iA)return this.consumeCodePoint(),this.consumeCodePoint(),FB;break;case Ma:var i=this.peekCodePoint(0),a=this.peekCodePoint(1),B=this.peekCodePoint(2);if(ht(i,a,B)){var f=this.consumeName();return{type:w.AT_KEYWORD_TOKEN,value:f}}break;case La:return KB;case Le:if(TA(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),this.consumeIdentLikeToken();break;case _a:return IB;case Da:if(this.peekCodePoint(0)===Re)return this.consumeCodePoint(),hB;break;case Va:return CB;case Pa:return UB;case AB:case tB:var d=this.peekCodePoint(0),l=this.peekCodePoint(1);return d===YA&&(ne(l)||l===ft)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(e),this.consumeIdentLikeToken();case ms:if(this.peekCodePoint(0)===Re)return this.consumeCodePoint(),gB;if(this.peekCodePoint(0)===ms)return this.consumeCodePoint(),QB;break;case za:if(this.peekCodePoint(0)===Re)return this.consumeCodePoint(),wB;break;case wA:return jr}return dt(e)?(this.consumeWhiteSpace(),RB):Y(e)?(this.reconsumeCodePoint(e),this.consumeNumericToken()):Kt(e)?(this.reconsumeCodePoint(e),this.consumeIdentLikeToken()):{type:w.DELIM_TOKEN,value:X(e)}},A.prototype.consumeCodePoint=function(){let e=this._value.shift();return typeof e>"u"?-1:e},A.prototype.reconsumeCodePoint=function(e){this._value.unshift(e)},A.prototype.peekCodePoint=function(e){return e>=this._value.length?-1:this._value[e]},A.prototype.consumeUnicodeRangeToken=function(){let e=[],t=this.consumeCodePoint();for(;ne(t)&&e.length<6;)e.push(t),t=this.consumeCodePoint();let r=!1;for(;t===ft&&e.length<6;)e.push(t),t=this.consumeCodePoint(),r=!0;if(r){let o=parseInt(X.apply(void 0,e.map(function(i){return i===ft?so:i})),16),c=parseInt(X.apply(void 0,e.map(function(i){return i===ft?Bo:i})),16);return{type:w.UNICODE_RANGE_TOKEN,start:o,end:c}}let n=parseInt(X.apply(void 0,e),16);if(this.peekCodePoint(0)===iA&&ne(this.peekCodePoint(1))){this.consumeCodePoint(),t=this.consumeCodePoint();let o=[];for(;ne(t)&&o.length<6;)o.push(t),t=this.consumeCodePoint();var s=parseInt(X.apply(void 0,o),16);return{type:w.UNICODE_RANGE_TOKEN,start:n,end:s}}return{type:w.UNICODE_RANGE_TOKEN,start:n,end:n}},A.prototype.consumeIdentLikeToken=function(){let e=this.consumeName();return e.toLowerCase()==="url"&&this.peekCodePoint(0)===lt?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===lt?(this.consumeCodePoint(),{type:w.FUNCTION_TOKEN,value:e}):{type:w.IDENT_TOKEN,value:e}},A.prototype.consumeUrlToken=function(){let e=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===wA)return{type:w.URL_TOKEN,value:""};let t=this.peekCodePoint(0);if(t===ut||t===Bt){let r=this.consumeStringToken(this.consumeCodePoint());return r.type===w.STRING_TOKEN&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===wA||this.peekCodePoint(0)===be)?(this.consumeCodePoint(),{type:w.URL_TOKEN,value:r.value}):(this.consumeBadUrlRemnants(),Qt)}for(;;){let r=this.consumeCodePoint();if(r===wA||r===be)return{type:w.URL_TOKEN,value:X.apply(void 0,e)};if(dt(r))return this.consumeWhiteSpace(),this.peekCodePoint(0)===wA||this.peekCodePoint(0)===be?(this.consumeCodePoint(),{type:w.URL_TOKEN,value:X.apply(void 0,e)}):(this.consumeBadUrlRemnants(),Qt);if(r===Bt||r===ut||r===lt||aB(r))return this.consumeBadUrlRemnants(),Qt;if(r===Le)if(TA(r,this.peekCodePoint(0)))e.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),Qt;else e.push(r)}},A.prototype.consumeWhiteSpace=function(){for(;dt(this.peekCodePoint(0));)this.consumeCodePoint()},A.prototype.consumeBadUrlRemnants=function(){for(;;){let e=this.consumeCodePoint();if(e===be||e===wA)return;TA(e,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},A.prototype.consumeStringSlice=function(e){let r="";for(;e>0;){let n=Math.min(6e4,e);r+=X.apply(void 0,this._value.splice(0,n)),e-=n}return this._value.shift(),r},A.prototype.consumeStringToken=function(e){let t="",r=0;do{let n=this._value[r];if(n===wA||n===void 0||n===e)return t+=this.consumeStringSlice(r),{type:w.STRING_TOKEN,value:t};if(n===Nt)return this._value.splice(0,r),pB;if(n===Le){let s=this._value[r+1];s!==wA&&s!==void 0&&(s===Nt?(t+=this.consumeStringSlice(r),r=-1,this._value.shift()):TA(n,s)&&(t+=this.consumeStringSlice(r),t+=X(this.consumeEscapedCodePoint()),r=-1))}r++}while(!0)},A.prototype.consumeNumber=function(){let e=[],t=Ge,r=this.peekCodePoint(0);for((r===YA||r===iA)&&e.push(this.consumeCodePoint());Y(this.peekCodePoint(0));)e.push(this.consumeCodePoint());r=this.peekCodePoint(0);let n=this.peekCodePoint(1);if(r===Pe&&Y(n))for(e.push(this.consumeCodePoint(),this.consumeCodePoint()),t=ps;Y(this.peekCodePoint(0));)e.push(this.consumeCodePoint());r=this.peekCodePoint(0),n=this.peekCodePoint(1);let s=this.peekCodePoint(2);if((r===ao||r===io)&&((n===YA||n===iA)&&Y(s)||Y(n)))for(e.push(this.consumeCodePoint(),this.consumeCodePoint()),t=ps;Y(this.peekCodePoint(0));)e.push(this.consumeCodePoint());return[BB(e),t]},A.prototype.consumeNumericToken=function(){let e=this.consumeNumber(),t=e[0],r=e[1],n=this.peekCodePoint(0),s=this.peekCodePoint(1),o=this.peekCodePoint(2);if(ht(n,s,o)){let c=this.consumeName();return{type:w.DIMENSION_TOKEN,number:t,flags:r,unit:c}}return n===xa?(this.consumeCodePoint(),{type:w.PERCENTAGE_TOKEN,number:t,flags:r}):{type:w.NUMBER_TOKEN,number:t,flags:r}},A.prototype.consumeEscapedCodePoint=function(){let e=this.consumeCodePoint();if(ne(e)){let t=X(e);for(;ne(this.peekCodePoint(0))&&t.length<6;)t+=X(this.consumeCodePoint());dt(this.peekCodePoint(0))&&this.consumeCodePoint();let r=parseInt(t,16);return r===0||nB(r)||r>1114111?Hs:r}return e===wA?Hs:e},A.prototype.consumeName=function(){let e="";for(;;){let t=this.consumeCodePoint();if(Ns(t))e+=X(t);else if(TA(t,this.peekCodePoint(0)))e+=X(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(t),e}},A}(),cn=function(){function A(e){this._tokens=e}return A.create=function(e){let t=new uo;return t.write(e),new A(t.read())},A.parseValue=function(e){return A.create(e).parseComponentValue()},A.parseValues=function(e){return A.create(e).parseComponentValues()},A.prototype.parseComponentValue=function(){let e=this.consumeToken();for(;e.type===w.WHITESPACE_TOKEN;)e=this.consumeToken();if(e.type===w.EOF_TOKEN)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(e);let t=this.consumeComponentValue();do e=this.consumeToken();while(e.type===w.WHITESPACE_TOKEN);if(e.type===w.EOF_TOKEN)return t;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},A.prototype.parseComponentValues=function(){let e=[];for(;;){let t=this.consumeComponentValue();if(t.type===w.EOF_TOKEN)return e;e.push(t),e.push()}},A.prototype.consumeComponentValue=function(){let e=this.consumeToken();switch(e.type){case w.LEFT_CURLY_BRACKET_TOKEN:case w.LEFT_SQUARE_BRACKET_TOKEN:case w.LEFT_PARENTHESIS_TOKEN:return this.consumeSimpleBlock(e.type);case w.FUNCTION_TOKEN:return this.consumeFunction(e)}return e},A.prototype.consumeSimpleBlock=function(e){let t={type:e,values:[]},r=function(s,o){return o===w.LEFT_CURLY_BRACKET_TOKEN&&s.type===w.RIGHT_CURLY_BRACKET_TOKEN||o===w.LEFT_SQUARE_BRACKET_TOKEN&&s.type===w.RIGHT_SQUARE_BRACKET_TOKEN?!0:o===w.LEFT_PARENTHESIS_TOKEN&&s.type===w.RIGHT_PARENTHESIS_TOKEN},n=this.consumeToken();for(;;){if(n.type===w.EOF_TOKEN||r(n,e))return t;this.reconsumeToken(n),t.values.push(this.consumeComponentValue()),n=this.consumeToken()}},A.prototype.consumeFunction=function(e){let t={name:e.value,values:[],type:w.FUNCTION};for(;;){let r=this.consumeToken();if(r.type===w.EOF_TOKEN||r.type===w.RIGHT_PARENTHESIS_TOKEN)return t;this.reconsumeToken(r),t.values.push(this.consumeComponentValue())}},A.prototype.consumeToken=function(){let e=this._tokens.shift();return typeof e>"u"?jr:e},A.prototype.reconsumeToken=function(e){this._tokens.unshift(e)},A}(),an=function(A){return A.type===w.DIMENSION_TOKEN},he=function(A){return A.type===w.NUMBER_TOKEN},S=function(A){return A.type===w.IDENT_TOKEN},bB=function(A){return A.type===w.STRING_TOKEN},$r=function(A,e){return S(A)&&A.value===e},lo=function(A){return A.type!==w.WHITESPACE_TOKEN},fe=function(A){return A.type!==w.WHITESPACE_TOKEN&&A.type!==w.COMMA_TOKEN},UA=function(A){let e=[],t=[];return A.forEach(function(r){if(r.type===w.COMMA_TOKEN){if(t.length===0)throw new Error("Error parsing function args, zero tokens for arg");e.push(t),t=[];return}r.type!==w.WHITESPACE_TOKEN&&t.push(r)}),t.length&&e.push(t),e},vA=function(A){return A.type===w.NUMBER_TOKEN||A.type===w.DIMENSION_TOKEN},v=function(A){return A.type===w.PERCENTAGE_TOKEN||vA(A)},fo=function(A){return A.length>1?[A[0],A[1]]:[A[0]]},Z={type:w.NUMBER_TOKEN,number:0,flags:Ge},Bn={type:w.PERCENTAGE_TOKEN,number:50,flags:Ge},DA={type:w.PERCENTAGE_TOKEN,number:100,flags:Ge},T=function(A,e){if(A.type===w.PERCENTAGE_TOKEN)return A.number/100*e;if(an(A))switch(A.unit){case"rem":case"em":return 16*A.number;case"px":default:return A.number}return A.number},Te=function(A,e,t){let r=A[0],n=A[1];return[T(r,e),T(typeof n<"u"?n:r,t)]},ho="deg",lA=function(A){return Math.PI*A/180},Qo="grad",go="rad",wo="turn",Dt={name:"angle",parse:function(A){if(A.type===w.DIMENSION_TOKEN)switch(A.unit){case ho:return Math.PI*A.number/180;case Qo:return Math.PI/200*A.number;case go:return A.number;case wo:return Math.PI*2*A.number}throw new Error("Unsupported angle type")}},Co=function(A){return A.type===w.DIMENSION_TOKEN&&(A.unit===ho||A.unit===Qo||A.unit===go||A.unit===wo)},Uo=function(A){switch(A.filter(S).map(function(t){return t.value}).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[Z,Z];case"to top":case"bottom":return lA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[Z,DA];case"to right":case"left":return lA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[DA,DA];case"to bottom":case"top":return lA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[DA,Z];case"to left":case"right":return lA(270)}return 0},RA={name:"color",parse:function(A){if(A.type===w.FUNCTION){let e=xB[A.name];if(typeof e>"u")throw new Error(`Attempting to parse an unsupported color function "${A.name}"`);return e(A.values)}if(A.type===w.HASH_TOKEN){if(A.value.length===3){let e=A.value.substring(0,1),t=A.value.substring(1,2),r=A.value.substring(2,3);return VA(parseInt(e+e,16),parseInt(t+t,16),parseInt(r+r,16),1)}if(A.value.length===4){let e=A.value.substring(0,1),t=A.value.substring(1,2),r=A.value.substring(2,3),n=A.value.substring(3,4);return VA(parseInt(e+e,16),parseInt(t+t,16),parseInt(r+r,16),parseInt(n+n,16)/255)}if(A.value.length===6){let e=A.value.substring(0,2),t=A.value.substring(2,4),r=A.value.substring(4,6);return VA(parseInt(e,16),parseInt(t,16),parseInt(r,16),1)}if(A.value.length===8){let e=A.value.substring(0,2),t=A.value.substring(2,4),r=A.value.substring(4,6),n=A.value.substring(6,8);return VA(parseInt(e,16),parseInt(t,16),parseInt(r,16),parseInt(n,16)/255)}}if(A.type===w.IDENT_TOKEN){let e=HA[A.value.toUpperCase()];if(typeof e<"u")return e}return HA.TRANSPARENT}},zA=function(A){return(255&A)===0},eA=function(A){let e=255&A,t=255&A>>8,r=255&A>>16,n=255&A>>24;return e<255?`rgba(${n},${r},${t},${e/255})`:`rgb(${n},${r},${t})`},VA=function(A,e,t,r){return(A<<24|e<<16|t<<8|Math.round(r*255)<<0)>>>0},Ks=function(A,e){if(A.type===w.NUMBER_TOKEN)return A.number;if(A.type===w.PERCENTAGE_TOKEN){let t=e===3?1:255;return e===3?A.number/100*t:Math.round(A.number/100*t)}return 0},Is=function(A){let e=A.filter(fe);if(e.length===3){let t=e.map(Ks),r=t[0],n=t[1],s=t[2];return VA(r,n,s,1)}if(e.length===4){let t=e.map(Ks),r=t[0],n=t[1],s=t[2],o=t[3];return VA(r,n,s,o)}return 0};function Mr(A,e,t){return t<0&&(t+=1),t>=1&&(t-=1),t<1/6?(e-A)*t*6+A:t<1/2?e:t<2/3?(e-A)*6*(2/3-t)+A:A}var Rs=function(A){let e=A.filter(fe),t=e[0],r=e[1],n=e[2],s=e[3],o=(t.type===w.NUMBER_TOKEN?lA(t.number):Dt.parse(t))/(Math.PI*2),c=v(r)?r.number/100:0,i=v(n)?n.number/100:0,a=typeof s<"u"&&v(s)?T(s,1):1;if(c===0)return VA(i*255,i*255,i*255,1);let B=i<=.5?i*(c+1):i+c-i*c,f=i*2-B,d=Mr(f,B,o+1/3),l=Mr(f,B,o),u=Mr(f,B,o-1/3);return VA(d*255,l*255,u*255,a)},xB={hsl:Rs,hsla:Rs,rgb:Is,rgba:Is},HA={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},m;(function(A){A[A.VALUE=0]="VALUE",A[A.LIST=1]="LIST",A[A.IDENT_VALUE=2]="IDENT_VALUE",A[A.TYPE_VALUE=3]="TYPE_VALUE",A[A.TOKEN_VALUE=4]="TOKEN_VALUE"})(m||(m={}));var hA;(function(A){A[A.BORDER_BOX=0]="BORDER_BOX",A[A.PADDING_BOX=1]="PADDING_BOX",A[A.CONTENT_BOX=2]="CONTENT_BOX"})(hA||(hA={}));var OB={name:"background-clip",initialValue:"border-box",prefix:!1,type:m.LIST,parse:function(A){return A.map(function(e){if(S(e))switch(e.value){case"padding-box":return hA.PADDING_BOX;case"content-box":return hA.CONTENT_BOX}return hA.BORDER_BOX})}},SB={name:"background-color",initialValue:"transparent",prefix:!1,type:m.TYPE_VALUE,format:"color"},Vt=function(A){let e=RA.parse(A[0]),t=A[1];return t&&v(t)?{color:e,stop:t}:{color:e,stop:null}},bs=function(A,e){let t=A[0],r=A[A.length-1];t.stop===null&&(t.stop=Z),r.stop===null&&(r.stop=DA);let n=[],s=0;for(let i=0;i<A.length;i++){let a=A[i].stop;if(a!==null){let B=T(a,e);B>s?n.push(B):n.push(s),s=B}else n.push(null)}let o=null;for(var c=0;c<n.length;c++){let i=n[c];if(i===null)o===null&&(o=c);else if(o!==null){let a=c-o,B=n[o-1],f=(i-B)/(a+1);for(let d=1;d<=a;d++)n[o+d-1]=f*d;o=null}}return A.map(function(i,a){return{color:i.color,stop:Math.max(Math.min(1,n[a]/e),0)}})},yB=function(A,e,t){let r=e/2,n=t/2,s=T(A[0],e)-r,o=n-T(A[1],t);return(Math.atan2(o,s)+Math.PI*2)%(Math.PI*2)},TB=function(A,e,t){let r=typeof A=="number"?A:yB(A,e,t),n=Math.abs(e*Math.sin(r))+Math.abs(t*Math.cos(r)),s=e/2,o=t/2,c=n/2,i=Math.sin(r-Math.PI/2)*c,a=Math.cos(r-Math.PI/2)*c;return[n,s-a,s+a,o-i,o+i]},dA=function(A,e){return Math.sqrt(A*A+e*e)},xs=function(A,e,t,r,n){return[[0,0],[0,e],[A,0],[A,e]].reduce(function(o,c){let i=c[0],a=c[1],B=dA(t-i,r-a);return(n?B<o.optimumDistance:B>o.optimumDistance)?{optimumCorner:c,optimumDistance:B}:o},{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},MB=function(A,e,t,r,n){let s=0,o=0;switch(A.size){case $.CLOSEST_SIDE:A.shape===q.CIRCLE?s=o=Math.min(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):A.shape===q.ELLIPSE&&(s=Math.min(Math.abs(e),Math.abs(e-r)),o=Math.min(Math.abs(t),Math.abs(t-n)));break;case $.CLOSEST_CORNER:if(A.shape===q.CIRCLE)s=o=Math.min(dA(e,t),dA(e,t-n),dA(e-r,t),dA(e-r,t-n));else if(A.shape===q.ELLIPSE){let f=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(e),Math.abs(e-r)),d=xs(r,n,e,t,!0),l=d[0],u=d[1];s=dA(l-e,(u-t)/f),o=f*s}break;case $.FARTHEST_SIDE:A.shape===q.CIRCLE?s=o=Math.max(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):A.shape===q.ELLIPSE&&(s=Math.max(Math.abs(e),Math.abs(e-r)),o=Math.max(Math.abs(t),Math.abs(t-n)));break;case $.FARTHEST_CORNER:if(A.shape===q.CIRCLE)s=o=Math.max(dA(e,t),dA(e,t-n),dA(e-r,t),dA(e-r,t-n));else if(A.shape===q.ELLIPSE){var c=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(e),Math.abs(e-r)),i=xs(r,n,e,t,!1),a=i[0],B=i[1];s=dA(a-e,(B-t)/c),o=c*s}break}return Array.isArray(A.size)&&(s=T(A.size[0],r),o=A.size.length===2?T(A.size[1],n):s),[s,o]},LB=function(A){let e=lA(180),t=[];return UA(A).forEach(function(r,n){if(n===0){let o=r[0];if(o.type===w.IDENT_TOKEN&&o.value==="to"){e=Uo(r);return}else if(Co(o)){e=Dt.parse(o);return}}let s=Vt(r);t.push(s)}),{angle:e,stops:t,type:tA.LINEAR_GRADIENT}},gt=function(A){let e=lA(180),t=[];return UA(A).forEach(function(r,n){if(n===0){let o=r[0];if(o.type===w.IDENT_TOKEN&&["top","left","right","bottom"].indexOf(o.value)!==-1){e=Uo(r);return}else if(Co(o)){e=(Dt.parse(o)+lA(270))%lA(360);return}}let s=Vt(r);t.push(s)}),{angle:e,stops:t,type:tA.LINEAR_GRADIENT}},_B=function(A){if(A.createRange){let t=A.createRange();if(t.getBoundingClientRect){let r=A.createElement("boundtest");r.style.height="123px",r.style.display="block",A.body.appendChild(r),t.selectNode(r);let n=t.getBoundingClientRect(),s=Math.round(n.height);if(A.body.removeChild(r),s===123)return!0}}return!1},DB=function(){return typeof new Image().crossOrigin<"u"},VB=function(){return typeof new XMLHttpRequest().responseType=="string"},PB=function(A){let e=new Image,t=A.createElement("canvas"),r=t.getContext("2d");if(!r)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(e,0,0),t.toDataURL()}catch{return!1}return!0},Os=function(A){return A[0]===0&&A[1]===255&&A[2]===0&&A[3]===255},zB=function(A){let e=A.createElement("canvas"),t=100;e.width=t,e.height=t;let r=e.getContext("2d");if(!r)return Promise.reject(!1);r.fillStyle="rgb(0, 255, 0)",r.fillRect(0,0,t,t);let n=new Image,s=e.toDataURL();n.src=s;let o=Zr(t,t,0,0,n);return r.fillStyle="red",r.fillRect(0,0,t,t),Ss(o).then(function(c){r.drawImage(c,0,0);let i=r.getImageData(0,0,t,t).data;r.fillStyle="red",r.fillRect(0,0,t,t);let a=A.createElement("div");return a.style.backgroundImage=`url(${s})`,a.style.height=`${t}px`,Os(i)?Ss(Zr(t,t,0,0,a)):Promise.reject(!1)}).then(function(c){return r.drawImage(c,0,0),Os(r.getImageData(0,0,t,t).data)}).catch(function(){return!1})},Zr=function(A,e,t,r,n){let s="http://www.w3.org/2000/svg",o=document.createElementNS(s,"svg"),c=document.createElementNS(s,"foreignObject");return o.setAttributeNS(null,"width",A.toString()),o.setAttributeNS(null,"height",e.toString()),c.setAttributeNS(null,"width","100%"),c.setAttributeNS(null,"height","100%"),c.setAttributeNS(null,"x",t.toString()),c.setAttributeNS(null,"y",r.toString()),c.setAttributeNS(null,"externalResourcesRequired","true"),o.appendChild(c),c.appendChild(n),o},Ss=function(A){return new Promise(function(e,t){let r=new Image;r.onload=function(){return e(r)},r.onerror=t,r.src=`data:image/svg+xml;charset=utf-8,${encodeURIComponent(new XMLSerializer().serializeToString(A))}`})},uA={get SUPPORT_RANGE_BOUNDS(){let A=_B(document);return Object.defineProperty(uA,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_SVG_DRAWING(){let A=PB(document);return Object.defineProperty(uA,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){let A=typeof Array.from=="function"&&typeof window.fetch=="function"?zB(document):Promise.resolve(!1);return Object.defineProperty(uA,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){let A=DB();return Object.defineProperty(uA,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){let A=VB();return Object.defineProperty(uA,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){let A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(uA,"SUPPORT_CORS_XHR",{value:A}),A}},W=function(){function A(e){let t=e.id,r=e.enabled;this.id=t,this.enabled=r,this.start=Date.now()}return A.prototype.debug=function(){let e=[];for(let t=0;t<arguments.length;t++)e[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,[this.id,`${this.getTime()}ms`].concat(e)):this.info.apply(this,e))},A.prototype.getTime=function(){return Date.now()-this.start},A.create=function(e){A.instances[e.id]=new A(e)},A.destroy=function(e){delete A.instances[e]},A.getInstance=function(e){let t=A.instances[e];if(typeof t>"u")throw new Error(`No logger instance found with id ${e}`);return t},A.prototype.info=function(){let e=[];for(let t=0;t<arguments.length;t++)e[t]=arguments[t];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,[this.id,`${this.getTime()}ms`].concat(e))},A.prototype.error=function(){let e=[];for(let t=0;t<arguments.length;t++)e[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,[this.id,`${this.getTime()}ms`].concat(e)):this.info.apply(this,e))},A.instances={},A}(),NA=function(){function A(){}return A.create=function(e,t){return A._caches[e]=new XB(e,t)},A.destroy=function(e){delete A._caches[e]},A.open=function(e){let t=A._caches[e];if(typeof t<"u")return t;throw new Error(`Cache with key "${e}" not found`)},A.getOrigin=function(e){let t=A._link;return t?(t.href=e,t.href=t.href,t.protocol+t.hostname+t.port):"about:blank"},A.isSameOrigin=function(e){return A.getOrigin(e)===A._origin},A.setContext=function(e){A._link=e.document.createElement("a"),A._origin=A.getOrigin(e.location.href)},A.getInstance=function(){let e=A._current;if(e===null)throw new Error("No cache instance attached");return e},A.attachInstance=function(e){A._current=e},A.detachInstance=function(){A._current=null},A._caches={},A._origin="about:blank",A._current=null,A}(),XB=function(){function A(e,t){this.id=e,this._options=t,this._cache={}}return A.prototype.addImage=function(e){let t=Promise.resolve();return this.has(e)||(qB(e)||WB(e))&&(this._cache[e]=this.loadImage(e)),t},A.prototype.match=function(e){return this._cache[e]},A.prototype.loadImage=function(e){return cA(this,void 0,void 0,function(){let t,r,n,s,o=this;return oA(this,function(c){switch(c.label){case 0:return t=NA.isSameOrigin(e),r=!Lr(e)&&this._options.useCORS===!0&&uA.SUPPORT_CORS_IMAGES&&!t,n=!Lr(e)&&!t&&typeof this._options.proxy=="string"&&uA.SUPPORT_CORS_XHR&&!r,!t&&this._options.allowTaint===!1&&!Lr(e)&&!n&&!r?[2]:(s=e,n?[4,this.proxy(s)]:[3,2]);case 1:s=c.sent(),c.label=2;case 2:return W.getInstance(this.id).debug(`Added image ${e.substring(0,256)}`),[4,new Promise(function(i,a){let B=new Image;B.onload=function(){return i(B)},B.onerror=a,(kB(s)||r)&&(B.crossOrigin="anonymous"),B.src=s,B.complete===!0&&setTimeout(function(){return i(B)},500),o._options.imageTimeout>0&&setTimeout(function(){return a(`Timed out (${o._options.imageTimeout}ms) loading image`)},o._options.imageTimeout)})];case 3:return[2,c.sent()]}})})},A.prototype.has=function(e){return typeof this._cache[e]<"u"},A.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},A.prototype.proxy=function(e){let t=this,r=this._options.proxy;if(!r)throw new Error("No proxy defined");let n=e.substring(0,256);return new Promise(function(s,o){let c=uA.SUPPORT_RESPONSE_TYPE?"blob":"text",i=new XMLHttpRequest;if(i.onload=function(){if(i.status===200)if(c==="text")s(i.response);else{let a=new FileReader;a.addEventListener("load",function(){return s(a.result)},!1),a.addEventListener("error",function(B){return o(B)},!1),a.readAsDataURL(i.response)}else o(`Failed to proxy resource ${n} with status code ${i.status}`)},i.onerror=o,i.open("GET",`${r}?url=${encodeURIComponent(e)}&responseType=${c}`),c!=="text"&&i instanceof XMLHttpRequest&&(i.responseType=c),t._options.imageTimeout){let a=t._options.imageTimeout;i.timeout=a,i.ontimeout=function(){return o(`Timed out (${a}ms) proxying ${n}`)}}i.send()})},A}(),vB=/^data:image\/svg\+xml/i,GB=/^data:image\/.*;base64,/i,JB=/^data:image\/.*/i,WB=function(A){return uA.SUPPORT_SVG_DRAWING||!jB(A)},Lr=function(A){return JB.test(A)},kB=function(A){return GB.test(A)},qB=function(A){return A.substr(0,4)==="blob"},jB=function(A){return A.substr(-3).toLowerCase()==="svg"||vB.test(A)},$B=function(A){let e=lA(180),t=[],r=tA.LINEAR_GRADIENT,n=q.CIRCLE,s=$.FARTHEST_CORNER,o=[];return UA(A).forEach(function(c,i){let a=c[0];if(i===0){if(S(a)&&a.value==="linear"){r=tA.LINEAR_GRADIENT;return}else if(S(a)&&a.value==="radial"){r=tA.RADIAL_GRADIENT;return}}if(a.type===w.FUNCTION){if(a.name==="from"){let B=RA.parse(a.values[0]);t.push({stop:Z,color:B})}else if(a.name==="to"){let B=RA.parse(a.values[0]);t.push({stop:DA,color:B})}else if(a.name==="color-stop"){let B=a.values.filter(fe);if(B.length===2){let f=RA.parse(B[1]),d=B[0];he(d)&&t.push({stop:{type:w.PERCENTAGE_TOKEN,number:d.number*100,flags:d.flags},color:f})}}}}),r===tA.LINEAR_GRADIENT?{angle:(e+lA(180))%lA(360),stops:t,type:r}:{size:s,shape:n,stops:t,position:o,type:r}},Eo="closest-side",po="farthest-side",Fo="closest-corner",mo="farthest-corner",Ho="circle",No="ellipse",Ko="cover",Io="contain",ZB=function(A){let e=q.CIRCLE,t=$.FARTHEST_CORNER,r=[],n=[];return UA(A).forEach(function(s,o){let c=!0;if(o===0){let i=!1;c=s.reduce(function(a,B){if(i)if(S(B))switch(B.value){case"center":return n.push(Bn),a;case"top":case"left":return n.push(Z),a;case"right":case"bottom":return n.push(DA),a}else(v(B)||vA(B))&&n.push(B);else if(S(B))switch(B.value){case Ho:return e=q.CIRCLE,!1;case No:return e=q.ELLIPSE,!1;case"at":return i=!0,!1;case Eo:return t=$.CLOSEST_SIDE,!1;case Ko:case po:return t=$.FARTHEST_SIDE,!1;case Io:case Fo:return t=$.CLOSEST_CORNER,!1;case mo:return t=$.FARTHEST_CORNER,!1}else if(vA(B)||v(B))return Array.isArray(t)||(t=[]),t.push(B),!1;return a},c)}if(c){let i=Vt(s);r.push(i)}}),{size:t,shape:e,stops:r,position:n,type:tA.RADIAL_GRADIENT}},wt=function(A){let e=q.CIRCLE,t=$.FARTHEST_CORNER,r=[],n=[];return UA(A).forEach(function(s,o){let c=!0;if(o===0?c=s.reduce(function(i,a){if(S(a))switch(a.value){case"center":return n.push(Bn),!1;case"top":case"left":return n.push(Z),!1;case"right":case"bottom":return n.push(DA),!1}else if(v(a)||vA(a))return n.push(a),!1;return i},c):o===1&&(c=s.reduce(function(i,a){if(S(a))switch(a.value){case Ho:return e=q.CIRCLE,!1;case No:return e=q.ELLIPSE,!1;case Io:case Eo:return t=$.CLOSEST_SIDE,!1;case po:return t=$.FARTHEST_SIDE,!1;case Fo:return t=$.CLOSEST_CORNER,!1;case Ko:case mo:return t=$.FARTHEST_CORNER,!1}else if(vA(a)||v(a))return Array.isArray(t)||(t=[]),t.push(a),!1;return i},c)),c){let i=Vt(s);r.push(i)}}),{size:t,shape:e,stops:r,position:n,type:tA.RADIAL_GRADIENT}},tA;(function(A){A[A.URL=0]="URL",A[A.LINEAR_GRADIENT=1]="LINEAR_GRADIENT",A[A.RADIAL_GRADIENT=2]="RADIAL_GRADIENT"})(tA||(tA={}));var YB=function(A){return A.type===tA.LINEAR_GRADIENT},Au=function(A){return A.type===tA.RADIAL_GRADIENT},q;(function(A){A[A.CIRCLE=0]="CIRCLE",A[A.ELLIPSE=1]="ELLIPSE"})(q||(q={}));var $;(function(A){A[A.CLOSEST_SIDE=0]="CLOSEST_SIDE",A[A.FARTHEST_SIDE=1]="FARTHEST_SIDE",A[A.CLOSEST_CORNER=2]="CLOSEST_CORNER",A[A.FARTHEST_CORNER=3]="FARTHEST_CORNER"})($||($={}));var un={name:"image",parse:function(A){if(A.type===w.URL_TOKEN){let e={url:A.value,type:tA.URL};return NA.getInstance().addImage(A.value),e}if(A.type===w.FUNCTION){let e=Ro[A.name];if(typeof e>"u")throw new Error(`Attempting to parse an unsupported image function "${A.name}"`);return e(A.values)}throw new Error("Unsupported image type")}};function eu(A){return A.type!==w.FUNCTION||Ro[A.name]}var Ro={"linear-gradient":LB,"-moz-linear-gradient":gt,"-ms-linear-gradient":gt,"-o-linear-gradient":gt,"-webkit-linear-gradient":gt,"radial-gradient":ZB,"-moz-radial-gradient":wt,"-ms-radial-gradient":wt,"-o-radial-gradient":wt,"-webkit-radial-gradient":wt,"-webkit-gradient":$B},tu={name:"background-image",initialValue:"none",type:m.LIST,prefix:!1,parse:function(A){if(A.length===0)return[];let e=A[0];return e.type===w.IDENT_TOKEN&&e.value==="none"?[]:A.filter(function(t){return fe(t)&&eu(t)}).map(un.parse)}},ru={name:"background-origin",initialValue:"border-box",prefix:!1,type:m.LIST,parse:function(A){return A.map(function(e){if(S(e))switch(e.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},nu={name:"background-position",initialValue:"0% 0%",type:m.LIST,prefix:!1,parse:function(A){return UA(A).map(function(e){return e.filter(v)}).map(fo)}},KA;(function(A){A[A.REPEAT=0]="REPEAT",A[A.NO_REPEAT=1]="NO_REPEAT",A[A.REPEAT_X=2]="REPEAT_X",A[A.REPEAT_Y=3]="REPEAT_Y"})(KA||(KA={}));var su={name:"background-repeat",initialValue:"repeat",prefix:!1,type:m.LIST,parse:function(A){return UA(A).map(function(e){return e.filter(S).map(function(t){return t.value}).join(" ")}).map(ou)}},ou=function(A){switch(A){case"no-repeat":return KA.NO_REPEAT;case"repeat-x":case"repeat no-repeat":return KA.REPEAT_X;case"repeat-y":case"no-repeat repeat":return KA.REPEAT_Y;case"repeat":default:return KA.REPEAT}},ue;(function(A){A.AUTO="auto",A.CONTAIN="contain",A.COVER="cover"})(ue||(ue={}));var iu={name:"background-size",initialValue:"0",prefix:!1,type:m.LIST,parse:function(A){return UA(A).map(function(e){return e.filter(cu)})}},cu=function(A){return S(A)||v(A)},Pt=function(A){return{name:`border-${A}-color`,initialValue:"transparent",prefix:!1,type:m.TYPE_VALUE,format:"color"}},au=Pt("top"),Bu=Pt("right"),uu=Pt("bottom"),lu=Pt("left"),zt=function(A){return{name:`border-radius-${A}`,initialValue:"0 0",prefix:!1,type:m.LIST,parse:function(e){return fo(e.filter(v))}}},fu=zt("top-left"),du=zt("top-right"),hu=zt("bottom-right"),Qu=zt("bottom-left"),de;(function(A){A[A.NONE=0]="NONE",A[A.SOLID=1]="SOLID"})(de||(de={}));var Xt=function(A){return{name:`border-${A}-style`,initialValue:"solid",prefix:!1,type:m.IDENT_VALUE,parse:function(e){switch(e){case"none":return de.NONE}return de.SOLID}}},gu=Xt("top"),wu=Xt("right"),Cu=Xt("bottom"),Uu=Xt("left"),vt=function(A){return{name:`border-${A}-width`,initialValue:"0",type:m.VALUE,prefix:!1,parse:function(e){return an(e)?e.number:0}}},Eu=vt("top"),pu=vt("right"),Fu=vt("bottom"),mu=vt("left"),Hu={name:"color",initialValue:"transparent",prefix:!1,type:m.TYPE_VALUE,format:"color"},Nu={name:"display",initialValue:"inline-block",prefix:!1,type:m.LIST,parse:function(A){return A.filter(S).reduce(function(e,t){return e|Ku(t.value)},0)}},Ku=function(A){switch(A){case"block":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},MA;(function(A){A[A.NONE=0]="NONE",A[A.LEFT=1]="LEFT",A[A.RIGHT=2]="RIGHT",A[A.INLINE_START=3]="INLINE_START",A[A.INLINE_END=4]="INLINE_END"})(MA||(MA={}));var Iu={name:"float",initialValue:"none",prefix:!1,type:m.IDENT_VALUE,parse:function(A){switch(A){case"left":return MA.LEFT;case"right":return MA.RIGHT;case"inline-start":return MA.INLINE_START;case"inline-end":return MA.INLINE_END}return MA.NONE}},Ru={name:"letter-spacing",initialValue:"0",prefix:!1,type:m.VALUE,parse:function(A){return A.type===w.IDENT_TOKEN&&A.value==="normal"?0:A.type===w.NUMBER_TOKEN||A.type===w.DIMENSION_TOKEN?A.number:0}},It;(function(A){A.NORMAL="normal",A.STRICT="strict"})(It||(It={}));var bu={name:"line-break",initialValue:"normal",prefix:!1,type:m.IDENT_VALUE,parse:function(A){switch(A){case"strict":return It.STRICT;case"normal":default:return It.NORMAL}}},xu={name:"line-height",initialValue:"normal",prefix:!1,type:m.TOKEN_VALUE},Ou=function(A,e){return S(A)&&A.value==="normal"?1.2*e:A.type===w.NUMBER_TOKEN?e*A.number:v(A)?T(A,e):e},Su={name:"list-style-image",initialValue:"none",type:m.VALUE,prefix:!1,parse:function(A){return A.type===w.IDENT_TOKEN&&A.value==="none"?null:un.parse(A)}},Rt;(function(A){A[A.INSIDE=0]="INSIDE",A[A.OUTSIDE=1]="OUTSIDE"})(Rt||(Rt={}));var yu={name:"list-style-position",initialValue:"outside",prefix:!1,type:m.IDENT_VALUE,parse:function(A){switch(A){case"inside":return Rt.INSIDE;case"outside":default:return Rt.OUTSIDE}}},g;(function(A){A[A.NONE=-1]="NONE",A[A.DISC=0]="DISC",A[A.CIRCLE=1]="CIRCLE",A[A.SQUARE=2]="SQUARE",A[A.DECIMAL=3]="DECIMAL",A[A.CJK_DECIMAL=4]="CJK_DECIMAL",A[A.DECIMAL_LEADING_ZERO=5]="DECIMAL_LEADING_ZERO",A[A.LOWER_ROMAN=6]="LOWER_ROMAN",A[A.UPPER_ROMAN=7]="UPPER_ROMAN",A[A.LOWER_GREEK=8]="LOWER_GREEK",A[A.LOWER_ALPHA=9]="LOWER_ALPHA",A[A.UPPER_ALPHA=10]="UPPER_ALPHA",A[A.ARABIC_INDIC=11]="ARABIC_INDIC",A[A.ARMENIAN=12]="ARMENIAN",A[A.BENGALI=13]="BENGALI",A[A.CAMBODIAN=14]="CAMBODIAN",A[A.CJK_EARTHLY_BRANCH=15]="CJK_EARTHLY_BRANCH",A[A.CJK_HEAVENLY_STEM=16]="CJK_HEAVENLY_STEM",A[A.CJK_IDEOGRAPHIC=17]="CJK_IDEOGRAPHIC",A[A.DEVANAGARI=18]="DEVANAGARI",A[A.ETHIOPIC_NUMERIC=19]="ETHIOPIC_NUMERIC",A[A.GEORGIAN=20]="GEORGIAN",A[A.GUJARATI=21]="GUJARATI",A[A.GURMUKHI=22]="GURMUKHI",A[A.HEBREW=22]="HEBREW",A[A.HIRAGANA=23]="HIRAGANA",A[A.HIRAGANA_IROHA=24]="HIRAGANA_IROHA",A[A.JAPANESE_FORMAL=25]="JAPANESE_FORMAL",A[A.JAPANESE_INFORMAL=26]="JAPANESE_INFORMAL",A[A.KANNADA=27]="KANNADA",A[A.KATAKANA=28]="KATAKANA",A[A.KATAKANA_IROHA=29]="KATAKANA_IROHA",A[A.KHMER=30]="KHMER",A[A.KOREAN_HANGUL_FORMAL=31]="KOREAN_HANGUL_FORMAL",A[A.KOREAN_HANJA_FORMAL=32]="KOREAN_HANJA_FORMAL",A[A.KOREAN_HANJA_INFORMAL=33]="KOREAN_HANJA_INFORMAL",A[A.LAO=34]="LAO",A[A.LOWER_ARMENIAN=35]="LOWER_ARMENIAN",A[A.MALAYALAM=36]="MALAYALAM",A[A.MONGOLIAN=37]="MONGOLIAN",A[A.MYANMAR=38]="MYANMAR",A[A.ORIYA=39]="ORIYA",A[A.PERSIAN=40]="PERSIAN",A[A.SIMP_CHINESE_FORMAL=41]="SIMP_CHINESE_FORMAL",A[A.SIMP_CHINESE_INFORMAL=42]="SIMP_CHINESE_INFORMAL",A[A.TAMIL=43]="TAMIL",A[A.TELUGU=44]="TELUGU",A[A.THAI=45]="THAI",A[A.TIBETAN=46]="TIBETAN",A[A.TRAD_CHINESE_FORMAL=47]="TRAD_CHINESE_FORMAL",A[A.TRAD_CHINESE_INFORMAL=48]="TRAD_CHINESE_INFORMAL",A[A.UPPER_ARMENIAN=49]="UPPER_ARMENIAN",A[A.DISCLOSURE_OPEN=50]="DISCLOSURE_OPEN",A[A.DISCLOSURE_CLOSED=51]="DISCLOSURE_CLOSED"})(g||(g={}));var Yr={name:"list-style-type",initialValue:"none",prefix:!1,type:m.IDENT_VALUE,parse:function(A){switch(A){case"disc":return g.DISC;case"circle":return g.CIRCLE;case"square":return g.SQUARE;case"decimal":return g.DECIMAL;case"cjk-decimal":return g.CJK_DECIMAL;case"decimal-leading-zero":return g.DECIMAL_LEADING_ZERO;case"lower-roman":return g.LOWER_ROMAN;case"upper-roman":return g.UPPER_ROMAN;case"lower-greek":return g.LOWER_GREEK;case"lower-alpha":return g.LOWER_ALPHA;case"upper-alpha":return g.UPPER_ALPHA;case"arabic-indic":return g.ARABIC_INDIC;case"armenian":return g.ARMENIAN;case"bengali":return g.BENGALI;case"cambodian":return g.CAMBODIAN;case"cjk-earthly-branch":return g.CJK_EARTHLY_BRANCH;case"cjk-heavenly-stem":return g.CJK_HEAVENLY_STEM;case"cjk-ideographic":return g.CJK_IDEOGRAPHIC;case"devanagari":return g.DEVANAGARI;case"ethiopic-numeric":return g.ETHIOPIC_NUMERIC;case"georgian":return g.GEORGIAN;case"gujarati":return g.GUJARATI;case"gurmukhi":return g.GURMUKHI;case"hebrew":return g.HEBREW;case"hiragana":return g.HIRAGANA;case"hiragana-iroha":return g.HIRAGANA_IROHA;case"japanese-formal":return g.JAPANESE_FORMAL;case"japanese-informal":return g.JAPANESE_INFORMAL;case"kannada":return g.KANNADA;case"katakana":return g.KATAKANA;case"katakana-iroha":return g.KATAKANA_IROHA;case"khmer":return g.KHMER;case"korean-hangul-formal":return g.KOREAN_HANGUL_FORMAL;case"korean-hanja-formal":return g.KOREAN_HANJA_FORMAL;case"korean-hanja-informal":return g.KOREAN_HANJA_INFORMAL;case"lao":return g.LAO;case"lower-armenian":return g.LOWER_ARMENIAN;case"malayalam":return g.MALAYALAM;case"mongolian":return g.MONGOLIAN;case"myanmar":return g.MYANMAR;case"oriya":return g.ORIYA;case"persian":return g.PERSIAN;case"simp-chinese-formal":return g.SIMP_CHINESE_FORMAL;case"simp-chinese-informal":return g.SIMP_CHINESE_INFORMAL;case"tamil":return g.TAMIL;case"telugu":return g.TELUGU;case"thai":return g.THAI;case"tibetan":return g.TIBETAN;case"trad-chinese-formal":return g.TRAD_CHINESE_FORMAL;case"trad-chinese-informal":return g.TRAD_CHINESE_INFORMAL;case"upper-armenian":return g.UPPER_ARMENIAN;case"disclosure-open":return g.DISCLOSURE_OPEN;case"disclosure-closed":return g.DISCLOSURE_CLOSED;case"none":default:return g.NONE}}},Gt=function(A){return{name:`margin-${A}`,initialValue:"0",prefix:!1,type:m.TOKEN_VALUE}},Tu=Gt("top"),Mu=Gt("right"),Lu=Gt("bottom"),_u=Gt("left"),PA;(function(A){A[A.VISIBLE=0]="VISIBLE",A[A.HIDDEN=1]="HIDDEN",A[A.SCROLL=2]="SCROLL",A[A.AUTO=3]="AUTO"})(PA||(PA={}));var Du={name:"overflow",initialValue:"visible",prefix:!1,type:m.LIST,parse:function(A){return A.filter(S).map(function(e){switch(e.value){case"hidden":return PA.HIDDEN;case"scroll":return PA.SCROLL;case"auto":return PA.AUTO;case"visible":default:return PA.VISIBLE}})}},ze;(function(A){A.NORMAL="normal",A.BREAK_WORD="break-word"})(ze||(ze={}));var Vu={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:m.IDENT_VALUE,parse:function(A){switch(A){case"break-word":return ze.BREAK_WORD;case"normal":default:return ze.NORMAL}}},Jt=function(A){return{name:`padding-${A}`,initialValue:"0",prefix:!1,type:m.TYPE_VALUE,format:"length-percentage"}},Pu=Jt("top"),zu=Jt("right"),Xu=Jt("bottom"),vu=Jt("left"),CA;(function(A){A[A.LEFT=0]="LEFT",A[A.CENTER=1]="CENTER",A[A.RIGHT=2]="RIGHT"})(CA||(CA={}));var Gu={name:"text-align",initialValue:"left",prefix:!1,type:m.IDENT_VALUE,parse:function(A){switch(A){case"right":return CA.RIGHT;case"center":case"justify":return CA.CENTER;case"left":default:return CA.LEFT}}},LA;(function(A){A[A.STATIC=0]="STATIC",A[A.RELATIVE=1]="RELATIVE",A[A.ABSOLUTE=2]="ABSOLUTE",A[A.FIXED=3]="FIXED",A[A.STICKY=4]="STICKY"})(LA||(LA={}));var Ju={name:"position",initialValue:"static",prefix:!1,type:m.IDENT_VALUE,parse:function(A){switch(A){case"relative":return LA.RELATIVE;case"absolute":return LA.ABSOLUTE;case"fixed":return LA.FIXED;case"sticky":return LA.STICKY}return LA.STATIC}},Wu={name:"text-shadow",initialValue:"none",type:m.LIST,prefix:!1,parse:function(A){return A.length===1&&$r(A[0],"none")?[]:UA(A).map(function(e){let t={color:HA.TRANSPARENT,offsetX:Z,offsetY:Z,blur:Z},r=0;for(let n=0;n<e.length;n++){let s=e[n];vA(s)?(r===0?t.offsetX=s:r===1?t.offsetY=s:t.blur=s,r++):t.color=RA.parse(s)}return t})}},IA;(function(A){A[A.NONE=0]="NONE",A[A.LOWERCASE=1]="LOWERCASE",A[A.UPPERCASE=2]="UPPERCASE",A[A.CAPITALIZE=3]="CAPITALIZE"})(IA||(IA={}));var ku={name:"text-transform",initialValue:"none",prefix:!1,type:m.IDENT_VALUE,parse:function(A){switch(A){case"uppercase":return IA.UPPERCASE;case"lowercase":return IA.LOWERCASE;case"capitalize":return IA.CAPITALIZE}return IA.NONE}},qu={name:"transform",initialValue:"none",prefix:!0,type:m.VALUE,parse:function(A){if(A.type===w.IDENT_TOKEN&&A.value==="none")return null;if(A.type===w.FUNCTION){let e=Zu[A.name];if(typeof e>"u")throw new Error(`Attempting to parse an unsupported transform function "${A.name}"`);return e(A.values)}return null}},ju=function(A){let e=A.filter(function(t){return t.type===w.NUMBER_TOKEN}).map(function(t){return t.number});return e.length===6?e:null},$u=function(A){let e=A.filter(function(p){return p.type===w.NUMBER_TOKEN}).map(function(p){return p.number}),t=e[0],r=e[1],n=e[2],s=e[3],o=e[4],c=e[5],i=e[6],a=e[7],B=e[8],f=e[9],d=e[10],l=e[11],u=e[12],h=e[13],U=e[14],C=e[15];return e.length===16?[t,r,o,c,u,h]:null},Zu={matrix:ju,matrix3d:$u},ys={type:w.PERCENTAGE_TOKEN,number:50,flags:Ge},Yu=[ys,ys],Al={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:m.LIST,parse:function(A){let e=A.filter(v);return e.length!==2?Yu:[e[0],e[1]]}},le;(function(A){A[A.VISIBLE=0]="VISIBLE",A[A.HIDDEN=1]="HIDDEN",A[A.COLLAPSE=2]="COLLAPSE"})(le||(le={}));var el={name:"visible",initialValue:"none",prefix:!1,type:m.IDENT_VALUE,parse:function(A){switch(A){case"hidden":return le.HIDDEN;case"collapse":return le.COLLAPSE;case"visible":default:return le.VISIBLE}}},_e;(function(A){A.NORMAL="normal",A.BREAK_ALL="break-all",A.KEEP_ALL="keep-all"})(_e||(_e={}));var tl={name:"word-break",initialValue:"normal",prefix:!1,type:m.IDENT_VALUE,parse:function(A){switch(A){case"break-all":return _e.BREAK_ALL;case"keep-all":return _e.KEEP_ALL;case"normal":default:return _e.NORMAL}}},rl={name:"z-index",initialValue:"auto",prefix:!1,type:m.VALUE,parse:function(A){if(A.type===w.IDENT_TOKEN)return{auto:!0,order:0};if(he(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},nl={name:"opacity",initialValue:"1",type:m.VALUE,prefix:!1,parse:function(A){return he(A)?A.number:1}},sl={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:m.TYPE_VALUE,format:"color"},il={name:"text-decoration-line",initialValue:"none",prefix:!1,type:m.LIST,parse:function(A){return A.filter(S).map(function(e){switch(e.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(e){return e!==0})}},cl={name:"font-family",initialValue:"",prefix:!1,type:m.LIST,parse:function(A){return A.filter(al).map(function(e){return e.value})}},al=function(A){return A.type===w.STRING_TOKEN||A.type===w.IDENT_TOKEN},Bl={name:"font-size",initialValue:"0",prefix:!1,type:m.TYPE_VALUE,format:"length"},ul={name:"font-weight",initialValue:"normal",type:m.VALUE,prefix:!1,parse:function(A){if(he(A))return A.number;if(S(A))switch(A.value){case"bold":return 700;case"normal":default:return 400}return 400}},ll={name:"font-variant",initialValue:"none",type:m.LIST,prefix:!1,parse:function(A){return A.filter(S).map(function(e){return e.value})}},De;(function(A){A.NORMAL="normal",A.ITALIC="italic",A.OBLIQUE="oblique"})(De||(De={}));var fl={name:"font-style",initialValue:"normal",prefix:!1,type:m.IDENT_VALUE,parse:function(A){switch(A){case"oblique":return De.OBLIQUE;case"italic":return De.ITALIC;case"normal":default:return De.NORMAL}}},j=function(A,e){return(A&e)!==0},dl={name:"content",initialValue:"none",type:m.LIST,prefix:!1,parse:function(A){if(A.length===0)return[];let e=A[0];return e.type===w.IDENT_TOKEN&&e.value==="none"?[]:A}},hl={name:"counter-increment",initialValue:"none",prefix:!0,type:m.LIST,parse:function(A){if(A.length===0)return null;let e=A[0];if(e.type===w.IDENT_TOKEN&&e.value==="none")return null;let t=[],r=A.filter(lo);for(let n=0;n<r.length;n++){let s=r[n],o=r[n+1];if(s.type===w.IDENT_TOKEN){let c=o&&he(o)?o.number:1;t.push({counter:s.value,increment:c})}}return t}},Ql={name:"counter-reset",initialValue:"none",prefix:!0,type:m.LIST,parse:function(A){if(A.length===0)return[];let e=[],t=A.filter(lo);for(let r=0;r<t.length;r++){let n=t[r],s=t[r+1];if(S(n)&&n.value!=="none"){let o=s&&he(s)?s.number:0;e.push({counter:n.value,reset:o})}}return e}},gl={name:"quotes",initialValue:"none",prefix:!0,type:m.LIST,parse:function(A){if(A.length===0)return null;let e=A[0];if(e.type===w.IDENT_TOKEN&&e.value==="none")return null;let t=[],r=A.filter(bB);if(r.length%2!==0)return null;for(let n=0;n<r.length;n+=2){let s=r[n].value,o=r[n+1].value;t.push({open:s,close:o})}return t}},Ts=function(A,e,t){if(!A)return"";let r=A[Math.min(e,A.length-1)];return r?t?r.open:r.close:""},wl={name:"box-shadow",initialValue:"none",type:m.LIST,prefix:!1,parse:function(A){return A.length===1&&$r(A[0],"none")?[]:UA(A).map(function(e){let t={color:255,offsetX:Z,offsetY:Z,blur:Z,spread:Z,inset:!1},r=0;for(let n=0;n<e.length;n++){let s=e[n];$r(s,"inset")?t.inset=!0:vA(s)?(r===0?t.offsetX=s:r===1?t.offsetY=s:r===2?t.blur=s:t.spread=s,r++):t.color=RA.parse(s)}return t})}},Cl=function(){function A(e){this.backgroundClip=E(OB,e.backgroundClip),this.backgroundColor=E(SB,e.backgroundColor),this.backgroundImage=E(tu,e.backgroundImage),this.backgroundOrigin=E(ru,e.backgroundOrigin),this.backgroundPosition=E(nu,e.backgroundPosition),this.backgroundRepeat=E(su,e.backgroundRepeat),this.backgroundSize=E(iu,e.backgroundSize),this.borderTopColor=E(au,e.borderTopColor),this.borderRightColor=E(Bu,e.borderRightColor),this.borderBottomColor=E(uu,e.borderBottomColor),this.borderLeftColor=E(lu,e.borderLeftColor),this.borderTopLeftRadius=E(fu,e.borderTopLeftRadius),this.borderTopRightRadius=E(du,e.borderTopRightRadius),this.borderBottomRightRadius=E(hu,e.borderBottomRightRadius),this.borderBottomLeftRadius=E(Qu,e.borderBottomLeftRadius),this.borderTopStyle=E(gu,e.borderTopStyle),this.borderRightStyle=E(wu,e.borderRightStyle),this.borderBottomStyle=E(Cu,e.borderBottomStyle),this.borderLeftStyle=E(Uu,e.borderLeftStyle),this.borderTopWidth=E(Eu,e.borderTopWidth),this.borderRightWidth=E(pu,e.borderRightWidth),this.borderBottomWidth=E(Fu,e.borderBottomWidth),this.borderLeftWidth=E(mu,e.borderLeftWidth),this.boxShadow=E(wl,e.boxShadow),this.color=E(Hu,e.color),this.display=E(Nu,e.display),this.float=E(Iu,e.cssFloat),this.fontFamily=E(cl,e.fontFamily),this.fontSize=E(Bl,e.fontSize),this.fontStyle=E(fl,e.fontStyle),this.fontVariant=E(ll,e.fontVariant),this.fontWeight=E(ul,e.fontWeight),this.letterSpacing=E(Ru,e.letterSpacing),this.lineBreak=E(bu,e.lineBreak),this.lineHeight=E(xu,e.lineHeight),this.listStyleImage=E(Su,e.listStyleImage),this.listStylePosition=E(yu,e.listStylePosition),this.listStyleType=E(Yr,e.listStyleType),this.marginTop=E(Tu,e.marginTop),this.marginRight=E(Mu,e.marginRight),this.marginBottom=E(Lu,e.marginBottom),this.marginLeft=E(_u,e.marginLeft),this.opacity=E(nl,e.opacity);let t=E(Du,e.overflow);this.overflowX=t[0],this.overflowY=t[t.length>1?1:0],this.overflowWrap=E(Vu,e.overflowWrap),this.paddingTop=E(Pu,e.paddingTop),this.paddingRight=E(zu,e.paddingRight),this.paddingBottom=E(Xu,e.paddingBottom),this.paddingLeft=E(vu,e.paddingLeft),this.position=E(Ju,e.position),this.textAlign=E(Gu,e.textAlign),this.textDecorationColor=E(sl,e.textDecorationColor||e.color),this.textDecorationLine=E(il,e.textDecorationLine),this.textShadow=E(Wu,e.textShadow),this.textTransform=E(ku,e.textTransform),this.transform=E(qu,e.transform),this.transformOrigin=E(Al,e.transformOrigin),this.visibility=E(el,e.visibility),this.wordBreak=E(tl,e.wordBreak),this.zIndex=E(rl,e.zIndex)}return A.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===le.VISIBLE},A.prototype.isTransparent=function(){return zA(this.backgroundColor)},A.prototype.isTransformed=function(){return this.transform!==null},A.prototype.isPositioned=function(){return this.position!==LA.STATIC},A.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},A.prototype.isFloating=function(){return this.float!==MA.NONE},A.prototype.isInlineLevel=function(){return j(this.display,4)||j(this.display,33554432)||j(this.display,268435456)||j(this.display,536870912)||j(this.display,67108864)||j(this.display,134217728)},A}(),Ul=function(){function A(e){this.content=E(dl,e.content),this.quotes=E(gl,e.quotes)}return A}(),Ms=function(){function A(e){this.counterIncrement=E(hl,e.counterIncrement),this.counterReset=E(Ql,e.counterReset)}return A}(),E=function(A,e){let t=new uo,r=e!==null&&typeof e<"u"?e.toString():A.initialValue;t.write(r);let n=new cn(t.read());switch(A.type){case m.IDENT_VALUE:var s=n.parseComponentValue();return A.parse(S(s)?s.value:A.initialValue);case m.VALUE:return A.parse(n.parseComponentValue());case m.LIST:return A.parse(n.parseComponentValues());case m.TOKEN_VALUE:return n.parseComponentValue();case m.TYPE_VALUE:switch(A.format){case"angle":return Dt.parse(n.parseComponentValue());case"color":return RA.parse(n.parseComponentValue());case"image":return un.parse(n.parseComponentValue());case"length":var o=n.parseComponentValue();return vA(o)?o:Z;case"length-percentage":var c=n.parseComponentValue();return v(c)?c:Z}}throw new Error(`Attempting to parse unsupported css format type ${A.format}`)},EA=function(){function A(e){this.styles=new Cl(window.getComputedStyle(e,null)),this.textNodes=[],this.elements=[],this.styles.transform!==null&&Po(e)&&(e.style.transform="none"),this.bounds=sn(e),this.flags=0}return A}(),bt=function(){function A(e,t){this.text=e,this.bounds=t}return A}(),El=function(A,e,t){let r=ml(A,e),n=[],s=0;return r.forEach(function(o){if(e.textDecorationLine.length||o.trim().length>0)if(uA.SUPPORT_RANGE_BOUNDS)n.push(new bt(o,Fl(t,s,o.length)));else{let c=t.splitText(o.length);n.push(new bt(o,pl(t))),t=c}else uA.SUPPORT_RANGE_BOUNDS||(t=t.splitText(o.length));s+=o.length}),n},pl=function(A){let e=A.ownerDocument;if(e){let t=e.createElement("html2canvaswrapper");t.appendChild(A.cloneNode(!0));let r=A.parentNode;if(r){r.replaceChild(t,A);let n=sn(t);return t.firstChild&&r.replaceChild(t.firstChild,t),n}}return new XA(0,0,0,0)},Fl=function(A,e,t){let r=A.ownerDocument;if(!r)throw new Error("Node has no owner document");let n=r.createRange();return n.setStart(A,e),n.setEnd(A,e+t),XA.fromClientRect(n.getBoundingClientRect())},ml=function(A,e){return e.letterSpacing!==0?_t(A).map(function(t){return X(t)}):Hl(A,e)},Hl=function(A,e){let t=ma(A,{lineBreak:e.lineBreak,wordBreak:e.overflowWrap===ze.BREAK_WORD?"break-word":e.wordBreak}),r=[],n;for(;!(n=t.next()).done;)n.value&&r.push(n.value.slice());return r},Nl=function(){function A(e,t){this.text=Kl(e.data,t.textTransform),this.textBounds=El(this.text,t,e)}return A}(),Kl=function(A,e){switch(e){case IA.LOWERCASE:return A.toLowerCase();case IA.CAPITALIZE:return A.replace(Il,Rl);case IA.UPPERCASE:return A.toUpperCase();default:return A}},Il=/(^|\s|:|-|\(|\))([a-z])/g,Rl=function(A,e,t){return A.length>0?e+t.toUpperCase():A},bo=function(A){bA(e,A);function e(t){let r=A.call(this,t)||this;return r.src=t.currentSrc||t.src,r.intrinsicWidth=t.naturalWidth,r.intrinsicHeight=t.naturalHeight,NA.getInstance().addImage(r.src),r}return e}(EA),xo=function(A){bA(e,A);function e(t){let r=A.call(this,t)||this;return r.canvas=t,r.intrinsicWidth=t.width,r.intrinsicHeight=t.height,r}return e}(EA),Oo=function(A){bA(e,A);function e(t){let r=A.call(this,t)||this,n=new XMLSerializer;return r.svg=`data:image/svg+xml,${encodeURIComponent(n.serializeToString(t))}`,r.intrinsicWidth=t.width.baseVal.value,r.intrinsicHeight=t.height.baseVal.value,NA.getInstance().addImage(r.svg),r}return e}(EA),So=function(A){bA(e,A);function e(t){let r=A.call(this,t)||this;return r.value=t.value,r}return e}(EA),An=function(A){bA(e,A);function e(t){let r=A.call(this,t)||this;return r.start=t.start,r.reversed=typeof t.reversed=="boolean"&&t.reversed===!0,r}return e}(EA),bl=[{type:w.DIMENSION_TOKEN,flags:0,unit:"px",number:3}],xl=[{type:w.PERCENTAGE_TOKEN,flags:0,number:50}],Ol=function(A){return A.width>A.height?new XA(A.left+(A.width-A.height)/2,A.top,A.height,A.height):A.width<A.height?new XA(A.left,A.top+(A.height-A.width)/2,A.width,A.width):A},Sl=function(A){let e=A.type===yl?new Array(A.value.length+1).join("\u2022"):A.value;return e.length===0?A.placeholder||"":e},xt="checkbox",Ot="radio",yl="password",Ls=707406591,ln=function(A){bA(e,A);function e(t){let r=A.call(this,t)||this;switch(r.type=t.type.toLowerCase(),r.checked=t.checked,r.value=Sl(t),(r.type===xt||r.type===Ot)&&(r.styles.backgroundColor=3739148031,r.styles.borderTopColor=r.styles.borderRightColor=r.styles.borderBottomColor=r.styles.borderLeftColor=2779096575,r.styles.borderTopWidth=r.styles.borderRightWidth=r.styles.borderBottomWidth=r.styles.borderLeftWidth=1,r.styles.borderTopStyle=r.styles.borderRightStyle=r.styles.borderBottomStyle=r.styles.borderLeftStyle=de.SOLID,r.styles.backgroundClip=[hA.BORDER_BOX],r.styles.backgroundOrigin=[0],r.bounds=Ol(r.bounds)),r.type){case xt:r.styles.borderTopRightRadius=r.styles.borderTopLeftRadius=r.styles.borderBottomRightRadius=r.styles.borderBottomLeftRadius=bl;break;case Ot:r.styles.borderTopRightRadius=r.styles.borderTopLeftRadius=r.styles.borderBottomRightRadius=r.styles.borderBottomLeftRadius=xl;break}return r}return e}(EA),yo=function(A){bA(e,A);function e(t){let r=A.call(this,t)||this,n=t.options[t.selectedIndex||0];return r.value=n&&n.text||"",r}return e}(EA),To=function(A){bA(e,A);function e(t){let r=A.call(this,t)||this;return r.value=t.value,r}return e}(EA),_s=function(A){return RA.parse(cn.create(A).parseComponentValue())},Mo=function(A){bA(e,A);function e(t){let r=A.call(this,t)||this;r.src=t.src,r.width=parseInt(t.width,10)||0,r.height=parseInt(t.height,10)||0,r.backgroundColor=r.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){r.tree=Do(t.contentWindow.document.documentElement);let n=t.contentWindow.document.documentElement?_s(getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):HA.TRANSPARENT,s=t.contentWindow.document.body?_s(getComputedStyle(t.contentWindow.document.body).backgroundColor):HA.TRANSPARENT;r.backgroundColor=zA(n)?zA(s)?r.styles.backgroundColor:s:n}}catch{}return r}return e}(EA),Tl=["OL","UL","MENU"],Lo=function(A,e,t){for(let r=A.firstChild,n=void 0;r;r=n)if(n=r.nextSibling,Vo(r)&&r.data.trim().length>0)e.textNodes.push(new Nl(r,e.styles));else if(en(r)){let s=_o(r);s.styles.isVisible()&&(Ml(r,s,t)?s.flags|=4:Ll(s.styles)&&(s.flags|=2),Tl.indexOf(r.tagName)!==-1&&(s.flags|=8),e.elements.push(s),!St(r)&&!zo(r)&&!yt(r)&&Lo(r,s,t))}},_o=function(A){return Xl(A)?new bo(A):Xo(A)?new xo(A):zo(A)?new Oo(A):Dl(A)?new So(A):Vl(A)?new An(A):Pl(A)?new ln(A):yt(A)?new yo(A):St(A)?new To(A):vo(A)?new Mo(A):new EA(A)},Do=function(A){let e=_o(A);return e.flags|=4,Lo(A,e,e),e},Ml=function(A,e,t){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||fn(A)&&t.styles.isTransparent()},Ll=function(A){return A.isPositioned()||A.isFloating()},Vo=function(A){return A.nodeType===Node.TEXT_NODE},en=function(A){return A.nodeType===Node.ELEMENT_NODE},Po=function(A){return typeof A.style<"u"},_l=function(A){return typeof A.className=="object"},Dl=function(A){return A.tagName==="LI"},Vl=function(A){return A.tagName==="OL"},Pl=function(A){return A.tagName==="INPUT"},zl=function(A){return A.tagName==="HTML"},zo=function(A){return A.tagName==="svg"},fn=function(A){return A.tagName==="BODY"},Xo=function(A){return A.tagName==="CANVAS"},Xl=function(A){return A.tagName==="IMG"},vo=function(A){return A.tagName==="IFRAME"},Ds=function(A){return A.tagName==="STYLE"},vl=function(A){return A.tagName==="SCRIPT"},St=function(A){return A.tagName==="TEXTAREA"},yt=function(A){return A.tagName==="SELECT"},Gl=function(){function A(){this.counters={}}return A.prototype.getCounterValue=function(e){let t=this.counters[e];return t&&t.length?t[t.length-1]:1},A.prototype.getCounterValues=function(e){let t=this.counters[e];return t||[]},A.prototype.pop=function(e){let t=this;e.forEach(function(r){return t.counters[r].pop()})},A.prototype.parse=function(e){let t=this,r=e.counterIncrement,n=e.counterReset,s=!0;r!==null&&r.forEach(function(c){let i=t.counters[c.counter];i&&c.increment!==0&&(s=!1,i[Math.max(0,i.length-1)]+=c.increment)});let o=[];return s&&n.forEach(function(c){let i=t.counters[c.counter];o.push(c.counter),i||(i=t.counters[c.counter]=[]),i.push(c.reset)}),o},A}(),Vs={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},Ps={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u0554","\u0553","\u0552","\u0551","\u0550","\u054F","\u054E","\u054D","\u054C","\u054B","\u054A","\u0549","\u0548","\u0547","\u0546","\u0545","\u0544","\u0543","\u0542","\u0541","\u0540","\u053F","\u053E","\u053D","\u053C","\u053B","\u053A","\u0539","\u0538","\u0537","\u0536","\u0535","\u0534","\u0533","\u0532","\u0531"]},Jl={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["\u05D9\u05F3","\u05D8\u05F3","\u05D7\u05F3","\u05D6\u05F3","\u05D5\u05F3","\u05D4\u05F3","\u05D3\u05F3","\u05D2\u05F3","\u05D1\u05F3","\u05D0\u05F3","\u05EA","\u05E9","\u05E8","\u05E7","\u05E6","\u05E4","\u05E2","\u05E1","\u05E0","\u05DE","\u05DC","\u05DB","\u05D9\u05D8","\u05D9\u05D7","\u05D9\u05D6","\u05D8\u05D6","\u05D8\u05D5","\u05D9","\u05D8","\u05D7","\u05D6","\u05D5","\u05D4","\u05D3","\u05D2","\u05D1","\u05D0"]},Wl={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u10F5","\u10F0","\u10EF","\u10F4","\u10EE","\u10ED","\u10EC","\u10EB","\u10EA","\u10E9","\u10E8","\u10E7","\u10E6","\u10E5","\u10E4","\u10F3","\u10E2","\u10E1","\u10E0","\u10DF","\u10DE","\u10DD","\u10F2","\u10DC","\u10DB","\u10DA","\u10D9","\u10D8","\u10D7","\u10F1","\u10D6","\u10D5","\u10D4","\u10D3","\u10D2","\u10D1","\u10D0"]},se=function(A,e,t,r,n,s){return A<e||A>t?Xe(A,n,s.length>0):r.integers.reduce(function(o,c,i){for(;A>=c;)A-=c,o+=r.values[i];return o},"")+s},Go=function(A,e,t,r){let n="";do t||A--,n=r(A)+n,A/=e;while(A*e>=e);return n},V=function(A,e,t,r,n){let s=t-e+1;return(A<0?"-":"")+(Go(Math.abs(A),s,r,function(o){return X(Math.floor(o%s)+e)})+n)},jA=function(A,e,t){t===void 0&&(t=". ");let r=e.length;return Go(Math.abs(A),r,!1,function(n){return e[Math.floor(n%r)]})+t},ce=1,SA=2,yA=4,Me=8,mA=function(A,e,t,r,n,s){if(A<-9999||A>9999)return Xe(A,g.CJK_DECIMAL,n.length>0);let o=Math.abs(A),c=n;if(o===0)return e[0]+c;for(let i=0;o>0&&i<=4;i++){let a=o%10;a===0&&j(s,ce)&&c!==""?c=e[a]+c:a>1||a===1&&i===0||a===1&&i===1&&j(s,SA)||a===1&&i===1&&j(s,yA)&&A>100||a===1&&i>1&&j(s,Me)?c=e[a]+(i>0?t[i-1]:"")+c:a===1&&i>0&&(c=t[i-1]+c),o=Math.floor(o/10)}return(A<0?r:"")+c},zs="\u5341\u767E\u5343\u842C",Xs="\u62FE\u4F70\u4EDF\u842C",vs="\u30DE\u30A4\u30CA\u30B9",_r="\uB9C8\uC774\uB108\uC2A4",Xe=function(A,e,t){let r=t?". ":"",n=t?"\u3001":"",s=t?", ":"",o=t?" ":"";switch(e){case g.DISC:return`\u2022${o}`;case g.CIRCLE:return`\u25E6${o}`;case g.SQUARE:return`\u25FE${o}`;case g.DECIMAL_LEADING_ZERO:var c=V(A,48,57,!0,r);return c.length<4?`0${c}`:c;case g.CJK_DECIMAL:return jA(A,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",n);case g.LOWER_ROMAN:return se(A,1,3999,Vs,g.DECIMAL,r).toLowerCase();case g.UPPER_ROMAN:return se(A,1,3999,Vs,g.DECIMAL,r);case g.LOWER_GREEK:return V(A,945,969,!1,r);case g.LOWER_ALPHA:return V(A,97,122,!1,r);case g.UPPER_ALPHA:return V(A,65,90,!1,r);case g.ARABIC_INDIC:return V(A,1632,1641,!0,r);case g.ARMENIAN:case g.UPPER_ARMENIAN:return se(A,1,9999,Ps,g.DECIMAL,r);case g.LOWER_ARMENIAN:return se(A,1,9999,Ps,g.DECIMAL,r).toLowerCase();case g.BENGALI:return V(A,2534,2543,!0,r);case g.CAMBODIAN:case g.KHMER:return V(A,6112,6121,!0,r);case g.CJK_EARTHLY_BRANCH:return jA(A,"\u5B50\u4E11\u5BC5\u536F\u8FB0\u5DF3\u5348\u672A\u7533\u9149\u620C\u4EA5",n);case g.CJK_HEAVENLY_STEM:return jA(A,"\u7532\u4E59\u4E19\u4E01\u620A\u5DF1\u5E9A\u8F9B\u58EC\u7678",n);case g.CJK_IDEOGRAPHIC:case g.TRAD_CHINESE_INFORMAL:return mA(A,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",zs,"\u8CA0",n,SA|yA|Me);case g.TRAD_CHINESE_FORMAL:return mA(A,"\u96F6\u58F9\u8CB3\u53C3\u8086\u4F0D\u9678\u67D2\u634C\u7396",Xs,"\u8CA0",n,ce|SA|yA|Me);case g.SIMP_CHINESE_INFORMAL:return mA(A,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",zs,"\u8D1F",n,SA|yA|Me);case g.SIMP_CHINESE_FORMAL:return mA(A,"\u96F6\u58F9\u8D30\u53C1\u8086\u4F0D\u9646\u67D2\u634C\u7396",Xs,"\u8D1F",n,ce|SA|yA|Me);case g.JAPANESE_INFORMAL:return mA(A,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u4E07",vs,n,0);case g.JAPANESE_FORMAL:return mA(A,"\u96F6\u58F1\u5F10\u53C2\u56DB\u4F0D\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343\u4E07",vs,n,ce|SA|yA);case g.KOREAN_HANGUL_FORMAL:return mA(A,"\uC601\uC77C\uC774\uC0BC\uC0AC\uC624\uC721\uCE60\uD314\uAD6C","\uC2ED\uBC31\uCC9C\uB9CC",_r,s,ce|SA|yA);case g.KOREAN_HANJA_INFORMAL:return mA(A,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u842C",_r,s,0);case g.KOREAN_HANJA_FORMAL:return mA(A,"\u96F6\u58F9\u8CB3\u53C3\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343",_r,s,ce|SA|yA);case g.DEVANAGARI:return V(A,2406,2415,!0,r);case g.GEORGIAN:return se(A,1,19999,Wl,g.DECIMAL,r);case g.GUJARATI:return V(A,2790,2799,!0,r);case g.GURMUKHI:return V(A,2662,2671,!0,r);case g.HEBREW:return se(A,1,10999,Jl,g.DECIMAL,r);case g.HIRAGANA:return jA(A,"\u3042\u3044\u3046\u3048\u304A\u304B\u304D\u304F\u3051\u3053\u3055\u3057\u3059\u305B\u305D\u305F\u3061\u3064\u3066\u3068\u306A\u306B\u306C\u306D\u306E\u306F\u3072\u3075\u3078\u307B\u307E\u307F\u3080\u3081\u3082\u3084\u3086\u3088\u3089\u308A\u308B\u308C\u308D\u308F\u3090\u3091\u3092\u3093");case g.HIRAGANA_IROHA:return jA(A,"\u3044\u308D\u306F\u306B\u307B\u3078\u3068\u3061\u308A\u306C\u308B\u3092\u308F\u304B\u3088\u305F\u308C\u305D\u3064\u306D\u306A\u3089\u3080\u3046\u3090\u306E\u304A\u304F\u3084\u307E\u3051\u3075\u3053\u3048\u3066\u3042\u3055\u304D\u3086\u3081\u307F\u3057\u3091\u3072\u3082\u305B\u3059");case g.KANNADA:return V(A,3302,3311,!0,r);case g.KATAKANA:return jA(A,"\u30A2\u30A4\u30A6\u30A8\u30AA\u30AB\u30AD\u30AF\u30B1\u30B3\u30B5\u30B7\u30B9\u30BB\u30BD\u30BF\u30C1\u30C4\u30C6\u30C8\u30CA\u30CB\u30CC\u30CD\u30CE\u30CF\u30D2\u30D5\u30D8\u30DB\u30DE\u30DF\u30E0\u30E1\u30E2\u30E4\u30E6\u30E8\u30E9\u30EA\u30EB\u30EC\u30ED\u30EF\u30F0\u30F1\u30F2\u30F3",n);case g.KATAKANA_IROHA:return jA(A,"\u30A4\u30ED\u30CF\u30CB\u30DB\u30D8\u30C8\u30C1\u30EA\u30CC\u30EB\u30F2\u30EF\u30AB\u30E8\u30BF\u30EC\u30BD\u30C4\u30CD\u30CA\u30E9\u30E0\u30A6\u30F0\u30CE\u30AA\u30AF\u30E4\u30DE\u30B1\u30D5\u30B3\u30A8\u30C6\u30A2\u30B5\u30AD\u30E6\u30E1\u30DF\u30B7\u30F1\u30D2\u30E2\u30BB\u30B9",n);case g.LAO:return V(A,3792,3801,!0,r);case g.MONGOLIAN:return V(A,6160,6169,!0,r);case g.MYANMAR:return V(A,4160,4169,!0,r);case g.ORIYA:return V(A,2918,2927,!0,r);case g.PERSIAN:return V(A,1776,1785,!0,r);case g.TAMIL:return V(A,3046,3055,!0,r);case g.TELUGU:return V(A,3174,3183,!0,r);case g.THAI:return V(A,3664,3673,!0,r);case g.TIBETAN:return V(A,3872,3881,!0,r);case g.DECIMAL:default:return V(A,48,57,!0,r)}},Jo="data-html2canvas-ignore",Gs=function(){function A(e,t){if(this.options=t,this.scrolledElements=[],this.referenceElement=e,this.counters=new Gl,this.quoteDepth=0,!e.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(e.ownerDocument.documentElement)}return A.prototype.toIFrame=function(e,t){let r=this,n=kl(e,t);if(!n.contentWindow)return Promise.reject("Unable to find iframe window");let s=e.defaultView.pageXOffset,o=e.defaultView.pageYOffset,c=n.contentWindow,i=c.document,a=ql(n).then(function(){return cA(r,void 0,void 0,function(){let B;return oA(this,function(f){switch(f.label){case 0:return this.scrolledElements.forEach(Zl),c&&(c.scrollTo(t.left,t.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(c.scrollY!==t.top||c.scrollX!==t.left)&&(i.documentElement.style.top=`${-t.top}px`,i.documentElement.style.left=`${-t.left}px`,i.documentElement.style.position="absolute")),B=this.options.onclone,typeof this.clonedReferenceElement>"u"?[2,Promise.reject(`Error finding the ${this.referenceElement.nodeName} in the cloned document`)]:i.fonts&&i.fonts.ready?[4,i.fonts.ready]:[3,2];case 1:f.sent(),f.label=2;case 2:return typeof B=="function"?[2,Promise.resolve().then(function(){return B(i)}).then(function(){return n})]:[2,n]}})})});return i.open(),i.write(`${jl(document.doctype)}<html></html>`),$l(this.referenceElement.ownerDocument,s,o),i.replaceChild(i.adoptNode(this.documentElement),i.documentElement),i.close(),a},A.prototype.createElementClone=function(e){return Xo(e)?this.createCanvasClone(e):Ds(e)?this.createStyleClone(e):e.cloneNode(!1)},A.prototype.createStyleClone=function(e){try{let t=e.sheet;if(t&&t.cssRules){let r=[].slice.call(t.cssRules,0).reduce(function(s,o){return o&&typeof o.cssText=="string"?s+o.cssText:s},""),n=e.cloneNode(!1);return n.textContent=r,n}}catch(t){if(W.getInstance(this.options.id).error("Unable to access cssRules property",t),t.name!=="SecurityError")throw t}return e.cloneNode(!1)},A.prototype.createCanvasClone=function(e){if(this.options.inlineImages&&e.ownerDocument){let r=e.ownerDocument.createElement("img");try{return r.src=e.toDataURL(),r}catch{W.getInstance(this.options.id).info("Unable to clone canvas contents, canvas is tainted")}}let t=e.cloneNode(!1);try{t.width=e.width,t.height=e.height;let r=e.getContext("2d"),n=t.getContext("2d");return n&&(r?n.putImageData(r.getImageData(0,0,e.width,e.height),0,0):n.drawImage(e,0,0)),t}catch{}return t},A.prototype.cloneNode=function(e){if(Vo(e))return document.createTextNode(e.data);if(!e.ownerDocument)return e.cloneNode(!1);let t=e.ownerDocument.defaultView;if(Po(e)&&t){let r=this.createElementClone(e),n=t.getComputedStyle(e),s=t.getComputedStyle(e,":before"),o=t.getComputedStyle(e,":after");this.referenceElement===e&&(this.clonedReferenceElement=r),fn(r)&&ef(r);let c=this.counters.parse(new Ms(n)),i=this.resolvePseudoContent(e,r,s,Ve.BEFORE);for(let B=e.firstChild;B;B=B.nextSibling)(!en(B)||!vl(B)&&!B.hasAttribute(Jo)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(B)))&&(!this.options.copyStyles||!en(B)||!Ds(B))&&r.appendChild(this.cloneNode(B));i&&r.insertBefore(i,r.firstChild);let a=this.resolvePseudoContent(e,r,o,Ve.AFTER);return a&&r.appendChild(a),this.counters.pop(c),n&&this.options.copyStyles&&!vo(e)&&Js(n,r),(e.scrollTop!==0||e.scrollLeft!==0)&&this.scrolledElements.push([r,e.scrollLeft,e.scrollTop]),(St(e)||yt(e))&&(St(r)||yt(r))&&(r.value=e.value),r}return e.cloneNode(!1)},A.prototype.resolvePseudoContent=function(e,t,r,n){let s=this;if(!r)return;let o=r.content,c=t.ownerDocument;if(!c||!o||o==="none"||o==="-moz-alt-content"||r.display==="none")return;this.counters.parse(new Ms(r));let i=new Ul(r),a=c.createElement("html2canvaspseudoelement");Js(r,a),i.content.forEach(function(f){if(f.type===w.STRING_TOKEN)a.appendChild(c.createTextNode(f.value));else if(f.type===w.URL_TOKEN){let U=c.createElement("img");U.src=f.value,U.style.opacity="1",a.appendChild(U)}else if(f.type===w.FUNCTION){if(f.name==="attr"){let U=f.values.filter(S);U.length&&a.appendChild(c.createTextNode(e.getAttribute(U[0].value)||""))}else if(f.name==="counter"){let U=f.values.filter(fe),C=U[0],p=U[1];if(C&&S(C)){let b=s.counters.getCounterValue(C.value),N=p&&S(p)?Yr.parse(p.value):g.DECIMAL;a.appendChild(c.createTextNode(Xe(b,N,!1)))}}else if(f.name==="counters"){var d=f.values.filter(fe),l=d[0],u=d[1],h=d[2];if(l&&S(l)){let U=s.counters.getCounterValues(l.value),C=h&&S(h)?Yr.parse(h.value):g.DECIMAL,p=u&&u.type===w.STRING_TOKEN?u.value:"",b=U.map(function(N){return Xe(N,C,!1)}).join(p);a.appendChild(c.createTextNode(b))}}}else if(f.type===w.IDENT_TOKEN)switch(f.value){case"open-quote":a.appendChild(c.createTextNode(Ts(i.quotes,s.quoteDepth++,!0)));break;case"close-quote":a.appendChild(c.createTextNode(Ts(i.quotes,--s.quoteDepth,!1)));break;default:a.appendChild(c.createTextNode(f.value))}}),a.className=`${tn} ${rn}`;let B=n===Ve.BEFORE?` ${tn}`:` ${rn}`;return _l(t)?t.className.baseValue+=B:t.className+=B,a},A.destroy=function(e){return e.parentNode?(e.parentNode.removeChild(e),!0):!1},A}(),Ve;(function(A){A[A.BEFORE=0]="BEFORE",A[A.AFTER=1]="AFTER"})(Ve||(Ve={}));var kl=function(A,e){let t=A.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=e.width.toString(),t.height=e.height.toString(),t.scrolling="no",t.setAttribute(Jo,"true"),A.body.appendChild(t),t},ql=function(A){return new Promise(function(e,t){let r=A.contentWindow;if(!r)return t("No window assigned for iframe");let n=r.document;r.onload=A.onload=n.onreadystatechange=function(){r.onload=A.onload=n.onreadystatechange=null;var s=setInterval(function(){n.body.childNodes.length>0&&n.readyState==="complete"&&(clearInterval(s),e(A))},50)}})},Js=function(A,e){for(let t=A.length-1;t>=0;t--){let r=A.item(t);r!=="content"&&e.style.setProperty(r,A.getPropertyValue(r))}return e},jl=function(A){let e="";return A&&(e+="<!DOCTYPE ",A.name&&(e+=A.name),A.internalSubset&&(e+=A.internalSubset),A.publicId&&(e+=`"${A.publicId}"`),A.systemId&&(e+=`"${A.systemId}"`),e+=">"),e},$l=function(A,e,t){A&&A.defaultView&&(e!==A.defaultView.pageXOffset||t!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(e,t)},Zl=function(A){let e=A[0],t=A[1],r=A[2];e.scrollLeft=t,e.scrollTop=r},Yl=":before",Af=":after",tn="___html2canvas___pseudoelement_before",rn="___html2canvas___pseudoelement_after",Ws=`{
    content: "" !important;
    display: none !important;
}`,ef=function(A){tf(A,`.${tn}${Yl}${Ws}
         .${rn}${Af}${Ws}`)},tf=function(A,e){let t=A.ownerDocument;if(t){let r=t.createElement("style");r.textContent=e,A.appendChild(r)}},ve;(function(A){A[A.VECTOR=0]="VECTOR",A[A.BEZIER_CURVE=1]="BEZIER_CURVE"})(ve||(ve={}));var ks=function(A,e){return A.length===e.length?A.some(function(t,r){return t===e[r]}):!1},rf=function(A,e,t,r,n){return A.map(function(s,o){switch(o){case 0:return s.add(e,t);case 1:return s.add(e+r,t);case 2:return s.add(e+r,t+n);case 3:return s.add(e,t+n)}return s})},F=function(){function A(e,t){this.type=ve.VECTOR,this.x=e,this.y=t}return A.prototype.add=function(e,t){return new A(this.x+e,this.y+t)},A}(),oe=function(A,e,t){return new F(A.x+(e.x-A.x)*t,A.y+(e.y-A.y)*t)},Ct=function(){function A(e,t,r,n){this.type=ve.BEZIER_CURVE,this.start=e,this.startControl=t,this.endControl=r,this.end=n}return A.prototype.subdivide=function(e,t){let r=oe(this.start,this.startControl,e),n=oe(this.startControl,this.endControl,e),s=oe(this.endControl,this.end,e),o=oe(r,n,e),c=oe(n,s,e),i=oe(o,c,e);return t?new A(this.start,r,o,i):new A(i,c,s,this.end)},A.prototype.add=function(e,t){return new A(this.start.add(e,t),this.startControl.add(e,t),this.endControl.add(e,t),this.end.add(e,t))},A.prototype.reverse=function(){return new A(this.end,this.endControl,this.startControl,this.start)},A}(),Be=function(A){return A.type===ve.BEZIER_CURVE},nf=function(){function A(e){let t=e.styles,r=e.bounds,n=Te(t.borderTopLeftRadius,r.width,r.height),s=Te(t.borderTopRightRadius,r.width,r.height),o=Te(t.borderBottomRightRadius,r.width,r.height),c=Te(t.borderBottomLeftRadius,r.width,r.height),i=n[0],a=n[1],B=s[0],f=s[1],d=o[0],l=o[1],u=c[0],h=c[1],U=[];U.push((i+B)/r.width),U.push((u+d)/r.width),U.push((a+h)/r.height),U.push((f+l)/r.height);let C=Math.max.apply(Math,U);C>1&&(i/=C,a/=C,B/=C,f/=C,d/=C,l/=C,u/=C,h/=C);let p=r.width-B,b=r.height-l,N=r.width-d,L=r.height-h,x=t.borderTopWidth,y=t.borderRightWidth,O=t.borderBottomWidth,H=t.borderLeftWidth,P=T(t.paddingTop,e.bounds.width),M=T(t.paddingRight,e.bounds.width),k=T(t.paddingBottom,e.bounds.width),K=T(t.paddingLeft,e.bounds.width);this.topLeftBorderBox=i>0||a>0?BA(r.left,r.top,i,a,J.TOP_LEFT):new F(r.left,r.top),this.topRightBorderBox=B>0||f>0?BA(r.left+p,r.top,B,f,J.TOP_RIGHT):new F(r.left+r.width,r.top),this.bottomRightBorderBox=d>0||l>0?BA(r.left+N,r.top+b,d,l,J.BOTTOM_RIGHT):new F(r.left+r.width,r.top+r.height),this.bottomLeftBorderBox=u>0||h>0?BA(r.left,r.top+L,u,h,J.BOTTOM_LEFT):new F(r.left,r.top+r.height),this.topLeftPaddingBox=i>0||a>0?BA(r.left+H,r.top+x,Math.max(0,i-H),Math.max(0,a-x),J.TOP_LEFT):new F(r.left+H,r.top+x),this.topRightPaddingBox=B>0||f>0?BA(r.left+Math.min(p,r.width+H),r.top+x,p>r.width+H?0:B-H,f-x,J.TOP_RIGHT):new F(r.left+r.width-y,r.top+x),this.bottomRightPaddingBox=d>0||l>0?BA(r.left+Math.min(N,r.width-H),r.top+Math.min(b,r.height+x),Math.max(0,d-y),l-O,J.BOTTOM_RIGHT):new F(r.left+r.width-y,r.top+r.height-O),this.bottomLeftPaddingBox=u>0||h>0?BA(r.left+H,r.top+L,Math.max(0,u-H),h-O,J.BOTTOM_LEFT):new F(r.left+H,r.top+r.height-O),this.topLeftContentBox=i>0||a>0?BA(r.left+H+K,r.top+x+P,Math.max(0,i-(H+K)),Math.max(0,a-(x+P)),J.TOP_LEFT):new F(r.left+H+K,r.top+x+P),this.topRightContentBox=B>0||f>0?BA(r.left+Math.min(p,r.width+H+K),r.top+x+P,p>r.width+H+K?0:B-H+K,f-(x+P),J.TOP_RIGHT):new F(r.left+r.width-(y+M),r.top+x+P),this.bottomRightContentBox=d>0||l>0?BA(r.left+Math.min(N,r.width-(H+K)),r.top+Math.min(b,r.height+x+P),Math.max(0,d-(y+M)),l-(O+k),J.BOTTOM_RIGHT):new F(r.left+r.width-(y+M),r.top+r.height-(O+k)),this.bottomLeftContentBox=u>0||h>0?BA(r.left+H+K,r.top+L,Math.max(0,u-(H+K)),h-(O+k),J.BOTTOM_LEFT):new F(r.left+H+K,r.top+r.height-(O+k))}return A}(),J;(function(A){A[A.TOP_LEFT=0]="TOP_LEFT",A[A.TOP_RIGHT=1]="TOP_RIGHT",A[A.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",A[A.BOTTOM_LEFT=3]="BOTTOM_LEFT"})(J||(J={}));var BA=function(A,e,t,r,n){let s=4*((Math.sqrt(2)-1)/3),o=t*s,c=r*s,i=A+t,a=e+r;switch(n){case J.TOP_LEFT:return new Ct(new F(A,a),new F(A,a-c),new F(i-o,e),new F(i,e));case J.TOP_RIGHT:return new Ct(new F(A,e),new F(A+o,e),new F(i,a-c),new F(i,a));case J.BOTTOM_RIGHT:return new Ct(new F(i,e),new F(i,e+c),new F(A+o,a),new F(A,a));case J.BOTTOM_LEFT:default:return new Ct(new F(i,a),new F(i-o,a),new F(A,e+c),new F(A,e))}},Tt=function(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]},sf=function(A){return[A.topLeftContentBox,A.topRightContentBox,A.bottomRightContentBox,A.bottomLeftContentBox]},Mt=function(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]},of=function(){function A(e,t,r){this.type=0,this.offsetX=e,this.offsetY=t,this.matrix=r,this.target=6}return A}(),Ut=function(){function A(e,t){this.type=1,this.target=t,this.path=e}return A}(),cf=function(A){return A.type===0},af=function(A){return A.type===1},Wo=function(){function A(e){this.element=e,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return A}(),ko=function(){function A(e,t){if(this.container=e,this.effects=t.slice(0),this.curves=new nf(e),e.styles.transform!==null){let r=e.bounds.left+e.styles.transformOrigin[0].number,n=e.bounds.top+e.styles.transformOrigin[1].number,s=e.styles.transform;this.effects.push(new of(r,n,s))}if(e.styles.overflowX!==PA.VISIBLE){let r=Tt(this.curves),n=Mt(this.curves);ks(r,n)?this.effects.push(new Ut(r,6)):(this.effects.push(new Ut(r,2)),this.effects.push(new Ut(n,4)))}}return A.prototype.getParentEffects=function(){let e=this.effects.slice(0);if(this.container.styles.overflowX!==PA.VISIBLE){let t=Tt(this.curves),r=Mt(this.curves);ks(t,r)||e.push(new Ut(r,6))}return e},A}(),nn=function(A,e,t,r){A.container.elements.forEach(function(n){let s=j(n.flags,4),o=j(n.flags,2),c=new ko(n,A.getParentEffects());j(n.styles.display,2048)&&r.push(c);let i=j(n.flags,8)?[]:r;if(s||o){let a=s||n.styles.isPositioned()?t:e,B=new Wo(c);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){let f=n.styles.zIndex.order;if(f<0){let d=0;a.negativeZIndex.some(function(l,u){return f>l.element.container.styles.zIndex.order?(d=u,!1):d>0}),a.negativeZIndex.splice(d,0,B)}else if(f>0){let d=0;a.positiveZIndex.some(function(l,u){return f>l.element.container.styles.zIndex.order?(d=u+1,!1):d>0}),a.positiveZIndex.splice(d,0,B)}else a.zeroOrAutoZIndexOrTransformedOrOpacity.push(B)}else n.styles.isFloating()?a.nonPositionedFloats.push(B):a.nonPositionedInlineLevel.push(B);nn(c,B,s?B:t,i)}else n.styles.isInlineLevel()?e.inlineLevel.push(c):e.nonInlineLevel.push(c),nn(c,e,t,i);j(n.flags,8)&&qo(n,i)})},qo=function(A,e){let t=A instanceof An?A.start:1,r=A instanceof An?A.reversed:!1;for(let n=0;n<e.length;n++){let s=e[n];s.container instanceof So&&typeof s.container.value=="number"&&s.container.value!==0&&(t=s.container.value),s.listValue=Xe(t,s.container.styles.listStyleType,!0),t+=r?-1:1}},Bf=function(A){let e=new ko(A,[]),t=new Wo(e),r=[];return nn(e,t,t,r),qo(e.container,r),t},uf=function(A,e){switch(e){case 0:return Et(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return Et(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return Et(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);case 3:default:return Et(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}},Et=function(A,e,t,r){let n=[];return Be(A)?n.push(A.subdivide(.5,!1)):n.push(A),Be(t)?n.push(t.subdivide(.5,!0)):n.push(t),Be(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),Be(e)?n.push(e.subdivide(.5,!1).reverse()):n.push(e),n},jo=function(A){let e=A.bounds,t=A.styles;return e.add(t.borderLeftWidth,t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth),-(t.borderTopWidth+t.borderBottomWidth))},Lt=function(A){let e=A.styles,t=A.bounds,r=T(e.paddingLeft,t.width),n=T(e.paddingRight,t.width),s=T(e.paddingTop,t.width),o=T(e.paddingBottom,t.width);return t.add(r+e.borderLeftWidth,s+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+r+n),-(e.borderTopWidth+e.borderBottomWidth+s+o))},lf=function(A,e){return A===0?e.bounds:A===2?Lt(e):jo(e)},ff=function(A,e){return A===hA.BORDER_BOX?e.bounds:A===hA.CONTENT_BOX?Lt(e):jo(e)},Dr=function(A,e,t){let r=lf(ae(A.styles.backgroundOrigin,e),A),n=ff(ae(A.styles.backgroundClip,e),A),s=df(ae(A.styles.backgroundSize,e),t,r),o=s[0],c=s[1],i=Te(ae(A.styles.backgroundPosition,e),r.width-o,r.height-c),a=hf(ae(A.styles.backgroundRepeat,e),i,s,r,n),B=Math.round(r.left+i[0]),f=Math.round(r.top+i[1]);return[a,B,f,o,c]},ie=function(A){return S(A)&&A.value===ue.AUTO},pt=function(A){return typeof A=="number"},df=function(A,e,t){let r=e[0],n=e[1],s=e[2],o=A[0],c=A[1];if(v(o)&&c&&v(c))return[T(o,t.width),T(c,t.height)];let i=pt(s);if(S(o)&&(o.value===ue.CONTAIN||o.value===ue.COVER))return pt(s)?t.width/t.height<s!=(o.value===ue.COVER)?[t.width,t.width/s]:[t.height*s,t.height]:[t.width,t.height];let a=pt(r),B=pt(n),f=a||B;if(ie(o)&&(!c||ie(c))){if(a&&B)return[r,n];if(!i&&!f)return[t.width,t.height];if(f&&i){let U=a?r:n*s,C=B?n:r/s;return[U,C]}let u=a?r:t.width,h=B?n:t.height;return[u,h]}if(i){let u=0,h=0;return v(o)?u=T(o,t.width):v(c)&&(h=T(c,t.height)),ie(o)?u=h*s:(!c||ie(c))&&(h=u/s),[u,h]}let d=null,l=null;if(v(o)?d=T(o,t.width):c&&v(c)&&(l=T(c,t.height)),d!==null&&(!c||ie(c))&&(l=a&&B?d/r*n:t.height),l!==null&&ie(o)&&(d=a&&B?l/n*r:t.width),d!==null&&l!==null)return[d,l];throw new Error("Unable to calculate background-size for element")},ae=function(A,e){let t=A[e];return typeof t>"u"?A[0]:t},hf=function(A,e,t,r,n){let s=e[0],o=e[1],c=t[0],i=t[1];switch(A){case KA.REPEAT_X:return[new F(Math.round(r.left),Math.round(r.top+o)),new F(Math.round(r.left+r.width),Math.round(r.top+o)),new F(Math.round(r.left+r.width),Math.round(i+r.top+o)),new F(Math.round(r.left),Math.round(i+r.top+o))];case KA.REPEAT_Y:return[new F(Math.round(r.left+s),Math.round(r.top)),new F(Math.round(r.left+s+c),Math.round(r.top)),new F(Math.round(r.left+s+c),Math.round(r.height+r.top)),new F(Math.round(r.left+s),Math.round(r.height+r.top))];case KA.NO_REPEAT:return[new F(Math.round(r.left+s),Math.round(r.top+o)),new F(Math.round(r.left+s+c),Math.round(r.top+o)),new F(Math.round(r.left+s+c),Math.round(r.top+o+i)),new F(Math.round(r.left+s),Math.round(r.top+o+i))];default:return[new F(Math.round(n.left),Math.round(n.top)),new F(Math.round(n.left+n.width),Math.round(n.top)),new F(Math.round(n.left+n.width),Math.round(n.height+n.top)),new F(Math.round(n.left),Math.round(n.height+n.top))]}},Qf="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",qs="Hidden Text",gf=function(){function A(e){this._data={},this._document=e}return A.prototype.parseMetrics=function(e,t){let r=this._document.createElement("div"),n=this._document.createElement("img"),s=this._document.createElement("span"),o=this._document.body;r.style.visibility="hidden",r.style.fontFamily=e,r.style.fontSize=t,r.style.margin="0",r.style.padding="0",o.appendChild(r),n.src=Qf,n.width=1,n.height=1,n.style.margin="0",n.style.padding="0",n.style.verticalAlign="baseline",s.style.fontFamily=e,s.style.fontSize=t,s.style.margin="0",s.style.padding="0",s.appendChild(this._document.createTextNode(qs)),r.appendChild(s),r.appendChild(n);let c=n.offsetTop-s.offsetTop+2;r.removeChild(s),r.appendChild(this._document.createTextNode(qs)),r.style.lineHeight="normal",n.style.verticalAlign="super";let i=n.offsetTop-r.offsetTop+2;return o.removeChild(r),{baseline:c,middle:i}},A.prototype.getMetrics=function(e,t){let r=`${e} ${t}`;return typeof this._data[r]>"u"&&(this._data[r]=this.parseMetrics(e,t)),this._data[r]},A}(),wf=1e4,Cf=function(){function A(e){this._activeEffects=[],this.canvas=e.canvas?e.canvas:document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),this.options=e,e.canvas||(this.canvas.width=Math.floor(e.width*e.scale),this.canvas.height=Math.floor(e.height*e.scale),this.canvas.style.width=`${e.width}px`,this.canvas.style.height=`${e.height}px`),this.fontMetrics=new gf(document),this.ctx.scale(this.options.scale,this.options.scale),this.ctx.translate(-e.x+e.scrollX,-e.y+e.scrollY),this.ctx.textBaseline="bottom",this._activeEffects=[],W.getInstance(e.id).debug(`Canvas renderer initialized (${e.width}x${e.height} at ${e.x},${e.y}) with scale ${e.scale}`)}return A.prototype.applyEffects=function(e,t){let r=this;for(;this._activeEffects.length;)this.popEffect();e.filter(function(n){return j(n.target,t)}).forEach(function(n){return r.applyEffect(n)})},A.prototype.applyEffect=function(e){this.ctx.save(),cf(e)&&(this.ctx.translate(e.offsetX,e.offsetY),this.ctx.transform(e.matrix[0],e.matrix[1],e.matrix[2],e.matrix[3],e.matrix[4],e.matrix[5]),this.ctx.translate(-e.offsetX,-e.offsetY)),af(e)&&(this.path(e.path),this.ctx.clip()),this._activeEffects.push(e)},A.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},A.prototype.renderStack=function(e){return cA(this,void 0,void 0,function(){let t;return oA(this,function(r){switch(r.label){case 0:return t=e.element.container.styles,t.isVisible()?(this.ctx.globalAlpha=t.opacity,[4,this.renderStackContent(e)]):[3,2];case 1:r.sent(),r.label=2;case 2:return[2]}})})},A.prototype.renderNode=function(e){return cA(this,void 0,void 0,function(){return oA(this,function(t){switch(t.label){case 0:return e.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(e)]:[3,3];case 1:return t.sent(),[4,this.renderNodeContent(e)];case 2:t.sent(),t.label=3;case 3:return[2]}})})},A.prototype.renderTextWithLetterSpacing=function(e,t){let r=this;t===0?this.ctx.fillText(e.text,e.bounds.left,e.bounds.top+e.bounds.height):_t(e.text).map(function(s){return X(s)}).reduce(function(s,o){return r.ctx.fillText(o,s,e.bounds.top+e.bounds.height),s+r.ctx.measureText(o).width},e.bounds.left)},A.prototype.createFontStyle=function(e){let t=e.fontVariant.filter(function(s){return s==="normal"||s==="small-caps"}).join(""),r=e.fontFamily.join(", "),n=an(e.fontSize)?`${e.fontSize.number}${e.fontSize.unit}`:`${e.fontSize.number}px`;return[[e.fontStyle,t,e.fontWeight,n,r].join(" "),r,n]},A.prototype.renderTextNode=function(e,t){return cA(this,void 0,void 0,function(){let r,n,s,o,c=this;return oA(this,function(i){return r=this.createFontStyle(t),n=r[0],s=r[1],o=r[2],this.ctx.font=n,e.textBounds.forEach(function(a){c.ctx.fillStyle=eA(t.color),c.renderTextWithLetterSpacing(a,t.letterSpacing);let B=t.textShadow;B.length&&a.text.trim().length&&(B.slice(0).reverse().forEach(function(f){c.ctx.shadowColor=eA(f.color),c.ctx.shadowOffsetX=f.offsetX.number*c.options.scale,c.ctx.shadowOffsetY=f.offsetY.number*c.options.scale,c.ctx.shadowBlur=f.blur.number,c.ctx.fillText(a.text,a.bounds.left,a.bounds.top+a.bounds.height)}),c.ctx.shadowColor="",c.ctx.shadowOffsetX=0,c.ctx.shadowOffsetY=0,c.ctx.shadowBlur=0),t.textDecorationLine.length&&(c.ctx.fillStyle=eA(t.textDecorationColor||t.color),t.textDecorationLine.forEach(function(f){switch(f){case 1:var d=c.fontMetrics.getMetrics(s,o).baseline;c.ctx.fillRect(a.bounds.left,Math.round(a.bounds.top+d),a.bounds.width,1);break;case 2:c.ctx.fillRect(a.bounds.left,Math.round(a.bounds.top),a.bounds.width,1);break;case 3:var l=c.fontMetrics.getMetrics(s,o).middle;c.ctx.fillRect(a.bounds.left,Math.ceil(a.bounds.top+l),a.bounds.width,1);break}}))}),[2]})})},A.prototype.renderReplacedElement=function(e,t,r){if(r&&e.intrinsicWidth>0&&e.intrinsicHeight>0){let n=Lt(e),s=Mt(t);this.path(s),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(r,0,0,e.intrinsicWidth,e.intrinsicHeight,n.left,n.top,n.width,n.height),this.ctx.restore()}},A.prototype.renderNodeContent=function(e){return cA(this,void 0,void 0,function(){var t,r,n,s,o,c,i,a,B,f,d,l,u,h,U,C,p,b;return oA(this,function(N){switch(N.label){case 0:this.applyEffects(e.effects,4),t=e.container,r=e.curves,n=t.styles,s=0,o=t.textNodes,N.label=1;case 1:return s<o.length?(c=o[s],[4,this.renderTextNode(c,n)]):[3,4];case 2:N.sent(),N.label=3;case 3:return s++,[3,1];case 4:if(!(t instanceof bo))return[3,8];N.label=5;case 5:return N.trys.push([5,7,,8]),[4,this.options.cache.match(t.src)];case 6:return i=N.sent(),this.renderReplacedElement(t,r,i),[3,8];case 7:return a=N.sent(),W.getInstance(this.options.id).error(`Error loading image ${t.src}`),[3,8];case 8:if(t instanceof xo&&this.renderReplacedElement(t,r,t.canvas),!(t instanceof Oo))return[3,12];N.label=9;case 9:return N.trys.push([9,11,,12]),[4,this.options.cache.match(t.svg)];case 10:return i=N.sent(),this.renderReplacedElement(t,r,i),[3,12];case 11:return B=N.sent(),W.getInstance(this.options.id).error(`Error loading svg ${t.svg.substring(0,255)}`),[3,12];case 12:return t instanceof Mo&&t.tree?(f=new A({id:this.options.id,scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,scrollX:0,scrollY:0,width:t.width,height:t.height,cache:this.options.cache,windowWidth:t.width,windowHeight:t.height}),[4,f.render(t.tree)]):[3,14];case 13:d=N.sent(),t.width&&t.height&&this.ctx.drawImage(d,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),N.label=14;case 14:if(t instanceof ln&&(l=Math.min(t.bounds.width,t.bounds.height),t.type===xt?t.checked&&(this.ctx.save(),this.path([new F(t.bounds.left+l*.39363,t.bounds.top+l*.79),new F(t.bounds.left+l*.16,t.bounds.top+l*.5549),new F(t.bounds.left+l*.27347,t.bounds.top+l*.44071),new F(t.bounds.left+l*.39694,t.bounds.top+l*.5649),new F(t.bounds.left+l*.72983,t.bounds.top+l*.23),new F(t.bounds.left+l*.84,t.bounds.top+l*.34085),new F(t.bounds.left+l*.39363,t.bounds.top+l*.79)]),this.ctx.fillStyle=eA(Ls),this.ctx.fill(),this.ctx.restore()):t.type===Ot&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+l/2,t.bounds.top+l/2,l/4,0,Math.PI*2,!0),this.ctx.fillStyle=eA(Ls),this.ctx.fill(),this.ctx.restore())),Uf(t)&&t.value.length){switch(this.ctx.font=this.createFontStyle(n)[0],this.ctx.fillStyle=eA(n.color),this.ctx.textBaseline="middle",this.ctx.textAlign=pf(t.styles.textAlign),u=Lt(t),h=0,t.styles.textAlign){case CA.CENTER:h+=u.width/2;break;case CA.RIGHT:h+=u.width;break}U=u.add(h,0,0,-u.height/2+1),this.ctx.save(),this.path([new F(u.left,u.top),new F(u.left+u.width,u.top),new F(u.left+u.width,u.top+u.height),new F(u.left,u.top+u.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new bt(t.value,U),n.letterSpacing),this.ctx.restore(),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"}if(!j(t.styles.display,2048))return[3,20];if(t.styles.listStyleImage===null)return[3,19];if(C=t.styles.listStyleImage,C.type!==tA.URL)return[3,18];i=void 0,p=C.url,N.label=15;case 15:return N.trys.push([15,17,,18]),[4,this.options.cache.match(p)];case 16:return i=N.sent(),this.ctx.drawImage(i,t.bounds.left-(i.width+10),t.bounds.top),[3,18];case 17:return b=N.sent(),W.getInstance(this.options.id).error(`Error loading list-style-image ${p}`),[3,18];case 18:return[3,20];case 19:e.listValue&&t.styles.listStyleType!==g.NONE&&(this.ctx.font=this.createFontStyle(n)[0],this.ctx.fillStyle=eA(n.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",u=new XA(t.bounds.left,t.bounds.top+T(t.styles.paddingTop,t.bounds.width),t.bounds.width,Ou(n.lineHeight,n.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new bt(e.listValue,u),n.letterSpacing),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),N.label=20;case 20:return[2]}})})},A.prototype.renderStackContent=function(e){return cA(this,void 0,void 0,function(){var t,r,n,s,o,c,i,a,B,f,d,l,u,h,U;return oA(this,function(C){switch(C.label){case 0:return[4,this.renderNodeBackgroundAndBorders(e.element)];case 1:C.sent(),t=0,r=e.negativeZIndex,C.label=2;case 2:return t<r.length?(n=r[t],[4,this.renderStack(n)]):[3,5];case 3:C.sent(),C.label=4;case 4:return t++,[3,2];case 5:return[4,this.renderNodeContent(e.element)];case 6:C.sent(),s=0,o=e.nonInlineLevel,C.label=7;case 7:return s<o.length?(n=o[s],[4,this.renderNode(n)]):[3,10];case 8:C.sent(),C.label=9;case 9:return s++,[3,7];case 10:c=0,i=e.nonPositionedFloats,C.label=11;case 11:return c<i.length?(n=i[c],[4,this.renderStack(n)]):[3,14];case 12:C.sent(),C.label=13;case 13:return c++,[3,11];case 14:a=0,B=e.nonPositionedInlineLevel,C.label=15;case 15:return a<B.length?(n=B[a],[4,this.renderStack(n)]):[3,18];case 16:C.sent(),C.label=17;case 17:return a++,[3,15];case 18:f=0,d=e.inlineLevel,C.label=19;case 19:return f<d.length?(n=d[f],[4,this.renderNode(n)]):[3,22];case 20:C.sent(),C.label=21;case 21:return f++,[3,19];case 22:l=0,u=e.zeroOrAutoZIndexOrTransformedOrOpacity,C.label=23;case 23:return l<u.length?(n=u[l],[4,this.renderStack(n)]):[3,26];case 24:C.sent(),C.label=25;case 25:return l++,[3,23];case 26:h=0,U=e.positiveZIndex,C.label=27;case 27:return h<U.length?(n=U[h],[4,this.renderStack(n)]):[3,30];case 28:C.sent(),C.label=29;case 29:return h++,[3,27];case 30:return[2]}})})},A.prototype.mask=function(e){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(e.slice(0).reverse()),this.ctx.closePath()},A.prototype.path=function(e){this.ctx.beginPath(),this.formatPath(e),this.ctx.closePath()},A.prototype.formatPath=function(e){let t=this;e.forEach(function(r,n){let s=Be(r)?r.start:r;n===0?t.ctx.moveTo(s.x,s.y):t.ctx.lineTo(s.x,s.y),Be(r)&&t.ctx.bezierCurveTo(r.startControl.x,r.startControl.y,r.endControl.x,r.endControl.y,r.end.x,r.end.y)})},A.prototype.renderRepeat=function(e,t,r,n){this.path(e),this.ctx.fillStyle=t,this.ctx.translate(r,n),this.ctx.fill(),this.ctx.translate(-r,-n)},A.prototype.resizeImage=function(e,t,r){if(e.width===t&&e.height===r)return e;let n=this.canvas.ownerDocument.createElement("canvas");return n.width=t,n.height=r,n.getContext("2d").drawImage(e,0,0,e.width,e.height,0,0,t,r),n},A.prototype.renderBackgroundImage=function(e){return cA(this,void 0,void 0,function(){let t,r,n,s,o,c;return oA(this,function(i){switch(i.label){case 0:t=e.styles.backgroundImage.length-1,r=function(a){var B,f,d,l,u,h,U,C,p,b,N,L,x,y,O,H,P,M,k,K,QA,tt,rt,nt,ar,kA,Br,ur,lr,Ee,fr,dr;return oA(this,function(ee){switch(ee.label){case 0:if(a.type!==tA.URL)return[3,5];B=void 0,f=a.url,ee.label=1;case 1:return ee.trys.push([1,3,,4]),[4,n.options.cache.match(f)];case 2:return B=ee.sent(),[3,4];case 3:return d=ee.sent(),W.getInstance(n.options.id).error(`Error loading background-image ${f}`),[3,4];case 4:return B&&(l=Dr(e,t,[B.width,B.height,B.width/B.height]),u=l[0],h=l[1],U=l[2],C=l[3],p=l[4],b=n.ctx.createPattern(n.resizeImage(B,C,p),"repeat"),n.renderRepeat(u,b,h,U)),[3,6];case 5:YB(a)?(N=Dr(e,t,[null,null,null]),u=N[0],h=N[1],U=N[2],C=N[3],p=N[4],L=TB(a.angle,C,p),x=L[0],y=L[1],O=L[2],H=L[3],P=L[4],M=document.createElement("canvas"),M.width=C,M.height=p,k=M.getContext("2d"),K=k.createLinearGradient(y,H,O,P),bs(a.stops,x).forEach(function(pe){return K.addColorStop(pe.stop,eA(pe.color))}),k.fillStyle=K,k.fillRect(0,0,C,p),C>0&&p>0&&(b=n.ctx.createPattern(M,"repeat"),n.renderRepeat(u,b,h,U))):Au(a)&&(QA=Dr(e,t,[null,null,null]),u=QA[0],tt=QA[1],rt=QA[2],C=QA[3],p=QA[4],nt=a.position.length===0?[Bn]:a.position,h=T(nt[0],C),U=T(nt[nt.length-1],p),ar=MB(a,h,U,C,p),kA=ar[0],Br=ar[1],kA>0&&kA>0&&(ur=n.ctx.createRadialGradient(tt+h,rt+U,0,tt+h,rt+U,kA),bs(a.stops,kA*2).forEach(function(pe){return ur.addColorStop(pe.stop,eA(pe.color))}),n.path(u),n.ctx.fillStyle=ur,kA!==Br?(lr=e.bounds.left+.5*e.bounds.width,Ee=e.bounds.top+.5*e.bounds.height,fr=Br/kA,dr=1/fr,n.ctx.save(),n.ctx.translate(lr,Ee),n.ctx.transform(1,0,0,fr,0,0),n.ctx.translate(-lr,-Ee),n.ctx.fillRect(tt,dr*(rt-Ee)+Ee,C,p*dr),n.ctx.restore()):n.ctx.fill())),ee.label=6;case 6:return t--,[2]}})},n=this,s=0,o=e.styles.backgroundImage.slice(0).reverse(),i.label=1;case 1:return s<o.length?(c=o[s],[5,r(c)]):[3,4];case 2:i.sent(),i.label=3;case 3:return s++,[3,1];case 4:return[2]}})})},A.prototype.renderBorder=function(e,t,r){return cA(this,void 0,void 0,function(){return oA(this,function(n){return this.path(uf(r,t)),this.ctx.fillStyle=eA(e),this.ctx.fill(),[2]})})},A.prototype.renderNodeBackgroundAndBorders=function(e){return cA(this,void 0,void 0,function(){let t,r,n,s,o,c,i,a,B=this;return oA(this,function(f){switch(f.label){case 0:return this.applyEffects(e.effects,2),t=e.container.styles,r=!zA(t.backgroundColor)||t.backgroundImage.length,n=[{style:t.borderTopStyle,color:t.borderTopColor},{style:t.borderRightStyle,color:t.borderRightColor},{style:t.borderBottomStyle,color:t.borderBottomColor},{style:t.borderLeftStyle,color:t.borderLeftColor}],s=Ef(ae(t.backgroundClip,0),e.curves),r||t.boxShadow.length?(this.ctx.save(),this.path(s),this.ctx.clip(),zA(t.backgroundColor)||(this.ctx.fillStyle=eA(t.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(e.container)]):[3,2];case 1:f.sent(),this.ctx.restore(),t.boxShadow.slice(0).reverse().forEach(function(d){B.ctx.save();let l=Tt(e.curves),u=d.inset?0:wf,h=rf(l,-u+(d.inset?1:-1)*d.spread.number,(d.inset?1:-1)*d.spread.number,d.spread.number*(d.inset?-2:2),d.spread.number*(d.inset?-2:2));d.inset?(B.path(l),B.ctx.clip(),B.mask(h)):(B.mask(l),B.ctx.clip(),B.path(h)),B.ctx.shadowOffsetX=d.offsetX.number+u,B.ctx.shadowOffsetY=d.offsetY.number,B.ctx.shadowColor=eA(d.color),B.ctx.shadowBlur=d.blur.number,B.ctx.fillStyle=d.inset?eA(d.color):"rgba(0,0,0,1)",B.ctx.fill(),B.ctx.restore()}),f.label=2;case 2:o=0,c=0,i=n,f.label=3;case 3:return c<i.length?(a=i[c],a.style!==de.NONE&&!zA(a.color)?[4,this.renderBorder(a.color,o,e.curves)]:[3,5]):[3,7];case 4:f.sent(),f.label=5;case 5:o++,f.label=6;case 6:return c++,[3,3];case 7:return[2]}})})},A.prototype.render=function(e){return cA(this,void 0,void 0,function(){let t;return oA(this,function(r){switch(r.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=eA(this.options.backgroundColor),this.ctx.fillRect(this.options.x-this.options.scrollX,this.options.y-this.options.scrollY,this.options.width,this.options.height)),t=Bf(e),[4,this.renderStack(t)];case 1:return r.sent(),this.applyEffects([],2),[2,this.canvas]}})})},A}(),Uf=function(A){return A instanceof To||A instanceof yo?!0:A instanceof ln&&A.type!==Ot&&A.type!==xt},Ef=function(A,e){switch(A){case hA.BORDER_BOX:return Tt(e);case hA.CONTENT_BOX:return sf(e);case hA.PADDING_BOX:default:return Mt(e)}},pf=function(A){switch(A){case CA.CENTER:return"center";case CA.RIGHT:return"right";case CA.LEFT:default:return"left"}},Ff=function(){function A(e){this.canvas=e.canvas?e.canvas:document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),this.options=e,this.canvas.width=Math.floor(e.width*e.scale),this.canvas.height=Math.floor(e.height*e.scale),this.canvas.style.width=`${e.width}px`,this.canvas.style.height=`${e.height}px`,this.ctx.scale(this.options.scale,this.options.scale),this.ctx.translate(-e.x+e.scrollX,-e.y+e.scrollY),W.getInstance(e.id).debug(`EXPERIMENTAL ForeignObject renderer initialized (${e.width}x${e.height} at ${e.x},${e.y}) with scale ${e.scale}`)}return A.prototype.render=function(e){return cA(this,void 0,void 0,function(){let t,r;return oA(this,function(n){switch(n.label){case 0:return t=Zr(Math.max(this.options.windowWidth,this.options.width)*this.options.scale,Math.max(this.options.windowHeight,this.options.height)*this.options.scale,this.options.scrollX*this.options.scale,this.options.scrollY*this.options.scale,e),[4,mf(t)];case 1:return r=n.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=eA(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(r,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},A}(),mf=function(A){return new Promise(function(e,t){let r=new Image;r.onload=function(){e(r)},r.onerror=t,r.src=`data:image/svg+xml;charset=utf-8,${encodeURIComponent(new XMLSerializer().serializeToString(A))}`})},Hf=void 0,Vr=function(A){return RA.parse(cn.create(A).parseComponentValue())},Nf=function(A,e){return e===void 0&&(e={}),Kf(A,e)};typeof window<"u"&&NA.setContext(window);var Kf=function(A,e){return cA(Hf,void 0,void 0,function(){var t,r,n,s,o,c,i,a,B,f,d,l,u,h,U,C,p,b,N,L,x,y,O,H,P;return oA(this,function(M){switch(M.label){case 0:if(t=A.ownerDocument,!t)throw new Error("Element is not attached to a Document");if(r=t.defaultView,!r)throw new Error("Document is not attached to a Window");return n=(Math.round(Math.random()*1e3)+Date.now()).toString(16),s=fn(A)||zl(A)?Wc(t):sn(A),o=s.width,c=s.height,i=s.left,a=s.top,B={allowTaint:!1,imageTimeout:15e3,proxy:void 0,useCORS:!1},f=Ht({},B,e),d={backgroundColor:"#ffffff",cache:e.cache?e.cache:NA.create(n,f),logging:!0,removeContainer:!0,foreignObjectRendering:!1,scale:r.devicePixelRatio||1,windowWidth:r.innerWidth,windowHeight:r.innerHeight,scrollX:r.pageXOffset,scrollY:r.pageYOffset,x:i,y:a,width:Math.ceil(o),height:Math.ceil(c),id:n},l=Ht({},d,f,e),u=new XA(l.scrollX,l.scrollY,l.windowWidth,l.windowHeight),W.create({id:n,enabled:l.logging}),W.getInstance(n).debug("Starting document clone"),h=new Gs(A,{id:n,onclone:l.onclone,ignoreElements:l.ignoreElements,inlineImages:l.foreignObjectRendering,copyStyles:l.foreignObjectRendering}),U=h.clonedReferenceElement,U?[4,h.toIFrame(t,u)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return C=M.sent(),p=t.documentElement?Vr(getComputedStyle(t.documentElement).backgroundColor):HA.TRANSPARENT,b=t.body?Vr(getComputedStyle(t.body).backgroundColor):HA.TRANSPARENT,N=e.backgroundColor,L=typeof N=="string"?Vr(N):N===null?HA.TRANSPARENT:4294967295,x=A===t.documentElement?zA(p)?zA(b)?L:b:p:L,y={id:n,cache:l.cache,canvas:l.canvas,backgroundColor:x,scale:l.scale,x:l.x,y:l.y,scrollX:l.scrollX,scrollY:l.scrollY,width:l.width,height:l.height,windowWidth:l.windowWidth,windowHeight:l.windowHeight},l.foreignObjectRendering?(W.getInstance(n).debug("Document cloned, using foreign object rendering"),H=new Ff(y),[4,H.render(U)]):[3,3];case 2:return O=M.sent(),[3,5];case 3:return W.getInstance(n).debug("Document cloned, using computed rendering"),NA.attachInstance(l.cache),W.getInstance(n).debug("Starting DOM parsing"),P=Do(U),NA.detachInstance(),x===P.styles.backgroundColor&&(P.styles.backgroundColor=HA.TRANSPARENT),W.getInstance(n).debug("Starting renderer"),H=new Cf(y),[4,H.render(P)];case 4:O=M.sent(),M.label=5;case 5:return l.removeContainer===!0&&(Gs.destroy(C)||W.getInstance(n).error("Cannot detach cloned iframe as it is not in the DOM anymore")),W.getInstance(n).debug("Finished rendering"),W.destroy(n),NA.destroy(n),[2,O]}})})},$o=Nf;var If=function(){let A=function(){let l=document.createElement("canvas"),u=l.getContext("2d");return{canvas:!!u,imageData:!!u.getImageData,dataURL:!!l.toDataURL,btoa:!!window.btoa}}(),e="image/octet-stream";function t(l,u,h){let U=l.width,C=l.height;u===void 0&&(u=U),h===void 0&&(h=C);let p=document.createElement("canvas"),b=p.getContext("2d");return p.width=u,p.height=h,b.drawImage(l,0,0,U,C,0,0,u,h),p}function r(l,u,h,U){return l=t(l,h,U),l.toDataURL(u)}function n(l){document.location.href=l}function s(l){let u=document.createElement("img");return u.src=l,u}function o(l){return l=l.toLowerCase().replace(/jpg/i,"jpeg"),`image/${l.match(/png|jpeg|bmp|gif/)[0]}`}function c(l){if(!window.btoa)throw"btoa undefined";let u="";if(typeof l=="string")u=l;else for(let h=0;h<l.length;h++)u+=String.fromCharCode(l[h]);return btoa(u)}function i(l){let u=l.width,h=l.height;return l.getContext("2d").getImageData(0,0,u,h)}function a(l,u){return`data:${u};base64,${l}`}let B=function(l){let u=l.width,h=l.height,U=u*h*3,C=U+54,p=[66,77,C&255,C>>8&255,C>>16&255,C>>24&255,0,0,0,0,54,0,0,0],b=[40,0,0,0,u&255,u>>8&255,u>>16&255,u>>24&255,h&255,h>>8&255,h>>16&255,h>>24&255,1,0,24,0,0,0,0,0,U&255,U>>8&255,U>>16&255,U>>24&255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],N=(4-u*3%4)%4,L=l.data,x="",y=u<<2,O=h,H=String.fromCharCode;do{let M=y*(O-1),k="";for(let K=0;K<u;K++){let QA=K<<2;k+=H(L[M+QA+2])+H(L[M+QA+1])+H(L[M+QA])}for(let K=0;K<N;K++)k+=String.fromCharCode(0);x+=k}while(--O);return c(p.concat(b))+c(x)},f=function(l,u,h,U){if(A.canvas&&A.dataURL)if(typeof l=="string"&&(l=document.getElementById(l)),U===void 0&&(U="png"),U=o(U),/bmp/.test(U)){let C=i(t(l,u,h)),p=B(C);n(a(p,e))}else{let C=r(l,U,u,h);n(C.replace(U,e))}},d=function(l,u,h,U){if(A.canvas&&A.dataURL){if(typeof l=="string"&&(l=document.getElementById(l)),U===void 0&&(U="png"),U=o(U),/bmp/.test(U)){let p=i(t(l,u,h)),b=B(p);return s(a(b,"image/bmp"))}let C=r(l,U,u,h);return s(C)}};return{saveAsImage:f,saveAsPNG:function(l,u,h){return f(l,u,h,"png")},saveAsJPEG:function(l,u,h){return f(l,u,h,"jpeg")},saveAsGIF:function(l,u,h){return f(l,u,h,"gif")},saveAsBMP:function(l,u,h){return f(l,u,h,"bmp")},convertToImage:d,convertToPNG:function(l,u,h){return d(l,u,h,"png")},convertToJPEG:function(l,u,h){return d(l,u,h,"jpeg")},convertToGIF:function(l,u,h){return d(l,u,h,"gif")},convertToBMP:function(l,u,h){return d(l,u,h,"bmp")}}}(),Zo=If;var cr=Sn(Nr(),1);function Je(A,e){return function(){return A.apply(e,arguments)}}var{toString:Rf}=Object.prototype,{getPrototypeOf:Qn}=Object,kt=(A=>e=>{let t=Rf.call(e);return A[t]||(A[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),pA=A=>(A=A.toLowerCase(),e=>kt(e)===A),qt=A=>e=>typeof e===A,{isArray:Qe}=Array,We=qt("undefined");function bf(A){return A!==null&&!We(A)&&A.constructor!==null&&!We(A.constructor)&&fA(A.constructor.isBuffer)&&A.constructor.isBuffer(A)}var ei=pA("ArrayBuffer");function xf(A){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(A):e=A&&A.buffer&&ei(A.buffer),e}var Of=qt("string"),fA=qt("function"),ti=qt("number"),jt=A=>A!==null&&typeof A=="object",Sf=A=>A===!0||A===!1,Wt=A=>{if(kt(A)!=="object")return!1;let e=Qn(A);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in A)&&!(Symbol.iterator in A)},yf=pA("Date"),Tf=pA("File"),Mf=pA("Blob"),Lf=pA("FileList"),_f=A=>jt(A)&&fA(A.pipe),Df=A=>{let e;return A&&(typeof FormData=="function"&&A instanceof FormData||fA(A.append)&&((e=kt(A))==="formdata"||e==="object"&&fA(A.toString)&&A.toString()==="[object FormData]"))},Vf=pA("URLSearchParams"),Pf=A=>A.trim?A.trim():A.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ke(A,e,{allOwnKeys:t=!1}={}){if(A===null||typeof A>"u")return;let r,n;if(typeof A!="object"&&(A=[A]),Qe(A))for(r=0,n=A.length;r<n;r++)e.call(null,A[r],r,A);else{let s=t?Object.getOwnPropertyNames(A):Object.keys(A),o=s.length,c;for(r=0;r<o;r++)c=s[r],e.call(null,A[c],c,A)}}function ri(A,e){e=e.toLowerCase();let t=Object.keys(A),r=t.length,n;for(;r-- >0;)if(n=t[r],e===n.toLowerCase())return n;return null}var ni=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),si=A=>!We(A)&&A!==ni;function hn(){let{caseless:A}=si(this)&&this||{},e={},t=(r,n)=>{let s=A&&ri(e,n)||n;Wt(e[s])&&Wt(r)?e[s]=hn(e[s],r):Wt(r)?e[s]=hn({},r):Qe(r)?e[s]=r.slice():e[s]=r};for(let r=0,n=arguments.length;r<n;r++)arguments[r]&&ke(arguments[r],t);return e}var zf=(A,e,t,{allOwnKeys:r}={})=>(ke(e,(n,s)=>{t&&fA(n)?A[s]=Je(n,t):A[s]=n},{allOwnKeys:r}),A),Xf=A=>(A.charCodeAt(0)===65279&&(A=A.slice(1)),A),vf=(A,e,t,r)=>{A.prototype=Object.create(e.prototype,r),A.prototype.constructor=A,Object.defineProperty(A,"super",{value:e.prototype}),t&&Object.assign(A.prototype,t)},Gf=(A,e,t,r)=>{let n,s,o,c={};if(e=e||{},A==null)return e;do{for(n=Object.getOwnPropertyNames(A),s=n.length;s-- >0;)o=n[s],(!r||r(o,A,e))&&!c[o]&&(e[o]=A[o],c[o]=!0);A=t!==!1&&Qn(A)}while(A&&(!t||t(A,e))&&A!==Object.prototype);return e},Jf=(A,e,t)=>{A=String(A),(t===void 0||t>A.length)&&(t=A.length),t-=e.length;let r=A.indexOf(e,t);return r!==-1&&r===t},Wf=A=>{if(!A)return null;if(Qe(A))return A;let e=A.length;if(!ti(e))return null;let t=new Array(e);for(;e-- >0;)t[e]=A[e];return t},kf=(A=>e=>A&&e instanceof A)(typeof Uint8Array<"u"&&Qn(Uint8Array)),qf=(A,e)=>{let r=(A&&A[Symbol.iterator]).call(A),n;for(;(n=r.next())&&!n.done;){let s=n.value;e.call(A,s[0],s[1])}},jf=(A,e)=>{let t,r=[];for(;(t=A.exec(e))!==null;)r.push(t);return r},$f=pA("HTMLFormElement"),Zf=A=>A.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,r,n){return r.toUpperCase()+n}),Yo=(({hasOwnProperty:A})=>(e,t)=>A.call(e,t))(Object.prototype),Yf=pA("RegExp"),oi=(A,e)=>{let t=Object.getOwnPropertyDescriptors(A),r={};ke(t,(n,s)=>{e(n,s,A)!==!1&&(r[s]=n)}),Object.defineProperties(A,r)},Ad=A=>{oi(A,(e,t)=>{if(fA(A)&&["arguments","caller","callee"].indexOf(t)!==-1)return!1;let r=A[t];if(fA(r)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},ed=(A,e)=>{let t={},r=n=>{n.forEach(s=>{t[s]=!0})};return Qe(A)?r(A):r(String(A).split(e)),t},td=()=>{},rd=(A,e)=>(A=+A,Number.isFinite(A)?A:e),dn="abcdefghijklmnopqrstuvwxyz",Ai="0123456789",ii={DIGIT:Ai,ALPHA:dn,ALPHA_DIGIT:dn+dn.toUpperCase()+Ai},nd=(A=16,e=ii.ALPHA_DIGIT)=>{let t="",{length:r}=e;for(;A--;)t+=e[Math.random()*r|0];return t};function sd(A){return!!(A&&fA(A.append)&&A[Symbol.toStringTag]==="FormData"&&A[Symbol.iterator])}var od=A=>{let e=new Array(10),t=(r,n)=>{if(jt(r)){if(e.indexOf(r)>=0)return;if(!("toJSON"in r)){e[n]=r;let s=Qe(r)?[]:{};return ke(r,(o,c)=>{let i=t(o,n+1);!We(i)&&(s[c]=i)}),e[n]=void 0,s}}return r};return t(A,0)},id=pA("AsyncFunction"),cd=A=>A&&(jt(A)||fA(A))&&fA(A.then)&&fA(A.catch),Q={isArray:Qe,isArrayBuffer:ei,isBuffer:bf,isFormData:Df,isArrayBufferView:xf,isString:Of,isNumber:ti,isBoolean:Sf,isObject:jt,isPlainObject:Wt,isUndefined:We,isDate:yf,isFile:Tf,isBlob:Mf,isRegExp:Yf,isFunction:fA,isStream:_f,isURLSearchParams:Vf,isTypedArray:kf,isFileList:Lf,forEach:ke,merge:hn,extend:zf,trim:Pf,stripBOM:Xf,inherits:vf,toFlatObject:Gf,kindOf:kt,kindOfTest:pA,endsWith:Jf,toArray:Wf,forEachEntry:qf,matchAll:jf,isHTMLForm:$f,hasOwnProperty:Yo,hasOwnProp:Yo,reduceDescriptors:oi,freezeMethods:Ad,toObjectSet:ed,toCamelCase:Zf,noop:td,toFiniteNumber:rd,findKey:ri,global:ni,isContextDefined:si,ALPHABET:ii,generateString:nd,isSpecCompliantForm:sd,toJSONObject:od,isAsyncFn:id,isThenable:cd};function ge(A,e,t,r,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=A,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),r&&(this.request=r),n&&(this.response=n)}Q.inherits(ge,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Q.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var ci=ge.prototype,ai={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(A=>{ai[A]={value:A}});Object.defineProperties(ge,ai);Object.defineProperty(ci,"isAxiosError",{value:!0});ge.from=(A,e,t,r,n,s)=>{let o=Object.create(ci);return Q.toFlatObject(A,o,function(i){return i!==Error.prototype},c=>c!=="isAxiosError"),ge.call(o,A.message,e,t,r,n),o.cause=A,o.name=A.name,s&&Object.assign(o,s),o};var I=ge;var $t=null;function gn(A){return Q.isPlainObject(A)||Q.isArray(A)}function ui(A){return Q.endsWith(A,"[]")?A.slice(0,-2):A}function Bi(A,e,t){return A?A.concat(e).map(function(n,s){return n=ui(n),!t&&s?"["+n+"]":n}).join(t?".":""):e}function ad(A){return Q.isArray(A)&&!A.some(gn)}var Bd=Q.toFlatObject(Q,{},null,function(e){return/^is[A-Z]/.test(e)});function ud(A,e,t){if(!Q.isObject(A))throw new TypeError("target must be an object");e=e||new($t||FormData),t=Q.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(h,U){return!Q.isUndefined(U[h])});let r=t.metaTokens,n=t.visitor||B,s=t.dots,o=t.indexes,i=(t.Blob||typeof Blob<"u"&&Blob)&&Q.isSpecCompliantForm(e);if(!Q.isFunction(n))throw new TypeError("visitor must be a function");function a(u){if(u===null)return"";if(Q.isDate(u))return u.toISOString();if(!i&&Q.isBlob(u))throw new I("Blob is not supported. Use a Buffer instead.");return Q.isArrayBuffer(u)||Q.isTypedArray(u)?i&&typeof Blob=="function"?new Blob([u]):Buffer.from(u):u}function B(u,h,U){let C=u;if(u&&!U&&typeof u=="object"){if(Q.endsWith(h,"{}"))h=r?h:h.slice(0,-2),u=JSON.stringify(u);else if(Q.isArray(u)&&ad(u)||(Q.isFileList(u)||Q.endsWith(h,"[]"))&&(C=Q.toArray(u)))return h=ui(h),C.forEach(function(b,N){!(Q.isUndefined(b)||b===null)&&e.append(o===!0?Bi([h],N,s):o===null?h:h+"[]",a(b))}),!1}return gn(u)?!0:(e.append(Bi(U,h,s),a(u)),!1)}let f=[],d=Object.assign(Bd,{defaultVisitor:B,convertValue:a,isVisitable:gn});function l(u,h){if(!Q.isUndefined(u)){if(f.indexOf(u)!==-1)throw Error("Circular reference detected in "+h.join("."));f.push(u),Q.forEach(u,function(C,p){(!(Q.isUndefined(C)||C===null)&&n.call(e,C,Q.isString(p)?p.trim():p,h,d))===!0&&l(C,h?h.concat(p):[p])}),f.pop()}}if(!Q.isObject(A))throw new TypeError("data must be an object");return l(A),e}var GA=ud;function li(A){let e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(A).replace(/[!'()~]|%20|%00/g,function(r){return e[r]})}function fi(A,e){this._pairs=[],A&&GA(A,this,e)}var di=fi.prototype;di.append=function(e,t){this._pairs.push([e,t])};di.toString=function(e){let t=e?function(r){return e.call(this,r,li)}:li;return this._pairs.map(function(n){return t(n[0])+"="+t(n[1])},"").join("&")};var Zt=fi;function ld(A){return encodeURIComponent(A).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function qe(A,e,t){if(!e)return A;let r=t&&t.encode||ld,n=t&&t.serialize,s;if(n?s=n(e,t):s=Q.isURLSearchParams(e)?e.toString():new Zt(e,t).toString(r),s){let o=A.indexOf("#");o!==-1&&(A=A.slice(0,o)),A+=(A.indexOf("?")===-1?"?":"&")+s}return A}var wn=class{constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Q.forEach(this.handlers,function(r){r!==null&&e(r)})}},Cn=wn;var Yt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var hi=typeof URLSearchParams<"u"?URLSearchParams:Zt;var Qi=typeof FormData<"u"?FormData:null;var gi=typeof Blob<"u"?Blob:null;var fd=(()=>{let A;return typeof navigator<"u"&&((A=navigator.product)==="ReactNative"||A==="NativeScript"||A==="NS")?!1:typeof window<"u"&&typeof document<"u"})(),dd=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),AA={isBrowser:!0,classes:{URLSearchParams:hi,FormData:Qi,Blob:gi},isStandardBrowserEnv:fd,isStandardBrowserWebWorkerEnv:dd,protocols:["http","https","file","blob","url","data"]};function Un(A,e){return GA(A,new AA.classes.URLSearchParams,Object.assign({visitor:function(t,r,n,s){return AA.isNode&&Q.isBuffer(t)?(this.append(r,t.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},e))}function hd(A){return Q.matchAll(/\w+|\[(\w*)]/g,A).map(e=>e[0]==="[]"?"":e[1]||e[0])}function Qd(A){let e={},t=Object.keys(A),r,n=t.length,s;for(r=0;r<n;r++)s=t[r],e[s]=A[s];return e}function gd(A){function e(t,r,n,s){let o=t[s++],c=Number.isFinite(+o),i=s>=t.length;return o=!o&&Q.isArray(n)?n.length:o,i?(Q.hasOwnProp(n,o)?n[o]=[n[o],r]:n[o]=r,!c):((!n[o]||!Q.isObject(n[o]))&&(n[o]=[]),e(t,r,n[o],s)&&Q.isArray(n[o])&&(n[o]=Qd(n[o])),!c)}if(Q.isFormData(A)&&Q.isFunction(A.entries)){let t={};return Q.forEachEntry(A,(r,n)=>{e(hd(r),n,t,0)}),t}return null}var Ar=gd;var wd={"Content-Type":void 0};function Cd(A,e,t){if(Q.isString(A))try{return(e||JSON.parse)(A),Q.trim(A)}catch(r){if(r.name!=="SyntaxError")throw r}return(t||JSON.stringify)(A)}var er={transitional:Yt,adapter:["xhr","http"],transformRequest:[function(e,t){let r=t.getContentType()||"",n=r.indexOf("application/json")>-1,s=Q.isObject(e);if(s&&Q.isHTMLForm(e)&&(e=new FormData(e)),Q.isFormData(e))return n&&n?JSON.stringify(Ar(e)):e;if(Q.isArrayBuffer(e)||Q.isBuffer(e)||Q.isStream(e)||Q.isFile(e)||Q.isBlob(e))return e;if(Q.isArrayBufferView(e))return e.buffer;if(Q.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let c;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Un(e,this.formSerializer).toString();if((c=Q.isFileList(e))||r.indexOf("multipart/form-data")>-1){let i=this.env&&this.env.FormData;return GA(c?{"files[]":e}:e,i&&new i,this.formSerializer)}}return s||n?(t.setContentType("application/json",!1),Cd(e)):e}],transformResponse:[function(e){let t=this.transitional||er.transitional,r=t&&t.forcedJSONParsing,n=this.responseType==="json";if(e&&Q.isString(e)&&(r&&!this.responseType||n)){let o=!(t&&t.silentJSONParsing)&&n;try{return JSON.parse(e)}catch(c){if(o)throw c.name==="SyntaxError"?I.from(c,I.ERR_BAD_RESPONSE,this,null,this.response):c}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:AA.classes.FormData,Blob:AA.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};Q.forEach(["delete","get","head"],function(e){er.headers[e]={}});Q.forEach(["post","put","patch"],function(e){er.headers[e]=Q.merge(wd)});var we=er;var Ud=Q.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),wi=A=>{let e={},t,r,n;return A&&A.split(`
`).forEach(function(o){n=o.indexOf(":"),t=o.substring(0,n).trim().toLowerCase(),r=o.substring(n+1).trim(),!(!t||e[t]&&Ud[t])&&(t==="set-cookie"?e[t]?e[t].push(r):e[t]=[r]:e[t]=e[t]?e[t]+", "+r:r)}),e};var Ci=Symbol("internals");function je(A){return A&&String(A).trim().toLowerCase()}function tr(A){return A===!1||A==null?A:Q.isArray(A)?A.map(tr):String(A)}function Ed(A){let e=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g,r;for(;r=t.exec(A);)e[r[1]]=r[2];return e}var pd=A=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(A.trim());function En(A,e,t,r,n){if(Q.isFunction(r))return r.call(this,e,t);if(n&&(e=t),!!Q.isString(e)){if(Q.isString(r))return e.indexOf(r)!==-1;if(Q.isRegExp(r))return r.test(e)}}function Fd(A){return A.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,r)=>t.toUpperCase()+r)}function md(A,e){let t=Q.toCamelCase(" "+e);["get","set","has"].forEach(r=>{Object.defineProperty(A,r+t,{value:function(n,s,o){return this[r].call(this,e,n,s,o)},configurable:!0})})}var Ce=class{constructor(e){e&&this.set(e)}set(e,t,r){let n=this;function s(c,i,a){let B=je(i);if(!B)throw new Error("header name must be a non-empty string");let f=Q.findKey(n,B);(!f||n[f]===void 0||a===!0||a===void 0&&n[f]!==!1)&&(n[f||i]=tr(c))}let o=(c,i)=>Q.forEach(c,(a,B)=>s(a,B,i));return Q.isPlainObject(e)||e instanceof this.constructor?o(e,t):Q.isString(e)&&(e=e.trim())&&!pd(e)?o(wi(e),t):e!=null&&s(t,e,r),this}get(e,t){if(e=je(e),e){let r=Q.findKey(this,e);if(r){let n=this[r];if(!t)return n;if(t===!0)return Ed(n);if(Q.isFunction(t))return t.call(this,n,r);if(Q.isRegExp(t))return t.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=je(e),e){let r=Q.findKey(this,e);return!!(r&&this[r]!==void 0&&(!t||En(this,this[r],r,t)))}return!1}delete(e,t){let r=this,n=!1;function s(o){if(o=je(o),o){let c=Q.findKey(r,o);c&&(!t||En(r,r[c],c,t))&&(delete r[c],n=!0)}}return Q.isArray(e)?e.forEach(s):s(e),n}clear(e){let t=Object.keys(this),r=t.length,n=!1;for(;r--;){let s=t[r];(!e||En(this,this[s],s,e,!0))&&(delete this[s],n=!0)}return n}normalize(e){let t=this,r={};return Q.forEach(this,(n,s)=>{let o=Q.findKey(r,s);if(o){t[o]=tr(n),delete t[s];return}let c=e?Fd(s):String(s).trim();c!==s&&delete t[s],t[c]=tr(n),r[c]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return Q.forEach(this,(r,n)=>{r!=null&&r!==!1&&(t[n]=e&&Q.isArray(r)?r.join(", "):r)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let r=new this(e);return t.forEach(n=>r.set(n)),r}static accessor(e){let r=(this[Ci]=this[Ci]={accessors:{}}).accessors,n=this.prototype;function s(o){let c=je(o);r[c]||(md(n,o),r[c]=!0)}return Q.isArray(e)?e.forEach(s):s(e),this}};Ce.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);Q.freezeMethods(Ce.prototype);Q.freezeMethods(Ce);var rA=Ce;function $e(A,e){let t=this||we,r=e||t,n=rA.from(r.headers),s=r.data;return Q.forEach(A,function(c){s=c.call(t,s,n.normalize(),e?e.status:void 0)}),n.normalize(),s}function Ze(A){return!!(A&&A.__CANCEL__)}function Ui(A,e,t){I.call(this,A??"canceled",I.ERR_CANCELED,e,t),this.name="CanceledError"}Q.inherits(Ui,I,{__CANCEL__:!0});var JA=Ui;function pn(A,e,t){let r=t.config.validateStatus;!t.status||!r||r(t.status)?A(t):e(new I("Request failed with status code "+t.status,[I.ERR_BAD_REQUEST,I.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}var Ei=AA.isStandardBrowserEnv?function(){return{write:function(t,r,n,s,o,c){let i=[];i.push(t+"="+encodeURIComponent(r)),Q.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),Q.isString(s)&&i.push("path="+s),Q.isString(o)&&i.push("domain="+o),c===!0&&i.push("secure"),document.cookie=i.join("; ")},read:function(t){let r=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove:function(t){this.write(t,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}();function Fn(A){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(A)}function mn(A,e){return e?A.replace(/\/+$/,"")+"/"+e.replace(/^\/+/,""):A}function Ye(A,e){return A&&!Fn(e)?mn(A,e):e}var pi=AA.isStandardBrowserEnv?function(){let e=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a"),r;function n(s){let o=s;return e&&(t.setAttribute("href",o),o=t.href),t.setAttribute("href",o),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:t.pathname.charAt(0)==="/"?t.pathname:"/"+t.pathname}}return r=n(window.location.href),function(o){let c=Q.isString(o)?n(o):o;return c.protocol===r.protocol&&c.host===r.host}}():function(){return function(){return!0}}();function Hn(A){let e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(A);return e&&e[1]||""}function Hd(A,e){A=A||10;let t=new Array(A),r=new Array(A),n=0,s=0,o;return e=e!==void 0?e:1e3,function(i){let a=Date.now(),B=r[s];o||(o=a),t[n]=i,r[n]=a;let f=s,d=0;for(;f!==n;)d+=t[f++],f=f%A;if(n=(n+1)%A,n===s&&(s=(s+1)%A),a-o<e)return;let l=B&&a-B;return l?Math.round(d*1e3/l):void 0}}var Fi=Hd;function mi(A,e){let t=0,r=Fi(50,250);return n=>{let s=n.loaded,o=n.lengthComputable?n.total:void 0,c=s-t,i=r(c),a=s<=o;t=s;let B={loaded:s,total:o,progress:o?s/o:void 0,bytes:c,rate:i||void 0,estimated:i&&o&&a?(o-s)/i:void 0,event:n};B[e?"download":"upload"]=!0,A(B)}}var Nd=typeof XMLHttpRequest<"u",Hi=Nd&&function(A){return new Promise(function(t,r){let n=A.data,s=rA.from(A.headers).normalize(),o=A.responseType,c;function i(){A.cancelToken&&A.cancelToken.unsubscribe(c),A.signal&&A.signal.removeEventListener("abort",c)}Q.isFormData(n)&&(AA.isStandardBrowserEnv||AA.isStandardBrowserWebWorkerEnv?s.setContentType(!1):s.setContentType("multipart/form-data;",!1));let a=new XMLHttpRequest;if(A.auth){let l=A.auth.username||"",u=A.auth.password?unescape(encodeURIComponent(A.auth.password)):"";s.set("Authorization","Basic "+btoa(l+":"+u))}let B=Ye(A.baseURL,A.url);a.open(A.method.toUpperCase(),qe(B,A.params,A.paramsSerializer),!0),a.timeout=A.timeout;function f(){if(!a)return;let l=rA.from("getAllResponseHeaders"in a&&a.getAllResponseHeaders()),h={data:!o||o==="text"||o==="json"?a.responseText:a.response,status:a.status,statusText:a.statusText,headers:l,config:A,request:a};pn(function(C){t(C),i()},function(C){r(C),i()},h),a=null}if("onloadend"in a?a.onloadend=f:a.onreadystatechange=function(){!a||a.readyState!==4||a.status===0&&!(a.responseURL&&a.responseURL.indexOf("file:")===0)||setTimeout(f)},a.onabort=function(){a&&(r(new I("Request aborted",I.ECONNABORTED,A,a)),a=null)},a.onerror=function(){r(new I("Network Error",I.ERR_NETWORK,A,a)),a=null},a.ontimeout=function(){let u=A.timeout?"timeout of "+A.timeout+"ms exceeded":"timeout exceeded",h=A.transitional||Yt;A.timeoutErrorMessage&&(u=A.timeoutErrorMessage),r(new I(u,h.clarifyTimeoutError?I.ETIMEDOUT:I.ECONNABORTED,A,a)),a=null},AA.isStandardBrowserEnv){let l=(A.withCredentials||pi(B))&&A.xsrfCookieName&&Ei.read(A.xsrfCookieName);l&&s.set(A.xsrfHeaderName,l)}n===void 0&&s.setContentType(null),"setRequestHeader"in a&&Q.forEach(s.toJSON(),function(u,h){a.setRequestHeader(h,u)}),Q.isUndefined(A.withCredentials)||(a.withCredentials=!!A.withCredentials),o&&o!=="json"&&(a.responseType=A.responseType),typeof A.onDownloadProgress=="function"&&a.addEventListener("progress",mi(A.onDownloadProgress,!0)),typeof A.onUploadProgress=="function"&&a.upload&&a.upload.addEventListener("progress",mi(A.onUploadProgress)),(A.cancelToken||A.signal)&&(c=l=>{a&&(r(!l||l.type?new JA(null,A,a):l),a.abort(),a=null)},A.cancelToken&&A.cancelToken.subscribe(c),A.signal&&(A.signal.aborted?c():A.signal.addEventListener("abort",c)));let d=Hn(B);if(d&&AA.protocols.indexOf(d)===-1){r(new I("Unsupported protocol "+d+":",I.ERR_BAD_REQUEST,A));return}a.send(n||null)})};var rr={http:$t,xhr:Hi};Q.forEach(rr,(A,e)=>{if(A){try{Object.defineProperty(A,"name",{value:e})}catch{}Object.defineProperty(A,"adapterName",{value:e})}});var Ni={getAdapter:A=>{A=Q.isArray(A)?A:[A];let{length:e}=A,t,r;for(let n=0;n<e&&(t=A[n],!(r=Q.isString(t)?rr[t.toLowerCase()]:t));n++);if(!r)throw r===!1?new I(`Adapter ${t} is not supported by the environment`,"ERR_NOT_SUPPORT"):new Error(Q.hasOwnProp(rr,t)?`Adapter '${t}' is not available in the build`:`Unknown adapter '${t}'`);if(!Q.isFunction(r))throw new TypeError("adapter is not a function");return r},adapters:rr};function Nn(A){if(A.cancelToken&&A.cancelToken.throwIfRequested(),A.signal&&A.signal.aborted)throw new JA(null,A)}function nr(A){return Nn(A),A.headers=rA.from(A.headers),A.data=$e.call(A,A.transformRequest),["post","put","patch"].indexOf(A.method)!==-1&&A.headers.setContentType("application/x-www-form-urlencoded",!1),Ni.getAdapter(A.adapter||we.adapter)(A).then(function(r){return Nn(A),r.data=$e.call(A,A.transformResponse,r),r.headers=rA.from(r.headers),r},function(r){return Ze(r)||(Nn(A),r&&r.response&&(r.response.data=$e.call(A,A.transformResponse,r.response),r.response.headers=rA.from(r.response.headers))),Promise.reject(r)})}var Ki=A=>A instanceof rA?A.toJSON():A;function xA(A,e){e=e||{};let t={};function r(a,B,f){return Q.isPlainObject(a)&&Q.isPlainObject(B)?Q.merge.call({caseless:f},a,B):Q.isPlainObject(B)?Q.merge({},B):Q.isArray(B)?B.slice():B}function n(a,B,f){if(Q.isUndefined(B)){if(!Q.isUndefined(a))return r(void 0,a,f)}else return r(a,B,f)}function s(a,B){if(!Q.isUndefined(B))return r(void 0,B)}function o(a,B){if(Q.isUndefined(B)){if(!Q.isUndefined(a))return r(void 0,a)}else return r(void 0,B)}function c(a,B,f){if(f in e)return r(a,B);if(f in A)return r(void 0,a)}let i={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:c,headers:(a,B)=>n(Ki(a),Ki(B),!0)};return Q.forEach(Object.keys(Object.assign({},A,e)),function(B){let f=i[B]||n,d=f(A[B],e[B],B);Q.isUndefined(d)&&f!==c||(t[B]=d)}),t}var sr="1.4.0";var Kn={};["object","boolean","number","function","string","symbol"].forEach((A,e)=>{Kn[A]=function(r){return typeof r===A||"a"+(e<1?"n ":" ")+A}});var Ii={};Kn.transitional=function(e,t,r){function n(s,o){return"[Axios v"+sr+"] Transitional option '"+s+"'"+o+(r?". "+r:"")}return(s,o,c)=>{if(e===!1)throw new I(n(o," has been removed"+(t?" in "+t:"")),I.ERR_DEPRECATED);return t&&!Ii[o]&&(Ii[o]=!0,console.warn(n(o," has been deprecated since v"+t+" and will be removed in the near future"))),e?e(s,o,c):!0}};function Kd(A,e,t){if(typeof A!="object")throw new I("options must be an object",I.ERR_BAD_OPTION_VALUE);let r=Object.keys(A),n=r.length;for(;n-- >0;){let s=r[n],o=e[s];if(o){let c=A[s],i=c===void 0||o(c,s,A);if(i!==!0)throw new I("option "+s+" must be "+i,I.ERR_BAD_OPTION_VALUE);continue}if(t!==!0)throw new I("Unknown option "+s,I.ERR_BAD_OPTION)}}var or={assertOptions:Kd,validators:Kn};var WA=or.validators,Ue=class{constructor(e){this.defaults=e,this.interceptors={request:new Cn,response:new Cn}}request(e,t){typeof e=="string"?(t=t||{},t.url=e):t=e||{},t=xA(this.defaults,t);let{transitional:r,paramsSerializer:n,headers:s}=t;r!==void 0&&or.assertOptions(r,{silentJSONParsing:WA.transitional(WA.boolean),forcedJSONParsing:WA.transitional(WA.boolean),clarifyTimeoutError:WA.transitional(WA.boolean)},!1),n!=null&&(Q.isFunction(n)?t.paramsSerializer={serialize:n}:or.assertOptions(n,{encode:WA.function,serialize:WA.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o;o=s&&Q.merge(s.common,s[t.method]),o&&Q.forEach(["delete","get","head","post","put","patch","common"],u=>{delete s[u]}),t.headers=rA.concat(o,s);let c=[],i=!0;this.interceptors.request.forEach(function(h){typeof h.runWhen=="function"&&h.runWhen(t)===!1||(i=i&&h.synchronous,c.unshift(h.fulfilled,h.rejected))});let a=[];this.interceptors.response.forEach(function(h){a.push(h.fulfilled,h.rejected)});let B,f=0,d;if(!i){let u=[nr.bind(this),void 0];for(u.unshift.apply(u,c),u.push.apply(u,a),d=u.length,B=Promise.resolve(t);f<d;)B=B.then(u[f++],u[f++]);return B}d=c.length;let l=t;for(f=0;f<d;){let u=c[f++],h=c[f++];try{l=u(l)}catch(U){h.call(this,U);break}}try{B=nr.call(this,l)}catch(u){return Promise.reject(u)}for(f=0,d=a.length;f<d;)B=B.then(a[f++],a[f++]);return B}getUri(e){e=xA(this.defaults,e);let t=Ye(e.baseURL,e.url);return qe(t,e.params,e.paramsSerializer)}};Q.forEach(["delete","get","head","options"],function(e){Ue.prototype[e]=function(t,r){return this.request(xA(r||{},{method:e,url:t,data:(r||{}).data}))}});Q.forEach(["post","put","patch"],function(e){function t(r){return function(s,o,c){return this.request(xA(c||{},{method:e,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:o}))}}Ue.prototype[e]=t(),Ue.prototype[e+"Form"]=t(!0)});var At=Ue;var et=class{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(s){t=s});let r=this;this.promise.then(n=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](n);r._listeners=null}),this.promise.then=n=>{let s,o=new Promise(c=>{r.subscribe(c),s=c}).then(n);return o.cancel=function(){r.unsubscribe(s)},o},e(function(s,o,c){r.reason||(r.reason=new JA(s,o,c),t(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);t!==-1&&this._listeners.splice(t,1)}static source(){let e;return{token:new et(function(n){e=n}),cancel:e}}},Ri=et;function In(A){return function(t){return A.apply(null,t)}}function Rn(A){return Q.isObject(A)&&A.isAxiosError===!0}var bn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(bn).forEach(([A,e])=>{bn[e]=A});var bi=bn;function xi(A){let e=new At(A),t=Je(At.prototype.request,e);return Q.extend(t,At.prototype,e,{allOwnKeys:!0}),Q.extend(t,e,null,{allOwnKeys:!0}),t.create=function(n){return xi(xA(A,n))},t}var G=xi(we);G.Axios=At;G.CanceledError=JA;G.CancelToken=Ri;G.isCancel=Ze;G.VERSION=sr;G.toFormData=GA;G.AxiosError=I;G.Cancel=G.CanceledError;G.all=function(e){return Promise.all(e)};G.spread=In;G.isAxiosError=Rn;G.mergeConfig=xA;G.AxiosHeaders=rA;G.formToJSON=A=>Ar(Q.isHTMLForm(A)?new FormData(A):A);G.HttpStatusCode=bi;G.default=G;var ir=G;var{Axios:Kg,AxiosError:Ig,CanceledError:Rg,isCancel:bg,CancelToken:xg,VERSION:Og,all:Sg,Cancel:yg,isAxiosError:Tg,spread:Mg,toFormData:Lg,AxiosHeaders:_g,HttpStatusCode:Dg,formToJSON:Vg,mergeConfig:Pg}=ir;var xn=class{constructor(e){this.viewer=e.viewer,this.olMap=e.olMap,this.screenId=e.screenId,this.olMapId=e.olMapId,this.viewerId=e.viewerId,this.cameraManager=new ot(e.viewer,e.olMap),this.groupManager=new it}async createViewPoint(e,t,r,n,s){this.viewer.render();let o=await Oi(this.screenId,n,s,this.olMapId,this.viewerId),{position:c,heading:i,pitch:a,roll:B}=this.viewer.camera,{longitude:f,latitude:d,height:l}=Cesium.Cartographic.fromCartesian(c),u=Cesium.Math.toDegrees(f),h=Cesium.Math.toDegrees(d),U=Cesium.Math.toDegrees(i),C=Cesium.Math.toDegrees(a)+90,p=Cesium.Math.toDegrees(B),b=this.olMap.getView().getZoom(),N=this.olMap.getView().getResolutionForZoom(b),L=this.olMap.getView().getProjection(),x=this.olMap.getView().getCenter(),y=0;L.code_==="EPSG:3857"?(y=N*96*39.37001*11e4/111319,x=ol.proj.transform(x,L,"EPSG:4326")):y=N*96*39.37001*11e4;let O=e.ViewPoint;if(!(O instanceof Array)){let K=e.ViewPoint;O=[],Object.keys(K).length&&O.push(K)}let H={type:"view",name:t,guid:r,CamLon:u,CamLat:h,CamAltitude:l,CamHeading:U,CamTilt:C,CamRoll:p,GlobeVisible:s,MapVisible:n,Layer2D:{CurrentScale:y,CenterX:x[0],CenterY:x[1]},LinkPlottingPath:r+"_armyMark.sml",SituationFilePath:"",SpecialTopicsPath:""};O.push(H);let P=O,M={parent_code:e.parent_code,ViewPoint:P},k=this.modifyViewPointXML(M);return{newViewPoints:M,base64URL:o,newViewPointXML:k}}async editViewPoint(e,t,r,n,s,o){this.viewer.render();let c=await Oi(this.screenId,s,o,this.olMapId,this.viewerId),{position:i,heading:a,pitch:B,roll:f}=this.viewer.camera,{longitude:d,latitude:l,height:u}=Cesium.Cartographic.fromCartesian(i),h=Cesium.Math.toDegrees(d),U=Cesium.Math.toDegrees(l),C=Cesium.Math.toDegrees(a),p=Cesium.Math.toDegrees(B)+90,b=Cesium.Math.toDegrees(f),N=this.olMap.getView().getZoom(),L=this.olMap.getView().getResolutionForZoom(N),x=this.olMap.getView().getProjection(),y=this.olMap.getView().getCenter(),O=0;x.code_==="EPSG:3857"?(O=L*96*39.37001*11e4/111319,y=ol.proj.transform(y,x,"EPSG:4326")):O=L*96*39.37001*11e4;let H=e.ViewPoint;if(H instanceof Array){for(let K of H)if(K.guid==r){K.name=t,K.guid=n,K.CamLon=h,K.CamLat=U,K.CamAltitude=u,K.CamHeading=C,K.CamTilt=p,K.CamRoll=b,K.GlobeVisible=o,K.MapVisible=s,K.Layer2D={CurrentScale:O,CenterX:y[0],CenterY:y[1]},K.LinkPlottingPath=n+"_armyMark.sml";break}}else H.name=t,H.guid=n,H.CamLon=h,H.CamLat=U,H.CamAltitude=u,H.CamHeading=C,H.CamTilt=p,H.CamRoll=b,H.GlobeVisible=o,H.MapVisible=s,H.Layer2D={CurrentScale:O,CenterX:y[0],CenterY:y[1]},H.LinkPlottingPath=n+"_armyMark.sml";let P=H,M={parent_code:e.parent_code,ViewPoint:P},k=this.modifyViewPointXML(M);return{newViewPoints:M,base64URL:c,newViewPointXML:k}}deleteViewPoint(e,t){let r=e.ViewPoint,n={parent_code:e.parent_code,ViewPoint:{}};if(r instanceof Array){let o=r.findIndex(c=>c.guid==t);r.splice(o,1),r.length==1?n.ViewPoint=r[0]:n.ViewPoint=r}let s=this.modifyViewPointXML(n);return{newViewPoints:n,newViewPointXML:s}}saveViewPoint(e){return this.modifyViewPointXML(e)}modifyViewPointXML(e){if(e&&e.ViewPoint&&Object.keys(e.ViewPoint).length){let t={config:{ViewNavigation:JSON.parse(JSON.stringify(e))}},r=e.ViewPoint,n;r instanceof Array?n=r.map(c=>this.dataFormat(c)):n=this.dataFormat(r),t.config.ViewNavigation.ViewPoint=n;let s=t.config.ViewNavigation.parent_code;return delete t.config.ViewNavigation.parent_code,t.config.ViewNavigation["@parent_code"]=s,Si(t)}else return null}async loadViewPointXML(e){let t=null;return Id(e).then(r=>{t=r}),t}saveViewPointXML(e,t){let r=e.config.ViewNavigation.ViewPoint;if(r instanceof Array)for(let s=0;s<r.length;s++){let o=this.dataFormat(r[s]);r[s]=o}else r=this.dataFormat(r);let n=e.config.ViewNavigation.parent_code;delete e.config.ViewNavigation.parent_code,e.config.ViewNavigation["@parent_code"]=n,Rd(Si(e),t)}dataFormat(e){return{"@type":e.type,"@name":e.name,"@guid":e.guid,"@CamLon":e.CamLon,"@CamLat":e.CamLat,"@CamAltitude":e.CamAltitude,"@CamHeading":e.CamHeading,"@CamTilt":e.CamTilt,"@CamRoll":e.CamRoll,"@LinkPlottingPath":e.LinkPlottingPath,"@SituationFilePath":e.SituationFilePath,"@SpecialTopicsPath":e.SpecialTopicsPath,GlobeVisible:e.GlobeVisible,MapVisible:e.MapVisible,Layer2D:{"@CurrentScale":e.Layer2D.CurrentScale,"@CenterX":e.Layer2D.CenterX,"@CenterY":e.Layer2D.CenterY}}}};async function Oi(A,e,t,r,n){let s="",o=null;return e&&t?o=document.getElementById(A):t&&!e?o=document.getElementById(n):!t&&e&&(o=document.getElementById(r)),await $o(o,{useCORS:!0,allowTaint:!0}).then(function(c){let i=c.width,a=c.height;s=Zo.convertToImage(c,i,a,"jpg").src}),s}async function Id(A){try{let e=null;if(await ir.get(A).then(t=>{e=t}).catch(t=>{e={data:""}}),e.data){let t=e.data,n=new DOMParser().parseFromString(t,"application/xml");return bd.toJSON(t,[n.documentElement.tagName,"0"])}else return""}catch(e){console.log(e)}}function Si(A,e="@"){let t={ignoreAttributes:!1,attributeNamePrefix:e,format:!0};return new cr.XMLBuilder(t).build(A)}function Rd(A,e){if(cr.XMLValidator.validate(A,{allowBooleanAttributes:!0})&&e){let r=new Blob([A],{type:"application/xml"}),n=document.createElement("a");n.href=window.URL.createObjectURL(r),n.download=e,n.click(),window.URL.revokeObjectURL(n.href)}}var bd=function(){function A(t,r,n,s){var o=new DOMParser().parseFromString(t,"application/xml"),c=function(d){var l=d.ownerDocument.createNSResolver(d),u=d.getAttribute("xmlns");return function(h){return l.lookupNamespaceURI(h)||u}}(o.documentElement),i=o.documentElement.getAttribute("xmlns")?"default:":"",a=new Array(+r[1]+1).join("/*")+"/"+i+r[0],B=o.evaluate(a,o,c,XPathResult.ORDERED_NODE_ITERATOR_TYPE,null),f=B.iterateNext();return s=typeof s>"u"?!1:!!s,e(f,s?"":"    ",n)}function e(t,r,n){n=typeof n>"u"?!0:!!n;var s=!!r.length,o={toObj:function(i){var a={};if(i.nodeType==1){if(n&&i.attributes.length)for(var B=0;B<i.attributes.length;B++)a[i.attributes[B].nodeName]=(i.attributes[B].nodeValue||"").toString();if(i.firstChild){for(var f=0,d=0,l=!1,u=i.firstChild;u;u=u.nextSibling)u.nodeType==1?l=!0:u.nodeType==3&&u.nodeValue.match(/[^ \f\n\r\t\v]/)?f++:u.nodeType==4&&d++;if(l)if(f<2&&d<2){o.removeWhite(i);for(var u=i.firstChild;u;u=u.nextSibling)u.nodeType==3?a["#text"]=o.escape(u.nodeValue):u.nodeType==4?a["#cdata"]=o.escape(u.nodeValue):a[u.nodeName]?a[u.nodeName]instanceof Array?a[u.nodeName][a[u.nodeName].length]=o.toObj(u):a[u.nodeName]=[a[u.nodeName],o.toObj(u)]:a[u.nodeName]=o.toObj(u)}else i.attributes.length?a["#text"]=o.escape(o.innerXml(i)):a=o.escape(o.innerXml(i));else if(f)n&&i.attributes.length?a["#text"]=o.escape(o.innerXml(i)):a=o.escape(o.innerXml(i));else if(d)if(d>1)a=o.escape(o.innerXml(i));else for(var u=i.firstChild;u;u=u.nextSibling)a["#cdata"]=o.escape(u.nodeValue)}!i.attributes.length&&!i.firstChild&&(a=null)}else i.nodeType==9?a=o.toObj(i.documentElement):console.log("unhandled node type: "+i.nodeType);return a},toJson:function(i,a,B){var f=a?'"'+a+'"':"";if(i instanceof Array){for(var d=0,l=i.length;d<l;d++)i[d]=o.toJson(i[d],"",B+"	");f+=(a?s?": [":":[":"[")+(i.length>1?`
`+B+"	"+i.join(`,
`+B+"	")+`
`+B:i.join(""))+"]"}else if(i==null)f+=(a&&":")+"null";else if(typeof i=="object"){var u=[];for(var h in i)u[u.length]=o.toJson(i[h],h,B+"	");f+=(a?s?": {":":{":"{")+(u.length>=1?`
`+B+"	"+u.join(`,
`+B+"	")+`
`+B:u.join(""))+"}"}else typeof i=="string"?f+=(a&&(s?": ":":"))+'"'+i.toString()+'"':f+=(a&&(s?": ":":"))+i.toString();return f},innerXml:function(i){var a="";if("innerHTML"in i)a=i.innerHTML;else for(var B=function(d){var l="";if(d.nodeType==1){l+="<"+d.nodeName;for(var u=0;u<d.attributes.length;u++)l+=" "+d.attributes[u].nodeName+'="'+(d.attributes[u].nodeValue||"").toString()+'"';if(d.firstChild){l+=">";for(var h=d.firstChild;h;h=h.nextSibling)l+=B(h);l+="</"+d.nodeName+">"}else l+="/>"}else d.nodeType==3?l+=d.nodeValue:d.nodeType==4&&(l+="<![CDATA["+d.nodeValue+"]]>");return l},f=i.firstChild;f;f=f.nextSibling)a+=B(f);return a},escape:function(i){return i.replace(/[\\]/g,"\\\\").replace(/[\"]/g,'\\"').replace(/[\n]/g,"\\n").replace(/[\r]/g,"\\r")},removeWhite:function(i){i.normalize();for(var a=i.firstChild;a;)if(a.nodeType==3)if(a.nodeValue.match(/[^ \f\n\r\t\v]/))a=a.nextSibling;else{var B=a.nextSibling;i.removeChild(a),a=B}else a.nodeType==1&&o.removeWhite(a),a=a.nextSibling;return i}};t.nodeType==9&&(t=t.documentElement);var c=o.toJson(o.toObj(o.removeWhite(t)),t.nodeName,"	");return"{"+(s?`
`:"")+r+(r?c.replace(/\t/g,r):c.replace(/\t|\n/g,""))+(s?`
`:"")+"}"}return{toJSON:A}}(),yi=xn;globalThis.CESIUM_VERSION="1.105.2";return Pi(xd);})();
