define(["./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./ArcType-26a3f38d","./BoundingRectangle-a5a1c0f3","./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./ComponentDatatype-ab629b88","./EllipsoidTangentPlane-a6ea67fb","./GeometryAttribute-f7a0845b","./GeometryInstance-1d11f88d","./GeometryOffsetAttribute-2579b8d2","./GeometryPipeline-69b47aa9","./IndexDatatype-58eb7805","./Math-355606c6","./Matrix3-31d1f01f","./PolygonGeometryLibrary-b9a68ba7","./PolygonPipeline-39b84ada","./Rectangle-98b0bef0","./IntersectionTests-01432fe7","./VertexFormat-fbdec922","./Interval-d6c8d27a","./DeveloperError-c85858c1","./combine-0c102d93","./RequestType-735c98f2","./RuntimeError-9b4ce3fb","./WebGLConstants-7f557f93","./AxisAlignedBoundingBox-d98e5354","./Plane-06f34fae","./Matrix2-e4a4609a","./AttributeCompression-d2ca507e","./EncodedCartesian3-94199dac","./arrayRemoveDuplicates-0d8dde26","./EllipsoidRhumbLine-6774fec3","./GeometryAttributes-1e4ddcd2"],(function(t,e,o,n,i,r,a,s,l,u,c,d,h,g,m,p,y,f,P,C,_,b,T,w,x,E,I,A,O,M,v,N,G,H,L,R){"use strict";function V(e,o){this.position=e,t.defined(this.position)||(this.position=new r.Cartesian2),this.tangentPlane=o,t.defined(this.tangentPlane)||(this.tangentPlane=V.NORTH_POLE_TANGENT_PLANE)}Object.defineProperties(V.prototype,{ellipsoid:{get:function(){return this.tangentPlane.ellipsoid}},x:{get:function(){return this.position.x}},y:{get:function(){return this.position.y}},conformalLatitude:{get:function(){const t=r.Cartesian2.magnitude(this.position),e=2*this.ellipsoid.maximumRadius;return this.tangentPlane.plane.normal.z*(p.CesiumMath.PI_OVER_TWO-2*Math.atan2(t,e))}},longitude:{get:function(){let t=p.CesiumMath.PI_OVER_TWO+Math.atan2(this.y,this.x);return t>Math.PI&&(t-=p.CesiumMath.TWO_PI),t}}});const F=new s.Cartographic,S=new a.Cartesian3;V.prototype.getLatitude=function(o){t.defined(o)||(o=e.Ellipsoid.WGS84),F.latitude=this.conformalLatitude,F.longitude=this.longitude,F.height=0;const n=this.ellipsoid.cartographicToCartesian(F,S);return o.cartesianToCartographic(n,F),F.latitude};const D=new _.Ray,B=new a.Cartesian3,z=new a.Cartesian3;V.fromCartesian=function(e,o){const n=p.CesiumMath.signNotZero(e.z);let i=V.NORTH_POLE_TANGENT_PLANE,s=V.SOUTH_POLE;n<0&&(i=V.SOUTH_POLE_TANGENT_PLANE,s=V.NORTH_POLE);const l=D;l.origin=i.ellipsoid.scaleToGeocentricSurface(e,l.origin),l.direction=a.Cartesian3.subtract(l.origin,s,B),a.Cartesian3.normalize(l.direction,l.direction);const u=_.IntersectionTests.rayPlane(l,i.plane,z),c=a.Cartesian3.subtract(u,s,u),d=a.Cartesian3.dot(i.xAxis,c),h=n*a.Cartesian3.dot(i.yAxis,c);return t.defined(o)?(o.position=new r.Cartesian2(d,h),o.tangentPlane=i,o):new V(new r.Cartesian2(d,h),i)},V.fromCartesianArray=function(e,o){const n=e.length;t.defined(o)?o.length=n:o=new Array(n);for(let t=0;t<n;t++)o[t]=V.fromCartesian(e[t],o[t]);return o},V.clone=function(e,o){if(t.defined(e))return t.defined(o)?(o.position=e.position,o.tangentPlane=e.tangentPlane,o):new V(e.position,e.tangentPlane)},V.HALF_UNIT_SPHERE=Object.freeze(new e.Ellipsoid(.5,.5,.5)),V.NORTH_POLE=Object.freeze(new a.Cartesian3(0,0,.5)),V.SOUTH_POLE=Object.freeze(new a.Cartesian3(0,0,-.5)),V.NORTH_POLE_TANGENT_PLANE=Object.freeze(new u.EllipsoidTangentPlane(V.NORTH_POLE,V.HALF_UNIT_SPHERE)),V.SOUTH_POLE_TANGENT_PLANE=Object.freeze(new u.EllipsoidTangentPlane(V.SOUTH_POLE,V.HALF_UNIT_SPHERE));const k=new s.Cartographic,W=new s.Cartographic;function U(t,e,o,n){const i=n.cartesianToCartographic(t,k).height,r=n.cartesianToCartographic(e,W);r.height=i,n.cartographicToCartesian(r,e);const a=n.cartesianToCartographic(o,W);a.height=i-100,n.cartographicToCartesian(a,o)}const j=new n.BoundingRectangle,Y=new a.Cartesian3,q=new a.Cartesian3,Q=new a.Cartesian3,Z=new a.Cartesian3,J=new a.Cartesian3,K=new a.Cartesian3;let X=new a.Cartesian3,$=new a.Cartesian3,tt=new a.Cartesian3;const et=new r.Cartesian2,ot=new r.Cartesian2,nt=new a.Cartesian3,it=new i.Quaternion,rt=new y.Matrix3,at=new y.Matrix3;function st(e){const o=e.vertexFormat,n=e.geometry,s=e.shadowVolume,u=n.attributes.position.values,d=t.defined(n.attributes.st)?n.attributes.st.values:void 0;let g=u.length;const m=e.wall,f=e.top||m,P=e.bottom||m;if(o.st||o.normal||o.tangent||o.bitangent||s){const h=e.boundingRectangle,C=e.rotationAxis,_=e.projectTo2d,b=e.ellipsoid,T=e.stRotation,w=e.perPositionHeight,x=et;x.x=h.x,x.y=h.y;const E=o.st?new Float32Array(g/3*2):void 0;let I;o.normal&&(I=w&&f&&!m?n.attributes.normal.values:new Float32Array(g));const A=o.tangent?new Float32Array(g):void 0,O=o.bitangent?new Float32Array(g):void 0,M=s?new Float32Array(g):void 0;let v=0,N=0,G=q,H=Q,L=Z,R=!0,V=rt,F=at;if(0!==T){let t=i.Quaternion.fromAxisAngle(C,T,it);V=y.Matrix3.fromQuaternion(t,V),t=i.Quaternion.fromAxisAngle(C,-T,it),F=y.Matrix3.fromQuaternion(t,F)}else V=y.Matrix3.clone(y.Matrix3.IDENTITY,V),F=y.Matrix3.clone(y.Matrix3.IDENTITY,F);let S=0,D=0;f&&P&&(S=g/2,D=g/3,g/=2);for(let n=0;n<g;n+=3){const i=a.Cartesian3.fromArray(u,n,nt);if(o.st&&!t.defined(d)){let t=y.Matrix3.multiplyByVector(V,i,Y);t=b.scaleToGeodeticSurface(t,t);const e=_(t,ot);r.Cartesian2.subtract(e,x,e);const o=p.CesiumMath.clamp(e.x/h.width,0,1),n=p.CesiumMath.clamp(e.y/h.height,0,1);P&&(E[v+D]=o,E[v+1+D]=n),f&&(E[v]=o,E[v+1]=n),v+=2}if(o.normal||o.tangent||o.bitangent||s){const t=N+1,r=N+2;if(m){if(n+3<g){const t=a.Cartesian3.fromArray(u,n+3,J);if(R){const e=a.Cartesian3.fromArray(u,n+g,K);w&&U(i,t,e,b),a.Cartesian3.subtract(t,i,t),a.Cartesian3.subtract(e,i,e),G=a.Cartesian3.normalize(a.Cartesian3.cross(e,t,G),G),R=!1}a.Cartesian3.equalsEpsilon(t,i,p.CesiumMath.EPSILON10)&&(R=!0)}(o.tangent||o.bitangent)&&(L=b.geodeticSurfaceNormal(i,L),o.tangent&&(H=a.Cartesian3.normalize(a.Cartesian3.cross(L,G,H),H)))}else G=b.geodeticSurfaceNormal(i,G),(o.tangent||o.bitangent)&&(w&&(X=a.Cartesian3.fromArray(I,N,X),$=a.Cartesian3.cross(a.Cartesian3.UNIT_Z,X,$),$=a.Cartesian3.normalize(y.Matrix3.multiplyByVector(F,$,$),$),o.bitangent&&(tt=a.Cartesian3.normalize(a.Cartesian3.cross(X,$,tt),tt))),H=a.Cartesian3.cross(a.Cartesian3.UNIT_Z,G,H),H=a.Cartesian3.normalize(y.Matrix3.multiplyByVector(F,H,H),H),o.bitangent&&(L=a.Cartesian3.normalize(a.Cartesian3.cross(G,H,L),L)));o.normal&&(e.wall?(I[N+S]=G.x,I[t+S]=G.y,I[r+S]=G.z):P&&(I[N+S]=-G.x,I[t+S]=-G.y,I[r+S]=-G.z),(f&&!w||m)&&(I[N]=G.x,I[t]=G.y,I[r]=G.z)),s&&(m&&(G=b.geodeticSurfaceNormal(i,G)),M[N+S]=-G.x,M[t+S]=-G.y,M[r+S]=-G.z),o.tangent&&(e.wall?(A[N+S]=H.x,A[t+S]=H.y,A[r+S]=H.z):P&&(A[N+S]=-H.x,A[t+S]=-H.y,A[r+S]=-H.z),f&&(w?(A[N]=$.x,A[t]=$.y,A[r]=$.z):(A[N]=H.x,A[t]=H.y,A[r]=H.z))),o.bitangent&&(P&&(O[N+S]=L.x,O[t+S]=L.y,O[r+S]=L.z),f&&(w?(O[N]=tt.x,O[t]=tt.y,O[r]=tt.z):(O[N]=L.x,O[t]=L.y,O[r]=L.z))),N+=3}}o.st&&!t.defined(d)&&(n.attributes.st=new c.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:E})),o.normal&&(n.attributes.normal=new c.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:I})),o.tangent&&(n.attributes.tangent=new c.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:A})),o.bitangent&&(n.attributes.bitangent=new c.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:O})),s&&(n.attributes.extrudeDirection=new c.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:M}))}if(e.extrude&&t.defined(e.offsetAttribute)){const t=u.length/3;let o=new Uint8Array(t);if(e.offsetAttribute===h.GeometryOffsetAttribute.TOP)f&&P||m?o=o.fill(1,0,t/2):f&&(o=o.fill(1));else{const t=e.offsetAttribute===h.GeometryOffsetAttribute.NONE?0:1;o=o.fill(t)}n.attributes.applyOffset=new c.GeometryAttribute({componentDatatype:l.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:o})}return n}const lt=[];function ut(e,o,n,i,r,a,s,l,c,h){const g={walls:[]};let p;if(s||l){const r=f.PolygonGeometryLibrary.createGeometryFromPositions(e,o,n,i,a,c,h),u=r.attributes.position.values,y=r.indices;let P,C;if(s&&l){const e=u.concat(u);P=e.length/3,C=m.IndexDatatype.createTypedArray(P,2*y.length),C.set(y);const o=y.length,i=P/2;for(p=0;p<o;p+=3){const t=C[p]+i,e=C[p+1]+i,n=C[p+2]+i;C[p+o]=n,C[p+1+o]=e,C[p+2+o]=t}if(r.attributes.position.values=e,a&&c.normal){const t=r.attributes.normal.values;r.attributes.normal.values=new Float32Array(e.length),r.attributes.normal.values.set(t)}if(c.st&&t.defined(n)){const t=r.attributes.st.values;r.attributes.st.values=new Float32Array(2*P),r.attributes.st.values=t.concat(t)}r.indices=C}else if(l){for(P=u.length/3,C=m.IndexDatatype.createTypedArray(P,y.length),p=0;p<y.length;p+=3)C[p]=y[p+2],C[p+1]=y[p+1],C[p+2]=y[p];r.indices=C}g.topAndBottom=new d.GeometryInstance({geometry:r})}let y=r.outerRing;const C=u.EllipsoidTangentPlane.fromPoints(y,e);let _=C.projectPointsOntoPlane(y,lt),b=P.PolygonPipeline.computeWindingOrder2D(_);b===P.WindingOrder.CLOCKWISE&&(y=y.slice().reverse());let T=f.PolygonGeometryLibrary.computeWallGeometry(y,n,e,i,a,h);g.walls.push(new d.GeometryInstance({geometry:T}));const w=r.holes;for(p=0;p<w.length;p++){let t=w[p];_=C.projectPointsOntoPlane(t,lt),b=P.PolygonPipeline.computeWindingOrder2D(_),b===P.WindingOrder.COUNTER_CLOCKWISE&&(t=t.slice().reverse()),T=f.PolygonGeometryLibrary.computeWallGeometry(t,n,e,i,a,h),g.walls.push(new d.GeometryInstance({geometry:T}))}return g}function ct(n){const i=n.polygonHierarchy,s=t.defaultValue(n.vertexFormat,b.VertexFormat.DEFAULT),l=t.defaultValue(n.ellipsoid,e.Ellipsoid.WGS84),u=t.defaultValue(n.granularity,p.CesiumMath.RADIANS_PER_DEGREE),c=t.defaultValue(n.stRotation,0),d=n.textureCoordinates,h=t.defaultValue(n.perPositionHeight,!1),g=h&&t.defined(n.extrudedHeight);let m=t.defaultValue(n.height,0),y=t.defaultValue(n.extrudedHeight,m);if(!g){const t=Math.max(m,y);y=Math.min(m,y),m=t}this._vertexFormat=b.VertexFormat.clone(s),this._ellipsoid=e.Ellipsoid.clone(l),this._granularity=u,this._stRotation=c,this._height=m,this._extrudedHeight=y,this._closeTop=t.defaultValue(n.closeTop,!0),this._closeBottom=t.defaultValue(n.closeBottom,!0),this._polygonHierarchy=i,this._perPositionHeight=h,this._perPositionHeightExtrude=g,this._shadowVolume=t.defaultValue(n.shadowVolume,!1),this._workerName="createPolygonGeometry",this._offsetAttribute=n.offsetAttribute,this._arcType=t.defaultValue(n.arcType,o.ArcType.GEODESIC),this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0,this._textureCoordinates=d,this.packedLength=f.PolygonGeometryLibrary.computeHierarchyPackedLength(i,a.Cartesian3)+e.Ellipsoid.packedLength+b.VertexFormat.packedLength+(d?f.PolygonGeometryLibrary.computeHierarchyPackedLength(d,r.Cartesian2):1)+12}ct.fromPositions=function(e){return new ct({polygonHierarchy:{positions:(e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT)).positions},height:e.height,extrudedHeight:e.extrudedHeight,vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid,granularity:e.granularity,perPositionHeight:e.perPositionHeight,closeTop:e.closeTop,closeBottom:e.closeBottom,offsetAttribute:e.offsetAttribute,arcType:e.arcType,textureCoordinates:e.textureCoordinates})},ct.pack=function(o,n,i){return i=t.defaultValue(i,0),i=f.PolygonGeometryLibrary.packPolygonHierarchy(o._polygonHierarchy,n,i,a.Cartesian3),e.Ellipsoid.pack(o._ellipsoid,n,i),i+=e.Ellipsoid.packedLength,b.VertexFormat.pack(o._vertexFormat,n,i),i+=b.VertexFormat.packedLength,n[i++]=o._height,n[i++]=o._extrudedHeight,n[i++]=o._granularity,n[i++]=o._stRotation,n[i++]=o._perPositionHeightExtrude?1:0,n[i++]=o._perPositionHeight?1:0,n[i++]=o._closeTop?1:0,n[i++]=o._closeBottom?1:0,n[i++]=o._shadowVolume?1:0,n[i++]=t.defaultValue(o._offsetAttribute,-1),n[i++]=o._arcType,t.defined(o._textureCoordinates)?i=f.PolygonGeometryLibrary.packPolygonHierarchy(o._textureCoordinates,n,i,r.Cartesian2):n[i++]=-1,n[i++]=o.packedLength,n};const dt=e.Ellipsoid.clone(e.Ellipsoid.UNIT_SPHERE),ht=new b.VertexFormat,gt={polygonHierarchy:{}};ct.unpack=function(o,n,i){n=t.defaultValue(n,0);const s=f.PolygonGeometryLibrary.unpackPolygonHierarchy(o,n,a.Cartesian3);n=s.startingIndex,delete s.startingIndex;const l=e.Ellipsoid.unpack(o,n,dt);n+=e.Ellipsoid.packedLength;const u=b.VertexFormat.unpack(o,n,ht);n+=b.VertexFormat.packedLength;const c=o[n++],d=o[n++],h=o[n++],g=o[n++],m=1===o[n++],p=1===o[n++],y=1===o[n++],P=1===o[n++],C=1===o[n++],_=o[n++],T=o[n++],w=-1===o[n]?void 0:f.PolygonGeometryLibrary.unpackPolygonHierarchy(o,n,r.Cartesian2);t.defined(w)?(n=w.startingIndex,delete w.startingIndex):n++;const x=o[n++];return t.defined(i)||(i=new ct(gt)),i._polygonHierarchy=s,i._ellipsoid=e.Ellipsoid.clone(l,i._ellipsoid),i._vertexFormat=b.VertexFormat.clone(u,i._vertexFormat),i._height=c,i._extrudedHeight=d,i._granularity=h,i._stRotation=g,i._perPositionHeightExtrude=m,i._perPositionHeight=p,i._closeTop=y,i._closeBottom=P,i._shadowVolume=C,i._offsetAttribute=-1===_?void 0:_,i._arcType=T,i._textureCoordinates=w,i.packedLength=x,i};const mt=new r.Cartesian2,pt=new r.Cartesian2,yt=new V;function ft(t,e,n,i,a,s){const l=t.longitude,u=l>=0?l:l+p.CesiumMath.TWO_PI;a.westOverIdl=Math.min(a.westOverIdl,u),a.eastOverIdl=Math.max(a.eastOverIdl,u),s.west=Math.min(s.west,l),s.east=Math.max(s.east,l);const c=t.getLatitude(n);let d=c;if(s.south=Math.min(s.south,c),s.north=Math.max(s.north,c),i!==o.ArcType.RHUMB){const o=r.Cartesian2.subtract(e.position,t.position,mt),i=r.Cartesian2.dot(e.position,o)/r.Cartesian2.dot(o,o);if(i>0&&i<1){const t=r.Cartesian2.add(e.position,r.Cartesian2.multiplyByScalar(o,-i,o),pt),a=V.clone(e,yt);a.position=t;const l=a.getLatitude(n);s.south=Math.min(s.south,l),s.north=Math.max(s.north,l),Math.abs(c)>Math.abs(l)&&(d=l)}}const h=e.x*t.y-t.x*e.y;let g=Math.sign(h);0!==g&&(g*=r.Cartesian2.angleBetween(e.position,t.position)),d>=0&&(a.northAngle+=g),d<=0&&(a.southAngle+=g)}const Pt=new V,Ct=new V,_t={northAngle:0,southAngle:0,westOverIdl:0,eastOverIdl:0};ct.computeRectangleFromPositions=function(e,o,n,i){if(t.defined(i)||(i=new C.Rectangle),e.length<3)return i;i.west=Number.POSITIVE_INFINITY,i.east=Number.NEGATIVE_INFINITY,i.south=Number.POSITIVE_INFINITY,i.north=Number.NEGATIVE_INFINITY,_t.northAngle=0,_t.southAngle=0,_t.westOverIdl=Number.POSITIVE_INFINITY,_t.eastOverIdl=Number.NEGATIVE_INFINITY;const r=e.length;let a=V.fromCartesian(e[0],Ct);for(let t=1;t<r;t++){const r=V.fromCartesian(e[t],Pt);ft(r,a,o,n,_t,i),a=V.clone(r,a)}return ft(V.fromCartesian(e[0],Pt),a,o,n,_t,i),i.east-i.west>_t.eastOverIdl-_t.westOverIdl&&(i.west=_t.westOverIdl,i.east=_t.eastOverIdl,i.east>p.CesiumMath.PI&&(i.east=i.east-p.CesiumMath.TWO_PI),i.west>p.CesiumMath.PI&&(i.west=i.west-p.CesiumMath.TWO_PI)),p.CesiumMath.equalsEpsilon(Math.abs(_t.northAngle),p.CesiumMath.TWO_PI,p.CesiumMath.EPSILON10)&&(i.north=p.CesiumMath.PI_OVER_TWO,i.east=p.CesiumMath.PI,i.west=-p.CesiumMath.PI),p.CesiumMath.equalsEpsilon(Math.abs(_t.southAngle),p.CesiumMath.TWO_PI,p.CesiumMath.EPSILON10)&&(i.south=-p.CesiumMath.PI_OVER_TWO,i.east=p.CesiumMath.PI,i.west=-p.CesiumMath.PI),i};const bt=new V;function Tt(t,e,o){if(t.height>=p.CesiumMath.PI||t.width>=p.CesiumMath.PI){return V.fromCartesian(e[0],bt).tangentPlane}return u.EllipsoidTangentPlane.fromPoints(e,o)}const wt=new s.Cartographic;function xt(e,o,n){if(e.height>=p.CesiumMath.PI||e.width>=p.CesiumMath.PI)return(o,i)=>{if(e.south<0&&e.north>0){const e=n.cartesianToCartographic(o,wt);return t.defined(i)||(i=new r.Cartesian2),i.x=e.longitude/p.CesiumMath.PI,i.y=e.latitude/p.CesiumMath.PI_OVER_TWO,i}return V.fromCartesian(o,i)};const i=u.EllipsoidTangentPlane.fromPoints(o,n);return(t,e)=>i.projectPointsOntoPlane(t,e)}return ct.createGeometry=function(e){const o=e._vertexFormat,a=e._ellipsoid,s=e._granularity,y=e._stRotation,C=e._polygonHierarchy,_=e._perPositionHeight,b=e._closeTop,T=e._closeBottom,w=e._arcType,x=e._textureCoordinates,E=t.defined(x),I=C.positions;if(I.length<3)return;const A=e.rectangle,O=f.PolygonGeometryLibrary.polygonsFromHierarchy(C,E,function(e,o,n){return(i,a)=>{if(e.height>=p.CesiumMath.PI||e.width>=p.CesiumMath.PI){if(e.south<0&&e.north>0){t.defined(a)||(a=[]);for(let t=0;t<i.length;++t){const e=n.cartesianToCartographic(i[t],wt);a[t]=new r.Cartesian2(e.longitude/p.CesiumMath.PI,e.latitude/p.CesiumMath.PI_OVER_TWO)}return a.length=i.length,a}return V.fromCartesianArray(i,a)}return u.EllipsoidTangentPlane.fromPoints(o,n).projectPointsOntoPlane(i,a)}}(A,I,a),!_,a,function(t,e,o,n){return(i,r)=>!n&&(t.height>=p.CesiumMath.PI_OVER_TWO||t.width>=2*p.CesiumMath.PI_OVER_THREE)?f.PolygonGeometryLibrary.splitPolygonsOnEquator(i,e,o,r):i}(A,a,w,_)),M=O.hierarchy,v=O.polygons,N=E?f.PolygonGeometryLibrary.polygonsFromHierarchy(x,!0,(function(t){return t}),!1,a).polygons:void 0;if(0===M.length)return;const G=M[0].outerRing,H=function(t,e,o,i){if(e.height>=p.CesiumMath.PI||e.width>=p.CesiumMath.PI)return n.BoundingRectangle.fromRectangle(e,void 0,j);const r=t,a=u.EllipsoidTangentPlane.fromPoints(r,o);return f.PolygonGeometryLibrary.computeBoundingRectangle(a.plane.normal,a.projectPointOntoPlane.bind(a),r,i,j)}(G,A,a,y),L=[],R=e._height,F=e._extrudedHeight,S=e._perPositionHeightExtrude||!p.CesiumMath.equalsEpsilon(R,F,0,p.CesiumMath.EPSILON2),D={perPositionHeight:_,vertexFormat:o,geometry:void 0,rotationAxis:Tt(A,G,a).plane.normal,projectTo2d:xt(A,G,a),boundingRectangle:H,ellipsoid:a,stRotation:y,textureCoordinates:void 0,bottom:!1,top:!0,wall:!1,extrude:!1,arcType:w};let B;if(S)for(D.extrude=!0,D.top=b,D.bottom=T,D.shadowVolume=e._shadowVolume,D.offsetAttribute=e._offsetAttribute,B=0;B<v.length;B++){const t=ut(a,v[B],E?N[B]:void 0,s,M[B],_,b,T,o,w);let e;b&&T?(e=t.topAndBottom,D.geometry=f.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(e.geometry,R,F,a,_)):b?(e=t.topAndBottom,e.geometry.attributes.position.values=P.PolygonPipeline.scaleToGeodeticHeight(e.geometry.attributes.position.values,R,a,!_),D.geometry=e.geometry):T&&(e=t.topAndBottom,e.geometry.attributes.position.values=P.PolygonPipeline.scaleToGeodeticHeight(e.geometry.attributes.position.values,F,a,!0),D.geometry=e.geometry),(b||T)&&(D.wall=!1,e.geometry=st(D),L.push(e));const n=t.walls;D.wall=!0;for(let t=0;t<n.length;t++){const e=n[t];D.geometry=f.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(e.geometry,R,F,a,_),e.geometry=st(D),L.push(e)}}else for(B=0;B<v.length;B++){const n=new d.GeometryInstance({geometry:f.PolygonGeometryLibrary.createGeometryFromPositions(a,v[B],E?N[B]:void 0,s,_,o,w)});if(n.geometry.attributes.position.values=P.PolygonPipeline.scaleToGeodeticHeight(n.geometry.attributes.position.values,R,a,!_),D.geometry=n.geometry,n.geometry=st(D),t.defined(e._offsetAttribute)){const t=n.geometry.attributes.position.values.length,o=e._offsetAttribute===h.GeometryOffsetAttribute.NONE?0:1,i=new Uint8Array(t/3).fill(o);n.geometry.attributes.applyOffset=new c.GeometryAttribute({componentDatatype:l.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}L.push(n)}const z=g.GeometryPipeline.combineInstances(L)[0];z.attributes.position.values=new Float64Array(z.attributes.position.values),z.indices=m.IndexDatatype.createTypedArray(z.attributes.position.values.length/3,z.indices);const k=z.attributes,W=i.BoundingSphere.fromVertices(k.position.values);return o.position||delete k.position,new c.Geometry({attributes:k,indices:z.indices,primitiveType:z.primitiveType,boundingSphere:W,offsetAttribute:e._offsetAttribute})},ct.createShadowVolume=function(t,e,o){const n=t._granularity,i=t._ellipsoid,r=e(n,i),a=o(n,i);return new ct({polygonHierarchy:t._polygonHierarchy,ellipsoid:i,stRotation:t._stRotation,granularity:n,perPositionHeight:!1,extrudedHeight:r,height:a,vertexFormat:b.VertexFormat.POSITION_ONLY,shadowVolume:!0,arcType:t._arcType})},Object.defineProperties(ct.prototype,{rectangle:{get:function(){if(!t.defined(this._rectangle)){const t=this._polygonHierarchy.positions;this._rectangle=ct.computeRectangleFromPositions(t,this._ellipsoid,this._arcType)}return this._rectangle}},textureCoordinateRotationPoints:{get:function(){return t.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(t){const e=-t._stRotation;if(0===e)return[0,0,0,1,1,0];const o=t._ellipsoid,n=t._polygonHierarchy.positions,i=t.rectangle;return c.Geometry._textureCoordinateRotationPoints(n,e,o,i)}(this)),this._textureCoordinateRotationPoints}}}),function(o,n){return t.defined(n)&&(o=ct.unpack(o,n)),o._ellipsoid=e.Ellipsoid.clone(o._ellipsoid),ct.createGeometry(o)}}));
