define(["./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./arrayRemoveDuplicates-0d8dde26","./BoundingRectangle-a5a1c0f3","./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./PolylineVolumeGeometryLibrary-e8146b77","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./IndexDatatype-58eb7805","./Math-355606c6","./PolygonPipeline-39b84ada","./Cartographic-dbefb6fa","./Rectangle-98b0bef0","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./combine-0c102d93","./RequestType-735c98f2","./RuntimeError-9b4ce3fb","./WebGLConstants-7f557f93","./EllipsoidTangentPlane-a6ea67fb","./AxisAlignedBoundingBox-d98e5354","./IntersectionTests-01432fe7","./Plane-06f34fae","./PolylinePipeline-07b67faf","./EllipsoidGeodesic-8ac7b85d","./EllipsoidRhumbLine-6774fec3","./Matrix2-e4a4609a"],(function(e,t,i,n,o,r,a,l,s,p,c,u,d,y,g,h,m,f,E,P,_,k,C,L,T,D,G,v,b,A,R){"use strict";function V(i){const n=(i=e.defaultValue(i,e.defaultValue.EMPTY_OBJECT)).polylinePositions,o=i.shapePositions;this._positions=n,this._shape=o,this._ellipsoid=t.Ellipsoid.clone(e.defaultValue(i.ellipsoid,t.Ellipsoid.WGS84)),this._cornerType=e.defaultValue(i.cornerType,s.CornerType.ROUNDED),this._granularity=e.defaultValue(i.granularity,d.CesiumMath.RADIANS_PER_DEGREE),this._workerName="createPolylineVolumeOutlineGeometry";let l=1+n.length*a.Cartesian3.packedLength;l+=1+o.length*r.Cartesian2.packedLength,this.packedLength=l+t.Ellipsoid.packedLength+2}V.pack=function(i,n,o){let l;o=e.defaultValue(o,0);const s=i._positions;let p=s.length;for(n[o++]=p,l=0;l<p;++l,o+=a.Cartesian3.packedLength)a.Cartesian3.pack(s[l],n,o);const c=i._shape;for(p=c.length,n[o++]=p,l=0;l<p;++l,o+=r.Cartesian2.packedLength)r.Cartesian2.pack(c[l],n,o);return t.Ellipsoid.pack(i._ellipsoid,n,o),o+=t.Ellipsoid.packedLength,n[o++]=i._cornerType,n[o]=i._granularity,n};const B=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),I={polylinePositions:void 0,shapePositions:void 0,ellipsoid:B,height:void 0,cornerType:void 0,granularity:void 0};V.unpack=function(i,n,o){let l;n=e.defaultValue(n,0);let s=i[n++];const p=new Array(s);for(l=0;l<s;++l,n+=a.Cartesian3.packedLength)p[l]=a.Cartesian3.unpack(i,n);s=i[n++];const c=new Array(s);for(l=0;l<s;++l,n+=r.Cartesian2.packedLength)c[l]=r.Cartesian2.unpack(i,n);const u=t.Ellipsoid.unpack(i,n,B);n+=t.Ellipsoid.packedLength;const d=i[n++],y=i[n];return e.defined(o)?(o._positions=p,o._shape=c,o._ellipsoid=t.Ellipsoid.clone(u,o._ellipsoid),o._cornerType=d,o._granularity=y,o):(I.polylinePositions=p,I.shapePositions=c,I.cornerType=d,I.granularity=y,new V(I))};const w=new n.BoundingRectangle;return V.createGeometry=function(e){const t=e._positions,r=i.arrayRemoveDuplicates(t,a.Cartesian3.equalsEpsilon);let d=e._shape;if(d=s.PolylineVolumeGeometryLibrary.removeDuplicatesFromShape(d),r.length<2||d.length<3)return;y.PolygonPipeline.computeWindingOrder2D(d)===y.WindingOrder.CLOCKWISE&&d.reverse();const g=n.BoundingRectangle.fromPoints(d,w);return function(e,t){const i=new c.GeometryAttributes;i.position=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e});const n=t.length,r=i.position.values.length/3,a=e.length/3/n,s=u.IndexDatatype.createTypedArray(r,2*n*(a+1));let d,y,g=0;d=0;let h=d*n;for(y=0;y<n-1;y++)s[g++]=y+h,s[g++]=y+h+1;for(s[g++]=n-1+h,s[g++]=h,d=a-1,h=d*n,y=0;y<n-1;y++)s[g++]=y+h,s[g++]=y+h+1;for(s[g++]=n-1+h,s[g++]=h,d=0;d<a-1;d++){const e=n*d,t=e+n;for(y=0;y<n;y++)s[g++]=y+e,s[g++]=y+t}return new p.Geometry({attributes:i,indices:u.IndexDatatype.createTypedArray(r,s),boundingSphere:o.BoundingSphere.fromVertices(e),primitiveType:p.PrimitiveType.LINES})}(s.PolylineVolumeGeometryLibrary.computePositions(r,d,g,e,!1),d)},function(i,n){return e.defined(n)&&(i=V.unpack(i,n)),i._ellipsoid=t.Ellipsoid.clone(i._ellipsoid),V.createGeometry(i)}}));
