define(["exports","./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-58eb7805","./Math-355606c6","./VertexFormat-fbdec922"],(function(t,e,a,n,i,r,o,s,m,u,l,c,f){"use strict";const d=new n.Cartesian3,C=new n.Cartesian3,p=new n.Cartesian3,y=new n.Cartesian3,_=new n.Cartesian3,h=new n.Cartesian3(1,1,1),x=Math.cos,A=Math.sin;function k(t){t=r.defaultValue(t,r.defaultValue.EMPTY_OBJECT);const e=r.defaultValue(t.radii,h),a=r.defaultValue(t.innerRadii,e),i=r.defaultValue(t.minimumClock,0),o=r.defaultValue(t.maximumClock,c.CesiumMath.TWO_PI),s=r.defaultValue(t.minimumCone,0),m=r.defaultValue(t.maximumCone,c.CesiumMath.PI),u=Math.round(r.defaultValue(t.stackPartitions,64)),l=Math.round(r.defaultValue(t.slicePartitions,64)),d=r.defaultValue(t.vertexFormat,f.VertexFormat.DEFAULT);this._radii=n.Cartesian3.clone(e),this._innerRadii=n.Cartesian3.clone(a),this._minimumClock=i,this._maximumClock=o,this._minimumCone=s,this._maximumCone=m,this._stackPartitions=u,this._slicePartitions=l,this._vertexFormat=f.VertexFormat.clone(d),this._offsetAttribute=t.offsetAttribute,this._workerName="createEllipsoidGeometry"}k.packedLength=2*n.Cartesian3.packedLength+f.VertexFormat.packedLength+7,k.pack=function(t,e,a){return a=r.defaultValue(a,0),n.Cartesian3.pack(t._radii,e,a),a+=n.Cartesian3.packedLength,n.Cartesian3.pack(t._innerRadii,e,a),a+=n.Cartesian3.packedLength,f.VertexFormat.pack(t._vertexFormat,e,a),a+=f.VertexFormat.packedLength,e[a++]=t._minimumClock,e[a++]=t._maximumClock,e[a++]=t._minimumCone,e[a++]=t._maximumCone,e[a++]=t._stackPartitions,e[a++]=t._slicePartitions,e[a]=r.defaultValue(t._offsetAttribute,-1),e};const b=new n.Cartesian3,w=new n.Cartesian3,P=new f.VertexFormat,g={radii:b,innerRadii:w,vertexFormat:P,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,offsetAttribute:void 0};let v;k.unpack=function(t,e,a){e=r.defaultValue(e,0);const i=n.Cartesian3.unpack(t,e,b);e+=n.Cartesian3.packedLength;const o=n.Cartesian3.unpack(t,e,w);e+=n.Cartesian3.packedLength;const s=f.VertexFormat.unpack(t,e,P);e+=f.VertexFormat.packedLength;const m=t[e++],u=t[e++],l=t[e++],c=t[e++],d=t[e++],C=t[e++],p=t[e];return r.defined(a)?(a._radii=n.Cartesian3.clone(i,a._radii),a._innerRadii=n.Cartesian3.clone(o,a._innerRadii),a._vertexFormat=f.VertexFormat.clone(s,a._vertexFormat),a._minimumClock=m,a._maximumClock=u,a._minimumCone=l,a._maximumCone=c,a._stackPartitions=d,a._slicePartitions=C,a._offsetAttribute=-1===p?void 0:p,a):(g.minimumClock=m,g.maximumClock=u,g.minimumCone=l,g.maximumCone=c,g.stackPartitions=d,g.slicePartitions=C,g.offsetAttribute=-1===p?void 0:p,new k(g))},k.createGeometry=function(t){const f=t._radii;if(f.x<=0||f.y<=0||f.z<=0)return;const h=t._innerRadii;if(h.x<=0||h.y<=0||h.z<=0)return;const k=t._minimumClock,b=t._maximumClock,w=t._minimumCone,P=t._maximumCone,g=t._vertexFormat;let v,F,V=t._slicePartitions+1,M=t._stackPartitions+1;V=Math.round(V*Math.abs(b-k)/c.CesiumMath.TWO_PI),M=Math.round(M*Math.abs(P-w)/c.CesiumMath.PI),V<2&&(V=2),M<2&&(M=2);let T=0;const D=[w],G=[k];for(v=0;v<M;v++)D.push(w+v*(P-w)/(M-1));for(D.push(P),F=0;F<V;F++)G.push(k+F*(b-k)/(V-1));G.push(b);const L=D.length,O=G.length;let I=0,E=1;const z=h.x!==f.x||h.y!==f.y||h.z!==f.z;let N=!1,R=!1,U=!1;z&&(E=2,w>0&&(N=!0,I+=V-1),P<Math.PI&&(R=!0,I+=V-1),(b-k)%c.CesiumMath.TWO_PI?(U=!0,I+=2*(M-1)+1):I+=1);const S=O*L*E,B=new Float64Array(3*S),W=new Array(S).fill(!1),Y=new Array(S).fill(!1),q=V*M*E,J=6*(q+I+1-(V+M)*E),X=l.IndexDatatype.createTypedArray(q,J),Z=g.normal?new Float32Array(3*S):void 0,j=g.tangent?new Float32Array(3*S):void 0,H=g.bitangent?new Float32Array(3*S):void 0,K=g.st?new Float32Array(2*S):void 0,Q=new Array(L),$=new Array(L);for(v=0;v<L;v++)Q[v]=A(D[v]),$[v]=x(D[v]);const tt=new Array(O),et=new Array(O);for(F=0;F<O;F++)et[F]=x(G[F]),tt[F]=A(G[F]);for(v=0;v<L;v++)for(F=0;F<O;F++)B[T++]=f.x*Q[v]*et[F],B[T++]=f.y*Q[v]*tt[F],B[T++]=f.z*$[v];let at,nt,it,rt,ot=S/2;if(z)for(v=0;v<L;v++)for(F=0;F<O;F++)B[T++]=h.x*Q[v]*et[F],B[T++]=h.y*Q[v]*tt[F],B[T++]=h.z*$[v],W[ot]=!0,v>0&&v!==L-1&&0!==F&&F!==O-1&&(Y[ot]=!0),ot++;for(T=0,v=1;v<L-2;v++)for(at=v*O,nt=(v+1)*O,F=1;F<O-2;F++)X[T++]=nt+F,X[T++]=nt+F+1,X[T++]=at+F+1,X[T++]=nt+F,X[T++]=at+F+1,X[T++]=at+F;if(z){const t=L*O;for(v=1;v<L-2;v++)for(at=t+v*O,nt=t+(v+1)*O,F=1;F<O-2;F++)X[T++]=nt+F,X[T++]=at+F,X[T++]=at+F+1,X[T++]=nt+F,X[T++]=at+F+1,X[T++]=nt+F+1}if(z){if(N)for(rt=L*O,v=1;v<O-2;v++)X[T++]=v,X[T++]=v+1,X[T++]=rt+v+1,X[T++]=v,X[T++]=rt+v+1,X[T++]=rt+v;if(R)for(it=L*O-O,rt=L*O*E-O,v=1;v<O-2;v++)X[T++]=it+v+1,X[T++]=it+v,X[T++]=rt+v,X[T++]=it+v+1,X[T++]=rt+v,X[T++]=rt+v+1}if(U){for(v=1;v<L-2;v++)rt=O*L+O*v,it=O*v,X[T++]=rt,X[T++]=it+O,X[T++]=it,X[T++]=rt,X[T++]=rt+O,X[T++]=it+O;for(v=1;v<L-2;v++)rt=O*L+O*(v+1)-1,it=O*(v+1)-1,X[T++]=it+O,X[T++]=rt,X[T++]=it,X[T++]=it+O,X[T++]=rt+O,X[T++]=rt}const st=new m.GeometryAttributes;g.position&&(st.position=new s.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:B}));let mt=0,ut=0,lt=0,ct=0;const ft=S/2;let dt;const Ct=o.Ellipsoid.fromCartesian3(f),pt=o.Ellipsoid.fromCartesian3(h);if(g.st||g.normal||g.tangent||g.bitangent){for(v=0;v<S;v++){dt=W[v]?pt:Ct;const t=n.Cartesian3.fromArray(B,3*v,d),e=dt.geodeticSurfaceNormal(t,C);if(Y[v]&&n.Cartesian3.negate(e,e),g.st){const t=a.Cartesian2.negate(e,_);K[mt++]=Math.atan2(t.y,t.x)/c.CesiumMath.TWO_PI+.5,K[mt++]=Math.asin(e.z)/Math.PI+.5}if(g.normal&&(Z[ut++]=e.x,Z[ut++]=e.y,Z[ut++]=e.z),g.tangent||g.bitangent){const t=p;let a,i=0;if(W[v]&&(i=ft),a=!N&&v>=i&&v<i+2*O?n.Cartesian3.UNIT_X:n.Cartesian3.UNIT_Z,n.Cartesian3.cross(a,e,t),n.Cartesian3.normalize(t,t),g.tangent&&(j[lt++]=t.x,j[lt++]=t.y,j[lt++]=t.z),g.bitangent){const a=n.Cartesian3.cross(e,t,y);n.Cartesian3.normalize(a,a),H[ct++]=a.x,H[ct++]=a.y,H[ct++]=a.z}}}g.st&&(st.st=new s.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:K})),g.normal&&(st.normal=new s.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:Z})),g.tangent&&(st.tangent=new s.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:j})),g.bitangent&&(st.bitangent=new s.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:H}))}if(r.defined(t._offsetAttribute)){const e=B.length,a=t._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,n=new Uint8Array(e/3).fill(a);st.applyOffset=new s.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:n})}return new s.Geometry({attributes:st,indices:X,primitiveType:s.PrimitiveType.TRIANGLES,boundingSphere:e.BoundingSphere.fromEllipsoid(Ct),offsetAttribute:t._offsetAttribute})},k.getUnitEllipsoid=function(){return r.defined(v)||(v=k.createGeometry(new k({radii:new n.Cartesian3(1,1,1),vertexFormat:f.VertexFormat.POSITION_ONLY}))),v},t.EllipsoidGeometry=k}));
