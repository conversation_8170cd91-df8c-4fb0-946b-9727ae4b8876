define(["./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./CylinderGeometryLibrary-4d7f606d","./defaultValue-f6d5e6da","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-58eb7805","./Cartographic-dbefb6fa","./Math-355606c6","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./RuntimeError-9b4ce3fb","./WebGLConstants-7f557f93","./Matrix2-e4a4609a"],(function(t,e,i,n,r,o,a,s,u,f,l,d,c,m,b,p,y,_,h,A,R,G){"use strict";const O=new e.Cartesian2;function V(t){const e=(t=o.defaultValue(t,o.defaultValue.EMPTY_OBJECT)).length,i=t.topRadius,n=t.bottomRadius,r=o.defaultValue(t.slices,128),a=Math.max(o.defaultValue(t.numberOfVerticalLines,16),0);this._length=e,this._topRadius=i,this._bottomRadius=n,this._slices=r,this._numberOfVerticalLines=a,this._offsetAttribute=t.offsetAttribute,this._workerName="createCylinderOutlineGeometry"}V.packedLength=6,V.pack=function(t,e,i){return i=o.defaultValue(i,0),e[i++]=t._length,e[i++]=t._topRadius,e[i++]=t._bottomRadius,e[i++]=t._slices,e[i++]=t._numberOfVerticalLines,e[i]=o.defaultValue(t._offsetAttribute,-1),e};const g={length:void 0,topRadius:void 0,bottomRadius:void 0,slices:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};return V.unpack=function(t,e,i){e=o.defaultValue(e,0);const n=t[e++],r=t[e++],a=t[e++],s=t[e++],u=t[e++],f=t[e];return o.defined(i)?(i._length=n,i._topRadius=r,i._bottomRadius=a,i._slices=s,i._numberOfVerticalLines=u,i._offsetAttribute=-1===f?void 0:f,i):(g.length=n,g.topRadius=r,g.bottomRadius=a,g.slices=s,g.numberOfVerticalLines=u,g.offsetAttribute=-1===f?void 0:f,new V(g))},V.createGeometry=function(l){let d=l._length;const c=l._topRadius,m=l._bottomRadius,b=l._slices,p=l._numberOfVerticalLines;if(d<=0||c<0||m<0||0===c&&0===m)return;const y=2*b,_=r.CylinderGeometryLibrary.computePositions(d,c,m,b,!1);let h,A=2*b;if(p>0){const t=Math.min(p,b);h=Math.round(b/t),A+=t}const R=f.IndexDatatype.createTypedArray(y,2*A);let G,V=0;for(G=0;G<b-1;G++)R[V++]=G,R[V++]=G+1,R[V++]=G+b,R[V++]=G+1+b;if(R[V++]=b-1,R[V++]=0,R[V++]=b+b-1,R[V++]=b,p>0)for(G=0;G<b;G+=h)R[V++]=G,R[V++]=G+b;const g=new s.GeometryAttributes;g.position=new a.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:_}),O.x=.5*d,O.y=Math.max(m,c);const v=new t.BoundingSphere(i.Cartesian3.ZERO,e.Cartesian2.magnitude(O));if(o.defined(l._offsetAttribute)){d=_.length;const t=l._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,e=new Uint8Array(d/3).fill(t);g.applyOffset=new a.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:e})}return new a.Geometry({attributes:g,indices:R,primitiveType:a.PrimitiveType.LINES,boundingSphere:v,offsetAttribute:l._offsetAttribute})},function(t,e){return o.defined(e)&&(t=V.unpack(t,e)),V.createGeometry(t)}}));
