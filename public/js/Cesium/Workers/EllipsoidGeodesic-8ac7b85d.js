define(["exports","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./Math-355606c6"],(function(t,a,i,n,e,s){"use strict";function o(t,a,i,n,e,s,o){const r=function(t,a){return t*a*(4+t*(4-3*a))/16}(t,i);return(1-r)*t*a*(n+r*e*(o+r*s*(2*o*o-1)))}const r=new a.Cartesian3,h=new a.Cartesian3;function c(t,n,e,c){a.Car<PERSON>ian3.normalize(c.cartographicToCartesian(n,h),r),a.Cartesian3.normalize(c.cartographicToCartesian(e,h),h),function(t,a,i,n,e,r,h){const c=(a-i)/a,d=r-n,u=Math.atan((1-c)*Math.tan(e)),l=Math.atan((1-c)*Math.tan(h)),M=Math.cos(u),g=Math.sin(u),p=Math.cos(l),_=Math.sin(l),f=M*p,m=M*_,C=g*_,H=g*p;let v,O,S,q,U,A=d,w=s.CesiumMath.TWO_PI,R=Math.cos(A),b=Math.sin(A);do{R=Math.cos(A),b=Math.sin(A);const t=m-H*R;let a;S=Math.sqrt(p*p*b*b+t*t),O=C+f*R,v=Math.atan2(S,O),0===S?(a=0,q=1):(a=f*b/S,q=1-a*a),w=A,U=O-2*C/q,isFinite(U)||(U=0),A=d+o(c,a,q,v,S,O,U)}while(Math.abs(A-w)>s.CesiumMath.EPSILON12);const E=q*(a*a-i*i)/(i*i),y=E*(256+E*(E*(74-47*E)-128))/1024,P=U*U,x=i*(1+E*(4096+E*(E*(320-175*E)-768))/16384)*(v-y*S*(U+y*(O*(2*P-1)-y*U*(4*S*S-3)*(4*P-3)/6)/4)),D=Math.atan2(p*b,m-H*R),T=Math.atan2(M*b,m*R-H);t._distance=x,t._startHeading=D,t._endHeading=T,t._uSquared=E}(t,c.maximumRadius,c.minimumRadius,n.longitude,n.latitude,e.longitude,e.latitude),t._start=i.Cartographic.clone(n,t._start),t._end=i.Cartographic.clone(e,t._end),t._start.height=0,t._end.height=0,function(t){const a=t._uSquared,i=t._ellipsoid.maximumRadius,n=t._ellipsoid.minimumRadius,e=(i-n)/i,s=Math.cos(t._startHeading),o=Math.sin(t._startHeading),r=(1-e)*Math.tan(t._start.latitude),h=1/Math.sqrt(1+r*r),c=h*r,d=Math.atan2(r,s),u=h*o,l=u*u,M=1-l,g=Math.sqrt(M),p=a/4,_=p*p,f=_*p,m=_*_,C=1+p-3*_/4+5*f/4-175*m/64,H=1-p+15*_/8-35*f/8,v=1-3*p+35*_/4,O=1-5*p,S=C*d-H*Math.sin(2*d)*p/2-v*Math.sin(4*d)*_/16-O*Math.sin(6*d)*f/48-5*Math.sin(8*d)*m/512,q=t._constants;q.a=i,q.b=n,q.f=e,q.cosineHeading=s,q.sineHeading=o,q.tanU=r,q.cosineU=h,q.sineU=c,q.sigma=d,q.sineAlpha=u,q.sineSquaredAlpha=l,q.cosineSquaredAlpha=M,q.cosineAlpha=g,q.u2Over4=p,q.u4Over16=_,q.u6Over64=f,q.u8Over256=m,q.a0=C,q.a1=H,q.a2=v,q.a3=O,q.distanceRatio=S}(t)}function d(t,a,s){const o=n.defaultValue(s,e.Ellipsoid.WGS84);this._ellipsoid=o,this._start=new i.Cartographic,this._end=new i.Cartographic,this._constants={},this._startHeading=void 0,this._endHeading=void 0,this._distance=void 0,this._uSquared=void 0,n.defined(t)&&n.defined(a)&&c(this,t,a,o)}Object.defineProperties(d.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},surfaceDistance:{get:function(){return this._distance}},start:{get:function(){return this._start}},end:{get:function(){return this._end}},startHeading:{get:function(){return this._startHeading}},endHeading:{get:function(){return this._endHeading}}}),d.prototype.setEndPoints=function(t,a){c(this,t,a,this._ellipsoid)},d.prototype.interpolateUsingFraction=function(t,a){return this.interpolateUsingSurfaceDistance(this._distance*t,a)},d.prototype.interpolateUsingSurfaceDistance=function(t,a){const e=this._constants,s=e.distanceRatio+t/e.b,r=Math.cos(2*s),h=Math.cos(4*s),c=Math.cos(6*s),d=Math.sin(2*s),u=Math.sin(4*s),l=Math.sin(6*s),M=Math.sin(8*s),g=s*s,p=s*g,_=e.u8Over256,f=e.u2Over4,m=e.u6Over64,C=e.u4Over16;let H=2*p*_*r/3+s*(1-f+7*C/4-15*m/4+579*_/64-(C-15*m/4+187*_/16)*r-(5*m/4-115*_/16)*h-29*_*c/16)+(f/2-C+71*m/32-85*_/16)*d+(5*C/16-5*m/4+383*_/96)*u-g*((m-11*_/2)*d+5*_*u/2)+(29*m/96-29*_/16)*l+539*_*M/1536;const v=Math.asin(Math.sin(H)*e.cosineAlpha),O=Math.atan(e.a/e.b*Math.tan(v));H-=e.sigma;const S=Math.cos(2*e.sigma+H),q=Math.sin(H),U=Math.cos(H),A=e.cosineU*U,w=e.sineU*q,R=Math.atan2(q*e.sineHeading,A-w*e.cosineHeading)-o(e.f,e.sineAlpha,e.cosineSquaredAlpha,H,q,U,S);return n.defined(a)?(a.longitude=this._start.longitude+R,a.latitude=O,a.height=0,a):new i.Cartographic(this._start.longitude+R,O,0)},t.EllipsoidGeodesic=d}));
