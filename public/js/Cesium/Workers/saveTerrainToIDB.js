define(["./defaultValue-f6d5e6da","./RequestType-735c98f2","./createTaskProcessorWorker","./EV_IndexedDBProvider-596d1bfc","./DeveloperError-c85858c1"],(function(e,r,t,s,a){"use strict";let n;function i(e){const t=self.webkitPostMessage||self.postMessage;try{t({workerType:r.RequestType.TERRAIN})}catch(e){t({})}}return t((function(t,a){const o=t.indexedDB;let d=t.taskData;d.workerTaskID=t.workerTaskID,e.defined(n)?e.defined(n.iDB)&&(d.requestType==r.RequestType.TERRAIN?n.saveTerrainData("EVIDB",1,"terrain",d):d.requestType==r.RequestType.IMAGERY&&n.saveTerrainData("EVIDB",1,"imagery",d)):(n=new s.EV_IndexedDBProvider(o),n.createIDB("EVIDB",1,["model","imagery","terrain","3DTile"],i))}))}));
