define(["exports","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./Math-355606c6"],(function(t,e,a,n,i){"use strict";function s(t,e,n,i){this.west=a.defaultValue(t,0),this.south=a.defaultValue(e,0),this.east=a.defaultValue(n,0),this.north=a.defaultValue(i,0)}Object.defineProperties(s.prototype,{width:{get:function(){return s.computeWidth(this)}},height:{get:function(){return s.computeHeight(this)}}}),s.packedLength=4,s.pack=function(t,e,n){return n=a.defaultValue(n,0),e[n++]=t.west,e[n++]=t.south,e[n++]=t.east,e[n]=t.north,e},s.unpack=function(t,e,n){return e=a.defaultValue(e,0),a.defined(n)||(n=new s),n.west=t[e++],n.south=t[e++],n.east=t[e++],n.north=t[e],n},s.computeWidth=function(t){let e=t.east;const a=t.west;return e<a&&(e+=i.CesiumMath.TWO_PI),e-a},s.computeHeight=function(t){return t.north-t.south},s.fromDegrees=function(t,e,n,u,o){return t=i.CesiumMath.toRadians(a.defaultValue(t,0)),e=i.CesiumMath.toRadians(a.defaultValue(e,0)),n=i.CesiumMath.toRadians(a.defaultValue(n,0)),u=i.CesiumMath.toRadians(a.defaultValue(u,0)),a.defined(o)?(o.west=t,o.south=e,o.east=n,o.north=u,o):new s(t,e,n,u)},s.fromRadians=function(t,e,n,i,u){return a.defined(u)?(u.west=a.defaultValue(t,0),u.south=a.defaultValue(e,0),u.east=a.defaultValue(n,0),u.north=a.defaultValue(i,0),u):new s(t,e,n,i)},s.fromCartographicArray=function(t,e){let n=Number.MAX_VALUE,u=-Number.MAX_VALUE,o=Number.MAX_VALUE,h=-Number.MAX_VALUE,r=Number.MAX_VALUE,d=-Number.MAX_VALUE;for(let e=0,a=t.length;e<a;e++){const a=t[e];n=Math.min(n,a.longitude),u=Math.max(u,a.longitude),r=Math.min(r,a.latitude),d=Math.max(d,a.latitude);const s=a.longitude>=0?a.longitude:a.longitude+i.CesiumMath.TWO_PI;o=Math.min(o,s),h=Math.max(h,s)}return u-n>h-o&&(n=o,u=h,u>i.CesiumMath.PI&&(u-=i.CesiumMath.TWO_PI),n>i.CesiumMath.PI&&(n-=i.CesiumMath.TWO_PI)),a.defined(e)?(e.west=n,e.south=r,e.east=u,e.north=d,e):new s(n,r,u,d)},s.fromCartesianArray=function(t,e,u){e=a.defaultValue(e,n.Ellipsoid.WGS84);let o=Number.MAX_VALUE,h=-Number.MAX_VALUE,r=Number.MAX_VALUE,d=-Number.MAX_VALUE,l=Number.MAX_VALUE,c=-Number.MAX_VALUE;for(let a=0,n=t.length;a<n;a++){const n=e.cartesianToCartographic(t[a]);o=Math.min(o,n.longitude),h=Math.max(h,n.longitude),l=Math.min(l,n.latitude),c=Math.max(c,n.latitude);const s=n.longitude>=0?n.longitude:n.longitude+i.CesiumMath.TWO_PI;r=Math.min(r,s),d=Math.max(d,s)}return h-o>d-r&&(o=r,h=d,h>i.CesiumMath.PI&&(h-=i.CesiumMath.TWO_PI),o>i.CesiumMath.PI&&(o-=i.CesiumMath.TWO_PI)),a.defined(u)?(u.west=o,u.south=l,u.east=h,u.north=c,u):new s(o,l,h,c)},s.clone=function(t,e){if(a.defined(t))return a.defined(e)?(e.west=t.west,e.south=t.south,e.east=t.east,e.north=t.north,e):new s(t.west,t.south,t.east,t.north)},s.equalsEpsilon=function(t,e,n){return n=a.defaultValue(n,0),t===e||a.defined(t)&&a.defined(e)&&Math.abs(t.west-e.west)<=n&&Math.abs(t.south-e.south)<=n&&Math.abs(t.east-e.east)<=n&&Math.abs(t.north-e.north)<=n},s.prototype.clone=function(t){return s.clone(this,t)},s.prototype.equals=function(t){return s.equals(this,t)},s.equals=function(t,e){return t===e||a.defined(t)&&a.defined(e)&&t.west===e.west&&t.south===e.south&&t.east===e.east&&t.north===e.north},s.prototype.equalsEpsilon=function(t,e){return s.equalsEpsilon(this,t,e)},s.validate=function(t){},s.southwest=function(t,n){return a.defined(n)?(n.longitude=t.west,n.latitude=t.south,n.height=0,n):new e.Cartographic(t.west,t.south)},s.northwest=function(t,n){return a.defined(n)?(n.longitude=t.west,n.latitude=t.north,n.height=0,n):new e.Cartographic(t.west,t.north)},s.northeast=function(t,n){return a.defined(n)?(n.longitude=t.east,n.latitude=t.north,n.height=0,n):new e.Cartographic(t.east,t.north)},s.southeast=function(t,n){return a.defined(n)?(n.longitude=t.east,n.latitude=t.south,n.height=0,n):new e.Cartographic(t.east,t.south)},s.center=function(t,n){let s=t.east;const u=t.west;s<u&&(s+=i.CesiumMath.TWO_PI);const o=i.CesiumMath.negativePiToPi(.5*(u+s)),h=.5*(t.south+t.north);return a.defined(n)?(n.longitude=o,n.latitude=h,n.height=0,n):new e.Cartographic(o,h)},s.intersection=function(t,e,n){let u=t.east,o=t.west,h=e.east,r=e.west;u<o&&h>0?u+=i.CesiumMath.TWO_PI:h<r&&u>0&&(h+=i.CesiumMath.TWO_PI),u<o&&r<0?r+=i.CesiumMath.TWO_PI:h<r&&o<0&&(o+=i.CesiumMath.TWO_PI);const d=i.CesiumMath.negativePiToPi(Math.max(o,r)),l=i.CesiumMath.negativePiToPi(Math.min(u,h));if((t.west<t.east||e.west<e.east)&&l<=d)return;const c=Math.max(t.south,e.south),m=Math.min(t.north,e.north);return c>=m?void 0:a.defined(n)?(n.west=d,n.south=c,n.east=l,n.north=m,n):new s(d,c,l,m)},s.simpleIntersection=function(t,e,n){const i=Math.max(t.west,e.west),u=Math.max(t.south,e.south),o=Math.min(t.east,e.east),h=Math.min(t.north,e.north);if(!(u>=h||i>=o))return a.defined(n)?(n.west=i,n.south=u,n.east=o,n.north=h,n):new s(i,u,o,h)},s.union=function(t,e,n){a.defined(n)||(n=new s);let u=t.east,o=t.west,h=e.east,r=e.west;u<o&&h>0?u+=i.CesiumMath.TWO_PI:h<r&&u>0&&(h+=i.CesiumMath.TWO_PI),u<o&&r<0?r+=i.CesiumMath.TWO_PI:h<r&&o<0&&(o+=i.CesiumMath.TWO_PI);const d=i.CesiumMath.negativePiToPi(Math.min(o,r)),l=i.CesiumMath.negativePiToPi(Math.max(u,h));return n.west=d,n.south=Math.min(t.south,e.south),n.east=l,n.north=Math.max(t.north,e.north),n},s.expand=function(t,e,n){return a.defined(n)||(n=new s),n.west=Math.min(t.west,e.longitude),n.south=Math.min(t.south,e.latitude),n.east=Math.max(t.east,e.longitude),n.north=Math.max(t.north,e.latitude),n},s.contains=function(t,e){let a=e.longitude;const n=e.latitude,s=t.west;let u=t.east;return u<s&&(u+=i.CesiumMath.TWO_PI,a<0&&(a+=i.CesiumMath.TWO_PI)),(a>s||i.CesiumMath.equalsEpsilon(a,s,i.CesiumMath.EPSILON14))&&(a<u||i.CesiumMath.equalsEpsilon(a,u,i.CesiumMath.EPSILON14))&&n>=t.south&&n<=t.north};const u=new e.Cartographic;s.subsample=function(t,e,o,h){e=a.defaultValue(e,n.Ellipsoid.WGS84),o=a.defaultValue(o,0),a.defined(h)||(h=[]);let r=0;const d=t.north,l=t.south,c=t.east,m=t.west,M=u;M.height=o,M.longitude=m,M.latitude=d,h[r]=e.cartographicToCartesian(M,h[r]),r++,M.longitude=c,h[r]=e.cartographicToCartesian(M,h[r]),r++,M.latitude=l,h[r]=e.cartographicToCartesian(M,h[r]),r++,M.longitude=m,h[r]=e.cartographicToCartesian(M,h[r]),r++,M.latitude=d<0?d:l>0?l:0;for(let a=1;a<8;++a)M.longitude=-Math.PI+a*i.CesiumMath.PI_OVER_TWO,s.contains(t,M)&&(h[r]=e.cartographicToCartesian(M,h[r]),r++);return 0===M.latitude&&(M.longitude=m,h[r]=e.cartographicToCartesian(M,h[r]),r++,M.longitude=c,h[r]=e.cartographicToCartesian(M,h[r]),r++),h.length=r,h},s.subsection=function(t,e,n,u,o,h){if(a.defined(h)||(h=new s),t.west<=t.east){const a=t.east-t.west;h.west=t.west+e*a,h.east=t.west+u*a}else{const a=i.CesiumMath.TWO_PI+t.east-t.west;h.west=i.CesiumMath.negativePiToPi(t.west+e*a),h.east=i.CesiumMath.negativePiToPi(t.west+u*a)}const r=t.north-t.south;return h.south=t.south+n*r,h.north=t.south+o*r,1===e&&(h.west=t.east),1===u&&(h.east=t.east),1===n&&(h.south=t.north),1===o&&(h.north=t.north),h},s.MAX_VALUE=Object.freeze(new s(-Math.PI,-i.CesiumMath.PI_OVER_TWO,Math.PI,i.CesiumMath.PI_OVER_TWO)),t.Rectangle=s}));
