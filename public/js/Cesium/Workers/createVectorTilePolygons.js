define(["./AttributeCompression-d2ca507e","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./Color-e24f904b","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./IndexDatatype-58eb7805","./Math-355606c6","./OrientedBoundingBox-5f5e4f4c","./Rectangle-98b0bef0","./createTaskProcessorWorker","./Matrix4-c57ffbd8","./Matrix3-31d1f01f","./RuntimeError-9b4ce3fb","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./Matrix2-e4a4609a","./Transforms-2afbbfb5","./Interval-d6c8d27a","./DeveloperError-c85858c1","./combine-0c102d93","./RequestType-735c98f2","./EllipsoidTangentPlane-a6ea67fb","./AxisAlignedBoundingBox-d98e5354","./IntersectionTests-01432fe7","./Plane-06f34fae"],(function(e,t,n,r,a,o,i,s,c,l,f,d,u,h,p,g,y,C,b,m,I,x,w,A,E,T){"use strict";const N=new t.Cartesian3,k=new o.Ellipsoid,B=new l.Rectangle,L={min:void 0,max:void 0,indexBytesPerElement:void 0};function O(e,t,n){const a=t.length,o=2+a*c.OrientedBoundingBox.packedLength+1+function(e){const t=e.length;let n=0;for(let a=0;a<t;++a)n+=r.Color.packedLength+3+e[a].batchIds.length;return n}(n),i=new Float64Array(o);let s=0;i[s++]=e,i[s++]=a;for(let e=0;e<a;++e)c.OrientedBoundingBox.pack(t[e],i,s),s+=c.OrientedBoundingBox.packedLength;const l=n.length;i[s++]=l;for(let e=0;e<l;++e){const t=n[e];r.Color.pack(t.color,i,s),s+=r.Color.packedLength,i[s++]=t.offset,i[s++]=t.count;const a=t.batchIds,o=a.length;i[s++]=o;for(let e=0;e<o;++e)i[s++]=a[e]}return i}const U=new t.Cartesian3,F=new t.Cartesian3,M=new t.Cartesian3,P=new t.Cartesian3,R=new t.Cartesian3,D=new n.Cartographic,S=new l.Rectangle;return f((function(f,d){let u;!function(e){const n=new Float64Array(e);let r=0;L.indexBytesPerElement=n[r++],L.min=n[r++],L.max=n[r++],t.Cartesian3.unpack(n,r,N),r+=t.Cartesian3.packedLength,o.Ellipsoid.unpack(n,r,k),r+=o.Ellipsoid.packedLength,l.Rectangle.unpack(n,r,B)}(f.packedBuffer),u=2===L.indexBytesPerElement?new Uint16Array(f.indices):new Uint32Array(f.indices);const h=new Uint16Array(f.positions),p=new Uint32Array(f.counts),g=new Uint32Array(f.indexCounts),y=new Uint32Array(f.batchIds),C=new Uint32Array(f.batchTableColors),b=new Array(p.length),m=N,I=k;let x=B;const w=L.min,A=L.max;let E,T,_,G=f.minimumHeights,v=f.maximumHeights;a.defined(G)&&a.defined(v)&&(G=new Float32Array(G),v=new Float32Array(v));const V=h.length/2,Y=h.subarray(0,V),H=h.subarray(V,2*V);e.AttributeCompression.zigZagDeltaDecode(Y,H);const q=new Float64Array(3*V);for(E=0;E<V;++E){const e=Y[E],r=H[E],a=s.CesiumMath.lerp(x.west,x.east,e/32767),o=s.CesiumMath.lerp(x.south,x.north,r/32767),i=n.Cartographic.fromRadians(a,o,0,D),c=I.cartographicToCartesian(i,U);t.Cartesian3.pack(c,q,3*E)}const W=p.length,j=new Array(W),z=new Array(W);let Z=0,J=0;for(E=0;E<W;++E)j[E]=Z,z[E]=J,Z+=p[E],J+=g[E];const K=new Float32Array(3*V*2),Q=new Uint16Array(2*V),X=new Uint32Array(z.length),$=new Uint32Array(g.length);let ee=[];const te={};for(E=0;E<W;++E)_=C[E],a.defined(te[_])?(te[_].positionLength+=p[E],te[_].indexLength+=g[E],te[_].batchIds.push(E)):te[_]={positionLength:p[E],indexLength:g[E],offset:0,indexOffset:0,batchIds:[E]};let ne,re=0,ae=0;for(_ in te)if(te.hasOwnProperty(_)){ne=te[_],ne.offset=re,ne.indexOffset=ae;const e=2*ne.positionLength,t=2*ne.indexLength+6*ne.positionLength;re+=e,ae+=t,ne.indexLength=t}const oe=[];for(_ in te)te.hasOwnProperty(_)&&(ne=te[_],oe.push({color:r.Color.fromRgba(parseInt(_)),offset:ne.indexOffset,count:ne.indexLength,batchIds:ne.batchIds}));for(E=0;E<W;++E){_=C[E],ne=te[_];const e=ne.offset;let n=3*e,r=e;const o=j[E],i=p[E],s=y[E];let l=w,f=A;a.defined(G)&&a.defined(v)&&(l=G[E],f=v[E]);let d=Number.POSITIVE_INFINITY,h=Number.NEGATIVE_INFINITY,N=Number.POSITIVE_INFINITY,k=Number.NEGATIVE_INFINITY;for(T=0;T<i;++T){const e=t.Cartesian3.unpack(q,3*o+3*T,U);I.scaleToGeodeticSurface(e,e);const a=I.cartesianToCartographic(e,D),i=a.latitude,c=a.longitude;d=Math.min(i,d),h=Math.max(i,h),N=Math.min(c,N),k=Math.max(c,k);const u=I.geodeticSurfaceNormal(e,F);let p=t.Cartesian3.multiplyByScalar(u,l,M);const g=t.Cartesian3.add(e,p,P);p=t.Cartesian3.multiplyByScalar(u,f,p);const y=t.Cartesian3.add(e,p,R);t.Cartesian3.subtract(y,m,y),t.Cartesian3.subtract(g,m,g),t.Cartesian3.pack(y,K,n),t.Cartesian3.pack(g,K,n+3),Q[r]=s,Q[r+1]=s,n+=6,r+=2}x=S,x.west=N,x.east=k,x.south=d,x.north=h,b[E]=c.OrientedBoundingBox.fromRectangle(x,w,A,I);let B=ne.indexOffset;const L=z[E],O=g[E];for(X[E]=B,T=0;T<O;T+=3){const t=u[L+T]-o,n=u[L+T+1]-o,r=u[L+T+2]-o;ee[B++]=2*t+e,ee[B++]=2*n+e,ee[B++]=2*r+e,ee[B++]=2*r+1+e,ee[B++]=2*n+1+e,ee[B++]=2*t+1+e}for(T=0;T<i;++T){const t=T,n=(T+1)%i;ee[B++]=2*t+1+e,ee[B++]=2*n+e,ee[B++]=2*t+e,ee[B++]=2*t+1+e,ee[B++]=2*n+1+e,ee[B++]=2*n+e}ne.offset+=2*i,ne.indexOffset=B,$[E]=B-X[E]}ee=i.IndexDatatype.createTypedArray(K.length/3,ee);const ie=oe.length;for(let e=0;e<ie;++e){const t=oe[e].batchIds;let n=0;const r=t.length;for(let e=0;e<r;++e)n+=$[t[e]];oe[e].count=n}const se=O(2===ee.BYTES_PER_ELEMENT?i.IndexDatatype.UNSIGNED_SHORT:i.IndexDatatype.UNSIGNED_INT,b,oe);return d.push(K.buffer,ee.buffer,X.buffer,$.buffer,Q.buffer,se.buffer),{positions:K.buffer,indices:ee.buffer,indexOffsets:X.buffer,indexCounts:$.buffer,batchIds:Q.buffer,packedBuffer:se.buffer}}))}));
