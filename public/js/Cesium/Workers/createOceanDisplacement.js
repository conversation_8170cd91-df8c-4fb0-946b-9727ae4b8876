define(["./defaultValue-f6d5e6da","./createTaskProcessorWorker"],(function(t,e){"use strict";function n(t,e){this.x=t,this.y=e}function i(t){this.size=t}function s(e){this.size=t.defaultValue(e.size,128),this.length=900,this.windSpeed=t.defaultValue(e.windSpeed,50);var n=t.defaultValue(e.windDir,Math.PI/3);this.windX=Math.cos(n),this.windY=Math.sin(n),this.A=t.defaultValue(e.waveHeight,9e-4),this.g=9.81}function r(t,e){this.gl=t,this.size=e,this.lambda=.8}let h,o,a;n.Conj=function(t){return new n(t.x,-t.y)},n.add=function(t,e){return new n(t.x+e.x,t.y+e.y)},n.substract=function(t,e){return new n(t.x-e.x,t.y-e.y)},n.mult=function(t,e){return new n(t.x*e.x-t.y*e.y,t.x*e.y+e.x*t.y)},n.multScalar=function(t,e){return new n(t.x*e,t.y*e)},n.divideScalar=function(t,e){return new n(t.x/e,t.y/e)},n.Polar=function(t,e){return new n(t*Math.cos(e),t*Math.sin(e))},n.Modulus=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},i.prototype.Inverse=function(t){let e=[];for(let e=0;e<t.length;e++)t[e]=n.Conj(t[e]);e=this.Forward(t);for(let i=0;i<t.length;i++)e[i]=n.Conj(e[i]);return e},i.prototype.Forward=function(t){const e=new Array(t.length),i=-2*Math.PI/t.length;if(t.length<=1)return e[0]=t[0],(isNaN(t[0].x)||isNaN(t[0].y))&&(e[0]=new n(0,0),t[0]=e[0]),e;const s=new Array(t.length/2),r=new Array(t.length/2);for(let e=0;e<t.length/2;e++)s[e]=t[2*e],r[e]=t[2*e+1];const h=this.Forward(s),o=this.Forward(r);for(let e=0;e<t.length/2;e++){const t=n.Polar(1,i*e);o[e]=n.mult(o[e],t)}for(let i=0;i<t.length/2;i++)e[i]=n.add(h[i],o[i]),e[i+t.length/2]=n.substract(h[i],o[i]);return e},i.prototype.Inverse2D=function(t){const e=[],i=[],s=[],r=[];for(let n=0;n<this.size;n++)e[n]=this.Inverse(t[n]);for(let t=0;t<this.size;t++){s[t]=new Array(this.size);for(let i=0;i<this.size;i++)s[t][i]=n.divideScalar(e[i][t],this.size*this.size);i[t]=this.Inverse(s[t])}for(let t=0;t<this.size;t++){r[t]=[];for(let e=0;e<this.size;e++)r[t][e]=i[t][e].x}return r},s.prototype.createH0=function(){const t=[];for(let e=0;e<2*this.size;e++){t[e]=[];for(let n=0;n<2*this.size;n++){const i={g:this.g,A:this.A,windSpeed:this.windSpeed,windX:this.windX,windY:this.windY,Kx:2*Math.PI*(e-this.size/2)/this.length,Ky:2*Math.PI*(n-this.size/2)/this.length},s=this.spectrum(i),r=this.calculateH0(s);t[e][n]=r}}return t},s.prototype.createH1=function(){const t=[];for(let e=0;e<2*this.size;e++){t[e]=[];for(let i=0;i<2*this.size;i++){const s={g:this.g,A:this.A,windSpeed:this.windSpeed,windX:this.windX,windY:this.windY,Kx:2*Math.PI*(-e-this.size/2)/this.length,Ky:2*Math.PI*(-i-this.size/2)/this.length};t[e][i]=n.Conj(this.calculateH0(this.spectrum(s)))}}return t},s.prototype.update=function(t,e,i){const s={h:[],z:[],x:[]};let r,h=0;for(let o=0;o<this.size;o++){s.h[o]=[],s.z[o]=[],s.x[o]=[];for(let a=0;a<this.size;a++){r=2*Math.PI*(o-this.size/2)/this.length,h=2*Math.PI*(a-this.size/2)/this.length;const l=Math.sqrt(r*r+h*h),u=Math.sqrt(9.81*l),d=n.Polar(1,u*t),c=n.mult(e[o][a],d),w=n.mult(i[o][a],n.Conj(d)),f=n.add(c,w),p=n.mult(new n(0,1),f),y=n.multScalar(p,r/l),M=n.multScalar(p,h/l);s.h[o][a]=f,s.z[o][a]=M,s.x[o][a]=y}}return s},s.prototype.spectrum=function(t){let e=Math.sqrt(t.Kx*t.Kx+t.Ky*t.Ky);e<1e-6&&(e=0);const n=Math.sqrt(t.windX*t.windX+t.windY*t.windY),i=t.windSpeed*t.windSpeed*n/t.g,s=t.Kx/e,r=t.Ky/e,h=s*t.windX/n+r*t.windY/n;if(0===h)return 0;return t.A/(e*e*e*e)*Math.exp(-1/(e*e*i*i))*h*h*Math.exp(-e*e*i*i*.001*.001)},s.prototype.calculateH0=function(t){return new n(1/Math.sqrt(2)*this.randomBM()*Math.sqrt(t),1/Math.sqrt(2)*this.randomBM()*Math.sqrt(t))},s.prototype.randomBM=function(){const t=1-Math.random(),e=1-Math.random();return Math.sqrt(-2*Math.log(t))*Math.cos(2*Math.PI*e)},r.prototype.texture=function(t,e,n){const i=[];let s=0;const r=[1,-1];var h=0,o=0;for(let a=0;a<this.size;a++)for(let l=0;l<this.size;l++){const u=r[a+l&1];h>e[a][l]&&(h=e[a][l]),o<e[a][l]&&(o=e[a][l]),i[s]=t[a][l]*this.lambda*u,i[s+1]=e[a][l]*u,i[s+2]=n[a][l]*this.lambda*u,i[s+3]=1,s+=4}return i};const l={h0:void 0,h1:void 0},u={cnt:0,data:void 0};return e((function(e){const n=e.mapSize,d=e.windDir,c=e.windSpeed,w=e.waveHeight;t.defined(l.h0)||(h=new s({size:n,windDir:d,waveHeight:w,windSpeed:c}),o=new i(n),a=new r(null,n),l.h0=h.createH0(),l.h1=h.createH1());const f=e.interval,p=h.update(f,l.h0,l.h1),y=o.Inverse2D(p.h),M=o.Inverse2D(p.x),z=o.Inverse2D(p.z),g=a.texture(M,y,z);return u.cnt+=1,u.data=g,u}))}));
