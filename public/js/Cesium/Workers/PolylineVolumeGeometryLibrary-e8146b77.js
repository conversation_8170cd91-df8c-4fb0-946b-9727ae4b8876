define(["exports","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./EllipsoidTangentPlane-a6ea67fb","./Math-355606c6","./Matrix3-31d1f01f","./PolylinePipeline-07b67faf","./Transforms-2afbbfb5","./defaultValue-f6d5e6da"],(function(e,a,t,n,r,i,s,o,l,c){"use strict";var C=Object.freeze({ROUNDED:0,MITERED:1,BEVELED:2,EV_ARC:3});const u={};function d(e,a){c.defined(u[e])||(u[e]=!0,console.warn(c.defaultValue(a,e)))}d.geometryOutlines="Entity geometry outlines are unsupported on terrain. Outlines will be disabled. To enable outlines, disable geometry terrain clamping by explicitly setting height to 0.",d.geometryZIndex="Entity geometry with zIndex are unsupported when height or extrudedHeight are defined.  zIndex will be ignored",d.geometryHeightReference="Entity corridor, ellipse, polygon or rectangle with heightReference must also have a defined height.  heightReference will be ignored",d.geometryExtrudedHeightReference="Entity corridor, ellipse, polygon or rectangle with extrudedHeightReference must also have a defined extrudedHeight.  extrudedHeightReference will be ignored";const g=[new t.Cartesian3,new t.Cartesian3],y=new t.Cartesian3,h=new t.Cartesian3,m=new t.Cartesian3,f=new t.Cartesian3,p=new t.Cartesian3,w=new t.Cartesian3,x=new t.Cartesian3,E=new t.Cartesian3,P=new t.Cartesian3,M=new t.Cartesian3,T=new t.Cartesian3,b={};let B=new n.Cartographic;function z(e,a,n,r){const i=e[0],s=e[1],o=t.Cartesian3.angleBetween(i,s),l=Math.ceil(o/r),c=new Array(l);let C;if(a===n){for(C=0;C<l;C++)c[C]=a;return c.push(n),c}const u=(n-a)/l;for(C=1;C<l;C++){const e=a+C*u;c[C]=e}return c[0]=a,c.push(n),c}const A=new t.Cartesian3,S=new t.Cartesian3;const R=new t.Cartesian3(-1,0,0);let D=new a.Matrix4;const O=new a.Matrix4;let V=new s.Matrix3;const I=s.Matrix3.IDENTITY.clone(),v=new t.Cartesian3,G=new a.Cartesian4,N=new t.Cartesian3;function H(e,n,i,o,c,C,u,d){let g=v,y=G;D=l.Transforms.eastNorthUpToFixedFrame(e,c,D),g=a.Matrix4.multiplyByPointAsVector(D,R,g),g=t.Cartesian3.normalize(g,g);const h=function(e,n,i,s){const o=new r.EllipsoidTangentPlane(i,s),l=o.projectPointOntoPlane(t.Cartesian3.add(i,e,A),A),c=o.projectPointOntoPlane(t.Cartesian3.add(i,n,S),S),C=a.Cartesian2.angleBetween(l,c);return c.x*l.y-c.y*l.x>=0?-C:C}(g,n,e,c);V=s.Matrix3.fromRotationZ(h,V),N.z=C,D=a.Matrix4.multiplyTransformation(D,a.Matrix4.fromRotationTranslation(V,N,O),D);const m=I;m[0]=u;for(let e=0;e<d;e++)for(let e=0;e<i.length;e+=3)y=t.Cartesian3.fromArray(i,e,y),y=s.Matrix3.multiplyByVector(m,y,y),y=a.Matrix4.multiplyByPoint(D,y,y),o.push(y.x,y.y,y.z);return o}const L=new t.Cartesian3;function j(e,a,n,r,i,s,o){for(let l=0;l<e.length;l+=3){r=H(t.Cartesian3.fromArray(e,l,L),a,n,r,i,s[l/3],o,1)}return r}function Q(e,a){const t=e.length,n=new Array(3*t);let r=0;const i=a.x+a.width/2,s=a.y+a.height/2;for(let a=0;a<t;a++)n[r++]=e[a].x-i,n[r++]=0,n[r++]=e[a].y-s;return n}const q=new l.Quaternion,F=new t.Cartesian3,U=new s.Matrix3;function _(e,a,n,r,o,c,u,d,g,y){const h=t.Cartesian3.angleBetween(t.Cartesian3.subtract(a,e,M),t.Cartesian3.subtract(n,e,T)),m=r===C.BEVELED?0:Math.ceil(h/i.CesiumMath.toRadians(5));let f,p,w;if(f=o?s.Matrix3.fromQuaternion(l.Quaternion.fromAxisAngle(t.Cartesian3.negate(e,M),h/(m+1),q),U):s.Matrix3.fromQuaternion(l.Quaternion.fromAxisAngle(e,h/(m+1),q),U),a=t.Cartesian3.clone(a,F),m>0){const n=y?2:1;for(let r=0;r<m;r++)a=s.Matrix3.multiplyByVector(f,a,a),p=t.Cartesian3.subtract(a,e,M),p=t.Cartesian3.normalize(p,p),o||(p=t.Cartesian3.negate(p,p)),w=c.scaleToGeodeticSurface(a,T),u=H(w,p,d,u,c,g,1,n)}else p=t.Cartesian3.subtract(a,e,M),p=t.Cartesian3.normalize(p,p),o||(p=t.Cartesian3.negate(p,p)),w=c.scaleToGeodeticSurface(a,T),u=H(w,p,d,u,c,g,1,1),n=t.Cartesian3.clone(n,F),p=t.Cartesian3.subtract(n,e,M),p=t.Cartesian3.normalize(p,p),o||(p=t.Cartesian3.negate(p,p)),w=c.scaleToGeodeticSurface(n,T),u=H(w,p,d,u,c,g,1,1);return u}b.removeDuplicatesFromShape=function(e){const t=e.length,n=[];for(let r=t-1,i=0;i<t;r=i++){const t=e[r],s=e[i];a.Cartesian2.equals(t,s)||n.push(s)}return n},b.angleIsGreaterThanPi=function(e,a,n,i){const s=new r.EllipsoidTangentPlane(n,i),o=s.projectPointOntoPlane(t.Cartesian3.add(n,e,A),A),l=s.projectPointOntoPlane(t.Cartesian3.add(n,a,S),S);return l.x*o.y-l.y*o.x>=0};const Z=new t.Cartesian3,W=new t.Cartesian3;b.computePositions=function(e,a,n,r,s){const l=r._ellipsoid,c=function(e,a){const t=new Array(e.length);for(let n=0;n<e.length;n++){const r=e[n];B=a.cartesianToCartographic(r,B),t[n]=B.height,e[n]=a.scaleToGeodeticSurface(r,r)}return t}(e,l),u=r._granularity,T=r._cornerType,A=s?function(e,a){const t=e.length,n=new Array(6*t);let r=0;const i=a.x+a.width/2,s=a.y+a.height/2;let o=e[0];n[r++]=o.x-i,n[r++]=0,n[r++]=o.y-s;for(let a=1;a<t;a++){o=e[a];const t=o.x-i,l=o.y-s;n[r++]=t,n[r++]=0,n[r++]=l,n[r++]=t,n[r++]=0,n[r++]=l}return o=e[0],n[r++]=o.x-i,n[r++]=0,n[r++]=o.y-s,n}(a,n):Q(a,n),S=s?Q(a,n):void 0,R=n.height/2,D=n.width/2;let O=e.length,V=[],I=s?[]:void 0,v=y,G=h,N=m,L=f,q=p,F=w,U=x,Y=E,k=P,J=e[0],K=e[1];L=l.geodeticSurfaceNormal(J,L),v=t.Cartesian3.subtract(K,J,v),v=t.Cartesian3.normalize(v,v),Y=t.Cartesian3.cross(L,v,Y),Y=t.Cartesian3.normalize(Y,Y);let X,$,ee=c[0],ae=c[1];s&&(I=H(J,Y,S,I,l,ee+R,1,1)),k=t.Cartesian3.clone(J,k),J=K,G=t.Cartesian3.negate(v,G);for(let a=1;a<O-1;a++){const n=s?2:1;if(K=e[a+1],J.equals(K)){d("Positions are too close and are considered equivalent with rounding error.");continue}v=t.Cartesian3.subtract(K,J,v),v=t.Cartesian3.normalize(v,v),N=t.Cartesian3.add(v,G,N),N=t.Cartesian3.normalize(N,N),L=l.geodeticSurfaceNormal(J,L);const r=t.Cartesian3.multiplyByScalar(L,t.Cartesian3.dot(v,L),Z);t.Cartesian3.subtract(v,r,r),t.Cartesian3.normalize(r,r);const y=t.Cartesian3.multiplyByScalar(L,t.Cartesian3.dot(G,L),W);t.Cartesian3.subtract(G,y,y),t.Cartesian3.normalize(y,y);if(!i.CesiumMath.equalsEpsilon(Math.abs(t.Cartesian3.dot(r,y)),1,i.CesiumMath.EPSILON7)){N=t.Cartesian3.cross(N,L,N),N=t.Cartesian3.cross(L,N,N),N=t.Cartesian3.normalize(N,N);const e=1/Math.max(.25,t.Cartesian3.magnitude(t.Cartesian3.cross(N,G,M))),a=b.angleIsGreaterThanPi(v,G,J,l);a?(q=t.Cartesian3.add(J,t.Cartesian3.multiplyByScalar(N,e*D,N),q),F=t.Cartesian3.add(q,t.Cartesian3.multiplyByScalar(Y,D,F),F),g[0]=t.Cartesian3.clone(k,g[0]),g[1]=t.Cartesian3.clone(F,g[1]),X=z(g,ee+R,ae+R,u),$=o.PolylinePipeline.generateArc({positions:g,granularity:u,ellipsoid:l}),V=j($,Y,A,V,l,X,1),Y=t.Cartesian3.cross(L,v,Y),Y=t.Cartesian3.normalize(Y,Y),U=t.Cartesian3.add(q,t.Cartesian3.multiplyByScalar(Y,D,U),U),T===C.ROUNDED||T===C.BEVELED?_(q,F,U,T,a,l,V,A,ae+R,s):(N=t.Cartesian3.negate(N,N),V=H(J,N,A,V,l,ae+R,e,n)),k=t.Cartesian3.clone(U,k)):(q=t.Cartesian3.add(J,t.Cartesian3.multiplyByScalar(N,e*D,N),q),F=t.Cartesian3.add(q,t.Cartesian3.multiplyByScalar(Y,-D,F),F),g[0]=t.Cartesian3.clone(k,g[0]),g[1]=t.Cartesian3.clone(F,g[1]),X=z(g,ee+R,ae+R,u),$=o.PolylinePipeline.generateArc({positions:g,granularity:u,ellipsoid:l}),V=j($,Y,A,V,l,X,1),Y=t.Cartesian3.cross(L,v,Y),Y=t.Cartesian3.normalize(Y,Y),U=t.Cartesian3.add(q,t.Cartesian3.multiplyByScalar(Y,-D,U),U),T===C.ROUNDED||T===C.BEVELED?_(q,F,U,T,a,l,V,A,ae+R,s):V=H(J,N,A,V,l,ae+R,e,n),k=t.Cartesian3.clone(U,k)),G=t.Cartesian3.negate(v,G)}else V=H(k,Y,A,V,l,ee+R,1,1),k=J;ee=ae,ae=c[a+1],J=K}g[0]=t.Cartesian3.clone(k,g[0]),g[1]=t.Cartesian3.clone(J,g[1]),X=z(g,ee+R,ae+R,u),$=o.PolylinePipeline.generateArc({positions:g,granularity:u,ellipsoid:l}),V=j($,Y,A,V,l,X,1),s&&(I=H(J,Y,S,I,l,ae+R,1,1)),O=V.length;const te=s?O+I.length:O,ne=new Float64Array(te);return ne.set(V),s&&ne.set(I,O),ne};var Y=b;e.CornerType=C,e.PolylineVolumeGeometryLibrary=Y,e.oneTimeWarning=d}));
