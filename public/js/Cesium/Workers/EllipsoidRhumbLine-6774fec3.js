define(["exports","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./Math-355606c6"],(function(t,i,e,a,n,s){"use strict";function h(t,i,e){if(0===t)return i*e;const a=t*t,n=a*a,s=n*a,h=s*a,u=h*a,o=u*a,l=e;return i*((1-a/4-3*n/64-5*s/256-175*h/16384-441*u/65536-4851*o/1048576)*l-(3*a/8+3*n/32+45*s/1024+105*h/4096+2205*u/131072+6237*o/524288)*Math.sin(2*l)+(15*n/256+45*s/1024+525*h/16384+1575*u/65536+155925*o/8388608)*Math.sin(4*l)-(35*s/3072+175*h/12288+3675*u/262144+13475*o/1048576)*Math.sin(6*l)+(315*h/131072+2205*u/524288+43659*o/8388608)*Math.sin(8*l)-(693*u/1310720+6237*o/5242880)*Math.sin(10*l)+1001*o/8388608*Math.sin(12*l))}function u(t,i){if(0===t)return Math.log(Math.tan(.5*(s.CesiumMath.PI_OVER_TWO+i)));const e=t*Math.sin(i);return Math.log(Math.tan(.5*(s.CesiumMath.PI_OVER_TWO+i)))-t/2*Math.log((1+e)/(1-e))}const o=new i.Cartesian3,l=new i.Cartesian3;function r(t,a,n,r){i.Cartesian3.normalize(r.cartographicToCartesian(a,l),o),i.Cartesian3.normalize(r.cartographicToCartesian(n,l),l);const d=r.maximumRadius,M=r.minimumRadius,c=d*d,g=M*M;t._ellipticitySquared=(c-g)/c,t._ellipticity=Math.sqrt(t._ellipticitySquared),t._start=e.Cartographic.clone(a,t._start),t._start.height=0,t._end=e.Cartographic.clone(n,t._end),t._end.height=0,t._heading=function(t,i,e,a,n){const h=u(t._ellipticity,e),o=u(t._ellipticity,n);return Math.atan2(s.CesiumMath.negativePiToPi(a-i),o-h)}(t,a.longitude,a.latitude,n.longitude,n.latitude),t._distance=function(t,i,e,a,n,u,o){const l=t._heading,r=u-a;let d=0;if(s.CesiumMath.equalsEpsilon(Math.abs(l),s.CesiumMath.PI_OVER_TWO,s.CesiumMath.EPSILON8))if(i===e)d=i*Math.cos(n)*s.CesiumMath.negativePiToPi(r);else{const e=Math.sin(n);d=i*Math.cos(n)*s.CesiumMath.negativePiToPi(r)/Math.sqrt(1-t._ellipticitySquared*e*e)}else{const e=h(t._ellipticity,i,n);d=(h(t._ellipticity,i,o)-e)/Math.cos(l)}return Math.abs(d)}(t,r.maximumRadius,r.minimumRadius,a.longitude,a.latitude,n.longitude,n.latitude)}function d(t,i,n,o,l,r){if(0===n)return e.Cartographic.clone(t,r);const d=l*l;let M,c,g;if(Math.abs(s.CesiumMath.PI_OVER_TWO-Math.abs(i))>s.CesiumMath.EPSILON8){if(c=function(t,i,e){const a=t/e;if(0===i)return a;const n=a*a,s=n*a,h=s*a,u=i*i,o=u*u,l=o*u,r=l*u,d=r*u,M=d*u,c=Math.sin(2*a),g=Math.cos(2*a),m=Math.sin(4*a),_=Math.cos(4*a),p=Math.sin(6*a),C=Math.cos(6*a),f=Math.sin(8*a),P=Math.cos(8*a),O=Math.sin(10*a);return a+a*u/4+7*a*o/64+15*a*l/256+579*a*r/16384+1515*a*d/65536+16837*a*M/1048576+(3*a*o/16+45*a*l/256-a*(32*n-561)*r/4096-a*(232*n-1677)*d/16384+a*(399985-90560*n+512*h)*M/5242880)*g+(21*a*l/256+483*a*r/4096-a*(224*n-1969)*d/16384-a*(33152*n-112599)*M/1048576)*_+(151*a*r/4096+4681*a*d/65536+1479*a*M/16384-453*s*M/32768)*C+(1097*a*d/65536+42783*a*M/1048576)*P+8011*a*M/1048576*Math.cos(10*a)+(3*u/8+3*o/16+213*l/2048-3*n*l/64+255*r/4096-33*n*r/512+20861*d/524288-33*n*d/512+h*d/1024+28273*M/1048576-471*n*M/8192+9*h*M/4096)*c+(21*o/256+21*l/256+533*r/8192-21*n*r/512+197*d/4096-315*n*d/4096+584039*M/16777216-12517*n*M/131072+7*h*M/2048)*m+(151*l/6144+151*r/4096+5019*d/131072-453*n*d/16384+26965*M/786432-8607*n*M/131072)*p+(1097*r/131072+1097*d/65536+225797*M/10485760-1097*n*M/65536)*f+(8011*d/2621440+8011*M/1048576)*O+293393*M/251658240*Math.sin(12*a)}(h(l,o,t.latitude)+n*Math.cos(i),l,o),Math.abs(i)<s.CesiumMath.EPSILON10)M=s.CesiumMath.negativePiToPi(t.longitude);else{const e=u(l,t.latitude),a=u(l,c);g=Math.tan(i)*(a-e),M=s.CesiumMath.negativePiToPi(t.longitude+g)}}else{let e;if(c=t.latitude,0===l)e=o*Math.cos(t.latitude);else{const i=Math.sin(t.latitude);e=o*Math.cos(t.latitude)/Math.sqrt(1-d*i*i)}g=n/e,M=i>0?s.CesiumMath.negativePiToPi(t.longitude+g):s.CesiumMath.negativePiToPi(t.longitude-g)}return a.defined(r)?(r.longitude=M,r.latitude=c,r.height=0,r):new e.Cartographic(M,c,0)}function M(t,i,s){const h=a.defaultValue(s,n.Ellipsoid.WGS84);this._ellipsoid=h,this._start=new e.Cartographic,this._end=new e.Cartographic,this._heading=void 0,this._distance=void 0,this._ellipticity=void 0,this._ellipticitySquared=void 0,a.defined(t)&&a.defined(i)&&r(this,t,i,h)}Object.defineProperties(M.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},surfaceDistance:{get:function(){return this._distance}},start:{get:function(){return this._start}},end:{get:function(){return this._end}},heading:{get:function(){return this._heading}}}),M.fromStartHeadingDistance=function(t,i,e,h,u){const o=a.defaultValue(h,n.Ellipsoid.WGS84),l=o.maximumRadius,r=o.minimumRadius,c=l*l,g=r*r,m=Math.sqrt((c-g)/c),_=d(t,i=s.CesiumMath.negativePiToPi(i),e,o.maximumRadius,m);return!a.defined(u)||a.defined(h)&&!h.equals(u.ellipsoid)?new M(t,_,o):(u.setEndPoints(t,_),u)},M.prototype.setEndPoints=function(t,i){r(this,t,i,this._ellipsoid)},M.prototype.interpolateUsingFraction=function(t,i){return this.interpolateUsingSurfaceDistance(t*this._distance,i)},M.prototype.interpolateUsingSurfaceDistance=function(t,i){return d(this._start,this._heading,t,this._ellipsoid.maximumRadius,this._ellipticity,i)},M.prototype.findIntersectionWithLongitude=function(t,i){const n=this._ellipticity,h=this._heading,u=Math.abs(h),o=this._start;if(t=s.CesiumMath.negativePiToPi(t),s.CesiumMath.equalsEpsilon(Math.abs(t),Math.PI,s.CesiumMath.EPSILON14)&&(t=s.CesiumMath.sign(o.longitude)*Math.PI),a.defined(i)||(i=new e.Cartographic),Math.abs(s.CesiumMath.PI_OVER_TWO-u)<=s.CesiumMath.EPSILON8)return i.longitude=t,i.latitude=o.latitude,i.height=0,i;if(s.CesiumMath.equalsEpsilon(Math.abs(s.CesiumMath.PI_OVER_TWO-u),s.CesiumMath.PI_OVER_TWO,s.CesiumMath.EPSILON8)){if(s.CesiumMath.equalsEpsilon(t,o.longitude,s.CesiumMath.EPSILON12))return;return i.longitude=t,i.latitude=s.CesiumMath.PI_OVER_TWO*s.CesiumMath.sign(s.CesiumMath.PI_OVER_TWO-h),i.height=0,i}const l=o.latitude,r=n*Math.sin(l),d=Math.tan(.5*(s.CesiumMath.PI_OVER_TWO+l))*Math.exp((t-o.longitude)/Math.tan(h)),M=(1+r)/(1-r);let c,g=o.latitude;do{c=g;const t=n*Math.sin(c),i=(1+t)/(1-t);g=2*Math.atan(d*Math.pow(i/M,n/2))-s.CesiumMath.PI_OVER_TWO}while(!s.CesiumMath.equalsEpsilon(g,c,s.CesiumMath.EPSILON12));return i.longitude=t,i.latitude=g,i.height=0,i},M.prototype.findIntersectionWithLatitude=function(t,i){const n=this._ellipticity,h=this._heading,o=this._start;if(s.CesiumMath.equalsEpsilon(Math.abs(h),s.CesiumMath.PI_OVER_TWO,s.CesiumMath.EPSILON8))return;const l=u(n,o.latitude),r=u(n,t),d=Math.tan(h)*(r-l),M=s.CesiumMath.negativePiToPi(o.longitude+d);return a.defined(i)?(i.longitude=M,i.latitude=t,i.height=0,i):new e.Cartographic(M,t,0)},t.EllipsoidRhumbLine=M}));
