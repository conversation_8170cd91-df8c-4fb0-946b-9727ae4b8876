define(["exports","./Transforms-2afbbfb5","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./defaultValue-f6d5e6da","./EllipseGeometryLibrary-15deff83","./Ellipsoid-8e26549b","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-58eb7805","./Math-355606c6"],(function(e,t,i,r,n,o,a,s,l,u,d,c){"use strict";const p=new i.Cartesian3;let f=new i.Cartesian3;const m=new t.BoundingSphere,h=new t.BoundingSphere;function y(e){const t=(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT)).center,r=n.defaultValue(e.ellipsoid,a.Ellipsoid.WGS84),o=e.semiMajorAxis,s=e.semiMinorAxis,l=n.defaultValue(e.granularity,c.CesiumMath.RADIANS_PER_DEGREE),u=n.defaultValue(e.height,0),d=n.defaultValue(e.extrudedHeight,u);this._center=i.Cartesian3.clone(t),this._semiMajorAxis=o,this._semiMinorAxis=s,this._ellipsoid=a.Ellipsoid.clone(r),this._rotation=n.defaultValue(e.rotation,0),this._height=Math.max(d,u),this._granularity=l,this._extrudedHeight=Math.min(d,u),this._numberOfVerticalLines=Math.max(n.defaultValue(e.numberOfVerticalLines,16),0),this._offsetAttribute=e.offsetAttribute,this._workerName="createEllipseOutlineGeometry"}y.packedLength=i.Cartesian3.packedLength+a.Ellipsoid.packedLength+8,y.pack=function(e,t,r){return r=n.defaultValue(r,0),i.Cartesian3.pack(e._center,t,r),r+=i.Cartesian3.packedLength,a.Ellipsoid.pack(e._ellipsoid,t,r),r+=a.Ellipsoid.packedLength,t[r++]=e._semiMajorAxis,t[r++]=e._semiMinorAxis,t[r++]=e._rotation,t[r++]=e._height,t[r++]=e._granularity,t[r++]=e._extrudedHeight,t[r++]=e._numberOfVerticalLines,t[r]=n.defaultValue(e._offsetAttribute,-1),t};const A=new i.Cartesian3,_=new a.Ellipsoid,b={center:A,ellipsoid:_,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};y.unpack=function(e,t,r){t=n.defaultValue(t,0);const o=i.Cartesian3.unpack(e,t,A);t+=i.Cartesian3.packedLength;const s=a.Ellipsoid.unpack(e,t,_);t+=a.Ellipsoid.packedLength;const l=e[t++],u=e[t++],d=e[t++],c=e[t++],p=e[t++],f=e[t++],m=e[t++],h=e[t];return n.defined(r)?(r._center=i.Cartesian3.clone(o,r._center),r._ellipsoid=a.Ellipsoid.clone(s,r._ellipsoid),r._semiMajorAxis=l,r._semiMinorAxis=u,r._rotation=d,r._height=c,r._granularity=p,r._extrudedHeight=f,r._numberOfVerticalLines=m,r._offsetAttribute=-1===h?void 0:h,r):(b.height=c,b.extrudedHeight=f,b.granularity=p,b.rotation=d,b.semiMajorAxis=l,b.semiMinorAxis=u,b.numberOfVerticalLines=m,b.offsetAttribute=-1===h?void 0:h,new y(b))},y.createGeometry=function(e){if(e._semiMajorAxis<=0||e._semiMinorAxis<=0)return;const a=e._height,y=e._extrudedHeight,A=!c.CesiumMath.equalsEpsilon(a,y,0,c.CesiumMath.EPSILON2);e._center=e._ellipsoid.scaleToGeodeticSurface(e._center,e._center);const _={center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:e._ellipsoid,rotation:e._rotation,height:a,granularity:e._granularity,numberOfVerticalLines:e._numberOfVerticalLines};let b;if(A)_.extrudedHeight=y,_.offsetAttribute=e._offsetAttribute,b=function(e){const a=e.center,f=e.ellipsoid,y=e.semiMajorAxis;let A=i.Cartesian3.multiplyByScalar(f.geodeticSurfaceNormal(a,p),e.height,p);m.center=i.Cartesian3.add(a,A,m.center),m.radius=y,A=i.Cartesian3.multiplyByScalar(f.geodeticSurfaceNormal(a,A),e.extrudedHeight,A),h.center=i.Cartesian3.add(a,A,h.center),h.radius=y;let _=o.EllipseGeometryLibrary.computeEllipsePositions(e,!1,!0).outerPositions;const b=new l.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:o.EllipseGeometryLibrary.raisePositionsToHeight(_,e,!0)})});_=b.position.values;const g=t.BoundingSphere.union(m,h);let x=_.length/3;if(n.defined(e.offsetAttribute)){let t=new Uint8Array(x);if(e.offsetAttribute===u.GeometryOffsetAttribute.TOP)t=t.fill(1,0,x/2);else{const i=e.offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1;t=t.fill(i)}b.applyOffset=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:t})}let E=n.defaultValue(e.numberOfVerticalLines,16);E=c.CesiumMath.clamp(E,0,x/2);const M=d.IndexDatatype.createTypedArray(x,2*x+2*E);x/=2;let C,G,L=0;for(C=0;C<x;++C)M[L++]=C,M[L++]=(C+1)%x,M[L++]=C+x,M[L++]=(C+1)%x+x;if(E>0){const e=Math.min(E,x);G=Math.round(x/e);const t=Math.min(G*E,x);for(C=0;C<t;C+=G)M[L++]=C,M[L++]=C+x}return{boundingSphere:g,attributes:b,indices:M}}(_);else if(b=function(e){const n=e.center;f=i.Cartesian3.multiplyByScalar(e.ellipsoid.geodeticSurfaceNormal(n,f),e.height,f),f=i.Cartesian3.add(n,f,f);const a=new t.BoundingSphere(f,e.semiMajorAxis),u=o.EllipseGeometryLibrary.computeEllipsePositions(e,!1,!0).outerPositions,c=new l.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:o.EllipseGeometryLibrary.raisePositionsToHeight(u,e,!1)})}),p=u.length/3,m=d.IndexDatatype.createTypedArray(p,2*p);let h=0;for(let e=0;e<p;++e)m[h++]=e,m[h++]=(e+1)%p;return{boundingSphere:a,attributes:c,indices:m}}(_),n.defined(e._offsetAttribute)){const t=b.attributes.position.values.length,i=e._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,n=new Uint8Array(t/3).fill(i);b.attributes.applyOffset=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:n})}return new s.Geometry({attributes:b.attributes,indices:b.indices,primitiveType:s.PrimitiveType.LINES,boundingSphere:b.boundingSphere,offsetAttribute:e._offsetAttribute})},e.EllipseOutlineGeometry=y}));
