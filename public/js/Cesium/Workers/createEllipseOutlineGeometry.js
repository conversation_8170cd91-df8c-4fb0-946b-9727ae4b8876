define(["./Cartesian3-529c236c","./defaultValue-f6d5e6da","./EllipseOutlineGeometry-34206fce","./Ellipsoid-8e26549b","./Math-355606c6","./Transforms-2afbbfb5","./Cartographic-dbefb6fa","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./EllipseGeometryLibrary-15deff83","./GeometryAttribute-f7a0845b","./Matrix2-e4a4609a","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-58eb7805"],(function(e,t,r,i,n,l,o,a,s,u,p,c,m,y,d,f,G,E,b,x,C,M,O){"use strict";return function(n,l){return t.defined(l)&&(n=r.EllipseOutlineGeometry.unpack(n,l)),n._center=e.Cartesian3.clone(n._center),n._ellipsoid=i.Ellipsoid.clone(n._ellipsoid),r.EllipseOutlineGeometry.createGeometry(n)}}));
