define(["./Cartesian3-529c236c","./defaultValue-f6d5e6da","./EllipseGeometry-a9ea9512","./Ellipsoid-8e26549b","./VertexFormat-fbdec922","./Math-355606c6","./Transforms-2afbbfb5","./Cartographic-dbefb6fa","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./EllipseGeometryLibrary-15deff83","./GeometryAttribute-f7a0845b","./Matrix2-e4a4609a","./GeometryAttributes-1e4ddcd2","./GeometryInstance-1d11f88d","./GeometryOffsetAttribute-2579b8d2","./GeometryPipeline-69b47aa9","./AttributeCompression-d2ca507e","./EncodedCartesian3-94199dac","./IndexDatatype-58eb7805","./IntersectionTests-01432fe7","./Plane-06f34fae"],(function(e,t,i,r,o,n,l,s,a,m,d,u,p,c,y,_,x,h,G,g,E,f,v,w,M,A,C,V,R,F){"use strict";function b(e){const r=(e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT)).radius,o={center:e.center,semiMajorAxis:r,semiMinorAxis:r,ellipsoid:e.ellipsoid,height:e.height,extrudedHeight:e.extrudedHeight,granularity:e.granularity,vertexFormat:e.vertexFormat,stRotation:e.stRotation,shadowVolume:e.shadowVolume};this._ellipseGeometry=new i.EllipseGeometry(o),this._workerName="createCircleGeometry"}b.packedLength=i.EllipseGeometry.packedLength,b.pack=function(e,t,r){return i.EllipseGeometry.pack(e._ellipseGeometry,t,r)};const j=new i.EllipseGeometry({center:new e.Cartesian3,semiMajorAxis:1,semiMinorAxis:1}),k={center:new e.Cartesian3,radius:void 0,ellipsoid:r.Ellipsoid.clone(r.Ellipsoid.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,vertexFormat:new o.VertexFormat,stRotation:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0,shadowVolume:void 0};return b.unpack=function(n,l,s){const a=i.EllipseGeometry.unpack(n,l,j);return k.center=e.Cartesian3.clone(a._center,k.center),k.ellipsoid=r.Ellipsoid.clone(a._ellipsoid,k.ellipsoid),k.height=a._height,k.extrudedHeight=a._extrudedHeight,k.granularity=a._granularity,k.vertexFormat=o.VertexFormat.clone(a._vertexFormat,k.vertexFormat),k.stRotation=a._stRotation,k.shadowVolume=a._shadowVolume,t.defined(s)?(k.semiMajorAxis=a._semiMajorAxis,k.semiMinorAxis=a._semiMinorAxis,s._ellipseGeometry=new i.EllipseGeometry(k),s):(k.radius=a._semiMajorAxis,new b(k))},b.createGeometry=function(e){return i.EllipseGeometry.createGeometry(e._ellipseGeometry)},b.createShadowVolume=function(e,t,i){const r=e._ellipseGeometry._granularity,n=e._ellipseGeometry._ellipsoid,l=t(r,n),s=i(r,n);return new b({center:e._ellipseGeometry._center,radius:e._ellipseGeometry._semiMajorAxis,ellipsoid:n,stRotation:e._ellipseGeometry._stRotation,granularity:r,extrudedHeight:l,height:s,vertexFormat:o.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(b.prototype,{rectangle:{get:function(){return this._ellipseGeometry.rectangle}},textureCoordinateRotationPoints:{get:function(){return this._ellipseGeometry.textureCoordinateRotationPoints}}}),function(i,o){return t.defined(o)&&(i=b.unpack(i,o)),i._ellipseGeometry._center=e.Cartesian3.clone(i._ellipseGeometry._center),i._ellipseGeometry._ellipsoid=r.Ellipsoid.clone(i._ellipseGeometry._ellipsoid),b.createGeometry(i)}}));
