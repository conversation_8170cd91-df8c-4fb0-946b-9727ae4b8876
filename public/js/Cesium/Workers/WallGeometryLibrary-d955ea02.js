define(["exports","./arrayRemoveDuplicates-0d8dde26","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Math-355606c6","./PolylinePipeline-33453448"],(function(e,t,i,n,o,r,a){"use strict";const s={};function l(e,t){return r.CesiumMath.equalsEpsilon(e.latitude,t.latitude,r.CesiumMath.EPSILON10)&&r.CesiumMath.equalsEpsilon(e.longitude,t.longitude,r.CesiumMath.EPSILON10)}const h=new n.Cartographic,g=new n.Cartographic;const c=new Array(2),u=new Array(2),p={positions:void 0,height:void 0,granularity:void 0,ellipsoid:void 0};s.computePositions=function(e,s,d,y,m,P){const f=function(e,r,a,s){const c=(r=t.arrayRemoveDuplicates(r,i.Cartesian3.equalsEpsilon)).length;if(c<2)return;const u=o.defined(s),p=o.defined(a),d=new Array(c),y=new Array(c),m=new Array(c),P=r[0];d[0]=P;const f=e.cartesianToCartographic(P,h);p&&(f.height=a[0]),y[0]=f.height,m[0]=u?s[0]:0;let A=y[0]===m[0],C=1;for(let t=1;t<c;++t){const i=r[t],o=e.cartesianToCartographic(i,g);p&&(o.height=a[t]),A=A&&0===o.height,l(f,o)?f.height<o.height&&(y[C-1]=o.height):(d[C]=i,y[C]=o.height,m[C]=u?s[t]:0,A=A&&y[C]===m[C],n.Cartographic.clone(o,f),++C)}return A||C<2?void 0:(d.length=C,y.length=C,m.length=C,{positions:d,topHeights:y,bottomHeights:m})}(e,s,d,y);if(!o.defined(f))return;s=f.positions,d=f.topHeights,y=f.bottomHeights;const A=s.length,C=A-2;let w,v;const M=r.CesiumMath.chordLength(m,e.maximumRadius),b=p;if(b.minDistance=M,b.ellipsoid=e,P){let e,t=0;for(e=0;e<A-1;e++)t+=a.PolylinePipeline.numberOfPoints(s[e],s[e+1],M)+1;w=new Float64Array(3*t),v=new Float64Array(3*t);const i=c,n=u;b.positions=i,b.height=n;let o=0;for(e=0;e<A-1;e++){i[0]=s[e],i[1]=s[e+1],n[0]=d[e],n[1]=d[e+1];const t=a.PolylinePipeline.generateArc(b);w.set(t,o),n[0]=y[e],n[1]=y[e+1],v.set(a.PolylinePipeline.generateArc(b),o),o+=t.length}}else b.positions=s,b.height=d,w=new Float64Array(a.PolylinePipeline.generateArc(b)),b.height=y,v=new Float64Array(a.PolylinePipeline.generateArc(b));return{bottomPositions:v,topPositions:w,numCorners:C}};var d=s;e.WallGeometryLibrary=d}));
