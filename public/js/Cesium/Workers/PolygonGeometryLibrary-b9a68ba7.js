define(["exports","./ArcType-26a3f38d","./arrayRemoveDuplicates-0d8dde26","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./ComponentDatatype-ab629b88","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./EllipsoidRhumbLine-6774fec3","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryPipeline-69b47aa9","./IndexDatatype-58eb7805","./IntersectionTests-01432fe7","./Math-355606c6","./Matrix3-31d1f01f","./Plane-06f34fae","./PolygonPipeline-39b84ada","./Transforms-2afbbfb5"],(function(e,t,n,i,o,r,s,a,c,l,u,h,d,f,p,g,y,m,C,b){"use strict";function T(e,t){this.positions=a.defined(e)?e:[],this.holes=a.defined(t)?t:[]}function v(){this._array=[],this._offset=0,this._length=0}Object.defineProperties(v.prototype,{length:{get:function(){return this._length}}}),v.prototype.enqueue=function(e){this._array.push(e),this._length++},v.prototype.dequeue=function(){if(0===this._length)return;const e=this._array;let t=this._offset;const n=e[t];return e[t]=void 0,t++,t>10&&2*t>e.length&&(this._array=e.slice(t),t=0),this._offset=t,this._length--,n},v.prototype.peek=function(){if(0!==this._length)return this._array[this._offset]},v.prototype.contains=function(e){return-1!==this._array.indexOf(e)},v.prototype.clear=function(){this._array.length=this._offset=this._length=0},v.prototype.sort=function(e){this._offset>0&&(this._array=this._array.slice(this._offset),this._offset=0),this._array.sort(e)};const x={computeHierarchyPackedLength:function(e,t){let n=0;const i=[e];for(;i.length>0;){const e=i.pop();if(!a.defined(e))continue;n+=2;const o=e.positions,r=e.holes;if(a.defined(o)&&o.length>0&&(n+=o.length*t.packedLength),a.defined(r)){const e=r.length;for(let t=0;t<e;++t)i.push(r[t])}}return n},packPolygonHierarchy:function(e,t,n,i){const o=[e];for(;o.length>0;){const e=o.pop();if(!a.defined(e))continue;const r=e.positions,s=e.holes;if(t[n++]=a.defined(r)?r.length:0,t[n++]=a.defined(s)?s.length:0,a.defined(r)){const e=r.length;for(let o=0;o<e;++o,n+=i.packedLength)i.pack(r[o],t,n)}if(a.defined(s)){const e=s.length;for(let t=0;t<e;++t)o.push(s[t])}}return n},unpackPolygonHierarchy:function(e,t,n){const i=e[t++],o=e[t++],r=new Array(i),s=o>0?new Array(o):void 0;for(let o=0;o<i;++o,t+=n.packedLength)r[o]=n.unpack(e,t);for(let i=0;i<o;++i)s[i]=x.unpackPolygonHierarchy(e,t,n),t=s[i].startingIndex,delete s[i].startingIndex;return{positions:r,holes:s,startingIndex:t}}},A=new i.Cartesian2;function w(e,t,n,o){return i.Cartesian2.subtract(t,e,A),i.Cartesian2.multiplyByScalar(A,n/o,A),i.Cartesian2.add(e,A,A),[A.x,A.y]}const M=new o.Cartesian3;function P(e,t,n,i){return o.Cartesian3.subtract(t,e,M),o.Cartesian3.multiplyByScalar(M,n/i,M),o.Cartesian3.add(e,M,M),[M.x,M.y,M.z]}x.subdivideLineCount=function(e,t,n){const i=o.Cartesian3.distance(e,t)/n,r=Math.max(0,Math.ceil(g.CesiumMath.log2(i)));return Math.pow(2,r)};const E=new r.Cartographic,I=new r.Cartographic,L=new r.Cartographic,_=new o.Cartesian3,D=new l.EllipsoidRhumbLine;x.subdivideRhumbLineCount=function(e,t,n,i){const o=e.cartesianToCartographic(t,E),r=e.cartesianToCartographic(n,I),s=new l.EllipsoidRhumbLine(o,r,e).surfaceDistance/i,a=Math.max(0,Math.ceil(g.CesiumMath.log2(s)));return Math.pow(2,a)},x.subdivideTexcoordLine=function(e,t,n,o,r,s){const a=x.subdivideLineCount(n,o,r),c=i.Cartesian2.distance(e,t),l=c/a,u=s;u.length=2*a;let h=0;for(let n=0;n<a;n++){const i=w(e,t,n*l,c);u[h++]=i[0],u[h++]=i[1]}return u},x.subdivideLine=function(e,t,n,i){const r=x.subdivideLineCount(e,t,n),s=o.Cartesian3.distance(e,t),c=s/r;a.defined(i)||(i=[]);const l=i;l.length=3*r;let u=0;for(let n=0;n<r;n++){const i=P(e,t,n*c,s);l[u++]=i[0],l[u++]=i[1],l[u++]=i[2]}return l},x.subdivideTexcoordRhumbLine=function(e,t,n,o,r,s,a){const c=n.cartesianToCartographic(o,E),l=n.cartesianToCartographic(r,I);D.setEndPoints(c,l);const u=D.surfaceDistance/s,h=Math.max(0,Math.ceil(g.CesiumMath.log2(u))),d=Math.pow(2,h),f=i.Cartesian2.distance(e,t),p=f/d,y=a;y.length=2*d;let m=0;for(let n=0;n<d;n++){const i=w(e,t,n*p,f);y[m++]=i[0],y[m++]=i[1]}return y},x.subdivideRhumbLine=function(e,t,n,i,o){const r=e.cartesianToCartographic(t,E),s=e.cartesianToCartographic(n,I),c=new l.EllipsoidRhumbLine(r,s,e),u=c.surfaceDistance/i,h=Math.max(0,Math.ceil(g.CesiumMath.log2(u))),d=Math.pow(2,h),f=c.surfaceDistance/d;a.defined(o)||(o=[]);const p=o;p.length=3*d;let y=0;for(let t=0;t<d;t++){const n=c.interpolateUsingSurfaceDistance(t*f,L),i=e.cartographicToCartesian(n,_);p[y++]=i.x,p[y++]=i.y,p[y++]=i.z}return p};const G=new o.Cartesian3,N=new o.Cartesian3,S=new o.Cartesian3,R=new o.Cartesian3;x.scaleToGeodeticHeightExtruded=function(e,t,n,i,r){i=a.defaultValue(i,c.Ellipsoid.WGS84);const s=G;let l=N;const u=S;let h=R;if(a.defined(e)&&a.defined(e.attributes)&&a.defined(e.attributes.position)){const a=e.attributes.position.values,c=a.length/2;for(let e=0;e<c;e+=3)o.Cartesian3.fromArray(a,e,u),i.geodeticSurfaceNormal(u,s),h=i.scaleToGeodeticSurface(u,h),l=o.Cartesian3.multiplyByScalar(s,n,l),l=o.Cartesian3.add(h,l,l),a[e+c]=l.x,a[e+1+c]=l.y,a[e+2+c]=l.z,r&&(h=o.Cartesian3.clone(u,h)),l=o.Cartesian3.multiplyByScalar(s,t,l),l=o.Cartesian3.add(h,l,l),a[e]=l.x,a[e+1]=l.y,a[e+2]=l.z}return e},x.polygonOutlinesFromHierarchy=function(e,t,i){const r=[],s=new v;let c,l,u;for(s.enqueue(e);0!==s.length;){const e=s.dequeue();let h=e.positions;if(t)for(u=h.length,c=0;c<u;c++)i.scaleToGeodeticSurface(h[c],h[c]);if(h=n.arrayRemoveDuplicates(h,o.Cartesian3.equalsEpsilon,!0),h.length<3)continue;const d=e.holes?e.holes.length:0;for(c=0;c<d;c++){const h=e.holes[c];let d=h.positions;if(t)for(u=d.length,l=0;l<u;++l)i.scaleToGeodeticSurface(d[l],d[l]);if(d=n.arrayRemoveDuplicates(d,o.Cartesian3.equalsEpsilon,!0),d.length<3)continue;r.push(d);let f=0;for(a.defined(h.holes)&&(f=h.holes.length),l=0;l<f;l++)s.enqueue(h.holes[l])}r.push(h)}return r};const O=new r.Cartographic;function q(e,n,i,o){if(o===t.ArcType.RHUMB)return function(e,t,n){const i=n.cartesianToCartographic(e,E),o=n.cartesianToCartographic(t,I);if(Math.sign(i.latitude)===Math.sign(o.latitude))return;D.setEndPoints(i,o);const r=D.findIntersectionWithLatitude(0,O);if(!a.defined(r))return;let s=Math.min(i.longitude,o.longitude),c=Math.max(i.longitude,o.longitude);if(Math.abs(c-s)>g.CesiumMath.PI){const e=s;s=c,c=e}return r.longitude<s||r.longitude>c?void 0:n.cartographicToCartesian(r)}(e,n,i);const r=p.IntersectionTests.lineSegmentPlane(e,n,m.Plane.ORIGIN_XY_PLANE);return a.defined(r)?i.scaleToGeodeticSurface(r,r):void 0}const k=new r.Cartographic;function z(e,t,n){const i=[];let o,r,s,c,l,u=0;for(;u<e.length;){o=e[u],r=e[(u+1)%e.length],s=g.CesiumMath.sign(o.z),c=g.CesiumMath.sign(r.z);const h=e=>t.cartesianToCartographic(e,k).longitude;if(0===s)i.push({position:u,type:s,visited:!1,next:c,theta:h(o)});else if(0!==c){if(l=q(o,r,t,n),++u,!a.defined(l))continue;e.splice(u,0,l),i.push({position:u,type:s,visited:!1,next:c,theta:h(l)})}++u}return i}function B(e,t,n,i,o,r,s){const c=[];let l=r;const u=e=>t=>t.position===e,h=[];do{const e=n[l];c.push(e);const t=i.findIndex(u(l)),o=i[t];if(!a.defined(o)){++l;continue}const{visited:d,type:f,next:p}=o;if(o.visited=!0,0===f){if(0===p){const e=i[t-(s?1:-1)];if(e?.position!==l+1){++l;continue}e.visited=!0}if(!d&&s&&p>0||r===l&&!s&&p<0){++l;continue}}if(!(s?f>=0:f<=0)){++l;continue}d||h.push(l);const g=i[t+(s?1:-1)];a.defined(g)?l=g.position:++l}while(l<n.length&&l>=0&&l!==r&&c.length<n.length);e.splice(t,o,c);for(const o of h)t=B(e,++t,n,i,0,o,!s);return t}x.splitPolygonsOnEquator=function(e,t,n,i){a.defined(i)||(i=[]),i.splice(0,0,...e),i.length=e.length;let o=0;for(;o<i.length;){const e=i[o],r=e.slice();if(e.length<3){i[o]=r,++o;continue}const s=z(r,t,n);if(r.length===e.length||s.length<=1){i[o]=r,++o;continue}s.sort(((e,t)=>e.theta-t.theta));o=B(i,o,r,s,1,0,r[0].z>=0)}return i},x.polygonsFromHierarchy=function(e,t,i,r,s,c){const l=[],u=[],h=new v;h.enqueue(e);let d=a.defined(c);for(;0!==h.length;){const e=h.dequeue();let f=e.positions;const p=e.holes;let g,y;if(r)for(y=f.length,g=0;g<y;g++)s.scaleToGeodeticSurface(f[g],f[g]);if(t||(f=n.arrayRemoveDuplicates(f,o.Cartesian3.equalsEpsilon,!0)),f.length<3)continue;let m=i(f);if(!a.defined(m))continue;const b=[];let v=C.PolygonPipeline.computeWindingOrder2D(m);if(v===C.WindingOrder.CLOCKWISE&&(m.reverse(),f=f.slice().reverse()),d){d=!1;let e=[f];if(e=c(e,e),e.length>1){for(const t of e)h.enqueue(new T(t,p));continue}}let x=f.slice();const A=a.defined(p)?p.length:0,w=[];let M;for(g=0;g<A;g++){const e=p[g];let c=e.positions;if(r)for(y=c.length,M=0;M<y;++M)s.scaleToGeodeticSurface(c[M],c[M]);if(t||(c=n.arrayRemoveDuplicates(c,o.Cartesian3.equalsEpsilon,!0)),c.length<3)continue;const l=i(c);if(!a.defined(l))continue;v=C.PolygonPipeline.computeWindingOrder2D(l),v===C.WindingOrder.CLOCKWISE&&(l.reverse(),c=c.slice().reverse()),w.push(c),b.push(x.length),x=x.concat(c),m=m.concat(l);let u=0;for(a.defined(e.holes)&&(u=e.holes.length),M=0;M<u;M++)h.enqueue(e.holes[M])}l.push({outerRing:f,holes:w}),u.push({positions:x,positions2D:m,holes:b})}return{hierarchy:l,polygons:u}};const H=new i.Cartesian2,F=new o.Cartesian3,W=new b.Quaternion,U=new y.Matrix3;x.computeBoundingRectangle=function(e,t,n,i,r){const s=b.Quaternion.fromAxisAngle(e,i,W),c=y.Matrix3.fromQuaternion(s,U);let l=Number.POSITIVE_INFINITY,u=Number.NEGATIVE_INFINITY,h=Number.POSITIVE_INFINITY,d=Number.NEGATIVE_INFINITY;const f=n.length;for(let e=0;e<f;++e){const i=o.Cartesian3.clone(n[e],F);y.Matrix3.multiplyByVector(c,i,i);const r=t(i,H);a.defined(r)&&(l=Math.min(l,r.x),u=Math.max(u,r.x),h=Math.min(h,r.y),d=Math.max(d,r.y))}return r.x=l,r.y=h,r.width=u-l,r.height=d-h,r},x.createGeometryFromPositions=function(e,n,o,r,c,l,h){let f=C.PolygonPipeline.triangulate(n.positions2D,n.holes);f.length<3&&(f=[0,1,2]);const p=n.positions,g=a.defined(o),y=g?o.positions:void 0;if(c){const e=p.length,t=new Array(3*e);let n=0;for(let i=0;i<e;i++){const e=p[i];t[n++]=e.x,t[n++]=e.y,t[n++]=e.z}const o={attributes:{position:new u.GeometryAttribute({componentDatatype:s.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:t})},indices:f,primitiveType:u.PrimitiveType.TRIANGLES};g&&(o.attributes.st=new u.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:i.Cartesian2.packArray(y)}));const r=new u.Geometry(o);return l.normal?d.GeometryPipeline.computeNormal(r):r}return h===t.ArcType.GEODESIC?C.PolygonPipeline.computeSubdivision(e,p,f,y,r):h===t.ArcType.RHUMB?C.PolygonPipeline.computeRhumbLineSubdivision(e,p,f,y,r):h===t.ArcType.NONE?C.PolygonPipeline.computeNonedivision(e,p,f,r):void 0};const V=[],Y=[],Q=new o.Cartesian3,K=new o.Cartesian3;x.computeWallGeometry=function(e,n,i,r,c,l){let d,p,y,m,C,b,T,v,A,w=e.length,M=0,P=0;const E=a.defined(n),I=E?n.positions:void 0;if(c)for(p=3*w*2,d=new Array(2*p),E&&(A=2*w*2,v=new Array(2*A)),y=0;y<w;y++)m=e[y],C=e[(y+1)%w],d[M]=d[M+p]=m.x,++M,d[M]=d[M+p]=m.y,++M,d[M]=d[M+p]=m.z,++M,d[M]=d[M+p]=C.x,++M,d[M]=d[M+p]=C.y,++M,d[M]=d[M+p]=C.z,++M,E&&(b=I[y],T=I[(y+1)%w],v[P]=v[P+A]=b.x,++P,v[P]=v[P+A]=b.y,++P,v[P]=v[P+A]=T.x,++P,v[P]=v[P+A]=T.y,++P);else{const n=g.CesiumMath.chordLength(r,i.maximumRadius);let s=0;if(l===t.ArcType.GEODESIC)for(y=0;y<w;y++)s+=x.subdivideLineCount(e[y],e[(y+1)%w],n);else if(l===t.ArcType.RHUMB)for(y=0;y<w;y++)s+=x.subdivideRhumbLineCount(i,e[y],e[(y+1)%w],n);else l==t.ArcType.NONE&&(s=w);for(p=3*(s+w),d=new Array(2*p),E&&(A=2*(s+w),v=new Array(2*A)),y=0;y<w;y++){let r,s;m=e[y],C=e[(y+1)%w],E&&(b=I[y],T=I[(y+1)%w]),l===t.ArcType.GEODESIC?(r=x.subdivideLine(m,C,n,Y),E&&(s=x.subdivideTexcoordLine(b,T,m,C,n,V))):l===t.ArcType.RHUMB?(r=x.subdivideRhumbLine(i,m,C,n,Y),E&&(s=x.subdivideTexcoordRhumbLine(b,T,i,m,C,n,V))):l===t.ArcType.NONE&&(r=new Array(3),o.Cartesian3.packArray([e[y]],r));const a=r.length;for(let e=0;e<a;++e,++M)d[M]=r[e],d[M+p]=r[e];if(d[M]=C.x,d[M+p]=C.x,++M,d[M]=C.y,d[M+p]=C.y,++M,d[M]=C.z,d[M+p]=C.z,++M,E){const e=s.length;for(let t=0;t<e;++t,++P)v[P]=s[t],v[P+A]=s[t];v[P]=T.x,v[P+A]=T.x,++P,v[P]=T.y,v[P+A]=T.y,++P}}}w=d.length;const L=f.IndexDatatype.createTypedArray(w/3,w-6*e.length);let _=0;for(w/=6,y=0;y<w;y++){const e=y,t=e+1,n=e+w,i=n+1;m=o.Cartesian3.fromArray(d,3*e,Q),C=o.Cartesian3.fromArray(d,3*t,K),o.Cartesian3.equalsEpsilon(m,C,g.CesiumMath.EPSILON10,g.CesiumMath.EPSILON10)||(L[_++]=e,L[_++]=n,L[_++]=t,L[_++]=t,L[_++]=n,L[_++]=i)}const D={attributes:new h.GeometryAttributes({position:new u.GeometryAttribute({componentDatatype:s.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:d})}),indices:L,primitiveType:u.PrimitiveType.TRIANGLES};E&&(D.attributes.st=new u.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:v}));return new u.Geometry(D)};var j=x;e.PolygonGeometryLibrary=j}));
