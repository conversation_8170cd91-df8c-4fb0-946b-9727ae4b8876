define(["./createTaskProcessorWorker","./Cartographic-dbefb6fa","./Transforms-2afbbfb5","./defaultValue-f6d5e6da","./Cartesian3-529c236c","./IndexDatatype-58eb7805","./Color-e24f904b","./Math-355606c6","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./WebGLConstants-7f557f93"],(function(o,r,t,e,i,a,n,s,l,f,p,u,h,c,d,g,C,m){"use strict";return o((function(o,s){var l=o.traceWalls;t.Transforms.localFrameToFixedFrameGenerator("north","west");for(var f=l.length,p=[],u=0,h=0,c=0;c<f;c++)if(e.defined(l[c])&&l[c].trackPoints.length>1){var d=l[c].trackPoints,g=[],C=d.length;if(C>300){for(var m=C-50,v=Math.floor(m/250)+1,y=C-50-v,T=0;T<y;T+=v){g.push({position:d[T].wcPosition});var w=r.Cartographic.fromCartesian(d[T].wcPosition),P=i.Cartesian3.fromRadians(w.longitude,w.latitude);g.push({position:P}),u+=2}for(;T<C-1;T++){g.push({position:d[T].wcPosition});w=r.Cartographic.fromCartesian(d[T].wcPosition),P=i.Cartesian3.fromRadians(w.longitude,w.latitude);g.push({position:P}),u+=2}}else for(var b=0;b<d.length;b++){g.push({position:d[b].wcPosition});w=r.Cartographic.fromCartesian(d[b].wcPosition),P=i.Cartesian3.fromRadians(w.longitude,w.latitude);g.push({position:P}),u+=2}p.push(g),h+=3*(u-2)}if(0!=p.length){var B=new t.BoundingSphere,x=a.IndexDatatype.createTypedArray(u,h),R=new Float64Array(3*u),k=new Uint8Array(4*u),F=[],M=0,W=0;for(c=0;c<p.length;c++){for(var A=l[c].color,D=(g=p[c]).length,E=0;E<D;E++){F.push(g[E].position),R[3*(W+E)]=g[E].position.x,R[3*(W+E)+1]=g[E].position.y,R[3*(W+E)+2]=g[E].position.z;var I=1-(D-E)/D*1;k[4*(W+E)]=n.Color.floatToByte(A.red),k[4*(W+E)+1]=n.Color.floatToByte(A.green),k[4*(W+E)+2]=n.Color.floatToByte(A.blue),k[4*(W+E)+3]=n.Color.floatToByte(A.alpha*I)}for(var S=0;S+3<D;S+=2)x[M++]=W+S,x[M++]=W+S+1,x[M++]=W+S+2,x[M++]=W+S+1,x[M++]=W+S+3,x[M++]=W+S+2;W+=D}return F.length>0&&(B=t.BoundingSphere.fromPoints(F)),o.traceWalls=void 0,{positionBuffer:R.buffer,colorBuffer:k.buffer,indices:x.buffer,boundingSphere:B}}}))}));
