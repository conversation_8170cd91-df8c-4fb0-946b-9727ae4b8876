define(["./defaultValue-f6d5e6da","./createTaskProcessorWorker","./DeveloperError-c85858c1","./EV_ParseLargePlane-943a091a","./Cartesian3-529c236c","./RuntimeError-9b4ce3fb","./Math-355606c6"],(function(e,a,t,r,n,s,o){"use strict";function c(a,r,n){if(!e.defined(a))throw new t.DeveloperError("wsserver undefined!");this.url=a,this.datSource=r,this.startPlay=0,this.callbackFunc=n,this.websocket=new WebSocket(a),this.websocket.onopen=this.onOpen.bind(this),this.websocket.onclose=this.onClose.bind(this),this.websocket.onmessage=this.onMessage.bind(this),this.websocket.onerror=this.onError.bind(this)}function l(e){const a=new DataView(e);return{time:a.getBigInt64(0,!0),nLen:a.getUint8(8,!0)}}c.prototype.onOpen=function(e){this.websocket.binaryType="arraybuffer",console.log("Connection open ...");const a=JSON.stringify({type:0,fileName:this.datSource});this.doSend(a)},c.prototype.doSend=function(e){this.websocket.send(e)},c.prototype.onMessage=function(e){const a=10,t=this,n=JSON.parse(e.data);if(t.dataI3NSArr=[],t.dataRTSN=[],t.dataRTKN=[],t.dataTRAJ=[],t.dataGTSPI=[],t.dataIrsm=[],t.radarTarget=[],t.RadarWM=[],t.IRSTworkMode=[],t.dataEWS=[],t.LargePlane=[],t.SafetyAlarm=[],t.CombatPower=[],t.StaticTarget=[],t.flightState=[],t.SDRState=[],n.uint8Array instanceof Array){const e=n.uint8Array;for(let n=0;n<e.length;n++){const s=new Uint8Array(e[n]).buffer,o=l(s).nLen,c=new DataView(s);let i,S;switch(new r.EV_ParseHeader(c,a).msgType){case r.EV_MsgType.NET_MSG_TSPI:95===o?i=new r.EV_ParseI3NS_Deprecated(c,a):96===o&&(i=new r.EV_ParseI3NS(c,a)),t.dataI3NSArr.push(i);break;case r.EV_MsgType.NET_MSG_FIGHTER:{const e=new r.EV_ParseFlight(c,a);t.flightState.push(e);break}case r.EV_MsgType.NET_MSG_RTSN:{const e=new r.EV_ParseRTSN(c,a);t.dataRTSN.push(e);break}case r.EV_MsgType.NET_MSG_MISSILEPARAM:S=new r.EV_ParseParam(c,a);break;case r.EV_MsgType.NET_MSG_RTKN:S=new r.EV_ParseRTKN(c,a),t.dataRTKN.push(S);break;case r.EV_MsgType.NET_MSG_WPN_TRAJ:S=new r.EV_ParseTRAJ(c,a),t.dataTRAJ.push(S);break;case r.EV_MsgType.NET_MSG_RDR_STATE:S=new r.EV_ParseRadarWM(c,a),t.RadarWM.push(S);break;case r.EV_MsgType.NET_MSG_TARGET:S=new r.EV_ParseRadarTarget(c,a),t.radarTarget.push(S);break;case r.EV_MsgType.NET_MSG_IRST:S=new r.EV_ParseIRSTworkMode(c,a),t.IRSTworkMode.push(S);break;case r.EV_MsgType.NET_MSG_IRMSL_STATE:S=new r.EV_ParseIrsmState(c,a),t.dataIrsm.push(S);break;case r.EV_MsgType.NET_MSG_EWS_STA:S=new r.EV_ParseEWS(c,a),t.dataEWS.push(S);break;case r.EV_MsgType.NET_MSG_START_STOP:S=new r.EV_ParseStartStop(c,a),console.log(S);break;case r.EV_MsgType.NET_MSG_GTSPI:S=new r.EV_ParseGTspi(c,a),t.dataGTSPI.push(S);break;case r.EV_MsgType.NET_MSG_RAD_STATE:S=new r.EV_ParseSDRState(c,a),t.SDRState.push(S);break;case r.EV_MsgType.NET_MSG_COMBATPOWER:S=new r.EV_ParseCombatPower(c,a),t.CombatPower.push(S);break;case r.EV_MsgType.NET_MSG_SAFETYALARM:S=new r.EV_ParseSafetyAlarm(c,a),t.SafetyAlarm.push(S);break;case r.EV_MsgType.NET_MSG_LARGE_PLANE:S=new r.EV_ParseLargePlane(c,a),t.LargePlane.push(S)}}0!==t.dataI3NSArr.length&&t.callbackFunc(t.dataI3NSArr),0!==t.dataRTKN.length&&t.callbackFunc(t.dataRTKN),0!==t.dataRTSN.length&&t.callbackFunc(t.dataRTSN),0!==t.dataGTSPI.length&&t.callbackFunc(t.dataGTSPI),0!==t.dataTRAJ.length&&t.callbackFunc(t.dataTRAJ),0!==t.dataIrsm.length&&t.callbackFunc(t.dataIrsm),0!==t.radarTarget.length&&t.callbackFunc(t.radarTarget),0!==t.RadarWM.length&&t.callbackFunc(t.RadarWM),0!==t.IRSTworkMode.length&&t.callbackFunc(t.IRSTworkMode),0!==t.dataEWS.length&&t.callbackFunc(t.dataEWS),0!==t.LargePlane.length&&t.callbackFunc(t.LargePlane),0!==t.SafetyAlarm.length&&t.callbackFunc(t.SafetyAlarm),0!==t.CombatPower.length&&t.callbackFunc(t.CombatPower),0!==t.StaticTarget.length&&t.callbackFunc(t.StaticTarget),0!==t.flightState.length&&t.callbackFunc(t.flightState),0!==t.SDRState.length&&t.callbackFunc(t.SDRState)}else t.callbackFunc(n)},c.prototype.onClose=function(){console.log("DISCONNECTED")},c.prototype.onError=function(e){console.log("websocket error")};let i,S=[],T=self.postMessage,E=[];function _(e){Array.isArray(e)?95==e[0].SIZE||96==e[0].SIZE?(e.forEach((function(e){e.position=function(e){let a=n.Cartesian3.fromDegrees(e.fLon,e.fLat,e.fAlt);return a}(e)})),T(e)):77==e[0].SIZE?(e.forEach((function(e){let a=e.header.sID;S.forEach((function(t){t.header.sID==a&&(e.nRedOrBlue=t.nRedOrBlue)})),e.position=function(e){let a=n.Cartesian3.fromDegrees(e.mslLongitude,e.mslLatitude,e.mslAltitude);return a}(e)})),T(e)):58==e[0].SIZE?(e.forEach((function(e){e.position=function(e){let a=n.Cartesian3.fromDegrees(e.gTspiLon,e.gTspiLat,e.gTspiAlt);return a}(e)})),T(e)):(139==e[0].SIZE||e[0].SIZE,T(e)):(E.push(e.firstTime),E.push(e.lastTime),2==E.length&&T(E))}return a((function(a){let t=a.url,r=a.datSource;if(e.defined(t))i=new c(t,r,_);else{let e=JSON.stringify({type:2,fileName:r});i.doSend(e)}}))}));
