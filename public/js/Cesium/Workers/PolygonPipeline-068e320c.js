define(["exports","./Math-355606c6","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./ComponentDatatype-ab629b88","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./EllipsoidRhumbLine-81dc828b","./GeometryAttribute-f7a0845b","./WebGLConstants-7f557f93"],(function(e,t,n,r,a,i,o,s,u,x,p){"use strict";var l={exports:{}};function y(e,t,n){n=n||2;var r,a,i,o,s,u,x,p=t&&t.length,l=p?t[0]*n:e.length,y=h(e,0,l,n,!0),f=[];if(!y||y.next===y.prev)return f;if(p&&(y=function(e,t,n,r){var a,i,o,s=[];for(a=0,i=t.length;a<i;a++)(o=h(e,t[a]*r,a<i-1?t[a+1]*r:e.length,r,!1))===o.next&&(o.steiner=!0),s.push(E(o));for(s.sort(g),a=0;a<s.length;a++)n=w(s[a],n);return n}(e,t,y,n)),e.length>80*n){r=i=e[0],a=o=e[1];for(var m=n;m<l;m+=n)(s=e[m])<r&&(r=s),(u=e[m+1])<a&&(a=u),s>i&&(i=s),u>o&&(o=u);x=0!==(x=Math.max(i-r,o-a))?32767/x:0}return c(y,f,n,r,a,x,0),f}function h(e,t,n,r,a){var i,o;if(a===W(e,t,n,r)>0)for(i=t;i<n;i+=r)o=O(i,e[i],e[i+1],o);else for(i=n-r;i>=t;i-=r)o=O(i,e[i],e[i+1],o);return o&&Z(o,o.next)&&(B(o),o=o.next),o}function f(e,t){if(!e)return e;t||(t=e);var n,r=e;do{if(n=!1,r.steiner||!Z(r,r.next)&&0!==D(r.prev,r,r.next))r=r.next;else{if(B(r),(r=t=r.prev)===r.next)break;n=!0}}while(n||r!==t);return t}function c(e,t,n,r,a,i,o){if(e){!o&&i&&function(e,t,n,r){var a=e;do{0===a.z&&(a.z=b(a.x,a.y,t,n,r)),a.prevZ=a.prev,a.nextZ=a.next,a=a.next}while(a!==e);a.prevZ.nextZ=null,a.prevZ=null,function(e){var t,n,r,a,i,o,s,u,x=1;do{for(n=e,e=null,i=null,o=0;n;){for(o++,r=n,s=0,t=0;t<x&&(s++,r=r.nextZ);t++);for(u=x;s>0||u>0&&r;)0!==s&&(0===u||!r||n.z<=r.z)?(a=n,n=n.nextZ,s--):(a=r,r=r.nextZ,u--),i?i.nextZ=a:e=a,a.prevZ=i,i=a;n=r}i.nextZ=null,x*=2}while(o>1)}(a)}(e,r,a,i);for(var s,u,x=e;e.prev!==e.next;)if(s=e.prev,u=e.next,i?v(e,r,a,i):m(e))t.push(s.i/n|0),t.push(e.i/n|0),t.push(u.i/n|0),B(e),e=u.next,x=u.next;else if((e=u)===x){o?1===o?c(e=d(f(e),t,n),t,n,r,a,i,2):2===o&&C(e,t,n,r,a,i):c(f(e),t,n,r,a,i,1);break}}}function m(e){var t=e.prev,n=e,r=e.next;if(D(t,n,r)>=0)return!1;for(var a=t.x,i=n.x,o=r.x,s=t.y,u=n.y,x=r.y,p=a<i?a<o?a:o:i<o?i:o,l=s<u?s<x?s:x:u<x?u:x,y=a>i?a>o?a:o:i>o?i:o,h=s>u?s>x?s:x:u>x?u:x,f=r.next;f!==t;){if(f.x>=p&&f.x<=y&&f.y>=l&&f.y<=h&&S(a,s,i,u,o,x,f.x,f.y)&&D(f.prev,f,f.next)>=0)return!1;f=f.next}return!0}function v(e,t,n,r){var a=e.prev,i=e,o=e.next;if(D(a,i,o)>=0)return!1;for(var s=a.x,u=i.x,x=o.x,p=a.y,l=i.y,y=o.y,h=s<u?s<x?s:x:u<x?u:x,f=p<l?p<y?p:y:l<y?l:y,c=s>u?s>x?s:x:u>x?u:x,m=p>l?p>y?p:y:l>y?l:y,v=b(h,f,t,n,r),d=b(c,m,t,n,r),C=e.prevZ,g=e.nextZ;C&&C.z>=v&&g&&g.z<=d;){if(C.x>=h&&C.x<=c&&C.y>=f&&C.y<=m&&C!==a&&C!==o&&S(s,p,u,l,x,y,C.x,C.y)&&D(C.prev,C,C.next)>=0)return!1;if(C=C.prevZ,g.x>=h&&g.x<=c&&g.y>=f&&g.y<=m&&g!==a&&g!==o&&S(s,p,u,l,x,y,g.x,g.y)&&D(g.prev,g,g.next)>=0)return!1;g=g.nextZ}for(;C&&C.z>=v;){if(C.x>=h&&C.x<=c&&C.y>=f&&C.y<=m&&C!==a&&C!==o&&S(s,p,u,l,x,y,C.x,C.y)&&D(C.prev,C,C.next)>=0)return!1;C=C.prevZ}for(;g&&g.z<=d;){if(g.x>=h&&g.x<=c&&g.y>=f&&g.y<=m&&g!==a&&g!==o&&S(s,p,u,l,x,y,g.x,g.y)&&D(g.prev,g,g.next)>=0)return!1;g=g.nextZ}return!0}function d(e,t,n){var r=e;do{var a=r.prev,i=r.next.next;!Z(a,i)&&R(a,r,r.next,i)&&G(a,i)&&G(i,a)&&(t.push(a.i/n|0),t.push(r.i/n|0),t.push(i.i/n|0),B(r),B(r.next),r=e=i),r=r.next}while(r!==e);return f(r)}function C(e,t,n,r,a,i){var o=e;do{for(var s=o.next.next;s!==o.prev;){if(o.i!==s.i&&M(o,s)){var u=T(o,s);return o=f(o,o.next),u=f(u,u.next),c(o,t,n,r,a,i,0),void c(u,t,n,r,a,i,0)}s=s.next}o=o.next}while(o!==e)}function g(e,t){return e.x-t.x}function w(e,t){var n=function(e,t){var n,r=t,a=e.x,i=e.y,o=-1/0;do{if(i<=r.y&&i>=r.next.y&&r.next.y!==r.y){var s=r.x+(i-r.y)*(r.next.x-r.x)/(r.next.y-r.y);if(s<=a&&s>o&&(o=s,n=r.x<r.next.x?r:r.next,s===a))return n}r=r.next}while(r!==t);if(!n)return null;var u,x=n,p=n.x,l=n.y,y=1/0;r=n;do{a>=r.x&&r.x>=p&&a!==r.x&&S(i<l?a:o,i,p,l,i<l?o:a,i,r.x,r.y)&&(u=Math.abs(i-r.y)/(a-r.x),G(r,e)&&(u<y||u===y&&(r.x>n.x||r.x===n.x&&A(n,r)))&&(n=r,y=u)),r=r.next}while(r!==x);return n}(e,t);if(!n)return t;var r=T(n,e);return f(r,r.next),f(n,n.next)}function A(e,t){return D(e.prev,e,t.prev)<0&&D(t.next,e,e.next)<0}function b(e,t,n,r,a){return(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=(e-n)*a|0)|e<<8))|e<<4))|e<<2))|e<<1))|(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=(t-r)*a|0)|t<<8))|t<<4))|t<<2))|t<<1))<<1}function E(e){var t=e,n=e;do{(t.x<n.x||t.x===n.x&&t.y<n.y)&&(n=t),t=t.next}while(t!==e);return n}function S(e,t,n,r,a,i,o,s){return(a-o)*(t-s)>=(e-o)*(i-s)&&(e-o)*(r-s)>=(n-o)*(t-s)&&(n-o)*(i-s)>=(a-o)*(r-s)}function M(e,t){return e.next.i!==t.i&&e.prev.i!==t.i&&!function(e,t){var n=e;do{if(n.i!==e.i&&n.next.i!==e.i&&n.i!==t.i&&n.next.i!==t.i&&R(n,n.next,e,t))return!0;n=n.next}while(n!==e);return!1}(e,t)&&(G(e,t)&&G(t,e)&&function(e,t){var n=e,r=!1,a=(e.x+t.x)/2,i=(e.y+t.y)/2;do{n.y>i!=n.next.y>i&&n.next.y!==n.y&&a<(n.next.x-n.x)*(i-n.y)/(n.next.y-n.y)+n.x&&(r=!r),n=n.next}while(n!==e);return r}(e,t)&&(D(e.prev,e,t.prev)||D(e,t.prev,t))||Z(e,t)&&D(e.prev,e,e.next)>0&&D(t.prev,t,t.next)>0)}function D(e,t,n){return(t.y-e.y)*(n.x-t.x)-(t.x-e.x)*(n.y-t.y)}function Z(e,t){return e.x===t.x&&e.y===t.y}function R(e,t,n,r){var a=z(D(e,t,n)),i=z(D(e,t,r)),o=z(D(n,r,e)),s=z(D(n,r,t));return a!==i&&o!==s||(!(0!==a||!L(e,n,t))||(!(0!==i||!L(e,r,t))||(!(0!==o||!L(n,e,r))||!(0!==s||!L(n,t,r)))))}function L(e,t,n){return t.x<=Math.max(e.x,n.x)&&t.x>=Math.min(e.x,n.x)&&t.y<=Math.max(e.y,n.y)&&t.y>=Math.min(e.y,n.y)}function z(e){return e>0?1:e<0?-1:0}function G(e,t){return D(e.prev,e,e.next)<0?D(e,t,e.next)>=0&&D(e,e.prev,t)>=0:D(e,t,e.prev)<0||D(e,e.next,t)<0}function T(e,t){var n=new P(e.i,e.x,e.y),r=new P(t.i,t.x,t.y),a=e.next,i=t.prev;return e.next=t,t.prev=e,n.next=a,a.prev=n,r.next=n,n.prev=r,i.next=r,r.prev=i,r}function O(e,t,n,r){var a=new P(e,t,n);return r?(a.next=r.next,a.prev=r,r.next.prev=a,r.next=a):(a.prev=a,a.next=a),a}function B(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function P(e,t,n){this.i=e,this.x=t,this.y=n,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}function W(e,t,n,r){for(var a=0,i=t,o=n-r;i<n;i+=r)a+=(e[o]-e[i])*(e[i+1]+e[o+1]),o=i;return a}l.exports=y,l.exports.default=y,y.deviation=function(e,t,n,r){var a=t&&t.length,i=a?t[0]*n:e.length,o=Math.abs(W(e,0,i,n));if(a)for(var s=0,u=t.length;s<u;s++){var x=t[s]*n,p=s<u-1?t[s+1]*n:e.length;o-=Math.abs(W(e,x,p,n))}var l=0;for(s=0;s<r.length;s+=3){var y=r[s]*n,h=r[s+1]*n,f=r[s+2]*n;l+=Math.abs((e[y]-e[f])*(e[h+1]-e[y+1])-(e[y]-e[h])*(e[f+1]-e[y+1]))}return 0===o&&0===l?0:Math.abs((l-o)/o)},y.flatten=function(e){for(var t=e[0][0].length,n={vertices:[],holes:[],dimensions:t},r=0,a=0;a<e.length;a++){for(var i=0;i<e[a].length;i++)for(var o=0;o<t;o++)n.vertices.push(e[a][i][o]);a>0&&(r+=e[a-1].length,n.holes.push(r))}return n};var I=l.exports,$=t.getDefaultExportFromCjs(I);const N={CLOCKWISE:p.WebGLConstants.CW,COUNTER_CLOCKWISE:p.WebGLConstants.CCW,validate:function(e){return e===N.CLOCKWISE||e===N.COUNTER_CLOCKWISE}};var U=Object.freeze(N);const _=new r.Cartesian3,V=new r.Cartesian3,F={computeArea2D:function(e){const t=e.length;let n=0;for(let r=t-1,a=0;a<t;r=a++){const t=e[r],i=e[a];n+=t.x*i.y-i.x*t.y}return.5*n},computeWindingOrder2D:function(e){return F.computeArea2D(e)>0?U.COUNTER_CLOCKWISE:U.CLOCKWISE},triangulate:function(e,t){const r=n.Cartesian2.packArray(e);return $(r,t,2)}},K=new r.Cartesian3,k=new r.Cartesian3,q=new r.Cartesian3,j=new r.Cartesian3,H=new r.Cartesian3,J=new r.Cartesian3,Q=new r.Cartesian3,X=new n.Cartesian2,Y=new n.Cartesian2,ee=new n.Cartesian2,te=new n.Cartesian2;F.computeSubdivision=function(e,a,s,u,p){p=o.defaultValue(p,t.CesiumMath.RADIANS_PER_DEGREE);const l=o.defined(u),y=s.slice(0);let h;const f=a.length,c=new Array(3*f),m=new Array(2*f);let v=0,d=0;for(h=0;h<f;h++){const e=a[h];if(c[v++]=e.x,c[v++]=e.y,c[v++]=e.z,l){const e=u[h];m[d++]=e.x,m[d++]=e.y}}const C=[],g={},w=e.maximumRadius,A=t.CesiumMath.chordLength(p,w),b=A*A;for(;y.length>0;){const e=y.pop(),t=y.pop(),a=y.pop(),i=r.Cartesian3.fromArray(c,3*a,K),s=r.Cartesian3.fromArray(c,3*t,k),u=r.Cartesian3.fromArray(c,3*e,q);let x,p,f;l&&(x=n.Cartesian2.fromArray(m,2*a,X),p=n.Cartesian2.fromArray(m,2*t,Y),f=n.Cartesian2.fromArray(m,2*e,ee));const v=r.Cartesian3.multiplyByScalar(r.Cartesian3.normalize(i,j),w,j),d=r.Cartesian3.multiplyByScalar(r.Cartesian3.normalize(s,H),w,H),A=r.Cartesian3.multiplyByScalar(r.Cartesian3.normalize(u,J),w,J),E=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(v,d,Q)),S=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(d,A,Q)),M=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(A,v,Q)),D=Math.max(E,S,M);let Z,R,L;D>b?E===D?(Z=`${Math.min(a,t)} ${Math.max(a,t)}`,h=g[Z],o.defined(h)||(R=r.Cartesian3.add(i,s,Q),r.Cartesian3.multiplyByScalar(R,.5,R),c.push(R.x,R.y,R.z),h=c.length/3-1,g[Z]=h,l&&(L=n.Cartesian2.add(x,p,te),n.Cartesian2.multiplyByScalar(L,.5,L),m.push(L.x,L.y))),y.push(a,h,e),y.push(h,t,e)):S===D?(Z=`${Math.min(t,e)} ${Math.max(t,e)}`,h=g[Z],o.defined(h)||(R=r.Cartesian3.add(s,u,Q),r.Cartesian3.multiplyByScalar(R,.5,R),c.push(R.x,R.y,R.z),h=c.length/3-1,g[Z]=h,l&&(L=n.Cartesian2.add(p,f,te),n.Cartesian2.multiplyByScalar(L,.5,L),m.push(L.x,L.y))),y.push(t,h,a),y.push(h,e,a)):M===D&&(Z=`${Math.min(e,a)} ${Math.max(e,a)}`,h=g[Z],o.defined(h)||(R=r.Cartesian3.add(u,i,Q),r.Cartesian3.multiplyByScalar(R,.5,R),c.push(R.x,R.y,R.z),h=c.length/3-1,g[Z]=h,l&&(L=n.Cartesian2.add(f,x,te),n.Cartesian2.multiplyByScalar(L,.5,L),m.push(L.x,L.y))),y.push(e,h,t),y.push(h,a,t)):(C.push(a),C.push(t),C.push(e))}const E={attributes:{position:new x.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})},indices:C,primitiveType:x.PrimitiveType.TRIANGLES};return l&&(E.attributes.st=new x.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:m})),new x.Geometry(E)};const ne=new a.Cartographic,re=new a.Cartographic,ae=new a.Cartographic,ie=new a.Cartographic;F.computeRhumbLineSubdivision=function(e,a,s,p,l){l=o.defaultValue(l,t.CesiumMath.RADIANS_PER_DEGREE);const y=o.defined(p),h=s.slice(0);let f;const c=a.length,m=new Array(3*c),v=new Array(2*c);let d=0,C=0;for(f=0;f<c;f++){const e=a[f];if(m[d++]=e.x,m[d++]=e.y,m[d++]=e.z,y){const e=p[f];v[C++]=e.x,v[C++]=e.y}}const g=[],w={},A=e.maximumRadius,b=t.CesiumMath.chordLength(l,A),E=new u.EllipsoidRhumbLine(void 0,void 0,e),S=new u.EllipsoidRhumbLine(void 0,void 0,e),M=new u.EllipsoidRhumbLine(void 0,void 0,e);for(;h.length>0;){const t=h.pop(),a=h.pop(),i=h.pop(),s=r.Cartesian3.fromArray(m,3*i,K),u=r.Cartesian3.fromArray(m,3*a,k),x=r.Cartesian3.fromArray(m,3*t,q);let p,l,c;y&&(p=n.Cartesian2.fromArray(v,2*i,X),l=n.Cartesian2.fromArray(v,2*a,Y),c=n.Cartesian2.fromArray(v,2*t,ee));const d=e.cartesianToCartographic(s,ne),C=e.cartesianToCartographic(u,re),A=e.cartesianToCartographic(x,ae);E.setEndPoints(d,C);const D=E.surfaceDistance;S.setEndPoints(C,A);const Z=S.surfaceDistance;M.setEndPoints(A,d);const R=M.surfaceDistance,L=Math.max(D,Z,R);let z,G,T,O,B;L>b?D===L?(z=`${Math.min(i,a)} ${Math.max(i,a)}`,f=w[z],o.defined(f)||(G=E.interpolateUsingFraction(.5,ie),T=.5*(d.height+C.height),O=r.Cartesian3.fromRadians(G.longitude,G.latitude,T,e,Q),m.push(O.x,O.y,O.z),f=m.length/3-1,w[z]=f,y&&(B=n.Cartesian2.add(p,l,te),n.Cartesian2.multiplyByScalar(B,.5,B),v.push(B.x,B.y))),h.push(i,f,t),h.push(f,a,t)):Z===L?(z=`${Math.min(a,t)} ${Math.max(a,t)}`,f=w[z],o.defined(f)||(G=S.interpolateUsingFraction(.5,ie),T=.5*(C.height+A.height),O=r.Cartesian3.fromRadians(G.longitude,G.latitude,T,e,Q),m.push(O.x,O.y,O.z),f=m.length/3-1,w[z]=f,y&&(B=n.Cartesian2.add(l,c,te),n.Cartesian2.multiplyByScalar(B,.5,B),v.push(B.x,B.y))),h.push(a,f,i),h.push(f,t,i)):R===L&&(z=`${Math.min(t,i)} ${Math.max(t,i)}`,f=w[z],o.defined(f)||(G=M.interpolateUsingFraction(.5,ie),T=.5*(A.height+d.height),O=r.Cartesian3.fromRadians(G.longitude,G.latitude,T,e,Q),m.push(O.x,O.y,O.z),f=m.length/3-1,w[z]=f,y&&(B=n.Cartesian2.add(c,p,te),n.Cartesian2.multiplyByScalar(B,.5,B),v.push(B.x,B.y))),h.push(t,f,a),h.push(f,i,a)):(g.push(i),g.push(a),g.push(t))}const D={attributes:{position:new x.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:m})},indices:g,primitiveType:x.PrimitiveType.TRIANGLES};return y&&(D.attributes.st=new x.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:v})),new x.Geometry(D)},F.computeNonedivision=function(e,n,a,s){s=o.defaultValue(s,t.CesiumMath.RADIANS_PER_DEGREE);let u,p=a.slice(0),l=n.length,y=new Array(3*l),h=0;for(u=0;u<l;u++){let e=n[u];y[h++]=e.x,y[h++]=e.y,y[h++]=e.z}let f=[];for(e.maximumRadius;p.length>0;){let e=p.pop(),t=p.pop(),n=p.pop();r.Cartesian3.fromArray(y,3*n,K),r.Cartesian3.fromArray(y,3*t,k),r.Cartesian3.fromArray(y,3*e,q),f.push(n),f.push(t),f.push(e)}return new x.Geometry({attributes:{position:new x.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:y})},indices:f,primitiveType:x.PrimitiveType.TRIANGLES})},F.scaleToGeodeticHeight=function(e,t,n,a){n=o.defaultValue(n,s.Ellipsoid.WGS84);let i=_,u=V;if(t=o.defaultValue(t,0),a=o.defaultValue(a,!0),o.defined(e)){const o=e.length;for(let s=0;s<o;s+=3)r.Cartesian3.fromArray(e,s,u),a&&(u=n.scaleToGeodeticSurface(u,u)),0!==t&&(i=n.geodeticSurfaceNormal(u,i),r.Cartesian3.multiplyByScalar(i,t,i),r.Cartesian3.add(u,i,u)),e[s]=u.x,e[s+1]=u.y,e[s+2]=u.z}return e};var oe=F;e.PolygonPipeline=oe,e.WindingOrder=U}));
