define(["exports","./Cartesian3-529c236c","./defaultValue-f6d5e6da","./Math-355606c6"],(function(t,n,e,r){"use strict";function a(t,n,r,a,o,i,u,s,c){this[0]=e.defaultValue(t,0),this[1]=e.defaultValue(a,0),this[2]=e.defaultValue(u,0),this[3]=e.defaultValue(n,0),this[4]=e.defaultValue(o,0),this[5]=e.defaultValue(s,0),this[6]=e.defaultValue(r,0),this[7]=e.defaultValue(i,0),this[8]=e.defaultValue(c,0)}a.packedLength=9,a.pack=function(t,n,r){return r=e.defaultValue(r,0),n[r++]=t[0],n[r++]=t[1],n[r++]=t[2],n[r++]=t[3],n[r++]=t[4],n[r++]=t[5],n[r++]=t[6],n[r++]=t[7],n[r++]=t[8],n},a.unpack=function(t,n,r){return n=e.defaultValue(n,0),e.defined(r)||(r=new a),r[0]=t[n++],r[1]=t[n++],r[2]=t[n++],r[3]=t[n++],r[4]=t[n++],r[5]=t[n++],r[6]=t[n++],r[7]=t[n++],r[8]=t[n++],r},a.packArray=function(t,n){const r=t.length,o=9*r;e.defined(n)?(Array.isArray(n)||n.length===o)&&n.length!==o&&(n.length=o):n=new Array(o);for(let e=0;e<r;++e)a.pack(t[e],n,9*e);return n},a.unpackArray=function(t,n){const r=t.length;e.defined(n)?n.length=r/9:n=new Array(r/9);for(let e=0;e<r;e+=9){const r=e/9;n[r]=a.unpack(t,e,n[r])}return n},a.clone=function(t,n){if(e.defined(t))return e.defined(n)?(n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n):new a(t[0],t[3],t[6],t[1],t[4],t[7],t[2],t[5],t[8])},a.fromArray=a.unpack,a.fromColumnMajorArray=function(t,n){return a.clone(t,n)},a.fromRowMajorArray=function(t,n){return e.defined(n)?(n[0]=t[0],n[1]=t[3],n[2]=t[6],n[3]=t[1],n[4]=t[4],n[5]=t[7],n[6]=t[2],n[7]=t[5],n[8]=t[8],n):new a(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8])},a.fromQuaternion=function(t,n){const r=t.x*t.x,o=t.x*t.y,i=t.x*t.z,u=t.x*t.w,s=t.y*t.y,c=t.y*t.z,l=t.y*t.w,f=t.z*t.z,d=t.z*t.w,h=t.w*t.w,y=r-s-f+h,m=2*(o-d),M=2*(i+l),x=2*(o+d),g=-r+s-f+h,p=2*(c-u),w=2*(i-l),z=2*(c+u),C=-r-s+f+h;return e.defined(n)?(n[0]=y,n[1]=x,n[2]=w,n[3]=m,n[4]=g,n[5]=z,n[6]=M,n[7]=p,n[8]=C,n):new a(y,m,M,x,g,p,w,z,C)},a.fromHeadingPitchRoll=function(t,n){const r=Math.cos(-t.pitch),o=Math.cos(-t.heading),i=Math.cos(t.roll),u=Math.sin(-t.pitch),s=Math.sin(-t.heading),c=Math.sin(t.roll),l=r*o,f=-i*s+c*u*o,d=c*s+i*u*o,h=r*s,y=i*o+c*u*s,m=-c*o+i*u*s,M=-u,x=c*r,g=i*r;return e.defined(n)?(n[0]=l,n[1]=h,n[2]=M,n[3]=f,n[4]=y,n[5]=x,n[6]=d,n[7]=m,n[8]=g,n):new a(l,f,d,h,y,m,M,x,g)},a.fromScale=function(t,n){return e.defined(n)?(n[0]=t.x,n[1]=0,n[2]=0,n[3]=0,n[4]=t.y,n[5]=0,n[6]=0,n[7]=0,n[8]=t.z,n):new a(t.x,0,0,0,t.y,0,0,0,t.z)},a.fromUniformScale=function(t,n){return e.defined(n)?(n[0]=t,n[1]=0,n[2]=0,n[3]=0,n[4]=t,n[5]=0,n[6]=0,n[7]=0,n[8]=t,n):new a(t,0,0,0,t,0,0,0,t)},a.fromCrossProduct=function(t,n){return e.defined(n)?(n[0]=0,n[1]=t.z,n[2]=-t.y,n[3]=-t.z,n[4]=0,n[5]=t.x,n[6]=t.y,n[7]=-t.x,n[8]=0,n):new a(0,-t.z,t.y,t.z,0,-t.x,-t.y,t.x,0)},a.fromRotationX=function(t,n){const r=Math.cos(t),o=Math.sin(t);return e.defined(n)?(n[0]=1,n[1]=0,n[2]=0,n[3]=0,n[4]=r,n[5]=o,n[6]=0,n[7]=-o,n[8]=r,n):new a(1,0,0,0,r,-o,0,o,r)},a.fromRotationY=function(t,n){const r=Math.cos(t),o=Math.sin(t);return e.defined(n)?(n[0]=r,n[1]=0,n[2]=-o,n[3]=0,n[4]=1,n[5]=0,n[6]=o,n[7]=0,n[8]=r,n):new a(r,0,o,0,1,0,-o,0,r)},a.fromRotationZ=function(t,n){const r=Math.cos(t),o=Math.sin(t);return e.defined(n)?(n[0]=r,n[1]=o,n[2]=0,n[3]=-o,n[4]=r,n[5]=0,n[6]=0,n[7]=0,n[8]=1,n):new a(r,-o,0,o,r,0,0,0,1)},a.toArray=function(t,n){return e.defined(n)?(n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n):[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8]]},a.getElementIndex=function(t,n){return 3*t+n},a.getColumn=function(t,n,e){const r=3*n,a=t[r],o=t[r+1],i=t[r+2];return e.x=a,e.y=o,e.z=i,e},a.setColumn=function(t,n,e,r){const o=3*n;return(r=a.clone(t,r))[o]=e.x,r[o+1]=e.y,r[o+2]=e.z,r},a.getRow=function(t,n,e){const r=t[n],a=t[n+3],o=t[n+6];return e.x=r,e.y=a,e.z=o,e},a.setRow=function(t,n,e,r){return(r=a.clone(t,r))[n]=e.x,r[n+3]=e.y,r[n+6]=e.z,r};const o=new n.Cartesian3;a.setScale=function(t,n,e){const r=a.getScale(t,o),i=n.x/r.x,u=n.y/r.y,s=n.z/r.z;return e[0]=t[0]*i,e[1]=t[1]*i,e[2]=t[2]*i,e[3]=t[3]*u,e[4]=t[4]*u,e[5]=t[5]*u,e[6]=t[6]*s,e[7]=t[7]*s,e[8]=t[8]*s,e};const i=new n.Cartesian3;a.setUniformScale=function(t,n,e){const r=a.getScale(t,i),o=n/r.x,u=n/r.y,s=n/r.z;return e[0]=t[0]*o,e[1]=t[1]*o,e[2]=t[2]*o,e[3]=t[3]*u,e[4]=t[4]*u,e[5]=t[5]*u,e[6]=t[6]*s,e[7]=t[7]*s,e[8]=t[8]*s,e};const u=new n.Cartesian3;a.getScale=function(t,e){return e.x=n.Cartesian3.magnitude(n.Cartesian3.fromElements(t[0],t[1],t[2],u)),e.y=n.Cartesian3.magnitude(n.Cartesian3.fromElements(t[3],t[4],t[5],u)),e.z=n.Cartesian3.magnitude(n.Cartesian3.fromElements(t[6],t[7],t[8],u)),e};const s=new n.Cartesian3;a.getMaximumScale=function(t){return a.getScale(t,s),n.Cartesian3.maximumComponent(s)};const c=new n.Cartesian3;a.setRotation=function(t,n,e){const r=a.getScale(t,c);return e[0]=n[0]*r.x,e[1]=n[1]*r.x,e[2]=n[2]*r.x,e[3]=n[3]*r.y,e[4]=n[4]*r.y,e[5]=n[5]*r.y,e[6]=n[6]*r.z,e[7]=n[7]*r.z,e[8]=n[8]*r.z,e};const l=new n.Cartesian3;a.getRotation=function(t,n){const e=a.getScale(t,l);return n[0]=t[0]/e.x,n[1]=t[1]/e.x,n[2]=t[2]/e.x,n[3]=t[3]/e.y,n[4]=t[4]/e.y,n[5]=t[5]/e.y,n[6]=t[6]/e.z,n[7]=t[7]/e.z,n[8]=t[8]/e.z,n},a.multiply=function(t,n,e){const r=t[0]*n[0]+t[3]*n[1]+t[6]*n[2],a=t[1]*n[0]+t[4]*n[1]+t[7]*n[2],o=t[2]*n[0]+t[5]*n[1]+t[8]*n[2],i=t[0]*n[3]+t[3]*n[4]+t[6]*n[5],u=t[1]*n[3]+t[4]*n[4]+t[7]*n[5],s=t[2]*n[3]+t[5]*n[4]+t[8]*n[5],c=t[0]*n[6]+t[3]*n[7]+t[6]*n[8],l=t[1]*n[6]+t[4]*n[7]+t[7]*n[8],f=t[2]*n[6]+t[5]*n[7]+t[8]*n[8];return e[0]=r,e[1]=a,e[2]=o,e[3]=i,e[4]=u,e[5]=s,e[6]=c,e[7]=l,e[8]=f,e},a.add=function(t,n,e){return e[0]=t[0]+n[0],e[1]=t[1]+n[1],e[2]=t[2]+n[2],e[3]=t[3]+n[3],e[4]=t[4]+n[4],e[5]=t[5]+n[5],e[6]=t[6]+n[6],e[7]=t[7]+n[7],e[8]=t[8]+n[8],e},a.subtract=function(t,n,e){return e[0]=t[0]-n[0],e[1]=t[1]-n[1],e[2]=t[2]-n[2],e[3]=t[3]-n[3],e[4]=t[4]-n[4],e[5]=t[5]-n[5],e[6]=t[6]-n[6],e[7]=t[7]-n[7],e[8]=t[8]-n[8],e},a.multiplyByVector=function(t,n,e){const r=n.x,a=n.y,o=n.z,i=t[0]*r+t[3]*a+t[6]*o,u=t[1]*r+t[4]*a+t[7]*o,s=t[2]*r+t[5]*a+t[8]*o;return e.x=i,e.y=u,e.z=s,e},a.multiplyByScalar=function(t,n,e){return e[0]=t[0]*n,e[1]=t[1]*n,e[2]=t[2]*n,e[3]=t[3]*n,e[4]=t[4]*n,e[5]=t[5]*n,e[6]=t[6]*n,e[7]=t[7]*n,e[8]=t[8]*n,e},a.multiplyByScale=function(t,n,e){return e[0]=t[0]*n.x,e[1]=t[1]*n.x,e[2]=t[2]*n.x,e[3]=t[3]*n.y,e[4]=t[4]*n.y,e[5]=t[5]*n.y,e[6]=t[6]*n.z,e[7]=t[7]*n.z,e[8]=t[8]*n.z,e},a.multiplyByUniformScale=function(t,n,e){return e[0]=t[0]*n,e[1]=t[1]*n,e[2]=t[2]*n,e[3]=t[3]*n,e[4]=t[4]*n,e[5]=t[5]*n,e[6]=t[6]*n,e[7]=t[7]*n,e[8]=t[8]*n,e},a.negate=function(t,n){return n[0]=-t[0],n[1]=-t[1],n[2]=-t[2],n[3]=-t[3],n[4]=-t[4],n[5]=-t[5],n[6]=-t[6],n[7]=-t[7],n[8]=-t[8],n},a.transpose=function(t,n){const e=t[0],r=t[3],a=t[6],o=t[1],i=t[4],u=t[7],s=t[2],c=t[5],l=t[8];return n[0]=e,n[1]=r,n[2]=a,n[3]=o,n[4]=i,n[5]=u,n[6]=s,n[7]=c,n[8]=l,n};const f=[1,0,0],d=[2,2,1];function h(t){let n=0;for(let e=0;e<3;++e){const r=t[a.getElementIndex(d[e],f[e])];n+=2*r*r}return Math.sqrt(n)}function y(t,n){const e=r.CesiumMath.EPSILON15;let o=0,i=1;for(let n=0;n<3;++n){const e=Math.abs(t[a.getElementIndex(d[n],f[n])]);e>o&&(i=n,o=e)}let u=1,s=0;const c=f[i],l=d[i];if(Math.abs(t[a.getElementIndex(l,c)])>e){const n=(t[a.getElementIndex(l,l)]-t[a.getElementIndex(c,c)])/2/t[a.getElementIndex(l,c)];let e;e=n<0?-1/(-n+Math.sqrt(1+n*n)):1/(n+Math.sqrt(1+n*n)),u=1/Math.sqrt(1+e*e),s=e*u}return(n=a.clone(a.IDENTITY,n))[a.getElementIndex(c,c)]=n[a.getElementIndex(l,l)]=u,n[a.getElementIndex(l,c)]=s,n[a.getElementIndex(c,l)]=-s,n}const m=new a,M=new a;a.computeEigenDecomposition=function(t,n){const o=r.CesiumMath.EPSILON20;let i=0,u=0;e.defined(n)||(n={});const s=n.unitary=a.clone(a.IDENTITY,n.unitary),c=n.diagonal=a.clone(t,n.diagonal),l=o*function(t){let n=0;for(let e=0;e<9;++e){const r=t[e];n+=r*r}return Math.sqrt(n)}(c);for(;u<10&&h(c)>l;)y(c,m),a.transpose(m,M),a.multiply(c,m,c),a.multiply(M,c,c),a.multiply(s,m,s),++i>2&&(++u,i=0);return n},a.abs=function(t,n){return n[0]=Math.abs(t[0]),n[1]=Math.abs(t[1]),n[2]=Math.abs(t[2]),n[3]=Math.abs(t[3]),n[4]=Math.abs(t[4]),n[5]=Math.abs(t[5]),n[6]=Math.abs(t[6]),n[7]=Math.abs(t[7]),n[8]=Math.abs(t[8]),n},a.determinant=function(t){const n=t[0],e=t[3],r=t[6],a=t[1],o=t[4],i=t[7],u=t[2],s=t[5],c=t[8];return n*(o*c-s*i)+a*(s*r-e*c)+u*(e*i-o*r)},a.inverse=function(t,n){const e=t[0],r=t[1],o=t[2],i=t[3],u=t[4],s=t[5],c=t[6],l=t[7],f=t[8],d=a.determinant(t);n[0]=u*f-l*s,n[1]=l*o-r*f,n[2]=r*s-u*o,n[3]=c*s-i*f,n[4]=e*f-c*o,n[5]=i*o-e*s,n[6]=i*l-c*u,n[7]=c*r-e*l,n[8]=e*u-i*r;const h=1/d;return a.multiplyByScalar(n,h,n)};const x=new a;a.inverseTranspose=function(t,n){return a.inverse(a.transpose(t,x),n)},a.equals=function(t,n){return t===n||e.defined(t)&&e.defined(n)&&t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]&&t[6]===n[6]&&t[7]===n[7]&&t[8]===n[8]},a.equalsEpsilon=function(t,n,r){return r=e.defaultValue(r,0),t===n||e.defined(t)&&e.defined(n)&&Math.abs(t[0]-n[0])<=r&&Math.abs(t[1]-n[1])<=r&&Math.abs(t[2]-n[2])<=r&&Math.abs(t[3]-n[3])<=r&&Math.abs(t[4]-n[4])<=r&&Math.abs(t[5]-n[5])<=r&&Math.abs(t[6]-n[6])<=r&&Math.abs(t[7]-n[7])<=r&&Math.abs(t[8]-n[8])<=r},a.IDENTITY=Object.freeze(new a(1,0,0,0,1,0,0,0,1)),a.ZERO=Object.freeze(new a(0,0,0,0,0,0,0,0,0)),a.COLUMN0ROW0=0,a.COLUMN0ROW1=1,a.COLUMN0ROW2=2,a.COLUMN1ROW0=3,a.COLUMN1ROW1=4,a.COLUMN1ROW2=5,a.COLUMN2ROW0=6,a.COLUMN2ROW1=7,a.COLUMN2ROW2=8,Object.defineProperties(a.prototype,{length:{get:function(){return a.packedLength}}}),a.prototype.clone=function(t){return a.clone(this,t)},a.prototype.equals=function(t){return a.equals(this,t)},a.equalsArray=function(t,n,e){return t[0]===n[e]&&t[1]===n[e+1]&&t[2]===n[e+2]&&t[3]===n[e+3]&&t[4]===n[e+4]&&t[5]===n[e+5]&&t[6]===n[e+6]&&t[7]===n[e+7]&&t[8]===n[e+8]},a.prototype.equalsEpsilon=function(t,n){return a.equalsEpsilon(this,t,n)},a.prototype.toString=function(){return`(${this[0]}, ${this[3]}, ${this[6]})\n(${this[1]}, ${this[4]}, ${this[7]})\n(${this[2]}, ${this[5]}, ${this[8]})`},t.Matrix3=a}));
