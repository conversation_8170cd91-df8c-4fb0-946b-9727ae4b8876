define(["exports","./Cartesian3-529c236c","./Math-355606c6","./Matrix3-31d1f01f","./Transforms-2afbbfb5"],(function(t,a,e,n,i){"use strict";const r={},s=new a.Cartesian3,o=new a.Cartesian3,l=new i.Quaternion,c=new n.Matrix3;function C(t,e,r,C,y,u,m,h,x,M){const z=t+e;a.Cartesian3.multiplyByScalar(C,Math.cos(z),s),a.Cartesian3.multiplyByScalar(r,Math.sin(z),o),a.Cartesian3.add(s,o,s);let f=Math.cos(t);f*=f;let _=Math.sin(t);_*=_;const O=u/Math.sqrt(m*f+y*_)/h;return i.Quaternion.fromAxisAngle(s,O,l),n.Matrix3.fromQuaternion(l,c),n.Matrix3.multiplyByVector(c,x,M),a.<PERSON>.normalize(M,M),a.Car<PERSON>ian3.multiplyByScalar(M,h,M),M}const y=new a.Cartesian3,u=new a.Cartesian3,m=new a.Cartesian3,h=new a.Cartesian3;r.raisePositionsToHeight=function(t,e,n){const i=e.ellipsoid,r=e.height,s=e.extrudedHeight,o=n?t.length/3*2:t.length/3,l=new Float64Array(3*o),c=t.length,C=n?c:0;for(let e=0;e<c;e+=3){const o=e+1,c=e+2,x=a.Cartesian3.fromArray(t,e,y);i.scaleToGeodeticSurface(x,x);const M=a.Cartesian3.clone(x,u),z=i.geodeticSurfaceNormal(x,h),f=a.Cartesian3.multiplyByScalar(z,r,m);a.Cartesian3.add(x,f,x),n&&(a.Cartesian3.multiplyByScalar(z,s,f),a.Cartesian3.add(M,f,M),l[e+C]=M.x,l[o+C]=M.y,l[c+C]=M.z),l[e]=x.x,l[o]=x.y,l[c]=x.z}return l};const x=new a.Cartesian3,M=new a.Cartesian3,z=new a.Cartesian3;r.computeEllipsePositions=function(t,n,i){const r=t.semiMinorAxis,s=t.semiMajorAxis,o=t.rotation,l=t.center,c=8*t.granularity,h=r*r,f=s*s,_=s*r,O=a.Cartesian3.magnitude(l),d=a.Cartesian3.normalize(l,x);let p=a.Cartesian3.cross(a.Cartesian3.UNIT_Z,l,M);p=a.Cartesian3.normalize(p,p);const w=a.Cartesian3.cross(d,p,z);let P=1+Math.ceil(e.CesiumMath.PI_OVER_TWO/c);const T=e.CesiumMath.PI_OVER_TWO/(P-1);let I=e.CesiumMath.PI_OVER_TWO-P*T;I<0&&(P-=Math.ceil(Math.abs(I)/T));const g=n?new Array(3*(P*(P+2)*2)):void 0;let E=0,V=y,A=u;const R=4*P*3;let W=R-1,S=0;const B=i?new Array(R):void 0;let b,v,Q,G,H;for(I=e.CesiumMath.PI_OVER_TWO,V=C(I,o,w,p,h,_,f,O,d,V),n&&(g[E++]=V.x,g[E++]=V.y,g[E++]=V.z),i&&(B[W--]=V.z,B[W--]=V.y,B[W--]=V.x),I=e.CesiumMath.PI_OVER_TWO-T,b=1;b<P+1;++b){if(V=C(I,o,w,p,h,_,f,O,d,V),A=C(Math.PI-I,o,w,p,h,_,f,O,d,A),n){for(g[E++]=V.x,g[E++]=V.y,g[E++]=V.z,Q=2*b+2,v=1;v<Q-1;++v)G=v/(Q-1),H=a.Cartesian3.lerp(V,A,G,m),g[E++]=H.x,g[E++]=H.y,g[E++]=H.z;g[E++]=A.x,g[E++]=A.y,g[E++]=A.z}i&&(B[W--]=V.z,B[W--]=V.y,B[W--]=V.x,B[S++]=A.x,B[S++]=A.y,B[S++]=A.z),I=e.CesiumMath.PI_OVER_TWO-(b+1)*T}for(b=P;b>1;--b){if(I=e.CesiumMath.PI_OVER_TWO-(b-1)*T,V=C(-I,o,w,p,h,_,f,O,d,V),A=C(I+Math.PI,o,w,p,h,_,f,O,d,A),n){for(g[E++]=V.x,g[E++]=V.y,g[E++]=V.z,Q=2*(b-1)+2,v=1;v<Q-1;++v)G=v/(Q-1),H=a.Cartesian3.lerp(V,A,G,m),g[E++]=H.x,g[E++]=H.y,g[E++]=H.z;g[E++]=A.x,g[E++]=A.y,g[E++]=A.z}i&&(B[W--]=V.z,B[W--]=V.y,B[W--]=V.x,B[S++]=A.x,B[S++]=A.y,B[S++]=A.z)}I=e.CesiumMath.PI_OVER_TWO,V=C(-I,o,w,p,h,_,f,O,d,V);const N={};return n&&(g[E++]=V.x,g[E++]=V.y,g[E++]=V.z,N.positions=g,N.numPts=P),i&&(B[W--]=V.z,B[W--]=V.y,B[W--]=V.x,N.outerPositions=B),N};var f=r;t.EllipseGeometryLibrary=f}));
