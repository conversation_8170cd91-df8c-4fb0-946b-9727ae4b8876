define(["./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./Transforms-2afbbfb5","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./IndexDatatype-58eb7805","./Math-355606c6","./VertexFormat-fbdec922","./WallGeometryLibrary-88a3cb50","./Cartographic-dbefb6fa","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./WebGLConstants-7f557f93","./Matrix2-e4a4609a","./arrayRemoveDuplicates-0d8dde26","./PolylinePipeline-07b67faf","./EllipsoidGeodesic-8ac7b85d","./EllipsoidRhumbLine-6774fec3","./IntersectionTests-01432fe7","./Plane-06f34fae"],(function(e,t,i,n,a,r,o,s,l,m,u,p,c,d,y,g,f,h,C,x,A,E,_,b,v,w,F,L){"use strict";const k=new n.Cartesian3,V=new n.Cartesian3,H=new n.Cartesian3,D=new n.Cartesian3,G=new n.Cartesian3,P=new n.Cartesian3,T=new n.Cartesian3;function z(i){const a=(i=e.defaultValue(i,e.defaultValue.EMPTY_OBJECT)).positions,r=i.maximumHeights,o=i.minimumHeights,s=e.defaultValue(i.vertexFormat,m.VertexFormat.DEFAULT),u=e.defaultValue(i.granularity,l.CesiumMath.RADIANS_PER_DEGREE),p=e.defaultValue(i.ellipsoid,t.Ellipsoid.WGS84);this._positions=a,this._minimumHeights=o,this._maximumHeights=r,this._vertexFormat=m.VertexFormat.clone(s),this._granularity=u,this._ellipsoid=t.Ellipsoid.clone(p),this._workerName="createWallGeometry";let c=1+a.length*n.Cartesian3.packedLength+2;e.defined(o)&&(c+=o.length),e.defined(r)&&(c+=r.length),this.packedLength=c+t.Ellipsoid.packedLength+m.VertexFormat.packedLength+1}z.pack=function(i,a,r){let o;r=e.defaultValue(r,0);const s=i._positions;let l=s.length;for(a[r++]=l,o=0;o<l;++o,r+=n.Cartesian3.packedLength)n.Cartesian3.pack(s[o],a,r);const u=i._minimumHeights;if(l=e.defined(u)?u.length:0,a[r++]=l,e.defined(u))for(o=0;o<l;++o)a[r++]=u[o];const p=i._maximumHeights;if(l=e.defined(p)?p.length:0,a[r++]=l,e.defined(p))for(o=0;o<l;++o)a[r++]=p[o];return t.Ellipsoid.pack(i._ellipsoid,a,r),r+=t.Ellipsoid.packedLength,m.VertexFormat.pack(i._vertexFormat,a,r),a[r+=m.VertexFormat.packedLength]=i._granularity,a};const R=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),M=new m.VertexFormat,O={positions:void 0,minimumHeights:void 0,maximumHeights:void 0,ellipsoid:R,vertexFormat:M,granularity:void 0};return z.unpack=function(i,a,r){let o;a=e.defaultValue(a,0);let s=i[a++];const l=new Array(s);for(o=0;o<s;++o,a+=n.Cartesian3.packedLength)l[o]=n.Cartesian3.unpack(i,a);let u,p;if(s=i[a++],s>0)for(u=new Array(s),o=0;o<s;++o)u[o]=i[a++];if(s=i[a++],s>0)for(p=new Array(s),o=0;o<s;++o)p[o]=i[a++];const c=t.Ellipsoid.unpack(i,a,R);a+=t.Ellipsoid.packedLength;const d=m.VertexFormat.unpack(i,a,M),y=i[a+=m.VertexFormat.packedLength];return e.defined(r)?(r._positions=l,r._minimumHeights=u,r._maximumHeights=p,r._ellipsoid=t.Ellipsoid.clone(c,r._ellipsoid),r._vertexFormat=m.VertexFormat.clone(d,r._vertexFormat),r._granularity=y,r):(O.positions=l,O.minimumHeights=u,O.maximumHeights=p,O.granularity=y,new z(O))},z.fromConstantHeights=function(t){const i=(t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT)).positions;let n,a;const r=t.minimumHeight,o=t.maximumHeight,s=e.defined(r),l=e.defined(o);if(s||l){const e=i.length;n=s?new Array(e):void 0,a=l?new Array(e):void 0;for(let t=0;t<e;++t)s&&(n[t]=r),l&&(a[t]=o)}return new z({positions:i,maximumHeights:a,minimumHeights:n,ellipsoid:t.ellipsoid,vertexFormat:t.vertexFormat})},z.createGeometry=function(t){const m=t._positions,p=t._minimumHeights,c=t._maximumHeights,d=t._vertexFormat,y=t._granularity,g=t._ellipsoid,f=u.WallGeometryLibrary.computePositions(g,m,c,p,y,!0);if(!e.defined(f))return;const h=f.bottomPositions,C=f.topPositions,x=f.numCorners;let A=C.length,E=2*A;const _=d.position?new Float64Array(E):void 0,b=d.normal?new Float32Array(E):void 0,v=d.tangent?new Float32Array(E):void 0,w=d.bitangent?new Float32Array(E):void 0,F=d.st?new Float32Array(E/3*2):void 0;let L,z=0,R=0,M=0,O=0,I=0,S=T,N=P,W=G,q=!0;A/=3;let B=0;const J=1/(A-x-1);for(L=0;L<A;++L){const e=3*L,t=n.Cartesian3.fromArray(C,e,k),i=n.Cartesian3.fromArray(h,e,V);if(d.position&&(_[z++]=i.x,_[z++]=i.y,_[z++]=i.z,_[z++]=t.x,_[z++]=t.y,_[z++]=t.z),d.st&&(F[I++]=B,F[I++]=0,F[I++]=B,F[I++]=1),d.normal||d.tangent||d.bitangent){let i=n.Cartesian3.clone(n.Cartesian3.ZERO,D);const a=n.Cartesian3.subtract(t,g.geodeticSurfaceNormal(t,V),V);if(L+1<A&&(i=n.Cartesian3.fromArray(C,e+3,D)),q){const e=n.Cartesian3.subtract(i,t,H),r=n.Cartesian3.subtract(a,t,k);S=n.Cartesian3.normalize(n.Cartesian3.cross(r,e,S),S),q=!1}n.Cartesian3.equalsEpsilon(t,i,l.CesiumMath.EPSILON10)?q=!0:(B+=J,d.tangent&&(N=n.Cartesian3.normalize(n.Cartesian3.subtract(i,t,N),N)),d.bitangent&&(W=n.Cartesian3.normalize(n.Cartesian3.cross(S,N,W),W))),d.normal&&(b[R++]=S.x,b[R++]=S.y,b[R++]=S.z,b[R++]=S.x,b[R++]=S.y,b[R++]=S.z),d.tangent&&(v[O++]=N.x,v[O++]=N.y,v[O++]=N.z,v[O++]=N.x,v[O++]=N.y,v[O++]=N.z),d.bitangent&&(w[M++]=W.x,w[M++]=W.y,w[M++]=W.z,w[M++]=W.x,w[M++]=W.y,w[M++]=W.z)}}const U=new o.GeometryAttributes;d.position&&(U.position=new r.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:_})),d.normal&&(U.normal=new r.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:b})),d.tangent&&(U.tangent=new r.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:v})),d.bitangent&&(U.bitangent=new r.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:w})),d.st&&(U.st=new r.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:F}));const Y=E/3;E-=6*(x+1);const j=s.IndexDatatype.createTypedArray(Y,E);let Z=0;for(L=0;L<Y-2;L+=2){const e=L,t=L+2,i=n.Cartesian3.fromArray(_,3*e,k),a=n.Cartesian3.fromArray(_,3*t,V);if(n.Cartesian3.equalsEpsilon(i,a,l.CesiumMath.EPSILON10))continue;const r=L+1,o=L+3;j[Z++]=r,j[Z++]=e,j[Z++]=o,j[Z++]=o,j[Z++]=e,j[Z++]=t}return new r.Geometry({attributes:U,indices:j,primitiveType:r.PrimitiveType.TRIANGLES,boundingSphere:new i.BoundingSphere.fromVertices(_)})},function(i,n){return e.defined(n)&&(i=z.unpack(i,n)),i._ellipsoid=t.Ellipsoid.clone(i._ellipsoid),z.createGeometry(i)}}));
