define(["./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./Rectangle-98b0bef0","./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./ComponentDatatype-ab629b88","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryInstance-1d11f88d","./GeometryOffsetAttribute-2579b8d2","./GeometryPipeline-69b47aa9","./IndexDatatype-58eb7805","./Math-355606c6","./Matrix2-e4a4609a","./Matrix3-31d1f01f","./PolygonPipeline-39b84ada","./RectangleGeometryLibrary-4ef13be5","./VertexFormat-fbdec922","./Interval-d6c8d27a","./DeveloperError-c85858c1","./combine-0c102d93","./RequestType-735c98f2","./RuntimeError-9b4ce3fb","./WebGLConstants-7f557f93","./AttributeCompression-d2ca507e","./EncodedCartesian3-94199dac","./IntersectionTests-01432fe7","./Plane-06f34fae","./EllipsoidRhumbLine-6774fec3"],(function(t,e,n,a,o,r,i,s,l,u,c,m,p,g,d,y,f,h,b,_,A,x,w,C,v,R,E,G,F,P,V){"use strict";const L=new r.Cartesian3,D=new r.Cartesian3,M=new r.Cartesian3,T=new r.Cartesian3,O=new n.Rectangle,N=new o.Cartesian2,S=new a.BoundingSphere,I=new a.BoundingSphere;function k(t,e){const n=new l.Geometry({attributes:new u.GeometryAttributes,primitiveType:l.PrimitiveType.TRIANGLES});return n.attributes.position=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e.positions}),t.normal&&(n.attributes.normal=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.normals})),t.tangent&&(n.attributes.tangent=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.tangents})),t.bitangent&&(n.attributes.bitangent=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.bitangents})),n}const H=new r.Cartesian3,z=new r.Cartesian3;function U(t,e){const n=t._vertexFormat,a=t._ellipsoid,o=e.height,i=e.width,u=e.northCap,c=e.southCap;let m=0,p=o,d=o,y=0;u&&(m=1,d-=1,y+=1),c&&(p-=1,d-=1,y+=1),y+=i*d;const h=n.position?new Float64Array(3*y):void 0,_=n.st?new Float32Array(2*y):void 0;let A=0,x=0;const w=L,C=N;let v=Number.MAX_VALUE,R=Number.MAX_VALUE,E=-Number.MAX_VALUE,G=-Number.MAX_VALUE;for(let t=m;t<p;++t)for(let o=0;o<i;++o)b.RectangleGeometryLibrary.computePosition(e,a,n.st,t,o,w,C),h[A++]=w.x,h[A++]=w.y,h[A++]=w.z,n.st&&(_[x++]=C.x,_[x++]=C.y,v=Math.min(v,C.x),R=Math.min(R,C.y),E=Math.max(E,C.x),G=Math.max(G,C.y));if(u&&(b.RectangleGeometryLibrary.computePosition(e,a,n.st,0,0,w,C),h[A++]=w.x,h[A++]=w.y,h[A++]=w.z,n.st&&(_[x++]=C.x,_[x++]=C.y,v=C.x,R=C.y,E=C.x,G=C.y)),c&&(b.RectangleGeometryLibrary.computePosition(e,a,n.st,o-1,0,w,C),h[A++]=w.x,h[A++]=w.y,h[A]=w.z,n.st&&(_[x++]=C.x,_[x]=C.y,v=Math.min(v,C.x),R=Math.min(R,C.y),E=Math.max(E,C.x),G=Math.max(G,C.y))),n.st&&(v<0||R<0||E>1||G>1))for(let t=0;t<_.length;t+=2)_[t]=(_[t]-v)/(E-v),_[t+1]=(_[t+1]-R)/(G-R);const F=function(t,e,n,a){const o=t.length,i=e.normal?new Float32Array(o):void 0,s=e.tangent?new Float32Array(o):void 0,l=e.bitangent?new Float32Array(o):void 0;let u=0;const c=T,m=M;let p=D;if(e.normal||e.tangent||e.bitangent)for(let g=0;g<o;g+=3){const o=r.Cartesian3.fromArray(t,g,L),d=u+1,y=u+2;p=n.geodeticSurfaceNormal(o,p),(e.tangent||e.bitangent)&&(r.Cartesian3.cross(r.Cartesian3.UNIT_Z,p,m),f.Matrix3.multiplyByVector(a,m,m),r.Cartesian3.normalize(m,m),e.bitangent&&r.Cartesian3.normalize(r.Cartesian3.cross(p,m,c),c)),e.normal&&(i[u]=p.x,i[d]=p.y,i[y]=p.z),e.tangent&&(s[u]=m.x,s[d]=m.y,s[y]=m.z),e.bitangent&&(l[u]=c.x,l[d]=c.y,l[y]=c.z),u+=3}return k(e,{positions:t,normals:i,tangents:s,bitangents:l})}(h,n,a,e.tangentRotationMatrix);let P=6*(i-1)*(d-1);u&&(P+=3*(i-1)),c&&(P+=3*(i-1));const V=g.IndexDatatype.createTypedArray(y,P);let O,S=0,I=0;for(O=0;O<d-1;++O){for(let t=0;t<i-1;++t){const t=S,e=t+i,n=e+1,a=t+1;V[I++]=t,V[I++]=e,V[I++]=a,V[I++]=a,V[I++]=e,V[I++]=n,++S}++S}if(u||c){let t=y-1;const e=y-1;let n,a;if(u&&c&&(t=y-2),S=0,u)for(O=0;O<i-1;O++)n=S,a=n+1,V[I++]=t,V[I++]=n,V[I++]=a,++S;if(c)for(S=(d-1)*i,O=0;O<i-1;O++)n=S,a=n+1,V[I++]=n,V[I++]=e,V[I++]=a,++S}return F.indices=V,n.st&&(F.attributes.st=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:_})),F}function B(t,e,n,a,o){return t[e++]=a[n],t[e++]=a[n+1],t[e++]=a[n+2],t[e++]=o[n],t[e++]=o[n+1],t[e]=o[n+2],t}function q(t,e,n,a){return t[e++]=a[n],t[e++]=a[n+1],t[e++]=a[n],t[e]=a[n+1],t}const Y=new _.VertexFormat;function X(e,n){const a=e._shadowVolume,o=e._offsetAttribute,i=e._vertexFormat,u=e._extrudedHeight,y=e._surfaceHeight,f=e._ellipsoid,b=n.height,A=n.width;let x;if(a){const t=_.VertexFormat.clone(i,Y);t.normal=!0,e._vertexFormat=t}const w=U(e,n);a&&(e._vertexFormat=i);let C=h.PolygonPipeline.scaleToGeodeticHeight(w.attributes.position.values,y,f,!1);C=new Float64Array(C);let v=C.length;const R=2*v,E=new Float64Array(R);E.set(C);const G=h.PolygonPipeline.scaleToGeodeticHeight(w.attributes.position.values,u,f);E.set(G,v),w.attributes.position.values=E;const F=i.normal?new Float32Array(R):void 0,P=i.tangent?new Float32Array(R):void 0,V=i.bitangent?new Float32Array(R):void 0,O=i.st?new Float32Array(R/3*2):void 0;let N,S,I;if(i.normal){for(S=w.attributes.normal.values,F.set(S),x=0;x<v;x++)S[x]=-S[x];F.set(S,v),w.attributes.normal.values=F}if(a){S=w.attributes.normal.values,i.normal||(w.attributes.normal=void 0);const t=new Float32Array(R);for(x=0;x<v;x++)S[x]=-S[x];t.set(S,v),w.attributes.extrudeDirection=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:t})}const X=t.defined(o);if(X){const t=v/3*2;let e=new Uint8Array(t);o===m.GeometryOffsetAttribute.TOP?e=e.fill(1,0,t/2):(I=o===m.GeometryOffsetAttribute.NONE?0:1,e=e.fill(I)),w.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:e})}if(i.tangent){const t=w.attributes.tangent.values;for(P.set(t),x=0;x<v;x++)t[x]=-t[x];P.set(t,v),w.attributes.tangent.values=P}if(i.bitangent){const t=w.attributes.bitangent.values;V.set(t),V.set(t,v),w.attributes.bitangent.values=V}i.st&&(N=w.attributes.st.values,O.set(N),O.set(N,v/3*2),w.attributes.st.values=O);const J=w.indices,Q=J.length,W=v/3,j=g.IndexDatatype.createTypedArray(R/3,2*Q);for(j.set(J),x=0;x<Q;x+=3)j[x+Q]=J[x+2]+W,j[x+1+Q]=J[x+1]+W,j[x+2+Q]=J[x]+W;w.indices=j;const Z=n.northCap,K=n.southCap;let $=b,tt=2,et=0,nt=4,at=4;Z&&(tt-=1,$-=1,et+=1,nt-=2,at-=1),K&&(tt-=1,$-=1,et+=1,nt-=2,at-=1),et+=tt*A+2*$-nt;const ot=2*(et+at);let rt=new Float64Array(3*ot);const it=a?new Float32Array(3*ot):void 0;let st=X?new Uint8Array(ot):void 0,lt=i.st?new Float32Array(2*ot):void 0;const ut=o===m.GeometryOffsetAttribute.TOP;X&&!ut&&(I=o===m.GeometryOffsetAttribute.ALL?1:0,st=st.fill(I));let ct=0,mt=0,pt=0,gt=0;const dt=A*$;let yt;for(x=0;x<dt;x+=A)yt=3*x,rt=B(rt,ct,yt,C,G),ct+=6,i.st&&(lt=q(lt,mt,2*x,N),mt+=4),a&&(pt+=3,it[pt++]=S[yt],it[pt++]=S[yt+1],it[pt++]=S[yt+2]),ut&&(st[gt++]=1,gt+=1);if(K){const t=Z?dt+1:dt;for(yt=3*t,x=0;x<2;x++)rt=B(rt,ct,yt,C,G),ct+=6,i.st&&(lt=q(lt,mt,2*t,N),mt+=4),a&&(pt+=3,it[pt++]=S[yt],it[pt++]=S[yt+1],it[pt++]=S[yt+2]),ut&&(st[gt++]=1,gt+=1)}else for(x=dt-A;x<dt;x++)yt=3*x,rt=B(rt,ct,yt,C,G),ct+=6,i.st&&(lt=q(lt,mt,2*x,N),mt+=4),a&&(pt+=3,it[pt++]=S[yt],it[pt++]=S[yt+1],it[pt++]=S[yt+2]),ut&&(st[gt++]=1,gt+=1);for(x=dt-1;x>0;x-=A)yt=3*x,rt=B(rt,ct,yt,C,G),ct+=6,i.st&&(lt=q(lt,mt,2*x,N),mt+=4),a&&(pt+=3,it[pt++]=S[yt],it[pt++]=S[yt+1],it[pt++]=S[yt+2]),ut&&(st[gt++]=1,gt+=1);if(Z){const t=dt;for(yt=3*t,x=0;x<2;x++)rt=B(rt,ct,yt,C,G),ct+=6,i.st&&(lt=q(lt,mt,2*t,N),mt+=4),a&&(pt+=3,it[pt++]=S[yt],it[pt++]=S[yt+1],it[pt++]=S[yt+2]),ut&&(st[gt++]=1,gt+=1)}else for(x=A-1;x>=0;x--)yt=3*x,rt=B(rt,ct,yt,C,G),ct+=6,i.st&&(lt=q(lt,mt,2*x,N),mt+=4),a&&(pt+=3,it[pt++]=S[yt],it[pt++]=S[yt+1],it[pt++]=S[yt+2]),ut&&(st[gt++]=1,gt+=1);let ft=function(t,e,n){const a=t.length,o=e.normal?new Float32Array(a):void 0,i=e.tangent?new Float32Array(a):void 0,s=e.bitangent?new Float32Array(a):void 0;let l=0,u=0,c=0,m=!0,p=T,g=M,y=D;if(e.normal||e.tangent||e.bitangent)for(let f=0;f<a;f+=6){const h=r.Cartesian3.fromArray(t,f,L),b=r.Cartesian3.fromArray(t,(f+6)%a,H);if(m){const e=r.Cartesian3.fromArray(t,(f+3)%a,z);r.Cartesian3.subtract(b,h,b),r.Cartesian3.subtract(e,h,e),y=r.Cartesian3.normalize(r.Cartesian3.cross(e,b,y),y),m=!1}r.Cartesian3.equalsEpsilon(b,h,d.CesiumMath.EPSILON10)&&(m=!0),(e.tangent||e.bitangent)&&(p=n.geodeticSurfaceNormal(h,p),e.tangent&&(g=r.Cartesian3.normalize(r.Cartesian3.cross(p,y,g),g))),e.normal&&(o[l++]=y.x,o[l++]=y.y,o[l++]=y.z,o[l++]=y.x,o[l++]=y.y,o[l++]=y.z),e.tangent&&(i[u++]=g.x,i[u++]=g.y,i[u++]=g.z,i[u++]=g.x,i[u++]=g.y,i[u++]=g.z),e.bitangent&&(s[c++]=p.x,s[c++]=p.y,s[c++]=p.z,s[c++]=p.x,s[c++]=p.y,s[c++]=p.z)}return k(e,{positions:t,normals:o,tangents:i,bitangents:s})}(rt,i,f);i.st&&(ft.attributes.st=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:lt})),a&&(ft.attributes.extrudeDirection=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:it})),X&&(ft.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:st}));const ht=g.IndexDatatype.createTypedArray(ot,6*et);let bt,_t,At,xt;v=rt.length/3;let wt=0;for(x=0;x<v-1;x+=2){bt=x,xt=(bt+2)%v;const t=r.Cartesian3.fromArray(rt,3*bt,H),e=r.Cartesian3.fromArray(rt,3*xt,z);r.Cartesian3.equalsEpsilon(t,e,d.CesiumMath.EPSILON10)||(_t=(bt+1)%v,At=(_t+2)%v,ht[wt++]=bt,ht[wt++]=_t,ht[wt++]=xt,ht[wt++]=xt,ht[wt++]=_t,ht[wt++]=At)}return ft.indices=ht,ft=p.GeometryPipeline.combineInstances([new c.GeometryInstance({geometry:w}),new c.GeometryInstance({geometry:ft})]),ft[0]}const J=[new r.Cartesian3,new r.Cartesian3,new r.Cartesian3,new r.Cartesian3],Q=new i.Cartographic,W=new i.Cartographic;function j(t,e,a,o,r){if(0===a)return n.Rectangle.clone(t,r);const i=b.RectangleGeometryLibrary.computeOptions(t,e,a,0,O,Q),s=i.height,l=i.width,u=J;return b.RectangleGeometryLibrary.computePosition(i,o,!1,0,0,u[0]),b.RectangleGeometryLibrary.computePosition(i,o,!1,0,l-1,u[1]),b.RectangleGeometryLibrary.computePosition(i,o,!1,s-1,0,u[2]),b.RectangleGeometryLibrary.computePosition(i,o,!1,s-1,l-1,u[3]),n.Rectangle.fromCartesianArray(u,o,r)}function Z(a){const o=(a=t.defaultValue(a,t.defaultValue.EMPTY_OBJECT)).rectangle,r=t.defaultValue(a.height,0),i=t.defaultValue(a.extrudedHeight,r);this._rectangle=n.Rectangle.clone(o),this._granularity=t.defaultValue(a.granularity,d.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=e.Ellipsoid.clone(t.defaultValue(a.ellipsoid,e.Ellipsoid.WGS84)),this._surfaceHeight=Math.max(r,i),this._rotation=t.defaultValue(a.rotation,0),this._stRotation=t.defaultValue(a.stRotation,0),this._vertexFormat=_.VertexFormat.clone(t.defaultValue(a.vertexFormat,_.VertexFormat.DEFAULT)),this._extrudedHeight=Math.min(r,i),this._shadowVolume=t.defaultValue(a.shadowVolume,!1),this._workerName="createRectangleGeometry",this._offsetAttribute=a.offsetAttribute,this._rotatedRectangle=void 0,this._textureCoordinateRotationPoints=void 0}Z.packedLength=n.Rectangle.packedLength+e.Ellipsoid.packedLength+_.VertexFormat.packedLength+7,Z.pack=function(a,o,r){return r=t.defaultValue(r,0),n.Rectangle.pack(a._rectangle,o,r),r+=n.Rectangle.packedLength,e.Ellipsoid.pack(a._ellipsoid,o,r),r+=e.Ellipsoid.packedLength,_.VertexFormat.pack(a._vertexFormat,o,r),r+=_.VertexFormat.packedLength,o[r++]=a._granularity,o[r++]=a._surfaceHeight,o[r++]=a._rotation,o[r++]=a._stRotation,o[r++]=a._extrudedHeight,o[r++]=a._shadowVolume?1:0,o[r]=t.defaultValue(a._offsetAttribute,-1),o};const K=new n.Rectangle,$=e.Ellipsoid.clone(e.Ellipsoid.UNIT_SPHERE),tt={rectangle:K,ellipsoid:$,vertexFormat:Y,granularity:void 0,height:void 0,rotation:void 0,stRotation:void 0,extrudedHeight:void 0,shadowVolume:void 0,offsetAttribute:void 0};Z.unpack=function(a,o,r){o=t.defaultValue(o,0);const i=n.Rectangle.unpack(a,o,K);o+=n.Rectangle.packedLength;const s=e.Ellipsoid.unpack(a,o,$);o+=e.Ellipsoid.packedLength;const l=_.VertexFormat.unpack(a,o,Y);o+=_.VertexFormat.packedLength;const u=a[o++],c=a[o++],m=a[o++],p=a[o++],g=a[o++],d=1===a[o++],y=a[o];return t.defined(r)?(r._rectangle=n.Rectangle.clone(i,r._rectangle),r._ellipsoid=e.Ellipsoid.clone(s,r._ellipsoid),r._vertexFormat=_.VertexFormat.clone(l,r._vertexFormat),r._granularity=u,r._surfaceHeight=c,r._rotation=m,r._stRotation=p,r._extrudedHeight=g,r._shadowVolume=d,r._offsetAttribute=-1===y?void 0:y,r):(tt.granularity=u,tt.height=c,tt.rotation=m,tt.stRotation=p,tt.extrudedHeight=g,tt.shadowVolume=d,tt.offsetAttribute=-1===y?void 0:y,new Z(tt))},Z.computeRectangle=function(n,a){const o=(n=t.defaultValue(n,t.defaultValue.EMPTY_OBJECT)).rectangle,r=t.defaultValue(n.granularity,d.CesiumMath.RADIANS_PER_DEGREE),i=t.defaultValue(n.ellipsoid,e.Ellipsoid.WGS84);return j(o,r,t.defaultValue(n.rotation,0),i,a)};const et=new f.Matrix3,nt=new a.Quaternion,at=new i.Cartographic;Z.createGeometry=function(e){if(d.CesiumMath.equalsEpsilon(e._rectangle.north,e._rectangle.south,d.CesiumMath.EPSILON10)||d.CesiumMath.equalsEpsilon(e._rectangle.east,e._rectangle.west,d.CesiumMath.EPSILON10))return;let o=e._rectangle;const r=e._ellipsoid,i=e._rotation,u=e._stRotation,c=e._vertexFormat,p=b.RectangleGeometryLibrary.computeOptions(o,e._granularity,i,u,O,Q,W),g=et;if(0!==u||0!==i){const t=n.Rectangle.center(o,at),e=r.geodeticSurfaceNormalCartographic(t,H);a.Quaternion.fromAxisAngle(e,-u,nt),f.Matrix3.fromQuaternion(nt,g)}else f.Matrix3.clone(f.Matrix3.IDENTITY,g);const y=e._surfaceHeight,_=e._extrudedHeight,A=!d.CesiumMath.equalsEpsilon(y,_,0,d.CesiumMath.EPSILON2);let x,w;if(p.lonScalar=1/e._rectangle.width,p.latScalar=1/e._rectangle.height,p.tangentRotationMatrix=g,o=e._rectangle,A){x=X(e,p);const t=a.BoundingSphere.fromRectangle3D(o,r,y,I),n=a.BoundingSphere.fromRectangle3D(o,r,_,S);w=a.BoundingSphere.union(t,n)}else{if(x=U(e,p),x.attributes.position.values=h.PolygonPipeline.scaleToGeodeticHeight(x.attributes.position.values,y,r,!1),t.defined(e._offsetAttribute)){const t=x.attributes.position.values.length,n=e._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1,a=new Uint8Array(t/3).fill(n);x.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:a})}w=a.BoundingSphere.fromRectangle3D(o,r,y)}return c.position||delete x.attributes.position,new l.Geometry({attributes:x.attributes,indices:x.indices,primitiveType:x.primitiveType,boundingSphere:w,offsetAttribute:e._offsetAttribute})},Z.createShadowVolume=function(t,e,n){const a=t._granularity,o=t._ellipsoid,r=e(a,o),i=n(a,o);return new Z({rectangle:t._rectangle,rotation:t._rotation,ellipsoid:o,stRotation:t._stRotation,granularity:a,extrudedHeight:i,height:r,vertexFormat:_.VertexFormat.POSITION_ONLY,shadowVolume:!0})};const ot=new n.Rectangle,rt=[new o.Cartesian2,new o.Cartesian2,new o.Cartesian2],it=new y.Matrix2,st=new i.Cartographic;return Object.defineProperties(Z.prototype,{rectangle:{get:function(){return t.defined(this._rotatedRectangle)||(this._rotatedRectangle=j(this._rectangle,this._granularity,this._rotation,this._ellipsoid)),this._rotatedRectangle}},textureCoordinateRotationPoints:{get:function(){return t.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(t){if(0===t._stRotation)return[0,0,0,1,1,0];const e=n.Rectangle.clone(t._rectangle,ot),a=t._granularity,r=t._ellipsoid,i=j(e,a,t._rotation-t._stRotation,r,ot),s=rt;s[0].x=i.west,s[0].y=i.south,s[1].x=i.west,s[1].y=i.north,s[2].x=i.east,s[2].y=i.south;const l=t.rectangle,u=y.Matrix2.fromRotation(t._stRotation,it),c=n.Rectangle.center(l,st);for(let t=0;t<3;++t){const e=s[t];e.x-=c.longitude,e.y-=c.latitude,y.Matrix2.multiplyByVector(u,e,e),e.x+=c.longitude,e.y+=c.latitude,e.x=(e.x-l.west)/l.width,e.y=(e.y-l.south)/l.height}const m=s[0],p=s[1],g=s[2],d=new Array(6);return o.Cartesian2.pack(m,d),o.Cartesian2.pack(p,d,2),o.Cartesian2.pack(g,d,4),d}(this)),this._textureCoordinateRotationPoints}}}),function(a,o){return t.defined(o)&&(a=Z.unpack(a,o)),a._ellipsoid=e.Ellipsoid.clone(a._ellipsoid),a._rectangle=n.Rectangle.clone(a._rectangle),Z.createGeometry(a)}}));
