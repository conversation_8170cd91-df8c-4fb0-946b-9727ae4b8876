define(["./Cartesian3-529c236c","./Cartographic-dbefb6fa","./Ellipsoid-8e26549b","./IndexDatatype-58eb7805","./Math-355606c6","./Rectangle-98b0bef0","./createTaskProcessorWorker","./combine-0c102d93","./defaultValue-f6d5e6da","./WebGLConstants-7f557f93"],(function(a,e,r,n,t,i,s,o,f,u){"use strict";function c(a){return a>>>1^-(1&a)}function p(a){return a>>1^-(1&a)}var d,l=new e.Cartographic,C=new a.Cartesian3;function b(r,n,i,s,o){var u=r.length/3,b=r.subarray(0,u),h=r.subarray(u,2*u),w=r.subarray(2*u,3*u);!function(a,e,r){var n=a.length,t=0,i=0,s=0;if(2===a.BYTES_PER_ELEMENT)for(var o=0;o<n;++o)t+=p(a[o]),i+=p(e[o]),a[o]=t,e[o]=i,f.defined(r)&&(s+=p(r[o]),r[o]=s);else if(4===a.BYTES_PER_ELEMENT)for(o=0;o<n;++o)t+=c(a[o]),i+=c(e[o]),a[o]=t,e[o]=i,f.defined(r)&&(s+=c(r[o]),r[o]=s)}(b,h,w);for(var y=new Float64Array(r.length),v=0;v<u;++v){var k=b[v],E=h[v],g=w[v],A=t.CesiumMath.lerp(n.west,n.east,k/d),T=t.CesiumMath.lerp(n.south,n.north,E/d),m=t.CesiumMath.lerp(i,s,g/d),D=e.Cartographic.fromRadians(A,T,m,l),P=o.cartographicToCartesian(D,C);a.Cartesian3.pack(P,y,3*v)}return y}var h=new i.Rectangle,w=new r.Ellipsoid,y=new a.Cartesian3,v={min:void 0,max:void 0};var k=new a.Cartesian3,E=new a.Cartesian3,g=new a.Cartesian3,A=new a.Cartesian3,T=new a.Cartesian3;return s((function(e,t){var s;e.highPrecision?(d=2147483647,s=new Uint32Array(e.positions)):(d=32767,s=new Uint16Array(e.positions));var f=new Uint16Array(e.widths),u=new Uint32Array(e.counts),c=new Uint16Array(e.batchIds),p=new Float32Array(e.disableDepthTestDistance);!function(e){e=new Float64Array(e);var n=0;v.min=e[n++],v.max=e[n++],i.Rectangle.unpack(e,n,h),n+=i.Rectangle.packedLength,r.Ellipsoid.unpack(e,n,w),n+=r.Ellipsoid.packedLength,a.Cartesian3.unpack(e,n,y)}(e.packedBuffer);var l,C=w,m=y,D=b(s,h,v.min,v.max,C),P=D.length/3,x=4*P-4,I=new Float32Array(3*x),R=new Float32Array(3*x),U=new Float32Array(3*x),F=new Float32Array(3*x),N=new Uint16Array(x),_=new Float32Array(x),M=0,L=0,S=0,B=0,G=u.length;for(l=0;l<G;++l){for(var W=u[l],Y=f[l],O=c[l],j=p[l],q=0;q<W;++q){var H;if(0===q){var V=a.Cartesian3.unpack(D,3*B,k),z=a.Cartesian3.unpack(D,3*(B+1),E);H=a.Cartesian3.subtract(V,z,g),a.Cartesian3.add(V,H,H)}else H=a.Cartesian3.unpack(D,3*(B+q-1),g);var J,K=a.Cartesian3.unpack(D,3*(B+q),A);if(q===W-1){var Q=a.Cartesian3.unpack(D,3*(B+W-1),k),X=a.Cartesian3.unpack(D,3*(B+W-2),E);J=a.Cartesian3.subtract(Q,X,T),a.Cartesian3.add(Q,J,J)}else J=a.Cartesian3.unpack(D,3*(B+q+1),T);a.Cartesian3.subtract(H,m,H),a.Cartesian3.subtract(K,m,K),a.Cartesian3.subtract(J,m,J);for(var Z=q===W-1?2:4,$=0===q?2:0;$<Z;++$){a.Cartesian3.pack(K,I,M),a.Cartesian3.pack(H,R,M),a.Cartesian3.pack(J,U,M),M+=3;var aa=$-2<0?-1:1;F[L++]=$%2*2-1,F[L++]=aa*Y,F[L++]=q/(W-1),_[S]=j,N[S++]=O}}B+=W}var ea=n.IndexDatatype.createTypedArray(x,6*P-6),ra=0,na=0;for(G=P-1,l=0;l<G;++l)ea[na++]=ra,ea[na++]=ra+2,ea[na++]=ra+1,ea[na++]=ra+1,ea[na++]=ra+2,ea[na++]=ra+3,ra+=4;t.push(I.buffer,R.buffer,U.buffer),t.push(F.buffer,N.buffer,ea.buffer);let ta={indexDatatype:2===ea.BYTES_PER_ELEMENT?n.IndexDatatype.UNSIGNED_SHORT:n.IndexDatatype.UNSIGNED_INT,currentPositions:I.buffer,previousPositions:R.buffer,nextPositions:U.buffer,expandAndWidth:F.buffer,batchIds:N.buffer,indices:ea.buffer,disableDepthTestDistance:_.buffer};if(e.keepDecodedPositions){const a=function(a){const e=a.length,r=new Uint32Array(e+1);let n=0;for(let t=0;t<e;++t)r[t]=n,n+=a[t];return r[e]=n,r}(u);t.push(D.buffer,a.buffer),ta=o.combine(ta,{decodedPositions:D.buffer,decodedPositionOffsets:a.buffer})}return ta}))}));
