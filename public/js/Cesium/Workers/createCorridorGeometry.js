define(["./arrayRemoveDuplicates-0d8dde26","./Transforms-2afbbfb5","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./ComponentDatatype-ab629b88","./PolylineVolumeGeometryLibrary-e8146b77","./CorridorGeometryLibrary-b2575ddf","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-58eb7805","./Math-355606c6","./PolygonPipeline-39b84ada","./Rectangle-98b0bef0","./VertexFormat-fbdec922","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./combine-0c102d93","./RequestType-735c98f2","./WebGLConstants-7f557f93","./EllipsoidTangentPlane-a6ea67fb","./AxisAlignedBoundingBox-d98e5354","./IntersectionTests-01432fe7","./Plane-06f34fae","./PolylinePipeline-07b67faf","./EllipsoidGeodesic-8ac7b85d","./EllipsoidRhumbLine-6774fec3","./Matrix2-e4a4609a"],(function(t,e,r,a,i,n,o,s,l,d,u,m,c,y,p,f,g,h,C,b,A,w,_,v,G,T,E,L,F,V,x,D,P){"use strict";const N=new r.Cartesian3,O=new r.Cartesian3,M=new r.Cartesian3,I=new r.Cartesian3,S=new r.Cartesian3,R=new r.Cartesian3,k=new r.Cartesian3,B=new r.Cartesian3;function H(t,e){for(let r=0;r<t.length;r++)t[r]=e.scaleToGeodeticSurface(t[r],t[r]);return t}function z(t,e,a,i,n,s){const l=t.normals,d=t.tangents,u=t.bitangents,m=r.Cartesian3.normalize(r.Cartesian3.cross(a,e,k),k);s.normal&&o.CorridorGeometryLibrary.addAttribute(l,e,i,n),s.tangent&&o.CorridorGeometryLibrary.addAttribute(d,m,i,n),s.bitangent&&o.CorridorGeometryLibrary.addAttribute(u,a,i,n)}function U(t,e,a){const n=t.positions,l=t.corners,m=t.endPositions,p=t.lefts,f=t.normals,g=new u.GeometryAttributes;let h,C,b,A,w,_,v,G,T,E,L,F=0,V=0,x=0,D=N;if(0===l.length){for(C=0;C<n.length;C+=2)V+=n[C].length,F+=n[C+1].length;v=F+V,x=v-6,L=c.IndexDatatype.createTypedArray(v/3,x),_=new Float64Array(v);const t=e.normal?new Float32Array(v):void 0;E={normals:t,tangents:e.tangent?new Float32Array(v):void 0,bitangents:e.bitangent?new Float32Array(v):void 0};let i,s,l,d,u=0,m=v-1,y=0;const p=n.length;for(C=0;C<p;C+=2){const e=n[C],c=n[C+1];_.set(e,u),_.set(c,m-c.length+1);for(let n=0,f=e.length;n<f;n+=3)A=a.geodeticSurfaceNormal(r.Cartesian3.fromArray(e,n,k),k),w=a.geodeticSurfaceNormal(r.Cartesian3.fromArray(c,f-3-n,B),B),D=r.Cartesian3.normalize(r.Cartesian3.add(A,w,D),D),o.CorridorGeometryLibrary.addAttribute(t,D,u,m),C===p-2&&n===f-3||(d=u/3,l=d+1,s=(m-2)/3,i=s-1,L[y++]=i,L[y++]=s,L[y++]=l,L[y++]=l,L[y++]=s,L[y++]=d,u+=3,m-=3)}}else{for(C=0;C<n.length;C+=2)b=n[C].length-3,F+=b,x+=2*b,V+=n[C+1].length-3;for(F+=3,V+=3,C=0;C<l.length;C++){h=l[C];const t=l[C].leftPositions;s.defined(t)?(b=t.length,F+=b,x+=b):(b=l[C].rightPositions.length,V+=b,x+=b)}G=s.defined(m),G&&(T=m[0].length-3,F+=T,V+=T,T/=3,x+=6*T),v=F+V,_=new Float64Array(v);E={normals:e.normal?new Float32Array(v):void 0,tangents:e.tangent?new Float32Array(v):void 0,bitangents:e.bitangent?new Float32Array(v):void 0};let t,i,d,u,y,g,P=0,N=v-1,S=O;const H=T/2;L=c.IndexDatatype.createTypedArray(v/3,x);let U=0;if(G){g=M,y=I;const a=m[0];for(D=r.Cartesian3.fromArray(f,0,D),S=r.Cartesian3.fromArray(p,0,S),C=0;C<H;C++)g=r.Cartesian3.fromArray(a,3*(H-1-C),g),y=r.Cartesian3.fromArray(a,3*(H+C),y),o.CorridorGeometryLibrary.addAttribute(_,y,P),o.CorridorGeometryLibrary.addAttribute(_,g,void 0,N),z(E,D,S,P,N,e),i=P/3,u=i+1,t=(N-2)/3,d=t-1,L[U++]=t,L[U++]=i,L[U++]=d,L[U++]=d,L[U++]=i,L[U++]=u,P+=3,N-=3}let Y=0,W=0,q=n[Y++],J=n[Y++];for(_.set(q,P),_.set(J,N-J.length+1),S=r.Cartesian3.fromArray(p,W,S),b=J.length-3,C=0;C<b;C+=3)A=a.geodeticSurfaceNormal(r.Cartesian3.fromArray(q,C,k),k),w=a.geodeticSurfaceNormal(r.Cartesian3.fromArray(J,b-C,B),B),D=r.Cartesian3.normalize(r.Cartesian3.add(A,w,D),D),z(E,D,S,P,N,e),i=P/3,u=i+1,t=(N-2)/3,d=t-1,L[U++]=t,L[U++]=i,L[U++]=d,L[U++]=d,L[U++]=i,L[U++]=u,P+=3,N-=3;for(A=a.geodeticSurfaceNormal(r.Cartesian3.fromArray(q,b,k),k),w=a.geodeticSurfaceNormal(r.Cartesian3.fromArray(J,b,B),B),D=r.Cartesian3.normalize(r.Cartesian3.add(A,w,D),D),W+=3,C=0;C<l.length;C++){let m;h=l[C];const c=h.leftPositions,y=h.rightPositions;let g,v,G=R,T=M,F=I;if(D=r.Cartesian3.fromArray(f,W,D),s.defined(c)){for(z(E,D,S,void 0,N,e),N-=3,g=u,v=d,m=0;m<c.length/3;m++)G=r.Cartesian3.fromArray(c,3*m,G),L[U++]=g,L[U++]=v-m-1,L[U++]=v-m,o.CorridorGeometryLibrary.addAttribute(_,G,void 0,N),T=r.Cartesian3.fromArray(_,3*(v-m-1),T),F=r.Cartesian3.fromArray(_,3*g,F),S=r.Cartesian3.normalize(r.Cartesian3.subtract(T,F,S),S),z(E,D,S,void 0,N,e),N-=3;G=r.Cartesian3.fromArray(_,3*g,G),T=r.Cartesian3.subtract(r.Cartesian3.fromArray(_,3*v,T),G,T),F=r.Cartesian3.subtract(r.Cartesian3.fromArray(_,3*(v-m),F),G,F),S=r.Cartesian3.normalize(r.Cartesian3.add(T,F,S),S),z(E,D,S,P,void 0,e),P+=3}else{for(z(E,D,S,P,void 0,e),P+=3,g=d,v=u,m=0;m<y.length/3;m++)G=r.Cartesian3.fromArray(y,3*m,G),L[U++]=g,L[U++]=v+m,L[U++]=v+m+1,o.CorridorGeometryLibrary.addAttribute(_,G,P),T=r.Cartesian3.fromArray(_,3*g,T),F=r.Cartesian3.fromArray(_,3*(v+m),F),S=r.Cartesian3.normalize(r.Cartesian3.subtract(T,F,S),S),z(E,D,S,P,void 0,e),P+=3;G=r.Cartesian3.fromArray(_,3*g,G),T=r.Cartesian3.subtract(r.Cartesian3.fromArray(_,3*(v+m),T),G,T),F=r.Cartesian3.subtract(r.Cartesian3.fromArray(_,3*v,F),G,F),S=r.Cartesian3.normalize(r.Cartesian3.negate(r.Cartesian3.add(F,T,S),S),S),z(E,D,S,void 0,N,e),N-=3}for(q=n[Y++],J=n[Y++],q.splice(0,3),J.splice(J.length-3,3),_.set(q,P),_.set(J,N-J.length+1),b=J.length-3,W+=3,S=r.Cartesian3.fromArray(p,W,S),m=0;m<J.length;m+=3)A=a.geodeticSurfaceNormal(r.Cartesian3.fromArray(q,m,k),k),w=a.geodeticSurfaceNormal(r.Cartesian3.fromArray(J,b-m,B),B),D=r.Cartesian3.normalize(r.Cartesian3.add(A,w,D),D),z(E,D,S,P,N,e),u=P/3,i=u-1,d=(N-2)/3,t=d+1,L[U++]=t,L[U++]=i,L[U++]=d,L[U++]=d,L[U++]=i,L[U++]=u,P+=3,N-=3;P-=3,N+=3}if(D=r.Cartesian3.fromArray(f,f.length-3,D),z(E,D,S,P,N,e),G){P+=3,N-=3,g=M,y=I;const a=m[1];for(C=0;C<H;C++)g=r.Cartesian3.fromArray(a,3*(T-C-1),g),y=r.Cartesian3.fromArray(a,3*C,y),o.CorridorGeometryLibrary.addAttribute(_,g,void 0,N),o.CorridorGeometryLibrary.addAttribute(_,y,P),z(E,D,S,P,N,e),u=P/3,i=u-1,d=(N-2)/3,t=d+1,L[U++]=t,L[U++]=i,L[U++]=d,L[U++]=d,L[U++]=i,L[U++]=u,P+=3,N-=3}}if(g.position=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:_}),e.st){const t=new Float32Array(v/3*2);let e,a,n=0;if(G){F/=3,V/=3;const r=Math.PI/(T+1);let i;a=1/(F-T+1),e=1/(V-T+1);const o=T/2;for(C=o+1;C<T+1;C++)i=y.CesiumMath.PI_OVER_TWO+r*C,t[n++]=e*(1+Math.cos(i)),t[n++]=.5*(1+Math.sin(i));for(C=1;C<V-T+1;C++)t[n++]=C*e,t[n++]=0;for(C=T;C>o;C--)i=y.CesiumMath.PI_OVER_TWO-C*r,t[n++]=1-e*(1+Math.cos(i)),t[n++]=.5*(1+Math.sin(i));for(C=o;C>0;C--)i=y.CesiumMath.PI_OVER_TWO-r*C,t[n++]=1-a*(1+Math.cos(i)),t[n++]=.5*(1+Math.sin(i));for(C=F-T;C>0;C--)t[n++]=C*a,t[n++]=1;for(C=1;C<o+1;C++)i=y.CesiumMath.PI_OVER_TWO+r*C,t[n++]=a*(1+Math.cos(i)),t[n++]=.5*(1+Math.sin(i))}else if(F/=3,V/=3,F===V){const e=r.Cartesian3.unpackArray(_);let a=r.Cartesian3.multiplyByScalar(r.Cartesian3.add(e.shift(),e.pop()),.5,new r.Cartesian3),i=0;const o=[];let s=0;for(;e.length;){const t=r.Cartesian3.multiplyByScalar(r.Cartesian3.add(e.shift(),e.pop()),.5,new r.Cartesian3);i=r.Cartesian3.distance(t,a),o.push(i),s+=i,a=t}t[n++]=0,t[n++]=0;let l=2*(F+V)-1;t[l--]=1,t[l--]=0,i=0;for(let e=0,r=o.length;e<r;e++){i+=o[e];const r=i/s;t[n++]=r,t[n++]=0,t[l--]=1,t[l--]=r}}else{for(a=1/(F-1),e=1/(V-1),C=0;C<V;C++)t[n++]=C*e,t[n++]=0;for(C=F;C>0;C--)t[n++]=(C-1)*a,t[n++]=1}g.st=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:t})}return e.normal&&(g.normal=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E.normals})),e.tangent&&(g.tangent=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E.tangents})),e.bitangent&&(g.bitangent=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E.bitangents})),{attributes:g,indices:L}}function Y(t,e,r){r[e++]=t[0],r[e++]=t[1],r[e++]=t[2];for(let a=3;a<t.length;a+=3){const i=t[a],n=t[a+1],o=t[a+2];r[e++]=i,r[e++]=n,r[e++]=o,r[e++]=i,r[e++]=n,r[e++]=o}return r[e++]=t[0],r[e++]=t[1],r[e++]=t[2],r}function W(t,e){const a=new g.VertexFormat({position:e.position,normal:e.normal||e.bitangent||t.shadowVolume,tangent:e.tangent,bitangent:e.normal||e.bitangent,st:e.st}),n=t.ellipsoid,l=U(o.CorridorGeometryLibrary.computePositions(t),a,n),u=t.height,y=t.extrudedHeight;let f=l.attributes;const h=l.indices;let C=f.position.values,b=C.length;const A=new Float64Array(6*b);let w=new Float64Array(b);w.set(C);let _,v=new Float64Array(4*b);C=p.PolygonPipeline.scaleToGeodeticHeight(C,u,n),v=Y(C,0,v),w=p.PolygonPipeline.scaleToGeodeticHeight(w,y,n),v=Y(w,2*b,v),A.set(C),A.set(w,b),A.set(v,2*b),f.position.values=A,f=function(t,e){if(!(e.normal||e.tangent||e.bitangent||e.st))return t;const a=t.position.values;let i,n;(e.normal||e.bitangent)&&(i=t.normal.values,n=t.bitangent.values);const s=t.position.values.length/18,l=3*s,d=2*s,u=2*l;let m;if(e.normal||e.bitangent||e.tangent){const s=e.normal?new Float32Array(6*l):void 0,d=e.tangent?new Float32Array(6*l):void 0,c=e.bitangent?new Float32Array(6*l):void 0;let y=N,p=O,f=M,g=I,h=S,C=R,b=u;for(m=0;m<l;m+=3){const t=b+u;y=r.Cartesian3.fromArray(a,m,y),p=r.Cartesian3.fromArray(a,m+l,p),f=r.Cartesian3.fromArray(a,(m+3)%l,f),p=r.Cartesian3.subtract(p,y,p),f=r.Cartesian3.subtract(f,y,f),g=r.Cartesian3.normalize(r.Cartesian3.cross(p,f,g),g),e.normal&&(o.CorridorGeometryLibrary.addAttribute(s,g,t),o.CorridorGeometryLibrary.addAttribute(s,g,t+3),o.CorridorGeometryLibrary.addAttribute(s,g,b),o.CorridorGeometryLibrary.addAttribute(s,g,b+3)),(e.tangent||e.bitangent)&&(C=r.Cartesian3.fromArray(i,m,C),e.bitangent&&(o.CorridorGeometryLibrary.addAttribute(c,C,t),o.CorridorGeometryLibrary.addAttribute(c,C,t+3),o.CorridorGeometryLibrary.addAttribute(c,C,b),o.CorridorGeometryLibrary.addAttribute(c,C,b+3)),e.tangent&&(h=r.Cartesian3.normalize(r.Cartesian3.cross(C,g,h),h),o.CorridorGeometryLibrary.addAttribute(d,h,t),o.CorridorGeometryLibrary.addAttribute(d,h,t+3),o.CorridorGeometryLibrary.addAttribute(d,h,b),o.CorridorGeometryLibrary.addAttribute(d,h,b+3))),b+=6}if(e.normal){for(s.set(i),m=0;m<l;m+=3)s[m+l]=-i[m],s[m+l+1]=-i[m+1],s[m+l+2]=-i[m+2];t.normal.values=s}else t.normal=void 0;if(e.bitangent?(c.set(n),c.set(n,l),t.bitangent.values=c):t.bitangent=void 0,e.tangent){const e=t.tangent.values;d.set(e),d.set(e,l),t.tangent.values=d}}if(e.st){const e=t.st.values,r=new Float32Array(6*d);r.set(e),r.set(e,d);let a=2*d;for(let t=0;t<2;t++){for(r[a++]=e[0],r[a++]=e[1],m=2;m<d;m+=2){const t=e[m],i=e[m+1];r[a++]=t,r[a++]=i,r[a++]=t,r[a++]=i}r[a++]=e[0],r[a++]=e[1]}t.st.values=r}return t}(f,e);const G=b/3;if(t.shadowVolume){const t=f.normal.values;b=t.length;let r=new Float32Array(6*b);for(_=0;_<b;_++)t[_]=-t[_];r.set(t,b),r=Y(t,4*b,r),f.extrudeDirection=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:r}),e.normal||(f.normal=void 0)}if(s.defined(t.offsetAttribute)){let e=new Uint8Array(6*G);if(t.offsetAttribute===m.GeometryOffsetAttribute.TOP)e=e.fill(1,0,G).fill(1,2*G,4*G);else{const r=t.offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1;e=e.fill(r)}f.applyOffset=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:e})}const T=h.length,E=G+G,L=c.IndexDatatype.createTypedArray(A.length/3,2*T+3*E);L.set(h);let F,V,x,D,P=T;for(_=0;_<T;_+=3){const t=h[_],e=h[_+1],r=h[_+2];L[P++]=r+G,L[P++]=e+G,L[P++]=t+G}for(_=0;_<E;_+=2)F=_+E,V=F+E,x=F+1,D=V+1,L[P++]=F,L[P++]=V,L[P++]=x,L[P++]=x,L[P++]=V,L[P++]=D;return{attributes:f,indices:L}}const q=new r.Cartesian3,J=new r.Cartesian3,j=new a.Cartographic;function K(t,e,a,i,n,o){const s=r.Cartesian3.subtract(e,t,q);r.Cartesian3.normalize(s,s);const l=a.geodeticSurfaceNormal(t,J),d=r.Cartesian3.cross(s,l,q);r.Cartesian3.multiplyByScalar(d,i,d);let u=n.latitude,m=n.longitude,c=o.latitude,y=o.longitude;r.Cartesian3.add(t,d,J),a.cartesianToCartographic(J,j);let p=j.latitude,f=j.longitude;u=Math.min(u,p),m=Math.min(m,f),c=Math.max(c,p),y=Math.max(y,f),r.Cartesian3.subtract(t,d,J),a.cartesianToCartographic(J,j),p=j.latitude,f=j.longitude,u=Math.min(u,p),m=Math.min(m,f),c=Math.max(c,p),y=Math.max(y,f),n.latitude=u,n.longitude=m,o.latitude=c,o.longitude=y}const Q=new r.Cartesian3,X=new r.Cartesian3,Z=new a.Cartographic,$=new a.Cartographic;function tt(e,a,i,o,l){e=H(e,a);const d=t.arrayRemoveDuplicates(e,r.Cartesian3.equalsEpsilon),u=d.length;if(u<2||i<=0)return new f.Rectangle;const m=.5*i;let c,y;if(Z.latitude=Number.POSITIVE_INFINITY,Z.longitude=Number.POSITIVE_INFINITY,$.latitude=Number.NEGATIVE_INFINITY,$.longitude=Number.NEGATIVE_INFINITY,o===n.CornerType.ROUNDED){const t=d[0];r.Cartesian3.subtract(t,d[1],Q),r.Cartesian3.normalize(Q,Q),r.Cartesian3.multiplyByScalar(Q,m,Q),r.Cartesian3.add(t,Q,X),a.cartesianToCartographic(X,j),c=j.latitude,y=j.longitude,Z.latitude=Math.min(Z.latitude,c),Z.longitude=Math.min(Z.longitude,y),$.latitude=Math.max($.latitude,c),$.longitude=Math.max($.longitude,y)}for(let t=0;t<u-1;++t)K(d[t],d[t+1],a,m,Z,$);const p=d[u-1];r.Cartesian3.subtract(p,d[u-2],Q),r.Cartesian3.normalize(Q,Q),r.Cartesian3.multiplyByScalar(Q,m,Q),r.Cartesian3.add(p,Q,X),K(p,X,a,m,Z,$),o===n.CornerType.ROUNDED&&(a.cartesianToCartographic(X,j),c=j.latitude,y=j.longitude,Z.latitude=Math.min(Z.latitude,c),Z.longitude=Math.min(Z.longitude,y),$.latitude=Math.max($.latitude,c),$.longitude=Math.max($.longitude,y));const g=s.defined(l)?l:new f.Rectangle;return g.north=$.latitude,g.south=Z.latitude,g.east=$.longitude,g.west=Z.longitude,g}function et(t){const e=(t=s.defaultValue(t,s.defaultValue.EMPTY_OBJECT)).positions,a=t.width,i=s.defaultValue(t.height,0),o=s.defaultValue(t.extrudedHeight,i);this._positions=e,this._ellipsoid=l.Ellipsoid.clone(s.defaultValue(t.ellipsoid,l.Ellipsoid.WGS84)),this._vertexFormat=g.VertexFormat.clone(s.defaultValue(t.vertexFormat,g.VertexFormat.DEFAULT)),this._width=a,this._height=Math.max(i,o),this._extrudedHeight=Math.min(i,o),this._cornerType=s.defaultValue(t.cornerType,n.CornerType.ROUNDED),this._granularity=s.defaultValue(t.granularity,y.CesiumMath.RADIANS_PER_DEGREE),this._shadowVolume=s.defaultValue(t.shadowVolume,!1),this._workerName="createCorridorGeometry",this._offsetAttribute=t.offsetAttribute,this._rectangle=void 0,this.packedLength=1+e.length*r.Cartesian3.packedLength+l.Ellipsoid.packedLength+g.VertexFormat.packedLength+7}et.pack=function(t,e,a){a=s.defaultValue(a,0);const i=t._positions,n=i.length;e[a++]=n;for(let t=0;t<n;++t,a+=r.Cartesian3.packedLength)r.Cartesian3.pack(i[t],e,a);return l.Ellipsoid.pack(t._ellipsoid,e,a),a+=l.Ellipsoid.packedLength,g.VertexFormat.pack(t._vertexFormat,e,a),a+=g.VertexFormat.packedLength,e[a++]=t._width,e[a++]=t._height,e[a++]=t._extrudedHeight,e[a++]=t._cornerType,e[a++]=t._granularity,e[a++]=t._shadowVolume?1:0,e[a]=s.defaultValue(t._offsetAttribute,-1),e};const rt=l.Ellipsoid.clone(l.Ellipsoid.UNIT_SPHERE),at=new g.VertexFormat,it={positions:void 0,ellipsoid:rt,vertexFormat:at,width:void 0,height:void 0,extrudedHeight:void 0,cornerType:void 0,granularity:void 0,shadowVolume:void 0,offsetAttribute:void 0};return et.unpack=function(t,e,a){e=s.defaultValue(e,0);const i=t[e++],n=new Array(i);for(let a=0;a<i;++a,e+=r.Cartesian3.packedLength)n[a]=r.Cartesian3.unpack(t,e);const o=l.Ellipsoid.unpack(t,e,rt);e+=l.Ellipsoid.packedLength;const d=g.VertexFormat.unpack(t,e,at);e+=g.VertexFormat.packedLength;const u=t[e++],m=t[e++],c=t[e++],y=t[e++],p=t[e++],f=1===t[e++],h=t[e];return s.defined(a)?(a._positions=n,a._ellipsoid=l.Ellipsoid.clone(o,a._ellipsoid),a._vertexFormat=g.VertexFormat.clone(d,a._vertexFormat),a._width=u,a._height=m,a._extrudedHeight=c,a._cornerType=y,a._granularity=p,a._shadowVolume=f,a._offsetAttribute=-1===h?void 0:h,a):(it.positions=n,it.width=u,it.height=m,it.extrudedHeight=c,it.cornerType=y,it.granularity=p,it.shadowVolume=f,it.offsetAttribute=-1===h?void 0:h,new et(it))},et.computeRectangle=function(t,e){const r=(t=s.defaultValue(t,s.defaultValue.EMPTY_OBJECT)).positions,a=t.width;return tt(r,s.defaultValue(t.ellipsoid,l.Ellipsoid.WGS84),a,s.defaultValue(t.cornerType,n.CornerType.ROUNDED),e)},et.createGeometry=function(a){let l=a._positions;const f=a._width,g=a._ellipsoid,h=a._vertexFormat;let C;if(a._cornerType===n.CornerType.EV_ARC){if(l.length<2||f<=0)return;if(C=function(t,e,a){const n=t.positions,o=t.normals,s=new u.GeometryAttributes,l=n.length,m=new Float64Array(3*l),y=r.Cartesian3.packArray(n);m.set(y,0),s.position=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:m});const p=3*(l-2),f=c.IndexDatatype.createTypedArray(l,p);let g,h,C,b,A=0,w=l-1,_=0,v=l/2-1;for(let t=0;t<v;t++)b=A++,C=b+1,h=w--,g=h-1,f[_++]=g,f[_++]=h,f[_++]=C,f[_++]=C,f[_++]=h,f[_++]=b;if(e.st){let t=0,e=0;const a=[];let o=S,u=R;o=r.Cartesian3.multiplyByScalar(r.Cartesian3.add(n[0],n[l-1],new r.Cartesian3),.5,o),v=l-1;for(let i=1,s=l/2;i<s;i++)u=r.Cartesian3.multiplyByScalar(r.Cartesian3.add(n[i],n[v-i],new r.Cartesian3),.5,u),t=r.Cartesian3.distance(u,o),a.push(t),e+=t,o=r.Cartesian3.clone(u);const m=new Float32Array(2*l);let c=0;m[c++]=0,m[c++]=0;let y=2*l-1;m[y--]=1,m[y--]=0,t=0;for(let r=0,i=a.length;r<i;r++){t+=a[r];const i=t/e;m[c++]=i,m[c++]=0,m[y--]=1,m[y--]=i}s.st=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:m})}if(e.normal){const t=r.Cartesian3.packArray(o),e=new Float32Array(3*l);e.set(t),s.normal=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e})}return{attributes:s,indices:f}}(o.CorridorGeometryLibrary.ev_computePositions({positions:l,width:f,ellipsoid:g,interpolationCount:1e3}),h),s.defined(a._offsetAttribute)){const t=a._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1,e=C.attributes.position.values.length,r=new Uint8Array(e/3).fill(t);C.attributes.applyOffset=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:r})}}else{l=H(l,g);const e=t.arrayRemoveDuplicates(l,r.Cartesian3.equalsEpsilon);if(e.length<2||f<=0)return;const n=a._height,u=a._extrudedHeight,c=!y.CesiumMath.equalsEpsilon(n,u,0,y.CesiumMath.EPSILON2),b={ellipsoid:g,positions:e,width:f,cornerType:a._cornerType,granularity:a._granularity,saveAttributes:!0};if(c)b.height=n,b.extrudedHeight=u,b.shadowVolume=a._shadowVolume,b.offsetAttribute=a._offsetAttribute,C=W(b,h);else{if(C=U(o.CorridorGeometryLibrary.computePositions(b),h,g),C.attributes.position.values=p.PolygonPipeline.scaleToGeodeticHeight(C.attributes.position.values,n,g),s.defined(a._offsetAttribute)){const t=a._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1,e=C.attributes.position.values.length,r=new Uint8Array(e/3).fill(t);C.attributes.applyOffset=new d.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:r})}}}const b=C.attributes,A=e.BoundingSphere.fromVertices(b.position.values,void 0,3);return h.position||(C.attributes.position.values=void 0),new d.Geometry({attributes:b,indices:C.indices,primitiveType:d.PrimitiveType.TRIANGLES,boundingSphere:A,offsetAttribute:a._offsetAttribute})},et.createShadowVolume=function(t,e,r){const a=t._granularity,i=t._ellipsoid,n=e(a,i),o=r(a,i);return new et({positions:t._positions,width:t._width,cornerType:t._cornerType,ellipsoid:i,granularity:a,extrudedHeight:n,height:o,vertexFormat:g.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(et.prototype,{rectangle:{get:function(){return s.defined(this._rectangle)||(this._rectangle=tt(this._positions,this._ellipsoid,this._width,this._cornerType)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return[0,0,0,1,1,0]}}}),function(t,e){return s.defined(e)&&(t=et.unpack(t,e)),t._ellipsoid=l.Ellipsoid.clone(t._ellipsoid),et.createGeometry(t)}}));
