define(["exports","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./PolylineVolumeGeometryLibrary-e8146b77","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./EllipsoidGeodesic-8ac7b85d","./Math-355606c6","./Matrix3-31d1f01f","./PolylinePipeline-07b67faf","./Transforms-2afbbfb5"],(function(a,e,t,n,r,i,s,o,l,C,c){"use strict";var u=Object.freeze({NONE:0,CLAMP_TO_GROUND:1,RELATIVE_TO_GROUND:2});const p={},y=new e.Cartesian3,d=new e.Cartesian3,h=new e.Cartesian3,m=new e.Cartesian3,g=[new e.Cartesian3,new e.Cartesian3],f=new e.Cartesian3,w=new e.Cartesian3,z=new e.Cartesian3,E=new e.Cartesian3,S=new e.Cartesian3,B=new e.Cartesian3,x=new e.Cartesian3,N=new e.Cartesian3,P=new e.Cartesian3,b=new e.Cartesian3,A=new c.Quaternion,T=new l.Matrix3;function M(a,t,r,i,s){const C=e.Cartesian3.angleBetween(e.Cartesian3.subtract(t,a,y),e.Cartesian3.subtract(r,a,d)),u=i===n.CornerType.BEVELED?1:Math.ceil(C/o.CesiumMath.toRadians(5))+1,p=3*u,h=new Array(p);let m;h[p-3]=r.x,h[p-2]=r.y,h[p-1]=r.z,m=s?l.Matrix3.fromQuaternion(c.Quaternion.fromAxisAngle(e.Cartesian3.negate(a,y),C/u,A),T):l.Matrix3.fromQuaternion(c.Quaternion.fromAxisAngle(a,C/u,A),T);let g=0;t=e.Cartesian3.clone(t,y);for(let a=0;a<u;a++)t=l.Matrix3.multiplyByVector(m,t,t),h[g++]=t.x,h[g++]=t.y,h[g++]=t.z;return h}function D(a,t,n,r){let i=y;return r||(t=e.Cartesian3.negate(t,t)),i=e.Cartesian3.add(a,t,i),[i.x,i.y,i.z,n.x,n.y,n.z]}function O(a,t,n,r){const i=new Array(a.length),s=new Array(a.length),o=e.Cartesian3.multiplyByScalar(t,n,y),l=e.Cartesian3.negate(o,d);let C=0,c=a.length-1;for(let t=0;t<a.length;t+=3){const n=e.Cartesian3.fromArray(a,t,h),r=e.Cartesian3.add(n,l,m);i[C++]=r.x,i[C++]=r.y,i[C++]=r.z;const u=e.Cartesian3.add(n,o,m);s[c--]=u.z,s[c--]=u.y,s[c--]=u.x}return r.push(i,s),r}function R(a,t){const n=a[0],r=a[1],i=a[2],s=new e.Cartesian3,o=new e.Cartesian3,l=new e.Cartesian3,C=1/(t+1);let c=0;const u=new Array(t);for(let a=0;a<t;a++){c=(a+1)*C;const t=(1-c)*(1-c),p=2*c*(1-c),y=c*c;e.Cartesian3.multiplyByScalar(n,t,s),e.Cartesian3.multiplyByScalar(r,p,o),e.Cartesian3.multiplyByScalar(i,y,l),e.Cartesian3.add(s,o,o),u[a]=e.Cartesian3.add(o,l,new e.Cartesian3)}return u}p.addAttribute=function(a,e,t,n){const i=e.x,s=e.y,o=e.z;r.defined(t)&&(a[t]=i,a[t+1]=s,a[t+2]=o),r.defined(n)&&(a[n]=o,a[n-1]=s,a[n-2]=i)};const L=new e.Cartesian3,G=new e.Cartesian3;p.computePositions=function(a){const t=a.granularity,r=a.positions,i=a.ellipsoid,s=a.width/2,l=a.cornerType,c=a.saveAttributes;let u=f,p=w,d=z,h=E,m=S,A=B,T=x,R=N,V=P,U=b,_=[];const v=c?[]:void 0,I=c?[]:void 0;let Q,q=r[0],H=r[1];p=e.Cartesian3.normalize(e.Cartesian3.subtract(H,q,p),p),u=i.geodeticSurfaceNormal(q,u),h=e.Cartesian3.normalize(e.Cartesian3.cross(u,p,h),h),c&&(v.push(h.x,h.y,h.z),I.push(u.x,u.y,u.z)),T=e.Cartesian3.clone(q,T),q=H,d=e.Cartesian3.negate(p,d);const j=[];let K;const W=r.length;for(K=1;K<W-1;K++){u=i.geodeticSurfaceNormal(q,u),H=r[K+1],p=e.Cartesian3.normalize(e.Cartesian3.subtract(H,q,p),p),m=e.Cartesian3.normalize(e.Cartesian3.add(p,d,m),m);const a=e.Cartesian3.multiplyByScalar(u,e.Cartesian3.dot(p,u),L);e.Cartesian3.subtract(p,a,a),e.Cartesian3.normalize(a,a);const f=e.Cartesian3.multiplyByScalar(u,e.Cartesian3.dot(d,u),G);e.Cartesian3.subtract(d,f,f),e.Cartesian3.normalize(f,f);if(!o.CesiumMath.equalsEpsilon(Math.abs(e.Cartesian3.dot(a,f)),1,o.CesiumMath.EPSILON7)){m=e.Cartesian3.cross(m,u,m),m=e.Cartesian3.cross(u,m,m),m=e.Cartesian3.normalize(m,m);const a=s/Math.max(.25,e.Cartesian3.magnitude(e.Cartesian3.cross(m,d,y))),r=n.PolylineVolumeGeometryLibrary.angleIsGreaterThanPi(p,d,q,i);m=e.Cartesian3.multiplyByScalar(m,a,m),r?(R=e.Cartesian3.add(q,m,R),U=e.Cartesian3.add(R,e.Cartesian3.multiplyByScalar(h,s,U),U),V=e.Cartesian3.add(R,e.Cartesian3.multiplyByScalar(h,2*s,V),V),g[0]=e.Cartesian3.clone(T,g[0]),g[1]=e.Cartesian3.clone(U,g[1]),Q=C.PolylinePipeline.generateArc({positions:g,granularity:t,ellipsoid:i}),_=O(Q,h,s,_),c&&(v.push(h.x,h.y,h.z),I.push(u.x,u.y,u.z)),A=e.Cartesian3.clone(V,A),h=e.Cartesian3.normalize(e.Cartesian3.cross(u,p,h),h),V=e.Cartesian3.add(R,e.Cartesian3.multiplyByScalar(h,2*s,V),V),T=e.Cartesian3.add(R,e.Cartesian3.multiplyByScalar(h,s,T),T),l===n.CornerType.ROUNDED||l===n.CornerType.BEVELED?j.push({leftPositions:M(R,A,V,l,r)}):j.push({leftPositions:D(q,e.Cartesian3.negate(m,m),V,r)})):(V=e.Cartesian3.add(q,m,V),U=e.Cartesian3.add(V,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(h,s,U),U),U),R=e.Cartesian3.add(V,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(h,2*s,R),R),R),g[0]=e.Cartesian3.clone(T,g[0]),g[1]=e.Cartesian3.clone(U,g[1]),Q=C.PolylinePipeline.generateArc({positions:g,granularity:t,ellipsoid:i}),_=O(Q,h,s,_),c&&(v.push(h.x,h.y,h.z),I.push(u.x,u.y,u.z)),A=e.Cartesian3.clone(R,A),h=e.Cartesian3.normalize(e.Cartesian3.cross(u,p,h),h),R=e.Cartesian3.add(V,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(h,2*s,R),R),R),T=e.Cartesian3.add(V,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(h,s,T),T),T),l===n.CornerType.ROUNDED||l===n.CornerType.BEVELED?j.push({rightPositions:M(V,A,R,l,r)}):j.push({rightPositions:D(q,m,R,r)})),d=e.Cartesian3.negate(p,d)}q=H}let k;return u=i.geodeticSurfaceNormal(q,u),g[0]=e.Cartesian3.clone(T,g[0]),g[1]=e.Cartesian3.clone(q,g[1]),Q=C.PolylinePipeline.generateArc({positions:g,granularity:t,ellipsoid:i}),_=O(Q,h,s,_),c&&(v.push(h.x,h.y,h.z),I.push(u.x,u.y,u.z)),l===n.CornerType.ROUNDED&&(k=function(a){let t=f,r=w,i=z,s=a[1];r=e.Cartesian3.fromArray(a[1],s.length-3,r),i=e.Cartesian3.fromArray(a[0],0,i),t=e.Cartesian3.midpoint(r,i,t);const o=M(t,r,i,n.CornerType.ROUNDED,!1),l=a.length-1,C=a[l-1];return s=a[l],r=e.Cartesian3.fromArray(C,C.length-3,r),i=e.Cartesian3.fromArray(s,0,i),t=e.Cartesian3.midpoint(r,i,t),[o,M(t,r,i,n.CornerType.ROUNDED,!1)]}(_)),{positions:_,corners:j,lefts:v,normals:I,endPositions:k}},p.computeInterpolationPositions=function(a){const n=a.positions,i=a.width/2,l=a.globe,C=l.ellipsoid,c=a.granularity,p=a.cornerLength,y=a.height,d=r.defaultValue(a.heightReference,u.NONE),h=Math.PI;if(n.length<2)return;let m=f,g=w,x=z;const N=E;let P=S,b=B;const A=new t.Cartographic,T=o.CesiumMath.toRadians(170),M=new s.EllipsoidGeodesic(null,null,C);let D=n[0],O=n[1],V=null;const U=[I(n[0])];m=e.Cartesian3.normalize(e.Cartesian3.subtract(O,D,m),m),g=e.Cartesian3.negate(m,g);const _=n.length-1;for(let a=1;a<_;a++){V=n[a+1],m=e.Cartesian3.normalize(e.Cartesian3.subtract(V,O,m),m),x=C.geodeticSurfaceNormal(O,x);const t=e.Cartesian3.multiplyByScalar(x,e.Cartesian3.dot(m,x),L);e.Cartesian3.subtract(m,t,t),e.Cartesian3.normalize(t,t);const r=e.Cartesian3.multiplyByScalar(x,e.Cartesian3.dot(g,x),G);e.Cartesian3.subtract(g,r,r),e.Cartesian3.normalize(r,r);const s=Math.acos(e.Cartesian3.dot(t,r));if(s<T){let a=(p/(h-s)+i)*Math.tan((h-s)/2);if(a<=0)continue;const t=e.Cartesian3.distance(O,V),n=e.Cartesian3.distance(O,D);a=Math.min(a,t,n),P=e.Cartesian3.add(O,e.Cartesian3.multiplyByScalar(g,a,P),P),b=e.Cartesian3.add(O,e.Cartesian3.multiplyByScalar(m,a,b),b),v(D,P);const r=Math.ceil(p/i*10),o=R([P,O,b],r);for(let a=0;a<r;a++)U.push(I(o[a],o[a]));U.push(I(b)),D=e.Cartesian3.clone(b,N)}else v(D,O),D=O;O=V,g=e.Cartesian3.negate(m,g)}v(D,O);for(let a=1;a<U.length;++a)e.Cartesian3.equals(U[a-1],U[a])&&(U.splice(a,1),--a);return U;function v(a,e){const t=C.cartesianToCartographic(a),n=C.cartesianToCartographic(e),r=n.height-t.height;M.setEndPoints(t,n);let i=c;const s=M.surfaceDistance;if(!(s<=0)){for(;i<s;)M.interpolateUsingSurfaceDistance(i,A),A.height=t.height+i/s*r,Q(A),U.push(C.cartographicToCartesian(A)),i+=c;U.push(I(e))}}function I(a,t){return r.defined(y)||d!==u.NONE?(C.cartesianToCartographic(a,A),Q(A),C.cartographicToCartesian(A,t)):t||e.Cartesian3.clone(a)}function Q(a){if(d===u.CLAMP_TO_GROUND){const e=l.getHeight(a);e&&(a.height=e)}else if("number"==typeof y)if(d===u.NONE)a.height=y;else if(d===u.RELATIVE_TO_GROUND){const e=l.getHeight(a);e?a.height=e+y:console.error("��ȡ���θ߳�ʧ�ܣ�")}}},p.ev_computePositions=function(a){const t=a.positions,n=a.width/2,s=r.defaultValue(a.ellipsoid,i.Ellipsoid.WGS84);let o=f,l=w,C=z;const c=[],u=[],p=[],y=[];let d=t[0],h=null;for(let a=1,r=t.length;a<r;a++)h=t[a],o=s.geodeticSurfaceNormal(d,o),l=e.Cartesian3.subtract(h,d,l),C=e.Cartesian3.normalize(e.Cartesian3.cross(o,l,C),C),C=e.Cartesian3.multiplyByScalar(C,n,C),c.push(e.Cartesian3.add(d,C,new e.Cartesian3)),u.push(e.Cartesian3.subtract(d,C,new e.Cartesian3)),p.push(o),y.push(o),d=h;for(c.push(e.Cartesian3.add(d,C,new e.Cartesian3)),u.push(e.Cartesian3.subtract(d,C,new e.Cartesian3)),p.push(o),y.push(o);c.length>0;)u.push(c.pop()),y.push(p.pop());return{positions:u,normals:y}};var V=p;a.CorridorGeometryLibrary=V}));
