define(["exports","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./defaultValue-f6d5e6da","./Math-355606c6","./Matrix2-e4a4609a","./Matrix3-31d1f01f"],(function(t,e,n,o,a,r,c,s){"use strict";const u={SCALAR:"SCALAR",VEC2:"VEC2",VEC3:"VEC3",VEC4:"VEC4",MAT2:"MAT2",MAT3:"MAT3",MAT4:"MAT4",getMathType:function(t){switch(t){case u.SCALAR:return Number;case u.VEC2:return e.Cartesian2;case u.VEC3:return n.Cartesian3;case u.VEC4:return e.Cartesian4;case u.MAT2:return c.Matrix2;case u.MAT3:return s.Matrix3;case u.MAT4:return e.Matrix4}},getNumberOfComponents:function(t){switch(t){case u.SCALAR:return 1;case u.VEC2:return 2;case u.VEC3:return 3;case u.VEC4:case u.MAT2:return 4;case u.MAT3:return 9;case u.MAT4:return 16}},getAttributeLocationCount:function(t){switch(t){case u.SCALAR:case u.VEC2:case u.VEC3:case u.VEC4:return 1;case u.MAT2:return 2;case u.MAT3:return 3;case u.MAT4:return 4}},getGlslType:function(t){switch(t){case u.SCALAR:return"float";case u.VEC2:return"vec2";case u.VEC3:return"vec3";case u.VEC4:return"vec4";case u.MAT2:return"mat2";case u.MAT3:return"mat3";case u.MAT4:return"mat4"}}};var i=Object.freeze(u);const C=1/256,M={octEncodeInRange:function(t,e,n){if(n.x=t.x/(Math.abs(t.x)+Math.abs(t.y)+Math.abs(t.z)),n.y=t.y/(Math.abs(t.x)+Math.abs(t.y)+Math.abs(t.z)),t.z<0){const t=n.x,e=n.y;n.x=(1-Math.abs(e))*r.CesiumMath.signNotZero(t),n.y=(1-Math.abs(t))*r.CesiumMath.signNotZero(e)}return n.x=r.CesiumMath.toSNorm(n.x,e),n.y=r.CesiumMath.toSNorm(n.y,e),n},octEncode:function(t,e){return M.octEncodeInRange(t,255,e)}},f=new e.Cartesian2,m=new Uint8Array(1);function y(t){return m[0]=t,m[0]}M.octEncodeToCartesian4=function(t,e){return M.octEncodeInRange(t,65535,f),e.x=y(f.x*C),e.y=y(f.x),e.z=y(f.y*C),e.w=y(f.y),e},M.octDecodeInRange=function(t,e,o,a){if(a.x=r.CesiumMath.fromSNorm(t,o),a.y=r.CesiumMath.fromSNorm(e,o),a.z=1-(Math.abs(a.x)+Math.abs(a.y)),a.z<0){const t=a.x;a.x=(1-Math.abs(a.y))*r.CesiumMath.signNotZero(t),a.y=(1-Math.abs(t))*r.CesiumMath.signNotZero(a.y)}return n.Cartesian3.normalize(a,a)},M.octDecode=function(t,e,n){return M.octDecodeInRange(t,e,255,n)},M.octDecodeFromCartesian4=function(t,e){const n=256*t.x+t.y,o=256*t.z+t.w;return M.octDecodeInRange(n,o,65535,e)},M.octPackFloat=function(t){return 256*t.x+t.y};const A=new e.Cartesian2;function d(t){return t>>1^-(1&t)}M.octEncodeFloat=function(t){return M.octEncode(t,A),M.octPackFloat(A)},M.octDecodeFloat=function(t,e){const n=t/256,o=Math.floor(n),a=256*(n-o);return M.octDecode(o,a,e)},M.octPack=function(t,e,n,o){const a=M.octEncodeFloat(t),r=M.octEncodeFloat(e),c=M.octEncode(n,A);return o.x=65536*c.x+a,o.y=65536*c.y+r,o},M.octUnpack=function(t,e,n,o){let a=t.x/65536;const r=Math.floor(a),c=65536*(a-r);a=t.y/65536;const s=Math.floor(a),u=65536*(a-s);M.octDecodeFloat(c,e),M.octDecodeFloat(u,n),M.octDecode(r,s,o)},M.compressTextureCoordinates=function(t){return 4096*(4095*t.x|0)+(4095*t.y|0)},M.decompressTextureCoordinates=function(t,e){const n=t/4096,o=Math.floor(n);return e.x=o/4095,e.y=(t-4096*o)/4095,e},M.zigZagDeltaDecode=function(t,e,n){const o=t.length;let r=0,c=0,s=0;for(let u=0;u<o;++u)r+=d(t[u]),c+=d(e[u]),t[u]=r,e[u]=c,a.defined(n)&&(s+=d(n[u]),n[u]=s)},M.dequantize=function(t,e,n,a){const r=i.getNumberOfComponents(n);let c;switch(e){case o.ComponentDatatype.BYTE:c=127;break;case o.ComponentDatatype.UNSIGNED_BYTE:c=255;break;case o.ComponentDatatype.SHORT:c=32767;break;case o.ComponentDatatype.UNSIGNED_SHORT:c=65535;break;case o.ComponentDatatype.INT:c=2147483647;break;case o.ComponentDatatype.UNSIGNED_INT:c=4294967295}const s=new Float32Array(a*r);for(let e=0;e<a;e++)for(let n=0;n<r;n++){const o=e*r+n;s[o]=Math.max(t[o]/c,-1)}return s},M.decodeRGB565=function(t,e){const n=t.length;a.defined(e)||(e=new Float32Array(3*n));const o=1/31;for(let a=0;a<n;a++){const n=t[a],r=n>>11,c=n>>5&63,s=31&n,u=3*a;e[u]=r*o,e[u+1]=.015873015873015872*c,e[u+2]=s*o}return e};var h=M;t.AttributeCompression=h}));
