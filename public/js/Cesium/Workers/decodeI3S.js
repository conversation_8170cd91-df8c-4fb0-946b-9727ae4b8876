define(["./createTaskProcessorWorker","./defaultValue-f6d5e6da","./WebMercatorProjection-03b5db31","./Ellipsoid-8e26549b","./Cartographic-dbefb6fa","./Cartesian3-529c236c","./Matrix3-31d1f01f","./Math-355606c6"],(function(e,t,n,r,o,i,a,s){"use strict";let u;function c(e,t,n,r){return r[e+t*n]}function f(e,t,n){const r=n.nativeExtent;let o=(e-r.west)/(r.east-r.west)*(n.width-1),i=(t-r.south)/(r.north-r.south)*(n.height-1);const a=Math.floor(o);let s=Math.floor(i);o-=a,i-=s;const u=a<n.width?a+1:a;let f=s<n.height?s+1:s;s=n.height-1-s,f=n.height-1-f;let l=function(e,t,n,r,o,i){return(n*(1-e)+r*e)*(1-t)+(o*(1-e)+i*e)*t}(o,i,c(a,s,n.width,n.buffer),c(u,s,n.width,n.buffer),c(a,f,n.width,n.buffer),c(u,f,n.width,n.buffer));return l=l*n.scale+n.offset,l}function l(e,t,a){for(let s=0;s<a.length;s++){const u=a[s].nativeExtent;let c=new i.Cartesian3;if("WebMercator"===a[s].projectionType){const i=a[s].projection._ellipsoid._radii;c=new n.WebMercatorProjection(new r.Ellipsoid(i.x,i.y,i.z)).project(new o.Cartographic(e,t,0))}else c.x=e,c.y=t;if(c.x>u.west&&c.x<u.east&&c.y>u.south&&c.y<u.north)return f(c.x,c.y,a[s])}return 0}function d(e,n,r,o){const i=new Uint8Array(e,0,5);return i[0]==="D".charCodeAt()&&i[1]==="R".charCodeAt()&&i[2]==="A".charCodeAt()&&i[3]==="C".charCodeAt()&&i[4]==="O".charCodeAt()?function(e){const n=u,r=new n.DecoderBuffer,o=new Uint8Array(e);r.Init(o,o.length);const i=new n.Decoder,a=i.GetEncodedGeometryType(r),s=new n.MetadataQuerier;let c,f;a===n.TRIANGULAR_MESH&&(c=new n.Mesh,f=i.DecodeBufferToMesh(r,c));const l={vertexCount:[0],featureCount:0};if(t.defined(f)&&f.ok()&&0!==c.ptr){const e=c.num_faces(),r=c.num_attributes(),o=c.num_points();l.indices=new Uint32Array(3*e);const a=l.indices;l.vertexCount[0]=o,l.scale_x=1,l.scale_y=1;const u=new n.DracoInt32Array(3);for(let t=0;t<e;++t)i.GetFaceFromMesh(c,t,u),a[3*t]=u.GetValue(0),a[3*t+1]=u.GetValue(1),a[3*t+2]=u.GetValue(2);n.destroy(u);for(let e=0;e<r;++e){const r=i.GetAttribute(c,e),a=y(n,i,c,r,o),u=r.attribute_type();let f="unknown";u===n.POSITION?f="positions":u===n.NORMAL?f="normals":u===n.COLOR?f="colors":u===n.TEX_COORD&&(f="uv0s");const d=i.GetAttributeMetadata(c,e);if(0!==d.ptr){const e=s.NumEntries(d);for(let t=0;t<e;++t){const e=s.GetEntryName(d,t);"i3s-scale_x"===e?l.scale_x=s.GetDoubleEntry(d,"i3s-scale_x"):"i3s-scale_y"===e?l.scale_y=s.GetDoubleEntry(d,"i3s-scale_y"):"i3s-attribute-type"===e&&(f=s.GetStringEntry(d,"i3s-attribute-type"))}}t.defined(l[f])&&console.log("Attribute already exists",f),l[f]=a,"feature-index"===f&&l.featureCount++}n.destroy(c)}return n.destroy(s),n.destroy(i),l}(e):function(e,n,r,o){const i={vertexCount:0},a=new DataView(e);try{let s=0;if(i.vertexCount=a.getUint32(s,1),s+=4,i.featureCount=a.getUint32(s,1),s+=4,t.defined(r))for(let n=0;n<r.attributes.length;n++)t.defined(h[r.attributes[n]])?s=h[r.attributes[n]](i,e,s):console.error("Unknown decoder for",r.attributes[n]);else{let r=n.ordering,a=n.featureAttributeOrder;t.defined(o)&&t.defined(o.geometryData)&&t.defined(o.geometryData[0])&&t.defined(o.geometryData[0].params)&&(r=Object.keys(o.geometryData[0].params.vertexAttributes),a=Object.keys(o.geometryData[0].params.featureAttributes));for(let n=0;n<r.length;n++){const o=h[r[n]];t.defined(o)||console.log(r[n]),s=o(i,e,s)}for(let n=0;n<a.length;n++){const r=h[a[n]];t.defined(r)||console.log(a[n]),s=r(i,e,s)}}}catch(e){console.error(e)}return i.scale_x=1,i.scale_y=1,i}(e,n,r,o)}function y(e,n,r,o,i){const a=o.num_components()*i;let s;const u=[function(){},function(){s=new e.DracoInt8Array(a);n.GetAttributeInt8ForAllPoints(r,o,s)||console.error("Bad stream");const t=new Int8Array(a);for(let e=0;e<a;++e)t[e]=s.GetValue(e);return t},function(){s=new e.DracoInt8Array(a);n.GetAttributeUInt8ForAllPoints(r,o,s)||console.error("Bad stream");const t=new Uint8Array(a);for(let e=0;e<a;++e)t[e]=s.GetValue(e);return t},function(){s=new e.DracoInt16Array(a);n.GetAttributeInt16ForAllPoints(r,o,s)||console.error("Bad stream");const t=new Int16Array(a);for(let e=0;e<a;++e)t[e]=s.GetValue(e);return t},function(){s=new e.DracoInt16Array(a);n.GetAttributeUInt16ForAllPoints(r,o,s)||console.error("Bad stream");const t=new Uint16Array(a);for(let e=0;e<a;++e)t[e]=s.GetValue(e);return t},function(){s=new e.DracoInt32Array(a);n.GetAttributeInt32ForAllPoints(r,o,s)||console.error("Bad stream");const t=new Int32Array(a);for(let e=0;e<a;++e)t[e]=s.GetValue(e);return t},function(){s=new e.DracoInt32Array(a);n.GetAttributeUInt32ForAllPoints(r,o,s)||console.error("Bad stream");const t=new Uint32Array(a);for(let e=0;e<a;++e)t[e]=s.GetValue(e);return t},function(){},function(){},function(){s=new e.DracoFloat32Array(a);n.GetAttributeFloatForAllPoints(r,o,s)||console.error("Bad stream");const t=new Float32Array(a);for(let e=0;e<a;++e)t[e]=s.GetValue(e);return t},function(){},function(){s=new e.DracoUInt8Array(a);n.GetAttributeUInt8ForAllPoints(r,o,s)||console.error("Bad stream");const t=new Uint8Array(a);for(let e=0;e<a;++e)t[e]=s.GetValue(e);return t}],c=u[o.data_type()]();return t.defined(s)&&e.destroy(s),c}const h={position:function(e,t,n){const r=3*e.vertexCount;return e.positions=new Float32Array(t,n,r),n+=4*r},normal:function(e,t,n){const r=3*e.vertexCount;return e.normals=new Float32Array(t,n,r),n+=4*r},uv0:function(e,t,n){const r=2*e.vertexCount;return e.uv0s=new Float32Array(t,n,r),n+=4*r},color:function(e,t,n){const r=4*e.vertexCount;return e.colors=new Uint8Array(t,n,r),n+=r},featureId:function(e,t,n){return n+=8*e.featureCount},id:function(e,t,n){return n+=8*e.featureCount},faceRange:function(e,t,n){const r=2*e.featureCount;return e.faceRange=new Uint32Array(t,n,r),n+=4*r},uvRegion:function(e,t,n){const r=4*e.vertexCount;return e["uv-region"]=new Uint16Array(t,n,r),n+=2*r},region:function(e,t,n){const r=4*e.vertexCount;return e["uv-region"]=new Uint16Array(t,n,r),n+=2*r}};function b(e){const n=d(e.binaryData,e.schema,e.bufferInfo,e.featureData);t.defined(e.geoidDataList)&&e.geoidDataList.length>0&&function(e,t,n,r,o,i,a){if(a)return;const u=l(o.longitude,o.latitude,i);for(let a=0;a<e;++a){const e=l(o.longitude+s.CesiumMath.toRadians(n*t[3*a]),o.latitude+s.CesiumMath.toRadians(r*t[3*a+1]),i);t[3*a+2]+=e-u}}(n.vertexCount,n.positions,n.scale_x,n.scale_y,e.cartographicCenter,e.geoidDataList,!1),function(e,n,u,c,f,l,d,y,h){if(0===e||!t.defined(n)||0===n.length)return;const b=new r.Ellipsoid(Math.sqrt(d.x),Math.sqrt(d.y),Math.sqrt(d.z));for(let r=0;r<e;++r){const e=3*r,d=e+1,p=e+2,g=new o.Cartographic;g.longitude=c.longitude+s.CesiumMath.toRadians(y*n[e]),g.latitude=c.latitude+s.CesiumMath.toRadians(h*n[d]),g.height=c.height+n[p];const m={};b.cartographicToCartesian(g,m),m.x-=f.x,m.y-=f.y,m.z-=f.z;const A={};if(a.Matrix3.multiplyByVector(l,m,A),n[e]=A.x,n[d]=A.y,n[p]=A.z,t.defined(u)){const t=new i.Cartesian3(u[e],u[d],u[p]),n={};a.Matrix3.multiplyByVector(l,t,n),u[e]=n.x,u[d]=n.y,u[p]=n.z}}}(n.vertexCount,n.positions,n.normals,e.cartographicCenter,e.cartesianCenter,e.parentRotation,e.ellipsoidRadiiSquare,n.scale_x,n.scale_y),t.defined(n.uv0s)&&t.defined(n["uv-region"])&&function(e,t,n){for(let r=0;r<e;++r){const e=n[4*r]/65535,o=n[4*r+1]/65535,i=(n[4*r+2]-n[4*r])/65535,a=(n[4*r+3]-n[4*r+1])/65535;t[2*r]*=i,t[2*r]+=e,t[2*r+1]*=a,t[2*r+1]+=o}}(n.vertexCount,n.uv0s,n["uv-region"]);const u=function(e,n,r,o,i,a){if(0===e||!t.defined(r)||0===r.length)return{buffers:[],bufferViews:[],accessors:[],meshes:[],nodes:[],nodesInScene:[]};const s=[],u=[],c=[],f=[],l=[],d=[];t.defined(n)&&(e=n.length);const y=new Uint32Array(e);if(t.defined(n))for(let t=0;t<e;++t)y[t]=n[t];else for(let t=0;t<e;++t)y[t]=t;const h=new Blob([y],{type:"application/binary"}),b=URL.createObjectURL(h),p=e,g=r.subarray(0,3*p),m=new Blob([g],{type:"application/binary"}),A=URL.createObjectURL(m);let w=Number.POSITIVE_INFINITY,I=Number.NEGATIVE_INFINITY,C=Number.POSITIVE_INFINITY,x=Number.NEGATIVE_INFINITY,L=Number.POSITIVE_INFINITY,R=Number.NEGATIVE_INFINITY;for(let e=0;e<g.length/3;e++)w=Math.min(w,g[3*e+0]),I=Math.max(I,g[3*e+0]),C=Math.min(C,g[3*e+1]),x=Math.max(x,g[3*e+1]),L=Math.min(L,g[3*e+2]),R=Math.max(R,g[3*e+2]);const O=o?o.subarray(0,3*p):void 0;let _;if(t.defined(O)){const e=new Blob([O],{type:"application/binary"});_=URL.createObjectURL(e)}const v=i?i.subarray(0,2*p):void 0;let G;if(t.defined(v)){const e=new Blob([v],{type:"application/binary"});G=URL.createObjectURL(e)}const M=t.defined(a)?a.subarray(0,4*p):void 0;let U;if(t.defined(M)){const e=new Blob([M],{type:"application/binary"});U=URL.createObjectURL(e)}let V=0,D=0,N=0,E=0,T=0;const F={POSITION:0};return s.push({uri:A,byteLength:g.byteLength}),u.push({buffer:0,byteOffset:0,byteLength:g.byteLength,target:34962}),c.push({bufferView:0,byteOffset:0,componentType:5126,count:e,type:"VEC3",max:[w,C,L],min:[I,x,R]}),t.defined(_)&&(++T,V=T,F.NORMAL=V,s.push({uri:_,byteLength:O.byteLength}),u.push({buffer:V,byteOffset:0,byteLength:O.byteLength,target:34962}),c.push({bufferView:V,byteOffset:0,componentType:5126,count:e,type:"VEC3"})),t.defined(G)&&(++T,D=T,F.TEXCOORD_0=D,s.push({uri:G,byteLength:v.byteLength}),u.push({buffer:D,byteOffset:0,byteLength:v.byteLength,target:34962}),c.push({bufferView:D,byteOffset:0,componentType:5126,count:e,type:"VEC2"})),t.defined(U)&&(++T,N=T,F.COLOR_0=N,s.push({uri:U,byteLength:M.byteLength}),u.push({buffer:N,byteOffset:0,byteLength:M.byteLength,target:34962}),c.push({bufferView:N,byteOffset:0,componentType:5121,normalized:!0,count:e,type:"VEC4"})),++T,E=T,s.push({uri:b,byteLength:y.byteLength}),u.push({buffer:E,byteOffset:0,byteLength:y.byteLength,target:34963}),c.push({bufferView:E,byteOffset:0,componentType:5125,count:e,type:"SCALAR"}),f.push({primitives:[{attributes:F,indices:E,material:0}]}),d.push(0),l.push({mesh:0}),{buffers:s,bufferViews:u,accessors:c,meshes:f,nodes:l,nodesInScene:d}}(n.vertexCount,n.indices,n.positions,n.normals,n.uv0s,n.colors),c={};if(t.defined(n["feature-index"]))c.positions=n.positions,c.indices=n.indices,c.featureIndex=n["feature-index"],c.cartesianCenter=e.cartesianCenter,c.parentRotation=e.parentRotation;else if(t.defined(n.faceRange)){c.positions=n.positions,c.indices=n.indices,c.sourceURL=e.url,c.cartesianCenter=e.cartesianCenter,c.parentRotation=e.parentRotation,c.featureIndex=new Array(n.positions.length);for(let e=0;e<n.faceRange.length-1;e+=2){const t=e/2,r=n.faceRange[e],o=n.faceRange[e+1];for(let e=r;e<=o;e++)c.featureIndex[3*e]=t,c.featureIndex[3*e+1]=t,c.featureIndex[3*e+2]=t}}u._customAttributes=c;return{meshData:u}}function p(t){u=t,self.onmessage=e(b),self.postMessage(!0)}return function(e){const n=e.data.webAssemblyConfig;if(t.defined(n))return require([n.modulePath],(function(e){t.defined(n.wasmBinaryFile)?(t.defined(e)||(e=self.DracoDecoderModule),e(n).then((function(e){p(e)}))):p(e())}))}}));
