define(["exports","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Transforms-2afbbfb5","./Math-355606c6","./Matrix2-e4a4609a","./Rectangle-98b0bef0"],(function(t,n,a,e,r,o,s,i){"use strict";const c=Math.cos,g=Math.sin,h=Math.sqrt,u={computePosition:function(t,n,a,r,o,s,i){const u=n.radiiSquared,l=t.nwCorner,C=t.boundingRectangle;let S=l.latitude-t.granYCos*r+o*t.granXSin;const d=c(S),M=g(S),w=u.z*M;let X=l.longitude+r*t.granYSin+o*t.granXCos;const Y=d*c(X),m=d*g(X),f=u.x*Y,p=u.y*m,x=h(f*Y+p*m+w*M);if(s.x=f/x,s.y=p/x,s.z=w/x,a){const n=t.stNwCorner;e.defined(n)?(S=n.latitude-t.stGranYCos*r+o*t.stGranXSin,X=n.longitude+r*t.stGranYSin+o*t.stGranXCos,i.x=(X-t.stWest)*t.lonScalar,i.y=(S-t.stSouth)*t.latScalar):(i.x=(X-C.west)*t.lonScalar,i.y=(S-C.south)*t.latScalar)}}},l=new s.Matrix2;let C=new n.Cartesian3;const S=new a.Cartographic;let d=new n.Cartesian3;const M=new r.GeographicProjection;function w(t,a,e,r,o,i,c){const g=Math.cos(a),h=r*g,u=e*g,S=Math.sin(a),w=r*S,X=e*S;C=M.project(t,C),C=n.Cartesian3.subtract(C,d,C);const Y=s.Matrix2.fromRotation(a,l);C=s.Matrix2.multiplyByVector(Y,C,C),C=n.Cartesian3.add(C,d,C),i-=1,c-=1;const m=(t=M.unproject(C,t)).latitude,f=m+i*X,p=m-h*c,x=m-h*c+i*X,R=Math.max(m,f,p,x),G=Math.min(m,f,p,x),y=t.longitude,O=y+i*u,P=y+c*w,W=y+c*w+i*u;return{north:R,south:G,east:Math.max(y,O,P,W),west:Math.min(y,O,P,W),granYCos:h,granYSin:w,granXCos:u,granXSin:X,nwCorner:t}}u.computeOptions=function(t,n,a,e,r,s,c){let g,h=t.east,u=t.west,l=t.north,C=t.south,X=!1,Y=!1;l===o.CesiumMath.PI_OVER_TWO&&(X=!0),C===-o.CesiumMath.PI_OVER_TWO&&(Y=!0);const m=l-C;g=u>h?o.CesiumMath.TWO_PI-u+h:h-u;const f=Math.ceil(g/n)+1,p=Math.ceil(m/n)+1,x=g/(f-1),R=m/(p-1),G=i.Rectangle.northwest(t,s),y=i.Rectangle.center(t,S);0===a&&0===e||(y.longitude<G.longitude&&(y.longitude+=o.CesiumMath.TWO_PI),d=M.project(y,d));const O=R,P=x,W=i.Rectangle.clone(t,r),_={granYCos:O,granYSin:0,granXCos:P,granXSin:0,nwCorner:G,boundingRectangle:W,width:f,height:p,northCap:X,southCap:Y};if(0!==a){const t=w(G,a,x,R,0,f,p);l=t.north,C=t.south,h=t.east,u=t.west,_.granYCos=t.granYCos,_.granYSin=t.granYSin,_.granXCos=t.granXCos,_.granXSin=t.granXSin,W.north=l,W.south=C,W.east=h,W.west=u}if(0!==e){a-=e;const t=i.Rectangle.northwest(W,c),n=w(t,a,x,R,0,f,p);_.stGranYCos=n.granYCos,_.stGranXCos=n.granXCos,_.stGranYSin=n.granYSin,_.stGranXSin=n.granXSin,_.stNwCorner=t,_.stWest=n.west,_.stSouth=n.south}return _};var X=u;t.RectangleGeometryLibrary=X}));
