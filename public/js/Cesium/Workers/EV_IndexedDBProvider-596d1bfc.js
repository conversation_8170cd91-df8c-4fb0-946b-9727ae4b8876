define(["exports","./defaultValue-f6d5e6da","./DeveloperError-c85858c1"],(function(t,e,a){"use strict";var o=Object.freeze({TERRAIN:0,IMAGERY:1,TILES3D:2,BIN:3,IMAGE:4,FILE:5});const n=["model","imagery","terrain","3DTile","gltfBin","images","file"];function i(t){this._windowIDB=e.defaultValue(t,void 0),this._iDB=void 0,this.dataName="EVIDB",this.version=2,this._dbDivides=10,this._dbDivideTable={},this._dbTransaction={},this._isDbTransactionClock=!1,this._dbTransactionQueue=[],this._dbLoadDatas={},this._dbSaveDatas={}}Object.defineProperties(i.prototype,{iDB:{get:function(){return this._iDB}}}),i.prototype.createIDB=function(t){const o=this.dataName,i=this.version;if(e.defined(this._windowIDB)){const s=this,r=this._windowIDB.open(o,i);r.onupgradeneeded=function(t){const e=t.target.result;for(let t=0;t<n.length;t++)if(!e.objectStoreNames.contains(n[t])){const a=e.createObjectStore(n[t],{keyPath:"key",autoIncrement:!0});"model"===n[t]?a.createIndex("name","name",{unique:!1}):a.createIndex("name","name",{unique:!0})}for(let t=0;t<s._dbDivides;t++){const a="imagery_"+t;e.objectStoreNames.contains(a)||e.createObjectStore(a,{keyPath:"name"})}},r.onsuccess=function(a){const o=a.target.result;if(!s._dbDivideTable.imagery){s._dbDivideTable.imagery=[];for(let t=0;t<s._dbDivides;t++)s._dbDivideTable.imagery.push("imagery_"+t)}s._iDB=o,e.defined(t)&&t()},r.onerror=function(t){throw new a.DeveloperError("indexDB Open failed")}}},i.closeIDB=function(t,a){e.defined(this._iDB)&&this._iDB.name===t&&this._iDB.version===a&&this._iDB.close()};const s=67108864;i.prototype.saveModelArrayBuffer=function(t,a,o,n,i){if(e.defined(this._iDB)&&this._iDB.name===t&&this._iDB.version===a)if(this._iDB.objectStoreNames.contains(o)){const t=this._iDB.transaction([o],"readwrite").objectStore(o);let e=0,a=0;const r=Math.ceil(i.byteLength/s);for(let o=0;o<r;o++){a+=s;const r={name:n,chunkId:o,data:i.slice(e,a)},c=t.put(r);e=a,c.onsuccess=function(t){console.log("arraybuffer writed success")},c.onerror=function(t){console.log("arraybuffer writed error")}}}else console.log(`${o}:该表不存在!`);else console.log(`${t}:保存数据有问题!`)},i.prototype.downloadModelArrayBuffer=function(t,a,o,n,i,s,r){if(e.defined(this._iDB)&&this._iDB.name===t&&this._iDB.version===a)if(this._iDB.objectStoreNames.contains(o)){const t=this._iDB.transaction([o],"readonly").objectStore(o),e=i,a=n,c=new Array;let d=0;const l=t.index("name").openCursor(IDBKeyRange.only(n));l.onsuccess=function(t){const o=t.target.result;if(o){const t=o.value;if(t.name===a){const e=new Uint8Array(t.data);c.push(e),d+=e.byteLength}o.continue()}else{let t=0;const a=new Uint8Array(d);for(let e=0;e<c.length;e++)a.set(c[e],t),t+=c[e].length;c.length>0?r(e,a):s(e)}},l.onerror=function(t){console.log("索引查询失败!")}}else s(i);else console.log(`${t}:下载数据有问题!`)},i.prototype.saveTerrainData=function(t,a){const o="terrain";if(e.defined(this._iDB))if(this._iDB.objectStoreNames.contains(o)){if(!t.data)return void console.log("数据为空");const n={name:t.keyName,level:t.level,x:t.x,y:t.y,data:t.data,upsample:e.defaultValue(t.upsample,!1)},i=(new Date).getTime();this._dbSaveDatas[o]||(this._dbSaveDatas[o]=[]),this._dbSaveDatas[o].push({terrainData:n,callback:a,timestamp:i}),this.initTransactionAndLoadData(o,"readwrite",this.saveTerrainDatas)}else console.log(`${o}:该表不存在!`);else console.log(`${t}:保存数据有问题!`)},i.prototype.saveTerrainDatas=function(t){const e=this._dbTransaction[t+"_readwrite"].objectStore(t);this._dbSaveDatas[t].forEach((t=>{const a=t.terrainData,n=t.callback;t.timestamp;const i=e.put(a);i.onsuccess=function(t){n(o.TERRAIN,1,{keyName:a.name})},i.onerror=function(t){n(o.TERRAIN,0,{keyName:a.name})}})),this._dbSaveDatas[t]=[]},i.prototype.downloadTerrainData=function(t,a){const o="terrain";if(e.defined(this._iDB)){if(this._iDB.objectStoreNames.contains(o)){const e=(new Date).getTime();this._dbLoadDatas[o]||(this._dbLoadDatas[o]=[]),this._dbLoadDatas[o].push({terrainData:t,callback:a,timestamp:e}),this.initTransactionAndLoadData(o,"readonly",this.getTerrainDatas)}}else console.log(`${t}:下载数据有问题!`)},i.prototype.getTerrainDatas=function(t){const e=this._dbTransaction[t+"_readonly"].objectStore(t);this._dbLoadDatas[t].forEach((t=>{const a=t.terrainData,n=t.callback;t.timestamp;const i=e.index("name").openCursor(a.keyName);i.onsuccess=function(t){const e=t.target.result;e&&e.value.data.buffer?(a.level=e.value.level,a.x=e.value.x,a.y=e.value.y,a.upsample=e.value.upsample,a.data=e.value.data,n(o.TERRAIN,1,a)):n(o.TERRAIN,0,a)},i.onerror=function(t){console.log("索引查询失败!")}})),this._dbLoadDatas[t]=[]},i.prototype.saveImagery=function(t,a){const o="imagery",n=o+"_"+(t.x+t.y)%this._dbDivides,i=(new Date).getTime();if(e.defined(this._iDB))if(this._iDB.objectStoreNames.contains(n)){const s={name:t.keyName,level:t.level,x:t.x,y:t.y,data:t.data,upsample:e.defaultValue(t.upsample,!1)};this._dbSaveDatas[o]||(this._dbSaveDatas[o]={}),this._dbSaveDatas[o][n]||(this._dbSaveDatas[o][n]=[]),this._dbSaveDatas[o][n].push({terrainData:s,callback:a,timestamp:i}),this.initTransactionAndLoadData("imagery","readwrite",this.saveImageryDatas)}else console.log(`${n}:该表不存在!`);else console.log(`${t}:保存数据有问题!`)},i.prototype.saveImageryDatas=function(t){for(var e in this._dbSaveDatas[t]){const a=this._dbTransaction[t+"_readwrite"].objectStore(e);this._dbSaveDatas[t][e].forEach((t=>{const e=t.terrainData,n=t.callback;t.timestamp;const i=a.put(e);i.onsuccess=function(t){n(o.IMAGERY,1)},i.onerror=function(t){n(o.IMAGERY,0)}})),this._dbSaveDatas[t][e]=[]}},i.prototype.downloadImagery=function(t,a){const o="imagery",n=o+"_"+(t.x+t.y)%this._dbDivides;if(e.defined(this._iDB)){if(this._iDB.objectStoreNames.contains(n)){const e=(new Date).getTime();this._dbLoadDatas[o]||(this._dbLoadDatas[o]={}),this._dbLoadDatas[o][n]||(this._dbLoadDatas[o][n]=[]),this._dbLoadDatas[o][n].push({terrainData:t,callback:a,timestamp:e}),this.initTransactionAndLoadData("imagery","readonly",this.getImageryDatas)}}else console.log(`${t}:下载数据有问题!`)},i.prototype.getImageryDatas=function(t){for(var e in this._dbLoadDatas[t]){const a=this._dbTransaction[t+"_readonly"].objectStore(e);this._dbLoadDatas[t][e].forEach((t=>{const e=t.terrainData,n=t.callback;t.timestamp;const i=a.get(e.keyName);i.onsuccess=function(t){const a=t.target.result;a?(e.level=a.level,e.x=a.x,e.y=a.y,e.upsample=a.upsample,e.data=a.data,n(o.IMAGERY,1,e)):n(o.IMAGERY,0,e)},i.onerror=function(t){console.log("索引查询失败!")}})),this._dbLoadDatas[t][e]=[]}},i.prototype.save3DTileArrayBuffer=function(t,e){const a="3DTile";if(this._iDB.objectStoreNames.contains(a)){const o={name:t.keyName,meshPrimitives:t.meshPrimitives,data:t.data},n=(new Date).getTime();this._dbSaveDatas[a]||(this._dbSaveDatas[a]=[]),this._dbSaveDatas[a].push({terrainData:o,callback:e,timestamp:n}),this.initTransactionAndLoadData(a,"readwrite",this.save3DTileDatas)}else console.log(`${a}:该表不存在!`)},i.prototype.save3DTileDatas=function(t){const e=this._dbTransaction[t+"_readwrite"].objectStore(t);this._dbSaveDatas[t].forEach((t=>{let a=t.terrainData;const n=t.callback;t.timestamp;const i=e.put(a);i.onsuccess=function(t){a.data=void 0,a.meshPrimitives=[],a=void 0,n(o.TILES3D,1)},i.onerror=function(t){n(o.TILES3D,0)}})),this._dbSaveDatas[t]=[]},i.prototype.download3DTileArrayBuffer=function(t,e){const a="3DTile";if(this._iDB.objectStoreNames.contains(a)){const o=(new Date).getTime();this._dbLoadDatas[a]||(this._dbLoadDatas[a]=[]),this._dbLoadDatas[a].push({terrainData:t,callback:e,timestamp:o}),this.initTransactionAndLoadData(a,"readonly",this.get3DTileDatas)}else console.log(`${a}不存在!`)},i.prototype.get3DTileDatas=function(t){const a=this._dbTransaction[t+"_readonly"].objectStore(t);this._dbLoadDatas[t].forEach((t=>{const n=t.terrainData,i=t.callback;t.timestamp;const s=a.index("name").openCursor(n.keyName);s.onsuccess=function(t){const a=t.target.result;e.defined(a)?(n.data=a.value.data,n.meshPrimitives=a.value.meshPrimitives,i(o.TILES3D,1,n)):i(o.TILES3D,0,n)},s.onerror=function(t){console.log("索引查询失败!")}})),this._dbLoadDatas[t]=[]},i.prototype.saveGltfBin=function(t,e){const a="gltfBin";if(this._iDB.objectStoreNames.contains(a)){const n=this._iDB.transaction([a],"readwrite").objectStore(a),i={name:t.keyName,data:t.data},s=n.put(i);s.onsuccess=function(a){t.data=void 0,t=void 0,e(o.BIN,1)},s.onerror=function(t){e(o.BIN,0)}}else console.log(`${a}:该表不存在!`)},i.prototype.downloadGltfBin=function(t,a){const n="gltfBin";if(this._iDB.objectStoreNames.contains(n)){const i=this._iDB.transaction([n],"readonly").objectStore(n).index("name").openCursor(IDBKeyRange.only(t.keyName));i.onsuccess=function(n){const i=n.target.result;e.defined(i)?(t.data=i.value.data,a(o.BIN,1,t)):a(o.BIN,0,t)},i.onerror=function(t){console.log("索引查询失败!")}}},i.prototype.saveFile=function(t,e){const a="file";if(this._iDB.objectStoreNames.contains(a)){const n=this._iDB.transaction([a],"readwrite").objectStore(a),i={name:t.keyName,data:t.data},s=n.put(i);s.onsuccess=function(a){t.data=void 0,t=void 0,e(o.FILE,1)},s.onerror=function(t){e(o.FILE,0)}}else console.log(`${a}:该表不存在!`)},i.prototype.downloadFile=function(t,a){const n="file";if(this._iDB.objectStoreNames.contains(n)){const i=this._iDB.transaction([n],"readonly").objectStore(n).index("name").openCursor(IDBKeyRange.only(t.keyName));i.onsuccess=function(n){const i=n.target.result;e.defined(i)?(t.data=i.value.data,a(o.FILE,1,t)):a(o.FILE,0,t)},i.onerror=function(t){console.log("索引查询失败!")}}},i.prototype.saveImage=function(t,e){const a="images";if(this._iDB.objectStoreNames.contains(a)){const n=this._iDB.transaction([a],"readwrite").objectStore(a),i={name:t.keyName,data:t.data},s=n.put(i);s.onsuccess=function(a){t.data=void 0,t=void 0,e(o.IMAGE,1)},s.onerror=function(t){e(o.IMAGE,0)}}else console.log(`${a}:该表不存在!`)},i.prototype.downloadImage=function(t,a){const n="images";if(this._iDB.objectStoreNames.contains(n)){const i=this._iDB.transaction([n],"readonly").objectStore(n).index("name").openCursor(IDBKeyRange.only(t.keyName));i.onsuccess=function(n){const i=n.target.result;e.defined(i)?(t.data=i.value.data,a(o.IMAGE,1,t)):a(o.IMAGE,0,t)},i.onerror=function(t){console.log("索引查询失败!")}}},i.prototype.initTransactionAndLoadData=function(t,e,a){if(-1==this._dbTransactionQueue.findIndex((a=>a.tableName==t&&a.type==e))&&this._dbTransactionQueue.push({tableName:t,type:e,callback:a}),this._dbTransactionQueue.length>0&&!this._isDbTransactionClock){this._isDbTransactionClock=!0;const t=this._dbTransactionQueue.shift(),e=t.tableName,a=t.type,o=t.callback;let n=e+"_"+a,i=this;this._dbTransaction[n]=this._iDB.transaction("imagery"==e?this._dbDivideTable.imagery:[e],a),this._dbTransaction[n].oncomplete=t=>{i._dbTransaction[n]=void 0,i._isDbTransactionClock=!1,i._dbTransactionQueue.length>0&&i.initTransactionAndLoadData(i._dbTransactionQueue[0].tableName,i._dbTransactionQueue[0].type,i._dbTransactionQueue[0].callback)},this._dbTransaction[n].onerror=t=>{i._dbTransaction[n]=void 0,i._isDbTransactionClock=!1,i._dbTransactionQueue.length>0&&i.initTransactionAndLoadData(i._dbTransactionQueue[0].tableName,i._dbTransactionQueue[0].type,i._dbTransactionQueue[0].callback)},o.call(this,e)}},t.EV_IndexDBType=o,t.EV_IndexedDBProvider=i}));
