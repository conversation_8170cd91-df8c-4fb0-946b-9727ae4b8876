define(["./defaultValue-f6d5e6da","./createTaskProcessorWorker","./DeveloperError-c85858c1"],(function(e,o,n){"use strict";const t=[],s=self.postMessage;function r(e){isNaN(Number(e))?s(e):(t.push(e),2===t.length&&s(t))}let i,c,l;function u(o,t){if(!e.defined(o))throw new n.DeveloperError("wsserver undefined!");this.url=o,this.startPlay=0,this.callbackFunc=t}return u.prototype.onOpen=function(e){this.websocket.binaryType="arraybuffer",console.log("Connection open ..."),this.doSend(e)},u.prototype.doSend=function(e){i?this.websocket.send(i+""):this.websocket.send(e)},u.prototype.onMessage=function(e){this.callbackFunc(e.data)},u.prototype.onClose=function(){console.log("DISCONNECTED")},u.prototype.onError=function(e){console.log("websocket error")},o((function(o){const n=o.url;i=o.firstTime,l=o.rate,e.defined(n)?(c=new u(n,r),c.websocket=new WebSocket(n),c.websocket.onopen=c.onOpen.bind(c),c.websocket.onclose=c.onClose.bind(c),c.websocket.onmessage=c.onMessage.bind(c),c.websocket.onerror=c.onError.bind(c)):null==n&&null!=c&&null!=i?c.websocket.send(JSON.stringify({firstTime:i})):null==n&&null!=c&&null==i&&null!=l&&c.websocket.send(JSON.stringify({rate:l}))}))}));
