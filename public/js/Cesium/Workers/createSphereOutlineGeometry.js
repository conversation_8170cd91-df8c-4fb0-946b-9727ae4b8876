define(["./defaultValue-f6d5e6da","./Cartesian3-529c236c","./EllipsoidOutlineGeometry-bba37f0d","./Math-355606c6","./Transforms-2afbbfb5","./Cartographic-dbefb6fa","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./GeometryAttribute-f7a0845b","./Matrix2-e4a4609a","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-58eb7805"],(function(e,i,t,r,n,o,s,a,l,u,d,c,p,m,y,G,f,k,b,v,E,O){"use strict";function P(r){const n=e.defaultValue(r.radius,1),o={radii:new i.Cartesian3(n,n,n),stackPartitions:r.stackPartitions,slicePartitions:r.slicePartitions,subdivisions:r.subdivisions};this._ellipsoidGeometry=new t.EllipsoidOutlineGeometry(o),this._workerName="createSphereOutlineGeometry"}P.packedLength=t.EllipsoidOutlineGeometry.packedLength,P.pack=function(e,i,r){return t.EllipsoidOutlineGeometry.pack(e._ellipsoidGeometry,i,r)};const _=new t.EllipsoidOutlineGeometry,h={radius:void 0,radii:new i.Cartesian3,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0};return P.unpack=function(r,n,o){const s=t.EllipsoidOutlineGeometry.unpack(r,n,_);return h.stackPartitions=s._stackPartitions,h.slicePartitions=s._slicePartitions,h.subdivisions=s._subdivisions,e.defined(o)?(i.Cartesian3.clone(s._radii,h.radii),o._ellipsoidGeometry=new t.EllipsoidOutlineGeometry(h),o):(h.radius=s._radii.x,new P(h))},P.createGeometry=function(e){return t.EllipsoidOutlineGeometry.createGeometry(e._ellipsoidGeometry)},function(i,t){return e.defined(t)&&(i=P.unpack(i,t)),P.createGeometry(i)}}));
