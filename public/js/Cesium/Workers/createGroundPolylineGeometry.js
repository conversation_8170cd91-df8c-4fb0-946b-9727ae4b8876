define(["./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./Math-355606c6","./Rectangle-98b0bef0","./ArcType-26a3f38d","./arrayRemoveDuplicates-0d8dde26","./ComponentDatatype-ab629b88","./EllipsoidGeodesic-8ac7b85d","./EllipsoidRhumbLine-6774fec3","./EncodedCartesian3-94199dac","./GeometryAttribute-f7a0845b","./IntersectionTests-01432fe7","./Matrix3-31d1f01f","./Plane-06f34fae","./WebMercatorProjection-03b5db31","./Interval-d6c8d27a","./DeveloperError-c85858c1","./combine-0c102d93","./RequestType-735c98f2","./RuntimeError-9b4ce3fb","./WebGLConstants-7f557f93","./Matrix2-e4a4609a"],(function(e,t,a,n,i,r,s,o,l,c,u,C,p,h,d,g,f,m,w,y,M,T,E,_,O,P){"use strict";function b(t){t=i.defaultValue(t,i.defaultValue.EMPTY_OBJECT),this._ellipsoid=i.defaultValue(t.ellipsoid,r.Ellipsoid.WGS84),this._rectangle=i.defaultValue(t.rectangle,o.Rectangle.MAX_VALUE),this._projection=new e.GeographicProjection(this._ellipsoid),this._numberOfLevelZeroTilesX=i.defaultValue(t.numberOfLevelZeroTilesX,2),this._numberOfLevelZeroTilesY=i.defaultValue(t.numberOfLevelZeroTilesY,1)}Object.defineProperties(b.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},rectangle:{get:function(){return this._rectangle}},projection:{get:function(){return this._projection}}}),b.prototype.getNumberOfXTilesAtLevel=function(e){return this._numberOfLevelZeroTilesX<<e},b.prototype.getNumberOfYTilesAtLevel=function(e){return this._numberOfLevelZeroTilesY<<e},b.prototype.rectangleToNativeRectangle=function(e,t){const a=s.CesiumMath.toDegrees(e.west),n=s.CesiumMath.toDegrees(e.south),r=s.CesiumMath.toDegrees(e.east),l=s.CesiumMath.toDegrees(e.north);return i.defined(t)?(t.west=a,t.south=n,t.east=r,t.north=l,t):new o.Rectangle(a,n,r,l)},b.prototype.tileXYToNativeRectangle=function(e,t,a,n){const i=this.tileXYToRectangle(e,t,a,n);return i.west=s.CesiumMath.toDegrees(i.west),i.south=s.CesiumMath.toDegrees(i.south),i.east=s.CesiumMath.toDegrees(i.east),i.north=s.CesiumMath.toDegrees(i.north),i},b.prototype.tileXYToRectangle=function(e,t,a,n){const r=this._rectangle,s=this.getNumberOfXTilesAtLevel(a),l=this.getNumberOfYTilesAtLevel(a),c=r.width/s,u=e*c+r.west,C=(e+1)*c+r.west,p=r.height/l,h=r.north-t*p,d=r.north-(t+1)*p;return i.defined(n)||(n=new o.Rectangle(u,d,C,h)),n.west=u,n.south=d,n.east=C,n.north=h,n},b.prototype.positionToTileXY=function(e,a,n){const r=this._rectangle;if(!o.Rectangle.contains(r,e))return;const l=this.getNumberOfXTilesAtLevel(a),c=this.getNumberOfYTilesAtLevel(a),u=r.width/l,C=r.height/c;let p=e.longitude;r.east<r.west&&(p+=s.CesiumMath.TWO_PI);let h=(p-r.west)/u|0;h>=l&&(h=l-1);let d=(r.north-e.latitude)/C|0;return d>=c&&(d=c-1),i.defined(n)?(n.x=h,n.y=d,n):new t.Cartesian2(h,d)};const A=new a.Cartesian3,k=new a.Cartesian3,L=new n.Cartographic,S=new a.Cartesian3,x=new a.Cartesian3,I=new e.BoundingSphere,N=new b,R=[new n.Cartographic,new n.Cartographic,new n.Cartographic,new n.Cartographic],D=new t.Cartesian2,v={};function z(e){n.Cartographic.fromRadians(e.east,e.north,0,R[0]),n.Cartographic.fromRadians(e.west,e.north,0,R[1]),n.Cartographic.fromRadians(e.east,e.south,0,R[2]),n.Cartographic.fromRadians(e.west,e.south,0,R[3]);let t=0,a=0,i=0,r=0;const s=v._terrainHeightsMaxLevel;let o;for(o=0;o<=s;++o){let e=!1;for(let t=0;t<4;++t){const a=R[t];if(N.positionToTileXY(a,o,D),0===t)i=D.x,r=D.y;else if(i!==D.x||r!==D.y){e=!0;break}}if(e)break;t=i,a=r}if(0!==o)return{x:t,y:a,level:o>s?s:o-1}}v.initialize=function(){let t=v._initPromise;return i.defined(t)||(t=e.Resource.fetchJson(e.buildModuleUrl("Assets/approximateTerrainHeights.json")).then((function(e){v._terrainHeights=e})),v._initPromise=t),t},v.getMinimumMaximumHeights=function(e,t){t=i.defaultValue(t,r.Ellipsoid.WGS84);const n=z(e);let s=v._defaultMinTerrainHeight,l=v._defaultMaxTerrainHeight;if(i.defined(n)){const r=`${n.level}-${n.x}-${n.y}`,c=v._terrainHeights[r];i.defined(c)&&(s=c[0],l=c[1]),t.cartographicToCartesian(o.Rectangle.northeast(e,L),A),t.cartographicToCartesian(o.Rectangle.southwest(e,L),k),a.Cartesian3.midpoint(k,A,S);const u=t.scaleToGeodeticSurface(S,x);if(i.defined(u)){const e=a.Cartesian3.distance(S,u);s=Math.min(s,-e)}else s=v._defaultMinTerrainHeight}return s=Math.max(v._defaultMinTerrainHeight,s),{minimumTerrainHeight:s,maximumTerrainHeight:l}},v.getBoundingSphere=function(t,a){a=i.defaultValue(a,r.Ellipsoid.WGS84);const n=z(t);let s=v._defaultMaxTerrainHeight;if(i.defined(n)){const e=`${n.level}-${n.x}-${n.y}`,t=v._terrainHeights[e];i.defined(t)&&(s=t[1])}const o=e.BoundingSphere.fromRectangle3D(t,a,0);return e.BoundingSphere.fromRectangle3D(t,a,s,I),e.BoundingSphere.union(o,I,o)},v._terrainHeightsMaxLevel=6,v._defaultMaxTerrainHeight=9e3,v._defaultMinTerrainHeight=-1e5,v._terrainHeights=void 0,v._initPromise=void 0,Object.defineProperties(v,{initialized:{get:function(){return i.defined(v._terrainHeights)}}});var H=v;const j=[e.GeographicProjection,w.WebMercatorProjection],B=j.length,V=Math.cos(s.CesiumMath.toRadians(30)),G=Math.cos(s.CesiumMath.toRadians(150)),Y=0,q=1e3;function F(e){const t=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).positions;this.width=i.defaultValue(e.width,1),this._positions=t,this.granularity=i.defaultValue(e.granularity,9999),this.loop=i.defaultValue(e.loop,!1),this.arcType=i.defaultValue(e.arcType,l.ArcType.GEODESIC),this._ellipsoid=r.Ellipsoid.WGS84,this._projectionIndex=0,this._workerName="createGroundPolylineGeometry",this._scene3DOnly=!1}Object.defineProperties(F.prototype,{packedLength:{get:function(){return 1+3*this._positions.length+1+1+1+r.Ellipsoid.packedLength+1+1}}}),F.setProjectionAndEllipsoid=function(e,t){let a=0;for(let e=0;e<B;e++)if(t instanceof j[e]){a=e;break}e._projectionIndex=a,e._ellipsoid=t.ellipsoid};const X=new a.Cartesian3,W=new a.Cartesian3,U=new a.Cartesian3;function Z(e,t,n,i,r){const s=ae(i,e,0,X),o=ae(i,e,n,W),l=ae(i,t,0,U),c=ne(o,s,W),u=ne(l,s,U);return a.Cartesian3.cross(u,c,r),a.Cartesian3.normalize(r,r)}const $=new n.Cartographic,J=new a.Cartesian3,Q=new a.Cartesian3,K=new a.Cartesian3;function ee(e,t,n,i,r,s,o,c,u,h,d){if(0===r)return;let g;s===l.ArcType.GEODESIC?g=new C.EllipsoidGeodesic(e,t,o):s===l.ArcType.RHUMB&&(g=new p.EllipsoidRhumbLine(e,t,o));const f=g.surfaceDistance;if(f<r)return;const m=Z(e,t,i,o,K),w=Math.ceil(f/r),y=f/w;let M=y;const T=w-1;let E=c.length;for(let e=0;e<T;e++){const e=g.interpolateUsingSurfaceDistance(M,$),t=ae(o,e,n,J),r=ae(o,e,i,Q);a.Cartesian3.pack(m,c,E),a.Cartesian3.pack(t,u,E),a.Cartesian3.pack(r,h,E),d.push(e.latitude),d.push(e.longitude),E+=3,M+=y}}const te=new n.Cartographic;function ae(e,t,a,i){return n.Cartographic.clone(t,te),te.height=a,n.Cartographic.toCartesian(te,e,i)}function ne(e,t,n){return a.Cartesian3.subtract(e,t,n),a.Cartesian3.normalize(n,n),n}function ie(e,t,n,i){return i=ne(e,t,i),i=a.Cartesian3.cross(i,n,i),i=a.Cartesian3.normalize(i,i),i=a.Cartesian3.cross(n,i,i)}F.pack=function(e,t,n){let s=i.defaultValue(n,0);const o=e._positions,l=o.length;t[s++]=l;for(let e=0;e<l;++e){const n=o[e];a.Cartesian3.pack(n,t,s),s+=3}return t[s++]=e.granularity,t[s++]=e.loop?1:0,t[s++]=e.arcType,r.Ellipsoid.pack(e._ellipsoid,t,s),s+=r.Ellipsoid.packedLength,t[s++]=e._projectionIndex,t[s++]=e._scene3DOnly?1:0,t},F.unpack=function(e,t,n){let s=i.defaultValue(t,0);const o=e[s++],l=new Array(o);for(let t=0;t<o;t++)l[t]=a.Cartesian3.unpack(e,s),s+=3;const c=e[s++],u=1===e[s++],C=e[s++],p=r.Ellipsoid.unpack(e,s);s+=r.Ellipsoid.packedLength;const h=e[s++],d=1===e[s++];return i.defined(n)||(n=new F({positions:l})),n._positions=l,n.granularity=c,n.loop=u,n.arcType=C,n._ellipsoid=p,n._projectionIndex=h,n._scene3DOnly=d,n};const re=new a.Cartesian3,se=new a.Cartesian3,oe=new a.Cartesian3,le=new a.Cartesian3;function ce(e,t,n,i,r){const o=ne(n,t,le),l=ie(e,t,o,re),c=ie(i,t,o,se);if(s.CesiumMath.equalsEpsilon(a.Cartesian3.dot(l,c),-1,s.CesiumMath.EPSILON5))return r=a.Cartesian3.cross(o,l,r),r=a.Cartesian3.normalize(r,r);r=a.Cartesian3.add(c,l,r),r=a.Cartesian3.normalize(r,r);const u=a.Cartesian3.cross(o,r,oe);return a.Cartesian3.dot(c,u)<0&&(r=a.Cartesian3.negate(r,r)),r}const ue=m.Plane.fromPointNormal(a.Cartesian3.ZERO,a.Cartesian3.UNIT_Y),Ce=new a.Cartesian3,pe=new a.Cartesian3,he=new a.Cartesian3,de=new a.Cartesian3,ge=new a.Cartesian3,fe=new a.Cartesian3,me=new n.Cartographic,we=new n.Cartographic,ye=new n.Cartographic;F.createGeometry=function(t){const r=!t._scene3DOnly;let C=t.loop;const f=t._ellipsoid,m=t.granularity,w=t.arcType,y=new j[t._projectionIndex](f),M=Y,T=q;let E,_;const O=t._positions,P=O.length;let b,A,k,L;2===P&&(C=!1);const S=new p.EllipsoidRhumbLine(void 0,void 0,f);let x,I,N;const R=[O[0]];for(_=0;_<P-1;_++)b=O[_],A=O[_+1],x=g.IntersectionTests.lineSegmentPlane(b,A,ue,fe),!i.defined(x)||a.Cartesian3.equalsEpsilon(x,b,s.CesiumMath.EPSILON7)||a.Cartesian3.equalsEpsilon(x,A,s.CesiumMath.EPSILON7)||(t.arcType===l.ArcType.GEODESIC?R.push(a.Cartesian3.clone(x)):t.arcType===l.ArcType.RHUMB&&(N=f.cartesianToCartographic(x,me).longitude,k=f.cartesianToCartographic(b,me),L=f.cartesianToCartographic(A,we),S.setEndPoints(k,L),I=S.findIntersectionWithLongitude(N,ye),x=f.cartographicToCartesian(I,fe),!i.defined(x)||a.Cartesian3.equalsEpsilon(x,b,s.CesiumMath.EPSILON7)||a.Cartesian3.equalsEpsilon(x,A,s.CesiumMath.EPSILON7)||R.push(a.Cartesian3.clone(x)))),R.push(A);C&&(b=O[P-1],A=O[0],x=g.IntersectionTests.lineSegmentPlane(b,A,ue,fe),!i.defined(x)||a.Cartesian3.equalsEpsilon(x,b,s.CesiumMath.EPSILON7)||a.Cartesian3.equalsEpsilon(x,A,s.CesiumMath.EPSILON7)||(t.arcType===l.ArcType.GEODESIC?R.push(a.Cartesian3.clone(x)):t.arcType===l.ArcType.RHUMB&&(N=f.cartesianToCartographic(x,me).longitude,k=f.cartesianToCartographic(b,me),L=f.cartesianToCartographic(A,we),S.setEndPoints(k,L),I=S.findIntersectionWithLongitude(N,ye),x=f.cartographicToCartesian(I,fe),!i.defined(x)||a.Cartesian3.equalsEpsilon(x,b,s.CesiumMath.EPSILON7)||a.Cartesian3.equalsEpsilon(x,A,s.CesiumMath.EPSILON7)||R.push(a.Cartesian3.clone(x)))));let D=R.length,v=new Array(D);for(_=0;_<D;_++){const e=n.Cartographic.fromCartesian(R[_],f);e.height=0,v[_]=e}if(v=c.arrayRemoveDuplicates(v,n.Cartographic.equalsEpsilon),D=v.length,D<2)return;const z=[],B=[],G=[],F=[];let X=Ce,W=pe,U=he,$=de,J=ge;const Q=v[0],K=v[1];for(X=ae(f,v[D-1],M,X),$=ae(f,K,M,$),W=ae(f,Q,M,W),U=ae(f,Q,T,U),J=C?ce(X,W,U,$,J):Z(Q,K,T,f,J),a.Cartesian3.pack(J,B,0),a.Cartesian3.pack(W,G,0),a.Cartesian3.pack(U,F,0),z.push(Q.latitude),z.push(Q.longitude),ee(Q,K,M,T,m,w,f,B,G,F,z),_=1;_<D-1;++_){X=a.Cartesian3.clone(W,X),W=a.Cartesian3.clone($,W);const e=v[_];ae(f,e,T,U),ae(f,v[_+1],M,$),ce(X,W,U,$,J),E=B.length,a.Cartesian3.pack(J,B,E),a.Cartesian3.pack(W,G,E),a.Cartesian3.pack(U,F,E),z.push(e.latitude),z.push(e.longitude),ee(v[_],v[_+1],M,T,m,w,f,B,G,F,z)}const te=v[D-1],ie=v[D-2];if(W=ae(f,te,M,W),U=ae(f,te,T,U),C){const e=v[0];X=ae(f,ie,M,X),$=ae(f,e,M,$),J=ce(X,W,U,$,J)}else J=Z(ie,te,T,f,J);if(E=B.length,a.Cartesian3.pack(J,B,E),a.Cartesian3.pack(W,G,E),a.Cartesian3.pack(U,F,E),z.push(te.latitude),z.push(te.longitude),C){for(ee(te,Q,M,T,m,w,f,B,G,F,z),E=B.length,_=0;_<3;++_)B[E+_]=B[_],G[E+_]=G[_],F[E+_]=F[_];z.push(Q.latitude),z.push(Q.longitude)}return function(t,n,i,r,l,c,C){let p,g;const f=n._ellipsoid,m=i.length/3-1,w=8*m,y=4*w,M=36*m,T=w>65535?new Uint32Array(M):new Uint16Array(M),E=new Float64Array(3*w),_=new Float32Array(y),O=new Float32Array(y),P=new Float32Array(y),b=new Float32Array(y),A=new Float32Array(y);let k,L,S,x;C&&(k=new Float32Array(y),L=new Float32Array(y),S=new Float32Array(y),x=new Float32Array(2*w));const I=c.length/2;let N=0;const R=Re;R.height=0;const D=De;D.height=0;let v=ve,z=ze;if(C)for(g=0,p=1;p<I;p++)R.latitude=c[g],R.longitude=c[g+1],D.latitude=c[g+2],D.longitude=c[g+3],v=n.project(R,v),z=n.project(D,z),N+=a.Cartesian3.distance(v,z),g+=2;const j=r.length/3;z=a.Cartesian3.unpack(r,0,z);let B,G=0;for(g=3,p=1;p<j;p++)v=a.Cartesian3.clone(z,v),z=a.Cartesian3.unpack(r,g,z),G+=a.Cartesian3.distance(v,z),g+=3;g=3;let Y=0,q=0,F=0,X=0,W=!1,U=a.Cartesian3.unpack(i,0,je),Z=a.Cartesian3.unpack(r,0,ze),$=a.Cartesian3.unpack(l,0,Ve);if(t){_e($,a.Cartesian3.unpack(i,i.length-6,He),U,Z)&&($=a.Cartesian3.negate($,$))}let J=0,Q=0,K=0;for(p=0;p<m;p++){const e=a.Cartesian3.clone(U,He),t=a.Cartesian3.clone(Z,ve);let u,p,d,m,w=a.Cartesian3.clone($,Be);if(W&&(w=a.Cartesian3.negate(w,w)),U=a.Cartesian3.unpack(i,g,je),Z=a.Cartesian3.unpack(r,g,ze),$=a.Cartesian3.unpack(l,g,Ve),W=_e($,e,U,Z),R.latitude=c[Y],R.longitude=c[Y+1],D.latitude=c[Y+2],D.longitude=c[Y+3],C){const e=Ne(R,D);u=n.project(R,Ue),p=n.project(D,Ze);const t=ne(p,u,st);t.y=Math.abs(t.y),d=$e,m=Je,0===e||a.Cartesian3.dot(t,a.Cartesian3.UNIT_Y)>V?(d=Ae(n,R,w,u,$e),m=Ae(n,D,$,p,Je)):1===e?(m=Ae(n,D,$,p,Je),d.x=0,d.y=s.CesiumMath.sign(R.longitude-Math.abs(D.longitude)),d.z=0):(d=Ae(n,R,w,u,$e),m.x=0,m.y=s.CesiumMath.sign(R.longitude-D.longitude),m.z=0)}const y=a.Cartesian3.distance(t,Z),M=h.EncodedCartesian3.fromCartesian(e,it),T=a.Cartesian3.subtract(U,e,Qe),I=a.Cartesian3.normalize(T,tt);let v=a.Cartesian3.subtract(t,e,Ke);v=a.Cartesian3.normalize(v,v);let z=a.Cartesian3.cross(I,v,tt);z=a.Cartesian3.normalize(z,z);let j=a.Cartesian3.cross(v,w,at);j=a.Cartesian3.normalize(j,j);let ee=a.Cartesian3.subtract(Z,U,et);ee=a.Cartesian3.normalize(ee,ee);let te=a.Cartesian3.cross($,ee,nt);te=a.Cartesian3.normalize(te,te);const ae=y/G,ie=J/G;let re,se,oe,le=0,ce=0,ue=0;if(C){le=a.Cartesian3.distance(u,p),re=h.EncodedCartesian3.fromCartesian(u,rt),se=a.Cartesian3.subtract(p,u,st),oe=a.Cartesian3.normalize(se,ot);const e=oe.x;oe.x=oe.y,oe.y=-e,ce=le/N,ue=Q/N}for(B=0;B<8;B++){const e=X+4*B,t=q+2*B,n=e+3,i=B<4?1:-1,r=2===B||3===B||6===B||7===B?1:-1;a.Cartesian3.pack(M.high,_,e),_[n]=T.x,a.Cartesian3.pack(M.low,O,e),O[n]=T.y,a.Cartesian3.pack(j,P,e),P[n]=T.z,a.Cartesian3.pack(te,b,e),b[n]=ae*i,a.Cartesian3.pack(z,A,e);let s=ie*r;0===s&&r<0&&(s=9),A[n]=s,C&&(k[e]=re.high.x,k[e+1]=re.high.y,k[e+2]=re.low.x,k[e+3]=re.low.y,S[e]=-d.y,S[e+1]=d.x,S[e+2]=m.y,S[e+3]=-m.x,L[e]=se.x,L[e+1]=se.y,L[e+2]=oe.x,L[e+3]=oe.y,x[t]=ce*i,s=ue*r,0===s&&r<0&&(s=9),x[t+1]=s)}const Ce=Xe,pe=We,he=qe,de=Fe,ge=o.Rectangle.fromCartographicArray(Ge,Ye),fe=H.getMinimumMaximumHeights(ge,f),me=fe.minimumTerrainHeight,we=fe.maximumTerrainHeight;K+=Math.abs(me),K+=Math.abs(we),Se(e,t,me,we,Ce,he),Se(U,Z,me,we,pe,de);let ye=a.Cartesian3.multiplyByScalar(z,s.CesiumMath.EPSILON5,lt);a.Cartesian3.add(Ce,ye,Ce),a.Cartesian3.add(pe,ye,pe),a.Cartesian3.add(he,ye,he),a.Cartesian3.add(de,ye,de),Ie(Ce,pe),Ie(he,de),a.Cartesian3.pack(Ce,E,F),a.Cartesian3.pack(pe,E,F+3),a.Cartesian3.pack(de,E,F+6),a.Cartesian3.pack(he,E,F+9),ye=a.Cartesian3.multiplyByScalar(z,-2*s.CesiumMath.EPSILON5,lt),a.Cartesian3.add(Ce,ye,Ce),a.Cartesian3.add(pe,ye,pe),a.Cartesian3.add(he,ye,he),a.Cartesian3.add(de,ye,de),Ie(Ce,pe),Ie(he,de),a.Cartesian3.pack(Ce,E,F+12),a.Cartesian3.pack(pe,E,F+15),a.Cartesian3.pack(de,E,F+18),a.Cartesian3.pack(he,E,F+21),Y+=2,g+=3,q+=16,F+=24,X+=32,J+=y,Q+=le}g=0;let ee=0;for(p=0;p<m;p++){for(B=0;B<Ct;B++)T[g+B]=ut[B]+ee;ee+=8,g+=Ct}const te=ct;e.BoundingSphere.fromVertices(i,a.Cartesian3.ZERO,3,te[0]),e.BoundingSphere.fromVertices(r,a.Cartesian3.ZERO,3,te[1]);const ae=e.BoundingSphere.fromBoundingSpheres(te);ae.radius+=K/(2*m);const ie={position:new d.GeometryAttribute({componentDatatype:u.ComponentDatatype.DOUBLE,componentsPerAttribute:3,normalize:!1,values:E}),startHiAndForwardOffsetX:pt(_),startLoAndForwardOffsetY:pt(O),startNormalAndForwardOffsetZ:pt(P),endNormalAndTextureCoordinateNormalizationX:pt(b),rightNormalAndTextureCoordinateNormalizationY:pt(A)};C&&(ie.startHiLo2D=pt(k),ie.offsetAndRight2D=pt(L),ie.startEndNormals2D=pt(S),ie.texcoordNormalization2D=new d.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:2,normalize:!1,values:x}));return new d.Geometry({attributes:ie,indices:T,boundingSphere:ae})}(C,y,G,F,B,z,r)};const Me=new a.Cartesian3,Te=new f.Matrix3,Ee=new e.Quaternion;function _e(t,n,i,r){const o=ne(i,n,Me),l=a.Cartesian3.dot(o,t);if(l>V||l<G){const a=ne(r,i,le),n=l<G?s.CesiumMath.PI_OVER_TWO:-s.CesiumMath.PI_OVER_TWO,o=e.Quaternion.fromAxisAngle(a,n,Ee),c=f.Matrix3.fromQuaternion(o,Te);return f.Matrix3.multiplyByVector(c,t,t),!0}return!1}const Oe=new n.Cartographic,Pe=new a.Cartesian3,be=new a.Cartesian3;function Ae(e,t,i,r,o){const l=n.Cartographic.toCartesian(t,e._ellipsoid,Pe);let c=a.Cartesian3.add(l,i,be),u=!1;const C=e._ellipsoid;let p=C.cartesianToCartographic(c,Oe);Math.abs(t.longitude-p.longitude)>s.CesiumMath.PI_OVER_TWO&&(u=!0,c=a.Cartesian3.subtract(l,i,be),p=C.cartesianToCartographic(c,Oe)),p.height=0;const h=e.project(p,o);return(o=a.Cartesian3.subtract(h,r,o)).z=0,o=a.Cartesian3.normalize(o,o),u&&a.Cartesian3.negate(o,o),o}const ke=new a.Cartesian3,Le=new a.Cartesian3;function Se(e,t,n,i,r,s){const o=a.Cartesian3.subtract(t,e,ke);a.Cartesian3.normalize(o,o);const l=n-Y;let c=a.Cartesian3.multiplyByScalar(o,l,Le);a.Cartesian3.add(e,c,r);const u=i-q;c=a.Cartesian3.multiplyByScalar(o,u,Le),a.Cartesian3.add(t,c,s)}const xe=new a.Cartesian3;function Ie(e,t){const n=m.Plane.getPointDistance(ue,e),i=m.Plane.getPointDistance(ue,t);let r=xe;s.CesiumMath.equalsEpsilon(n,0,s.CesiumMath.EPSILON2)?(r=ne(t,e,r),a.Cartesian3.multiplyByScalar(r,s.CesiumMath.EPSILON2,r),a.Cartesian3.add(e,r,e)):s.CesiumMath.equalsEpsilon(i,0,s.CesiumMath.EPSILON2)&&(r=ne(e,t,r),a.Cartesian3.multiplyByScalar(r,s.CesiumMath.EPSILON2,r),a.Cartesian3.add(t,r,t))}function Ne(e,t){const a=Math.abs(e.longitude),n=Math.abs(t.longitude);if(s.CesiumMath.equalsEpsilon(a,s.CesiumMath.PI,s.CesiumMath.EPSILON11)){const n=s.CesiumMath.sign(t.longitude);return e.longitude=n*(a-s.CesiumMath.EPSILON11),1}if(s.CesiumMath.equalsEpsilon(n,s.CesiumMath.PI,s.CesiumMath.EPSILON11)){const a=s.CesiumMath.sign(e.longitude);return t.longitude=a*(n-s.CesiumMath.EPSILON11),2}return 0}const Re=new n.Cartographic,De=new n.Cartographic,ve=new a.Cartesian3,ze=new a.Cartesian3,He=new a.Cartesian3,je=new a.Cartesian3,Be=new a.Cartesian3,Ve=new a.Cartesian3,Ge=[Re,De],Ye=new o.Rectangle,qe=new a.Cartesian3,Fe=new a.Cartesian3,Xe=new a.Cartesian3,We=new a.Cartesian3,Ue=new a.Cartesian3,Ze=new a.Cartesian3,$e=new a.Cartesian3,Je=new a.Cartesian3,Qe=new a.Cartesian3,Ke=new a.Cartesian3,et=new a.Cartesian3,tt=new a.Cartesian3,at=new a.Cartesian3,nt=new a.Cartesian3,it=new h.EncodedCartesian3,rt=new h.EncodedCartesian3,st=new a.Cartesian3,ot=new a.Cartesian3,lt=new a.Cartesian3,ct=[new e.BoundingSphere,new e.BoundingSphere],ut=[0,2,1,0,3,2,0,7,3,0,4,7,0,5,4,0,1,5,5,7,4,5,6,7,5,2,6,5,1,2,3,6,2,3,7,6],Ct=ut.length;function pt(e){return new d.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:4,normalize:!1,values:e})}return F._projectNormal=Ae,function(e,t){return H.initialize().then((function(){return i.defined(t)&&(e=F.unpack(e,t)),F.createGeometry(e)}))}}));
