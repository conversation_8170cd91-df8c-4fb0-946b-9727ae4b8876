define(["./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./ArcType-26a3f38d","./arrayRemoveDuplicates-0d8dde26","./Transforms-2afbbfb5","./Cartesian3-529c236c","./Color-e24f904b","./ComponentDatatype-ab629b88","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./IndexDatatype-58eb7805","./Math-355606c6","./PolylinePipeline-07b67faf","./VertexFormat-fbdec922","./Cartographic-dbefb6fa","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./WebGLConstants-7f557f93","./Matrix2-e4a4609a","./EllipsoidGeodesic-8ac7b85d","./EllipsoidRhumbLine-6774fec3","./IntersectionTests-01432fe7","./Plane-06f34fae"],(function(e,t,o,r,n,a,i,l,s,c,p,d,u,y,m,h,f,C,g,_,A,E,P,x,T,w,b,v,D){"use strict";const k=[];function V(e,t,o,r,n){const a=k;let l;a.length=n;const s=o.red,c=o.green,p=o.blue,d=o.alpha,u=r.red,y=r.green,m=r.blue,h=r.alpha;if(i.Color.equals(o,r)){for(l=0;l<n;l++)a[l]=i.Color.clone(o);return a}const f=(u-s)/n,C=(y-c)/n,g=(m-p)/n,_=(h-d)/n;for(l=0;l<n;l++)a[l]=new i.Color(s+l*f,c+l*C,p+l*g,d+l*_);return a}function L(r){const n=(r=e.defaultValue(r,e.defaultValue.EMPTY_OBJECT)).positions,l=r.colors,s=e.defaultValue(r.width,1),c=e.defaultValue(r.colorsPerVertex,!1);this._positions=n,this._colors=l,this._width=s,this._colorsPerVertex=c,this._vertexFormat=y.VertexFormat.clone(e.defaultValue(r.vertexFormat,y.VertexFormat.DEFAULT)),this._arcType=e.defaultValue(r.arcType,o.ArcType.GEODESIC),this._granularity=e.defaultValue(r.granularity,d.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=t.Ellipsoid.clone(e.defaultValue(r.ellipsoid,t.Ellipsoid.WGS84)),this._workerName="createPolylineGeometry";let p=1+n.length*a.Cartesian3.packedLength;p+=e.defined(l)?1+l.length*i.Color.packedLength:1,this.packedLength=p+t.Ellipsoid.packedLength+y.VertexFormat.packedLength+4}L.pack=function(o,r,n){let l;n=e.defaultValue(n,0);const s=o._positions;let c=s.length;for(r[n++]=c,l=0;l<c;++l,n+=a.Cartesian3.packedLength)a.Cartesian3.pack(s[l],r,n);const p=o._colors;for(c=e.defined(p)?p.length:0,r[n++]=c,l=0;l<c;++l,n+=i.Color.packedLength)i.Color.pack(p[l],r,n);return t.Ellipsoid.pack(o._ellipsoid,r,n),n+=t.Ellipsoid.packedLength,y.VertexFormat.pack(o._vertexFormat,r,n),n+=y.VertexFormat.packedLength,r[n++]=o._width,r[n++]=o._colorsPerVertex?1:0,r[n++]=o._arcType,r[n]=o._granularity,r};const F=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),G=new y.VertexFormat,R={positions:void 0,colors:void 0,ellipsoid:F,vertexFormat:G,width:void 0,colorsPerVertex:void 0,arcType:void 0,granularity:void 0};L.unpack=function(o,r,n){let l;r=e.defaultValue(r,0);let s=o[r++];const c=new Array(s);for(l=0;l<s;++l,r+=a.Cartesian3.packedLength)c[l]=a.Cartesian3.unpack(o,r);s=o[r++];const p=s>0?new Array(s):void 0;for(l=0;l<s;++l,r+=i.Color.packedLength)p[l]=i.Color.unpack(o,r);const d=t.Ellipsoid.unpack(o,r,F);r+=t.Ellipsoid.packedLength;const u=y.VertexFormat.unpack(o,r,G);r+=y.VertexFormat.packedLength;const m=o[r++],h=1===o[r++],f=o[r++],C=o[r];return e.defined(n)?(n._positions=c,n._colors=p,n._ellipsoid=t.Ellipsoid.clone(d,n._ellipsoid),n._vertexFormat=y.VertexFormat.clone(u,n._vertexFormat),n._width=m,n._colorsPerVertex=h,n._arcType=f,n._granularity=C,n):(R.positions=c,R.colors=p,R.width=m,R.colorsPerVertex=h,R.arcType=f,R.granularity=C,new L(R))};const I=new a.Cartesian3,O=new a.Cartesian3,S=new a.Cartesian3,B=new a.Cartesian3;return L.createGeometry=function(t){const y=t._width,m=t._vertexFormat;let h=t._colors;const f=t._colorsPerVertex,C=t._arcType,g=t._granularity,_=t._ellipsoid;let A,E,P;const x=[];let T=r.arrayRemoveDuplicates(t._positions,a.Cartesian3.equalsEpsilon,!1,x);if(e.defined(h)&&x.length>0){let e=0,t=x[0];h=h.filter((function(o,r){let n=!1;return n=f?r===t||0===r&&1===t:r+1===t,!n||(e++,t=x[e],!1)}))}let w=T.length;if(w<2||y<=0)return;if(C===o.ArcType.GEODESIC||C===o.ArcType.RHUMB){let t,r;C===o.ArcType.GEODESIC?(t=d.CesiumMath.chordLength(g,_.maximumRadius),r=u.PolylinePipeline.numberOfPoints):(t=g,r=u.PolylinePipeline.numberOfPointsRhumbLine);const n=u.PolylinePipeline.extractHeights(T,_);if(e.defined(h)){let e=1;for(A=0;A<w-1;++A)e+=r(T[A],T[A+1],t);const o=new Array(e);let n=0;for(A=0;A<w-1;++A){const a=T[A],l=T[A+1],s=h[A],c=r(a,l,t);if(f&&A<e){const e=V(0,0,s,h[A+1],c),t=e.length;for(E=0;E<t;++E)o[n++]=e[E]}else for(E=0;E<c;++E)o[n++]=i.Color.clone(s)}o[n]=i.Color.clone(h[h.length-1]),h=o,k.length=0}T=C===o.ArcType.GEODESIC?u.PolylinePipeline.generateCartesianArc({positions:T,minDistance:t,ellipsoid:_,height:n}):u.PolylinePipeline.generateCartesianRhumbArc({positions:T,granularity:t,ellipsoid:_,height:n})}w=T.length;const b=4*w-4,v=new Float64Array(3*b),D=new Float64Array(3*b),L=new Float64Array(3*b),F=new Float32Array(2*b),G=m.st?new Float32Array(2*b):void 0,R=e.defined(h)?new Uint8Array(4*b):void 0;let M,N=0,U=0,q=0,H=0;for(E=0;E<w;++E){let t,o;0===E?(M=I,a.Cartesian3.subtract(T[0],T[1],M),a.Cartesian3.add(T[0],M,M)):M=T[E-1],a.Cartesian3.clone(M,S),a.Cartesian3.clone(T[E],O),E===w-1?(M=I,a.Cartesian3.subtract(T[w-1],T[w-2],M),a.Cartesian3.add(T[w-1],M,M)):M=T[E+1],a.Cartesian3.clone(M,B),e.defined(R)&&(t=0===E||f?h[E]:h[E-1],E!==w-1&&(o=h[E]));const r=E===w-1?2:4;for(P=0===E?2:0;P<r;++P){a.Cartesian3.pack(O,v,N),a.Cartesian3.pack(S,D,N),a.Cartesian3.pack(B,L,N),N+=3;const r=P-2<0?-1:1;if(F[U++]=P%2*2-1,F[U++]=r*y,m.st&&(G[q++]=E/(w-1),G[q++]=Math.max(F[U-2],0)),e.defined(R)){const e=P<2?t:o;R[H++]=i.Color.floatToByte(e.red),R[H++]=i.Color.floatToByte(e.green),R[H++]=i.Color.floatToByte(e.blue),R[H++]=i.Color.floatToByte(e.alpha)}}}const W=new c.GeometryAttributes;W.position=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:v}),W.prevPosition=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:D}),W.nextPosition=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:L}),W.expandAndWidth=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:F}),m.st&&(W.st=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:G})),e.defined(R)&&(W.color=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:R,normalize:!0}));const Y=p.IndexDatatype.createTypedArray(b,6*w-6);let J=0,j=0;const z=w-1;for(E=0;E<z;++E)Y[j++]=J,Y[j++]=J+2,Y[j++]=J+1,Y[j++]=J+1,Y[j++]=J+2,Y[j++]=J+3,J+=4;return new s.Geometry({attributes:W,indices:Y,primitiveType:s.PrimitiveType.TRIANGLES,boundingSphere:n.BoundingSphere.fromPoints(T),geometryType:s.GeometryType.POLYLINES})},function(o,r){return e.defined(r)&&(o=L.unpack(o,r)),o._ellipsoid=t.Ellipsoid.clone(o._ellipsoid),L.createGeometry(o)}}));
