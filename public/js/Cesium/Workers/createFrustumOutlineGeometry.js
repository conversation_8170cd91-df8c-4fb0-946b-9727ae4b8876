define(["./defaultValue-f6d5e6da","./Transforms-2afbbfb5","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./FrustumGeometry-4dc5632b","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./Cartographic-dbefb6fa","./Math-355606c6","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./WebGLConstants-7f557f93","./Plane-06f34fae","./VertexFormat-fbdec922","./Matrix2-e4a4609a"],(function(e,t,r,n,a,i,u,o,s,c,p,m,h,l,d,f,g,_,k,y,F,w){"use strict";const P=0,L=1;function v(n){const i=n.frustum,u=n.orientation,o=n.origin,s=e.defaultValue(n._drawNearPlane,!0);let c,p;i instanceof a.PerspectiveFrustum?(c=P,p=a.PerspectiveFrustum.packedLength):i instanceof a.OrthographicFrustum&&(c=L,p=a.OrthographicFrustum.packedLength),this._frustumType=c,this._frustum=i.clone(),this._origin=r.Cartesian3.clone(o),this._orientation=t.Quaternion.clone(u),this._drawNearPlane=s,this._workerName="createFrustumOutlineGeometry",this.packedLength=2+p+r.Cartesian3.packedLength+t.Quaternion.packedLength}v.pack=function(n,i,u){u=e.defaultValue(u,0);const o=n._frustumType,s=n._frustum;return i[u++]=o,o===P?(a.PerspectiveFrustum.pack(s,i,u),u+=a.PerspectiveFrustum.packedLength):(a.OrthographicFrustum.pack(s,i,u),u+=a.OrthographicFrustum.packedLength),r.Cartesian3.pack(n._origin,i,u),u+=r.Cartesian3.packedLength,t.Quaternion.pack(n._orientation,i,u),i[u+=t.Quaternion.packedLength]=n._drawNearPlane?1:0,i};const C=new a.PerspectiveFrustum,G=new a.OrthographicFrustum,b=new t.Quaternion,N=new r.Cartesian3;return v.unpack=function(n,i,u){i=e.defaultValue(i,0);const o=n[i++];let s;o===P?(s=a.PerspectiveFrustum.unpack(n,i,C),i+=a.PerspectiveFrustum.packedLength):(s=a.OrthographicFrustum.unpack(n,i,G),i+=a.OrthographicFrustum.packedLength);const c=r.Cartesian3.unpack(n,i,N);i+=r.Cartesian3.packedLength;const p=t.Quaternion.unpack(n,i,b),m=1===n[i+=t.Quaternion.packedLength];if(!e.defined(u))return new v({frustum:s,origin:c,orientation:p,_drawNearPlane:m});const h=o===u._frustumType?u._frustum:void 0;return u._frustum=s.clone(h),u._frustumType=o,u._origin=r.Cartesian3.clone(c,u._origin),u._orientation=t.Quaternion.clone(p,u._orientation),u._drawNearPlane=m,u},v.createGeometry=function(e){const r=e._frustumType,o=e._frustum,s=e._origin,c=e._orientation,p=e._drawNearPlane,m=new Float64Array(24);a.FrustumGeometry._computeNearFarPlanes(s,c,r,o,m);const h=new u.GeometryAttributes({position:new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:m})});let l,d;const f=p?2:1,g=new Uint16Array(8*(f+1));let _=p?0:1;for(;_<2;++_)l=p?8*_:0,d=4*_,g[l]=d,g[l+1]=d+1,g[l+2]=d+1,g[l+3]=d+2,g[l+4]=d+2,g[l+5]=d+3,g[l+6]=d+3,g[l+7]=d;for(_=0;_<2;++_)l=8*(f+_),d=4*_,g[l]=d,g[l+1]=d+4,g[l+2]=d+1,g[l+3]=d+5,g[l+4]=d+2,g[l+5]=d+6,g[l+6]=d+3,g[l+7]=d+7;return new i.Geometry({attributes:h,indices:g,primitiveType:i.PrimitiveType.LINES,boundingSphere:t.BoundingSphere.fromVertices(m)})},function(t,r){return e.defined(r)&&(t=v.unpack(t,r)),v.createGeometry(t)}}));
