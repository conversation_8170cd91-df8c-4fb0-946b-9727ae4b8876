define(["exports","./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./EllipsoidTangentPlane-a6ea67fb","./Interval-d6c8d27a","./Math-355606c6","./Matrix3-31d1f01f","./Plane-06f34fae","./Rectangle-98b0bef0"],(function(a,t,e,n,r,i,s,o,C,c,u,l,d){"use strict";function h(a,t){this.center=n.Cartesian3.clone(i.defaultValue(a,n.Cartesian3.ZERO)),this.halfAxes=u.Matrix3.clone(i.defaultValue(t,u.Matrix3.ZERO))}h.packedLength=n.Cartesian3.packedLength+u.Matrix3.packedLength,h.pack=function(a,t,e){return e=i.defaultValue(e,0),n.Cartesian3.pack(a.center,t,e),u.Matrix3.pack(a.halfAxes,t,e+n.Cartesian3.packedLength),t},h.unpack=function(a,t,e){return t=i.defaultValue(t,0),i.defined(e)||(e=new h),n.Cartesian3.unpack(a,t,e.center),u.Matrix3.unpack(a,t+n.Cartesian3.packedLength,e.halfAxes),e};const x=new n.Cartesian3,m=new n.Cartesian3,M=new n.Cartesian3,f=new n.Cartesian3,p=new n.Cartesian3,w=new n.Cartesian3,g=new u.Matrix3,y={unitary:new u.Matrix3,diagonal:new u.Matrix3};h.fromPoints=function(a,t){if(i.defined(t)||(t=new h),!i.defined(a)||0===a.length)return t.halfAxes=u.Matrix3.ZERO,t.center=n.Cartesian3.ZERO,t;let e;const r=a.length,s=n.Cartesian3.clone(a[0],x);for(e=1;e<r;e++)n.Cartesian3.add(s,a[e],s);const o=1/r;n.Cartesian3.multiplyByScalar(s,o,s);let C,c=0,l=0,d=0,b=0,N=0,T=0;for(e=0;e<r;e++)C=n.Cartesian3.subtract(a[e],s,m),c+=C.x*C.x,l+=C.x*C.y,d+=C.x*C.z,b+=C.y*C.y,N+=C.y*C.z,T+=C.z*C.z;c*=o,l*=o,d*=o,b*=o,N*=o,T*=o;const O=g;O[0]=c,O[1]=l,O[2]=d,O[3]=l,O[4]=b,O[5]=N,O[6]=d,O[7]=N,O[8]=T;const A=u.Matrix3.computeEigenDecomposition(O,y),P=u.Matrix3.clone(A.unitary,t.halfAxes);let I=u.Matrix3.getColumn(P,0,f),R=u.Matrix3.getColumn(P,1,p),E=u.Matrix3.getColumn(P,2,w),S=-Number.MAX_VALUE,U=-Number.MAX_VALUE,L=-Number.MAX_VALUE,z=Number.MAX_VALUE,B=Number.MAX_VALUE,V=Number.MAX_VALUE;for(e=0;e<r;e++)C=a[e],S=Math.max(n.Cartesian3.dot(I,C),S),U=Math.max(n.Cartesian3.dot(R,C),U),L=Math.max(n.Cartesian3.dot(E,C),L),z=Math.min(n.Cartesian3.dot(I,C),z),B=Math.min(n.Cartesian3.dot(R,C),B),V=Math.min(n.Cartesian3.dot(E,C),V);I=n.Cartesian3.multiplyByScalar(I,.5*(z+S),I),R=n.Cartesian3.multiplyByScalar(R,.5*(B+U),R),E=n.Cartesian3.multiplyByScalar(E,.5*(V+L),E);const _=n.Cartesian3.add(I,R,t.center);n.Cartesian3.add(_,E,_);const k=M;return k.x=S-z,k.y=U-B,k.z=L-V,n.Cartesian3.multiplyByScalar(k,.5,k),u.Matrix3.multiplyByScale(t.halfAxes,k,t.halfAxes),t};const b=new n.Cartesian3,N=new n.Cartesian3;function T(a,t,e,r,s,o,C,c,l,d,x){i.defined(x)||(x=new h);const m=x.halfAxes;u.Matrix3.setColumn(m,0,t,m),u.Matrix3.setColumn(m,1,e,m),u.Matrix3.setColumn(m,2,r,m);let M=b;M.x=(s+o)/2,M.y=(C+c)/2,M.z=(l+d)/2;const f=N;f.x=(o-s)/2,f.y=(c-C)/2,f.z=(d-l)/2;const p=x.center;return M=u.Matrix3.multiplyByVector(m,M,M),n.Cartesian3.add(a,M,p),u.Matrix3.multiplyByScale(m,f,m),x}const O=new r.Cartographic,A=new n.Cartesian3,P=new r.Cartographic,I=new r.Cartographic,R=new r.Cartographic,E=new r.Cartographic,S=new r.Cartographic,U=new n.Cartesian3,L=new n.Cartesian3,z=new n.Cartesian3,B=new n.Cartesian3,V=new n.Cartesian3,_=new e.Cartesian2,k=new e.Cartesian2,W=new e.Cartesian2,X=new e.Cartesian2,q=new e.Cartesian2,D=new n.Cartesian3,j=new n.Cartesian3,Z=new n.Cartesian3,v=new n.Cartesian3,G=new e.Cartesian2,Y=new n.Cartesian3,F=new n.Cartesian3,H=new n.Cartesian3,J=new l.Plane(n.Cartesian3.UNIT_X,0);h.fromRectangle=function(a,t,e,C,u){let h,x,m,M,f,p,w;if(t=i.defaultValue(t,0),e=i.defaultValue(e,0),C=i.defaultValue(C,s.Ellipsoid.WGS84),a.width<=c.CesiumMath.PI){const n=d.Rectangle.center(a,O),i=C.cartographicToCartesian(n,A),s=new o.EllipsoidTangentPlane(i,C);w=s.plane;const c=n.longitude,g=a.south<0&&a.north>0?0:n.latitude,y=r.Cartographic.fromRadians(c,a.north,e,P),b=r.Cartographic.fromRadians(a.west,a.north,e,I),N=r.Cartographic.fromRadians(a.west,g,e,R),D=r.Cartographic.fromRadians(a.west,a.south,e,E),j=r.Cartographic.fromRadians(c,a.south,e,S),Z=C.cartographicToCartesian(y,U);let v=C.cartographicToCartesian(b,L);const G=C.cartographicToCartesian(N,z);let Y=C.cartographicToCartesian(D,B);const F=C.cartographicToCartesian(j,V),H=s.projectPointToNearestOnPlane(Z,_),J=s.projectPointToNearestOnPlane(v,k),K=s.projectPointToNearestOnPlane(G,W),Q=s.projectPointToNearestOnPlane(Y,X),$=s.projectPointToNearestOnPlane(F,q);return h=Math.min(J.x,K.x,Q.x),x=-h,M=Math.max(J.y,H.y),m=Math.min(Q.y,$.y),b.height=D.height=t,v=C.cartographicToCartesian(b,L),Y=C.cartographicToCartesian(D,B),f=Math.min(l.Plane.getPointDistance(w,v),l.Plane.getPointDistance(w,Y)),p=e,T(s.origin,s.xAxis,s.yAxis,s.zAxis,h,x,m,M,f,p,u)}const g=a.south>0,y=a.north<0,b=g?a.south:y?a.north:0,N=d.Rectangle.center(a,O).longitude,K=n.Cartesian3.fromRadians(N,b,e,C,D);K.z=0;const Q=Math.abs(K.x)<c.CesiumMath.EPSILON10&&Math.abs(K.y)<c.CesiumMath.EPSILON10?n.Cartesian3.UNIT_X:n.Cartesian3.normalize(K,j),$=n.Cartesian3.UNIT_Z,aa=n.Cartesian3.cross(Q,$,Z);w=l.Plane.fromPointNormal(K,Q,J);const ta=n.Cartesian3.fromRadians(N+c.CesiumMath.PI_OVER_TWO,b,e,C,v);x=n.Cartesian3.dot(l.Plane.projectPointOntoPlane(w,ta,G),aa),h=-x,M=n.Cartesian3.fromRadians(0,a.north,y?t:e,C,Y).z,m=n.Cartesian3.fromRadians(0,a.south,g?t:e,C,F).z;const ea=n.Cartesian3.fromRadians(a.east,b,e,C,H);return f=l.Plane.getPointDistance(w,ea),p=0,T(K,aa,$,Q,h,x,m,M,f,p,u)},h.fromTransformation=function(a,t){return i.defined(t)||(t=new h),t.center=e.Matrix4.getTranslation(a,t.center),t.halfAxes=e.Matrix4.getMatrix3(a,t.halfAxes),t.halfAxes=u.Matrix3.multiplyByScalar(t.halfAxes,.5,t.halfAxes),t},h.clone=function(a,t){if(i.defined(a))return i.defined(t)?(n.Cartesian3.clone(a.center,t.center),u.Matrix3.clone(a.halfAxes,t.halfAxes),t):new h(a.center,a.halfAxes)},h.intersectPlane=function(a,e){const r=a.center,i=e.normal,s=a.halfAxes,o=i.x,C=i.y,c=i.z,l=Math.abs(o*s[u.Matrix3.COLUMN0ROW0]+C*s[u.Matrix3.COLUMN0ROW1]+c*s[u.Matrix3.COLUMN0ROW2])+Math.abs(o*s[u.Matrix3.COLUMN1ROW0]+C*s[u.Matrix3.COLUMN1ROW1]+c*s[u.Matrix3.COLUMN1ROW2])+Math.abs(o*s[u.Matrix3.COLUMN2ROW0]+C*s[u.Matrix3.COLUMN2ROW1]+c*s[u.Matrix3.COLUMN2ROW2]),d=n.Cartesian3.dot(i,r)+e.distance;return d<=-l?t.Intersect.OUTSIDE:d>=l?t.Intersect.INSIDE:t.Intersect.INTERSECTING};const K=new n.Cartesian3,Q=new n.Cartesian3,$=new n.Cartesian3,aa=new n.Cartesian3,ta=new n.Cartesian3,ea=new n.Cartesian3;h.distanceSquaredTo=function(a,t){const e=n.Cartesian3.subtract(t,a.center,b),r=a.halfAxes;let i=u.Matrix3.getColumn(r,0,K),s=u.Matrix3.getColumn(r,1,Q),o=u.Matrix3.getColumn(r,2,$);const C=n.Cartesian3.magnitude(i),l=n.Cartesian3.magnitude(s),d=n.Cartesian3.magnitude(o);let h=!0,x=!0,m=!0;C>0?n.Cartesian3.divideByScalar(i,C,i):h=!1,l>0?n.Cartesian3.divideByScalar(s,l,s):x=!1,d>0?n.Cartesian3.divideByScalar(o,d,o):m=!1;const M=!h+!x+!m;let f,p,w;if(1===M){let a=i;f=s,p=o,x?m||(a=o,p=i):(a=s,f=i),w=n.Cartesian3.cross(f,p,ta),a===i?i=w:a===s?s=w:a===o&&(o=w)}else if(2===M){f=i,x?f=s:m&&(f=o);let a=n.Cartesian3.UNIT_Y;a.equalsEpsilon(f,c.CesiumMath.EPSILON3)&&(a=n.Cartesian3.UNIT_X),p=n.Cartesian3.cross(f,a,aa),n.Cartesian3.normalize(p,p),w=n.Cartesian3.cross(f,p,ta),n.Cartesian3.normalize(w,w),f===i?(s=p,o=w):f===s?(o=p,i=w):f===o&&(i=p,s=w)}else 3===M&&(i=n.Cartesian3.UNIT_X,s=n.Cartesian3.UNIT_Y,o=n.Cartesian3.UNIT_Z);const g=ea;g.x=n.Cartesian3.dot(e,i),g.y=n.Cartesian3.dot(e,s),g.z=n.Cartesian3.dot(e,o);let y,N=0;return g.x<-C?(y=g.x+C,N+=y*y):g.x>C&&(y=g.x-C,N+=y*y),g.y<-l?(y=g.y+l,N+=y*y):g.y>l&&(y=g.y-l,N+=y*y),g.z<-d?(y=g.z+d,N+=y*y):g.z>d&&(y=g.z-d,N+=y*y),N};const na=new n.Cartesian3,ra=new n.Cartesian3;h.computePlaneDistances=function(a,t,e,r){i.defined(r)||(r=new C.Interval);let s=Number.POSITIVE_INFINITY,o=Number.NEGATIVE_INFINITY;const c=a.center,l=a.halfAxes,d=u.Matrix3.getColumn(l,0,K),h=u.Matrix3.getColumn(l,1,Q),x=u.Matrix3.getColumn(l,2,$),m=n.Cartesian3.add(d,h,na);n.Cartesian3.add(m,x,m),n.Cartesian3.add(m,c,m);const M=n.Cartesian3.subtract(m,t,ra);let f=n.Cartesian3.dot(e,M);return s=Math.min(f,s),o=Math.max(f,o),n.Cartesian3.add(c,d,m),n.Cartesian3.add(m,h,m),n.Cartesian3.subtract(m,x,m),n.Cartesian3.subtract(m,t,M),f=n.Cartesian3.dot(e,M),s=Math.min(f,s),o=Math.max(f,o),n.Cartesian3.add(c,d,m),n.Cartesian3.subtract(m,h,m),n.Cartesian3.add(m,x,m),n.Cartesian3.subtract(m,t,M),f=n.Cartesian3.dot(e,M),s=Math.min(f,s),o=Math.max(f,o),n.Cartesian3.add(c,d,m),n.Cartesian3.subtract(m,h,m),n.Cartesian3.subtract(m,x,m),n.Cartesian3.subtract(m,t,M),f=n.Cartesian3.dot(e,M),s=Math.min(f,s),o=Math.max(f,o),n.Cartesian3.subtract(c,d,m),n.Cartesian3.add(m,h,m),n.Cartesian3.add(m,x,m),n.Cartesian3.subtract(m,t,M),f=n.Cartesian3.dot(e,M),s=Math.min(f,s),o=Math.max(f,o),n.Cartesian3.subtract(c,d,m),n.Cartesian3.add(m,h,m),n.Cartesian3.subtract(m,x,m),n.Cartesian3.subtract(m,t,M),f=n.Cartesian3.dot(e,M),s=Math.min(f,s),o=Math.max(f,o),n.Cartesian3.subtract(c,d,m),n.Cartesian3.subtract(m,h,m),n.Cartesian3.add(m,x,m),n.Cartesian3.subtract(m,t,M),f=n.Cartesian3.dot(e,M),s=Math.min(f,s),o=Math.max(f,o),n.Cartesian3.subtract(c,d,m),n.Cartesian3.subtract(m,h,m),n.Cartesian3.subtract(m,x,m),n.Cartesian3.subtract(m,t,M),f=n.Cartesian3.dot(e,M),s=Math.min(f,s),o=Math.max(f,o),r.start=s,r.stop=o,r};const ia=new n.Cartesian3,sa=new n.Cartesian3,oa=new n.Cartesian3;h.computeCorners=function(a,t){i.defined(t)||(t=[new n.Cartesian3,new n.Cartesian3,new n.Cartesian3,new n.Cartesian3,new n.Cartesian3,new n.Cartesian3,new n.Cartesian3,new n.Cartesian3]);const e=a.center,r=a.halfAxes,s=u.Matrix3.getColumn(r,0,ia),o=u.Matrix3.getColumn(r,1,sa),C=u.Matrix3.getColumn(r,2,oa);return n.Cartesian3.clone(e,t[0]),n.Cartesian3.subtract(t[0],s,t[0]),n.Cartesian3.subtract(t[0],o,t[0]),n.Cartesian3.subtract(t[0],C,t[0]),n.Cartesian3.clone(e,t[1]),n.Cartesian3.subtract(t[1],s,t[1]),n.Cartesian3.subtract(t[1],o,t[1]),n.Cartesian3.add(t[1],C,t[1]),n.Cartesian3.clone(e,t[2]),n.Cartesian3.subtract(t[2],s,t[2]),n.Cartesian3.add(t[2],o,t[2]),n.Cartesian3.subtract(t[2],C,t[2]),n.Cartesian3.clone(e,t[3]),n.Cartesian3.subtract(t[3],s,t[3]),n.Cartesian3.add(t[3],o,t[3]),n.Cartesian3.add(t[3],C,t[3]),n.Cartesian3.clone(e,t[4]),n.Cartesian3.add(t[4],s,t[4]),n.Cartesian3.subtract(t[4],o,t[4]),n.Cartesian3.subtract(t[4],C,t[4]),n.Cartesian3.clone(e,t[5]),n.Cartesian3.add(t[5],s,t[5]),n.Cartesian3.subtract(t[5],o,t[5]),n.Cartesian3.add(t[5],C,t[5]),n.Cartesian3.clone(e,t[6]),n.Cartesian3.add(t[6],s,t[6]),n.Cartesian3.add(t[6],o,t[6]),n.Cartesian3.subtract(t[6],C,t[6]),n.Cartesian3.clone(e,t[7]),n.Cartesian3.add(t[7],s,t[7]),n.Cartesian3.add(t[7],o,t[7]),n.Cartesian3.add(t[7],C,t[7]),t};const Ca=new u.Matrix3;h.computeTransformation=function(a,t){i.defined(t)||(t=new e.Matrix4);const n=a.center,r=u.Matrix3.multiplyByUniformScale(a.halfAxes,2,Ca);return e.Matrix4.fromRotationTranslation(r,n,t)};const ca=new t.BoundingSphere;h.isOccluded=function(a,e){const n=t.BoundingSphere.fromOrientedBoundingBox(a,ca);return!e.isBoundingSphereVisible(n)},h.prototype.intersectPlane=function(a){return h.intersectPlane(this,a)},h.prototype.distanceSquaredTo=function(a){return h.distanceSquaredTo(this,a)},h.prototype.computePlaneDistances=function(a,t,e){return h.computePlaneDistances(this,a,t,e)},h.prototype.computeCorners=function(a){return h.computeCorners(this,a)},h.prototype.computeTransformation=function(a){return h.computeTransformation(this,a)},h.prototype.isOccluded=function(a){return h.isOccluded(this,a)},h.equals=function(a,t){return a===t||i.defined(a)&&i.defined(t)&&n.Cartesian3.equals(a.center,t.center)&&u.Matrix3.equals(a.halfAxes,t.halfAxes)},h.prototype.clone=function(a){return h.clone(this,a)},h.prototype.equals=function(a){return h.equals(this,a)},a.OrientedBoundingBox=h}));
