define(["./arrayRemoveDuplicates-0d8dde26","./BoundingRectangle-a5a1c0f3","./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./CoplanarPolygonGeometryLibrary-5cd2e1e7","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryInstance-1d11f88d","./GeometryPipeline-69b47aa9","./IndexDatatype-58eb7805","./Math-355606c6","./Matrix3-31d1f01f","./PolygonGeometryLibrary-b9a68ba7","./PolygonPipeline-39b84ada","./VertexFormat-fbdec922","./Cartographic-dbefb6fa","./Rectangle-98b0bef0","./Interval-d6c8d27a","./DeveloperError-c85858c1","./combine-0c102d93","./RequestType-735c98f2","./RuntimeError-9b4ce3fb","./WebGLConstants-7f557f93","./OrientedBoundingBox-5f5e4f4c","./EllipsoidTangentPlane-a6ea67fb","./AxisAlignedBoundingBox-d98e5354","./IntersectionTests-01432fe7","./Plane-06f34fae","./Matrix2-e4a4609a","./AttributeCompression-d2ca507e","./EncodedCartesian3-94199dac","./ArcType-26a3f38d","./EllipsoidRhumbLine-6774fec3"],(function(e,t,n,o,r,a,i,s,l,y,c,p,u,m,d,g,C,h,x,b,f,P,A,G,L,w,F,v,E,T,_,D,k,V,R,I,H){"use strict";const M=new r.Cartesian3,B=new t.BoundingRectangle,O=new o.Cartesian2,z=new o.Cartesian2,S=new r.Cartesian3,N=new r.Cartesian3,Q=new r.Cartesian3,j=new r.Cartesian3,q=new r.Cartesian3,J=new r.Cartesian3,U=new n.Quaternion,Y=new g.Matrix3,W=new g.Matrix3,Z=new r.Cartesian3;function K(e,t,i,l,p,u,C,x,b){const f=e.positions;let P=h.PolygonPipeline.triangulate(e.positions2D,e.holes);P.length<3&&(P=[0,1,2]);const A=m.IndexDatatype.createTypedArray(f.length,P.length);A.set(P);let G=Y;if(0!==l){let e=n.Quaternion.fromAxisAngle(C,l,U);if(G=g.Matrix3.fromQuaternion(e,G),t.tangent||t.bitangent){e=n.Quaternion.fromAxisAngle(C,-l,U);const o=g.Matrix3.fromQuaternion(e,W);x=r.Cartesian3.normalize(g.Matrix3.multiplyByVector(o,x,x),x),t.bitangent&&(b=r.Cartesian3.normalize(r.Cartesian3.cross(C,x,b),b))}}else G=g.Matrix3.clone(g.Matrix3.IDENTITY,G);const L=z;t.st&&(L.x=i.x,L.y=i.y);const w=f.length,F=3*w,v=new Float64Array(F),E=t.normal?new Float32Array(F):void 0,T=t.tangent?new Float32Array(F):void 0,_=t.bitangent?new Float32Array(F):void 0,D=t.st?new Float32Array(2*w):void 0;let k=0,V=0,R=0,I=0,H=0;for(let e=0;e<w;e++){const n=f[e];if(v[k++]=n.x,v[k++]=n.y,v[k++]=n.z,t.st)if(s.defined(p)&&p.positions.length===w)D[H++]=p.positions[e].x,D[H++]=p.positions[e].y;else{const e=u(g.Matrix3.multiplyByVector(G,n,M),O);o.Cartesian2.subtract(e,L,e);const t=d.CesiumMath.clamp(e.x/i.width,0,1),r=d.CesiumMath.clamp(e.y/i.height,0,1);D[H++]=t,D[H++]=r}t.normal&&(E[V++]=C.x,E[V++]=C.y,E[V++]=C.z),t.tangent&&(T[I++]=x.x,T[I++]=x.y,T[I++]=x.z),t.bitangent&&(_[R++]=b.x,_[R++]=b.y,_[R++]=b.z)}const B=new c.GeometryAttributes;return t.position&&(B.position=new y.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:v})),t.normal&&(B.normal=new y.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E})),t.tangent&&(B.tangent=new y.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:T})),t.bitangent&&(B.bitangent=new y.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:_})),t.st&&(B.st=new y.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:D})),new y.Geometry({attributes:B,indices:A,primitiveType:y.PrimitiveType.TRIANGLES})}function X(e){const t=(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).polygonHierarchy,n=e.textureCoordinates,a=s.defaultValue(e.vertexFormat,x.VertexFormat.DEFAULT);this._vertexFormat=x.VertexFormat.clone(a),this._polygonHierarchy=t,this._stRotation=s.defaultValue(e.stRotation,0),this._ellipsoid=l.Ellipsoid.clone(s.defaultValue(e.ellipsoid,l.Ellipsoid.WGS84)),this._workerName="createCoplanarPolygonGeometry",this._textureCoordinates=n,this.packedLength=C.PolygonGeometryLibrary.computeHierarchyPackedLength(t,r.Cartesian3)+x.VertexFormat.packedLength+l.Ellipsoid.packedLength+(s.defined(n)?C.PolygonGeometryLibrary.computeHierarchyPackedLength(n,o.Cartesian2):1)+2}X.fromPositions=function(e){return new X({polygonHierarchy:{positions:(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).positions},vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid,textureCoordinates:e.textureCoordinates})},X.pack=function(e,t,n){return n=s.defaultValue(n,0),n=C.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,n,r.Cartesian3),l.Ellipsoid.pack(e._ellipsoid,t,n),n+=l.Ellipsoid.packedLength,x.VertexFormat.pack(e._vertexFormat,t,n),n+=x.VertexFormat.packedLength,t[n++]=e._stRotation,s.defined(e._textureCoordinates)?n=C.PolygonGeometryLibrary.packPolygonHierarchy(e._textureCoordinates,t,n,o.Cartesian2):t[n++]=-1,t[n++]=e.packedLength,t};const $=l.Ellipsoid.clone(l.Ellipsoid.UNIT_SPHERE),ee=new x.VertexFormat,te={polygonHierarchy:{}};return X.unpack=function(e,t,n){t=s.defaultValue(t,0);const a=C.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t,r.Cartesian3);t=a.startingIndex,delete a.startingIndex;const i=l.Ellipsoid.unpack(e,t,$);t+=l.Ellipsoid.packedLength;const y=x.VertexFormat.unpack(e,t,ee);t+=x.VertexFormat.packedLength;const c=e[t++],p=-1===e[t]?void 0:C.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t,o.Cartesian2);s.defined(p)?(t=p.startingIndex,delete p.startingIndex):t++;const u=e[t++];return s.defined(n)||(n=new X(te)),n._polygonHierarchy=a,n._ellipsoid=l.Ellipsoid.clone(i,n._ellipsoid),n._vertexFormat=x.VertexFormat.clone(y,n._vertexFormat),n._stRotation=c,n._textureCoordinates=p,n.packedLength=u,n},X.createGeometry=function(t){const o=t._vertexFormat,a=t._polygonHierarchy,l=t._stRotation,c=t._textureCoordinates,g=s.defined(c);let h=a.positions;if(h=e.arrayRemoveDuplicates(h,r.Cartesian3.equalsEpsilon,!0),h.length<3)return;let x=S,b=N,f=Q,P=q;const A=J;if(!i.CoplanarPolygonGeometryLibrary.computeProjectTo2DArguments(h,j,P,A))return;if(x=r.Cartesian3.cross(P,A,x),x=r.Cartesian3.normalize(x,x),!r.Cartesian3.equalsEpsilon(j,r.Cartesian3.ZERO,d.CesiumMath.EPSILON6)){const e=t._ellipsoid.geodeticSurfaceNormal(j,Z);r.Cartesian3.dot(x,e)<0&&(x=r.Cartesian3.negate(x,x),P=r.Cartesian3.negate(P,P))}const G=i.CoplanarPolygonGeometryLibrary.createProjectPointsTo2DFunction(j,P,A),L=i.CoplanarPolygonGeometryLibrary.createProjectPointTo2DFunction(j,P,A);o.tangent&&(b=r.Cartesian3.clone(P,b)),o.bitangent&&(f=r.Cartesian3.clone(A,f));const w=C.PolygonGeometryLibrary.polygonsFromHierarchy(a,g,G,!1),F=w.hierarchy,v=w.polygons,E=g?C.PolygonGeometryLibrary.polygonsFromHierarchy(c,!0,(function(e){return e}),!1).polygons:void 0;if(0===F.length)return;h=F[0].outerRing;const T=n.BoundingSphere.fromPoints(h),_=C.PolygonGeometryLibrary.computeBoundingRectangle(x,L,h,l,B),D=[];for(let e=0;e<v.length;e++){const t=new p.GeometryInstance({geometry:K(v[e],o,_,l,g?E[e]:void 0,L,x,b,f)});D.push(t)}const k=u.GeometryPipeline.combineInstances(D)[0];k.attributes.position.values=new Float64Array(k.attributes.position.values),k.indices=m.IndexDatatype.createTypedArray(k.attributes.position.values.length/3,k.indices);const V=k.attributes;return o.position||delete V.position,new y.Geometry({attributes:V,indices:k.indices,primitiveType:k.primitiveType,boundingSphere:T})},function(e,t){return s.defined(t)&&(e=X.unpack(e,t)),X.createGeometry(e)}}));
