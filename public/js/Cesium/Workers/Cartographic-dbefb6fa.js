define(["exports","./Cartesian3-529c236c","./defaultValue-f6d5e6da","./Math-355606c6"],(function(e,t,i,n){"use strict";const a=new t.Cartesian3,u=new t.Cartesian3;function r(e,r,d,s,o){const l=e.x,h=e.y,f=e.z,c=r.x,g=r.y,C=r.z,m=l*l*c*c,w=h*h*g*g,p=f*f*C*C,y=m+w+p,O=Math.sqrt(1/y),M=t.Cartesian3.multiplyByScalar(e,O,a);if(y<s)return isFinite(O)?t.Cartesian3.clone(M,o):void 0;const R=d.x,q=d.y,z=d.z,S=u;S.x=M.x*R*2,S.y=M.y*q*2,S.z=M.z*z*2;let x,v,b,E,V,$,I,L,N,P,T,j=(1-O)*t.Cartesian3.magnitude(e)/(.5*t.Cartesian3.magnitude(S)),B=0;do{j-=B,b=1/(1+j*R),E=1/(1+j*q),V=1/(1+j*z),$=b*b,I=E*E,L=V*V,N=$*b,P=I*E,T=L*V,x=m*$+w*I+p*L-1,v=m*N*R+w*P*q+p*T*z;B=x/(-2*v)}while(Math.abs(x)>n.CesiumMath.EPSILON12);return i.defined(o)?(o.x=l*b,o.y=h*E,o.z=f*V,o):new t.Cartesian3(l*b,h*E,f*V)}function d(e,t,n){this.longitude=i.defaultValue(e,0),this.latitude=i.defaultValue(t,0),this.height=i.defaultValue(n,0)}d.fromRadians=function(e,t,n,a){return n=i.defaultValue(n,0),i.defined(a)?(a.longitude=e,a.latitude=t,a.height=n,a):new d(e,t,n)},d.fromDegrees=function(e,t,i,a){return e=n.CesiumMath.toRadians(e),t=n.CesiumMath.toRadians(t),d.fromRadians(e,t,i,a)};const s=new t.Cartesian3,o=new t.Cartesian3,l=new t.Cartesian3;d.wgs84OneOverRadii=new t.Cartesian3(1/6378137,1/6378137,1/6356752.314245179),d.wgs84OneOverRadiiSquared=new t.Cartesian3(1/40680631590769,1/40680631590769,1/40408299984661.445);const h=n.CesiumMath.EPSILON1;d.fromCartesian=function(e,a,u){const f=i.defined(a)?a.oneOverRadii:this.wgs84OneOverRadii,c=i.defined(a)?a.oneOverRadiiSquared:this.wgs84OneOverRadiiSquared,g=r(e,f,c,i.defined(a)?a._centerToleranceSquared:h,o);if(!i.defined(g))return;let C=t.Cartesian3.multiplyComponents(g,c,s);C=t.Cartesian3.normalize(C,C);const m=t.Cartesian3.subtract(e,g,l),w=Math.atan2(C.y,C.x),p=Math.asin(C.z),y=n.CesiumMath.sign(t.Cartesian3.dot(m,e))*t.Cartesian3.magnitude(m);return i.defined(u)?(u.longitude=w,u.latitude=p,u.height=y,u):new d(w,p,y)},d.toCartesian=function(e,i,n){return t.Cartesian3.fromRadians(e.longitude,e.latitude,e.height,i,n)},d.clone=function(e,t){if(i.defined(e))return i.defined(t)?(t.longitude=e.longitude,t.latitude=e.latitude,t.height=e.height,t):new d(e.longitude,e.latitude,e.height)},d.equals=function(e,t){return e===t||i.defined(e)&&i.defined(t)&&e.longitude===t.longitude&&e.latitude===t.latitude&&e.height===t.height},d.equalsEpsilon=function(e,t,n){return n=i.defaultValue(n,0),e===t||i.defined(e)&&i.defined(t)&&Math.abs(e.longitude-t.longitude)<=n&&Math.abs(e.latitude-t.latitude)<=n&&Math.abs(e.height-t.height)<=n},d.ZERO=Object.freeze(new d(0,0,0)),d.prototype.clone=function(e){return d.clone(this,e)},d.prototype.equals=function(e){return d.equals(this,e)},d.prototype.equalsEpsilon=function(e,t){return d.equalsEpsilon(this,e,t)},d.prototype.toString=function(){return`(${this.longitude}, ${this.latitude}, ${this.height})`},e.Cartographic=d,e.scaleToGeodeticSurface=r}));
