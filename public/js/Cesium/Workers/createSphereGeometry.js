define(["./defaultValue-f6d5e6da","./Cartesian3-529c236c","./EllipsoidGeometry-1e084b4a","./VertexFormat-fbdec922","./Math-355606c6","./Transforms-2afbbfb5","./Cartographic-dbefb6fa","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./GeometryAttribute-f7a0845b","./Matrix2-e4a4609a","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-58eb7805"],(function(e,t,r,i,o,a,n,s,l,c,d,m,u,p,y,G,f,k,x,v,E,P,_){"use strict";function F(i){const o=e.defaultValue(i.radius,1),a={radii:new t.Cartesian3(o,o,o),stackPartitions:i.stackPartitions,slicePartitions:i.slicePartitions,vertexFormat:i.vertexFormat};this._ellipsoidGeometry=new r.EllipsoidGeometry(a),this._workerName="createSphereGeometry"}F.packedLength=r.EllipsoidGeometry.packedLength,F.pack=function(e,t,i){return r.EllipsoidGeometry.pack(e._ellipsoidGeometry,t,i)};const h=new r.EllipsoidGeometry,w={radius:void 0,radii:new t.Cartesian3,vertexFormat:new i.VertexFormat,stackPartitions:void 0,slicePartitions:void 0};return F.unpack=function(o,a,n){const s=r.EllipsoidGeometry.unpack(o,a,h);return w.vertexFormat=i.VertexFormat.clone(s._vertexFormat,w.vertexFormat),w.stackPartitions=s._stackPartitions,w.slicePartitions=s._slicePartitions,e.defined(n)?(t.Cartesian3.clone(s._radii,w.radii),n._ellipsoidGeometry=new r.EllipsoidGeometry(w),n):(w.radius=s._radii.x,new F(w))},F.createGeometry=function(e){return r.EllipsoidGeometry.createGeometry(e._ellipsoidGeometry)},function(t,r){return e.defined(r)&&(t=F.unpack(t,r)),F.createGeometry(t)}}));
