define(["./defaultValue-f6d5e6da","./createTaskProcessorWorker","./DeveloperError-c85858c1","./EV_ParseLargePlane-943a091a","./Cartesian3-529c236c","./RuntimeError-9b4ce3fb","./Math-355606c6"],(function(e,a,t,r,n,s,o){"use strict";function c(a,r){if(!e.defined(a))throw new t.DeveloperError("wsserver undefined!");this.url=a,this.startPlay=0,this.callbackFunc=r,this.websocket=new WebSocket(a),this.websocket.onopen=this.onOpen.bind(this),this.websocket.onclose=this.onClose.bind(this),this.websocket.onmessage=this.onMessage.bind(this),this.websocket.onerror=this.onError.bind(this)}function l(e,a){const t=new DataView(e);return{time:t.getBigInt64(a,!0),nLen:t.getUint8(a+8,!0)}}c.prototype.onOpen=function(e){this.websocket.binaryType="arraybuffer",console.log("Connection open ..."),this.doSend(e)},c.prototype.doSend=function(e){this.websocket.send(e)},c.prototype.onMessage=function(e){let a=0;const t=this,n=e.data;if(t.dataI3NSArr=[],t.dataRTSN=[],t.dataRTKN=[],t.dataTRAJ=[],t.dataGTSPI=[],t.dataIrsm=[],t.radarTarget=[],t.RadarWM=[],t.IRSTworkMode=[],t.dataEWS=[],t.LargePlane=[],t.SafetyAlarm=[],t.CombatPower=[],t.StaticTarget=[],t.flightState=[],t.SDRState=[],e.data instanceof ArrayBuffer){for(;a<n.byteLength;){const s=l(e.data,a);a+=10;const o=s.nLen,c=new DataView(n,a,o);let T,S;switch(new r.EV_ParseHeader(c,0).msgType){case r.EV_MsgType.NET_MSG_TSPI:95===o?T=new r.EV_ParseI3NS_Deprecated(c):96===o&&(T=new r.EV_ParseI3NS(c)),t.dataI3NSArr.push(T);break;case r.EV_MsgType.NET_MSG_FIGHTER:{const e=new r.EV_ParseFlight(c);t.flightState.push(e);break}case r.EV_MsgType.NET_MSG_RTSN:{const e=new r.EV_ParseRTSN(c);t.dataRTSN.push(e);break}case r.EV_MsgType.NET_MSG_MISSILEPARAM:S=new r.EV_ParseParam(c);break;case r.EV_MsgType.NET_MSG_RTKN:S=new r.EV_ParseRTKN(c),t.dataRTKN.push(S);break;case r.EV_MsgType.NET_MSG_WPN_TRAJ:S=new r.EV_ParseTRAJ(c),t.dataTRAJ.push(S);break;case r.EV_MsgType.NET_MSG_RDR_STATE:S=new r.EV_ParseRadarWM(c),t.RadarWM.push(S);break;case r.EV_MsgType.NET_MSG_TARGET:S=new r.EV_ParseRadarTarget(c),t.radarTarget.push(S);break;case r.EV_MsgType.NET_MSG_IRST:S=new r.EV_ParseIRSTworkMode(c),t.IRSTworkMode.push(S);break;case r.EV_MsgType.NET_MSG_IRMSL_STATE:S=new r.EV_ParseIrsmState(c),t.dataIrsm.push(S);break;case r.EV_MsgType.NET_MSG_EWS_STA:S=new r.EV_ParseEWS(c),t.dataEWS.push(S);break;case r.EV_MsgType.NET_MSG_START_STOP:S=new r.EV_ParseStartStop(c),console.log(S);break;case r.EV_MsgType.NET_MSG_GTSPI:S=new r.EV_ParseGTspi(c),t.dataGTSPI.push(S);break;case r.EV_MsgType.NET_MSG_RAD_STATE:S=new r.EV_ParseSDRState(c),t.SDRState.push(S);break;case r.EV_MsgType.NET_MSG_COMBATPOWER:S=new r.EV_ParseCombatPower(c),t.CombatPower.push(S);break;case r.EV_MsgType.NET_MSG_SAFETYALARM:S=new r.EV_ParseSafetyAlarm(c),t.SafetyAlarm.push(S);break;case r.EV_MsgType.NET_MSG_LARGE_PLANE:S=new r.EV_ParseLargePlane(c),t.LargePlane.push(S)}a+=o}0!==t.dataI3NSArr.length&&t.callbackFunc(t.dataI3NSArr),0!==t.dataRTKN.length&&t.callbackFunc(t.dataRTKN),0!==t.dataRTSN.length&&t.callbackFunc(t.dataRTSN),0!==t.dataGTSPI.length&&t.callbackFunc(t.dataGTSPI),0!==t.dataTRAJ.length&&t.callbackFunc(t.dataTRAJ),0!==t.dataIrsm.length&&t.callbackFunc(t.dataIrsm),0!==t.radarTarget.length&&t.callbackFunc(t.radarTarget),0!==t.RadarWM.length&&t.callbackFunc(t.RadarWM),0!==t.IRSTworkMode.length&&t.callbackFunc(t.IRSTworkMode),0!==t.dataEWS.length&&t.callbackFunc(t.dataEWS),0!==t.LargePlane.length&&t.callbackFunc(t.LargePlane),0!==t.SafetyAlarm.length&&t.callbackFunc(t.SafetyAlarm),0!==t.CombatPower.length&&t.callbackFunc(t.CombatPower),0!==t.StaticTarget.length&&t.callbackFunc(t.StaticTarget),0!==t.flightState.length&&t.callbackFunc(t.flightState),0!==t.SDRState.length&&t.callbackFunc(t.SDRState)}else t.callbackFunc(e.data)},c.prototype.onClose=function(){console.log("DISCONNECTED")},c.prototype.onError=function(e){console.log("websocket error")};const T=[];let S;const E=self.postMessage;var i=[];function _(e){Array.isArray(e)?95===e[0].SIZE||96===e[0].SIZE?(e.forEach((function(e){e.position=function(e){const a=n.Cartesian3.fromDegrees(e.fLon,e.fLat,e.fAlt);return a}(e)})),E(e)):77===e[0].SIZE?(e.forEach((function(e){const a=e.header.sID;T.forEach((function(t){t.header.sID===a&&(e.nRedOrBlue=t.nRedOrBlue)})),e.position=function(e){const a=n.Cartesian3.fromDegrees(e.mslLongitude,e.mslLatitude,e.mslAltitude);return a}(e)})),E(e)):58===e[0].SIZE?(e.forEach((function(e){e.position=function(e){const a=n.Cartesian3.fromDegrees(e.gTspiLon,e.gTspiLat,e.gTspiAlt);return a}(e)})),E(e)):(139===e[0].SIZE||e[0].SIZE,E(e)):(i.push(e),2==i.length&&E(i))}return a((function(a){const t=a.url;e.defined(t)?S=new c(t,_):S.doSend(1)}))}));
