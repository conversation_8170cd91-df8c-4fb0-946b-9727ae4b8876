define(["./AttributeCompression-d2ca507e","./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./TerrainEncoding-443b37a0","./IndexDatatype-58eb7805","./Math-355606c6","./OrientedBoundingBox-5f5e4f4c","./Rectangle-98b0bef0","./createTaskProcessorWorker","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./Matrix2-e4a4609a","./Matrix3-31d1f01f","./Interval-d6c8d27a","./DeveloperError-c85858c1","./combine-0c102d93","./RequestType-735c98f2","./RuntimeError-9b4ce3fb","./TerrainQuantization-c16f42ed","./EllipsoidTangentPlane-a6ea67fb","./AxisAlignedBoundingBox-d98e5354","./IntersectionTests-01432fe7","./Plane-06f34fae"],(function(e,t,n,i,s,r,h,o,u,p,d,l,a,c,f,g,m,x,C,w,y,B,I,A,v,T,b){"use strict";const M={clipTriangleAtAxisAlignedThreshold:function(e,t,n,i,s,h){let o,u,p;r.defined(h)?h.length=0:h=[],t?(o=n<e,u=i<e,p=s<e):(o=n>e,u=i>e,p=s>e);const d=o+u+p;let l,a,c,f,g,m;return 1===d?o?(l=(e-n)/(i-n),a=(e-n)/(s-n),h.push(1),h.push(2),1!==a&&(h.push(-1),h.push(0),h.push(2),h.push(a)),1!==l&&(h.push(-1),h.push(0),h.push(1),h.push(l))):u?(c=(e-i)/(s-i),f=(e-i)/(n-i),h.push(2),h.push(0),1!==f&&(h.push(-1),h.push(1),h.push(0),h.push(f)),1!==c&&(h.push(-1),h.push(1),h.push(2),h.push(c))):p&&(g=(e-s)/(n-s),m=(e-s)/(i-s),h.push(0),h.push(1),1!==m&&(h.push(-1),h.push(2),h.push(1),h.push(m)),1!==g&&(h.push(-1),h.push(2),h.push(0),h.push(g))):2===d?o||n===e?u||i===e?p||s===e||(a=(e-n)/(s-n),c=(e-i)/(s-i),h.push(2),h.push(-1),h.push(0),h.push(2),h.push(a),h.push(-1),h.push(1),h.push(2),h.push(c)):(m=(e-s)/(i-s),l=(e-n)/(i-n),h.push(1),h.push(-1),h.push(2),h.push(1),h.push(m),h.push(-1),h.push(0),h.push(1),h.push(l)):(f=(e-i)/(n-i),g=(e-s)/(n-s),h.push(0),h.push(-1),h.push(1),h.push(0),h.push(f),h.push(-1),h.push(2),h.push(0),h.push(g)):3!==d&&(h.push(0),h.push(1),h.push(2)),h},computeBarycentricCoordinates:function(e,t,n,s,h,o,u,p,d){const l=n-u,a=u-h,c=o-p,f=s-p,g=1/(c*l+a*f),m=t-p,x=e-u,C=(c*x+a*m)*g,w=(-f*x+l*m)*g,y=1-C-w;return r.defined(d)?(d.x=C,d.y=w,d.z=y,d):new i.Cartesian3(C,w,y)},computeLineSegmentLineSegmentIntersection:function(e,t,i,s,h,o,u,p,d){const l=(p-o)*(i-e)-(u-h)*(s-t);if(0===l)return;const a=((u-h)*(t-o)-(p-o)*(e-h))/l,c=((i-e)*(t-o)-(s-t)*(e-h))/l;return a>=0&&a<=1&&c>=0&&c<=1?(r.defined(d)||(d=new n.Cartesian2),d.x=e+a*(i-e),d.y=t+a*(s-t),d):void 0}};var z=M;const V=32767,E=16383,N=[],R=[],H=[],O=new s.Cartographic;let F=new i.Cartesian3;const S=[],U=[],P=[],D=[],k=[],W=new i.Cartesian3,X=new t.BoundingSphere,K=new d.OrientedBoundingBox,L=new n.Cartesian2,Y=new i.Cartesian3;function q(){this.vertexBuffer=void 0,this.index=void 0,this.first=void 0,this.second=void 0,this.ratio=void 0}q.prototype.clone=function(e){return r.defined(e)||(e=new q),e.uBuffer=this.uBuffer,e.vBuffer=this.vBuffer,e.heightBuffer=this.heightBuffer,e.normalBuffer=this.normalBuffer,e.index=this.index,e.first=this.first,e.second=this.second,e.ratio=this.ratio,e},q.prototype.initializeIndexed=function(e,t,n,i,s){this.uBuffer=e,this.vBuffer=t,this.heightBuffer=n,this.normalBuffer=i,this.index=s,this.first=void 0,this.second=void 0,this.ratio=void 0},q.prototype.initializeFromClipResult=function(e,t,n){let i=t+1;return-1!==e[t]?n[e[t]].clone(this):(this.vertexBuffer=void 0,this.index=void 0,this.first=n[e[i]],++i,this.second=n[e[i]],++i,this.ratio=e[i],++i),i},q.prototype.getKey=function(){return this.isIndexed()?this.index:JSON.stringify({first:this.first.getKey(),second:this.second.getKey(),ratio:this.ratio})},q.prototype.isIndexed=function(){return r.defined(this.index)},q.prototype.getH=function(){return r.defined(this.index)?this.heightBuffer[this.index]:p.CesiumMath.lerp(this.first.getH(),this.second.getH(),this.ratio)},q.prototype.getU=function(){return r.defined(this.index)?this.uBuffer[this.index]:p.CesiumMath.lerp(this.first.getU(),this.second.getU(),this.ratio)},q.prototype.getV=function(){return r.defined(this.index)?this.vBuffer[this.index]:p.CesiumMath.lerp(this.first.getV(),this.second.getV(),this.ratio)};let G=new n.Cartesian2,_=-1;const j=[new i.Cartesian3,new i.Cartesian3],J=[new i.Cartesian3,new i.Cartesian3];function Q(t,n){++_;let s=j[_],r=J[_];return s=e.AttributeCompression.octDecode(t.first.getNormalX(),t.first.getNormalY(),s),r=e.AttributeCompression.octDecode(t.second.getNormalX(),t.second.getNormalY(),r),F=i.Cartesian3.lerp(s,r,t.ratio,F),i.Cartesian3.normalize(F,F),e.AttributeCompression.octEncode(F,n),--_,n}q.prototype.getNormalX=function(){return r.defined(this.index)?this.normalBuffer[2*this.index]:(G=Q(this,G),G.x)},q.prototype.getNormalY=function(){return r.defined(this.index)?this.normalBuffer[2*this.index+1]:(G=Q(this,G),G.y)};const Z=[];function $(e,t,n,i,s,h,o,u,p){if(0===o.length)return;let d=0,l=0;for(;l<o.length;)l=Z[d++].initializeFromClipResult(o,l,u);for(let s=0;s<d;++s){const o=Z[s];if(o.isIndexed())o.newIndex=h[o.index],o.uBuffer=e,o.vBuffer=t,o.heightBuffer=n,p&&(o.normalBuffer=i);else{const s=o.getKey();if(r.defined(h[s]))o.newIndex=h[s];else{const r=e.length;e.push(o.getU()),t.push(o.getV()),n.push(o.getH()),p&&(i.push(o.getNormalX()),i.push(o.getNormalY())),o.newIndex=r,h[s]=r}}}3===d?(s.push(Z[0].newIndex),s.push(Z[1].newIndex),s.push(Z[2].newIndex)):4===d&&(s.push(Z[0].newIndex),s.push(Z[1].newIndex),s.push(Z[2].newIndex),s.push(Z[0].newIndex),s.push(Z[2].newIndex),s.push(Z[3].newIndex))}return Z.push(new q),Z.push(new q),Z.push(new q),Z.push(new q),a((function(e,n){const s=e.isEastChild,r=e.isNorthChild,a=s?E:0,c=s?V:E,f=r?E:0,g=r?V:E,m=S,x=U,C=P,w=k;m.length=0,x.length=0,C.length=0,w.length=0;const y=D;y.length=0;const B={},I=e.vertices;let A=e.indices;A=A.subarray(0,e.indexCountWithoutSkirts);const v=o.TerrainEncoding.clone(e.encoding),T=v.hasVertexNormals;let b=0;const M=e.vertexCountWithoutSkirts,G=e.minimumHeight,_=e.maximumHeight,j=new Array(M),J=new Array(M),Q=new Array(M),Z=T?new Array(2*M):void 0;let ee,te,ne,ie,se;for(te=0,ne=0;te<M;++te,ne+=2){const e=v.decodeTextureCoordinates(I,te,L);if(ee=v.decodeHeight(I,te),ie=p.CesiumMath.clamp(e.x*V|0,0,V),se=p.CesiumMath.clamp(e.y*V|0,0,V),Q[te]=p.CesiumMath.clamp((ee-G)/(_-G)*V|0,0,V),ie<20&&(ie=0),se<20&&(se=0),V-ie<20&&(ie=V),V-se<20&&(se=V),j[te]=ie,J[te]=se,T){const e=v.getOctEncodedNormal(I,te,Y);Z[ne]=e.x,Z[ne+1]=e.y}(s&&ie>=E||!s&&ie<=E)&&(r&&se>=E||!r&&se<=E)&&(B[te]=b,m.push(ie),x.push(se),C.push(Q[te]),T&&(w.push(Z[ne]),w.push(Z[ne+1])),++b)}const re=[];re.push(new q),re.push(new q),re.push(new q);const he=[];let oe,ue;for(he.push(new q),he.push(new q),he.push(new q),te=0;te<A.length;te+=3){const e=A[te],t=A[te+1],n=A[te+2],i=j[e],h=j[t],o=j[n];re[0].initializeIndexed(j,J,Q,Z,e),re[1].initializeIndexed(j,J,Q,Z,t),re[2].initializeIndexed(j,J,Q,Z,n);const u=z.clipTriangleAtAxisAlignedThreshold(E,s,i,h,o,N);oe=0,oe>=u.length||(oe=he[0].initializeFromClipResult(u,oe,re),oe>=u.length||(oe=he[1].initializeFromClipResult(u,oe,re),oe>=u.length||(oe=he[2].initializeFromClipResult(u,oe,re),ue=z.clipTriangleAtAxisAlignedThreshold(E,r,he[0].getV(),he[1].getV(),he[2].getV(),R),$(m,x,C,w,y,B,ue,he,T),oe<u.length&&(he[2].clone(he[1]),he[2].initializeFromClipResult(u,oe,re),ue=z.clipTriangleAtAxisAlignedThreshold(E,r,he[0].getV(),he[1].getV(),he[2].getV(),R),$(m,x,C,w,y,B,ue,he,T)))))}const pe=s?-32767:0,de=r?-32767:0,le=[],ae=[],ce=[],fe=[];let ge=Number.MAX_VALUE,me=-ge;const xe=H;xe.length=0;const Ce=h.Ellipsoid.clone(e.ellipsoid),we=l.Rectangle.clone(e.childRectangle),ye=we.north,Be=we.south;let Ie=we.east;const Ae=we.west;for(Ie<Ae&&(Ie+=p.CesiumMath.TWO_PI),te=0;te<m.length;++te)ie=Math.round(m[te]),ie<=a?(le.push(te),ie=0):ie>=c?(ce.push(te),ie=V):ie=2*ie+pe,m[te]=ie,se=Math.round(x[te]),se<=f?(ae.push(te),se=0):se>=g?(fe.push(te),se=V):se=2*se+de,x[te]=se,ee=p.CesiumMath.lerp(G,_,C[te]/V),ee<ge&&(ge=ee),ee>me&&(me=ee),C[te]=ee,O.longitude=p.CesiumMath.lerp(Ae,Ie,ie/V),O.latitude=p.CesiumMath.lerp(Be,ye,se/V),O.height=ee,Ce.cartographicToCartesian(O,F),xe.push(F.x),xe.push(F.y),xe.push(F.z);const ve=t.BoundingSphere.fromVertices(xe,i.Cartesian3.ZERO,3,X),Te=d.OrientedBoundingBox.fromRectangle(we,ge,me,Ce,K),be=new o.EllipsoidalOccluder(Ce).computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid(ve.center,xe,3,ve.center,ge,W),Me=me-ge,ze=new Uint16Array(m.length+x.length+C.length);for(te=0;te<m.length;++te)ze[te]=m[te];let Ve=m.length;for(te=0;te<x.length;++te)ze[Ve+te]=x[te];for(Ve+=x.length,te=0;te<C.length;++te)ze[Ve+te]=V*(C[te]-ge)/Me;const Ee=u.IndexDatatype.createTypedArray(m.length,y);let Ne;if(T){const e=new Uint8Array(w);n.push(ze.buffer,Ee.buffer,e.buffer),Ne=e.buffer}else n.push(ze.buffer,Ee.buffer);return{vertices:ze.buffer,encodedNormals:Ne,indices:Ee.buffer,minimumHeight:ge,maximumHeight:me,westIndices:le,southIndices:ae,eastIndices:ce,northIndices:fe,boundingSphere:ve,orientedBoundingBox:Te,horizonOcclusionPoint:be}}))}));
