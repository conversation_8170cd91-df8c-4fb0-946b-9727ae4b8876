define(["./AttributeCompression-d2ca507e","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./combine-0c102d93","./Ellipsoid-8e26549b","./IndexDatatype-58eb7805","./Math-355606c6","./Rectangle-98b0bef0","./createTaskProcessorWorker","./Matrix4-c57ffbd8","./defaultValue-f6d5e6da","./Matrix3-31d1f01f","./RuntimeError-9b4ce3fb","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./Matrix2-e4a4609a"],(function(t,e,a,s,n,r,i,o,l,c,d,f,h,u,C,p){"use strict";const m=32767,A=Math.cos(i.CesiumMath.toRadians(150)),b=new a.Cartographic,g=new e.Cartesian3;const w=new a.Cartographic,y=new a.Cartographic;function N(t){const e=8*t,a=3*e,s=4*e;this.startEllipsoidNormals=new Float32Array(a),this.endEllipsoidNormals=new Float32Array(a),this.startPositionAndHeights=new Float32Array(s),this.startFaceNormalAndVertexCornerIds=new Float32Array(s),this.endPositionAndHeights=new Float32Array(s),this.endFaceNormalAndHalfWidths=new Float32Array(s),this.vertexBatchIds=new Uint16Array(e),this.indices=r.IndexDatatype.createTypedArray(e,36*t),this.vec3Offset=0,this.vec4Offset=0,this.batchIdOffset=0,this.indexOffset=0,this.volumeStartIndex=0}const k=new e.Cartesian3,x=new e.Cartesian3;function I(t,a,s,n,r){const i=e.Cartesian3.subtract(s,a,x);let o=e.Cartesian3.subtract(a,t,k);return e.Cartesian3.normalize(i,i),e.Cartesian3.normalize(o,o),e.Cartesian3.dot(i,o)<A&&(o=e.Cartesian3.multiplyByScalar(o,-1,k)),e.Cartesian3.add(i,o,r),e.Cartesian3.equals(r,e.Cartesian3.ZERO)&&(r=e.Cartesian3.subtract(t,a)),e.Cartesian3.cross(r,n,r),e.Cartesian3.cross(n,r,r),e.Cartesian3.normalize(r,r),r}const E=[0,2,6,0,6,4,0,1,3,0,3,2,0,4,5,0,5,1,5,3,1,5,7,3,7,5,4,7,4,6,7,6,2,7,2,3],F=E.length,H=new e.Cartesian3,O=new e.Cartesian3,P=new e.Cartesian3,v=new e.Cartesian3,M=new e.Cartesian3;N.prototype.addVolume=function(t,a,s,n,r,i,o,l,c,d){let f=e.Cartesian3.add(a,c,H);const h=d.geodeticSurfaceNormal(f,O);f=e.Cartesian3.add(s,c,H);const u=d.geodeticSurfaceNormal(f,v),C=I(t,a,s,h,P),p=I(n,s,a,u,M),m=this.startEllipsoidNormals,A=this.endEllipsoidNormals,b=this.startPositionAndHeights,g=this.startFaceNormalAndVertexCornerIds,w=this.endPositionAndHeights,y=this.endFaceNormalAndHalfWidths,N=this.vertexBatchIds;let k,x=this.batchIdOffset,D=this.vec3Offset,R=this.vec4Offset;for(k=0;k<8;k++)e.Cartesian3.pack(h,m,D),e.Cartesian3.pack(u,A,D),e.Cartesian3.pack(a,b,R),b[R+3]=r,e.Cartesian3.pack(s,w,R),w[R+3]=i,e.Cartesian3.pack(C,g,R),g[R+3]=k,e.Cartesian3.pack(p,y,R),y[R+3]=o,N[x++]=l,D+=3,R+=4;this.batchIdOffset=x,this.vec3Offset=D,this.vec4Offset=R;const S=this.indices,U=this.volumeStartIndex,B=this.indexOffset;for(k=0;k<F;k++)S[B+k]=E[k]+U;this.volumeStartIndex+=8,this.indexOffset+=F};const D=new o.Rectangle,R=new n.Ellipsoid,S=new e.Cartesian3,U=new e.Cartesian3,B=new e.Cartesian3,V=new e.Cartesian3,T=new e.Cartesian3;return l((function(l,c){const d=new Uint16Array(l.positions),f=new Uint16Array(l.widths),h=new Uint32Array(l.counts),u=new Uint16Array(l.batchIds),C=D,p=R,A=S,k=new Float64Array(l.packedBuffer);let x=0;const I=k[x++],E=k[x++];let F;o.Rectangle.unpack(k,x,C),x+=o.Rectangle.packedLength,n.Ellipsoid.unpack(k,x,p),x+=n.Ellipsoid.packedLength,e.Cartesian3.unpack(k,x,A);let H=d.length/3;const O=d.subarray(0,H),P=d.subarray(H,2*H),v=d.subarray(2*H,3*H);t.AttributeCompression.zigZagDeltaDecode(O,P,v),function(t,e,s,n){const r=n.length,i=t.length,o=new Uint8Array(i),l=w,c=y;let d=0;for(let s=0;s<r;s++){const r=n[s];let i=r;for(let s=1;s<r;s++){const n=d+s,r=n-1;c.longitude=t[n],c.latitude=e[n],l.longitude=t[r],l.latitude=e[r],a.Cartographic.equals(c,l)&&(i--,o[r]=1)}n[s]=i,d+=r}let f=0;for(let a=0;a<i;a++)1!==o[a]&&(t[f]=t[a],e[f]=e[a],s[f]=s[a],f++)}(O,P,v,h);const M=h.length;let W=0;for(F=0;F<M;F++){W+=h[F]-1}const q=new N(W),z=function(t,s,n,r,o,l,c){const d=t.length,f=new Float64Array(3*d);for(let h=0;h<d;++h){const d=t[h],u=s[h],C=n[h],p=i.CesiumMath.lerp(r.west,r.east,d/m),A=i.CesiumMath.lerp(r.south,r.north,u/m),w=i.CesiumMath.lerp(o,l,C/m),y=a.Cartographic.fromRadians(p,A,w,b),N=c.cartographicToCartesian(y,g);e.Cartesian3.pack(N,f,3*h)}return f}(O,P,v,C,I,E,p);H=O.length;const L=new Float32Array(3*H);for(F=0;F<H;++F)L[3*F]=z[3*F]-A.x,L[3*F+1]=z[3*F+1]-A.y,L[3*F+2]=z[3*F+2]-A.z;let _=0,G=0;for(F=0;F<M;F++){const t=h[F]-1,a=.5*f[F],s=u[F],n=_;for(let r=0;r<t;r++){const o=e.Cartesian3.unpack(L,_,B),l=e.Cartesian3.unpack(L,_+3,V);let c=v[G],d=v[G+1];c=i.CesiumMath.lerp(I,E,c/m),d=i.CesiumMath.lerp(I,E,d/m),G++;let f=U,h=T;if(0===r){const a=n+3*t,s=e.Cartesian3.unpack(L,a,U);if(e.Cartesian3.equals(s,o))e.Cartesian3.unpack(L,a-3,f);else{const t=e.Cartesian3.subtract(o,l,U);f=e.Cartesian3.add(t,o,U)}}else e.Cartesian3.unpack(L,_-3,f);if(r===t-1){const t=e.Cartesian3.unpack(L,n,T);if(e.Cartesian3.equals(t,l))e.Cartesian3.unpack(L,n+3,h);else{const t=e.Cartesian3.subtract(l,o,T);h=e.Cartesian3.add(t,l,T)}}else e.Cartesian3.unpack(L,_+6,h);q.addVolume(f,o,l,h,c,d,a,s,A,p),_+=3}_+=3,G++}const Z=q.indices;c.push(q.startEllipsoidNormals.buffer),c.push(q.endEllipsoidNormals.buffer),c.push(q.startPositionAndHeights.buffer),c.push(q.startFaceNormalAndVertexCornerIds.buffer),c.push(q.endPositionAndHeights.buffer),c.push(q.endFaceNormalAndHalfWidths.buffer),c.push(q.vertexBatchIds.buffer),c.push(Z.buffer);let j={indexDatatype:2===Z.BYTES_PER_ELEMENT?r.IndexDatatype.UNSIGNED_SHORT:r.IndexDatatype.UNSIGNED_INT,startEllipsoidNormals:q.startEllipsoidNormals.buffer,endEllipsoidNormals:q.endEllipsoidNormals.buffer,startPositionAndHeights:q.startPositionAndHeights.buffer,startFaceNormalAndVertexCornerIds:q.startFaceNormalAndVertexCornerIds.buffer,endPositionAndHeights:q.endPositionAndHeights.buffer,endFaceNormalAndHalfWidths:q.endFaceNormalAndHalfWidths.buffer,vertexBatchIds:q.vertexBatchIds.buffer,indices:Z.buffer};if(l.keepDecodedPositions){const t=function(t){const e=t.length,a=new Uint32Array(e+1);let s=0;for(let n=0;n<e;++n)a[n]=s,s+=t[n];return a[e]=s,a}(h);c.push(z.buffer,t.buffer),j=s.combine(j,{decodedPositions:z.buffer,decodedPositionOffsets:t.buffer})}return j}))}));
