define(["exports","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Matrix2-e4a4609a","./Matrix3-31d1f01f","./WebGLConstants-7f557f93","./Transforms-2afbbfb5","./Rectangle-98b0bef0"],(function(t,e,n,a,r,i,o,s,u,I){"use strict";var N=Object.freeze({NONE:0,TRIANGLES:1,LINES:2,POLYLINES:3});const T={POINTS:s.WebGLConstants.POINTS,LINES:s.WebGLConstants.LINES,LINE_LOOP:s.WebGLConstants.LINE_LOOP,LINE_STRIP:s.WebGLConstants.LINE_STRIP,TRIANGLES:s.WebGLConstants.TRIANGLES,TRIANGLE_STRIP:s.WebGLConstants.TRIANGLE_STRIP,TRIANGLE_FAN:s.WebGLConstants.TRIANGLE_FAN,isLines:function(t){return t===T.LINES||t===T.LINE_LOOP||t===T.LINE_STRIP},isTriangles:function(t){return t===T.TRIANGLES||t===T.TRIANGLE_STRIP||t===T.TRIANGLE_FAN},validate:function(t){return t===T.POINTS||t===T.LINES||t===T.LINE_LOOP||t===T.LINE_STRIP||t===T.TRIANGLES||t===T.TRIANGLE_STRIP||t===T.TRIANGLE_FAN}};var c=Object.freeze(T);function l(t){t=r.defaultValue(t,r.defaultValue.EMPTY_OBJECT),this.attributes=t.attributes,this.indices=t.indices,this.primitiveType=r.defaultValue(t.primitiveType,c.TRIANGLES),this.boundingSphere=t.boundingSphere,this.geometryType=r.defaultValue(t.geometryType,N.NONE),this.boundingSphereCV=t.boundingSphereCV,this.offsetAttribute=t.offsetAttribute}l.computeNumberOfVertices=function(t){let e=-1;for(const n in t.attributes)if(t.attributes.hasOwnProperty(n)&&r.defined(t.attributes[n])&&r.defined(t.attributes[n].values)){const a=t.attributes[n];e=a.values.length/a.componentsPerAttribute}return e};const L=new a.Cartographic,E=new n.Cartesian3,f=new e.Matrix4,p=[new a.Cartographic,new a.Cartographic,new a.Cartographic],m=[new e.Cartesian2,new e.Cartesian2,new e.Cartesian2],y=[new e.Cartesian2,new e.Cartesian2,new e.Cartesian2],C=new n.Cartesian3,h=new u.Quaternion,b=new e.Matrix4,x=new i.Matrix2;l._textureCoordinateRotationPoints=function(t,r,s,N){let T;const c=I.Rectangle.center(N,L),l=a.Cartographic.toCartesian(c,s,E),A=u.Transforms.eastNorthUpToFixedFrame(l,s,f),S=e.Matrix4.inverse(A,f),d=m,P=p;P[0].longitude=N.west,P[0].latitude=N.south,P[1].longitude=N.west,P[1].latitude=N.north,P[2].longitude=N.east,P[2].latitude=N.south;let G=C;for(T=0;T<3;T++)a.Cartographic.toCartesian(P[T],s,G),G=e.Matrix4.multiplyByPointAsVector(S,G,G),d[T].x=G.x,d[T].y=G.y;const R=u.Quaternion.fromAxisAngle(n.Cartesian3.UNIT_Z,-r,h),_=o.Matrix3.fromQuaternion(R,b),g=t.length;let O=Number.POSITIVE_INFINITY,w=Number.POSITIVE_INFINITY,M=Number.NEGATIVE_INFINITY,V=Number.NEGATIVE_INFINITY;for(T=0;T<g;T++)G=e.Matrix4.multiplyByPointAsVector(S,t[T],G),G=o.Matrix3.multiplyByVector(_,G,G),O=Math.min(O,G.x),w=Math.min(w,G.y),M=Math.max(M,G.x),V=Math.max(V,G.y);const v=i.Matrix2.fromRotation(r,x),F=y;F[0].x=O,F[0].y=w,F[1].x=O,F[1].y=V,F[2].x=M,F[2].y=w;const W=d[0],Y=d[2].x-W.x,B=d[1].y-W.y;for(T=0;T<3;T++){const t=F[T];i.Matrix2.multiplyByVector(v,t,t),t.x=(t.x-W.x)/Y,t.y=(t.y-W.y)/B}const z=F[0],j=F[1],k=F[2],Q=new Array(6);return e.Cartesian2.pack(z,Q),e.Cartesian2.pack(j,Q,2),e.Cartesian2.pack(k,Q,4),Q},t.Geometry=l,t.GeometryAttribute=function(t){t=r.defaultValue(t,r.defaultValue.EMPTY_OBJECT),this.componentDatatype=t.componentDatatype,this.componentsPerAttribute=t.componentsPerAttribute,this.normalize=r.defaultValue(t.normalize,!1),this.values=t.values},t.GeometryType=N,t.PrimitiveType=c}));
