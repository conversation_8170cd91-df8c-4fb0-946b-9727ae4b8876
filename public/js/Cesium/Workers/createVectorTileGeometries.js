define(["./Transforms-2afbbfb5","./BoxGeometry-24f2bb94","./Cartesian3-529c236c","./Color-e24f904b","./CylinderGeometry-c6e90524","./defaultValue-f6d5e6da","./EllipsoidGeometry-1e084b4a","./IndexDatatype-58eb7805","./Matrix4-c57ffbd8","./createTaskProcessorWorker","./Cartographic-dbefb6fa","./Math-355606c6","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./RuntimeError-9b4ce3fb","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./GeometryAttribute-f7a0845b","./Matrix2-e4a4609a","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./VertexFormat-fbdec922","./CylinderGeometryLibrary-4d7f606d"],(function(e,t,n,r,i,a,o,s,d,c,l,f,u,h,p,b,y,x,g,m,C,I,M,k,B,w,A,v){"use strict";function O(e){this.offset=e.offset,this.count=e.count,this.color=e.color,this.batchIds=e.batchIds}const E=new n.Cartesian3,L=d.Matrix4.packedLength+n.Cartesian3.packedLength,U=d.Matrix4.packedLength+2,G=d.Matrix4.packedLength+n.Cartesian3.packedLength,T=n.Cartesian3.packedLength+1,S={modelMatrix:new d.Matrix4,boundingVolume:new e.BoundingSphere};function V(e,t){let r=t*L;const i=n.Cartesian3.unpack(e,r,E);r+=n.Cartesian3.packedLength;const a=d.Matrix4.unpack(e,r,S.modelMatrix);d.Matrix4.multiplyByScale(a,i,a);const o=S.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,o.center),o.radius=Math.sqrt(3),S}function R(e,t){let r=t*U;const i=e[r++],a=e[r++],o=n.Cartesian3.fromElements(i,i,a,E),s=d.Matrix4.unpack(e,r,S.modelMatrix);d.Matrix4.multiplyByScale(s,o,s);const c=S.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,c.center),c.radius=Math.sqrt(2),S}function F(e,t){let r=t*G;const i=n.Cartesian3.unpack(e,r,E);r+=n.Cartesian3.packedLength;const a=d.Matrix4.unpack(e,r,S.modelMatrix);d.Matrix4.multiplyByScale(a,i,a);const o=S.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,o.center),o.radius=1,S}function q(e,t){let r=t*T;const i=e[r++],a=n.Cartesian3.unpack(e,r,E),o=d.Matrix4.fromTranslation(a,S.modelMatrix);d.Matrix4.multiplyByUniformScale(o,i,o);const s=S.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,s.center),s.radius=1,S}const D=new n.Cartesian3;function P(t,i,o,s,c){if(!a.defined(i))return;const l=o.length,f=s.attributes.position.values,u=s.indices,h=t.positions,p=t.vertexBatchIds,b=t.indices,y=t.batchIds,x=t.batchTableColors,g=t.batchedIndices,m=t.indexOffsets,C=t.indexCounts,I=t.boundingVolumes,M=t.modelMatrix,k=t.center;let B=t.positionOffset,w=t.batchIdIndex,A=t.indexOffset;const v=t.batchedIndicesOffset;for(let t=0;t<l;++t){const a=c(i,t),s=a.modelMatrix;d.Matrix4.multiply(M,s,s);const l=o[t],E=f.length;for(let e=0;e<E;e+=3){const t=n.Cartesian3.unpack(f,e,D);d.Matrix4.multiplyByPoint(s,t,t),n.Cartesian3.subtract(t,k,t),n.Cartesian3.pack(t,h,3*B+e),p[w++]=l}const L=u.length;for(let e=0;e<L;++e)b[A+e]=u[e]+B;const U=t+v;g[U]=new O({offset:A,count:L,color:r.Color.fromRgba(x[l]),batchIds:[l]}),y[U]=l,m[U]=A,C[U]=L,I[U]=e.BoundingSphere.transform(a.boundingVolume,s),B+=E/3,A+=L}t.positionOffset=B,t.batchIdIndex=w,t.indexOffset=A,t.batchedIndicesOffset+=l}const Z=new n.Cartesian3,W=new d.Matrix4;function _(t,n,i){const a=i.length,o=2+a*e.BoundingSphere.packedLength+1+function(e){const t=e.length;let n=0;for(let i=0;i<t;++i)n+=r.Color.packedLength+3+e[i].batchIds.length;return n}(n),s=new Float64Array(o);let d=0;s[d++]=t,s[d++]=a;for(let t=0;t<a;++t)e.BoundingSphere.pack(i[t],s,d),d+=e.BoundingSphere.packedLength;const c=n.length;s[d++]=c;for(let e=0;e<c;++e){const t=n[e];r.Color.pack(t.color,s,d),d+=r.Color.packedLength,s[d++]=t.offset,s[d++]=t.count;const i=t.batchIds,a=i.length;s[d++]=a;for(let e=0;e<a;++e)s[d++]=i[e]}return s}return c((function(e,r){const c=a.defined(e.boxes)?new Float32Array(e.boxes):void 0,l=a.defined(e.boxBatchIds)?new Uint16Array(e.boxBatchIds):void 0,f=a.defined(e.cylinders)?new Float32Array(e.cylinders):void 0,u=a.defined(e.cylinderBatchIds)?new Uint16Array(e.cylinderBatchIds):void 0,h=a.defined(e.ellipsoids)?new Float32Array(e.ellipsoids):void 0,p=a.defined(e.ellipsoidBatchIds)?new Uint16Array(e.ellipsoidBatchIds):void 0,b=a.defined(e.spheres)?new Float32Array(e.spheres):void 0,y=a.defined(e.sphereBatchIds)?new Uint16Array(e.sphereBatchIds):void 0,x=a.defined(c)?l.length:0,g=a.defined(f)?u.length:0,m=a.defined(h)?p.length:0,C=a.defined(b)?y.length:0,I=t.BoxGeometry.getUnitBox(),M=i.CylinderGeometry.getUnitCylinder(),k=o.EllipsoidGeometry.getUnitEllipsoid(),B=I.attributes.position.values,w=M.attributes.position.values,A=k.attributes.position.values;let v=B.length*x;v+=w.length*g,v+=A.length*(m+C);const O=I.indices,E=M.indices,L=k.indices;let U=O.length*x;U+=E.length*g,U+=L.length*(m+C);const G=new Float32Array(v),T=new Uint16Array(v/3),S=s.IndexDatatype.createTypedArray(v/3,U),D=x+g+m+C,j=new Uint16Array(D),N=new Array(D),Q=new Uint32Array(D),Y=new Uint32Array(D),z=new Array(D);!function(e){const t=new Float64Array(e);let r=0;n.Cartesian3.unpack(t,r,Z),r+=n.Cartesian3.packedLength,d.Matrix4.unpack(t,r,W)}(e.packedBuffer);const H={batchTableColors:new Uint32Array(e.batchTableColors),positions:G,vertexBatchIds:T,indices:S,batchIds:j,batchedIndices:N,indexOffsets:Q,indexCounts:Y,boundingVolumes:z,positionOffset:0,batchIdIndex:0,indexOffset:0,batchedIndicesOffset:0,modelMatrix:W,center:Z};P(H,c,l,I,V),P(H,f,u,M,R),P(H,h,p,k,F),P(H,b,y,k,q);const J=_(S.BYTES_PER_ELEMENT,N,z);return r.push(G.buffer,T.buffer,S.buffer),r.push(j.buffer,Q.buffer,Y.buffer),r.push(J.buffer),{positions:G.buffer,vertexBatchIds:T.buffer,indices:S.buffer,indexOffsets:Q.buffer,indexCounts:Y.buffer,batchIds:j.buffer,packedBuffer:J.buffer}}))}));
