define(["./createTaskProcessorWorker","./Cartographic-dbefb6fa","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Math-355606c6","./IntersectionTests-01432fe7","./defaultValue-f6d5e6da","./Interval-d6c8d27a","./DeveloperError-c85858c1","./AttributeCompression-d2ca507e","./TerrainQuantization-c16f42ed","./Matrix3-31d1f01f","./RuntimeError-9b4ce3fb","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./Matrix2-e4a4609a"],(function(e,t,a,i,n,r,s,o,l,u,h,C,d,c,m,f){"use strict";let M=self.postMessage,g=[],p=-90,w=new i.Cartesian3;function P(e,t,n,r,s){let o=[],l=s.length/e,u=r*l;console.log(r,u);for(let e=0;e<l;e++){let r=t[e],l=s[u+e];l=a.Matrix4.multiplyByVector(n,new a.Cartesian4(l.x,l.y,l.z,1),new i.Cartesian3);let h=i.Cartesian3.fromCartesian4(l);o.push({mPitchPoint:h,mPitchAngle:r.mPitchAngle})}return o}function z(e,a,r,o,l){let u,h=[],C=[],d=t.Cartographic.fromCartesian(r),c=t.Cartographic.fromCartesian(o),m=n.CesiumMath.toDegrees(d.longitude),f=n.CesiumMath.toDegrees(d.latitude),M=n.CesiumMath.toDegrees(c.longitude),g=n.CesiumMath.toDegrees(c.latitude);for(let t=a.length-1;t>=0&&(u=y(e,d,a[t]),!s.defined(u));t--);let w=new i.Cartesian3.fromDegrees(m,f,u);C=function(e,a,r,o,l,u,h,C,d){let c=[],m=0,f=function(e,t,a,i){let r=n.CesiumMath.toRadians(i-t),s=n.CesiumMath.toRadians(a-e),o=Math.sin(.5*s),l=Math.sin(.5*r),u=o*o+Math.cos(n.CesiumMath.toRadians(e))*Math.cos(n.CesiumMath.toRadians(a))*l*l;return 2*Math.asin(Math.min(1,Math.sqrt(u)))}(l,o,h,u);if(0==f)return c.push(i.Cartesian3.fromDegrees(o,l,C)),c.push(i.Cartesian3.fromDegrees(o,l,C)),c;let M=Math.sin(f),g=Math.cos(n.CesiumMath.toRadians(l)),p=Math.cos(n.CesiumMath.toRadians(h));for(let C=0;C<=r;C++){let w=C/r,P=Math.sin((1-w)*f)/M,z=Math.sin(w*f)/M,x=P*g*Math.cos(n.CesiumMath.toRadians(o))+z*p*Math.cos(n.CesiumMath.toRadians(u)),R=P*g*Math.sin(n.CesiumMath.toRadians(o))+z*p*Math.sin(n.CesiumMath.toRadians(u)),b=P*Math.sin(n.CesiumMath.toRadians(l))+z*Math.sin(n.CesiumMath.toRadians(h)),v=n.CesiumMath.toDegrees(Math.atan2(b,Math.sqrt(x*x+R*R))),A=n.CesiumMath.toDegrees(Math.atan2(R,x));if(d)for(let i=a.length-1;i>=0&&(m=y(e,new t.Cartographic(n.CesiumMath.toRadians(A),n.CesiumMath.toRadians(v)),a[i]),!s.defined(m));i--);c.push(i.Cartesian3.fromDegrees(A,v,m))}return c}(e,a,l,m,f,M,g,u,!0);let P=C.length;h=[{mPitchAngle:-90,mPitchPoint:C[0]}];let z=i.Cartesian3.normalize(new i.Cartesian3(-w.x,-w.y,-w.z),new i.Cartesian3);for(let e=0;e<P-1;e++){let t=i.Cartesian3.subtract(C[P-1-e],w);if(0!=t.x&&0!=t.y&&0!=t.z){let a=i.Cartesian3.normalize(t,new i.Cartesian3),r=i.Cartesian3.dot(z,a),s=n.CesiumMath.toDegrees(Math.acos(r))-90;s>p&&(h.push({mPitchAngle:s,mPitchPoint:C[P-1-e]}),p=s)}}return h}function x(e,t,a,r){let s=a.length;for(let o=0;o<s-1;o++){let s=i.Cartesian3.normalize(new i.Cartesian3(-r.x,-r.y,-r.z),new i.Cartesian3),l=i.Cartesian3.subtract(a[o+1].mPitchPoint,r);if(0!=l.x&&0!=l.y&&0!=l.z){let u=i.Cartesian3.normalize(l,new i.Cartesian3),h=n.CesiumMath.toDegrees(Math.acos(i.Cartesian3.dot(s,u)))-90,C=a[o+1].mPitchPoint;if(h>=p);else{const a=t.length;for(let n=0;n<a-1;n++){let a=t[n].mPitchAngle,s=t[n].mPitchPoint,l=t[n+1].mPitchAngle,u=t[n+1].mPitchPoint;if(h>a&&h<l){let t=i.Cartesian3.normalize(i.Cartesian3.subtract(r,s,new i.Cartesian3),new i.Cartesian3),n=i.Cartesian3.normalize(i.Cartesian3.subtract(u,s,new i.Cartesian3),new i.Cartesian3),l=i.Cartesian3.subtract(s,r,new i.Cartesian3),d=i.Cartesian3.subtract(C,r,new i.Cartesian3),c=h-a,m=Math.acos(i.Cartesian3.dot(t,n)),f=Math.tan(m)*i.Cartesian3.magnitude(l)/(Math.tan(m)+Math.tan(c))/i.Cartesian3.magnitude(d);f>1&&(f=1),f<0&&(f=1e-5),g[e+o].powerScale=f}}}}}}function y(e,a,u){let h=i.Cartesian3.fromRadians(a.longitude,a.latitude),C=new r.Ray;R(h,e,C.direction),function(e,t,a,r){if(!n.CesiumMath.equalsEpsilon(e._radii.x,e._radii.y,n.CesiumMath.EPSILON15))throw new l.DeveloperError("Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)");o.Check.typeOf.number.greaterThan("Ellipsoid.radii.z",e._radii.z,0),a=s.defaultValue(a,0);let u=e._squaredXOverSquaredZ;s.defined(r)||(r=new i.Cartesian3);if(r.x=0,r.y=0,r.z=t.z*(1-u),Math.abs(r.z)>=e._radii.z-a)return}(e,h,11500,C.origin);let d=function(e,t){if(!s.defined(t))return;let a=t.vertices,n=t.indices,o=t.encoding,l=n.length,u=Number.MAX_VALUE;for(let t=0;t<l;t+=3){let l=n[t],h=n[t+1],C=n[t+2],d=b(o,void 0,void 0,a,l,new i.Cartesian3),c=b(o,void 0,void 0,a,h,new i.Cartesian3),m=b(o,void 0,void 0,a,C,new i.Cartesian3),f=r.IntersectionTests.rayTriangleParametric(e,d,c,m,!1);s.defined(f)&&f<u&&f>=0&&(u=f)}return u!==Number.MAX_VALUE?r.Ray.getPoint(e,u,new i.Cartesian3):void 0}(C,u);if(s.defined(d))return function(e,a,r){let o=t.scaleToGeodeticSurface(a,e._oneOverRadii,e._oneOverRadiiSquared,e._centerToleranceSquared,new i.Cartesian3);if(!s.defined(o))return;let l=R(o,e),u=i.Cartesian3.subtract(a,o),h=Math.atan2(l.y,l.x),C=Math.asin(l.z),d=n.CesiumMath.sign(i.Cartesian3.dot(u,a))*i.Cartesian3.magnitude(u);if(!s.defined(r))return new t.Cartographic(h,C,d);return r.longitude=h,r.latitude=C,r.height=d,r}(e,d,new t.Cartographic).height}function R(e,t,a){return s.defined(a)||(a=new i.Cartesian3),a=i.Cartesian3.multiplyComponents(e,t._oneOverRadiiSquared,a),i.Cartesian3.normalize(a,a)}function b(e,t,n,r,o,l){return function(e,t,n,r){s.defined(r)||(r=new i.Cartesian3);if(n*=function(e){let t;if(e.quantization===h.TerrainQuantization.BITS12)t=3;else t=6;e.hasWebMercatorT&&++t;e.hasVertexNormals&&++t;return t}(e),e.quantization===h.TerrainQuantization.BITS12){let i=u.AttributeCompression.decompressTextureCoordinates(t[n],new a.Cartesian2);r.x=i.x,r.y=i.y;let s=u.AttributeCompression.decompressTextureCoordinates(t[n+1],new a.Cartesian2);return r.z=s.x,a.Matrix4.multiplyByPoint(e.fromScaledENU,r,r)}r.x=t[n],r.y=t[n+1],r.z=t[n+2],i.Cartesian3.add(r,e.center,r)}(e,r,o,l),l}return e((function(e){let t=e.count,a=e.index,i=e.taskNum,n=e.sectionPoints,r=e.modelMatrix,s=e.ellipsoid,o=e.tileMesh,l=e.radarPosition,u=e.splitNum,h=(t-1)/i,C=(t-1)/i;a==i-1&&(h+=1);let d=e.vertexVector,c=d.length/t;for(let e=0;e<h;e++){let i=e+a*C,h=[];h=h.concat(P(t,n,r,i,d));let m=h.length;for(let e=0;e<m;e++){let t=h[e].mPitchAngle,a={};a.verticalAngle=t,a.powerScale=1,g.push(a)}for(let t=0;t<c;t++){w=h[t].mPitchPoint,p=-90,x(e*c,z(s,o,l,w,u),h,l)}}M({index:a,mpScaleVectorList:g})}))}));
