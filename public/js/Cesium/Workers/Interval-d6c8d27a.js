define(["exports","./defaultValue-f6d5e6da","./DeveloperError-c85858c1"],(function(e,t,r){"use strict";const o={};function n(e,t,r){return`Expected ${r} to be typeof ${t}, actual typeof was ${e}`}o.typeOf={},o.defined=function(e,o){if(!t.defined(o))throw new r.DeveloperError(function(e){return`${e} is required, actual value was undefined`}(e))},o.typeOf.func=function(e,t){if("function"!=typeof t)throw new r.DeveloperError(n(typeof t,"function",e))},o.typeOf.string=function(e,t){if("string"!=typeof t)throw new r.DeveloperError(n(typeof t,"string",e))},o.typeOf.number=function(e,t){if("number"!=typeof t)throw new r.DeveloperError(n(typeof t,"number",e))},o.typeOf.number.lessThan=function(e,t,n){if(o.typeOf.number(e,t),t>=n)throw new r.DeveloperError(`Expected ${e} to be less than ${n}, actual value was ${t}`)},o.typeOf.number.lessThanOrEquals=function(e,t,n){if(o.typeOf.number(e,t),t>n)throw new r.DeveloperError(`Expected ${e} to be less than or equal to ${n}, actual value was ${t}`)},o.typeOf.number.greaterThan=function(e,t,n){if(o.typeOf.number(e,t),t<=n)throw new r.DeveloperError(`Expected ${e} to be greater than ${n}, actual value was ${t}`)},o.typeOf.number.greaterThanOrEquals=function(e,t,n){if(o.typeOf.number(e,t),t<n)throw new r.DeveloperError(`Expected ${e} to be greater than or equal to ${n}, actual value was ${t}`)},o.typeOf.object=function(e,t){if("object"!=typeof t)throw new r.DeveloperError(n(typeof t,"object",e))},o.typeOf.bool=function(e,t){if("boolean"!=typeof t)throw new r.DeveloperError(n(typeof t,"boolean",e))},o.typeOf.bigint=function(e,t){if("bigint"!=typeof t)throw new r.DeveloperError(n(typeof t,"bigint",e))},o.typeOf.number.equals=function(e,t,n,f){if(o.typeOf.number(e,n),o.typeOf.number(t,f),n!==f)throw new r.DeveloperError(`${e} must be equal to ${t}, the actual values are ${n} and ${f}`)};var f=o;e.Check=f,e.Interval=function(e,r){this.start=t.defaultValue(e,0),this.stop=t.defaultValue(r,0)}}));
