define(["exports","./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./ComponentDatatype-ab629b88","./defaultValue-f6d5e6da","./EllipseGeometryLibrary-15deff83","./Ellipsoid-8e26549b","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryInstance-1d11f88d","./GeometryOffsetAttribute-2579b8d2","./GeometryPipeline-69b47aa9","./IndexDatatype-58eb7805","./Math-355606c6","./Matrix3-31d1f01f","./Rectangle-98b0bef0","./VertexFormat-fbdec922"],(function(t,e,i,r,n,o,a,s,l,u,m,c,p,y,d,f,A,x,h){"use strict";const g=new r.Cartesian3,_=new r.Cartesian3,b=new r.Cartesian3,C=new r.Cartesian3,w=new i.Cartesian2,M=new A.Matrix3,E=new A.Matrix3,I=new e.Quaternion,T=new r.Cartesian3,G=new r.Cartesian3,N=new r.Cartesian3,P=new n.Cartographic,v=new r.Cartesian3,V=new i.Cartesian2,F=new i.Cartesian2;function D(t,n,l){const c=n.vertexFormat,y=n.center,d=n.semiMajorAxis,f=n.semiMinorAxis,x=n.ellipsoid,h=n.stRotation,C=l?t.length/3*2:t.length/3,D=n.shadowVolume,O=c.st?new Float32Array(2*C):void 0,S=c.normal?new Float32Array(3*C):void 0,L=c.tangent?new Float32Array(3*C):void 0,R=c.bitangent?new Float32Array(3*C):void 0,j=D?new Float32Array(3*C):void 0;let z=0,k=T,B=G,Y=N;const H=new e.GeographicProjection(x),U=H.project(x.cartesianToCartographic(y,P),v),Q=x.scaleToGeodeticSurface(y,g);x.geodeticSurfaceNormal(Q,Q);let W=M,q=E;if(0!==h){let t=e.Quaternion.fromAxisAngle(Q,h,I);W=A.Matrix3.fromQuaternion(t,W),t=e.Quaternion.fromAxisAngle(Q,-h,I),q=A.Matrix3.fromQuaternion(t,q)}else W=A.Matrix3.clone(A.Matrix3.IDENTITY,W),q=A.Matrix3.clone(A.Matrix3.IDENTITY,q);const J=i.Cartesian2.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,V),Z=i.Cartesian2.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,F);let K=t.length;const X=l?K:0,$=X/3*2;for(let e=0;e<K;e+=3){const i=e+1,n=e+2,o=r.Cartesian3.fromArray(t,e,g);if(c.st){const t=A.Matrix3.multiplyByVector(W,o,_),e=H.project(x.cartesianToCartographic(t,P),b);r.Cartesian3.subtract(e,U,e),w.x=(e.x+d)/(2*d),w.y=(e.y+f)/(2*f),J.x=Math.min(w.x,J.x),J.y=Math.min(w.y,J.y),Z.x=Math.max(w.x,Z.x),Z.y=Math.max(w.y,Z.y),l&&(O[z+$]=w.x,O[z+1+$]=w.y),O[z++]=w.x,O[z++]=w.y}(c.normal||c.tangent||c.bitangent||D)&&(k=x.geodeticSurfaceNormal(o,k),D&&(j[e+X]=-k.x,j[i+X]=-k.y,j[n+X]=-k.z),(c.normal||c.tangent||c.bitangent)&&((c.tangent||c.bitangent)&&(B=r.Cartesian3.normalize(r.Cartesian3.cross(r.Cartesian3.UNIT_Z,k,B),B),A.Matrix3.multiplyByVector(q,B,B)),c.normal&&(S[e]=k.x,S[i]=k.y,S[n]=k.z,l&&(S[e+X]=-k.x,S[i+X]=-k.y,S[n+X]=-k.z)),c.tangent&&(L[e]=B.x,L[i]=B.y,L[n]=B.z,l&&(L[e+X]=-B.x,L[i+X]=-B.y,L[n+X]=-B.z)),c.bitangent&&(Y=r.Cartesian3.normalize(r.Cartesian3.cross(k,B,Y),Y),R[e]=Y.x,R[i]=Y.y,R[n]=Y.z,l&&(R[e+X]=Y.x,R[i+X]=Y.y,R[n+X]=Y.z))))}if(c.st){K=O.length;for(let t=0;t<K;t+=2)O[t]=(O[t]-J.x)/(Z.x-J.x),O[t+1]=(O[t+1]-J.y)/(Z.y-J.y)}const tt=new m.GeometryAttributes;if(c.position){const e=s.EllipseGeometryLibrary.raisePositionsToHeight(t,n,l);tt.position=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e})}if(c.st&&(tt.st=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:O})),c.normal&&(tt.normal=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:S})),c.tangent&&(tt.tangent=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),c.bitangent&&(tt.bitangent=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R})),D&&(tt.extrudeDirection=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:j})),l&&a.defined(n.offsetAttribute)){let t=new Uint8Array(C);if(n.offsetAttribute===p.GeometryOffsetAttribute.TOP)t=t.fill(1,0,C/2);else{const e=n.offsetAttribute===p.GeometryOffsetAttribute.NONE?0:1;t=t.fill(e)}tt.applyOffset=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:t})}return tt}function O(t){const e=new Array(t*(t+1)*12-6);let i,r,n,o,a,s=0;for(i=0,n=1,o=0;o<3;o++)e[s++]=n++,e[s++]=i,e[s++]=n;for(o=2;o<t+1;++o){for(n=o*(o+1)-1,i=(o-1)*o-1,e[s++]=n++,e[s++]=i,e[s++]=n,r=2*o,a=0;a<r-1;++a)e[s++]=n,e[s++]=i++,e[s++]=i,e[s++]=n++,e[s++]=i,e[s++]=n;e[s++]=n++,e[s++]=i,e[s++]=n}for(r=2*t,++n,++i,o=0;o<r-1;++o)e[s++]=n,e[s++]=i++,e[s++]=i,e[s++]=n++,e[s++]=i,e[s++]=n;for(e[s++]=n,e[s++]=i++,e[s++]=i,e[s++]=n++,e[s++]=i++,e[s++]=i,++i,o=t-1;o>1;--o){for(e[s++]=i++,e[s++]=i,e[s++]=n,r=2*o,a=0;a<r-1;++a)e[s++]=n,e[s++]=i++,e[s++]=i,e[s++]=n++,e[s++]=i,e[s++]=n;e[s++]=i++,e[s++]=i++,e[s++]=n++}for(o=0;o<3;o++)e[s++]=i++,e[s++]=i,e[s++]=n;return e}let S=new r.Cartesian3;const L=new e.BoundingSphere,R=new e.BoundingSphere;function j(t){const n=t.center,l=t.ellipsoid,f=t.semiMajorAxis;let x=r.Cartesian3.multiplyByScalar(l.geodeticSurfaceNormal(n,g),t.height,g);L.center=r.Cartesian3.add(n,x,L.center),L.radius=f,x=r.Cartesian3.multiplyByScalar(l.geodeticSurfaceNormal(n,x),t.extrudedHeight,x),R.center=r.Cartesian3.add(n,x,R.center),R.radius=f;const h=s.EllipseGeometryLibrary.computeEllipsePositions(t,!0,!0),E=h.positions,S=h.numPts,j=h.outerPositions,z=e.BoundingSphere.union(L,R),k=D(E,t,!0);let B=O(S);const Y=B.length;B.length=2*Y;const H=E.length/3;for(let t=0;t<Y;t+=3)B[t+Y]=B[t+2]+H,B[t+1+Y]=B[t+1]+H,B[t+2+Y]=B[t]+H;const U=d.IndexDatatype.createTypedArray(2*H/3,B),Q=new u.Geometry({attributes:k,indices:U,primitiveType:u.PrimitiveType.TRIANGLES}),W=function(t,n){const s=n.vertexFormat,l=n.center,c=n.semiMajorAxis,y=n.semiMinorAxis,d=n.ellipsoid,f=n.height,x=n.extrudedHeight,h=n.stRotation,E=t.length/3*2,D=new Float64Array(3*E),O=s.st?new Float32Array(2*E):void 0,S=s.normal?new Float32Array(3*E):void 0,L=s.tangent?new Float32Array(3*E):void 0,R=s.bitangent?new Float32Array(3*E):void 0,j=n.shadowVolume,z=j?new Float32Array(3*E):void 0;let k=0,B=T,Y=G,H=N;const U=new e.GeographicProjection(d),Q=U.project(d.cartesianToCartographic(l,P),v),W=d.scaleToGeodeticSurface(l,g);d.geodeticSurfaceNormal(W,W);const q=e.Quaternion.fromAxisAngle(W,h,I),J=A.Matrix3.fromQuaternion(q,M),Z=i.Cartesian2.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,V),K=i.Cartesian2.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,F);let X=t.length;const $=X/3*2;for(let e=0;e<X;e+=3){const i=e+1,n=e+2;let o,a=r.Cartesian3.fromArray(t,e,g);if(s.st){const t=A.Matrix3.multiplyByVector(J,a,_),e=U.project(d.cartesianToCartographic(t,P),b);r.Cartesian3.subtract(e,Q,e),w.x=(e.x+c)/(2*c),w.y=(e.y+y)/(2*y),Z.x=Math.min(w.x,Z.x),Z.y=Math.min(w.y,Z.y),K.x=Math.max(w.x,K.x),K.y=Math.max(w.y,K.y),O[k+$]=w.x,O[k+1+$]=w.y,O[k++]=w.x,O[k++]=w.y}a=d.scaleToGeodeticSurface(a,a),o=r.Cartesian3.clone(a,_),B=d.geodeticSurfaceNormal(a,B),j&&(z[e+X]=-B.x,z[i+X]=-B.y,z[n+X]=-B.z);let l=r.Cartesian3.multiplyByScalar(B,f,C);if(a=r.Cartesian3.add(a,l,a),l=r.Cartesian3.multiplyByScalar(B,x,l),o=r.Cartesian3.add(o,l,o),s.position&&(D[e+X]=o.x,D[i+X]=o.y,D[n+X]=o.z,D[e]=a.x,D[i]=a.y,D[n]=a.z),s.normal||s.tangent||s.bitangent){H=r.Cartesian3.clone(B,H);const l=r.Cartesian3.fromArray(t,(e+3)%X,C);r.Cartesian3.subtract(l,a,l);const u=r.Cartesian3.subtract(o,a,b);B=r.Cartesian3.normalize(r.Cartesian3.cross(u,l,B),B),s.normal&&(S[e]=B.x,S[i]=B.y,S[n]=B.z,S[e+X]=B.x,S[i+X]=B.y,S[n+X]=B.z),s.tangent&&(Y=r.Cartesian3.normalize(r.Cartesian3.cross(H,B,Y),Y),L[e]=Y.x,L[i]=Y.y,L[n]=Y.z,L[e+X]=Y.x,L[e+1+X]=Y.y,L[e+2+X]=Y.z),s.bitangent&&(R[e]=H.x,R[i]=H.y,R[n]=H.z,R[e+X]=H.x,R[i+X]=H.y,R[n+X]=H.z)}}if(s.st){X=O.length;for(let t=0;t<X;t+=2)O[t]=(O[t]-Z.x)/(K.x-Z.x),O[t+1]=(O[t+1]-Z.y)/(K.y-Z.y)}const tt=new m.GeometryAttributes;if(s.position&&(tt.position=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:D})),s.st&&(tt.st=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:O})),s.normal&&(tt.normal=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:S})),s.tangent&&(tt.tangent=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),s.bitangent&&(tt.bitangent=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R})),j&&(tt.extrudeDirection=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:z})),a.defined(n.offsetAttribute)){let t=new Uint8Array(E);if(n.offsetAttribute===p.GeometryOffsetAttribute.TOP)t=t.fill(1,0,E/2);else{const e=n.offsetAttribute===p.GeometryOffsetAttribute.NONE?0:1;t=t.fill(e)}tt.applyOffset=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:t})}return tt}(j,t);B=function(t){const e=t.length/3,i=d.IndexDatatype.createTypedArray(e,6*e);let r=0;for(let t=0;t<e;t++){const n=t,o=t+e,a=(n+1)%e,s=a+e;i[r++]=n,i[r++]=o,i[r++]=a,i[r++]=a,i[r++]=o,i[r++]=s}return i}(j);const q=d.IndexDatatype.createTypedArray(2*j.length/3,B),J=new u.Geometry({attributes:W,indices:q,primitiveType:u.PrimitiveType.TRIANGLES}),Z=y.GeometryPipeline.combineInstances([new c.GeometryInstance({geometry:Q}),new c.GeometryInstance({geometry:J})]);return{boundingSphere:z,attributes:Z[0].attributes,indices:Z[0].indices}}function z(t,e,i,n,o,a,l){const u=s.EllipseGeometryLibrary.computeEllipsePositions({center:t,semiMajorAxis:e,semiMinorAxis:i,rotation:n,granularity:o},!1,!0).outerPositions,m=u.length/3,c=new Array(m);for(let t=0;t<m;++t)c[t]=r.Cartesian3.fromArray(u,3*t);const p=x.Rectangle.fromCartesianArray(c,a,l);return p.width>f.CesiumMath.PI&&(p.north=p.north>0?f.CesiumMath.PI_OVER_TWO-f.CesiumMath.EPSILON7:p.north,p.south=p.south<0?f.CesiumMath.EPSILON7-f.CesiumMath.PI_OVER_TWO:p.south,p.east=f.CesiumMath.PI,p.west=-f.CesiumMath.PI),p}function k(t){const e=(t=a.defaultValue(t,a.defaultValue.EMPTY_OBJECT)).center,i=a.defaultValue(t.ellipsoid,l.Ellipsoid.WGS84),n=t.semiMajorAxis,o=t.semiMinorAxis,s=a.defaultValue(t.granularity,f.CesiumMath.RADIANS_PER_DEGREE),u=a.defaultValue(t.vertexFormat,h.VertexFormat.DEFAULT),m=a.defaultValue(t.height,0),c=a.defaultValue(t.extrudedHeight,m);this._center=r.Cartesian3.clone(e),this._semiMajorAxis=n,this._semiMinorAxis=o,this._ellipsoid=l.Ellipsoid.clone(i),this._rotation=a.defaultValue(t.rotation,0),this._stRotation=a.defaultValue(t.stRotation,0),this._height=Math.max(c,m),this._granularity=s,this._vertexFormat=h.VertexFormat.clone(u),this._extrudedHeight=Math.min(c,m),this._shadowVolume=a.defaultValue(t.shadowVolume,!1),this._workerName="createEllipseGeometry",this._offsetAttribute=t.offsetAttribute,this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0}k.packedLength=r.Cartesian3.packedLength+l.Ellipsoid.packedLength+h.VertexFormat.packedLength+9,k.pack=function(t,e,i){return i=a.defaultValue(i,0),r.Cartesian3.pack(t._center,e,i),i+=r.Cartesian3.packedLength,l.Ellipsoid.pack(t._ellipsoid,e,i),i+=l.Ellipsoid.packedLength,h.VertexFormat.pack(t._vertexFormat,e,i),i+=h.VertexFormat.packedLength,e[i++]=t._semiMajorAxis,e[i++]=t._semiMinorAxis,e[i++]=t._rotation,e[i++]=t._stRotation,e[i++]=t._height,e[i++]=t._granularity,e[i++]=t._extrudedHeight,e[i++]=t._shadowVolume?1:0,e[i]=a.defaultValue(t._offsetAttribute,-1),e};const B=new r.Cartesian3,Y=new l.Ellipsoid,H=new h.VertexFormat,U={center:B,ellipsoid:Y,vertexFormat:H,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,stRotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,shadowVolume:void 0,offsetAttribute:void 0};k.unpack=function(t,e,i){e=a.defaultValue(e,0);const n=r.Cartesian3.unpack(t,e,B);e+=r.Cartesian3.packedLength;const o=l.Ellipsoid.unpack(t,e,Y);e+=l.Ellipsoid.packedLength;const s=h.VertexFormat.unpack(t,e,H);e+=h.VertexFormat.packedLength;const u=t[e++],m=t[e++],c=t[e++],p=t[e++],y=t[e++],d=t[e++],f=t[e++],A=1===t[e++],x=t[e];return a.defined(i)?(i._center=r.Cartesian3.clone(n,i._center),i._ellipsoid=l.Ellipsoid.clone(o,i._ellipsoid),i._vertexFormat=h.VertexFormat.clone(s,i._vertexFormat),i._semiMajorAxis=u,i._semiMinorAxis=m,i._rotation=c,i._stRotation=p,i._height=y,i._granularity=d,i._extrudedHeight=f,i._shadowVolume=A,i._offsetAttribute=-1===x?void 0:x,i):(U.height=y,U.extrudedHeight=f,U.granularity=d,U.stRotation=p,U.rotation=c,U.semiMajorAxis=u,U.semiMinorAxis=m,U.shadowVolume=A,U.offsetAttribute=-1===x?void 0:x,new k(U))},k.computeRectangle=function(t,e){const i=(t=a.defaultValue(t,a.defaultValue.EMPTY_OBJECT)).center,r=a.defaultValue(t.ellipsoid,l.Ellipsoid.WGS84),n=t.semiMajorAxis,o=t.semiMinorAxis,s=a.defaultValue(t.granularity,f.CesiumMath.RADIANS_PER_DEGREE);return z(i,n,o,a.defaultValue(t.rotation,0),s,r,e)},k.createGeometry=function(t){if(t._semiMajorAxis<=0||t._semiMinorAxis<=0)return;const i=t._height,n=t._extrudedHeight,l=!f.CesiumMath.equalsEpsilon(i,n,0,f.CesiumMath.EPSILON2);t._center=t._ellipsoid.scaleToGeodeticSurface(t._center,t._center);const m={center:t._center,semiMajorAxis:t._semiMajorAxis,semiMinorAxis:t._semiMinorAxis,ellipsoid:t._ellipsoid,rotation:t._rotation,height:i,granularity:t._granularity,vertexFormat:t._vertexFormat,stRotation:t._stRotation};let c;if(l)m.extrudedHeight=n,m.shadowVolume=t._shadowVolume,m.offsetAttribute=t._offsetAttribute,c=j(m);else if(c=function(t){const i=t.center;S=r.Cartesian3.multiplyByScalar(t.ellipsoid.geodeticSurfaceNormal(i,S),t.height,S),S=r.Cartesian3.add(i,S,S);const n=new e.BoundingSphere(S,t.semiMajorAxis),o=s.EllipseGeometryLibrary.computeEllipsePositions(t,!0,!1),a=o.positions,l=o.numPts,u=D(a,t,!1);let m=O(l);return m=d.IndexDatatype.createTypedArray(a.length/3,m),{boundingSphere:n,attributes:u,indices:m}}(m),a.defined(t._offsetAttribute)){const e=c.attributes.position.values.length,i=t._offsetAttribute===p.GeometryOffsetAttribute.NONE?0:1,r=new Uint8Array(e/3).fill(i);c.attributes.applyOffset=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:r})}return new u.Geometry({attributes:c.attributes,indices:c.indices,primitiveType:u.PrimitiveType.TRIANGLES,boundingSphere:c.boundingSphere,offsetAttribute:t._offsetAttribute})},k.createShadowVolume=function(t,e,i){const r=t._granularity,n=t._ellipsoid,o=e(r,n),a=i(r,n);return new k({center:t._center,semiMajorAxis:t._semiMajorAxis,semiMinorAxis:t._semiMinorAxis,ellipsoid:n,rotation:t._rotation,stRotation:t._stRotation,granularity:r,extrudedHeight:o,height:a,vertexFormat:h.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(k.prototype,{rectangle:{get:function(){return a.defined(this._rectangle)||(this._rectangle=z(this._center,this._semiMajorAxis,this._semiMinorAxis,this._rotation,this._granularity,this._ellipsoid)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return a.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(t){const e=-t._stRotation;if(0===e)return[0,0,0,1,1,0];const i=s.EllipseGeometryLibrary.computeEllipsePositions({center:t._center,semiMajorAxis:t._semiMajorAxis,semiMinorAxis:t._semiMinorAxis,rotation:t._rotation,granularity:t._granularity},!1,!0).outerPositions,n=i.length/3,o=new Array(n);for(let t=0;t<n;++t)o[t]=r.Cartesian3.fromArray(i,3*t);const a=t._ellipsoid,l=t.rectangle;return u.Geometry._textureCoordinateRotationPoints(o,e,a,l)}(this)),this._textureCoordinateRotationPoints}}}),t.EllipseGeometry=k}));
