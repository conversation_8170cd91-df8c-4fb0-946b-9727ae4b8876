define(["exports","./defaultValue-f6d5e6da","./RuntimeError-9b4ce3fb"],(function(t,e,i){"use strict";function s(t,i){const s={msgType:-1,sID:0,planeType:0,datTime:0,satTime:0,msgLen:0},a=e.defaultValue(i,0);return s.msgType=t.getUint16(a,!0),s.sID=t.getUint16(a+2,!0),s.planeType=t.getUint32(a+4,!0),s.datTime=n(t,a+8),s.satTime=n(t,a+16),s.msgLen=t.getUint16(a+24,!0),s}function n(t,e){const i=t.getUint32(e,!0),s=t.getUint32(e+4,!0),n=i+Math.pow(2,32)*s;return Number.isSafeInteger(n)||console.warn(n,"exceeds MAX_SAFE_INTEGER. Precision may be lost"),n}var a=Object.freeze({NET_MSG_MIN:-1,NET_MSG_FIGHTER:0,NET_MSG_COMMAND:1,NET_MSG_TSPI:2,NET_MSG_RDR_STATE:3,NET_MSG_EWS_STA:4,NET_MSG_TARGET:6,NET_MSG_IRST:9,NET_MSG_IRMSL_STATE:12,NET_MSG_GTSPI:20,NET_MSG_RAD_STATE:21,NET_MSG_STATICTARGET:22,NET_MSG_COMBATPOWER:23,NET_MSG_RTSN:30,NET_MSG_RTKN:31,NET_MSG_WPN_TRAJ:32,NET_MSG_LARGE_PLANE:35,NET_MSG_SAFETYALARM:40});function h(t,i,s){return i=e.defaultValue(i,0),s=e.defaultValue(s,t.byteLength-i),t=t.subarray(i,i+s),h.decode(t)}function l(t,e,i){return e<=t&&t<=i}h.decodeWithTextDecoder=function(t){return new TextDecoder("utf-8").decode(t)},h.decodeWithFromCharCode=function(t){let e="";const s=function(t){let e=0,s=0,n=0,a=128,h=191;const o=[],r=t.length;for(let g=0;g<r;++g){const r=t[g];if(0===n){if(l(r,0,127)){o.push(r);continue}if(l(r,194,223)){n=1,e=31&r;continue}if(l(r,224,239)){224===r&&(a=160),237===r&&(h=159),n=2,e=15&r;continue}if(l(r,240,244)){240===r&&(a=144),244===r&&(h=143),n=3,e=7&r;continue}throw new i.RuntimeError("String decoding failed.")}l(r,a,h)?(a=128,h=191,e=e<<6|63&r,++s,s===n&&(o.push(e),e=n=s=0)):(e=n=s=0,a=128,h=191,--g)}return o}(t),n=s.length;for(let t=0;t<n;++t){let i=s[t];i<=65535?e+=String.fromCharCode(i):(i-=65536,e+=String.fromCharCode(55296+(i>>10),56320+(1023&i)))}return e},"undefined"!=typeof TextDecoder?h.decode=h.decodeWithTextDecoder:h.decode=h.decodeWithFromCharCode,t.EV_MsgType=a,t.EV_ParseCombatPower=function(t,i){this.SIZE=39,this.header=s(t,i);let n=26+e.defaultValue(i,0),a=[];for(let e=0;e<7;e++)a.push(t.getInt8(n,!0)),n+=1;let l=String.fromCharCode(a[0],a[1]);const o=new Int8Array(a.splice(2,5));l=`${l}${h.decodeWithTextDecoder(o)}`,l=l.replace(/\u0000/g,""),this.chFlyUser=l,a="";for(let e=0;e<10;e++)a+=String.fromCharCode(t.getInt8(n,!0)),n+=1;this.chFighterName=a,this.nRedOrBlue=t.getUint8(n,!0),n+=1,this.remainMslNum=t.getUint8(n,!0),n+=1,this.remainChannelNum=t.getUint8(n,!0),n+=1,this.selfReliveFlag=t.getUint8(n,!0),n+=1,this.resetMslCountFlag=t.getUint8(n,!0),n+=1,this.trainMode=t.getUint8(n,!0)},t.EV_ParseEWS=function(t,i){this.SIZE=27,this.header=s(t,i);const n=26+e.defaultValue(i,0);this.ewsDir=t.getUint8(n,!0)},t.EV_ParseFlight=function(t,i){this.SIZE=57,this.header=s(t,i);let n=26+e.defaultValue(i,0);this.true_AOA=t.getFloat32(n,!0),n+=4,this.mach=t.getFloat32(n,!0),n+=4,this.normalAccel=t.getFloat32(n,!0),n+=4,this.trainMode=t.getUint8(n,!0),n+=1,this.cal_air_speed=t.getFloat32(n,!0),n+=4,this.baro_ref_alt=t.getFloat32(n,!0),n+=4,this.wireless_alt=t.getFloat32(n,!0),n+=4,this.remain_oil_amount=t.getFloat32(n,!0),n+=4,this.s2=t.getUint8(n,!0),n+=1,this.s3=t.getUint8(n,!0)},t.EV_ParseGTspi=function(t,i){this.SIZE=58,this.header=s(t,i);let n=26+e.defaultValue(i,0);this.fakeCampID=t.getUint8(n,!0),n+=1,this.campType=t.getInt32(n,!0),n+=4,this.equipmentType=t.getInt8(n,!0),n+=1,this.equipmentID=t.getInt8(n,!0),n+=1,this.gTspiState=t.getInt8(n,!0),n+=1,this.northAngle=t.getFloat32(n,!0),n+=4,this.gTspiAlt=t.getFloat32(n,!0),n+=4,this.gTspiLon=t.getFloat64(n,!0),n+=8,this.gTspiLat=t.getFloat64(n,!0)},t.EV_ParseHeader=s,t.EV_ParseI3NS=function(t,i){this.SIZE=96,this.header=s(t,i);let n=26+e.defaultValue(i,0);this.delayFlag=t.getUint8(n,!0),n+=1;const a=[];for(let e=0;e<7;e++)a.push(t.getInt8(n,!0)),n+=1;let l=String.fromCharCode(a[0],a[1]);const o=new Int8Array(a.splice(2,5));l=`${l}${h.decodeWithTextDecoder(o)}`,l=l.replace(/\u0000/g,""),this.chFlyUser=l;let r="";for(let e=0;e<10;e++)r+=String.fromCharCode(t.getInt8(n,!0)),n+=1;r=r.replace(/\u0000/g,""),this.chFighterName=r,this.nRedOrBlue=t.getUint8(n,!0),n+=1,this.fLon=t.getFloat64(n,!0),n+=8,this.fLat=t.getFloat64(n,!0),n+=8,this.fAlt=t.getFloat32(n,!0),n+=4,this.fRoll=t.getFloat32(n,!0),n+=4,this.fPitch=t.getFloat32(n,!0),n+=4,this.fHeading=t.getFloat32(n,!0),n+=4,this.decoyDrop=t.getUint8(n,!0),n+=1,this.enginePower=t.getUint8(n,!0),n+=1,this.trainPro=t.getUint8(n,!0),n+=1,this.height=t.getFloat32(n,!0),n+=4,this.vel_east=t.getFloat32(n,!0),n+=4,this.vel_north=t.getFloat32(n,!0),n+=4,this.vel_up=t.getFloat32(n,!0)},t.EV_ParseI3NS_Deprecated=function(t,i){this.SIZE=95,this.header=s(t,i);let n=26+e.defaultValue(i,0);const a=[];for(let e=0;e<7;e++)a.push(t.getInt8(n,!0)),n+=1;let l=String.fromCharCode(a[0],a[1]);const o=new Int8Array(a.splice(2,5));l=`${l}${h.decodeWithTextDecoder(o)}`,l=l.replace(/\u0000/g,""),this.chFlyUser=l;let r="";for(let e=0;e<10;e++)r+=String.fromCharCode(t.getInt8(n,!0)),n+=1;this.chFighterName=r,this.nRedOrBlue=t.getUint8(n,!0),n+=1,this.fLon=t.getFloat64(n,!0),n+=8,this.fLat=t.getFloat64(n,!0),n+=8,this.fAlt=t.getFloat32(n,!0),n+=4,this.fRoll=t.getFloat32(n,!0),n+=4,this.fPitch=t.getFloat32(n,!0),n+=4,this.fHeading=t.getFloat32(n,!0),n+=4,this.decoyDrop=t.getUint8(n,!0),n+=1,this.enginePower=t.getUint8(n,!0),n+=1,this.trainPro=t.getUint8(n,!0),n+=1,this.height=t.getFloat32(n,!0),n+=4,this.vel_east=t.getFloat32(n,!0),n+=4,this.vel_north=t.getFloat32(n,!0),n+=4,this.vel_up=t.getFloat32(n,!0)},t.EV_ParseIRSTworkMode=function(t,i){this.SIZE=29,this.header=s(t,i);let n=26+e.defaultValue(i,0);this.workMode=t.getInt8(n,!0),n+=1,this.openState=t.getInt8(n,!0),n+=1,this.interceptState=t.getInt8(n,!0)},t.EV_ParseIrsmState=function(t,i){this.SIZE=38,this.header=s(t,i);let n=26+e.defaultValue(i,0);this.irmslAzimuth=t.getFloat32(n,!0),n+=4,this.irmslPitch=t.getFloat32(n,!0),n+=4,this.fDistance=t.getFloat32(n,!0)},t.EV_ParseLargePlane=function(t,i){this.SIZE=72,this.header=s(t,i);let n=26+e.defaultValue(i,0),a=[];for(let e=0;e<7;e++)a.push(t.getInt8(n,!0)),n+=1;this.chFlyUser=a,a=[];for(let e=0;e<10;e++)a.push(t.getInt8(n,!0)),n+=1;this.chFighterName=a,this.nRedOrBlue=t.getUint8(n,!0),n+=1,this.fLon=t.getFloat64(n,!0),n+=8,this.fLat=t.getFloat64(n,!0),n+=8,this.fAlt=t.getFloat32(n,!0),n+=4,this.fHeading=t.getFloat32(n,!0),n+=4,this.fVel=t.getFloat32(n,!0)},t.EV_ParseParam=function(t,i){this.SIZE=38,this.header=s(t,i);let n=26+e.defaultValue(i,0);this.mslType=t.getInt32(n,!0),n+=4,this.fHalfAngle=t.getFloat32(n,!0),n+=4,this.fMaxDistance=t.getFloat32(n,!0)},t.EV_ParseRTKN=function(t,i){this.SIZE=50,this.header=s(t,i);let n=26+e.defaultValue(i,0);this.nMissileID=t.getUint32(n,!0),n+=4,this.chWeaponType=t.getInt32(n,!0),n+=4,this.fDistance=t.getFloat32(n,!0),n+=4,this.nTgtID=t.getUint16(n,!0),n+=2,this.nResult=t.getUint8(n,!0),n+=1,this.stopReason=t.getUint8(n,!0);let a=[];for(let e=0;e<4;e++)a.push(t.getInt8(n,!0)),n+=1;this.gTgtType=a,a=[];for(let e=0;e<4;e++)a.push(t.getInt8(n,!0)),n+=1;this.gTgtID=a},t.EV_ParseRTSN=function(t,i){this.SIZE=35,this.header=s(t,i);let n=26+e.defaultValue(i,0);this.nMissileID=t.getUint32(n,!0),n+=4,this.chWeaponType=t.getInt32(n,!0),n+=4,this.dropMslNum=t.getUint8(n,!0)},t.EV_ParseRadarTarget=function(t,i){this.SIZE=139,this.header=new s(t,i);let n=26+e.defaultValue(i,0);this.tgtNum=t.getUint8(n,!0),n+=1,this.tgtInfo=[];for(let t=0;t<8;t++)this.tgtInfo.push({tgtNTS:0,tgtSensor:0,tgtAzimuth:0,tgtPitch:0,tgtDis:0});for(let e=0;e<8;e++)this.tgtInfo[e].tgtNTS=t.getUint8(n,!0),n+=1,this.tgtInfo[e].tgtSensor=t.getUint8(n,!0),n+=1,this.tgtInfo[e].tgtAzimuth=t.getFloat32(n,!0),n+=4,this.tgtInfo[e].tgtPitch=t.getFloat32(n,!0),n+=4,this.tgtInfo[e].tgtDis=t.getFloat32(n,!0),e<7&&(n+=4)},t.EV_ParseRadarWM=function(t,i){this.SIZE=29,this.header=new s(t,i);let n=26+e.defaultValue(i,0);this.workMode=t.getInt8(n,!0),n+=1,this.openState=t.getInt8(n,!0),n+=1,this.radiaState=t.getInt8(n,!0)},t.EV_ParseSDRState=function(t,i){this.SIZE=35,this.header=s(t,i);let n=26+e.defaultValue(i,0);this.campType=t.getInt32(n,!0),n+=4,this.equipmentType=t.getUint8(n,!0),n+=1,this.equipmentID=t.getUint8(n,!0),n+=1,this.openState=t.getUint8(n,!0),n+=1,this.antennaDirection=t.getUint16(n,!0)},t.EV_ParseSafetyAlarm=function(t,i){this.SIZE=10,this.header=s(t,i);let n=26+e.defaultValue(i,0);this.uiAlarmType=t.getUint16(n,!0),this.uiPlaneID=new Array(3),this.ucMeetType=new Array(3),this.uiMeetTimeLinear=new Array(3),this.uiMeetTimeSprial=new Array(3);for(let e=0;e<3;e++)this.uiPlaneID[e]=t.getUint16(n,!0),n+=2,this.ucMeetType[e]=t.getUint8(n,!0),n+=1,this.uiMeetTimeLinear[e]=t.getUint32(n,!0),n+=4,this.uiMeetTimeSprial[e]=t.getUint32(n,!0)},t.EV_ParseStartStop=function(t,i){this.SIZE=27,I3_NET_COMMAND.header=s(t,i);const n=26+e.defaultValue(i,0);I3_NET_COMMAND.state=t.getUint8(n,!0)},t.EV_ParseTRAJ=function(t,i){this.SIZE=77,this.header=s(t,i);let n=26+e.defaultValue(i,0);this.targetID=t.getUint16(n,!0),n+=2,this.weaponID=t.getUint32(n,!0),n+=4,this.weaponType=t.getInt32(n,!0),n+=4,this.engineState=t.getUint8(n,!0),n+=1,this.captureState=t.getUint8(n,!0),n+=1,this.mslLongitude=t.getFloat64(n,!0),n+=8,this.mslLatitude=t.getFloat64(n,!0),n+=8,this.mslAltitude=t.getFloat32(n,!0),n+=4,this.mslCourse=t.getFloat32(n,!0),n+=4,this.mslPitch=t.getFloat32(n,!0),n+=4,this.vel=t.getFloat32(n,!0),n+=4,this.mslDistance=t.getFloat32(n,!0),n+=4,this.targetTspiValid=t.getUint8(n,!0),n+=1,this.ipk=t.getUint8(n,!0),n+=1,this.noKkkReason=t.getUint8(n,!0)}}));
