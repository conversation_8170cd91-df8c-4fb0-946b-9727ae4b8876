define(["./Ellipsoid-8e26549b","./AxisAlignedBoundingBox-d98e5354","./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./defaultValue-f6d5e6da","./TerrainEncoding-443b37a0","./Math-355606c6","./OrientedBoundingBox-5f5e4f4c","./Rectangle-98b0bef0","./WebMercatorProjection-03b5db31","./RuntimeError-9b4ce3fb","./createTaskProcessorWorker","./Cartographic-dbefb6fa","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./combine-0c102d93","./RequestType-735c98f2","./AttributeCompression-d2ca507e","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./Matrix2-e4a4609a","./TerrainQuantization-c16f42ed","./EllipsoidTangentPlane-a6ea67fb","./IntersectionTests-01432fe7","./Plane-06f34fae"],(function(e,t,i,a,n,r,s,l,o,f,u,c,d,h,m,g,p,x,w,y,k,I,U,b,M,T,v){"use strict";var V=Object.freeze({NONE:0,LERC:1});const A={};A.DEFAULT_STRUCTURE=Object.freeze({heightScale:1,heightOffset:0,elementsPerHeight:1,stride:1,elementMultiplier:256,isBigEndian:!1});const B=new n.Cartesian3,D=new a.Matrix4,S=new n.Cartesian3,P=new n.Cartesian3;A.computeVertices=function(c){const d=Math.cos,h=Math.sin,m=Math.sqrt,g=Math.atan,p=Math.exp,x=l.CesiumMath.PI_OVER_TWO,w=l.CesiumMath.toRadians,y=c.heightmap,k=c.width,I=c.height,U=c.skirtHeight,b=U>0,M=r.defaultValue(c.isGeographic,!0),T=r.defaultValue(c.ellipsoid,e.Ellipsoid.WGS84),v=1/T.maximumRadius,V=f.Rectangle.clone(c.nativeRectangle),E=f.Rectangle.clone(c.rectangle);let C,F,R,N;r.defined(E)?(C=E.west,F=E.south,R=E.east,N=E.north):M?(C=w(V.west),F=w(V.south),R=w(V.east),N=w(V.north)):(C=V.west*v,F=x-2*g(p(-V.south*v)),R=V.east*v,N=x-2*g(p(-V.north*v)));let O=c.relativeToCenter;const L=r.defined(O);O=L?O:n.Cartesian3.ZERO;const z=r.defaultValue(c.includeWebMercatorT,!1),_=r.defaultValue(c.exaggeration,1),H=r.defaultValue(c.exaggerationRelativeHeight,0),Y=1!==_,W=r.defaultValue(c.structure,A.DEFAULT_STRUCTURE),X=r.defaultValue(W.heightScale,A.DEFAULT_STRUCTURE.heightScale),Z=r.defaultValue(W.heightOffset,A.DEFAULT_STRUCTURE.heightOffset),j=r.defaultValue(W.elementsPerHeight,A.DEFAULT_STRUCTURE.elementsPerHeight),G=r.defaultValue(W.stride,A.DEFAULT_STRUCTURE.stride),q=r.defaultValue(W.elementMultiplier,A.DEFAULT_STRUCTURE.elementMultiplier),Q=r.defaultValue(W.isBigEndian,A.DEFAULT_STRUCTURE.isBigEndian);let J=f.Rectangle.computeWidth(V),K=f.Rectangle.computeHeight(V);const $=J/(k-1),ee=K/(I-1);M||(J*=v,K*=v);const te=T.radiiSquared,ie=te.x,ae=te.y,ne=te.z;let re=65536,se=-65536;const le=i.Transforms.eastNorthUpToFixedFrame(O,T),oe=a.Matrix4.inverseTransformation(le,D);let fe,ue;z&&(fe=u.WebMercatorProjection.geodeticLatitudeToMercatorAngle(F),ue=1/(u.WebMercatorProjection.geodeticLatitudeToMercatorAngle(N)-fe));const ce=S;ce.x=Number.POSITIVE_INFINITY,ce.y=Number.POSITIVE_INFINITY,ce.z=Number.POSITIVE_INFINITY;const de=P;de.x=Number.NEGATIVE_INFINITY,de.y=Number.NEGATIVE_INFINITY,de.z=Number.NEGATIVE_INFINITY;let he=Number.POSITIVE_INFINITY;const me=k*I,ge=me+(U>0?2*k+2*I:0),pe=new Array(ge),xe=new Array(ge),we=new Array(ge),ye=z?new Array(ge):[],ke=Y?new Array(ge):[];let Ie=0,Ue=I,be=0,Me=k;b&&(--Ie,++Ue,--be,++Me);const Te=1e-5;M||(1.4844222297453322===E.north?E.north=x:-1.4844222297453324===E.south&&(E.south=-x),F=E.south,N=E.north);for(let e=Ie;e<Ue;++e){let t=e;t<0&&(t=0),t>=I&&(t=I-1);let i=V.north-ee*t;M?i=w(i):(i=x-2*g(p(-i*v)),1.4844222297453322===i?i=x:-1.4844222297453324===i&&(i=-x));let s=(i-F)/(N-F);s=l.CesiumMath.clamp(s,0,1);const o=e===Ie,f=e===Ue-1;U>0&&(o?i+=Te*K:f&&(i-=Te*K));const b=d(i),D=h(i),S=ne*D;let P;z&&(P=(u.WebMercatorProjection.geodeticLatitudeToMercatorAngle(i)-fe)*ue);for(let e=be;e<Me;++e){let u=e;u<0&&(u=0),u>=k&&(u=k-1);const g=t*(k*G)+u*G;let p;if(1===j)p=y[g];else{let e;if(p=0,Q)for(e=0;e<j;++e)p=p*q+y[g+e];else for(e=j-1;e>=0;--e)p=p*q+y[g+e]}p=p*X+Z,se=Math.max(se,p),re=Math.min(re,p);let x=V.west+$*u;M?x=w(x):x*=v;let E=(x-C)/(R-C);E=l.CesiumMath.clamp(E,0,1);let F=t*k+u;if(U>0){const i=e===be,a=e===Me-1,n=o||f||i||a;if((o||f)&&(i||a))continue;n&&(p-=U,i?(F=me+(I-t-1),x-=Te*J):f?F=me+I+(k-u-1):a?(F=me+I+k+t,x+=Te*J):o&&(F=me+I+k+I+u))}const N=b*d(x),O=b*h(x),L=ie*N,_=ae*O,H=1/m(L*N+_*O+S*D),W=L*H,K=_*H,ee=S*H,te=r.defaultValue(c.createdByUpsampling,!1),ne=c.structure.terrainModifyInfoCollection;if(r.defined(ne)){const e=ne._array.length;for(let t=0;t<e;t++){const e=ne._array[t],a=A.isPointInsidePolygon(e._points,i,x),n=e._terrainModifyType,r=e._value;a&&(0!==n||te?1!==n||te?2===n&&(p=r):p+=r:p*=r)}se=Math.max(se,p),re=Math.min(re,p)}const le=new n.Cartesian3;le.x=W+N*p,le.y=K+O*p,le.z=ee+D*p,a.Matrix4.multiplyByPoint(oe,le,B),n.Cartesian3.minimumByComponent(B,ce,ce),n.Cartesian3.maximumByComponent(B,de,de),he=Math.min(he,p),pe[F]=le,we[F]=new a.Cartesian2(E,s),xe[F]=p,z&&(ye[F]=P),Y&&(ke[F]=T.geodeticSurfaceNormal(le))}}const ve=i.BoundingSphere.fromPoints(pe);let Ve,Ae;if(r.defined(E)&&(Ve=o.OrientedBoundingBox.fromRectangle(E,re,se,T)),L){Ae=new s.EllipsoidalOccluder(T).computeHorizonCullingPointPossiblyUnderEllipsoid(O,pe,re)}const Be=new t.AxisAlignedBoundingBox(ce,de,O),De=new s.TerrainEncoding(O,Be,he,se,le,!1,z,Y,_,H),Se=new Float32Array(ge*De.stride);let Pe=0;for(let e=0;e<ge;++e)Pe=De.encode(Se,Pe,pe[e],we[e],xe[e],void 0,ye[e],ke[e]);return{vertices:Se,maximumHeight:se,minimumHeight:re,encoding:De,boundingSphere3D:ve,orientedBoundingBox:Ve,occludeePointInScaledSpace:Ae}},A.isPointInsidePolygon=function(e,t,i){const a=e.length;if(a<=2)return!1;let n=!1;for(let r=0,s=a-1;r<a;r++){const a=e[r],l=e[s];(a.latitude<t&&l.latitude>=t||l.latitude<t&&a.latitude>=t)&&a.longitude+(t-a.latitude)/(l.latitude-a.latitude)*(l.longitude-a.longitude)<i&&(n=!n),s=r}return n};var E,C=A,F={exports:{}};E=F,
/* Copyright 2015-2018 Esri. Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 @preserve */
function(){var e,t,i,a,n,r,s,l,o,f,u,c,d,h,m,g,p=(e={defaultNoDataValue:-34027999387901484e22,decode:function(r,s){var l=(s=s||{}).encodedMaskData||null===s.encodedMaskData,o=n(r,s.inputOffset||0,l),f=null!==s.noDataValue?s.noDataValue:e.defaultNoDataValue,u=t(o,s.pixelType||Float32Array,s.encodedMaskData,f,s.returnMask),c={width:o.width,height:o.height,pixelData:u.resultPixels,minValue:u.minValue,maxValue:o.pixels.maxValue,noDataValue:f};return u.resultMask&&(c.maskData=u.resultMask),s.returnEncodedMask&&o.mask&&(c.encodedMaskData=o.mask.bitset?o.mask.bitset:null),s.returnFileInfo&&(c.fileInfo=i(o),s.computeUsedBitDepths&&(c.fileInfo.bitDepths=a(o))),c}},t=function(e,t,i,a,n){var s,l,o,f=0,u=e.pixels.numBlocksX,c=e.pixels.numBlocksY,d=Math.floor(e.width/u),h=Math.floor(e.height/c),m=2*e.maxZError,g=Number.MAX_VALUE;i=i||(e.mask?e.mask.bitset:null),l=new t(e.width*e.height),n&&i&&(o=new Uint8Array(e.width*e.height));for(var p,x,w=new Float32Array(d*h),y=0;y<=c;y++){var k=y!==c?h:e.height%c;if(0!==k)for(var I=0;I<=u;I++){var U=I!==u?d:e.width%u;if(0!==U){var b,M,T,v,V=y*e.width*h+I*d,A=e.width-U,B=e.pixels.blocks[f];if(B.encoding<2?(0===B.encoding?b=B.rawData:(r(B.stuffedData,B.bitsPerPixel,B.numValidPixels,B.offset,m,w,e.pixels.maxValue),b=w),M=0):T=2===B.encoding?0:B.offset,i)for(x=0;x<k;x++){for(7&V&&(v=i[V>>3],v<<=7&V),p=0;p<U;p++)7&V||(v=i[V>>3]),128&v?(o&&(o[V]=1),g=g>(s=B.encoding<2?b[M++]:T)?s:g,l[V++]=s):(o&&(o[V]=0),l[V++]=a),v<<=1;V+=A}else if(B.encoding<2)for(x=0;x<k;x++){for(p=0;p<U;p++)g=g>(s=b[M++])?s:g,l[V++]=s;V+=A}else for(g=g>T?T:g,x=0;x<k;x++){for(p=0;p<U;p++)l[V++]=T;V+=A}if(1===B.encoding&&M!==B.numValidPixels)throw"Block and Mask do not match";f++}}}return{resultPixels:l,resultMask:o,minValue:g}},i=function(e){return{fileIdentifierString:e.fileIdentifierString,fileVersion:e.fileVersion,imageType:e.imageType,height:e.height,width:e.width,maxZError:e.maxZError,eofOffset:e.eofOffset,mask:e.mask?{numBlocksX:e.mask.numBlocksX,numBlocksY:e.mask.numBlocksY,numBytes:e.mask.numBytes,maxValue:e.mask.maxValue}:null,pixels:{numBlocksX:e.pixels.numBlocksX,numBlocksY:e.pixels.numBlocksY,numBytes:e.pixels.numBytes,maxValue:e.pixels.maxValue,noDataValue:e.noDataValue}}},a=function(e){for(var t=e.pixels.numBlocksX*e.pixels.numBlocksY,i={},a=0;a<t;a++){var n=e.pixels.blocks[a];0===n.encoding?i.float32=!0:1===n.encoding?i[n.bitsPerPixel]=!0:i[0]=!0}return Object.keys(i)},n=function(e,t,i){var a={},n=new Uint8Array(e,t,10);if(a.fileIdentifierString=String.fromCharCode.apply(null,n),"CntZImage"!==a.fileIdentifierString.trim())throw"Unexpected file identifier string: "+a.fileIdentifierString;t+=10;var r=new DataView(e,t,24);if(a.fileVersion=r.getInt32(0,!0),a.imageType=r.getInt32(4,!0),a.height=r.getUint32(8,!0),a.width=r.getUint32(12,!0),a.maxZError=r.getFloat64(16,!0),t+=24,!i)if(r=new DataView(e,t,16),a.mask={},a.mask.numBlocksY=r.getUint32(0,!0),a.mask.numBlocksX=r.getUint32(4,!0),a.mask.numBytes=r.getUint32(8,!0),a.mask.maxValue=r.getFloat32(12,!0),t+=16,a.mask.numBytes>0){var s=new Uint8Array(Math.ceil(a.width*a.height/8)),l=(r=new DataView(e,t,a.mask.numBytes)).getInt16(0,!0),o=2,f=0;do{if(l>0)for(;l--;)s[f++]=r.getUint8(o++);else{var u=r.getUint8(o++);for(l=-l;l--;)s[f++]=u}l=r.getInt16(o,!0),o+=2}while(o<a.mask.numBytes);if(-32768!==l||f<s.length)throw"Unexpected end of mask RLE encoding";a.mask.bitset=s,t+=a.mask.numBytes}else 0==(a.mask.numBytes|a.mask.numBlocksY|a.mask.maxValue)&&(a.mask.bitset=new Uint8Array(Math.ceil(a.width*a.height/8)));r=new DataView(e,t,16),a.pixels={},a.pixels.numBlocksY=r.getUint32(0,!0),a.pixels.numBlocksX=r.getUint32(4,!0),a.pixels.numBytes=r.getUint32(8,!0),a.pixels.maxValue=r.getFloat32(12,!0),t+=16;var c=a.pixels.numBlocksX,d=a.pixels.numBlocksY,h=c+(a.width%c>0?1:0),m=d+(a.height%d>0?1:0);a.pixels.blocks=new Array(h*m);for(var g=0,p=0;p<m;p++)for(var x=0;x<h;x++){var w=0,y=e.byteLength-t;r=new DataView(e,t,Math.min(10,y));var k={};a.pixels.blocks[g++]=k;var I=r.getUint8(0);if(w++,k.encoding=63&I,k.encoding>3)throw"Invalid block encoding ("+k.encoding+")";if(2!==k.encoding){if(0!==I&&2!==I){if(I>>=6,k.offsetType=I,2===I)k.offset=r.getInt8(1),w++;else if(1===I)k.offset=r.getInt16(1,!0),w+=2;else{if(0!==I)throw"Invalid block offset type";k.offset=r.getFloat32(1,!0),w+=4}if(1===k.encoding)if(I=r.getUint8(w),w++,k.bitsPerPixel=63&I,I>>=6,k.numValidPixelsType=I,2===I)k.numValidPixels=r.getUint8(w),w++;else if(1===I)k.numValidPixels=r.getUint16(w,!0),w+=2;else{if(0!==I)throw"Invalid valid pixel count type";k.numValidPixels=r.getUint32(w,!0),w+=4}}var U;if(t+=w,3!==k.encoding)if(0===k.encoding){var b=(a.pixels.numBytes-1)/4;if(b!==Math.floor(b))throw"uncompressed block has invalid length";U=new ArrayBuffer(4*b),new Uint8Array(U).set(new Uint8Array(e,t,4*b));var M=new Float32Array(U);k.rawData=M,t+=4*b}else if(1===k.encoding){var T=Math.ceil(k.numValidPixels*k.bitsPerPixel/8),v=Math.ceil(T/4);U=new ArrayBuffer(4*v),new Uint8Array(U).set(new Uint8Array(e,t,T)),k.stuffedData=new Uint32Array(U),t+=T}}else t++}return a.eofOffset=t,a},r=function(e,t,i,a,n,r,s){var l,o,f,u=(1<<t)-1,c=0,d=0,h=Math.ceil((s-a)/n),m=4*e.length-Math.ceil(t*i/8);for(e[e.length-1]<<=8*m,l=0;l<i;l++){if(0===d&&(f=e[c++],d=32),d>=t)o=f>>>d-t&u,d-=t;else{var g=t-d;o=(f&u)<<g&u,o+=(f=e[c++])>>>(d=32-g)}r[l]=o<h?a+o*n:s}return r},e),x=(s=function(e,t,i,a,n,r,s,l){var o,f,u,c,d,h=(1<<i)-1,m=0,g=0,p=4*e.length-Math.ceil(i*a/8);if(e[e.length-1]<<=8*p,n)for(o=0;o<a;o++)0===g&&(u=e[m++],g=32),g>=i?(f=u>>>g-i&h,g-=i):(f=(u&h)<<(c=i-g)&h,f+=(u=e[m++])>>>(g=32-c)),t[o]=n[f];else for(d=Math.ceil((l-r)/s),o=0;o<a;o++)0===g&&(u=e[m++],g=32),g>=i?(f=u>>>g-i&h,g-=i):(f=(u&h)<<(c=i-g)&h,f+=(u=e[m++])>>>(g=32-c)),t[o]=f<d?r+f*s:l},l=function(e,t,i,a,n,r){var s,l=(1<<t)-1,o=0,f=0,u=0,c=0,d=0,h=[],m=4*e.length-Math.ceil(t*i/8);e[e.length-1]<<=8*m;var g=Math.ceil((r-a)/n);for(f=0;f<i;f++)0===c&&(s=e[o++],c=32),c>=t?(d=s>>>c-t&l,c-=t):(d=(s&l)<<(u=t-c)&l,d+=(s=e[o++])>>>(c=32-u)),h[f]=d<g?a+d*n:r;return h.unshift(a),h},o=function(e,t,i,a,n,r,s,l){var o,f,u,c,d=(1<<i)-1,h=0,m=0,g=0;if(n)for(o=0;o<a;o++)0===m&&(u=e[h++],m=32,g=0),m>=i?(f=u>>>g&d,m-=i,g+=i):(f=u>>>g&d,m=32-(c=i-m),f|=((u=e[h++])&(1<<c)-1)<<i-c,g=c),t[o]=n[f];else{var p=Math.ceil((l-r)/s);for(o=0;o<a;o++)0===m&&(u=e[h++],m=32,g=0),m>=i?(f=u>>>g&d,m-=i,g+=i):(f=u>>>g&d,m=32-(c=i-m),f|=((u=e[h++])&(1<<c)-1)<<i-c,g=c),t[o]=f<p?r+f*s:l}return t},f=function(e,t,i,a,n,r){var s,l=(1<<t)-1,o=0,f=0,u=0,c=0,d=0,h=0,m=[],g=Math.ceil((r-a)/n);for(f=0;f<i;f++)0===c&&(s=e[o++],c=32,h=0),c>=t?(d=s>>>h&l,c-=t,h+=t):(d=s>>>h&l,c=32-(u=t-c),d|=((s=e[o++])&(1<<u)-1)<<t-u,h=u),m[f]=d<g?a+d*n:r;return m.unshift(a),m},u=function(e,t,i,a){var n,r,s,l,o=(1<<i)-1,f=0,u=0,c=4*e.length-Math.ceil(i*a/8);for(e[e.length-1]<<=8*c,n=0;n<a;n++)0===u&&(s=e[f++],u=32),u>=i?(r=s>>>u-i&o,u-=i):(r=(s&o)<<(l=i-u)&o,r+=(s=e[f++])>>>(u=32-l)),t[n]=r;return t},c=function(e,t,i,a){var n,r,s,l,o=(1<<i)-1,f=0,u=0,c=0;for(n=0;n<a;n++)0===u&&(s=e[f++],u=32,c=0),u>=i?(r=s>>>c&o,u-=i,c+=i):(r=s>>>c&o,u=32-(l=i-u),r|=((s=e[f++])&(1<<l)-1)<<i-l,c=l),t[n]=r;return t},d={HUFFMAN_LUT_BITS_MAX:12,computeChecksumFletcher32:function(e){for(var t=65535,i=65535,a=e.length,n=Math.floor(a/2),r=0;n;){var s=n>=359?359:n;n-=s;do{t+=e[r++]<<8,i+=t+=e[r++]}while(--s);t=(65535&t)+(t>>>16),i=(65535&i)+(i>>>16)}return 1&a&&(i+=t+=e[r]<<8),((i=(65535&i)+(i>>>16))<<16|(t=(65535&t)+(t>>>16)))>>>0},readHeaderInfo:function(e,t){var i=t.ptr,a=new Uint8Array(e,i,6),n={};if(n.fileIdentifierString=String.fromCharCode.apply(null,a),0!==n.fileIdentifierString.lastIndexOf("Lerc2",0))throw"Unexpected file identifier string (expect Lerc2 ): "+n.fileIdentifierString;i+=6;var r,s=new DataView(e,i,8),l=s.getInt32(0,!0);if(n.fileVersion=l,i+=4,l>=3&&(n.checksum=s.getUint32(4,!0),i+=4),s=new DataView(e,i,12),n.height=s.getUint32(0,!0),n.width=s.getUint32(4,!0),i+=8,l>=4?(n.numDims=s.getUint32(8,!0),i+=4):n.numDims=1,s=new DataView(e,i,40),n.numValidPixel=s.getUint32(0,!0),n.microBlockSize=s.getInt32(4,!0),n.blobSize=s.getInt32(8,!0),n.imageType=s.getInt32(12,!0),n.maxZError=s.getFloat64(16,!0),n.zMin=s.getFloat64(24,!0),n.zMax=s.getFloat64(32,!0),i+=40,t.headerInfo=n,t.ptr=i,l>=3&&(r=l>=4?52:48,this.computeChecksumFletcher32(new Uint8Array(e,i-r,n.blobSize-14))!==n.checksum))throw"Checksum failed.";return!0},checkMinMaxRanges:function(e,t){var i=t.headerInfo,a=this.getDataTypeArray(i.imageType),n=i.numDims*this.getDataTypeSize(i.imageType),r=this.readSubArray(e,t.ptr,a,n),s=this.readSubArray(e,t.ptr+n,a,n);t.ptr+=2*n;var l,o=!0;for(l=0;l<i.numDims;l++)if(r[l]!==s[l]){o=!1;break}return i.minValues=r,i.maxValues=s,o},readSubArray:function(e,t,i,a){var n;if(i===Uint8Array)n=new Uint8Array(e,t,a);else{var r=new ArrayBuffer(a);new Uint8Array(r).set(new Uint8Array(e,t,a)),n=new i(r)}return n},readMask:function(e,t){var i,a,n=t.ptr,r=t.headerInfo,s=r.width*r.height,l=r.numValidPixel,o=new DataView(e,n,4),f={};if(f.numBytes=o.getUint32(0,!0),n+=4,(0===l||s===l)&&0!==f.numBytes)throw"invalid mask";if(0===l)i=new Uint8Array(Math.ceil(s/8)),f.bitset=i,a=new Uint8Array(s),t.pixels.resultMask=a,n+=f.numBytes;else if(f.numBytes>0){i=new Uint8Array(Math.ceil(s/8));var u=(o=new DataView(e,n,f.numBytes)).getInt16(0,!0),c=2,d=0,h=0;do{if(u>0)for(;u--;)i[d++]=o.getUint8(c++);else for(h=o.getUint8(c++),u=-u;u--;)i[d++]=h;u=o.getInt16(c,!0),c+=2}while(c<f.numBytes);if(-32768!==u||d<i.length)throw"Unexpected end of mask RLE encoding";a=new Uint8Array(s);var m=0,g=0;for(g=0;g<s;g++)7&g?(m=i[g>>3],m<<=7&g):m=i[g>>3],128&m&&(a[g]=1);t.pixels.resultMask=a,f.bitset=i,n+=f.numBytes}return t.ptr=n,t.mask=f,!0},readDataOneSweep:function(e,t,i){var a,n=t.ptr,r=t.headerInfo,s=r.numDims,l=r.width*r.height,o=r.imageType,f=r.numValidPixel*d.getDataTypeSize(o)*s,u=t.pixels.resultMask;if(i===Uint8Array)a=new Uint8Array(e,n,f);else{var c=new ArrayBuffer(f);new Uint8Array(c).set(new Uint8Array(e,n,f)),a=new i(c)}if(a.length===l*s)t.pixels.resultPixels=a;else{t.pixels.resultPixels=new i(l*s);var h=0,m=0,g=0,p=0;if(s>1)for(g=0;g<s;g++)for(p=g*l,m=0;m<l;m++)u[m]&&(t.pixels.resultPixels[p+m]=a[h++]);else for(m=0;m<l;m++)u[m]&&(t.pixels.resultPixels[m]=a[h++])}return n+=f,t.ptr=n,!0},readHuffmanTree:function(e,t){var i=this.HUFFMAN_LUT_BITS_MAX,a=new DataView(e,t.ptr,16);if(t.ptr+=16,a.getInt32(0,!0)<2)throw"unsupported Huffman version";var n=a.getInt32(4,!0),r=a.getInt32(8,!0),s=a.getInt32(12,!0);if(r>=s)return!1;var l=new Uint32Array(s-r);d.decodeBits(e,t,l);var o,f,u,c,m=[];for(o=r;o<s;o++)m[f=o-(o<n?0:n)]={first:l[o-r],second:null};var g=e.byteLength-t.ptr,p=Math.ceil(g/4),x=new ArrayBuffer(4*p);new Uint8Array(x).set(new Uint8Array(e,t.ptr,g));var w,y=new Uint32Array(x),k=0,I=0;for(w=y[0],o=r;o<s;o++)(c=m[f=o-(o<n?0:n)].first)>0&&(m[f].second=w<<k>>>32-c,32-k>=c?32===(k+=c)&&(k=0,w=y[++I]):(k+=c-32,w=y[++I],m[f].second|=w>>>32-k));var U=0,b=0,M=new h;for(o=0;o<m.length;o++)void 0!==m[o]&&(U=Math.max(U,m[o].first));b=U>=i?i:U,U>=30&&console.log("WARning, large NUM LUT BITS IS "+U);var T,v,V,A,B,D=[];for(o=r;o<s;o++)if((c=m[f=o-(o<n?0:n)].first)>0)if(T=[c,f],c<=b)for(v=m[f].second<<b-c,V=1<<b-c,u=0;u<V;u++)D[v|u]=T;else for(v=m[f].second,B=M,A=c-1;A>=0;A--)v>>>A&1?(B.right||(B.right=new h),B=B.right):(B.left||(B.left=new h),B=B.left),0!==A||B.val||(B.val=T[1]);return{decodeLut:D,numBitsLUTQick:b,numBitsLUT:U,tree:M,stuffedData:y,srcPtr:I,bitPos:k}},readHuffman:function(e,t,i){var a,n,r,s,l,o,f,u,c,d=t.headerInfo,h=d.numDims,m=t.headerInfo.height,g=t.headerInfo.width,p=g*m,x=this.readHuffmanTree(e,t),w=x.decodeLut,y=x.tree,k=x.stuffedData,I=x.srcPtr,U=x.bitPos,b=x.numBitsLUTQick,M=x.numBitsLUT,T=0===t.headerInfo.imageType?128:0,v=t.pixels.resultMask,V=0;U>0&&(I++,U=0);var A,B=k[I],D=1===t.encodeMode,S=new i(p*h),P=S;for(A=0;A<d.numDims;A++){if(h>1&&(P=new i(S.buffer,p*A,p),V=0),t.headerInfo.numValidPixel===g*m)for(u=0,o=0;o<m;o++)for(f=0;f<g;f++,u++){if(n=0,l=s=B<<U>>>32-b,32-U<b&&(l=s|=k[I+1]>>>64-U-b),w[l])n=w[l][1],U+=w[l][0];else for(l=s=B<<U>>>32-M,32-U<M&&(l=s|=k[I+1]>>>64-U-M),a=y,c=0;c<M;c++)if(!(a=s>>>M-c-1&1?a.right:a.left).left&&!a.right){n=a.val,U=U+c+1;break}U>=32&&(U-=32,B=k[++I]),r=n-T,D?(r+=f>0?V:o>0?P[u-g]:V,r&=255,P[u]=r,V=r):P[u]=r}else for(u=0,o=0;o<m;o++)for(f=0;f<g;f++,u++)if(v[u]){if(n=0,l=s=B<<U>>>32-b,32-U<b&&(l=s|=k[I+1]>>>64-U-b),w[l])n=w[l][1],U+=w[l][0];else for(l=s=B<<U>>>32-M,32-U<M&&(l=s|=k[I+1]>>>64-U-M),a=y,c=0;c<M;c++)if(!(a=s>>>M-c-1&1?a.right:a.left).left&&!a.right){n=a.val,U=U+c+1;break}U>=32&&(U-=32,B=k[++I]),r=n-T,D?(f>0&&v[u-1]?r+=V:o>0&&v[u-g]?r+=P[u-g]:r+=V,r&=255,P[u]=r,V=r):P[u]=r}t.ptr=t.ptr+4*(I+1)+(U>0?4:0)}t.pixels.resultPixels=S},decodeBits:function(e,t,i,a,n){var r=t.headerInfo,d=r.fileVersion,h=0,m=new DataView(e,t.ptr,5),g=m.getUint8(0);h++;var p=g>>6,x=0===p?4:3-p,w=(32&g)>0,y=31&g,k=0;if(1===x)k=m.getUint8(h),h++;else if(2===x)k=m.getUint16(h,!0),h+=2;else{if(4!==x)throw"Invalid valid pixel count type";k=m.getUint32(h,!0),h+=4}var I,U,b,M,T,v,V,A,B,D=2*r.maxZError,S=r.numDims>1?r.maxValues[n]:r.zMax;if(w){for(t.counter.lut++,A=m.getUint8(h),h++,M=Math.ceil((A-1)*y/8),T=Math.ceil(M/4),U=new ArrayBuffer(4*T),b=new Uint8Array(U),t.ptr+=h,b.set(new Uint8Array(e,t.ptr,M)),V=new Uint32Array(U),t.ptr+=M,B=0;A-1>>>B;)B++;M=Math.ceil(k*B/8),T=Math.ceil(M/4),U=new ArrayBuffer(4*T),(b=new Uint8Array(U)).set(new Uint8Array(e,t.ptr,M)),I=new Uint32Array(U),t.ptr+=M,v=d>=3?f(V,y,A-1,a,D,S):l(V,y,A-1,a,D,S),d>=3?o(I,i,B,k,v):s(I,i,B,k,v)}else t.counter.bitstuffer++,B=y,t.ptr+=h,B>0&&(M=Math.ceil(k*B/8),T=Math.ceil(M/4),U=new ArrayBuffer(4*T),(b=new Uint8Array(U)).set(new Uint8Array(e,t.ptr,M)),I=new Uint32Array(U),t.ptr+=M,d>=3?null==a?c(I,i,B,k):o(I,i,B,k,!1,a,D,S):null==a?u(I,i,B,k):s(I,i,B,k,!1,a,D,S))},readTiles:function(e,t,i){var a=t.headerInfo,n=a.width,r=a.height,s=a.microBlockSize,l=a.imageType,o=d.getDataTypeSize(l),f=Math.ceil(n/s),u=Math.ceil(r/s);t.pixels.numBlocksY=u,t.pixels.numBlocksX=f,t.pixels.ptr=0;var c,h,m,g,p,x,w,y,k=0,I=0,U=0,b=0,M=0,T=0,v=0,V=0,A=0,B=0,D=0,S=0,P=0,E=0,C=0,F=new i(s*s),R=r%s||s,N=n%s||s,O=a.numDims,L=t.pixels.resultMask,z=t.pixels.resultPixels;for(U=0;U<u;U++)for(M=U!==u-1?s:R,b=0;b<f;b++)for(B=U*n*s+b*s,D=n-(T=b!==f-1?s:N),y=0;y<O;y++){if(O>1&&(z=new i(t.pixels.resultPixels.buffer,n*r*y*o,n*r)),v=e.byteLength-t.ptr,h={},C=0,C++,A=(V=(c=new DataView(e,t.ptr,Math.min(10,v))).getUint8(0))>>6&255,(V>>2&15)!=(b*s>>3&15))throw"integrity issue";if((p=3&V)>3)throw t.ptr+=C,"Invalid block encoding ("+p+")";if(2!==p)if(0===p){if(t.counter.uncompressed++,t.ptr+=C,S=(S=M*T*o)<(P=e.byteLength-t.ptr)?S:P,m=new ArrayBuffer(S%o==0?S:S+o-S%o),new Uint8Array(m).set(new Uint8Array(e,t.ptr,S)),g=new i(m),E=0,L)for(k=0;k<M;k++){for(I=0;I<T;I++)L[B]&&(z[B]=g[E++]),B++;B+=D}else for(k=0;k<M;k++){for(I=0;I<T;I++)z[B++]=g[E++];B+=D}t.ptr+=E*o}else if(x=d.getDataTypeUsed(l,A),w=d.getOnePixel(h,C,x,c),C+=d.getDataTypeSize(x),3===p)if(t.ptr+=C,t.counter.constantoffset++,L)for(k=0;k<M;k++){for(I=0;I<T;I++)L[B]&&(z[B]=w),B++;B+=D}else for(k=0;k<M;k++){for(I=0;I<T;I++)z[B++]=w;B+=D}else if(t.ptr+=C,d.decodeBits(e,t,F,w,y),C=0,L)for(k=0;k<M;k++){for(I=0;I<T;I++)L[B]&&(z[B]=F[C++]),B++;B+=D}else for(k=0;k<M;k++){for(I=0;I<T;I++)z[B++]=F[C++];B+=D}else t.counter.constant++,t.ptr+=C}},formatFileInfo:function(e){return{fileIdentifierString:e.headerInfo.fileIdentifierString,fileVersion:e.headerInfo.fileVersion,imageType:e.headerInfo.imageType,height:e.headerInfo.height,width:e.headerInfo.width,numValidPixel:e.headerInfo.numValidPixel,microBlockSize:e.headerInfo.microBlockSize,blobSize:e.headerInfo.blobSize,maxZError:e.headerInfo.maxZError,pixelType:d.getPixelType(e.headerInfo.imageType),eofOffset:e.eofOffset,mask:e.mask?{numBytes:e.mask.numBytes}:null,pixels:{numBlocksX:e.pixels.numBlocksX,numBlocksY:e.pixels.numBlocksY,maxValue:e.headerInfo.zMax,minValue:e.headerInfo.zMin,noDataValue:e.noDataValue}}},constructConstantSurface:function(e){var t=e.headerInfo.zMax,i=e.headerInfo.numDims,a=e.headerInfo.height*e.headerInfo.width,n=a*i,r=0,s=0,l=0,o=e.pixels.resultMask;if(o)if(i>1)for(r=0;r<i;r++)for(l=r*a,s=0;s<a;s++)o[s]&&(e.pixels.resultPixels[l+s]=t);else for(s=0;s<a;s++)o[s]&&(e.pixels.resultPixels[s]=t);else if(e.pixels.resultPixels.fill)e.pixels.resultPixels.fill(t);else for(s=0;s<n;s++)e.pixels.resultPixels[s]=t},getDataTypeArray:function(e){var t;switch(e){case 0:t=Int8Array;break;case 1:t=Uint8Array;break;case 2:t=Int16Array;break;case 3:t=Uint16Array;break;case 4:t=Int32Array;break;case 5:t=Uint32Array;break;case 6:default:t=Float32Array;break;case 7:t=Float64Array}return t},getPixelType:function(e){var t;switch(e){case 0:t="S8";break;case 1:t="U8";break;case 2:t="S16";break;case 3:t="U16";break;case 4:t="S32";break;case 5:t="U32";break;case 6:default:t="F32";break;case 7:t="F64"}return t},isValidPixelValue:function(e,t){if(null==t)return!1;var i;switch(e){case 0:i=t>=-128&&t<=127;break;case 1:i=t>=0&&t<=255;break;case 2:i=t>=-32768&&t<=32767;break;case 3:i=t>=0&&t<=65536;break;case 4:i=t>=-2147483648&&t<=2147483647;break;case 5:i=t>=0&&t<=4294967296;break;case 6:i=t>=-34027999387901484e22&&t<=34027999387901484e22;break;case 7:i=t>=5e-324&&t<=17976931348623157e292;break;default:i=!1}return i},getDataTypeSize:function(e){var t=0;switch(e){case 0:case 1:t=1;break;case 2:case 3:t=2;break;case 4:case 5:case 6:t=4;break;case 7:t=8;break;default:t=e}return t},getDataTypeUsed:function(e,t){var i=e;switch(e){case 2:case 4:i=e-t;break;case 3:case 5:i=e-2*t;break;case 6:i=0===t?e:1===t?2:1;break;case 7:i=0===t?e:e-2*t+1;break;default:i=e}return i},getOnePixel:function(e,t,i,a){var n=0;switch(i){case 0:n=a.getInt8(t);break;case 1:n=a.getUint8(t);break;case 2:n=a.getInt16(t,!0);break;case 3:n=a.getUint16(t,!0);break;case 4:n=a.getInt32(t,!0);break;case 5:n=a.getUInt32(t,!0);break;case 6:n=a.getFloat32(t,!0);break;case 7:n=a.getFloat64(t,!0);break;default:throw"the decoder does not understand this pixel type"}return n}},h=function(e,t,i){this.val=e,this.left=t,this.right=i},{decode:function(e,t){var i=(t=t||{}).noDataValue,a=0,n={};if(n.ptr=t.inputOffset||0,n.pixels={},d.readHeaderInfo(e,n)){var r=n.headerInfo,s=r.fileVersion,l=d.getDataTypeArray(r.imageType);d.readMask(e,n),r.numValidPixel===r.width*r.height||n.pixels.resultMask||(n.pixels.resultMask=t.maskData);var o,f=r.width*r.height;if(n.pixels.resultPixels=new l(f*r.numDims),n.counter={onesweep:0,uncompressed:0,lut:0,bitstuffer:0,constant:0,constantoffset:0},0!==r.numValidPixel)if(r.zMax===r.zMin)d.constructConstantSurface(n);else if(s>=4&&d.checkMinMaxRanges(e,n))d.constructConstantSurface(n);else{var u=new DataView(e,n.ptr,2),c=u.getUint8(0);if(n.ptr++,c)d.readDataOneSweep(e,n,l);else if(s>1&&r.imageType<=1&&Math.abs(r.maxZError-.5)<1e-5){var h=u.getUint8(1);if(n.ptr++,n.encodeMode=h,h>2||s<4&&h>1)throw"Invalid Huffman flag "+h;h?d.readHuffman(e,n,l):d.readTiles(e,n,l)}else d.readTiles(e,n,l)}n.eofOffset=n.ptr,t.inputOffset?(o=n.headerInfo.blobSize+t.inputOffset-n.ptr,Math.abs(o)>=1&&(n.eofOffset=t.inputOffset+n.headerInfo.blobSize)):(o=n.headerInfo.blobSize-n.ptr,Math.abs(o)>=1&&(n.eofOffset=n.headerInfo.blobSize));var m={width:r.width,height:r.height,pixelData:n.pixels.resultPixels,minValue:r.zMin,maxValue:r.zMax,validPixelCount:r.numValidPixel,dimCount:r.numDims,dimStats:{minValues:r.minValues,maxValues:r.maxValues},maskData:n.pixels.resultMask};if(n.pixels.resultMask&&d.isValidPixelValue(r.imageType,i)){var g=n.pixels.resultMask;for(a=0;a<f;a++)g[a]||(m.pixelData[a]=i);m.noDataValue=i}return n.noDataValue=i,t.returnFileInfo&&(m.fileInfo=d.formatFileInfo(n)),m}},getBandCount:function(e){for(var t=0,i=0,a={ptr:0,pixels:{}};i<e.byteLength-58;)d.readHeaderInfo(e,a),i+=a.headerInfo.blobSize,t++,a.ptr=i;return t}}),w=(m=new ArrayBuffer(4),g=new Uint8Array(m),new Uint32Array(m)[0]=1,1===g[0]),y={decode:function(e,t){if(!w)throw"Big endian system is not supported.";var i,a,n=(t=t||{}).inputOffset||0,r=new Uint8Array(e,n,10),s=String.fromCharCode.apply(null,r);if("CntZImage"===s.trim())i=p,a=1;else{if("Lerc2"!==s.substring(0,5))throw"Unexpected file identifier string: "+s;i=x,a=2}for(var l,o,f,u,c,d,h=0,m=e.byteLength-10,g=[],y={width:0,height:0,pixels:[],pixelType:t.pixelType,mask:null,statistics:[]};n<m;){var k=i.decode(e,{inputOffset:n,encodedMaskData:l,maskData:f,returnMask:0===h,returnEncodedMask:0===h,returnFileInfo:!0,pixelType:t.pixelType||null,noDataValue:t.noDataValue||null});n=k.fileInfo.eofOffset,0===h&&(l=k.encodedMaskData,f=k.maskData,y.width=k.width,y.height=k.height,y.dimCount=k.dimCount||1,y.pixelType=k.pixelType||k.fileInfo.pixelType,y.mask=k.maskData),a>1&&k.fileInfo.mask&&k.fileInfo.mask.numBytes>0&&g.push(k.maskData),h++,y.pixels.push(k.pixelData),y.statistics.push({minValue:k.minValue,maxValue:k.maxValue,noDataValue:k.noDataValue,dimStats:k.dimStats})}if(a>1&&g.length>1){for(d=y.width*y.height,y.bandMasks=g,(f=new Uint8Array(d)).set(g[0]),u=1;u<g.length;u++)for(o=g[u],c=0;c<d;c++)f[c]=f[c]&o[c];y.maskData=f}return y}};E.exports?E.exports=y:this.Lerc=y}();var R=F.exports,N=l.getDefaultExportFromCjs(R);return d((function(t,i){if(t.encoding===V.LERC){let e;try{e=N.decode(t.heightmap)}catch(e){throw new c.RuntimeError(e)}if(e.statistics[0].minValue===Number.MAX_VALUE)throw new c.RuntimeError("Invalid tile data");t.heightmap=e.pixels[0],t.width=e.width,t.height=e.height}t.ellipsoid=e.Ellipsoid.clone(t.ellipsoid),t.rectangle=f.Rectangle.clone(t.rectangle);const a=C.computeVertices(t),n=a.vertices;return i.push(n.buffer),{vertices:n.buffer,numberOfAttributes:a.encoding.stride,minimumHeight:a.minimumHeight,maximumHeight:a.maximumHeight,gridWidth:t.width,gridHeight:t.height,boundingSphere3D:a.boundingSphere3D,orientedBoundingBox:a.orientedBoundingBox,occludeePointInScaledSpace:a.occludeePointInScaledSpace,encoding:a.encoding,westIndicesSouthToNorth:a.westIndicesSouthToNorth,southIndicesEastToWest:a.southIndicesEastToWest,eastIndicesNorthToSouth:a.eastIndicesNorthToSouth,northIndicesWestToEast:a.northIndicesWestToEast}}))}));
