define(["./Cartesian3-529c236c","./defaultValue-f6d5e6da","./EllipseOutlineGeometry-34206fce","./Ellipsoid-8e26549b","./Math-355606c6","./Transforms-2afbbfb5","./Cartographic-dbefb6fa","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./EllipseGeometryLibrary-15deff83","./GeometryAttribute-f7a0845b","./Matrix2-e4a4609a","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-58eb7805"],(function(e,i,t,r,l,n,o,s,a,u,m,c,d,p,y,G,_,h,f,x,E,g,M){"use strict";function O(e){const r=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).radius,l={center:e.center,semiMajorAxis:r,semiMinorAxis:r,ellipsoid:e.ellipsoid,height:e.height,extrudedHeight:e.extrudedHeight,granularity:e.granularity,numberOfVerticalLines:e.numberOfVerticalLines};this._ellipseGeometry=new t.EllipseOutlineGeometry(l),this._workerName="createCircleOutlineGeometry"}O.packedLength=t.EllipseOutlineGeometry.packedLength,O.pack=function(e,i,r){return t.EllipseOutlineGeometry.pack(e._ellipseGeometry,i,r)};const A=new t.EllipseOutlineGeometry({center:new e.Cartesian3,semiMajorAxis:1,semiMinorAxis:1}),b={center:new e.Cartesian3,radius:void 0,ellipsoid:r.Ellipsoid.clone(r.Ellipsoid.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,numberOfVerticalLines:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0};return O.unpack=function(l,n,o){const s=t.EllipseOutlineGeometry.unpack(l,n,A);return b.center=e.Cartesian3.clone(s._center,b.center),b.ellipsoid=r.Ellipsoid.clone(s._ellipsoid,b.ellipsoid),b.height=s._height,b.extrudedHeight=s._extrudedHeight,b.granularity=s._granularity,b.numberOfVerticalLines=s._numberOfVerticalLines,i.defined(o)?(b.semiMajorAxis=s._semiMajorAxis,b.semiMinorAxis=s._semiMinorAxis,o._ellipseGeometry=new t.EllipseOutlineGeometry(b),o):(b.radius=s._semiMajorAxis,new O(b))},O.createGeometry=function(e){return t.EllipseOutlineGeometry.createGeometry(e._ellipseGeometry)},function(t,l){return i.defined(l)&&(t=O.unpack(t,l)),t._ellipseGeometry._center=e.Cartesian3.clone(t._ellipseGeometry._center),t._ellipseGeometry._ellipsoid=r.Ellipsoid.clone(t._ellipseGeometry._ellipsoid),O.createGeometry(t)}}));
