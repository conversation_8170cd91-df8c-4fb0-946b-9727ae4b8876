define(["./AttributeCompression-d2ca507e","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./Ellipsoid-8e26549b","./Math-355606c6","./Rectangle-98b0bef0","./createTaskProcessorWorker","./Matrix4-c57ffbd8","./defaultValue-f6d5e6da","./Matrix3-31d1f01f","./RuntimeError-9b4ce3fb","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./Matrix2-e4a4609a"],(function(e,t,a,r,n,i,o,s,c,u,l,p,f,h){"use strict";const m=32767,C=new a.Cartographic,g=new t.Cartesian3,d=new i.Rectangle,b=new r.Ellipsoid,w={min:void 0,max:void 0};return o((function(o,s){const c=new Uint16Array(o.positions);!function(e){e=new Float64Array(e);let t=0;w.min=e[t++],w.max=e[t++],i.Rectangle.unpack(e,t,d),t+=i.Rectangle.packedLength,r.Ellipsoid.unpack(e,t,b)}(o.packedBuffer);const u=d,l=b,p=w.min,f=w.max,h=c.length/3,k=c.subarray(0,h),y=c.subarray(h,2*h),M=c.subarray(2*h,3*h);e.AttributeCompression.zigZagDeltaDecode(k,y,M);const x=new Float64Array(c.length);for(let e=0;e<h;++e){const r=k[e],i=y[e],o=M[e],s=n.CesiumMath.lerp(u.west,u.east,r/m),c=n.CesiumMath.lerp(u.south,u.north,i/m),h=n.CesiumMath.lerp(p,f,o/m),d=a.Cartographic.fromRadians(s,c,h,C),b=l.cartographicToCartesian(d,g);t.Cartesian3.pack(b,x,3*e)}return s.push(x.buffer),{positions:x.buffer}}))}));
