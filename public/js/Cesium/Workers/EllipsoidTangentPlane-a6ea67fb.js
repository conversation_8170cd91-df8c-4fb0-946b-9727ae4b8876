define(["exports","./AxisAlignedBoundingBox-d98e5354","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./IntersectionTests-01432fe7","./Plane-06f34fae","./Transforms-2afbbfb5"],(function(t,n,e,i,o,r,s,a,l){"use strict";const c=new e.Cartesian4;function d(t,n){t=(n=o.defaultValue(n,r.Ellipsoid.WGS84)).scaleToGeodeticSurface(t);const s=l.Transforms.eastNorthUpToFixedFrame(t,n);this._ellipsoid=n,this._origin=t,this._xAxis=i.Cartesian3.fromCartesian4(e.Matrix4.getColumn(s,0,c)),this._yAxis=i.<PERSON>3.fromCartesian4(e.Matrix4.getColumn(s,1,c));const d=i.Cartesian3.fromCartesian4(e.Matrix4.getColumn(s,2,c));this._plane=a.Plane.fromPointNormal(t,d)}Object.defineProperties(d.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},origin:{get:function(){return this._origin}},plane:{get:function(){return this._plane}},xAxis:{get:function(){return this._xAxis}},yAxis:{get:function(){return this._yAxis}},zAxis:{get:function(){return this._plane.normal}}});const p=new n.AxisAlignedBoundingBox;d.fromPoints=function(t,e){return new d(n.AxisAlignedBoundingBox.fromPoints(t,p).center,e)};const u=new s.Ray,f=new i.Cartesian3;d.prototype.projectPointOntoPlane=function(t,n){const r=u;r.origin=t,i.Cartesian3.normalize(t,r.direction);let a=s.IntersectionTests.rayPlane(r,this._plane,f);if(o.defined(a)||(i.Cartesian3.negate(r.direction,r.direction),a=s.IntersectionTests.rayPlane(r,this._plane,f)),o.defined(a)){const t=i.Cartesian3.subtract(a,this._origin,a),r=i.Cartesian3.dot(this._xAxis,t),s=i.Cartesian3.dot(this._yAxis,t);return o.defined(n)?(n.x=r,n.y=s,n):new e.Cartesian2(r,s)}},d.prototype.projectPointsOntoPlane=function(t,n){o.defined(n)||(n=[]);let e=0;const i=t.length;for(let r=0;r<i;r++){const i=this.projectPointOntoPlane(t[r],n[e]);o.defined(i)&&(n[e]=i,e++)}return n.length=e,n},d.prototype.projectPointToNearestOnPlane=function(t,n){o.defined(n)||(n=new e.Cartesian2);const r=u;r.origin=t,i.Cartesian3.clone(this._plane.normal,r.direction);let a=s.IntersectionTests.rayPlane(r,this._plane,f);o.defined(a)||(i.Cartesian3.negate(r.direction,r.direction),a=s.IntersectionTests.rayPlane(r,this._plane,f));const l=i.Cartesian3.subtract(a,this._origin,a),c=i.Cartesian3.dot(this._xAxis,l),d=i.Cartesian3.dot(this._yAxis,l);return n.x=c,n.y=d,n},d.prototype.projectPointsToNearestOnPlane=function(t,n){o.defined(n)||(n=[]);const e=t.length;n.length=e;for(let i=0;i<e;i++)n[i]=this.projectPointToNearestOnPlane(t[i],n[i]);return n};const h=new i.Cartesian3;d.prototype.projectPointOntoEllipsoid=function(t,n){o.defined(n)||(n=new i.Cartesian3);const e=this._ellipsoid,r=this._origin,s=this._xAxis,a=this._yAxis,l=h;return i.Cartesian3.multiplyByScalar(s,t.x,l),n=i.Cartesian3.add(r,l,n),i.Cartesian3.multiplyByScalar(a,t.y,l),i.Cartesian3.add(n,l,n),e.scaleToGeocentricSurface(n,n),n},d.prototype.projectPointsOntoEllipsoid=function(t,n){const e=t.length;o.defined(n)?n.length=e:n=new Array(e);for(let i=0;i<e;++i)n[i]=this.projectPointOntoEllipsoid(t[i],n[i]);return n},t.EllipsoidTangentPlane=d}));
