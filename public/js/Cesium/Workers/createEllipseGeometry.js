define(["./Cartesian3-529c236c","./defaultValue-f6d5e6da","./EllipseGeometry-a9ea9512","./Ellipsoid-8e26549b","./Math-355606c6","./Transforms-2afbbfb5","./Cartographic-dbefb6fa","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./EllipseGeometryLibrary-15deff83","./GeometryAttribute-f7a0845b","./Matrix2-e4a4609a","./GeometryAttributes-1e4ddcd2","./GeometryInstance-1d11f88d","./GeometryOffsetAttribute-2579b8d2","./GeometryPipeline-69b47aa9","./AttributeCompression-d2ca507e","./EncodedCartesian3-94199dac","./IndexDatatype-58eb7805","./IntersectionTests-01432fe7","./Plane-06f34fae","./VertexFormat-fbdec922"],(function(e,t,r,i,n,o,a,l,s,p,m,c,u,y,d,G,f,E,b,C,x,A,I,M,_,h,v,D,R,T){"use strict";return function(n,o){return t.defined(o)&&(n=r.EllipseGeometry.unpack(n,o)),n._center=e.Cartesian3.clone(n._center),n._ellipsoid=i.Ellipsoid.clone(n._ellipsoid),r.EllipseGeometry.createGeometry(n)}}));
