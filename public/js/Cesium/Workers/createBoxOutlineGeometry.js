define(["./Transforms-2afbbfb5","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./defaultValue-f6d5e6da","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./Cartographic-dbefb6fa","./Math-355606c6","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./WebGLConstants-7f557f93","./Matrix2-e4a4609a"],(function(t,e,n,a,i,r,o,u,s,m,f,c,l,p,d,y,C,b,A,x){"use strict";const _=new e.Cartesian3;function w(t){const n=(t=a.defaultValue(t,a.defaultValue.EMPTY_OBJECT)).minimum,i=t.maximum;this._min=e.Cartesian3.clone(n),this._max=e.Cartesian3.clone(i),this._offsetAttribute=t.offsetAttribute,this._workerName="createBoxOutlineGeometry"}w.fromDimensions=function(t){const n=(t=a.defaultValue(t,a.defaultValue.EMPTY_OBJECT)).dimensions,i=e.Cartesian3.multiplyByScalar(n,.5,new e.Cartesian3);return new w({minimum:e.Cartesian3.negate(i,new e.Cartesian3),maximum:i,offsetAttribute:t.offsetAttribute})},w.fromAxisAlignedBoundingBox=function(t){return new w({minimum:t.minimum,maximum:t.maximum})},w.packedLength=2*e.Cartesian3.packedLength+1,w.pack=function(t,n,i){return i=a.defaultValue(i,0),e.Cartesian3.pack(t._min,n,i),e.Cartesian3.pack(t._max,n,i+e.Cartesian3.packedLength),n[i+2*e.Cartesian3.packedLength]=a.defaultValue(t._offsetAttribute,-1),n};const h=new e.Cartesian3,g=new e.Cartesian3,k={minimum:h,maximum:g,offsetAttribute:void 0};return w.unpack=function(t,n,i){n=a.defaultValue(n,0);const r=e.Cartesian3.unpack(t,n,h),o=e.Cartesian3.unpack(t,n+e.Cartesian3.packedLength,g),u=t[n+2*e.Cartesian3.packedLength];return a.defined(i)?(i._min=e.Cartesian3.clone(r,i._min),i._max=e.Cartesian3.clone(o,i._max),i._offsetAttribute=-1===u?void 0:u,i):(k.offsetAttribute=-1===u?void 0:u,new w(k))},w.createGeometry=function(u){const s=u._min,m=u._max;if(e.Cartesian3.equals(s,m))return;const f=new r.GeometryAttributes,c=new Uint16Array(24),l=new Float64Array(24);l[0]=s.x,l[1]=s.y,l[2]=s.z,l[3]=m.x,l[4]=s.y,l[5]=s.z,l[6]=m.x,l[7]=m.y,l[8]=s.z,l[9]=s.x,l[10]=m.y,l[11]=s.z,l[12]=s.x,l[13]=s.y,l[14]=m.z,l[15]=m.x,l[16]=s.y,l[17]=m.z,l[18]=m.x,l[19]=m.y,l[20]=m.z,l[21]=s.x,l[22]=m.y,l[23]=m.z,f.position=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:l}),c[0]=4,c[1]=5,c[2]=5,c[3]=6,c[4]=6,c[5]=7,c[6]=7,c[7]=4,c[8]=0,c[9]=1,c[10]=1,c[11]=2,c[12]=2,c[13]=3,c[14]=3,c[15]=0,c[16]=0,c[17]=4,c[18]=1,c[19]=5,c[20]=2,c[21]=6,c[22]=3,c[23]=7;const p=e.Cartesian3.subtract(m,s,_),d=.5*e.Cartesian3.magnitude(p);if(a.defined(u._offsetAttribute)){const t=l.length,e=u._offsetAttribute===o.GeometryOffsetAttribute.NONE?0:1,a=new Uint8Array(t/3).fill(e);f.applyOffset=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:a})}return new i.Geometry({attributes:f,indices:c,primitiveType:i.PrimitiveType.LINES,boundingSphere:new t.BoundingSphere(e.Cartesian3.ZERO,d),offsetAttribute:u._offsetAttribute})},function(t,e){return a.defined(e)&&(t=w.unpack(t,e)),w.createGeometry(t)}}));
