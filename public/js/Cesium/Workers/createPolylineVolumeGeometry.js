define(["./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./arrayRemoveDuplicates-0d8dde26","./BoundingRectangle-a5a1c0f3","./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./PolylineVolumeGeometryLibrary-e8146b77","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryPipeline-69b47aa9","./IndexDatatype-58eb7805","./Math-355606c6","./PolygonPipeline-39b84ada","./VertexFormat-fbdec922","./Cartographic-dbefb6fa","./Rectangle-98b0bef0","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./combine-0c102d93","./RequestType-735c98f2","./RuntimeError-9b4ce3fb","./WebGLConstants-7f557f93","./EllipsoidTangentPlane-a6ea67fb","./AxisAlignedBoundingBox-d98e5354","./IntersectionTests-01432fe7","./Plane-06f34fae","./PolylinePipeline-07b67faf","./EllipsoidGeodesic-8ac7b85d","./EllipsoidRhumbLine-6774fec3","./Matrix2-e4a4609a","./AttributeCompression-d2ca507e","./EncodedCartesian3-94199dac"],(function(e,t,n,o,i,r,a,l,s,p,c,u,d,m,y,g,h,f,E,P,_,b,v,k,x,C,V,L,A,F,T,G,D,R,w){"use strict";function B(n){const o=(n=e.defaultValue(n,e.defaultValue.EMPTY_OBJECT)).polylinePositions,i=n.shapePositions;this._positions=o,this._shape=i,this._ellipsoid=t.Ellipsoid.clone(e.defaultValue(n.ellipsoid,t.Ellipsoid.WGS84)),this._cornerType=e.defaultValue(n.cornerType,s.CornerType.ROUNDED),this._vertexFormat=g.VertexFormat.clone(e.defaultValue(n.vertexFormat,g.VertexFormat.DEFAULT)),this._granularity=e.defaultValue(n.granularity,m.CesiumMath.RADIANS_PER_DEGREE),this._workerName="createPolylineVolumeGeometry";let l=1+o.length*a.Cartesian3.packedLength;l+=1+i.length*r.Cartesian2.packedLength,this.packedLength=l+t.Ellipsoid.packedLength+g.VertexFormat.packedLength+2}B.pack=function(n,o,i){let l;i=e.defaultValue(i,0);const s=n._positions;let p=s.length;for(o[i++]=p,l=0;l<p;++l,i+=a.Cartesian3.packedLength)a.Cartesian3.pack(s[l],o,i);const c=n._shape;for(p=c.length,o[i++]=p,l=0;l<p;++l,i+=r.Cartesian2.packedLength)r.Cartesian2.pack(c[l],o,i);return t.Ellipsoid.pack(n._ellipsoid,o,i),i+=t.Ellipsoid.packedLength,g.VertexFormat.pack(n._vertexFormat,o,i),i+=g.VertexFormat.packedLength,o[i++]=n._cornerType,o[i]=n._granularity,o};const I=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),S=new g.VertexFormat,M={polylinePositions:void 0,shapePositions:void 0,ellipsoid:I,vertexFormat:S,cornerType:void 0,granularity:void 0};B.unpack=function(n,o,i){let l;o=e.defaultValue(o,0);let s=n[o++];const p=new Array(s);for(l=0;l<s;++l,o+=a.Cartesian3.packedLength)p[l]=a.Cartesian3.unpack(n,o);s=n[o++];const c=new Array(s);for(l=0;l<s;++l,o+=r.Cartesian2.packedLength)c[l]=r.Cartesian2.unpack(n,o);const u=t.Ellipsoid.unpack(n,o,I);o+=t.Ellipsoid.packedLength;const d=g.VertexFormat.unpack(n,o,S);o+=g.VertexFormat.packedLength;const m=n[o++],y=n[o];return e.defined(i)?(i._positions=p,i._shape=c,i._ellipsoid=t.Ellipsoid.clone(u,i._ellipsoid),i._vertexFormat=g.VertexFormat.clone(d,i._vertexFormat),i._cornerType=m,i._granularity=y,i):(M.polylinePositions=p,M.shapePositions=c,M.cornerType=m,M.granularity=y,new B(M))};const N=new o.BoundingRectangle;return B.createGeometry=function(e){const t=e._positions,r=n.arrayRemoveDuplicates(t,a.Cartesian3.equalsEpsilon);let m=e._shape;if(m=s.PolylineVolumeGeometryLibrary.removeDuplicatesFromShape(m),r.length<2||m.length<3)return;y.PolygonPipeline.computeWindingOrder2D(m)===y.WindingOrder.CLOCKWISE&&m.reverse();const g=o.BoundingRectangle.fromPoints(m,N);return function(e,t,n,o){const r=new c.GeometryAttributes;o.position&&(r.position=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e}));const a=t.length,m=e.length/3,g=(m-2*a)/(2*a),h=y.PolygonPipeline.triangulate(t),f=(g-1)*a*6+2*h.length,E=d.IndexDatatype.createTypedArray(m,f);let P,_,b,v,k,x;const C=2*a;let V=0;for(P=0;P<g-1;P++){for(_=0;_<a-1;_++)b=2*_+P*a*2,x=b+C,v=b+1,k=v+C,E[V++]=v,E[V++]=b,E[V++]=k,E[V++]=k,E[V++]=b,E[V++]=x;b=2*a-2+P*a*2,v=b+1,k=v+C,x=b+C,E[V++]=v,E[V++]=b,E[V++]=k,E[V++]=k,E[V++]=b,E[V++]=x}if(o.st||o.tangent||o.bitangent){const e=new Float32Array(2*m),o=1/(g-1),i=1/n.height,s=n.height/2;let c,u,d=0;for(P=0;P<g;P++){for(c=P*o,u=i*(t[0].y+s),e[d++]=c,e[d++]=u,_=1;_<a;_++)u=i*(t[_].y+s),e[d++]=c,e[d++]=u,e[d++]=c,e[d++]=u;u=i*(t[0].y+s),e[d++]=c,e[d++]=u}for(_=0;_<a;_++)c=0,u=i*(t[_].y+s),e[d++]=c,e[d++]=u;for(_=0;_<a;_++)c=(g-1)*o,u=i*(t[_].y+s),e[d++]=c,e[d++]=u;r.st=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:new Float32Array(e)})}const L=m-2*a;for(P=0;P<h.length;P+=3){const e=h[P]+L,t=h[P+1]+L,n=h[P+2]+L;E[V++]=e,E[V++]=t,E[V++]=n,E[V++]=n+a,E[V++]=t+a,E[V++]=e+a}let A=new p.Geometry({attributes:r,indices:E,boundingSphere:i.BoundingSphere.fromVertices(e),primitiveType:p.PrimitiveType.TRIANGLES});if(o.normal&&(A=u.GeometryPipeline.computeNormal(A)),o.tangent||o.bitangent){try{A=u.GeometryPipeline.computeTangentAndBitangent(A)}catch(e){s.oneTimeWarning("polyline-volume-tangent-bitangent","Unable to compute tangents and bitangents for polyline volume geometry")}o.tangent||(A.attributes.tangent=void 0),o.bitangent||(A.attributes.bitangent=void 0),o.st||(A.attributes.st=void 0)}return A}(s.PolylineVolumeGeometryLibrary.computePositions(r,m,g,e,!0),m,g,e._vertexFormat)},function(n,o){return e.defined(o)&&(n=B.unpack(n,o)),n._ellipsoid=t.Ellipsoid.clone(n._ellipsoid),B.createGeometry(n)}}));
