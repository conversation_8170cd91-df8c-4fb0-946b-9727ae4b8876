define(["./defaultValue-f6d5e6da","./createTaskProcessorWorker","./EV_IndexedDBProvider-596d1bfc","./DeveloperError-c85858c1"],(function(e,a,s,r){"use strict";let d;function o(e,a,r){const d=self.webkitPostMessage||self.postMessage;try{if(a){let o;switch(e){case s.EV_IndexDBType.TERRAIN:case s.EV_IndexDBType.IMAGERY:o={workerType:e,success:a,dataName:r.keyName,workerTaskID:r.workerTaskID,level:r.level,x:r.x,y:r.y,upsample:r.upsample,data:r.data};break;case s.EV_IndexDBType.TILES3D:o={workerType:e,success:a,dataName:r.keyName,data:r.data,meshPrimitives:r.meshPrimitives};break;case s.EV_IndexDBType.BIN:case s.EV_IndexDBType.IMAGE:case s.EV_IndexDBType.FILE:o={workerType:e,success:a,dataName:r.keyName,data:r.data};break;default:console.log(" ")}d(o)}else d({workerType:e,success:a,dataName:r.keyName})}catch(s){d({workerType:e,success:a,dataName:r.keyName})}}return a((function(a,r){const n=a.indexedDB,t=a.taskData;if(t.workerTaskID=a.workerTaskID,e.defined(d))if(e.defined(d.iDB))switch(t.workerType){case s.EV_IndexDBType.TERRAIN:d.downloadTerrainData(t,o);break;case s.EV_IndexDBType.IMAGERY:d.downloadImagery(t,o);break;case s.EV_IndexDBType.TILES3D:d.download3DTileArrayBuffer(t,o);break;case s.EV_IndexDBType.BIN:d.downloadGltfBin(t,o);break;case s.EV_IndexDBType.IMAGE:d.downloadImage(t,o);break;case s.EV_IndexDBType.FILE:d.downloadFile(t,o);break;default:console.log(" ")}else o(t.workerType,0,t);else d=new s.EV_IndexedDBProvider(n),d.createIDB((function(){})),o(t.workerType,0,t)}))}));
