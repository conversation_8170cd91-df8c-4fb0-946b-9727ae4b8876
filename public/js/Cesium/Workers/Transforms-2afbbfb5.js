define(["require","exports","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./Math-355606c6","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./Rectangle-98b0bef0","./DeveloperError-c85858c1","./combine-0c102d93","./RequestType-735c98f2","./RuntimeError-9b4ce3fb"],(function(e,t,n,r,o,i,s,a,u,c,l,d,f,p,h){"use strict";function m(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}function g(e){this._ellipsoid=o.defaultValue(e,i.Ellipsoid.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(g.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}}),g.prototype.project=function(e,t){const r=this._semimajorAxis,i=e.longitude*r,s=e.latitude*r,a=e.height;return o.defined(t)?(t.x=i,t.y=s,t.z=a,t):new n.Cartesian3(i,s,a)},g.prototype.unproject=function(e,t){const n=this._oneOverSemimajorAxis,i=e.x*n,s=e.y*n,a=e.z;return o.defined(t)?(t.longitude=i,t.latitude=s,t.height=a,t):new r.Cartographic(i,s,a)};var y=Object.freeze({OUTSIDE:-1,INTERSECTING:0,INSIDE:1});function v(e,t){this.center=n.Cartesian3.clone(o.defaultValue(e,n.Cartesian3.ZERO)),this.radius=o.defaultValue(t,0)}const w=new n.Cartesian3,C=new n.Cartesian3,_=new n.Cartesian3,b=new n.Cartesian3,x=new n.Cartesian3,S=new n.Cartesian3,A=new n.Cartesian3,E=new n.Cartesian3,O=new n.Cartesian3,I=new n.Cartesian3,P=new n.Cartesian3,R=new n.Cartesian3,T=4/3*a.CesiumMath.PI;v.fromPoints=function(e,t){if(o.defined(t)||(t=new v),!o.defined(e)||0===e.length)return t.center=n.Cartesian3.clone(n.Cartesian3.ZERO,t.center),t.radius=0,t;const r=n.Cartesian3.clone(e[0],A),i=n.Cartesian3.clone(r,w),s=n.Cartesian3.clone(r,C),a=n.Cartesian3.clone(r,_),u=n.Cartesian3.clone(r,b),c=n.Cartesian3.clone(r,x),l=n.Cartesian3.clone(r,S),d=e.length;let f;for(f=1;f<d;f++){n.Cartesian3.clone(e[f],r);const t=r.x,o=r.y,d=r.z;t<i.x&&n.Cartesian3.clone(r,i),t>u.x&&n.Cartesian3.clone(r,u),o<s.y&&n.Cartesian3.clone(r,s),o>c.y&&n.Cartesian3.clone(r,c),d<a.z&&n.Cartesian3.clone(r,a),d>l.z&&n.Cartesian3.clone(r,l)}const p=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(u,i,E)),h=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(c,s,E)),m=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(l,a,E));let g=i,y=u,T=p;h>T&&(T=h,g=s,y=c),m>T&&(T=m,g=a,y=l);const q=O;q.x=.5*(g.x+y.x),q.y=.5*(g.y+y.y),q.z=.5*(g.z+y.z);let z=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(y,q,E)),M=Math.sqrt(z);const D=I;D.x=i.x,D.y=s.y,D.z=a.z;const U=P;U.x=u.x,U.y=c.y,U.z=l.z;const k=n.Cartesian3.midpoint(D,U,R);let F=0;for(f=0;f<d;f++){n.Cartesian3.clone(e[f],r);const t=n.Cartesian3.magnitude(n.Cartesian3.subtract(r,k,E));t>F&&(F=t);const o=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(r,q,E));if(o>z){const e=Math.sqrt(o);M=.5*(M+e),z=M*M;const t=e-M;q.x=(M*q.x+t*r.x)/e,q.y=(M*q.y+t*r.y)/e,q.z=(M*q.z+t*r.z)/e}}return M<F?(n.Cartesian3.clone(q,t.center),t.radius=M):(n.Cartesian3.clone(k,t.center),t.radius=F),t};const q=new g,z=new n.Cartesian3,M=new n.Cartesian3,D=new r.Cartographic,U=new r.Cartographic;v.fromRectangle2D=function(e,t,n){return v.fromRectangleWithHeights2D(e,t,0,0,n)},v.fromRectangleWithHeights2D=function(e,t,r,i,s){if(o.defined(s)||(s=new v),!o.defined(e))return s.center=n.Cartesian3.clone(n.Cartesian3.ZERO,s.center),s.radius=0,s;t=o.defaultValue(t,q),l.Rectangle.southwest(e,D),D.height=r,l.Rectangle.northeast(e,U),U.height=i;const a=t.project(D,z),u=t.project(U,M),c=u.x-a.x,d=u.y-a.y,f=u.z-a.z;s.radius=.5*Math.sqrt(c*c+d*d+f*f);const p=s.center;return p.x=a.x+.5*c,p.y=a.y+.5*d,p.z=a.z+.5*f,s};const k=[];v.fromRectangle3D=function(e,t,r,s){if(t=o.defaultValue(t,i.Ellipsoid.WGS84),r=o.defaultValue(r,0),o.defined(s)||(s=new v),!o.defined(e))return s.center=n.Cartesian3.clone(n.Cartesian3.ZERO,s.center),s.radius=0,s;const a=l.Rectangle.subsample(e,t,r,k);return v.fromPoints(a,s)},v.fromVertices=function(e,t,r,i){if(o.defined(i)||(i=new v),!o.defined(e)||0===e.length)return i.center=n.Cartesian3.clone(n.Cartesian3.ZERO,i.center),i.radius=0,i;t=o.defaultValue(t,n.Cartesian3.ZERO),r=o.defaultValue(r,3);const s=A;s.x=e[0]+t.x,s.y=e[1]+t.y,s.z=e[2]+t.z;const a=n.Cartesian3.clone(s,w),u=n.Cartesian3.clone(s,C),c=n.Cartesian3.clone(s,_),l=n.Cartesian3.clone(s,b),d=n.Cartesian3.clone(s,x),f=n.Cartesian3.clone(s,S),p=e.length;let h;for(h=0;h<p;h+=r){const r=e[h]+t.x,o=e[h+1]+t.y,i=e[h+2]+t.z;s.x=r,s.y=o,s.z=i,r<a.x&&n.Cartesian3.clone(s,a),r>l.x&&n.Cartesian3.clone(s,l),o<u.y&&n.Cartesian3.clone(s,u),o>d.y&&n.Cartesian3.clone(s,d),i<c.z&&n.Cartesian3.clone(s,c),i>f.z&&n.Cartesian3.clone(s,f)}const m=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(l,a,E)),g=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(d,u,E)),y=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(f,c,E));let T=a,q=l,z=m;g>z&&(z=g,T=u,q=d),y>z&&(z=y,T=c,q=f);const M=O;M.x=.5*(T.x+q.x),M.y=.5*(T.y+q.y),M.z=.5*(T.z+q.z);let D=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(q,M,E)),U=Math.sqrt(D);const k=I;k.x=a.x,k.y=u.y,k.z=c.z;const F=P;F.x=l.x,F.y=d.y,F.z=f.z;const N=n.Cartesian3.midpoint(k,F,R);let j=0;for(h=0;h<p;h+=r){s.x=e[h]+t.x,s.y=e[h+1]+t.y,s.z=e[h+2]+t.z;const r=n.Cartesian3.magnitude(n.Cartesian3.subtract(s,N,E));r>j&&(j=r);const o=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(s,M,E));if(o>D){const e=Math.sqrt(o);U=.5*(U+e),D=U*U;const t=e-U;M.x=(U*M.x+t*s.x)/e,M.y=(U*M.y+t*s.y)/e,M.z=(U*M.z+t*s.z)/e}}return U<j?(n.Cartesian3.clone(M,i.center),i.radius=U):(n.Cartesian3.clone(N,i.center),i.radius=j),i},v.fromEncodedCartesianVertices=function(e,t,r){if(o.defined(r)||(r=new v),!o.defined(e)||!o.defined(t)||e.length!==t.length||0===e.length)return r.center=n.Cartesian3.clone(n.Cartesian3.ZERO,r.center),r.radius=0,r;const i=A;i.x=e[0]+t[0],i.y=e[1]+t[1],i.z=e[2]+t[2];const s=n.Cartesian3.clone(i,w),a=n.Cartesian3.clone(i,C),u=n.Cartesian3.clone(i,_),c=n.Cartesian3.clone(i,b),l=n.Cartesian3.clone(i,x),d=n.Cartesian3.clone(i,S),f=e.length;let p;for(p=0;p<f;p+=3){const r=e[p]+t[p],o=e[p+1]+t[p+1],f=e[p+2]+t[p+2];i.x=r,i.y=o,i.z=f,r<s.x&&n.Cartesian3.clone(i,s),r>c.x&&n.Cartesian3.clone(i,c),o<a.y&&n.Cartesian3.clone(i,a),o>l.y&&n.Cartesian3.clone(i,l),f<u.z&&n.Cartesian3.clone(i,u),f>d.z&&n.Cartesian3.clone(i,d)}const h=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(c,s,E)),m=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(l,a,E)),g=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(d,u,E));let y=s,T=c,q=h;m>q&&(q=m,y=a,T=l),g>q&&(q=g,y=u,T=d);const z=O;z.x=.5*(y.x+T.x),z.y=.5*(y.y+T.y),z.z=.5*(y.z+T.z);let M=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(T,z,E)),D=Math.sqrt(M);const U=I;U.x=s.x,U.y=a.y,U.z=u.z;const k=P;k.x=c.x,k.y=l.y,k.z=d.z;const F=n.Cartesian3.midpoint(U,k,R);let N=0;for(p=0;p<f;p+=3){i.x=e[p]+t[p],i.y=e[p+1]+t[p+1],i.z=e[p+2]+t[p+2];const r=n.Cartesian3.magnitude(n.Cartesian3.subtract(i,F,E));r>N&&(N=r);const o=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(i,z,E));if(o>M){const e=Math.sqrt(o);D=.5*(D+e),M=D*D;const t=e-D;z.x=(D*z.x+t*i.x)/e,z.y=(D*z.y+t*i.y)/e,z.z=(D*z.z+t*i.z)/e}}return D<N?(n.Cartesian3.clone(z,r.center),r.radius=D):(n.Cartesian3.clone(F,r.center),r.radius=N),r},v.fromCornerPoints=function(e,t,r){o.defined(r)||(r=new v);const i=n.Cartesian3.midpoint(e,t,r.center);return r.radius=n.Cartesian3.distance(i,t),r},v.fromEllipsoid=function(e,t){return o.defined(t)||(t=new v),n.Cartesian3.clone(n.Cartesian3.ZERO,t.center),t.radius=e.maximumRadius,t};const F=new n.Cartesian3;v.fromBoundingSpheres=function(e,t){if(o.defined(t)||(t=new v),!o.defined(e)||0===e.length)return t.center=n.Cartesian3.clone(n.Cartesian3.ZERO,t.center),t.radius=0,t;const r=e.length;if(1===r)return v.clone(e[0],t);if(2===r)return v.union(e[0],e[1],t);const i=[];let s;for(s=0;s<r;s++)i.push(e[s].center);const a=(t=v.fromPoints(i,t)).center;let u=t.radius;for(s=0;s<r;s++){const t=e[s];u=Math.max(u,n.Cartesian3.distance(a,t.center,F)+t.radius)}return t.radius=u,t};const N=new n.Cartesian3,j=new n.Cartesian3,B=new n.Cartesian3;v.fromOrientedBoundingBox=function(e,t){o.defined(t)||(t=new v);const r=e.halfAxes,i=u.Matrix3.getColumn(r,0,N),s=u.Matrix3.getColumn(r,1,j),a=u.Matrix3.getColumn(r,2,B);return n.Cartesian3.add(i,s,i),n.Cartesian3.add(i,a,i),t.center=n.Cartesian3.clone(e.center,t.center),t.radius=n.Cartesian3.magnitude(i),t};const V=new n.Cartesian3,L=new n.Cartesian3;v.fromTransformation=function(e,t){o.defined(t)||(t=new v);const r=c.Matrix4.getTranslation(e,V),i=c.Matrix4.getScale(e,L),s=.5*n.Cartesian3.magnitude(i);return t.center=n.Cartesian3.clone(r,t.center),t.radius=s,t},v.clone=function(e,t){if(o.defined(e))return o.defined(t)?(t.center=n.Cartesian3.clone(e.center,t.center),t.radius=e.radius,t):new v(e.center,e.radius)},v.packedLength=4,v.pack=function(e,t,n){n=o.defaultValue(n,0);const r=e.center;return t[n++]=r.x,t[n++]=r.y,t[n++]=r.z,t[n]=e.radius,t},v.unpack=function(e,t,n){t=o.defaultValue(t,0),o.defined(n)||(n=new v);const r=n.center;return r.x=e[t++],r.y=e[t++],r.z=e[t++],n.radius=e[t],n};const Q=new n.Cartesian3,$=new n.Cartesian3;v.union=function(e,t,r){o.defined(r)||(r=new v);const i=e.center,s=e.radius,a=t.center,u=t.radius,c=n.Cartesian3.subtract(a,i,Q),l=n.Cartesian3.magnitude(c);if(s>=l+u)return e.clone(r),r;if(u>=l+s)return t.clone(r),r;const d=.5*(s+l+u),f=n.Cartesian3.multiplyByScalar(c,(-s+d)/l,$);return n.Cartesian3.add(f,i,f),n.Cartesian3.clone(f,r.center),r.radius=d,r};const W=new n.Cartesian3;v.expand=function(e,t,r){r=v.clone(e,r);const o=n.Cartesian3.magnitude(n.Cartesian3.subtract(t,r.center,W));return o>r.radius&&(r.radius=o),r},v.intersectPlane=function(e,t){const r=e.center,o=e.radius,i=t.normal,s=n.Cartesian3.dot(i,r)+t.distance;return s<-o?y.OUTSIDE:s<o?y.INTERSECTING:y.INSIDE},v.transform=function(e,t,n){return o.defined(n)||(n=new v),n.center=c.Matrix4.multiplyByPoint(t,e.center,n.center),n.radius=c.Matrix4.getMaximumScale(t)*e.radius,n};const H=new n.Cartesian3;v.distanceSquaredTo=function(e,t){const r=n.Cartesian3.subtract(e.center,t,H),o=n.Cartesian3.magnitude(r)-e.radius;return o<=0?0:o*o},v.transformWithoutScale=function(e,t,n){return o.defined(n)||(n=new v),n.center=c.Matrix4.multiplyByPoint(t,e.center,n.center),n.radius=e.radius,n};const Y=new n.Cartesian3;v.computePlaneDistances=function(e,t,r,i){o.defined(i)||(i=new s.Interval);const a=n.Cartesian3.subtract(e.center,t,Y),u=n.Cartesian3.dot(r,a);return i.start=u-e.radius,i.stop=u+e.radius,i};const Z=new n.Cartesian3,G=new n.Cartesian3,J=new n.Cartesian3,X=new n.Cartesian3,K=new n.Cartesian3,ee=new r.Cartographic,te=new Array(8);for(let e=0;e<8;++e)te[e]=new n.Cartesian3;const ne=new g;let re;v.projectTo2D=function(e,t,r){const i=(t=o.defaultValue(t,ne)).ellipsoid;let s=e.center;const a=e.radius;let u;u=n.Cartesian3.equals(s,n.Cartesian3.ZERO)?n.Cartesian3.clone(n.Cartesian3.UNIT_X,Z):i.geodeticSurfaceNormal(s,Z);const c=n.Cartesian3.cross(n.Cartesian3.UNIT_Z,u,G);n.Cartesian3.normalize(c,c);const l=n.Cartesian3.cross(u,c,J);n.Cartesian3.normalize(l,l),n.Cartesian3.multiplyByScalar(u,a,u),n.Cartesian3.multiplyByScalar(l,a,l),n.Cartesian3.multiplyByScalar(c,a,c);const d=n.Cartesian3.negate(l,K),f=n.Cartesian3.negate(c,X),p=te;let h=p[0];n.Cartesian3.add(u,l,h),n.Cartesian3.add(h,c,h),h=p[1],n.Cartesian3.add(u,l,h),n.Cartesian3.add(h,f,h),h=p[2],n.Cartesian3.add(u,d,h),n.Cartesian3.add(h,f,h),h=p[3],n.Cartesian3.add(u,d,h),n.Cartesian3.add(h,c,h),n.Cartesian3.negate(u,u),h=p[4],n.Cartesian3.add(u,l,h),n.Cartesian3.add(h,c,h),h=p[5],n.Cartesian3.add(u,l,h),n.Cartesian3.add(h,f,h),h=p[6],n.Cartesian3.add(u,d,h),n.Cartesian3.add(h,f,h),h=p[7],n.Cartesian3.add(u,d,h),n.Cartesian3.add(h,c,h);const m=p.length;for(let e=0;e<m;++e){const r=p[e];n.Cartesian3.add(s,r,r);const o=i.cartesianToCartographic(r,ee);t.project(o,r)}s=(r=v.fromPoints(p,r)).center;const g=s.x,y=s.y,w=s.z;return s.x=w,s.y=g,s.z=y,r},v.isOccluded=function(e,t){return!t.isBoundingSphereVisible(e)},v.equals=function(e,t){return e===t||o.defined(e)&&o.defined(t)&&n.Cartesian3.equals(e.center,t.center)&&e.radius===t.radius},v.prototype.intersectPlane=function(e){return v.intersectPlane(this,e)},v.prototype.distanceSquaredTo=function(e){return v.distanceSquaredTo(this,e)},v.prototype.computePlaneDistances=function(e,t,n){return v.computePlaneDistances(this,e,t,n)},v.prototype.isOccluded=function(e){return v.isOccluded(this,e)},v.prototype.equals=function(e){return v.equals(this,e)},v.prototype.clone=function(e){return v.clone(this,e)},v.prototype.volume=function(){const e=this.radius;return T*e*e*e};const oe={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},ie={};Object.defineProperties(ie,{element:{get:function(){if(ie.supportsFullscreen())return document[oe.fullscreenElement]}},changeEventName:{get:function(){if(ie.supportsFullscreen())return oe.fullscreenchange}},errorEventName:{get:function(){if(ie.supportsFullscreen())return oe.fullscreenerror}},enabled:{get:function(){if(ie.supportsFullscreen())return document[oe.fullscreenEnabled]}},fullscreen:{get:function(){if(ie.supportsFullscreen())return null!==ie.element}}}),ie.supportsFullscreen=function(){if(o.defined(re))return re;re=!1;const e=document.body;if("function"==typeof e.requestFullscreen)return oe.requestFullscreen="requestFullscreen",oe.exitFullscreen="exitFullscreen",oe.fullscreenEnabled="fullscreenEnabled",oe.fullscreenElement="fullscreenElement",oe.fullscreenchange="fullscreenchange",oe.fullscreenerror="fullscreenerror",re=!0,re;const t=["webkit","moz","o","ms","khtml"];let n;for(let r=0,o=t.length;r<o;++r){const o=t[r];n=`${o}RequestFullscreen`,"function"==typeof e[n]?(oe.requestFullscreen=n,re=!0):(n=`${o}RequestFullScreen`,"function"==typeof e[n]&&(oe.requestFullscreen=n,re=!0)),n=`${o}ExitFullscreen`,"function"==typeof document[n]?oe.exitFullscreen=n:(n=`${o}CancelFullScreen`,"function"==typeof document[n]&&(oe.exitFullscreen=n)),n=`${o}FullscreenEnabled`,void 0!==document[n]?oe.fullscreenEnabled=n:(n=`${o}FullScreenEnabled`,void 0!==document[n]&&(oe.fullscreenEnabled=n)),n=`${o}FullscreenElement`,void 0!==document[n]?oe.fullscreenElement=n:(n=`${o}FullScreenElement`,void 0!==document[n]&&(oe.fullscreenElement=n)),n=`${o}fullscreenchange`,void 0!==document[`on${n}`]&&("ms"===o&&(n="MSFullscreenChange"),oe.fullscreenchange=n),n=`${o}fullscreenerror`,void 0!==document[`on${n}`]&&("ms"===o&&(n="MSFullscreenError"),oe.fullscreenerror=n)}return re},ie.requestFullscreen=function(e,t){ie.supportsFullscreen()&&e[oe.requestFullscreen]({vrDisplay:t})},ie.exitFullscreen=function(){ie.supportsFullscreen()&&document[oe.exitFullscreen]()},ie._names=oe;var se=ie;let ae,ue,ce,le,de,fe,pe,he,me,ge,ye,ve,we,Ce,_e,be,xe,Se;function Ae(e){const t=e.split(".");for(let e=0,n=t.length;e<n;++e)t[e]=parseInt(t[e],10);return t}function Ee(){if(!o.defined(ue)&&(ue=!1,!Re())){const e=/ Chrome\/([\.0-9]+)/.exec(ae.userAgent);null!==e&&(ue=!0,ce=Ae(e[1]))}return ue}function Oe(){if(!o.defined(le)&&(le=!1,!Ee()&&!Re()&&/ Safari\/[\.0-9]+/.test(ae.userAgent))){const e=/ Version\/([\.0-9]+)/.exec(ae.userAgent);null!==e&&(le=!0,de=Ae(e[1]))}return le}function Ie(){if(!o.defined(fe)){fe=!1;const e=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(ae.userAgent);null!==e&&(fe=!0,pe=Ae(e[1]),pe.isNightly=!!e[2])}return fe}function Pe(){if(!o.defined(he)){let e;he=!1,"Microsoft Internet Explorer"===ae.appName?(e=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(ae.userAgent),null!==e&&(he=!0,me=Ae(e[1]))):"Netscape"===ae.appName&&(e=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(ae.userAgent),null!==e&&(he=!0,me=Ae(e[1])))}return he}function Re(){if(!o.defined(ge)){ge=!1;const e=/ Edg\/([\.0-9]+)/.exec(ae.userAgent);null!==e&&(ge=!0,ye=Ae(e[1]))}return ge}function Te(){if(!o.defined(ve)){ve=!1;const e=/Firefox\/([\.0-9]+)/.exec(ae.userAgent);null!==e&&(ve=!0,we=Ae(e[1]))}return ve}function qe(){if(!o.defined(Se)){const e=document.createElement("canvas");e.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");const t=e.style.imageRendering;Se=o.defined(t)&&""!==t,Se&&(xe=t)}return Se}function ze(){return ze._result}ae="undefined"!=typeof navigator?navigator:{},ze._promise=void 0,ze._result=void 0,ze.initialize=function(){return o.defined(ze._promise)||(ze._promise=new Promise((e=>{const t=new Image;t.onload=function(){ze._result=t.width>0&&t.height>0,e(ze._result)},t.onerror=function(){ze._result=!1,e(ze._result)},t.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA"}))),ze._promise},Object.defineProperties(ze,{initialized:{get:function(){return o.defined(ze._result)}}});const Me=[];"undefined"!=typeof ArrayBuffer&&(Me.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),"undefined"!=typeof Uint8ClampedArray&&Me.push(Uint8ClampedArray),"undefined"!=typeof Uint8ClampedArray&&Me.push(Uint8ClampedArray),"undefined"!=typeof BigInt64Array&&Me.push(BigInt64Array),"undefined"!=typeof BigUint64Array&&Me.push(BigUint64Array));const De={isChrome:Ee,chromeVersion:function(){return Ee()&&ce},isSafari:Oe,safariVersion:function(){return Oe()&&de},isWebkit:Ie,webkitVersion:function(){return Ie()&&pe},isInternetExplorer:Pe,internetExplorerVersion:function(){return Pe()&&me},isEdge:Re,edgeVersion:function(){return Re()&&ye},isFirefox:Te,firefoxVersion:function(){return Te()&&we},isWindows:function(){return o.defined(Ce)||(Ce=/Windows/i.test(ae.appVersion)),Ce},isIPadOrIOS:function(){return o.defined(_e)||(_e="iPhone"===navigator.platform||"iPod"===navigator.platform||"iPad"===navigator.platform),_e},hardwareConcurrency:o.defaultValue(ae.hardwareConcurrency,3),supportsPointerEvents:function(){return o.defined(be)||(be=!Te()&&"undefined"!=typeof PointerEvent&&(!o.defined(ae.pointerEnabled)||ae.pointerEnabled)),be},supportsImageRenderingPixelated:qe,supportsWebP:ze,imageRenderingValue:function(){return qe()?xe:void 0},typedArrayTypes:Me,supportsBasis:function(e){return De.supportsWebAssembly()&&e.context.supportsBasis},supportsFullscreen:function(){return se.supportsFullscreen()},supportsTypedArrays:function(){return"undefined"!=typeof ArrayBuffer},supportsBigInt64Array:function(){return"undefined"!=typeof BigInt64Array},supportsBigUint64Array:function(){return"undefined"!=typeof BigUint64Array},supportsBigInt:function(){return"undefined"!=typeof BigInt},supportsWebWorkers:function(){return"undefined"!=typeof Worker},supportsWebAssembly:function(){return"undefined"!=typeof WebAssembly},supportsWebgl2:function(e){return e.context.webgl2}};var Ue=De;function ke(e,t,n,r){this.x=o.defaultValue(e,0),this.y=o.defaultValue(t,0),this.z=o.defaultValue(n,0),this.w=o.defaultValue(r,0)}let Fe=new n.Cartesian3;ke.fromAxisAngle=function(e,t,r){const i=t/2,s=Math.sin(i);Fe=n.Cartesian3.normalize(e,Fe);const a=Fe.x*s,u=Fe.y*s,c=Fe.z*s,l=Math.cos(i);return o.defined(r)?(r.x=a,r.y=u,r.z=c,r.w=l,r):new ke(a,u,c,l)};const Ne=[1,2,0],je=new Array(3);ke.fromRotationMatrix=function(e,t){let n,r,i,s,a;const c=e[u.Matrix3.COLUMN0ROW0],l=e[u.Matrix3.COLUMN1ROW1],d=e[u.Matrix3.COLUMN2ROW2],f=c+l+d;if(f>0)n=Math.sqrt(f+1),a=.5*n,n=.5/n,r=(e[u.Matrix3.COLUMN1ROW2]-e[u.Matrix3.COLUMN2ROW1])*n,i=(e[u.Matrix3.COLUMN2ROW0]-e[u.Matrix3.COLUMN0ROW2])*n,s=(e[u.Matrix3.COLUMN0ROW1]-e[u.Matrix3.COLUMN1ROW0])*n;else{const t=Ne;let o=0;l>c&&(o=1),d>c&&d>l&&(o=2);const f=t[o],p=t[f];n=Math.sqrt(e[u.Matrix3.getElementIndex(o,o)]-e[u.Matrix3.getElementIndex(f,f)]-e[u.Matrix3.getElementIndex(p,p)]+1);const h=je;h[o]=.5*n,n=.5/n,a=(e[u.Matrix3.getElementIndex(p,f)]-e[u.Matrix3.getElementIndex(f,p)])*n,h[f]=(e[u.Matrix3.getElementIndex(f,o)]+e[u.Matrix3.getElementIndex(o,f)])*n,h[p]=(e[u.Matrix3.getElementIndex(p,o)]+e[u.Matrix3.getElementIndex(o,p)])*n,r=-h[0],i=-h[1],s=-h[2]}return o.defined(t)?(t.x=r,t.y=i,t.z=s,t.w=a,t):new ke(r,i,s,a)};const Be=new ke;let Ve=new ke,Le=new ke,Qe=new ke;ke.fromHeadingPitchRoll=function(e,t){return Qe=ke.fromAxisAngle(n.Cartesian3.UNIT_X,e.roll,Be),Le=ke.fromAxisAngle(n.Cartesian3.UNIT_Y,-e.pitch,t),t=ke.multiply(Le,Qe,Le),Ve=ke.fromAxisAngle(n.Cartesian3.UNIT_Z,-e.heading,Be),ke.multiply(Ve,t,t)};const $e=new n.Cartesian3,We=new n.Cartesian3,He=new ke,Ye=new ke,Ze=new ke;ke.packedLength=4,ke.pack=function(e,t,n){return n=o.defaultValue(n,0),t[n++]=e.x,t[n++]=e.y,t[n++]=e.z,t[n]=e.w,t},ke.unpack=function(e,t,n){return t=o.defaultValue(t,0),o.defined(n)||(n=new ke),n.x=e[t],n.y=e[t+1],n.z=e[t+2],n.w=e[t+3],n},ke.packedInterpolationLength=3,ke.convertPackedArrayForInterpolation=function(e,t,n,r){ke.unpack(e,4*n,Ze),ke.conjugate(Ze,Ze);for(let i=0,s=n-t+1;i<s;i++){const n=3*i;ke.unpack(e,4*(t+i),He),ke.multiply(He,Ze,He),He.w<0&&ke.negate(He,He),ke.computeAxis(He,$e);const s=ke.computeAngle(He);o.defined(r)||(r=[]),r[n]=$e.x*s,r[n+1]=$e.y*s,r[n+2]=$e.z*s}},ke.unpackInterpolationResult=function(e,t,r,i,s){o.defined(s)||(s=new ke),n.Cartesian3.fromArray(e,0,We);const a=n.Cartesian3.magnitude(We);return ke.unpack(t,4*i,Ye),0===a?ke.clone(ke.IDENTITY,He):ke.fromAxisAngle(We,a,He),ke.multiply(He,Ye,s)},ke.clone=function(e,t){if(o.defined(e))return o.defined(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new ke(e.x,e.y,e.z,e.w)},ke.conjugate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t},ke.magnitudeSquared=function(e){return e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},ke.magnitude=function(e){return Math.sqrt(ke.magnitudeSquared(e))},ke.normalize=function(e,t){const n=1/ke.magnitude(e),r=e.x*n,o=e.y*n,i=e.z*n,s=e.w*n;return t.x=r,t.y=o,t.z=i,t.w=s,t},ke.inverse=function(e,t){const n=ke.magnitudeSquared(e);return t=ke.conjugate(e,t),ke.multiplyByScalar(t,1/n,t)},ke.add=function(e,t,n){return n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n},ke.subtract=function(e,t,n){return n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n},ke.negate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t},ke.dot=function(e,t){return e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w},ke.multiply=function(e,t,n){const r=e.x,o=e.y,i=e.z,s=e.w,a=t.x,u=t.y,c=t.z,l=t.w,d=s*a+r*l+o*c-i*u,f=s*u-r*c+o*l+i*a,p=s*c+r*u-o*a+i*l,h=s*l-r*a-o*u-i*c;return n.x=d,n.y=f,n.z=p,n.w=h,n},ke.multiplyByScalar=function(e,t,n){return n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n},ke.divideByScalar=function(e,t,n){return n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n},ke.computeAxis=function(e,t){const n=e.w;if(Math.abs(n-1)<a.CesiumMath.EPSILON6)return t.x=t.y=t.z=0,t;const r=1/Math.sqrt(1-n*n);return t.x=e.x*r,t.y=e.y*r,t.z=e.z*r,t},ke.computeAngle=function(e){return Math.abs(e.w-1)<a.CesiumMath.EPSILON6?0:2*Math.acos(e.w)};let Ge=new ke;ke.lerp=function(e,t,n,r){return Ge=ke.multiplyByScalar(t,n,Ge),r=ke.multiplyByScalar(e,1-n,r),ke.add(Ge,r,r)};let Je=new ke,Xe=new ke,Ke=new ke;ke.slerp=function(e,t,n,r){let o=ke.dot(e,t),i=t;if(o<0&&(o=-o,i=Je=ke.negate(t,Je)),1-o<a.CesiumMath.EPSILON6)return ke.lerp(e,i,n,r);const s=Math.acos(o);return Xe=ke.multiplyByScalar(e,Math.sin((1-n)*s),Xe),Ke=ke.multiplyByScalar(i,Math.sin(n*s),Ke),r=ke.add(Xe,Ke,r),ke.multiplyByScalar(r,1/Math.sin(s),r)},ke.log=function(e,t){const r=a.CesiumMath.acosClamped(e.w);let o=0;return 0!==r&&(o=r/Math.sin(r)),n.Cartesian3.multiplyByScalar(e,o,t)},ke.exp=function(e,t){const r=n.Cartesian3.magnitude(e);let o=0;return 0!==r&&(o=Math.sin(r)/r),t.x=e.x*o,t.y=e.y*o,t.z=e.z*o,t.w=Math.cos(r),t};const et=new n.Cartesian3,tt=new n.Cartesian3,nt=new ke,rt=new ke;ke.computeInnerQuadrangle=function(e,t,r,o){const i=ke.conjugate(t,nt);ke.multiply(i,r,rt);const s=ke.log(rt,et);ke.multiply(i,e,rt);const a=ke.log(rt,tt);return n.Cartesian3.add(s,a,s),n.Cartesian3.multiplyByScalar(s,.25,s),n.Cartesian3.negate(s,s),ke.exp(s,nt),ke.multiply(t,nt,o)},ke.squad=function(e,t,n,r,o,i){const s=ke.slerp(e,t,o,nt),a=ke.slerp(n,r,o,rt);return ke.slerp(s,a,2*o*(1-o),i)};const ot=new ke,it=1.9011074535173003,st=Ue.supportsTypedArrays()?new Float32Array(8):[],at=Ue.supportsTypedArrays()?new Float32Array(8):[],ut=Ue.supportsTypedArrays()?new Float32Array(8):[],ct=Ue.supportsTypedArrays()?new Float32Array(8):[];for(let e=0;e<7;++e){const t=e+1,n=2*t+1;st[e]=1/(t*n),at[e]=t/n}function lt(e,t,n){let r,o,i=0,s=e.length-1;for(;i<=s;)if(r=~~((i+s)/2),o=n(e[r],t),o<0)i=r+1;else{if(!(o>0))return r;s=r-1}return~(s+1)}function dt(e,t,n,r,o){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=n,this.yPoleOffset=r,this.ut1MinusUtc=o}function ft(e,t,n,r,o,i,s,a){this.year=e,this.month=t,this.day=n,this.hour=r,this.minute=o,this.second=i,this.millisecond=s,this.isLeapSecond=a}function pt(e){return e%4==0&&e%100!=0||e%400==0}function ht(e,t){this.julianDate=e,this.offset=t}st[7]=it/136,at[7]=8*it/17,ke.fastSlerp=function(e,t,n,r){let o,i=ke.dot(e,t);i>=0?o=1:(o=-1,i=-i);const s=i-1,a=1-n,u=n*n,c=a*a;for(let e=7;e>=0;--e)ut[e]=(st[e]*u-at[e])*s,ct[e]=(st[e]*c-at[e])*s;const l=o*n*(1+ut[0]*(1+ut[1]*(1+ut[2]*(1+ut[3]*(1+ut[4]*(1+ut[5]*(1+ut[6]*(1+ut[7])))))))),d=a*(1+ct[0]*(1+ct[1]*(1+ct[2]*(1+ct[3]*(1+ct[4]*(1+ct[5]*(1+ct[6]*(1+ct[7])))))))),f=ke.multiplyByScalar(e,d,ot);return ke.multiplyByScalar(t,l,r),ke.add(f,r,r)},ke.fastSquad=function(e,t,n,r,o,i){const s=ke.fastSlerp(e,t,o,nt),a=ke.fastSlerp(n,r,o,rt);return ke.fastSlerp(s,a,2*o*(1-o),i)},ke.equals=function(e,t){return e===t||o.defined(e)&&o.defined(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w},ke.equalsEpsilon=function(e,t,n){return n=o.defaultValue(n,0),e===t||o.defined(e)&&o.defined(t)&&Math.abs(e.x-t.x)<=n&&Math.abs(e.y-t.y)<=n&&Math.abs(e.z-t.z)<=n&&Math.abs(e.w-t.w)<=n},ke.ZERO=Object.freeze(new ke(0,0,0,0)),ke.IDENTITY=Object.freeze(new ke(0,0,0,1)),ke.prototype.clone=function(e){return ke.clone(this,e)},ke.prototype.equals=function(e){return ke.equals(this,e)},ke.prototype.equalsEpsilon=function(e,t){return ke.equalsEpsilon(this,e,t)},ke.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var mt=Object.freeze({SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:2400000.5});var gt=Object.freeze({UTC:0,TAI:1});const yt=new ft,vt=[31,28,31,30,31,30,31,31,30,31,30,31];function wt(e,t){return Mt.compare(e.julianDate,t.julianDate)}const Ct=new ht;function _t(e){Ct.julianDate=e;const t=Mt.leapSeconds;let n=lt(t,Ct,wt);n<0&&(n=~n),n>=t.length&&(n=t.length-1);let r=t[n].offset;if(n>0){Mt.secondsDifference(t[n].julianDate,e)>r&&(n--,r=t[n].offset)}Mt.addSeconds(e,r,e)}function bt(e,t){Ct.julianDate=e;const n=Mt.leapSeconds;let r=lt(n,Ct,wt);if(r<0&&(r=~r),0===r)return Mt.addSeconds(e,-n[0].offset,t);if(r>=n.length)return Mt.addSeconds(e,-n[r-1].offset,t);const o=Mt.secondsDifference(n[r].julianDate,e);return 0===o?Mt.addSeconds(e,-n[r].offset,t):o<=1?void 0:Mt.addSeconds(e,-n[--r].offset,t)}function xt(e,t,n){const r=t/mt.SECONDS_PER_DAY|0;return e+=r,(t-=mt.SECONDS_PER_DAY*r)<0&&(e--,t+=mt.SECONDS_PER_DAY),n.dayNumber=e,n.secondsOfDay=t,n}function St(e,t,n,r,o,i,s){const a=(t-14)/12|0,u=e+4800+a;let c=(1461*u/4|0)+(367*(t-2-12*a)/12|0)-(3*((u+100)/100|0)/4|0)+n-32075;(r-=12)<0&&(r+=24);const l=i+(r*mt.SECONDS_PER_HOUR+o*mt.SECONDS_PER_MINUTE+s*mt.SECONDS_PER_MILLISECOND);return l>=43200&&(c-=1),[c,l]}const At=/^(\d{4})$/,Et=/^(\d{4})-(\d{2})$/,Ot=/^(\d{4})-?(\d{3})$/,It=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,Pt=/^(\d{4})-?(\d{2})-?(\d{2})$/,Rt=/([Z+\-])?(\d{2})?:?(\d{2})?$/,Tt=/^(\d{2})(\.\d+)?/.source+Rt.source,qt=/^(\d{2}):?(\d{2})(\.\d+)?/.source+Rt.source,zt=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+Rt.source;function Mt(e,t,n){this.dayNumber=void 0,this.secondsOfDay=void 0,e=o.defaultValue(e,0),t=o.defaultValue(t,0),n=o.defaultValue(n,gt.UTC);const r=0|e;xt(r,t+=(e-r)*mt.SECONDS_PER_DAY,this),n===gt.UTC&&_t(this)}Mt.fromGregorianDate=function(e,t){const n=St(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return o.defined(t)?(xt(n[0],n[1],t),_t(t),t):new Mt(n[0],n[1],gt.UTC)},Mt.fromDate=function(e,t){const n=St(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return o.defined(t)?(xt(n[0],n[1],t),_t(t),t):new Mt(n[0],n[1],gt.UTC)},Mt.fromIso8601=function(e,t){let n,r=(e=e.replace(",",".")).split("T"),i=1,s=1,a=0,u=0,c=0,l=0;const d=r[0],f=r[1];let p,h,m;if(r=d.match(Pt),null!==r)n=+r[1],i=+r[2],s=+r[3];else if(r=d.match(Et),null!==r)n=+r[1],i=+r[2];else if(r=d.match(At),null!==r)n=+r[1];else{let e;if(r=d.match(Ot),null!==r)n=+r[1],e=+r[2],h=pt(n);else if(r=d.match(It),null!==r){n=+r[1];e=7*+r[2]+(+r[3]||0)-new Date(Date.UTC(n,0,4)).getUTCDay()-3}p=new Date(Date.UTC(n,0,1)),p.setUTCDate(e),i=p.getUTCMonth()+1,s=p.getUTCDate()}if(h=pt(n),o.defined(f)){r=f.match(zt),null!==r?(a=+r[1],u=+r[2],c=+r[3],l=1e3*+(r[4]||0),m=5):(r=f.match(qt),null!==r?(a=+r[1],u=+r[2],c=60*+(r[3]||0),m=4):(r=f.match(Tt),null!==r&&(a=+r[1],u=60*+(r[2]||0),m=3)));const e=r[m],t=+r[m+1],o=+(r[m+2]||0);switch(e){case"+":a-=t,u-=o;break;case"-":a+=t,u+=o;break;case"Z":break;default:u+=new Date(Date.UTC(n,i-1,s,a,u)).getTimezoneOffset()}}const g=60===c;for(g&&c--;u>=60;)u-=60,a++;for(;a>=24;)a-=24,s++;for(p=h&&2===i?29:vt[i-1];s>p;)s-=p,i++,i>12&&(i-=12,n++),p=h&&2===i?29:vt[i-1];for(;u<0;)u+=60,a--;for(;a<0;)a+=24,s--;for(;s<1;)i--,i<1&&(i+=12,n--),p=h&&2===i?29:vt[i-1],s+=p;const y=St(n,i,s,a,u,c,l);return o.defined(t)?(xt(y[0],y[1],t),_t(t)):t=new Mt(y[0],y[1],gt.UTC),g&&Mt.addSeconds(t,1,t),t},Mt.now=function(e){return Mt.fromDate(new Date,e)};const Dt=new Mt(0,0,gt.TAI);Mt.toGregorianDate=function(e,t){let n=!1,r=bt(e,Dt);o.defined(r)||(Mt.addSeconds(e,-1,Dt),r=bt(Dt,Dt),n=!0);let i=r.dayNumber;const s=r.secondsOfDay;s>=43200&&(i+=1);let a=i+68569|0;const u=4*a/146097|0;a=a-((146097*u+3)/4|0)|0;const c=4e3*(a+1)/1461001|0;a=a-(1461*c/4|0)+31|0;const l=80*a/2447|0,d=a-(2447*l/80|0)|0;a=l/11|0;const f=l+2-12*a|0,p=100*(u-49)+c+a|0;let h=s/mt.SECONDS_PER_HOUR|0,m=s-h*mt.SECONDS_PER_HOUR;const g=m/mt.SECONDS_PER_MINUTE|0;m-=g*mt.SECONDS_PER_MINUTE;let y=0|m;const v=(m-y)/mt.SECONDS_PER_MILLISECOND;return h+=12,h>23&&(h-=24),n&&(y+=1),o.defined(t)?(t.year=p,t.month=f,t.day=d,t.hour=h,t.minute=g,t.second=y,t.millisecond=v,t.isLeapSecond=n,t):new ft(p,f,d,h,g,y,v,n)},Mt.toDate=function(e){const t=Mt.toGregorianDate(e,yt);let n=t.second;return t.isLeapSecond&&(n-=1),new Date(Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,n,t.millisecond))},Mt.toIso8601=function(e,t){const n=Mt.toGregorianDate(e,yt);let r=n.year,i=n.month,s=n.day,a=n.hour;const u=n.minute,c=n.second,l=n.millisecond;let d;return 1e4===r&&1===i&&1===s&&0===a&&0===u&&0===c&&0===l&&(r=9999,i=12,s=31,a=24),o.defined(t)||0===l?o.defined(t)&&0!==t?(d=(.01*l).toFixed(t).replace(".","").slice(0,t),`${r.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${s.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${c.toString().padStart(2,"0")}.${d}Z`):`${r.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${s.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${c.toString().padStart(2,"0")}Z`:(d=(.01*l).toString().replace(".",""),`${r.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${s.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${c.toString().padStart(2,"0")}.${d}Z`)},Mt.clone=function(e,t){if(o.defined(e))return o.defined(t)?(t.dayNumber=e.dayNumber,t.secondsOfDay=e.secondsOfDay,t):new Mt(e.dayNumber,e.secondsOfDay,gt.TAI)},Mt.compare=function(e,t){const n=e.dayNumber-t.dayNumber;return 0!==n?n:e.secondsOfDay-t.secondsOfDay},Mt.equals=function(e,t){return e===t||o.defined(e)&&o.defined(t)&&e.dayNumber===t.dayNumber&&e.secondsOfDay===t.secondsOfDay},Mt.equalsEpsilon=function(e,t,n){return n=o.defaultValue(n,0),e===t||o.defined(e)&&o.defined(t)&&Math.abs(Mt.secondsDifference(e,t))<=n},Mt.totalDays=function(e){return e.dayNumber+e.secondsOfDay/mt.SECONDS_PER_DAY},Mt.secondsDifference=function(e,t){return(e.dayNumber-t.dayNumber)*mt.SECONDS_PER_DAY+(e.secondsOfDay-t.secondsOfDay)},Mt.daysDifference=function(e,t){return e.dayNumber-t.dayNumber+(e.secondsOfDay-t.secondsOfDay)/mt.SECONDS_PER_DAY},Mt.computeTaiMinusUtc=function(e){Ct.julianDate=e;const t=Mt.leapSeconds;let n=lt(t,Ct,wt);return n<0&&(n=~n,--n,n<0&&(n=0)),t[n].offset},Mt.addSeconds=function(e,t,n){return xt(e.dayNumber,e.secondsOfDay+t,n)},Mt.addMinutes=function(e,t,n){const r=e.secondsOfDay+t*mt.SECONDS_PER_MINUTE;return xt(e.dayNumber,r,n)},Mt.addHours=function(e,t,n){const r=e.secondsOfDay+t*mt.SECONDS_PER_HOUR;return xt(e.dayNumber,r,n)},Mt.addDays=function(e,t,n){return xt(e.dayNumber+t,e.secondsOfDay,n)},Mt.lessThan=function(e,t){return Mt.compare(e,t)<0},Mt.lessThanOrEquals=function(e,t){return Mt.compare(e,t)<=0},Mt.greaterThan=function(e,t){return Mt.compare(e,t)>0},Mt.greaterThanOrEquals=function(e,t){return Mt.compare(e,t)>=0},Mt.prototype.clone=function(e){return Mt.clone(this,e)},Mt.prototype.equals=function(e){return Mt.equals(this,e)},Mt.prototype.equalsEpsilon=function(e,t){return Mt.equalsEpsilon(this,e,t)},Mt.prototype.toString=function(){return Mt.toIso8601(this)},Mt.leapSeconds=[new ht(new Mt(2441317,43210,gt.TAI),10),new ht(new Mt(2441499,43211,gt.TAI),11),new ht(new Mt(2441683,43212,gt.TAI),12),new ht(new Mt(2442048,43213,gt.TAI),13),new ht(new Mt(2442413,43214,gt.TAI),14),new ht(new Mt(2442778,43215,gt.TAI),15),new ht(new Mt(2443144,43216,gt.TAI),16),new ht(new Mt(2443509,43217,gt.TAI),17),new ht(new Mt(2443874,43218,gt.TAI),18),new ht(new Mt(2444239,43219,gt.TAI),19),new ht(new Mt(2444786,43220,gt.TAI),20),new ht(new Mt(2445151,43221,gt.TAI),21),new ht(new Mt(2445516,43222,gt.TAI),22),new ht(new Mt(2446247,43223,gt.TAI),23),new ht(new Mt(2447161,43224,gt.TAI),24),new ht(new Mt(2447892,43225,gt.TAI),25),new ht(new Mt(2448257,43226,gt.TAI),26),new ht(new Mt(2448804,43227,gt.TAI),27),new ht(new Mt(2449169,43228,gt.TAI),28),new ht(new Mt(2449534,43229,gt.TAI),29),new ht(new Mt(2450083,43230,gt.TAI),30),new ht(new Mt(2450630,43231,gt.TAI),31),new ht(new Mt(2451179,43232,gt.TAI),32),new ht(new Mt(2453736,43233,gt.TAI),33),new ht(new Mt(2454832,43234,gt.TAI),34),new ht(new Mt(2456109,43235,gt.TAI),35),new ht(new Mt(2457204,43236,gt.TAI),36),new ht(new Mt(2457754,43237,gt.TAI),37)];var Ut,kt={exports:{}},Ft={exports:{}};var Nt,jt={exports:{}};
/*!
   * URI.js - Mutating URLs
   * IPv6 Support
   *
   * Version: 1.19.11
   *
   * Author: Rodney Rehm
   * Web: http://medialize.github.io/URI.js/
   *
   * Licensed under
   *   MIT License http://www.opensource.org/licenses/mit-license
   *
   */var Bt,Vt,Lt,Qt,$t={exports:{}};
/*!
   * URI.js - Mutating URLs
   * Second Level Domain (SLD) Support
   *
   * Version: 1.19.11
   *
   * Author: Rodney Rehm
   * Web: http://medialize.github.io/URI.js/
   *
   * Licensed under
   *   MIT License http://www.opensource.org/licenses/mit-license
   *
   */
/*!
   * URI.js - Mutating URLs
   *
   * Version: 1.19.11
   *
   * Author: Rodney Rehm
   * Web: http://medialize.github.io/URI.js/
   *
   * Licensed under
   *   MIT License http://www.opensource.org/licenses/mit-license
   *
   */
Vt=kt,Lt=a.commonjsGlobal,Qt=function(e,t,n,r){var o=r&&r.URI;function i(e,t){var n=arguments.length>=1;if(!(this instanceof i))return n?arguments.length>=2?new i(e,t):new i(e):new i;if(void 0===e){if(n)throw new TypeError("undefined is not a valid argument for URI");e="undefined"!=typeof location?location.href+"":""}if(null===e&&n)throw new TypeError("null is not a valid argument for URI");return this.href(e),void 0!==t?this.absoluteTo(t):this}i.version="1.19.11";var s=i.prototype,a=Object.prototype.hasOwnProperty;function u(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function c(e){return void 0===e?"Undefined":String(Object.prototype.toString.call(e)).slice(8,-1)}function l(e){return"Array"===c(e)}function d(e,t){var n,r,o={};if("RegExp"===c(t))o=null;else if(l(t))for(n=0,r=t.length;n<r;n++)o[t[n]]=!0;else o[t]=!0;for(n=0,r=e.length;n<r;n++)(o&&void 0!==o[e[n]]||!o&&t.test(e[n]))&&(e.splice(n,1),r--,n--);return e}function f(e,t){var n,r;if(l(t)){for(n=0,r=t.length;n<r;n++)if(!f(e,t[n]))return!1;return!0}var o=c(t);for(n=0,r=e.length;n<r;n++)if("RegExp"===o){if("string"==typeof e[n]&&e[n].match(t))return!0}else if(e[n]===t)return!0;return!1}function p(e,t){if(!l(e)||!l(t))return!1;if(e.length!==t.length)return!1;e.sort(),t.sort();for(var n=0,r=e.length;n<r;n++)if(e[n]!==t[n])return!1;return!0}function h(e){return e.replace(/^\/+|\/+$/g,"")}function m(e){return escape(e)}function g(e){return encodeURIComponent(e).replace(/[!'()*]/g,m).replace(/\*/g,"%2A")}i._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:i.preventInvalidHostname,duplicateQueryParameters:i.duplicateQueryParameters,escapeQuerySpace:i.escapeQuerySpace}},i.preventInvalidHostname=!1,i.duplicateQueryParameters=!1,i.escapeQuerySpace=!0,i.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,i.idn_expression=/[^a-z0-9\._-]/i,i.punycode_expression=/(xn--)/i,i.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,i.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,i.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/gi,i.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},i.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,i.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,i.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},i.hostProtocols=["http","https"],i.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,i.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},i.getDomAttribute=function(e){if(e&&e.nodeName){var t=e.nodeName.toLowerCase();if("input"!==t||"image"===e.type)return i.domAttributes[t]}},i.encode=g,i.decode=decodeURIComponent,i.iso8859=function(){i.encode=escape,i.decode=unescape},i.unicode=function(){i.encode=g,i.decode=decodeURIComponent},i.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/gi,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/gi,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/gi,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},i.encodeQuery=function(e,t){var n=i.encode(e+"");return void 0===t&&(t=i.escapeQuerySpace),t?n.replace(/%20/g,"+"):n},i.decodeQuery=function(e,t){e+="",void 0===t&&(t=i.escapeQuerySpace);try{return i.decode(t?e.replace(/\+/g,"%20"):e)}catch(t){return e}};var y,v={encode:"encode",decode:"decode"},w=function(e,t){return function(n){try{return i[t](n+"").replace(i.characters[e][t].expression,(function(n){return i.characters[e][t].map[n]}))}catch(e){return n}}};for(y in v)i[y+"PathSegment"]=w("pathname",v[y]),i[y+"UrnPathSegment"]=w("urnpath",v[y]);var C=function(e,t,n){return function(r){var o;o=n?function(e){return i[t](i[n](e))}:i[t];for(var s=(r+"").split(e),a=0,u=s.length;a<u;a++)s[a]=o(s[a]);return s.join(e)}};function _(e){return function(t,n){return void 0===t?this._parts[e]||"":(this._parts[e]=t||null,this.build(!n),this)}}function b(e,t){return function(n,r){return void 0===n?this._parts[e]||"":(null!==n&&(n+="").charAt(0)===t&&(n=n.substring(1)),this._parts[e]=n,this.build(!r),this)}}i.decodePath=C("/","decodePathSegment"),i.decodeUrnPath=C(":","decodeUrnPathSegment"),i.recodePath=C("/","encodePathSegment","decode"),i.recodeUrnPath=C(":","encodeUrnPathSegment","decode"),i.encodeReserved=w("reserved","encode"),i.parse=function(e,t){var n;return t||(t={preventInvalidHostname:i.preventInvalidHostname}),(n=(e=(e=e.replace(i.leading_whitespace_expression,"")).replace(i.ascii_tab_whitespace,"")).indexOf("#"))>-1&&(t.fragment=e.substring(n+1)||null,e=e.substring(0,n)),(n=e.indexOf("?"))>-1&&(t.query=e.substring(n+1)||null,e=e.substring(0,n)),"//"===(e=(e=e.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://")).replace(/^[/\\]{2,}/i,"//")).substring(0,2)?(t.protocol=null,e=e.substring(2),e=i.parseAuthority(e,t)):(n=e.indexOf(":"))>-1&&(t.protocol=e.substring(0,n)||null,t.protocol&&!t.protocol.match(i.protocol_expression)?t.protocol=void 0:"//"===e.substring(n+1,n+3).replace(/\\/g,"/")?(e=e.substring(n+3),e=i.parseAuthority(e,t)):(e=e.substring(n+1),t.urn=!0)),t.path=e,t},i.parseHost=function(e,t){e||(e="");var n,r,o=(e=e.replace(/\\/g,"/")).indexOf("/");if(-1===o&&(o=e.length),"["===e.charAt(0))n=e.indexOf("]"),t.hostname=e.substring(1,n)||null,t.port=e.substring(n+2,o)||null,"/"===t.port&&(t.port=null);else{var s=e.indexOf(":"),a=e.indexOf("/"),u=e.indexOf(":",s+1);-1!==u&&(-1===a||u<a)?(t.hostname=e.substring(0,o)||null,t.port=null):(r=e.substring(0,o).split(":"),t.hostname=r[0]||null,t.port=r[1]||null)}return t.hostname&&"/"!==e.substring(o).charAt(0)&&(o++,e="/"+e),t.preventInvalidHostname&&i.ensureValidHostname(t.hostname,t.protocol),t.port&&i.ensureValidPort(t.port),e.substring(o)||"/"},i.parseAuthority=function(e,t){return e=i.parseUserinfo(e,t),i.parseHost(e,t)},i.parseUserinfo=function(e,t){var n=e;-1!==e.indexOf("\\")&&(e=e.replace(/\\/g,"/"));var r,o=e.indexOf("/"),s=e.lastIndexOf("@",o>-1?o:e.length-1);return s>-1&&(-1===o||s<o)?(r=e.substring(0,s).split(":"),t.username=r[0]?i.decode(r[0]):null,r.shift(),t.password=r[0]?i.decode(r.join(":")):null,e=n.substring(s+1)):(t.username=null,t.password=null),e},i.parseQuery=function(e,t){if(!e)return{};if(!(e=e.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,"")))return{};for(var n,r,o,s={},u=e.split("&"),c=u.length,l=0;l<c;l++)n=u[l].split("="),r=i.decodeQuery(n.shift(),t),o=n.length?i.decodeQuery(n.join("="),t):null,"__proto__"!==r&&(a.call(s,r)?("string"!=typeof s[r]&&null!==s[r]||(s[r]=[s[r]]),s[r].push(o)):s[r]=o);return s},i.build=function(e){var t="",n=!1;return e.protocol&&(t+=e.protocol+":"),e.urn||!t&&!e.hostname||(t+="//",n=!0),t+=i.buildAuthority(e)||"","string"==typeof e.path&&("/"!==e.path.charAt(0)&&n&&(t+="/"),t+=e.path),"string"==typeof e.query&&e.query&&(t+="?"+e.query),"string"==typeof e.fragment&&e.fragment&&(t+="#"+e.fragment),t},i.buildHost=function(e){var t="";return e.hostname?(i.ip6_expression.test(e.hostname)?t+="["+e.hostname+"]":t+=e.hostname,e.port&&(t+=":"+e.port),t):""},i.buildAuthority=function(e){return i.buildUserinfo(e)+i.buildHost(e)},i.buildUserinfo=function(e){var t="";return e.username&&(t+=i.encode(e.username)),e.password&&(t+=":"+i.encode(e.password)),t&&(t+="@"),t},i.buildQuery=function(e,t,n){var r,o,s,u,c="";for(o in e)if("__proto__"!==o&&a.call(e,o))if(l(e[o]))for(r={},s=0,u=e[o].length;s<u;s++)void 0!==e[o][s]&&void 0===r[e[o][s]+""]&&(c+="&"+i.buildQueryParameter(o,e[o][s],n),!0!==t&&(r[e[o][s]+""]=!0));else void 0!==e[o]&&(c+="&"+i.buildQueryParameter(o,e[o],n));return c.substring(1)},i.buildQueryParameter=function(e,t,n){return i.encodeQuery(e,n)+(null!==t?"="+i.encodeQuery(t,n):"")},i.addQuery=function(e,t,n){if("object"==typeof t)for(var r in t)a.call(t,r)&&i.addQuery(e,r,t[r]);else{if("string"!=typeof t)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");if(void 0===e[t])return void(e[t]=n);"string"==typeof e[t]&&(e[t]=[e[t]]),l(n)||(n=[n]),e[t]=(e[t]||[]).concat(n)}},i.setQuery=function(e,t,n){if("object"==typeof t)for(var r in t)a.call(t,r)&&i.setQuery(e,r,t[r]);else{if("string"!=typeof t)throw new TypeError("URI.setQuery() accepts an object, string as the name parameter");e[t]=void 0===n?null:n}},i.removeQuery=function(e,t,n){var r,o,s;if(l(t))for(r=0,o=t.length;r<o;r++)e[t[r]]=void 0;else if("RegExp"===c(t))for(s in e)t.test(s)&&(e[s]=void 0);else if("object"==typeof t)for(s in t)a.call(t,s)&&i.removeQuery(e,s,t[s]);else{if("string"!=typeof t)throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter");void 0!==n?"RegExp"===c(n)?!l(e[t])&&n.test(e[t])?e[t]=void 0:e[t]=d(e[t],n):e[t]!==String(n)||l(n)&&1!==n.length?l(e[t])&&(e[t]=d(e[t],n)):e[t]=void 0:e[t]=void 0}},i.hasQuery=function(e,t,n,r){switch(c(t)){case"String":break;case"RegExp":for(var o in e)if(a.call(e,o)&&t.test(o)&&(void 0===n||i.hasQuery(e,o,n)))return!0;return!1;case"Object":for(var s in t)if(a.call(t,s)&&!i.hasQuery(e,s,t[s]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(c(n)){case"Undefined":return t in e;case"Boolean":return n===Boolean(l(e[t])?e[t].length:e[t]);case"Function":return!!n(e[t],t,e);case"Array":return!!l(e[t])&&(r?f:p)(e[t],n);case"RegExp":return l(e[t])?!!r&&f(e[t],n):Boolean(e[t]&&e[t].match(n));case"Number":n=String(n);case"String":return l(e[t])?!!r&&f(e[t],n):e[t]===n;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},i.joinPaths=function(){for(var e=[],t=[],n=0,r=0;r<arguments.length;r++){var o=new i(arguments[r]);e.push(o);for(var s=o.segment(),a=0;a<s.length;a++)"string"==typeof s[a]&&t.push(s[a]),s[a]&&n++}if(!t.length||!n)return new i("");var u=new i("").segment(t);return""!==e[0].path()&&"/"!==e[0].path().slice(0,1)||u.path("/"+u.path()),u.normalize()},i.commonPath=function(e,t){var n,r=Math.min(e.length,t.length);for(n=0;n<r;n++)if(e.charAt(n)!==t.charAt(n)){n--;break}return n<1?e.charAt(0)===t.charAt(0)&&"/"===e.charAt(0)?"/":"":("/"===e.charAt(n)&&"/"===t.charAt(n)||(n=e.substring(0,n).lastIndexOf("/")),e.substring(0,n+1))},i.withinString=function(e,t,n){n||(n={});var r=n.start||i.findUri.start,o=n.end||i.findUri.end,s=n.trim||i.findUri.trim,a=n.parens||i.findUri.parens,u=/[a-z0-9-]=["']?$/i;for(r.lastIndex=0;;){var c=r.exec(e);if(!c)break;var l=c.index;if(n.ignoreHtml){var d=e.slice(Math.max(l-3,0),l);if(d&&u.test(d))continue}for(var f=l+e.slice(l).search(o),p=e.slice(l,f),h=-1;;){var m=a.exec(p);if(!m)break;var g=m.index+m[0].length;h=Math.max(h,g)}if(!((p=h>-1?p.slice(0,h)+p.slice(h).replace(s,""):p.replace(s,"")).length<=c[0].length||n.ignore&&n.ignore.test(p))){var y=t(p,l,f=l+p.length,e);void 0!==y?(y=String(y),e=e.slice(0,l)+y+e.slice(f),r.lastIndex=l+y.length):r.lastIndex=f}}return r.lastIndex=0,e},i.ensureValidHostname=function(t,n){var r=!!t,o=!1;if(!!n&&(o=f(i.hostProtocols,n)),o&&!r)throw new TypeError("Hostname cannot be empty, if protocol is "+n);if(t&&t.match(i.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(t).match(i.invalid_hostname_characters))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_]')}},i.ensureValidPort=function(e){if(e){var t=Number(e);if(!(/^[0-9]+$/.test(t)&&t>0&&t<65536))throw new TypeError('Port "'+e+'" is not a valid port')}},i.noConflict=function(e){if(e){var t={URI:this.noConflict()};return r.URITemplate&&"function"==typeof r.URITemplate.noConflict&&(t.URITemplate=r.URITemplate.noConflict()),r.IPv6&&"function"==typeof r.IPv6.noConflict&&(t.IPv6=r.IPv6.noConflict()),r.SecondLevelDomains&&"function"==typeof r.SecondLevelDomains.noConflict&&(t.SecondLevelDomains=r.SecondLevelDomains.noConflict()),t}return r.URI===this&&(r.URI=o),this},s.build=function(e){return!0===e?this._deferred_build=!0:(void 0===e||this._deferred_build)&&(this._string=i.build(this._parts),this._deferred_build=!1),this},s.clone=function(){return new i(this)},s.valueOf=s.toString=function(){return this.build(!1)._string},s.protocol=_("protocol"),s.username=_("username"),s.password=_("password"),s.hostname=_("hostname"),s.port=_("port"),s.query=b("query","?"),s.fragment=b("fragment","#"),s.search=function(e,t){var n=this.query(e,t);return"string"==typeof n&&n.length?"?"+n:n},s.hash=function(e,t){var n=this.fragment(e,t);return"string"==typeof n&&n.length?"#"+n:n},s.pathname=function(e,t){if(void 0===e||!0===e){var n=this._parts.path||(this._parts.hostname?"/":"");return e?(this._parts.urn?i.decodeUrnPath:i.decodePath)(n):n}return this._parts.urn?this._parts.path=e?i.recodeUrnPath(e):"":this._parts.path=e?i.recodePath(e):"/",this.build(!t),this},s.path=s.pathname,s.href=function(e,t){var n;if(void 0===e)return this.toString();this._string="",this._parts=i._parts();var r=e instanceof i,o="object"==typeof e&&(e.hostname||e.path||e.pathname);if(e.nodeName&&(e=e[i.getDomAttribute(e)]||"",o=!1),!r&&o&&void 0!==e.pathname&&(e=e.toString()),"string"==typeof e||e instanceof String)this._parts=i.parse(String(e),this._parts);else{if(!r&&!o)throw new TypeError("invalid input");var s=r?e._parts:e;for(n in s)"query"!==n&&a.call(this._parts,n)&&(this._parts[n]=s[n]);s.query&&this.query(s.query,!1)}return this.build(!t),this},s.is=function(e){var t=!1,r=!1,o=!1,s=!1,a=!1,u=!1,c=!1,l=!this._parts.urn;switch(this._parts.hostname&&(l=!1,r=i.ip4_expression.test(this._parts.hostname),o=i.ip6_expression.test(this._parts.hostname),a=(s=!(t=r||o))&&n&&n.has(this._parts.hostname),u=s&&i.idn_expression.test(this._parts.hostname),c=s&&i.punycode_expression.test(this._parts.hostname)),e.toLowerCase()){case"relative":return l;case"absolute":return!l;case"domain":case"name":return s;case"sld":return a;case"ip":return t;case"ip4":case"ipv4":case"inet4":return r;case"ip6":case"ipv6":case"inet6":return o;case"idn":return u;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return c}return null};var x=s.protocol,S=s.port,A=s.hostname;s.protocol=function(e,t){if(e&&!(e=e.replace(/:(\/\/)?$/,"")).match(i.protocol_expression))throw new TypeError('Protocol "'+e+"\" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]");return x.call(this,e,t)},s.scheme=s.protocol,s.port=function(e,t){return this._parts.urn?void 0===e?"":this:(void 0!==e&&(0===e&&(e=null),e&&(":"===(e+="").charAt(0)&&(e=e.substring(1)),i.ensureValidPort(e))),S.call(this,e,t))},s.hostname=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0!==e){var n={preventInvalidHostname:this._parts.preventInvalidHostname};if("/"!==i.parseHost(e,n))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');e=n.hostname,this._parts.preventInvalidHostname&&i.ensureValidHostname(e,this._parts.protocol)}return A.call(this,e,t)},s.origin=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var n=this.protocol();return this.authority()?(n?n+"://":"")+this.authority():""}var r=i(e);return this.protocol(r.protocol()).authority(r.authority()).build(!t),this},s.host=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?i.buildHost(this._parts):"";if("/"!==i.parseHost(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},s.authority=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?i.buildAuthority(this._parts):"";if("/"!==i.parseAuthority(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},s.userinfo=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var n=i.buildUserinfo(this._parts);return n?n.substring(0,n.length-1):n}return"@"!==e[e.length-1]&&(e+="@"),i.parseUserinfo(e,this._parts),this.build(!t),this},s.resource=function(e,t){var n;return void 0===e?this.path()+this.search()+this.hash():(n=i.parse(e),this._parts.path=n.path,this._parts.query=n.query,this._parts.fragment=n.fragment,this.build(!t),this)},s.subdomain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var n=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,n)||""}var r=this._parts.hostname.length-this.domain().length,o=this._parts.hostname.substring(0,r),s=new RegExp("^"+u(o));if(e&&"."!==e.charAt(e.length-1)&&(e+="."),-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");return e&&i.ensureValidHostname(e,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(s,e),this.build(!t),this},s.domain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var n=this._parts.hostname.match(/\./g);if(n&&n.length<2)return this._parts.hostname;var r=this._parts.hostname.length-this.tld(t).length-1;return r=this._parts.hostname.lastIndexOf(".",r-1)+1,this._parts.hostname.substring(r)||""}if(!e)throw new TypeError("cannot set domain empty");if(-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");if(i.ensureValidHostname(e,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=e;else{var o=new RegExp(u(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(o,e)}return this.build(!t),this},s.tld=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.lastIndexOf("."),o=this._parts.hostname.substring(r+1);return!0!==t&&n&&n.list[o.toLowerCase()]&&n.get(this._parts.hostname)||o}var i;if(!e)throw new TypeError("cannot set TLD empty");if(e.match(/[^a-zA-Z0-9-]/)){if(!n||!n.is(e))throw new TypeError('TLD "'+e+'" contains characters other than [A-Z0-9]');i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,e)}else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,e)}return this.build(!t),this},s.directory=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path&&!this._parts.hostname)return"";if("/"===this._parts.path)return"/";var n=this._parts.path.length-this.filename().length-1,r=this._parts.path.substring(0,n)||(this._parts.hostname?"/":"");return e?i.decodePath(r):r}var o=this._parts.path.length-this.filename().length,s=this._parts.path.substring(0,o),a=new RegExp("^"+u(s));return this.is("relative")||(e||(e="/"),"/"!==e.charAt(0)&&(e="/"+e)),e&&"/"!==e.charAt(e.length-1)&&(e+="/"),e=i.recodePath(e),this._parts.path=this._parts.path.replace(a,e),this.build(!t),this},s.filename=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("string"!=typeof e){if(!this._parts.path||"/"===this._parts.path)return"";var n=this._parts.path.lastIndexOf("/"),r=this._parts.path.substring(n+1);return e?i.decodePathSegment(r):r}var o=!1;"/"===e.charAt(0)&&(e=e.substring(1)),e.match(/\.?\//)&&(o=!0);var s=new RegExp(u(this.filename())+"$");return e=i.recodePath(e),this._parts.path=this._parts.path.replace(s,e),o?this.normalizePath(t):this.build(!t),this},s.suffix=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path||"/"===this._parts.path)return"";var n,r,o=this.filename(),s=o.lastIndexOf(".");return-1===s?"":(n=o.substring(s+1),r=/^[a-z0-9%]+$/i.test(n)?n:"",e?i.decodePathSegment(r):r)}"."===e.charAt(0)&&(e=e.substring(1));var a,c=this.suffix();if(c)a=e?new RegExp(u(c)+"$"):new RegExp(u("."+c)+"$");else{if(!e)return this;this._parts.path+="."+i.recodePath(e)}return a&&(e=i.recodePath(e),this._parts.path=this._parts.path.replace(a,e)),this.build(!t),this},s.segment=function(e,t,n){var r=this._parts.urn?":":"/",o=this.path(),i="/"===o.substring(0,1),s=o.split(r);if(void 0!==e&&"number"!=typeof e&&(n=t,t=e,e=void 0),void 0!==e&&"number"!=typeof e)throw new Error('Bad segment "'+e+'", must be 0-based integer');if(i&&s.shift(),e<0&&(e=Math.max(s.length+e,0)),void 0===t)return void 0===e?s:s[e];if(null===e||void 0===s[e])if(l(t)){s=[];for(var a=0,u=t.length;a<u;a++)(t[a].length||s.length&&s[s.length-1].length)&&(s.length&&!s[s.length-1].length&&s.pop(),s.push(h(t[a])))}else(t||"string"==typeof t)&&(t=h(t),""===s[s.length-1]?s[s.length-1]=t:s.push(t));else t?s[e]=h(t):s.splice(e,1);return i&&s.unshift(""),this.path(s.join(r),n)},s.segmentCoded=function(e,t,n){var r,o,s;if("number"!=typeof e&&(n=t,t=e,e=void 0),void 0===t){if(l(r=this.segment(e,t,n)))for(o=0,s=r.length;o<s;o++)r[o]=i.decode(r[o]);else r=void 0!==r?i.decode(r):void 0;return r}if(l(t))for(o=0,s=t.length;o<s;o++)t[o]=i.encode(t[o]);else t="string"==typeof t||t instanceof String?i.encode(t):t;return this.segment(e,t,n)};var E=s.query;return s.query=function(e,t){if(!0===e)return i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("function"==typeof e){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace),r=e.call(this,n);return this._parts.query=i.buildQuery(r||n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this}return void 0!==e&&"string"!=typeof e?(this._parts.query=i.buildQuery(e,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this):E.call(this,e,t)},s.setQuery=function(e,t,n){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("string"==typeof e||e instanceof String)r[e]=void 0!==t?t:null;else{if("object"!=typeof e)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");for(var o in e)a.call(e,o)&&(r[o]=e[o])}return this._parts.query=i.buildQuery(r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(n=t),this.build(!n),this},s.addQuery=function(e,t,n){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.addQuery(r,e,void 0===t?null:t),this._parts.query=i.buildQuery(r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(n=t),this.build(!n),this},s.removeQuery=function(e,t,n){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.removeQuery(r,e,t),this._parts.query=i.buildQuery(r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(n=t),this.build(!n),this},s.hasQuery=function(e,t,n){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.hasQuery(r,e,t,n)},s.setSearch=s.setQuery,s.addSearch=s.addQuery,s.removeSearch=s.removeQuery,s.hasSearch=s.hasQuery,s.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},s.normalizeProtocol=function(e){return"string"==typeof this._parts.protocol&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!e)),this},s.normalizeHostname=function(n){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&t&&(this._parts.hostname=t.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!n)),this},s.normalizePort=function(e){return"string"==typeof this._parts.protocol&&this._parts.port===i.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!e)),this},s.normalizePath=function(e){var t,n=this._parts.path;if(!n)return this;if(this._parts.urn)return this._parts.path=i.recodeUrnPath(this._parts.path),this.build(!e),this;if("/"===this._parts.path)return this;var r,o,s="";for("/"!==(n=i.recodePath(n)).charAt(0)&&(t=!0,n="/"+n),"/.."!==n.slice(-3)&&"/."!==n.slice(-2)||(n+="/"),n=n.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),t&&(s=n.substring(1).match(/^(\.\.\/)+/)||"")&&(s=s[0]);-1!==(r=n.search(/\/\.\.(\/|$)/));)0!==r?(-1===(o=n.substring(0,r).lastIndexOf("/"))&&(o=r),n=n.substring(0,o)+n.substring(r+3)):n=n.substring(3);return t&&this.is("relative")&&(n=s+n.substring(1)),this._parts.path=n,this.build(!e),this},s.normalizePathname=s.normalizePath,s.normalizeQuery=function(e){return"string"==typeof this._parts.query&&(this._parts.query.length?this.query(i.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!e)),this},s.normalizeFragment=function(e){return this._parts.fragment||(this._parts.fragment=null,this.build(!e)),this},s.normalizeSearch=s.normalizeQuery,s.normalizeHash=s.normalizeFragment,s.iso8859=function(){var e=i.encode,t=i.decode;i.encode=escape,i.decode=decodeURIComponent;try{this.normalize()}finally{i.encode=e,i.decode=t}return this},s.unicode=function(){var e=i.encode,t=i.decode;i.encode=g,i.decode=unescape;try{this.normalize()}finally{i.encode=e,i.decode=t}return this},s.readable=function(){var t=this.clone();t.username("").password("").normalize();var n="";if(t._parts.protocol&&(n+=t._parts.protocol+"://"),t._parts.hostname&&(t.is("punycode")&&e?(n+=e.toUnicode(t._parts.hostname),t._parts.port&&(n+=":"+t._parts.port)):n+=t.host()),t._parts.hostname&&t._parts.path&&"/"!==t._parts.path.charAt(0)&&(n+="/"),n+=t.path(!0),t._parts.query){for(var r="",o=0,s=t._parts.query.split("&"),a=s.length;o<a;o++){var u=(s[o]||"").split("=");r+="&"+i.decodeQuery(u[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),void 0!==u[1]&&(r+="="+i.decodeQuery(u[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}n+="?"+r.substring(1)}return n+=i.decodeQuery(t.hash(),!0)},s.absoluteTo=function(e){var t,n,r,o=this.clone(),s=["protocol","username","password","hostname","port"];if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e instanceof i||(e=new i(e)),o._parts.protocol)return o;if(o._parts.protocol=e._parts.protocol,this._parts.hostname)return o;for(n=0;r=s[n];n++)o._parts[r]=e._parts[r];return o._parts.path?(".."===o._parts.path.substring(-2)&&(o._parts.path+="/"),"/"!==o.path().charAt(0)&&(t=(t=e.directory())||(0===e.path().indexOf("/")?"/":""),o._parts.path=(t?t+"/":"")+o._parts.path,o.normalizePath())):(o._parts.path=e._parts.path,o._parts.query||(o._parts.query=e._parts.query)),o.build(),o},s.relativeTo=function(e){var t,n,r,o,s,a=this.clone().normalize();if(a._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e=new i(e).normalize(),t=a._parts,n=e._parts,o=a.path(),s=e.path(),"/"!==o.charAt(0))throw new Error("URI is already relative");if("/"!==s.charAt(0))throw new Error("Cannot calculate a URI relative to another relative URI");if(t.protocol===n.protocol&&(t.protocol=null),t.username!==n.username||t.password!==n.password)return a.build();if(null!==t.protocol||null!==t.username||null!==t.password)return a.build();if(t.hostname!==n.hostname||t.port!==n.port)return a.build();if(t.hostname=null,t.port=null,o===s)return t.path="",a.build();if(!(r=i.commonPath(o,s)))return a.build();var u=n.path.substring(r.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return t.path=u+t.path.substring(r.length)||"./",a.build()},s.equals=function(e){var t,n,r,o,s,u=this.clone(),c=new i(e),d={};if(u.normalize(),c.normalize(),u.toString()===c.toString())return!0;if(r=u.query(),o=c.query(),u.query(""),c.query(""),u.toString()!==c.toString())return!1;if(r.length!==o.length)return!1;for(s in t=i.parseQuery(r,this._parts.escapeQuerySpace),n=i.parseQuery(o,this._parts.escapeQuerySpace),t)if(a.call(t,s)){if(l(t[s])){if(!p(t[s],n[s]))return!1}else if(t[s]!==n[s])return!1;d[s]=!0}for(s in n)if(a.call(n,s)&&!d[s])return!1;return!0},s.preventInvalidHostname=function(e){return this._parts.preventInvalidHostname=!!e,this},s.duplicateQueryParameters=function(e){return this._parts.duplicateQueryParameters=!!e,this},s.escapeQuerySpace=function(e){return this._parts.escapeQuerySpace=!!e,this},i},Vt.exports?Vt.exports=Qt((Ut||(Ut=1,function(e,t){!function(n){var r=t&&!t.nodeType&&t,o=e&&!e.nodeType&&e,i="object"==typeof a.commonjsGlobal&&a.commonjsGlobal;i.global!==i&&i.window!==i&&i.self!==i||(n=i);var s,u,c=2147483647,l=36,d=1,f=26,p=38,h=700,m=72,g=128,y="-",v=/^xn--/,w=/[^\x20-\x7E]/,C=/[\x2E\u3002\uFF0E\uFF61]/g,_={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},b=l-d,x=Math.floor,S=String.fromCharCode;function A(e){throw new RangeError(_[e])}function E(e,t){for(var n=e.length,r=[];n--;)r[n]=t(e[n]);return r}function O(e,t){var n=e.split("@"),r="";return n.length>1&&(r=n[0]+"@",e=n[1]),r+E((e=e.replace(C,".")).split("."),t).join(".")}function I(e){for(var t,n,r=[],o=0,i=e.length;o<i;)(t=e.charCodeAt(o++))>=55296&&t<=56319&&o<i?56320==(64512&(n=e.charCodeAt(o++)))?r.push(((1023&t)<<10)+(1023&n)+65536):(r.push(t),o--):r.push(t);return r}function P(e){return E(e,(function(e){var t="";return e>65535&&(t+=S((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+S(e)})).join("")}function R(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function T(e,t,n){var r=0;for(e=n?x(e/h):e>>1,e+=x(e/t);e>b*f>>1;r+=l)e=x(e/b);return x(r+(b+1)*e/(e+p))}function q(e){var t,n,r,o,i,s,a,u,p,h,v,w=[],C=e.length,_=0,b=g,S=m;for((n=e.lastIndexOf(y))<0&&(n=0),r=0;r<n;++r)e.charCodeAt(r)>=128&&A("not-basic"),w.push(e.charCodeAt(r));for(o=n>0?n+1:0;o<C;){for(i=_,s=1,a=l;o>=C&&A("invalid-input"),((u=(v=e.charCodeAt(o++))-48<10?v-22:v-65<26?v-65:v-97<26?v-97:l)>=l||u>x((c-_)/s))&&A("overflow"),_+=u*s,!(u<(p=a<=S?d:a>=S+f?f:a-S));a+=l)s>x(c/(h=l-p))&&A("overflow"),s*=h;S=T(_-i,t=w.length+1,0==i),x(_/t)>c-b&&A("overflow"),b+=x(_/t),_%=t,w.splice(_++,0,b)}return P(w)}function z(e){var t,n,r,o,i,s,a,u,p,h,v,w,C,_,b,E=[];for(w=(e=I(e)).length,t=g,n=0,i=m,s=0;s<w;++s)(v=e[s])<128&&E.push(S(v));for(r=o=E.length,o&&E.push(y);r<w;){for(a=c,s=0;s<w;++s)(v=e[s])>=t&&v<a&&(a=v);for(a-t>x((c-n)/(C=r+1))&&A("overflow"),n+=(a-t)*C,t=a,s=0;s<w;++s)if((v=e[s])<t&&++n>c&&A("overflow"),v==t){for(u=n,p=l;!(u<(h=p<=i?d:p>=i+f?f:p-i));p+=l)b=u-h,_=l-h,E.push(S(R(h+b%_,0))),u=x(b/_);E.push(S(R(u,0))),i=T(n,C,r==o),n=0,++r}++n,++t}return E.join("")}if(s={version:"1.3.2",ucs2:{decode:I,encode:P},decode:q,encode:z,toASCII:function(e){return O(e,(function(e){return w.test(e)?"xn--"+z(e):e}))},toUnicode:function(e){return O(e,(function(e){return v.test(e)?q(e.slice(4).toLowerCase()):e}))}},r&&o)if(e.exports==r)o.exports=s;else for(u in s)s.hasOwnProperty(u)&&(r[u]=s[u]);else n.punycode=s}(a.commonjsGlobal)}(Ft,Ft.exports)),Ft.exports),function(){return Nt||(Nt=1,e=jt,t=a.commonjsGlobal,n=function(e){var t=e&&e.IPv6;return{best:function(e){var t,n,r=e.toLowerCase().split(":"),o=r.length,i=8;for(""===r[0]&&""===r[1]&&""===r[2]?(r.shift(),r.shift()):""===r[0]&&""===r[1]?r.shift():""===r[o-1]&&""===r[o-2]&&r.pop(),-1!==r[(o=r.length)-1].indexOf(".")&&(i=7),t=0;t<o&&""!==r[t];t++);if(t<i)for(r.splice(t,1,"0000");r.length<i;)r.splice(t,0,"0000");for(var s=0;s<i;s++){n=r[s].split("");for(var a=0;a<3&&"0"===n[0]&&n.length>1;a++)n.splice(0,1);r[s]=n.join("")}var u=-1,c=0,l=0,d=-1,f=!1;for(s=0;s<i;s++)f?"0"===r[s]?l+=1:(f=!1,l>c&&(u=d,c=l)):"0"===r[s]&&(f=!0,d=s,l=1);l>c&&(u=d,c=l),c>1&&r.splice(u,c,""),o=r.length;var p="";for(""===r[0]&&(p=":"),s=0;s<o&&(p+=r[s],s!==o-1);s++)p+=":";return""===r[o-1]&&(p+=":"),p},noConflict:function(){return e.IPv6===this&&(e.IPv6=t),this}}},e.exports?e.exports=n():t.IPv6=n(t)),jt.exports;var e,t,n}(),function(){return Bt||(Bt=1,e=$t,t=a.commonjsGlobal,n=function(e){var t=e&&e.SecondLevelDomains,n={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return!1;var r=e.lastIndexOf(".",t-1);if(r<=0||r>=t-1)return!1;var o=n.list[e.slice(t+1)];return!!o&&o.indexOf(" "+e.slice(r+1,t)+" ")>=0},is:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return!1;if(e.lastIndexOf(".",t-1)>=0)return!1;var r=n.list[e.slice(t+1)];return!!r&&r.indexOf(" "+e.slice(0,t)+" ")>=0},get:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return null;var r=e.lastIndexOf(".",t-1);if(r<=0||r>=t-1)return null;var o=n.list[e.slice(t+1)];return o?o.indexOf(" "+e.slice(r+1,t)+" ")<0?null:e.slice(r+1):null},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=t),this}};return n},e.exports?e.exports=n():t.SecondLevelDomains=n(t)),$t.exports;var e,t,n}()):Lt.URI=Qt(Lt.punycode,Lt.IPv6,Lt.SecondLevelDomains,Lt);var Wt=kt.exports,Ht=a.getDefaultExportFromCjs(Wt);function Yt(e,t){if(null===e||"object"!=typeof e)return e;t=o.defaultValue(t,!1);const n=new e.constructor;for(const r in e)if(e.hasOwnProperty(r)){let o=e[r];t&&(o=Yt(o,t)),n[r]=o}return n}function Zt(){let e,t;const n=new Promise((function(n,r){e=n,t=r}));return{resolve:e,reject:t,promise:n}}function Gt(e,t){let n;return"undefined"!=typeof document&&(n=document),Gt._implementation(e,t,n)}Gt._implementation=function(e,t,n){if(!o.defined(t)){if(void 0===n)return e;t=o.defaultValue(n.baseURI,n.location.href)}const r=new Ht(e);return""!==r.scheme()?r.toString():r.absoluteTo(t).toString()};const Jt={};function Xt(e,t,n){o.defined(t)||(t=e.width),o.defined(n)||(n=e.height);let r=Jt[t];o.defined(r)||(r={},Jt[t]=r);let i=r[n];if(!o.defined(i)){const e=document.createElement("canvas");e.width=t,e.height=n,i=e.getContext("2d",{willReadFrequently:!0}),i.globalCompositeOperation="copy",r[n]=i}return i.drawImage(e,0,0,t,n),i.getImageData(0,0,t,n).data}const Kt=/^blob:/i;function en(e){return Kt.test(e)}let tn;const nn=/^data:/i;function rn(e){return nn.test(e)}var on=Object.freeze({UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5});function sn(e){e=o.defaultValue(e,o.defaultValue.EMPTY_OBJECT);const t=o.defaultValue(e.throttleByServer,!1),n=o.defaultValue(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=o.defaultValue(e.priority,0),this.throttle=n,this.throttleByServer=t,this.type=o.defaultValue(e.type,p.RequestType.OTHER),this.serverKey=e.serverKey,this.state=on.UNISSUED,this.deferred=void 0,this.cancelled=!1}function an(e,t,n){this.statusCode=e,this.response=t,this.responseHeaders=n,"string"==typeof this.responseHeaders&&(this.responseHeaders=function(e){const t={};if(!e)return t;const n=e.split("\r\n");for(let e=0;e<n.length;++e){const r=n[e],o=r.indexOf(": ");if(o>0){const e=r.substring(0,o),n=r.substring(o+2);t[e]=n}}return t}(this.responseHeaders))}function un(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}function cn(e,t){return t-e}function ln(e){this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}function dn(e,t,n){const r=e[t];e[t]=e[n],e[n]=r}sn.prototype.cancel=function(){this.cancelled=!0},sn.prototype.clone=function(e){return o.defined(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=on.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new sn(this)},an.prototype.toString=function(){let e="Request has failed.";return o.defined(this.statusCode)&&(e+=` Status Code: ${this.statusCode}`),e},Object.defineProperties(un.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}}),un.prototype.addEventListener=function(e,t){this._listeners.push(e),this._scopes.push(t);const n=this;return function(){n.removeEventListener(e,t)}},un.prototype.removeEventListener=function(e,t){const n=this._listeners,r=this._scopes;let o=-1;for(let i=0;i<n.length;i++)if(n[i]===e&&r[i]===t){o=i;break}return-1!==o&&(this._insideRaiseEvent?(this._toRemove.push(o),n[o]=void 0,r[o]=void 0):(n.splice(o,1),r.splice(o,1)),!0)},un.prototype.raiseEvent=function(){let e;this._insideRaiseEvent=!0;const t=this._listeners,n=this._scopes;let r=t.length;for(e=0;e<r;e++){const r=t[e];o.defined(r)&&t[e].apply(n[e],arguments)}const i=this._toRemove;if(r=i.length,r>0){for(i.sort(cn),e=0;e<r;e++){const r=i[e];t.splice(r,1),n.splice(r,1)}i.length=0}this._insideRaiseEvent=!1},Object.defineProperties(ln.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){const t=this._length;if(e<t){const n=this._array;for(let r=e;r<t;++r)n[r]=void 0;this._length=e,n.length=e}this._maximumLength=e}},comparator:{get:function(){return this._comparator}}}),ln.prototype.reserve=function(e){e=o.defaultValue(e,this._length),this._array.length=e},ln.prototype.heapify=function(e){e=o.defaultValue(e,0);const t=this._length,n=this._comparator,r=this._array;let i=-1,s=!0;for(;s;){const o=2*(e+1),a=o-1;i=a<t&&n(r[a],r[e])<0?a:e,o<t&&n(r[o],r[i])<0&&(i=o),i!==e?(dn(r,i,e),e=i):s=!1}},ln.prototype.resort=function(){const e=this._length;for(let t=Math.ceil(e/2);t>=0;--t)this.heapify(t)},ln.prototype.insert=function(e){const t=this._array,n=this._comparator,r=this._maximumLength;let i,s=this._length++;for(s<t.length?t[s]=e:t.push(e);0!==s;){const e=Math.floor((s-1)/2);if(!(n(t[s],t[e])<0))break;dn(t,s,e),s=e}return o.defined(r)&&this._length>r&&(i=t[r],this._length=r),i},ln.prototype.pop=function(e){if(e=o.defaultValue(e,0),0===this._length)return;const t=this._array,n=t[e];return dn(t,e,--this._length),this.heapify(e),t[this._length]=void 0,n};const fn={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0};let pn=20;const hn=new ln({comparator:function(e,t){return e.priority-t.priority}});hn.maximumLength=pn,hn.reserve(pn);const mn=[];let gn={};const yn="undefined"!=typeof document?new Ht(document.location.href):new Ht,vn=new un;function wn(){}function Cn(e){o.defined(e.priorityFunction)&&(e.priority=e.priorityFunction())}function _n(e){return e.state===on.UNISSUED&&(e.state=on.ISSUED,e.deferred=Zt()),e.deferred.promise}function bn(e){const t=_n(e);return e.state=on.ACTIVE,mn.push(e),++fn.numberOfActiveRequests,++fn.numberOfActiveRequestsEver,++gn[e.serverKey],e.requestFunction().then(function(e){return function(t){if(e.state===on.CANCELLED)return;const n=e.deferred;--fn.numberOfActiveRequests,--gn[e.serverKey],vn.raiseEvent(),e.state=on.RECEIVED,e.deferred=void 0,n.resolve(t)}}(e)).catch(function(e){return function(t){e.state!==on.CANCELLED&&(++fn.numberOfFailedRequests,--fn.numberOfActiveRequests,--gn[e.serverKey],vn.raiseEvent(t),e.state=on.FAILED,e.deferred.reject(t))}}(e)),t}function xn(e){const t=e.state===on.ACTIVE;if(e.state=on.CANCELLED,++fn.numberOfCancelledRequests,o.defined(e.deferred)){const t=e.deferred;e.deferred=void 0,t.reject()}t&&(--fn.numberOfActiveRequests,--gn[e.serverKey],++fn.numberOfCancelledActiveRequests),o.defined(e.cancelFunction)&&e.cancelFunction()}wn.maximumRequests=250,wn.maximumRequestsPerServer=30,wn.requestsByServer={"api.cesium.com:443":18,"assets.ion.cesium.com:443":18,"ibasemaps-api.arcgis.com:443":18,"tile.googleapis.com:443":18},wn.throttleRequests=!0,wn.debugShowStatistics=!1,wn.requestCompletedEvent=vn,Object.defineProperties(wn,{statistics:{get:function(){return fn}},priorityHeapLength:{get:function(){return pn},set:function(e){if(e<pn)for(;hn.length>e;){xn(hn.pop())}pn=e,hn.maximumLength=e,hn.reserve(e)}}}),wn.serverHasOpenSlots=function(e,t){t=o.defaultValue(t,1);const n=o.defaultValue(wn.requestsByServer[e],wn.maximumRequestsPerServer);return gn[e]+t<=n},wn.heapHasOpenSlots=function(e){return hn.length+e<=pn},wn.update=function(){let e,t,n=0;const r=mn.length;for(e=0;e<r;++e)t=mn[e],t.cancelled&&xn(t),t.state===on.ACTIVE?n>0&&(mn[e-n]=t):++n;mn.length-=n;const o=hn.internalArray,i=hn.length;for(e=0;e<i;++e)Cn(o[e]);hn.resort();const s=Math.max(wn.maximumRequests-mn.length,0);let a=0;for(;a<s&&hn.length>0;)t=hn.pop(),t.cancelled?xn(t):!t.throttleByServer||wn.serverHasOpenSlots(t.serverKey)?(bn(t),++a):xn(t);!function(){if(!wn.debugShowStatistics)return;0===fn.numberOfActiveRequests&&fn.lastNumberOfActiveRequests>0&&(fn.numberOfAttemptedRequests>0&&(console.log(`Number of attempted requests: ${fn.numberOfAttemptedRequests}`),fn.numberOfAttemptedRequests=0),fn.numberOfCancelledRequests>0&&(console.log(`Number of cancelled requests: ${fn.numberOfCancelledRequests}`),fn.numberOfCancelledRequests=0),fn.numberOfCancelledActiveRequests>0&&(console.log(`Number of cancelled active requests: ${fn.numberOfCancelledActiveRequests}`),fn.numberOfCancelledActiveRequests=0),fn.numberOfFailedRequests>0&&(console.log(`Number of failed requests: ${fn.numberOfFailedRequests}`),fn.numberOfFailedRequests=0));fn.lastNumberOfActiveRequests=fn.numberOfActiveRequests}()},wn.getServerKey=function(e){let t=new Ht(e);""===t.scheme()&&(t=t.absoluteTo(yn),t.normalize());let n=t.authority();/:/.test(n)||(n=`${n}:${"https"===t.scheme()?"443":"80"}`);const r=gn[n];return o.defined(r)||(gn[n]=0),n},wn.request=function(e){if(rn(e.url)||en(e.url))return vn.raiseEvent(),e.state=on.RECEIVED,e.requestFunction();if(++fn.numberOfAttemptedRequests,o.defined(e.serverKey)||(e.serverKey=wn.getServerKey(e.url)),wn.throttleRequests&&e.throttleByServer&&!wn.serverHasOpenSlots(e.serverKey))return;if(!wn.throttleRequests||!e.throttle)return bn(e);if(mn.length>=wn.maximumRequests)return;Cn(e);const t=hn.insert(e);if(o.defined(t)){if(t===e)return;xn(t)}return _n(e)},wn.clearForSpecs=function(){for(;hn.length>0;){xn(hn.pop())}const e=mn.length;for(let t=0;t<e;++t)xn(mn[t]);mn.length=0,gn={},fn.numberOfAttemptedRequests=0,fn.numberOfActiveRequests=0,fn.numberOfCancelledRequests=0,fn.numberOfCancelledActiveRequests=0,fn.numberOfFailedRequests=0,fn.numberOfActiveRequestsEver=0,fn.lastNumberOfActiveRequests=0},wn.numberOfActiveRequestsByServer=function(e){return gn[e]},wn.requestHeap=hn;const Sn={};let An={};Sn.add=function(e,t){const n=`${e.toLowerCase()}:${t}`;o.defined(An[n])||(An[n]=!0)},Sn.remove=function(e,t){const n=`${e.toLowerCase()}:${t}`;o.defined(An[n])&&delete An[n]},Sn.contains=function(e){const t=function(e){const t=new Ht(e);t.normalize();let n=t.authority();if(0!==n.length){if(t.authority(n),-1!==n.indexOf("@")){const e=n.split("@");n=e[1]}if(-1===n.indexOf(":")){let e=t.scheme();if(0===e.length&&(e=window.location.protocol,e=e.substring(0,e.length-1)),"http"===e)n+=":80";else{if("https"!==e)return;n+=":443"}}return n}}(e);return!(!o.defined(t)||!o.defined(An[t]))},Sn.clear=function(){An={}};var En=Sn;const On=function(){try{const e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob","blob"===e.responseType}catch(e){return!1}}();function In(e){"string"==typeof(e=o.defaultValue(e,o.defaultValue.EMPTY_OBJECT))&&(e={url:e}),this._url=void 0,this._templateValues=Pn(e.templateValues,{}),this._queryParameters=Pn(e.queryParameters,{}),this.headers=Pn(e.headers,{}),this.request=o.defaultValue(e.request,new sn),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=o.defaultValue(e.retryAttempts,0),this._retryCount=0;o.defaultValue(e.parseUrl,!0)?this.parseUrl(e.url,!0,!0):this._url=e.url,this._credits=e.credits,this.useEVServer=!0}function Pn(e,t){return o.defined(e)?Yt(e):t}let Rn;function Tn(e,t,n){if(!n)return f.combine(e,t);const r=Yt(e,!0);for(const e in t)if(t.hasOwnProperty(e)){let n=r[e];const i=t[e];o.defined(n)?(Array.isArray(n)||(n=r[e]=[n]),r[e]=n.concat(i)):r[e]=Array.isArray(i)?i.slice():i}return r}function qn(e){const t=e.resource,n=e.flipY,r=e.skipColorSpaceConversion,i=e.preferImageBitmap,s=t.request;s.url=t.url,s.requestFunction=function(){let e=!1;t.isDataUri||t.isBlobUri||(e=t.isCrossOriginUrl);const o=Zt();return In._Implementations.createImage(s,e,o,n,r,i),o.promise};const a=wn.request(s);if(o.defined(a))return a.catch((function(e){return s.state!==on.FAILED?Promise.reject(e):t.retryOnError(e).then((function(o){return o?(s.state=on.UNISSUED,s.deferred=void 0,qn({resource:t,flipY:n,skipColorSpaceConversion:r,preferImageBitmap:i})):Promise.reject(e)}))}))}function zn(e,t,n){const r={};r[t]=n,e.setQueryParameters(r);const i=e.request,s=e.url;i.url=s,i.requestFunction=function(){const e=Zt();return window[n]=function(t){e.resolve(t);try{delete window[n]}catch(e){window[n]=void 0}},In._Implementations.loadAndExecuteScript(s,n,e),e.promise};const a=wn.request(i);if(o.defined(a))return a.catch((function(r){return i.state!==on.FAILED?Promise.reject(r):e.retryOnError(r).then((function(o){return o?(i.state=on.UNISSUED,i.deferred=void 0,zn(e,t,n)):Promise.reject(r)}))}))}function Mn(e){if(e.state===on.ISSUED||e.state===on.ACTIVE)throw new h.RuntimeError("The Resource is already being fetched.");e.state=on.UNISSUED,e.deferred=void 0}In.createIfNeeded=function(e){return e instanceof In?e.getDerivedResource({request:e.request}):"string"!=typeof e?e:new In({url:e})},In.supportsImageBitmapOptions=function(){if(o.defined(Rn))return Rn;if("function"!=typeof createImageBitmap)return Rn=Promise.resolve(!1),Rn;return Rn=In.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAABGdBTUEAAE4g3rEiDgAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAADElEQVQI12Ng6GAAAAEUAIngE3ZiAAAAAElFTkSuQmCC"}).then((function(e){return Promise.all([createImageBitmap(e,{imageOrientation:"flipY",premultiplyAlpha:"none",colorSpaceConversion:"none"}),createImageBitmap(e)])})).then((function(e){const t=Xt(e[0]),n=Xt(e[1]);return t[1]!==n[1]})).catch((function(){return!1})),Rn},Object.defineProperties(In,{isBlobSupported:{get:function(){return On}}}),Object.defineProperties(In.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){this.parseUrl(e,!1,!1)}},extension:{get:function(){return function(e){const t=new Ht(e);t.normalize();let n=t.path(),r=n.lastIndexOf("/");return-1!==r&&(n=n.substr(r+1)),r=n.lastIndexOf("."),n=-1===r?"":n.substr(r+1),n}(this._url)}},isDataUri:{get:function(){return rn(this._url)}},isBlobUri:{get:function(){return en(this._url)}},isCrossOriginUrl:{get:function(){return function(e){o.defined(tn)||(tn=document.createElement("a")),tn.href=window.location.href;const t=tn.host,n=tn.protocol;return tn.href=e,tn.href=tn.href,n!==tn.protocol||t!==tn.host}(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}},credits:{get:function(){return this._credits}}}),In.prototype.toString=function(){return this.getUrlComponent(!0,!0)},In.prototype.parseUrl=function(e,t,n,r){let i=new Ht(e);const s=function(e){if(0===e.length)return{};if(-1===e.indexOf("="))return{[e]:void 0};return function(e){const t={};if(""===e)return t;const n=e.replace(/\+/g,"%20").split(/[&;]/);for(let e=0,r=n.length;e<r;++e){const r=n[e].split("="),i=decodeURIComponent(r[0]);let s=r[1];s=o.defined(s)?decodeURIComponent(s):"";const a=t[i];"string"==typeof a?t[i]=[a,s]:Array.isArray(a)?a.push(s):t[i]=s}return t}(e)}(i.query());this._queryParameters=t?Tn(s,this.queryParameters,n):s,i.search(""),i.fragment(""),o.defined(r)&&""===i.scheme()&&(i=i.absoluteTo(Gt(r))),this._url=i.toString()},In.prototype.getUrlComponent=function(e,t){if(this.isDataUri)return this._url;let n=this._url;e&&(n=`${n}${function(e){const t=Object.keys(e);if(0===t.length)return"";if(1===t.length&&!o.defined(e[t[0]]))return`?${t[0]}`;return`?${function(e){let t="";for(const n in e)if(e.hasOwnProperty(n)){const r=e[n],o=`${encodeURIComponent(n)}=`;if(Array.isArray(r))for(let e=0,n=r.length;e<n;++e)t+=`${o+encodeURIComponent(r[e])}&`;else t+=`${o+encodeURIComponent(r)}&`}return t=t.slice(0,-1),t}(e)}`}(this.queryParameters)}`),n=n.replace(/%7B/g,"{").replace(/%7D/g,"}");const r=this._templateValues;return Object.keys(r).length>0&&(n=n.replace(/{(.*?)}/g,(function(e,t){const n=r[t];return o.defined(n)?encodeURIComponent(n):e}))),t&&o.defined(this.proxy)&&(n=this.proxy.getURL(n)),n},In.prototype.setQueryParameters=function(e,t){this._queryParameters=t?Tn(this._queryParameters,e,!1):Tn(e,this._queryParameters,!1)},In.prototype.appendQueryParameters=function(e){this._queryParameters=Tn(e,this._queryParameters,!0)},In.prototype.setTemplateValues=function(e,t){this._templateValues=t?f.combine(this._templateValues,e):f.combine(e,this._templateValues)},In.prototype.getDerivedResource=function(e){const t=this.clone();if(t._retryCount=0,o.defined(e.url)){const n=o.defaultValue(e.preserveQueryParameters,!1);t.parseUrl(e.url,!0,n,this._url)}return t.useEVServer&&(o.defined(e.queryParameters)||(e.queryParameters={}),o.defined(e.queryParameters.token)||-1===this._url.indexOf("earthview")&&-1===this._url.indexOf("EV_WebService")||(e.queryParameters.token="f584b6bdee30490")),o.defined(e.queryParameters)&&(t._queryParameters=f.combine(e.queryParameters,t.queryParameters)),o.defined(e.templateValues)&&(t._templateValues=f.combine(e.templateValues,t.templateValues)),o.defined(e.headers)&&(t.headers=f.combine(e.headers,t.headers)),o.defined(e.proxy)&&(t.proxy=e.proxy),o.defined(e.request)&&(t.request=e.request),o.defined(e.retryCallback)&&(t.retryCallback=e.retryCallback),o.defined(e.retryAttempts)&&(t.retryAttempts=e.retryAttempts),t},In.prototype.retryOnError=function(e){const t=this.retryCallback;if("function"!=typeof t||this._retryCount>=this.retryAttempts)return Promise.resolve(!1);const n=this;return Promise.resolve(t(this,e)).then((function(e){return++n._retryCount,e}))},In.prototype.clone=function(e){return o.defined(e)?(e._url=this._url,e._queryParameters=Yt(this._queryParameters),e._templateValues=Yt(this._templateValues),e.headers=Yt(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e):new In({url:this._url,queryParameters:this.queryParameters,templateValues:this.templateValues,headers:this.headers,proxy:this.proxy,retryCallback:this.retryCallback,retryAttempts:this.retryAttempts,request:this.request.clone(),parseUrl:!1,credits:o.defined(this.credits)?this.credits.slice():void 0})},In.prototype.getBaseUri=function(e){return function(e,t){let n="";const r=e.lastIndexOf("/");return-1!==r&&(n=e.substring(0,r+1)),t?(0!==(e=new Ht(e)).query().length&&(n+=`?${e.query()}`),0!==e.fragment().length&&(n+=`#${e.fragment()}`),n):n}(this.getUrlComponent(e),e)},In.prototype.appendForwardSlash=function(){var e;this._url=(0!==(e=this._url).length&&"/"===e[e.length-1]||(e=`${e}/`),e)},In.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})},In.fetchArrayBuffer=function(e){return new In(e).fetchArrayBuffer()},In.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})},In.fetchBlob=function(e){return new In(e).fetchBlob()},In.prototype.fetchImage=function(e){e=o.defaultValue(e,o.defaultValue.EMPTY_OBJECT);const t=o.defaultValue(e.preferImageBitmap,!1),n=o.defaultValue(e.preferBlob,!1),r=o.defaultValue(e.flipY,!1),i=o.defaultValue(e.skipColorSpaceConversion,!1);if(Mn(this.request),!On||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!n)return qn({resource:this,flipY:r,skipColorSpaceConversion:i,preferImageBitmap:t});const s=this.fetchBlob();if(!o.defined(s))return;let a,u,c,l;return In.supportsImageBitmapOptions().then((function(e){return a=e,u=a&&t,s})).then((function(e){if(!o.defined(e))return;if(l=e,u)return In.createImageBitmapFromBlob(e,{flipY:r,premultiplyAlpha:!1,skipColorSpaceConversion:i});const t=window.URL.createObjectURL(e);return c=new In({url:t}),qn({resource:c,flipY:r,skipColorSpaceConversion:i,preferImageBitmap:!1})})).then((function(e){if(o.defined(e))return e.blob=l,u||window.URL.revokeObjectURL(c.url),e})).catch((function(e){return o.defined(c)&&window.URL.revokeObjectURL(c.url),e.blob=l,Promise.reject(e)}))},In.fetchImage=function(e){return new In(e).fetchImage({flipY:e.flipY,skipColorSpaceConversion:e.skipColorSpaceConversion,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})},In.prototype.fetchText=function(){return this.fetch({responseType:"text"})},In.fetchText=function(e){return new In(e).fetchText()},In.prototype.fetchJson=function(){const e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(o.defined(e))return e.then((function(e){if(o.defined(e))return JSON.parse(e)}))},In.fetchJson=function(e){return new In(e).fetchJson()},In.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})},In.fetchXML=function(e){return new In(e).fetchXML()},In.prototype.fetchJsonp=function(e){let t;e=o.defaultValue(e,"callback"),Mn(this.request);do{t=`loadJsonp${a.CesiumMath.nextRandomNumber().toString().substring(2,8)}`}while(o.defined(window[t]));return zn(this,e,t)},In.fetchJsonp=function(e){return new In(e).fetchJsonp(e.callbackParameterName)},In.prototype._makeRequest=function(e){const t=this;Mn(t.request);const n=t.request,r=t.url;n.url=r,n.requestFunction=function(){const i=e.responseType,s=f.combine(e.headers,t.headers),a=e.overrideMimeType,u=e.method,c=e.data,l=Zt(),d=In._Implementations.loadWithXhr(r,i,u,c,s,l,a);return o.defined(d)&&o.defined(d.abort)&&(n.cancelFunction=function(){d.abort()}),l.promise};const i=wn.request(n);if(o.defined(i))return i.then((function(e){return n.cancelFunction=void 0,e})).catch((function(r){return n.cancelFunction=void 0,n.state!==on.FAILED?Promise.reject(r):t.retryOnError(r).then((function(o){return o?(n.state=on.UNISSUED,n.deferred=void 0,t.fetch(e)):Promise.reject(r)}))}))};const Dn=/^data:(.*?)(;base64)?,(.*)$/;function Un(e,t){const n=decodeURIComponent(t);return e?atob(n):n}function kn(e,t){const n=Un(e,t),r=new ArrayBuffer(n.length),o=new Uint8Array(r);for(let e=0;e<n.length;e++)o[e]=n.charCodeAt(e);return r}function Fn(e,t){switch(t){case"text":return e.toString("utf8");case"json":return JSON.parse(e.toString("utf8"));default:return new Uint8Array(e).buffer}}In.prototype.fetch=function(e){return(e=Pn(e,{})).method="GET",this._makeRequest(e)},In.fetch=function(e){return new In(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},In.prototype.delete=function(e){return(e=Pn(e,{})).method="DELETE",this._makeRequest(e)},In.delete=function(e){return new In(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})},In.prototype.head=function(e){return(e=Pn(e,{})).method="HEAD",this._makeRequest(e)},In.head=function(e){return new In(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},In.prototype.options=function(e){return(e=Pn(e,{})).method="OPTIONS",this._makeRequest(e)},In.options=function(e){return new In(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},In.prototype.post=function(e,t){return s.Check.defined("data",e),(t=Pn(t,{})).method="POST",t.data=e,this._makeRequest(t)},In.post=function(e){return new In(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},In.prototype.put=function(e,t){return s.Check.defined("data",e),(t=Pn(t,{})).method="PUT",t.data=e,this._makeRequest(t)},In.put=function(e){return new In(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},In.prototype.patch=function(e,t){return s.Check.defined("data",e),(t=Pn(t,{})).method="PATCH",t.data=e,this._makeRequest(t)},In.patch=function(e){return new In(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},In._Implementations={},In._Implementations.loadImageElement=function(e,t,n){const r=new Image;r.crossOrigin="anonymous",r.onload=function(){0===r.naturalWidth&&0===r.naturalHeight&&0===r.width&&0===r.height&&(r.width=300,r.height=150),n.resolve(r)},r.onerror=function(e){n.reject(e)},t&&(En.contains(e)?r.crossOrigin="use-credentials":r.crossOrigin=""),r.src=e},In._Implementations.createImage=function(e,t,n,r,i,s){const a=e.url;In.supportsImageBitmapOptions().then((function(u){if(!u||!s)return void In._Implementations.loadImageElement(a,t,n);const c=Zt(),l=In._Implementations.loadWithXhr(a,"blob","GET",void 0,void 0,c,void 0,void 0,void 0);return o.defined(l)&&o.defined(l.abort)&&(e.cancelFunction=function(){l.abort()}),c.promise.then((function(e){if(o.defined(e))return In.createImageBitmapFromBlob(e,{flipY:r,premultiplyAlpha:!1,skipColorSpaceConversion:i});n.reject(new h.RuntimeError(`Successfully retrieved ${a} but it contained no content.`))})).then((function(e){n.resolve(e)}))})).catch((function(e){n.reject(e)}))},In.createImageBitmapFromBlob=function(e,t){return s.Check.defined("options",t),s.Check.typeOf.bool("options.flipY",t.flipY),s.Check.typeOf.bool("options.premultiplyAlpha",t.premultiplyAlpha),s.Check.typeOf.bool("options.skipColorSpaceConversion",t.skipColorSpaceConversion),createImageBitmap(e,{imageOrientation:t.flipY?"flipY":"none",premultiplyAlpha:t.premultiplyAlpha?"premultiply":"none",colorSpaceConversion:t.skipColorSpaceConversion?"none":"default"})};const Nn="undefined"==typeof XMLHttpRequest;function jn(e){e=o.defaultValue(e,o.defaultValue.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._addNewLeapSeconds=o.defaultValue(e.addNewLeapSeconds,!0),o.defined(e.data)?Vn(this,e.data):Vn(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}function Bn(e,t){return Mt.compare(e.julianDate,t)}function Vn(e,t){if(!o.defined(t.columnNames))throw new h.RuntimeError("Error in loaded EOP data: The columnNames property is required.");if(!o.defined(t.samples))throw new h.RuntimeError("Error in loaded EOP data: The samples property is required.");const n=t.columnNames.indexOf("modifiedJulianDateUtc"),r=t.columnNames.indexOf("xPoleWanderRadians"),i=t.columnNames.indexOf("yPoleWanderRadians"),s=t.columnNames.indexOf("ut1MinusUtcSeconds"),a=t.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=t.columnNames.indexOf("yCelestialPoleOffsetRadians"),c=t.columnNames.indexOf("taiMinusUtcSeconds");if(n<0||r<0||i<0||s<0||a<0||u<0||c<0)throw new h.RuntimeError("Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns");const l=e._samples=t.samples,d=e._dates=[];let f;e._dateColumn=n,e._xPoleWanderRadiansColumn=r,e._yPoleWanderRadiansColumn=i,e._ut1MinusUtcSecondsColumn=s,e._xCelestialPoleOffsetRadiansColumn=a,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=c,e._columnCount=t.columnNames.length,e._lastIndex=void 0;const p=e._addNewLeapSeconds;for(let t=0,r=l.length;t<r;t+=e._columnCount){const e=l[t+n],r=l[t+c],i=new Mt(e+mt.MODIFIED_JULIAN_DATE_DIFFERENCE,r,gt.TAI);if(d.push(i),p){if(r!==f&&o.defined(f)){const e=Mt.leapSeconds,t=lt(e,i,Bn);if(t<0){const n=new ht(i,r);e.splice(~t,0,n)}}f=r}}}function Ln(e,t,n,r,o){const i=n*r;o.xPoleWander=t[i+e._xPoleWanderRadiansColumn],o.yPoleWander=t[i+e._yPoleWanderRadiansColumn],o.xPoleOffset=t[i+e._xCelestialPoleOffsetRadiansColumn],o.yPoleOffset=t[i+e._yCelestialPoleOffsetRadiansColumn],o.ut1MinusUtc=t[i+e._ut1MinusUtcSecondsColumn]}function Qn(e,t,n){return t+e*(n-t)}function $n(e,t,n,r,o,i,s){const a=e._columnCount;if(i>t.length-1)return s.xPoleWander=0,s.yPoleWander=0,s.xPoleOffset=0,s.yPoleOffset=0,s.ut1MinusUtc=0,s;const u=t[o],c=t[i];if(u.equals(c)||r.equals(u))return Ln(e,n,o,a,s),s;if(r.equals(c))return Ln(e,n,i,a,s),s;const l=Mt.secondsDifference(r,u)/Mt.secondsDifference(c,u),d=o*a,f=i*a;let p=n[d+e._ut1MinusUtcSecondsColumn],h=n[f+e._ut1MinusUtcSecondsColumn];const m=h-p;if(m>.5||m<-.5){const t=n[d+e._taiMinusUtcSecondsColumn],o=n[f+e._taiMinusUtcSecondsColumn];t!==o&&(c.equals(r)?p=h:h-=o-t)}return s.xPoleWander=Qn(l,n[d+e._xPoleWanderRadiansColumn],n[f+e._xPoleWanderRadiansColumn]),s.yPoleWander=Qn(l,n[d+e._yPoleWanderRadiansColumn],n[f+e._yPoleWanderRadiansColumn]),s.xPoleOffset=Qn(l,n[d+e._xCelestialPoleOffsetRadiansColumn],n[f+e._xCelestialPoleOffsetRadiansColumn]),s.yPoleOffset=Qn(l,n[d+e._yCelestialPoleOffsetRadiansColumn],n[f+e._yCelestialPoleOffsetRadiansColumn]),s.ut1MinusUtc=Qn(l,p,h),s}function Wn(e,t,n){this.heading=o.defaultValue(e,0),this.pitch=o.defaultValue(t,0),this.roll=o.defaultValue(n,0)}In._Implementations.loadWithXhr=function(t,n,r,i,s,a,u){const c=Dn.exec(t);if(null!==c)return void a.resolve(function(e,t){t=o.defaultValue(t,"");const n=e[1],r=!!e[2],i=e[3];let s,a;switch(t){case"":case"text":return Un(r,i);case"arraybuffer":return kn(r,i);case"blob":return s=kn(r,i),new Blob([s],{type:n});case"document":return a=new DOMParser,a.parseFromString(Un(r,i),n);case"json":return JSON.parse(Un(r,i))}}(c,n));if(Nn)return void function(t,n,r,o,i,s,a){let u,c;Promise.all([new Promise((function(t,n){e(["url"],(function(e){t(m(e))}),n)})),new Promise((function(t,n){e(["zlib"],(function(e){t(m(e))}),n)}))]).then((([n,r])=>(u=n.parse(t),c=r,"https:"===u.protocol?new Promise((function(t,n){e(["https"],(function(e){t(m(e))}),n)})):new Promise((function(t,n){e(["http"],(function(e){t(m(e))}),n)}))))).then((e=>{const t={protocol:u.protocol,hostname:u.hostname,port:u.port,path:u.path,query:u.query,method:r,headers:i};e.request(t).on("response",(function(e){if(e.statusCode<200||e.statusCode>=300)return void s.reject(new an(e.statusCode,e,e.headers));const t=[];e.on("data",(function(e){t.push(e)})),e.on("end",(function(){const r=Buffer.concat(t);"gzip"===e.headers["content-encoding"]?c.gunzip(r,(function(e,t){e?s.reject(new h.RuntimeError("Error decompressing response.")):s.resolve(Fn(t,n))})):s.resolve(Fn(r,n))}))})).on("error",(function(e){s.reject(new an)})).end()}))}(t,n,r,0,s,a);const l=new XMLHttpRequest;if(En.contains(t)&&(l.withCredentials=!0),l.open(r,t,!0),o.defined(u)&&o.defined(l.overrideMimeType)&&l.overrideMimeType(u),o.defined(s))for(const e in s)s.hasOwnProperty(e)&&l.setRequestHeader(e,s[e]);o.defined(n)&&(l.responseType=n);let d=!1;return"string"==typeof t&&(d=0===t.indexOf("file://")||"undefined"!=typeof window&&"file://"===window.location.origin),l.onload=function(){if((l.status<200||l.status>=300)&&(!d||0!==l.status))return void a.reject(new an(l.status,l.response,l.getAllResponseHeaders()));const e=l.response,t=l.responseType;if("HEAD"===r||"OPTIONS"===r){const e=l.getAllResponseHeaders().trim().split(/[\r\n]+/),t={};return e.forEach((function(e){const n=e.split(": "),r=n.shift();t[r]=n.join(": ")})),void a.resolve(t)}if(204===l.status)a.resolve();else if(!o.defined(e)||o.defined(n)&&t!==n)if("json"===n&&"string"==typeof e)try{a.resolve(JSON.parse(e))}catch(e){a.reject(e)}else(""===t||"document"===t)&&o.defined(l.responseXML)&&l.responseXML.hasChildNodes()?a.resolve(l.responseXML):""!==t&&"text"!==t||!o.defined(l.responseText)?a.reject(new h.RuntimeError("Invalid XMLHttpRequest response type.")):a.resolve(l.responseText);else a.resolve(e)},l.onerror=function(e){a.reject(new an)},l.send(i),l},In._Implementations.loadAndExecuteScript=function(e,t,n){return function(e){const t=document.createElement("script");return t.async=!0,t.src=e,new Promise(((e,n)=>{window.crossOriginIsolated&&t.setAttribute("crossorigin","anonymous");const r=document.getElementsByTagName("head")[0];t.onload=function(){t.onload=void 0,r.removeChild(t),e()},t.onerror=function(e){n(e)},r.appendChild(t)}))}(e).catch((function(e){n.reject(e)}))},In._DefaultImplementations={},In._DefaultImplementations.createImage=In._Implementations.createImage,In._DefaultImplementations.loadWithXhr=In._Implementations.loadWithXhr,In._DefaultImplementations.loadAndExecuteScript=In._Implementations.loadAndExecuteScript,In.DEFAULT=Object.freeze(new In({url:"undefined"==typeof document?"":document.location.href.split("?")[0]})),jn.fromUrl=async function(e,t){t=o.defaultValue(t,o.defaultValue.EMPTY_OBJECT);const n=In.createIfNeeded(e);let r;try{r=await n.fetchJson()}catch(e){throw new h.RuntimeError(`An error occurred while retrieving the EOP data from the URL ${n.url}.`)}return new jn({addNewLeapSeconds:t.addNewLeapSeconds,data:r})},jn.NONE=Object.freeze({compute:function(e,t){return o.defined(t)?(t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0):t=new dt(0,0,0,0,0),t}}),jn.prototype.compute=function(e,t){if(!o.defined(this._samples))return;if(o.defined(t)||(t=new dt(0,0,0,0,0)),0===this._samples.length)return t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0,t;const n=this._dates,r=this._lastIndex;let i=0,s=0;if(o.defined(r)){const a=n[r],u=n[r+1],c=Mt.lessThanOrEquals(a,e),l=!o.defined(u),d=l||Mt.greaterThanOrEquals(u,e);if(c&&d)return i=r,!l&&u.equals(e)&&++i,s=i+1,$n(this,n,this._samples,e,i,s,t),t}let a=lt(n,e,Mt.compare,this._dateColumn);return a>=0?(a<n.length-1&&n[a+1].equals(e)&&++a,i=a,s=a):(s=~a,i=s-1,i<0&&(i=0)),this._lastIndex=i,$n(this,n,this._samples,e,i,s,t),t},Wn.fromQuaternion=function(e,t){o.defined(t)||(t=new Wn);const n=2*(e.w*e.y-e.z*e.x),r=1-2*(e.x*e.x+e.y*e.y),i=2*(e.w*e.x+e.y*e.z),s=1-2*(e.y*e.y+e.z*e.z),u=2*(e.w*e.z+e.x*e.y);return t.heading=-Math.atan2(u,s),t.roll=Math.atan2(i,r),t.pitch=-a.CesiumMath.asinClamped(n),t},Wn.fromDegrees=function(e,t,n,r){return o.defined(r)||(r=new Wn),r.heading=e*a.CesiumMath.RADIANS_PER_DEGREE,r.pitch=t*a.CesiumMath.RADIANS_PER_DEGREE,r.roll=n*a.CesiumMath.RADIANS_PER_DEGREE,r},Wn.clone=function(e,t){if(o.defined(e))return o.defined(t)?(t.heading=e.heading,t.pitch=e.pitch,t.roll=e.roll,t):new Wn(e.heading,e.pitch,e.roll)},Wn.equals=function(e,t){return e===t||o.defined(e)&&o.defined(t)&&e.heading===t.heading&&e.pitch===t.pitch&&e.roll===t.roll},Wn.equalsEpsilon=function(e,t,n,r){return e===t||o.defined(e)&&o.defined(t)&&a.CesiumMath.equalsEpsilon(e.heading,t.heading,n,r)&&a.CesiumMath.equalsEpsilon(e.pitch,t.pitch,n,r)&&a.CesiumMath.equalsEpsilon(e.roll,t.roll,n,r)},Wn.prototype.clone=function(e){return Wn.clone(this,e)},Wn.prototype.equals=function(e){return Wn.equals(this,e)},Wn.prototype.equalsEpsilon=function(e,t,n){return Wn.equalsEpsilon(this,e,t,n)},Wn.prototype.toString=function(){return`(${this.heading}, ${this.pitch}, ${this.roll})`};const Hn=/((?:.*\/)|^)Cesium\.js(?:\?|\#|$)/;let Yn,Zn,Gn;function Jn(e){return"undefined"==typeof document?e:(o.defined(Yn)||(Yn=document.createElement("a")),Yn.href=e,Yn.href=Yn.href,Yn.href)}function Xn(){if(o.defined(Zn))return Zn;let t;return t="undefined"!=typeof CESIUM_BASE_URL?CESIUM_BASE_URL:"object"==typeof define&&o.defined(define.amd)&&!define.amd.toUrlUndefined&&o.defined(e.toUrl)?Gt("..",tr("Core/buildModuleUrl.js")):function(){const e=document.getElementsByTagName("script");for(let t=0,n=e.length;t<n;++t){const n=e[t].getAttribute("src"),r=Hn.exec(n);if(null!==r)return r[1]}}(),Zn=new In({url:Jn(t)}),Zn.appendForwardSlash(),Zn}function Kn(t){return Jn(e.toUrl(`../${t}`))}function er(e){return Xn().getDerivedResource({url:e}).url}function tr(t){o.defined(Gn)||(Gn="object"==typeof define&&o.defined(define.amd)&&!define.amd.toUrlUndefined&&o.defined(e.toUrl)?Kn:er);return Gn(t)}function nr(e,t,n){this.x=e,this.y=t,this.s=n}function rr(e){e=o.defaultValue(e,o.defaultValue.EMPTY_OBJECT),this._xysFileUrlTemplate=In.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=o.defaultValue(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=o.defaultValue(e.sampleZeroJulianEphemerisDate,2442396.5),this._sampleZeroDateTT=new Mt(this._sampleZeroJulianEphemerisDate,0,gt.TAI),this._stepSizeDays=o.defaultValue(e.stepSizeDays,1),this._samplesPerXysFile=o.defaultValue(e.samplesPerXysFile,1e3),this._totalSamples=o.defaultValue(e.totalSamples,27426),this._samples=new Array(3*this._totalSamples),this._chunkDownloadsInProgress=[];const t=this._interpolationOrder,n=this._denominators=new Array(t+1),r=this._xTable=new Array(t+1),i=Math.pow(this._stepSizeDays,t);for(let e=0;e<=t;++e){n[e]=i,r[e]=e*this._stepSizeDays;for(let r=0;r<=t;++r)r!==e&&(n[e]*=e-r);n[e]=1/n[e]}this._work=new Array(t+1),this._coef=new Array(t+1)}tr._cesiumScriptRegex=Hn,tr._buildModuleUrlFromBaseUrl=er,tr._clearBaseResource=function(){Zn=void 0},tr.setBaseUrl=function(e){Zn=In.DEFAULT.getDerivedResource({url:e})},tr.getCesiumBaseUrl=Xn;const or=new Mt(0,0,gt.TAI);function ir(e,t,n){const r=or;return r.dayNumber=t,r.secondsOfDay=n,Mt.daysDifference(r,e._sampleZeroDateTT)}function sr(e,t){if(e._chunkDownloadsInProgress[t])return e._chunkDownloadsInProgress[t];let n;const r=e._xysFileUrlTemplate;n=o.defined(r)?r.getDerivedResource({templateValues:{0:t}}):new In({url:tr(`Assets/IAU2006_XYS/IAU2006_XYS_${t}.json`)});const i=n.fetchJson().then((function(n){e._chunkDownloadsInProgress[t]=!1;const r=e._samples,o=n.samples,i=t*e._samplesPerXysFile*3;for(let e=0,t=o.length;e<t;++e)r[i+e]=o[e]}));return e._chunkDownloadsInProgress[t]=i,i}rr.prototype.preload=function(e,t,n,r){const o=ir(this,e,t),i=ir(this,n,r);let s=o/this._stepSizeDays-this._interpolationOrder/2|0;s<0&&(s=0);let a=i/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;a>=this._totalSamples&&(a=this._totalSamples-1);const u=s/this._samplesPerXysFile|0,c=a/this._samplesPerXysFile|0,l=[];for(let e=u;e<=c;++e)l.push(sr(this,e));return Promise.all(l)},rr.prototype.computeXysRadians=function(e,t,n){const r=ir(this,e,t);if(r<0)return;const i=r/this._stepSizeDays|0;if(i>=this._totalSamples)return;const s=this._interpolationOrder;let a=i-(s/2|0);a<0&&(a=0);let u=a+s;u>=this._totalSamples&&(u=this._totalSamples-1,a=u-s,a<0&&(a=0));let c=!1;const l=this._samples;if(o.defined(l[3*a])||(sr(this,a/this._samplesPerXysFile|0),c=!0),o.defined(l[3*u])||(sr(this,u/this._samplesPerXysFile|0),c=!0),c)return;o.defined(n)?(n.x=0,n.y=0,n.s=0):n=new nr(0,0,0);const d=r-a*this._stepSizeDays,f=this._work,p=this._denominators,h=this._coef,m=this._xTable;let g,y;for(g=0;g<=s;++g)f[g]=d-m[g];for(g=0;g<=s;++g){for(h[g]=1,y=0;y<=s;++y)y!==g&&(h[g]*=f[y]);h[g]*=p[g];let e=3*(a+g);n.x+=h[g]*l[e++],n.y+=h[g]*l[e++],n.s+=h[g]*l[e]}return n};const ar={},ur={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},cr={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},lr={},dr={east:new n.Cartesian3,north:new n.Cartesian3,up:new n.Cartesian3,west:new n.Cartesian3,south:new n.Cartesian3,down:new n.Cartesian3};let fr=new n.Cartesian3,pr=new n.Cartesian3,hr=new n.Cartesian3;ar.localFrameToFixedFrameGenerator=function(e,t){if(!ur.hasOwnProperty(e)||!ur[e].hasOwnProperty(t))throw new d.DeveloperError("firstAxis and secondAxis must be east, north, up, west, south or down.");const r=ur[e][t];let s;const u=e+t;return o.defined(lr[u])?s=lr[u]:(s=function(s,u,l){if(o.defined(l)||(l=new c.Matrix4),n.Cartesian3.equalsEpsilon(s,n.Cartesian3.ZERO,a.CesiumMath.EPSILON14))n.Cartesian3.unpack(cr[e],0,fr),n.Cartesian3.unpack(cr[t],0,pr),n.Cartesian3.unpack(cr[r],0,hr);else if(a.CesiumMath.equalsEpsilon(s.x,0,a.CesiumMath.EPSILON14)&&a.CesiumMath.equalsEpsilon(s.y,0,a.CesiumMath.EPSILON14)){const o=a.CesiumMath.sign(s.z);n.Cartesian3.unpack(cr[e],0,fr),"east"!==e&&"west"!==e&&n.Cartesian3.multiplyByScalar(fr,o,fr),n.Cartesian3.unpack(cr[t],0,pr),"east"!==t&&"west"!==t&&n.Cartesian3.multiplyByScalar(pr,o,pr),n.Cartesian3.unpack(cr[r],0,hr),"east"!==r&&"west"!==r&&n.Cartesian3.multiplyByScalar(hr,o,hr)}else{(u=o.defaultValue(u,i.Ellipsoid.WGS84)).geodeticSurfaceNormal(s,dr.up);const a=dr.up,c=dr.east;c.x=-s.y,c.y=s.x,c.z=0,n.Cartesian3.normalize(c,dr.east),n.Cartesian3.cross(a,c,dr.north),n.Cartesian3.multiplyByScalar(dr.up,-1,dr.down),n.Cartesian3.multiplyByScalar(dr.east,-1,dr.west),n.Cartesian3.multiplyByScalar(dr.north,-1,dr.south),fr=dr[e],pr=dr[t],hr=dr[r]}return l[0]=fr.x,l[1]=fr.y,l[2]=fr.z,l[3]=0,l[4]=pr.x,l[5]=pr.y,l[6]=pr.z,l[7]=0,l[8]=hr.x,l[9]=hr.y,l[10]=hr.z,l[11]=0,l[12]=s.x,l[13]=s.y,l[14]=s.z,l[15]=1,l},lr[u]=s),s},ar.eastNorthUpToFixedFrame=ar.localFrameToFixedFrameGenerator("east","north"),ar.northEastDownToFixedFrame=ar.localFrameToFixedFrameGenerator("north","east"),ar.northUpEastToFixedFrame=ar.localFrameToFixedFrameGenerator("north","up"),ar.northWestUpToFixedFrame=ar.localFrameToFixedFrameGenerator("north","west");const mr=new ke,gr=new n.Cartesian3(1,1,1),yr=new c.Matrix4;ar.headingPitchRollToFixedFrame=function(e,t,r,i,s){i=o.defaultValue(i,ar.eastNorthUpToFixedFrame);const a=ke.fromHeadingPitchRoll(t,mr),u=c.Matrix4.fromTranslationQuaternionRotationScale(n.Cartesian3.ZERO,a,gr,yr);return s=i(e,r,s),c.Matrix4.multiply(s,u,s)};const vr=new c.Matrix4,wr=new u.Matrix3;ar.headingPitchRollQuaternion=function(e,t,n,r,o){const i=ar.headingPitchRollToFixedFrame(e,t,n,r,vr),s=c.Matrix4.getMatrix3(i,wr);return ke.fromRotationMatrix(s,o)};const Cr=new n.Cartesian3(1,1,1),_r=new n.Cartesian3,br=new c.Matrix4,xr=new c.Matrix4,Sr=new u.Matrix3,Ar=new ke;ar.fixedFrameToHeadingPitchRoll=function(e,t,r,s){t=o.defaultValue(t,i.Ellipsoid.WGS84),r=o.defaultValue(r,ar.eastNorthUpToFixedFrame),o.defined(s)||(s=new Wn);const a=c.Matrix4.getTranslation(e,_r);if(n.Cartesian3.equals(a,n.Cartesian3.ZERO))return s.heading=0,s.pitch=0,s.roll=0,s;let u=c.Matrix4.inverseTransformation(r(a,t,br),br),l=c.Matrix4.setScale(e,Cr,xr);l=c.Matrix4.setTranslation(l,n.Cartesian3.ZERO,l),u=c.Matrix4.multiply(u,l,u);let d=ke.fromRotationMatrix(c.Matrix4.getMatrix3(u,Sr),Ar);return d=ke.normalize(d,d),Wn.fromQuaternion(d,s)};const Er=a.CesiumMath.TWO_PI/86400;let Or=new Mt;ar.computeTemeToPseudoFixedMatrix=function(e,t){Or=Mt.addSeconds(e,-Mt.computeTaiMinusUtc(e),Or);const n=Or.dayNumber,r=Or.secondsOfDay;let i;const s=n-2451545;i=r>=43200?(s+.5)/mt.DAYS_PER_JULIAN_CENTURY:(s-.5)/mt.DAYS_PER_JULIAN_CENTURY;const c=(24110.54841+i*(8640184.812866+i*(.093104+-62e-7*i)))*Er%a.CesiumMath.TWO_PI+(72921158553e-15+11772758384668e-32*(n-2451545.5))*((r+.5*mt.SECONDS_PER_DAY)%mt.SECONDS_PER_DAY),l=Math.cos(c),d=Math.sin(c);return o.defined(t)?(t[0]=l,t[1]=-d,t[2]=0,t[3]=d,t[4]=l,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t):new u.Matrix3(l,d,0,-d,l,0,0,0,1)},ar.iau2006XysData=new rr,ar.earthOrientationParameters=jn.NONE;const Ir=32.184;ar.preloadIcrfFixed=function(e){const t=e.start.dayNumber,n=e.start.secondsOfDay+Ir,r=e.stop.dayNumber,o=e.stop.secondsOfDay+Ir;return ar.iau2006XysData.preload(t,n,r,o)},ar.computeIcrfToFixedMatrix=function(e,t){o.defined(t)||(t=new u.Matrix3);const n=ar.computeFixedToIcrfMatrix(e,t);if(o.defined(n))return u.Matrix3.transpose(n,t)};const Pr=new nr(0,0,0),Rr=new dt(0,0,0,0,0),Tr=new u.Matrix3,qr=new u.Matrix3;ar.computeFixedToIcrfMatrix=function(e,t){o.defined(t)||(t=new u.Matrix3);const n=ar.earthOrientationParameters.compute(e,Rr);if(!o.defined(n))return;const r=e.dayNumber,i=e.secondsOfDay+Ir,s=ar.iau2006XysData.computeXysRadians(r,i,Pr);if(!o.defined(s))return;const c=s.x+n.xPoleOffset,l=s.y+n.yPoleOffset,d=1/(1+Math.sqrt(1-c*c-l*l)),f=Tr;f[0]=1-d*c*c,f[3]=-d*c*l,f[6]=c,f[1]=-d*c*l,f[4]=1-d*l*l,f[7]=l,f[2]=-c,f[5]=-l,f[8]=1-d*(c*c+l*l);const p=u.Matrix3.fromRotationZ(-s.s,qr),h=u.Matrix3.multiply(f,p,Tr),m=e.dayNumber-2451545,g=(e.secondsOfDay-Mt.computeTaiMinusUtc(e)+n.ut1MinusUtc)/mt.SECONDS_PER_DAY;let y=.779057273264+g+.00273781191135448*(m+g);y=y%1*a.CesiumMath.TWO_PI;const v=u.Matrix3.fromRotationZ(y,qr),w=u.Matrix3.multiply(h,v,Tr),C=Math.cos(n.xPoleWander),_=Math.cos(n.yPoleWander),b=Math.sin(n.xPoleWander),x=Math.sin(n.yPoleWander);let S=r-2451545+i/mt.SECONDS_PER_DAY;S/=36525;const A=-47e-6*S*a.CesiumMath.RADIANS_PER_DEGREE/3600,E=Math.cos(A),O=Math.sin(A),I=qr;return I[0]=C*E,I[1]=C*O,I[2]=b,I[3]=-_*O+x*b*E,I[4]=_*E+x*b*O,I[5]=-x*C,I[6]=-x*O-_*b*E,I[7]=x*E-_*b*O,I[8]=_*C,u.Matrix3.multiply(w,I,t)};const zr=new c.Cartesian4;ar.pointToWindowCoordinates=function(e,t,n,r){return(r=ar.pointToGLWindowCoordinates(e,t,n,r)).y=2*t[5]-r.y,r},ar.pointToGLWindowCoordinates=function(e,t,n,r){o.defined(r)||(r=new c.Cartesian2);const i=zr;return c.Matrix4.multiplyByVector(e,c.Cartesian4.fromElements(n.x,n.y,n.z,1,i),i),c.Cartesian4.multiplyByScalar(i,1/i.w,i),c.Matrix4.multiplyByVector(t,i,i),c.Cartesian2.fromCartesian4(i,r)};const Mr=new n.Cartesian3,Dr=new n.Cartesian3,Ur=new n.Cartesian3;ar.rotationMatrixFromPositionVelocity=function(e,t,r,s){const c=o.defaultValue(r,i.Ellipsoid.WGS84).geodeticSurfaceNormal(e,Mr);let l=n.Cartesian3.cross(t,c,Dr);n.Cartesian3.equalsEpsilon(l,n.Cartesian3.ZERO,a.CesiumMath.EPSILON6)&&(l=n.Cartesian3.clone(n.Cartesian3.UNIT_X,l));const d=n.Cartesian3.cross(l,t,Ur);return n.Cartesian3.normalize(d,d),n.Cartesian3.cross(t,d,l),n.Cartesian3.negate(l,l),n.Cartesian3.normalize(l,l),o.defined(s)||(s=new u.Matrix3),s[0]=t.x,s[1]=t.y,s[2]=t.z,s[3]=l.x,s[4]=l.y,s[5]=l.z,s[6]=d.x,s[7]=d.y,s[8]=d.z,s};const kr=new c.Matrix4(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),Fr=new r.Cartographic,Nr=new n.Cartesian3,jr=new n.Cartesian3,Br=new u.Matrix3,Vr=new c.Matrix4,Lr=new c.Matrix4;ar.basisTo2D=function(e,t,r){const o=c.Matrix4.getTranslation(t,jr),i=e.ellipsoid,s=i.cartesianToCartographic(o,Fr),a=e.project(s,Nr);n.Cartesian3.fromElements(a.z,a.x,a.y,a);const u=ar.eastNorthUpToFixedFrame(o,i,Vr),l=c.Matrix4.inverseTransformation(u,Lr),d=c.Matrix4.getMatrix3(t,Br),f=c.Matrix4.multiplyByMatrix3(l,d,r);return c.Matrix4.multiply(kr,f,r),c.Matrix4.setTranslation(r,a,r),r},ar.wgs84To2DModelMatrix=function(e,t,r){const o=e.ellipsoid,i=ar.eastNorthUpToFixedFrame(t,o,Vr),s=c.Matrix4.inverseTransformation(i,Lr),a=o.cartesianToCartographic(t,Fr),u=e.project(a,Nr);n.Cartesian3.fromElements(u.z,u.x,u.y,u);const l=c.Matrix4.fromTranslation(u,Vr);return c.Matrix4.multiply(kr,s,r),c.Matrix4.multiply(l,r,r),r};var Qr=ar;t.BoundingSphere=v,t.FeatureDetection=Ue,t.GeographicProjection=g,t.Intersect=y,t.Quaternion=ke,t.Resource=In,t.Transforms=Qr,t.buildModuleUrl=tr}));
