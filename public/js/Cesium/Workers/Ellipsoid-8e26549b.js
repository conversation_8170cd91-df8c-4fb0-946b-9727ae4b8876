define(["exports","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Math-355606c6"],(function(e,i,t,a,r){"use strict";function n(e,t,n,o){t=a.defaultValue(t,0),n=a.defaultValue(n,0),o=a.defaultValue(o,0),e._radii=new i.Cartesian3(t,n,o),e._radiiSquared=new i.Cartesian3(t*t,n*n,o*o),e._radiiToTheFourth=new i.Cartesian3(t*t*t*t,n*n*n*n,o*o*o*o),e._oneOverRadii=new i.Cartesian3(0===t?0:1/t,0===n?0:1/n,0===o?0:1/o),e._oneOverRadiiSquared=new i.Cartesian3(0===t?0:1/(t*t),0===n?0:1/(n*n),0===o?0:1/(o*o)),e._minimumRadius=Math.min(t,n,o),e._maximumRadius=Math.max(t,n,o),e._centerToleranceSquared=r.CesiumMath.EPSILON1,0!==e._radiiSquared.z&&(e._squaredXOverSquaredZ=e._radiiSquared.x/e._radiiSquared.z)}function o(e,i,t){this._radii=void 0,this._radiiSquared=void 0,this._radiiToTheFourth=void 0,this._oneOverRadii=void 0,this._oneOverRadiiSquared=void 0,this._minimumRadius=void 0,this._maximumRadius=void 0,this._centerToleranceSquared=void 0,this._squaredXOverSquaredZ=void 0,n(this,e,i,t)}Object.defineProperties(o.prototype,{radii:{get:function(){return this._radii}},radiiSquared:{get:function(){return this._radiiSquared}},radiiToTheFourth:{get:function(){return this._radiiToTheFourth}},oneOverRadii:{get:function(){return this._oneOverRadii}},oneOverRadiiSquared:{get:function(){return this._oneOverRadiiSquared}},minimumRadius:{get:function(){return this._minimumRadius}},maximumRadius:{get:function(){return this._maximumRadius}}}),o.clone=function(e,t){if(!a.defined(e))return;const r=e._radii;return a.defined(t)?(i.Cartesian3.clone(r,t._radii),i.Cartesian3.clone(e._radiiSquared,t._radiiSquared),i.Cartesian3.clone(e._radiiToTheFourth,t._radiiToTheFourth),i.Cartesian3.clone(e._oneOverRadii,t._oneOverRadii),i.Cartesian3.clone(e._oneOverRadiiSquared,t._oneOverRadiiSquared),t._minimumRadius=e._minimumRadius,t._maximumRadius=e._maximumRadius,t._centerToleranceSquared=e._centerToleranceSquared,t):new o(r.x,r.y,r.z)},o.fromCartesian3=function(e,i){return a.defined(i)||(i=new o),a.defined(e)?(n(i,e.x,e.y,e.z),i):i},o.WGS84=Object.freeze(new o(6378137,6378137,6356752.314140356)),o.UNIT_SPHERE=Object.freeze(new o(1,1,1)),o.MOON=Object.freeze(new o(r.CesiumMath.LUNAR_RADIUS,r.CesiumMath.LUNAR_RADIUS,r.CesiumMath.LUNAR_RADIUS)),o.prototype.clone=function(e){return o.clone(this,e)},o.packedLength=i.Cartesian3.packedLength,o.pack=function(e,t,r){return r=a.defaultValue(r,0),i.Cartesian3.pack(e._radii,t,r),t},o.unpack=function(e,t,r){t=a.defaultValue(t,0);const n=i.Cartesian3.unpack(e,t);return o.fromCartesian3(n,r)},o.prototype.geocentricSurfaceNormal=i.Cartesian3.normalize,o.prototype.geodeticSurfaceNormalCartographic=function(e,t){const r=e.longitude,n=e.latitude,o=Math.cos(n),s=o*Math.cos(r),u=o*Math.sin(r),d=Math.sin(n);return a.defined(t)||(t=new i.Cartesian3),t.x=s,t.y=u,t.z=d,i.Cartesian3.normalize(t,t)},o.prototype.geodeticSurfaceNormal=function(e,t){if(!i.Cartesian3.equalsEpsilon(e,i.Cartesian3.ZERO,r.CesiumMath.EPSILON14))return a.defined(t)||(t=new i.Cartesian3),t=i.Cartesian3.multiplyComponents(e,this._oneOverRadiiSquared,t),i.Cartesian3.normalize(t,t)};const s=new i.Cartesian3,u=new i.Cartesian3;o.prototype.cartographicToCartesian=function(e,t){const r=s,n=u;this.geodeticSurfaceNormalCartographic(e,r),i.Cartesian3.multiplyComponents(this._radiiSquared,r,n);const o=Math.sqrt(i.Cartesian3.dot(r,n));return i.Cartesian3.divideByScalar(n,o,n),i.Cartesian3.multiplyByScalar(r,e.height,r),a.defined(t)||(t=new i.Cartesian3),i.Cartesian3.add(n,r,t)},o.prototype.cartographicArrayToCartesianArray=function(e,i){const t=e.length;a.defined(i)?i.length=t:i=new Array(t);for(let a=0;a<t;a++)i[a]=this.cartographicToCartesian(e[a],i[a]);return i};const d=new i.Cartesian3,c=new i.Cartesian3,h=new i.Cartesian3;o.prototype.cartesianToCartographic=function(e,n){const o=this.scaleToGeodeticSurface(e,c);if(!a.defined(o))return;const s=this.geodeticSurfaceNormal(o,d),u=i.Cartesian3.subtract(e,o,h),f=Math.atan2(s.y,s.x),l=Math.asin(s.z),m=r.CesiumMath.sign(i.Cartesian3.dot(u,e))*i.Cartesian3.magnitude(u);return a.defined(n)?(n.longitude=f,n.latitude=l,n.height=m,n):new t.Cartographic(f,l,m)},o.prototype.cartesianArrayToCartographicArray=function(e,i){const t=e.length;a.defined(i)?i.length=t:i=new Array(t);for(let a=0;a<t;++a)i[a]=this.cartesianToCartographic(e[a],i[a]);return i},o.prototype.scaleToGeodeticSurface=function(e,i){return t.scaleToGeodeticSurface(e,this._oneOverRadii,this._oneOverRadiiSquared,this._centerToleranceSquared,i)},o.prototype.scaleToGeocentricSurface=function(e,t){a.defined(t)||(t=new i.Cartesian3);const r=e.x,n=e.y,o=e.z,s=this._oneOverRadiiSquared,u=1/Math.sqrt(r*r*s.x+n*n*s.y+o*o*s.z);return i.Cartesian3.multiplyByScalar(e,u,t)},o.prototype.transformPositionToScaledSpace=function(e,t){return a.defined(t)||(t=new i.Cartesian3),i.Cartesian3.multiplyComponents(e,this._oneOverRadii,t)},o.prototype.transformPositionFromScaledSpace=function(e,t){return a.defined(t)||(t=new i.Cartesian3),i.Cartesian3.multiplyComponents(e,this._radii,t)},o.prototype.equals=function(e){return this===e||a.defined(e)&&i.Cartesian3.equals(this._radii,e._radii)},o.prototype.toString=function(){return this._radii.toString()},o.prototype.getSurfaceNormalIntersectionWithZAxis=function(e,t,r){t=a.defaultValue(t,0);const n=this._squaredXOverSquaredZ;if(a.defined(r)||(r=new i.Cartesian3),r.x=0,r.y=0,r.z=e.z*(1-n),!(Math.abs(r.z)>=this._radii.z-t))return r};const f=[.14887433898163,.43339539412925,.67940956829902,.86506336668898,.97390652851717,0],l=[.29552422471475,.26926671930999,.21908636251598,.14945134915058,.066671344308684,0];function m(e,i,t){const a=.5*(i+e),r=.5*(i-e);let n=0;for(let e=0;e<5;e++){const i=r*f[e];n+=l[e]*(t(a+i)+t(a-i))}return n*=r,n}o.prototype.surfaceArea=function(e){const i=e.west;let t=e.east;const a=e.south,n=e.north;for(;t<i;)t+=r.CesiumMath.TWO_PI;const o=this._radiiSquared,s=o.x,u=o.y,d=o.z,c=s*u;return m(a,n,(function(e){const a=Math.cos(e),r=Math.sin(e);return Math.cos(e)*m(i,t,(function(e){const i=Math.cos(e),t=Math.sin(e);return Math.sqrt(c*r*r+d*(u*i*i+s*t*t)*a*a)}))}))},e.Ellipsoid=o}));
