define(["./AxisAlignedBoundingBox-d98e5354","./Transforms-2afbbfb5","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./TerrainEncoding-443b37a0","./Math-355606c6","./OrientedBoundingBox-5f5e4f4c","./Rectangle-98b0bef0","./RuntimeError-9b4ce3fb","./WebMercatorProjection-03b5db31","./createTaskProcessorWorker","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./combine-0c102d93","./RequestType-735c98f2","./AttributeCompression-d2ca507e","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./Matrix2-e4a4609a","./TerrainQuantization-c16f42ed","./EllipsoidTangentPlane-a6ea67fb","./IntersectionTests-01432fe7","./Plane-06f34fae"],(function(t,e,n,i,o,r,a,s,c,u,h,l,d,g,m,p,I,E,T,C,f,M,x,N,S,b,w){"use strict";const B=Uint16Array.BYTES_PER_ELEMENT,P=Int32Array.BYTES_PER_ELEMENT,R=Uint32Array.BYTES_PER_ELEMENT,A=Float32Array.BYTES_PER_ELEMENT,y=Float64Array.BYTES_PER_ELEMENT;function v(t,e,n){n=r.defaultValue(n,c.CesiumMath);const i=t.length;for(let o=0;o<i;++o)if(n.equalsEpsilon(t[o],e,c.CesiumMath.EPSILON12))return o;return-1}const _=new o.Cartographic,F=new i.Cartesian3,W=new i.Cartesian3,V=new i.Cartesian3,O=new n.Matrix4;function Y(t,e,a,s,u,h,l,d,g,m,p){const I=d.length;for(let E=0;E<I;++E){const T=d[E],C=T.cartographic,f=T.index,M=t.length,x=C.longitude;let N=C.latitude;N=c.CesiumMath.clamp(N,-c.CesiumMath.PI_OVER_TWO,c.CesiumMath.PI_OVER_TWO);const S=C.height-l.skirtHeight;l.hMin=Math.min(l.hMin,S),o.Cartographic.fromRadians(x,N,S,_),m&&(_.longitude+=g),m?E===I-1?_.latitude+=p:0===E&&(_.latitude-=p):_.latitude+=g;const b=l.ellipsoid.cartographicToCartesian(_);t.push(b),e.push(S),a.push(n.Cartesian2.clone(a[f])),s.length>0&&s.push(s[f]),u.length>0&&u.push(u[f]),n.Matrix4.multiplyByPoint(l.toENU,b,F);const w=l.minimum,B=l.maximum;i.Cartesian3.minimumByComponent(F,w,w),i.Cartesian3.maximumByComponent(F,B,B);const P=l.lastBorderPoint;if(r.defined(P)){const t=P.index;h.push(t,M-1,M,M,f,t)}l.lastBorderPoint=T}}return g((function(g,m){g.ellipsoid=a.Ellipsoid.clone(g.ellipsoid),g.rectangle=h.Rectangle.clone(g.rectangle);const p=function(a,g,m,p,I,E,T,C,f,M,x){let N,S,b,w,k,H;r.defined(p)?(N=p.west,S=p.south,b=p.east,w=p.north,k=p.width,H=p.height):(N=c.CesiumMath.toRadians(I.west),S=c.CesiumMath.toRadians(I.south),b=c.CesiumMath.toRadians(I.east),w=c.CesiumMath.toRadians(I.north),k=c.CesiumMath.toRadians(p.width),H=c.CesiumMath.toRadians(p.height));let U=[],L=[];const D=1!==E,G=new DataView(a);let j=Number.POSITIVE_INFINITY,z=Number.NEGATIVE_INFINITY;const q=W;q.x=Number.POSITIVE_INFINITY,q.y=Number.POSITIVE_INFINITY,q.z=Number.POSITIVE_INFINITY;const Q=V;Q.x=Number.NEGATIVE_INFINITY,Q.y=Number.NEGATIVE_INFINITY,Q.z=Number.NEGATIVE_INFINITY;let J,K,X=0,Z=0,$=0;for(K=0;K<4;++K){let t=X;J=G.getUint32(t,!0),t+=R;const e=c.CesiumMath.toRadians(180*G.getFloat64(t,!0));t+=y,-1===v(L,e)&&L.push(e);const n=c.CesiumMath.toRadians(180*G.getFloat64(t,!0));t+=y,-1===v(U,n)&&U.push(n),t+=2*y;let i=G.getInt32(t,!0);t+=P,Z+=i,i=G.getInt32(t,!0),$+=3*i,X+=J+R}U=U.sort(),L=L.sort(),k=2*(L[1]-L[0]),H=2*(U[1]-U[0]),U.push(U[0]+H),L.push(L[0]+k),N=L[0],S=U[0],b=L[L.length-1],w=U[U.length-1],p=new h.Rectangle(N,S,b,w),g=m.cartographicToCartesian(h.Rectangle.center(p));const tt=e.Transforms.eastNorthUpToFixedFrame(g,m),et=n.Matrix4.inverseTransformation(tt,O);let nt,it;f&&(nt=d.WebMercatorProjection.geodeticLatitudeToMercatorAngle(S),it=1/(d.WebMercatorProjection.geodeticLatitudeToMercatorAngle(w)-nt));const ot=[],rt=[],at=new Array(Z),st=new Array(Z),ct=new Array(Z),ut=f?new Array(Z):[],ht=D?new Array(Z):[],lt=new Array($),dt=[],gt=[],mt=[],pt=[];let It=0,Et=0;for(X=0,K=0;K<4;++K){J=G.getUint32(X,!0),X+=R;const t=X,e=c.CesiumMath.toRadians(180*G.getFloat64(X,!0));X+=y;const r=c.CesiumMath.toRadians(180*G.getFloat64(X,!0));X+=y;const a=c.CesiumMath.toRadians(180*G.getFloat64(X,!0)),s=.5*a;X+=y;const u=c.CesiumMath.toRadians(180*G.getFloat64(X,!0)),h=.5*u;X+=y;const g=G.getInt32(X,!0);X+=P;const p=G.getInt32(X,!0);X+=P,X+=P;const I=new Array(g);for(let t=0;t<g;++t){const l=e+G.getUint8(X++)*a;_.longitude=l;const g=r+G.getUint8(X++)*u;_.latitude=g;let p=G.getFloat32(X,!0);if(X+=A,0!==p&&p<x&&(p*=-Math.pow(2,M)),p*=6371010,_.height=p,-1!==v(L,l)||-1!==v(U,g)){const e=v(ot,_,o.Cartographic);if(-1!==e){I[t]=rt[e];continue}ot.push(o.Cartographic.clone(_)),rt.push(It)}I[t]=It,Math.abs(l-N)<s?dt.push({index:It,cartographic:o.Cartographic.clone(_)}):Math.abs(l-b)<s?mt.push({index:It,cartographic:o.Cartographic.clone(_)}):Math.abs(g-S)<h?gt.push({index:It,cartographic:o.Cartographic.clone(_)}):Math.abs(g-w)<h&&pt.push({index:It,cartographic:o.Cartographic.clone(_)}),j=Math.min(p,j),z=Math.max(p,z),ct[It]=p;const E=m.cartographicToCartesian(_);if(at[It]=E,f&&(ut[It]=(d.WebMercatorProjection.geodeticLatitudeToMercatorAngle(g)-nt)*it),D){const t=m.geodeticSurfaceNormal(E);ht[It]=t}n.Matrix4.multiplyByPoint(et,E,F),i.Cartesian3.minimumByComponent(F,q,q),i.Cartesian3.maximumByComponent(F,Q,Q);let T=(l-N)/(b-N);T=c.CesiumMath.clamp(T,0,1);let C=(g-S)/(w-S);C=c.CesiumMath.clamp(C,0,1),st[It]=new n.Cartesian2(T,C),++It}const E=3*p;for(let t=0;t<E;++t,++Et)lt[Et]=I[G.getUint16(X,!0)],X+=B;if(J!==X-t)throw new l.RuntimeError("Invalid terrain tile.")}at.length=It,st.length=It,ct.length=It,f&&(ut.length=It);D&&(ht.length=It);const Tt=It,Ct=Et,ft={hMin:j,lastBorderPoint:void 0,skirtHeight:C,toENU:et,ellipsoid:m,minimum:q,maximum:Q};dt.sort((function(t,e){return e.cartographic.latitude-t.cartographic.latitude})),gt.sort((function(t,e){return t.cartographic.longitude-e.cartographic.longitude})),mt.sort((function(t,e){return t.cartographic.latitude-e.cartographic.latitude})),pt.sort((function(t,e){return e.cartographic.longitude-t.cartographic.longitude}));const Mt=1e-5;if(Y(at,ct,st,ut,ht,lt,ft,dt,-Mt*k,!0,-Mt*H),Y(at,ct,st,ut,ht,lt,ft,gt,-Mt*H,!1),Y(at,ct,st,ut,ht,lt,ft,mt,Mt*k,!0,Mt*H),Y(at,ct,st,ut,ht,lt,ft,pt,Mt*H,!1),dt.length>0&&pt.length>0){const t=dt[0].index,e=Tt,n=pt[pt.length-1].index,i=at.length-1;lt.push(n,i,e,e,t,n)}Z=at.length;const xt=e.BoundingSphere.fromPoints(at);let Nt;r.defined(p)&&(Nt=u.OrientedBoundingBox.fromRectangle(p,j,z,m));const St=new s.EllipsoidalOccluder(m).computeHorizonCullingPointPossiblyUnderEllipsoid(g,at,j),bt=new t.AxisAlignedBoundingBox(q,Q,g),wt=new s.TerrainEncoding(g,bt,ft.hMin,z,tt,!1,f,D,E,T),Bt=new Float32Array(Z*wt.stride);let Pt=0;for(let t=0;t<Z;++t)Pt=wt.encode(Bt,Pt,at[t],st[t],ct[t],void 0,ut[t],ht[t]);const Rt=dt.map((function(t){return t.index})).reverse(),At=gt.map((function(t){return t.index})).reverse(),yt=mt.map((function(t){return t.index})).reverse(),vt=pt.map((function(t){return t.index})).reverse();return At.unshift(yt[yt.length-1]),At.push(Rt[0]),vt.unshift(Rt[Rt.length-1]),vt.push(yt[0]),{vertices:Bt,indices:new Uint16Array(lt),maximumHeight:z,minimumHeight:j,encoding:wt,boundingSphere3D:xt,orientedBoundingBox:Nt,occludeePointInScaledSpace:St,vertexCountWithoutSkirts:Tt,indexCountWithoutSkirts:Ct,westIndicesSouthToNorth:Rt,southIndicesEastToWest:At,eastIndicesNorthToSouth:yt,northIndicesWestToEast:vt}}(g.buffer,g.relativeToCenter,g.ellipsoid,g.rectangle,g.nativeRectangle,g.exaggeration,g.exaggerationRelativeHeight,g.skirtHeight,g.includeWebMercatorT,g.negativeAltitudeExponentBias,g.negativeElevationThreshold),I=p.vertices;m.push(I.buffer);const E=p.indices;return m.push(E.buffer),{vertices:I.buffer,indices:E.buffer,numberOfAttributes:p.encoding.stride,minimumHeight:p.minimumHeight,maximumHeight:p.maximumHeight,boundingSphere3D:p.boundingSphere3D,orientedBoundingBox:p.orientedBoundingBox,occludeePointInScaledSpace:p.occludeePointInScaledSpace,encoding:p.encoding,vertexCountWithoutSkirts:p.vertexCountWithoutSkirts,indexCountWithoutSkirts:p.indexCountWithoutSkirts,westIndicesSouthToNorth:p.westIndicesSouthToNorth,southIndicesEastToWest:p.southIndicesEastToWest,eastIndicesNorthToSouth:p.eastIndicesNorthToSouth,northIndicesWestToEast:p.northIndicesWestToEast}}))}));
