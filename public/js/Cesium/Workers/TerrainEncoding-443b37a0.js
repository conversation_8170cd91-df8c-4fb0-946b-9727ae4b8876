define(["exports","./Transforms-2afbbfb5","./Cartesian3-529c236c","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./Rectangle-98b0bef0","./AttributeCompression-d2ca507e","./Matrix4-c57ffbd8","./ComponentDatatype-ab629b88","./Math-355606c6","./TerrainQuantization-c16f42ed"],(function(t,e,i,a,n,o,r,s,c,u,d){"use strict";function l(t,e){this._ellipsoid=t,this._cameraPosition=new i.Cartesian3,this._cameraPositionInScaledSpace=new i.Cartesian3,this._distanceToLimbInScaledSpaceSquared=0,a.defined(e)&&(this.cameraPosition=e)}Object.defineProperties(l.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},cameraPosition:{get:function(){return this._cameraPosition},set:function(t){const e=this._ellipsoid.transformPositionToScaledSpace(t,this._cameraPositionInScaledSpace),a=i.Cartesian3.magnitudeSquared(e)-1;i.Cartesian3.clone(t,this._cameraPosition),this._cameraPositionInScaledSpace=e,this._distanceToLimbInScaledSpaceSquared=a}}});const m=new i.Cartesian3;l.prototype.isPointVisible=function(t){return T(this._ellipsoid.transformPositionToScaledSpace(t,m),this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)},l.prototype.isScaledSpacePointVisible=function(t){return T(t,this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)};const h=new i.Cartesian3;l.prototype.isScaledSpacePointVisiblePossiblyUnderEllipsoid=function(t,e){const i=this._ellipsoid;let n,o;return a.defined(e)&&e<0&&i.minimumRadius>-e?(o=h,o.x=this._cameraPosition.x/(i.radii.x+e),o.y=this._cameraPosition.y/(i.radii.y+e),o.z=this._cameraPosition.z/(i.radii.z+e),n=o.x*o.x+o.y*o.y+o.z*o.z-1):(o=this._cameraPositionInScaledSpace,n=this._distanceToLimbInScaledSpaceSquared),T(t,o,n)},l.prototype.computeHorizonCullingPoint=function(t,e,i){return C(this._ellipsoid,t,e,i)};const f=n.Ellipsoid.clone(n.Ellipsoid.UNIT_SPHERE);l.prototype.computeHorizonCullingPointPossiblyUnderEllipsoid=function(t,e,i,a){return C(S(this._ellipsoid,i,f),t,e,a)},l.prototype.computeHorizonCullingPointFromVertices=function(t,e,i,a,n){return y(this._ellipsoid,t,e,i,a,n)},l.prototype.computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid=function(t,e,i,a,n,o){return y(S(this._ellipsoid,n,f),t,e,i,a,o)};const p=[];l.prototype.computeHorizonCullingPointFromRectangle=function(t,a,n){const r=o.Rectangle.subsample(t,a,0,p),s=e.BoundingSphere.fromPoints(r);if(!(i.Cartesian3.magnitude(s.center)<.1*a.minimumRadius))return this.computeHorizonCullingPoint(s.center,r,n)};const x=new i.Cartesian3;function S(t,e,o){if(a.defined(e)&&e<0&&t.minimumRadius>-e){const a=i.Cartesian3.fromElements(t.radii.x+e,t.radii.y+e,t.radii.z+e,x);t=n.Ellipsoid.fromCartesian3(a,o)}return t}function C(t,e,n,o){a.defined(o)||(o=new i.Cartesian3);const r=_(t,e);let s=0;for(let e=0,i=n.length;e<i;++e){const i=M(t,n[e],r);if(i<0)return;s=Math.max(s,i)}return P(r,s,o)}const g=new i.Cartesian3;function y(t,e,n,o,r,s){a.defined(s)||(s=new i.Cartesian3),o=a.defaultValue(o,3),r=a.defaultValue(r,i.Cartesian3.ZERO);const c=_(t,e);let u=0;for(let e=0,i=n.length;e<i;e+=o){g.x=n[e]+r.x,g.y=n[e+1]+r.y,g.z=n[e+2]+r.z;const i=M(t,g,c);if(i<0)return;u=Math.max(u,i)}return P(c,u,s)}function T(t,e,a){const n=e,o=a,r=i.Cartesian3.subtract(t,n,m),s=-i.Cartesian3.dot(r,n);return!(o<0?s>0:s>o&&s*s/i.Cartesian3.magnitudeSquared(r)>o)}const N=new i.Cartesian3,z=new i.Cartesian3;function M(t,e,a){const n=t.transformPositionToScaledSpace(e,N);let o=i.Cartesian3.magnitudeSquared(n),r=Math.sqrt(o);const s=i.Cartesian3.divideByScalar(n,r,z);o=Math.max(1,o),r=Math.max(1,r);const c=1/r;return 1/(i.Cartesian3.dot(s,a)*c-i.Cartesian3.magnitude(i.Cartesian3.cross(s,a,s))*(Math.sqrt(o-1)*c))}function P(t,e,a){if(!(e<=0||e===1/0||e!=e))return i.Cartesian3.multiplyByScalar(t,e,a)}const b=new i.Cartesian3;function _(t,e){return i.Cartesian3.equals(e,i.Cartesian3.ZERO)?e:(t.transformPositionToScaledSpace(e,b),i.Cartesian3.normalize(b,b))}const E={getHeight:function(t,e,i){return(t-i)*e+i}},H=new i.Cartesian3;E.getPosition=function(t,e,a,n,o){const r=e.cartesianToCartographic(t,H),s=E.getHeight(r.height,a,n);return i.Cartesian3.fromRadians(r.longitude,r.latitude,s,e,o)};var w=E;const A=new i.Cartesian3,V=new i.Cartesian3,q=new s.Cartesian2,I=new s.Matrix4,G=new s.Matrix4,B=Math.pow(2,12);function O(t,e,n,o,r,c,u,l,m,h){let f,p,x=d.TerrainQuantization.NONE;if(a.defined(e)&&a.defined(n)&&a.defined(o)&&a.defined(r)){const t=e.minimum,a=e.maximum,c=i.Cartesian3.subtract(a,t,V),u=o-n;x=Math.max(i.Cartesian3.maximumComponent(c),u)<B-1?d.TerrainQuantization.BITS12:d.TerrainQuantization.NONE,f=s.Matrix4.inverseTransformation(r,new s.Matrix4);const l=i.Cartesian3.negate(t,A);s.Matrix4.multiply(s.Matrix4.fromTranslation(l,I),f,f);const m=A;m.x=1/c.x,m.y=1/c.y,m.z=1/c.z,s.Matrix4.multiply(s.Matrix4.fromScale(m,I),f,f),p=s.Matrix4.clone(r),s.Matrix4.setTranslation(p,i.Cartesian3.ZERO,p),r=s.Matrix4.clone(r,new s.Matrix4);const h=s.Matrix4.fromTranslation(t,I),S=s.Matrix4.fromScale(c,G),C=s.Matrix4.multiply(h,S,I);s.Matrix4.multiply(r,C,r),s.Matrix4.multiply(p,C,p)}this.quantization=x,this.minimumHeight=n,this.maximumHeight=o,this.center=i.Cartesian3.clone(t),this.toScaledENU=f,this.fromScaledENU=r,this.matrix=p,this.hasVertexNormals=c,this.hasWebMercatorT=a.defaultValue(u,!1),this.hasGeodeticSurfaceNormals=a.defaultValue(l,!1),this.exaggeration=a.defaultValue(m,1),this.exaggerationRelativeHeight=a.defaultValue(h,0),this.stride=0,this._offsetGeodeticSurfaceNormal=0,this._offsetVertexNormal=0,this._calculateStrideAndOffsets()}O.prototype.encode=function(t,e,a,n,o,c,l,m){const h=n.x,f=n.y;if(this.quantization===d.TerrainQuantization.BITS12){(a=s.Matrix4.multiplyByPoint(this.toScaledENU,a,A)).x=u.CesiumMath.clamp(a.x,0,1),a.y=u.CesiumMath.clamp(a.y,0,1),a.z=u.CesiumMath.clamp(a.z,0,1);const i=this.maximumHeight-this.minimumHeight,n=u.CesiumMath.clamp((o-this.minimumHeight)/i,0,1);s.Cartesian2.fromElements(a.x,a.y,q);const c=r.AttributeCompression.compressTextureCoordinates(q);s.Cartesian2.fromElements(a.z,n,q);const d=r.AttributeCompression.compressTextureCoordinates(q);s.Cartesian2.fromElements(h,f,q);const m=r.AttributeCompression.compressTextureCoordinates(q);if(t[e++]=c,t[e++]=d,t[e++]=m,this.hasWebMercatorT){s.Cartesian2.fromElements(l,0,q);const i=r.AttributeCompression.compressTextureCoordinates(q);t[e++]=i}}else i.Cartesian3.subtract(a,this.center,A),t[e++]=A.x,t[e++]=A.y,t[e++]=A.z,t[e++]=o,t[e++]=h,t[e++]=f,this.hasWebMercatorT&&(t[e++]=l);return this.hasVertexNormals&&(t[e++]=r.AttributeCompression.octPackFloat(c)),this.hasGeodeticSurfaceNormals&&(t[e++]=m.x,t[e++]=m.y,t[e++]=m.z),e};const R=new i.Cartesian3,Q=new i.Cartesian3;O.prototype.addGeodeticSurfaceNormals=function(t,e,i){if(this.hasGeodeticSurfaceNormals)return;const a=this.stride,n=t.length/a;this.hasGeodeticSurfaceNormals=!0,this._calculateStrideAndOffsets();const o=this.stride;for(let r=0;r<n;r++){for(let i=0;i<a;i++){const n=r*a+i;e[r*o+i]=t[n]}const n=this.decodePosition(e,r,R),s=i.geodeticSurfaceNormal(n,Q),c=r*o+this._offsetGeodeticSurfaceNormal;e[c]=s.x,e[c+1]=s.y,e[c+2]=s.z}},O.prototype.removeGeodeticSurfaceNormals=function(t,e){if(!this.hasGeodeticSurfaceNormals)return;const i=this.stride,a=t.length/i;this.hasGeodeticSurfaceNormals=!1,this._calculateStrideAndOffsets();const n=this.stride;for(let o=0;o<a;o++)for(let a=0;a<n;a++){const r=o*i+a;e[o*n+a]=t[r]}},O.prototype.decodePosition=function(t,e,n){if(a.defined(n)||(n=new i.Cartesian3),e*=this.stride,this.quantization===d.TerrainQuantization.BITS12){const i=r.AttributeCompression.decompressTextureCoordinates(t[e],q);n.x=i.x,n.y=i.y;const a=r.AttributeCompression.decompressTextureCoordinates(t[e+1],q);return n.z=a.x,s.Matrix4.multiplyByPoint(this.fromScaledENU,n,n)}return n.x=t[e],n.y=t[e+1],n.z=t[e+2],i.Cartesian3.add(n,this.center,n)},O.prototype.getExaggeratedPosition=function(t,e,i){i=this.decodePosition(t,e,i);const a=this.exaggeration,n=this.exaggerationRelativeHeight;if(1!==a&&this.hasGeodeticSurfaceNormals){const o=this.decodeGeodeticSurfaceNormal(t,e,Q),r=this.decodeHeight(t,e),s=w.getHeight(r,a,n)-r;i.x+=o.x*s,i.y+=o.y*s,i.z+=o.z*s}return i},O.prototype.decodeTextureCoordinates=function(t,e,i){return a.defined(i)||(i=new s.Cartesian2),e*=this.stride,this.quantization===d.TerrainQuantization.BITS12?r.AttributeCompression.decompressTextureCoordinates(t[e+2],i):s.Cartesian2.fromElements(t[e+4],t[e+5],i)},O.prototype.decodeHeight=function(t,e){if(e*=this.stride,this.quantization===d.TerrainQuantization.BITS12){return r.AttributeCompression.decompressTextureCoordinates(t[e+1],q).y*(this.maximumHeight-this.minimumHeight)+this.minimumHeight}return t[e+3]},O.prototype.decodeWebMercatorT=function(t,e){return e*=this.stride,this.quantization===d.TerrainQuantization.BITS12?r.AttributeCompression.decompressTextureCoordinates(t[e+3],q).x:t[e+6]},O.prototype.getOctEncodedNormal=function(t,e,i){const a=t[e=e*this.stride+this._offsetVertexNormal]/256,n=Math.floor(a),o=256*(a-n);return s.Cartesian2.fromElements(n,o,i)},O.prototype.decodeGeodeticSurfaceNormal=function(t,e,i){return e=e*this.stride+this._offsetGeodeticSurfaceNormal,i.x=t[e],i.y=t[e+1],i.z=t[e+2],i},O.prototype._calculateStrideAndOffsets=function(){let t=0;if(this.quantization===d.TerrainQuantization.BITS12)t+=3;else t+=6;this.hasWebMercatorT&&(t+=1),this.hasVertexNormals&&(this._offsetVertexNormal=t,t+=1),this.hasGeodeticSurfaceNormals&&(this._offsetGeodeticSurfaceNormal=t,t+=3),this.stride=t};const U={position3DAndHeight:0,textureCoordAndEncodedNormals:1,geodeticSurfaceNormal:2},W={compressed0:0,compressed1:1,geodeticSurfaceNormal:2};O.prototype.getAttributes=function(t){const e=c.ComponentDatatype.FLOAT,i=c.ComponentDatatype.getSizeInBytes(e),a=this.stride*i;let n=0;const o=[];function r(r,s){o.push({index:r,vertexBuffer:t,componentDatatype:e,componentsPerAttribute:s,offsetInBytes:n,strideInBytes:a}),n+=s*i}if(this.quantization===d.TerrainQuantization.NONE){r(U.position3DAndHeight,4);let t=2;t+=this.hasWebMercatorT?1:0,t+=this.hasVertexNormals?1:0,r(U.textureCoordAndEncodedNormals,t),this.hasGeodeticSurfaceNormals&&r(U.geodeticSurfaceNormal,3)}else{const t=this.hasWebMercatorT||this.hasVertexNormals,e=this.hasWebMercatorT&&this.hasVertexNormals;r(W.compressed0,t?4:3),e&&r(W.compressed1,1),this.hasGeodeticSurfaceNormals&&r(W.geodeticSurfaceNormal,3)}return o},O.prototype.getAttributeLocations=function(){return this.quantization===d.TerrainQuantization.NONE?U:W},O.clone=function(t,e){if(a.defined(t))return a.defined(e)||(e=new O),e.quantization=t.quantization,e.minimumHeight=t.minimumHeight,e.maximumHeight=t.maximumHeight,e.center=i.Cartesian3.clone(t.center),e.toScaledENU=s.Matrix4.clone(t.toScaledENU),e.fromScaledENU=s.Matrix4.clone(t.fromScaledENU),e.matrix=s.Matrix4.clone(t.matrix),e.hasVertexNormals=t.hasVertexNormals,e.hasWebMercatorT=t.hasWebMercatorT,e.hasGeodeticSurfaceNormals=t.hasGeodeticSurfaceNormals,e.exaggeration=t.exaggeration,e.exaggerationRelativeHeight=t.exaggerationRelativeHeight,e._calculateStrideAndOffsets(),e},t.EllipsoidalOccluder=l,t.TerrainEncoding=O}));
