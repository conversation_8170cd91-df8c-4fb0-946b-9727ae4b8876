define(["./defaultValue-f6d5e6da","./EllipsoidOutlineGeometry-bba37f0d","./Transforms-2afbbfb5","./Cartesian3-529c236c","./Math-355606c6","./Cartographic-dbefb6fa","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./GeometryAttribute-f7a0845b","./Matrix2-e4a4609a","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-58eb7805"],(function(e,t,r,i,n,o,a,u,l,s,m,f,p,d,y,c,G,b,E,x,C,M){"use strict";return function(r,i){return e.defined(r.buffer)&&(r=t.EllipsoidOutlineGeometry.unpack(r,i)),t.EllipsoidOutlineGeometry.createGeometry(r)}}));
