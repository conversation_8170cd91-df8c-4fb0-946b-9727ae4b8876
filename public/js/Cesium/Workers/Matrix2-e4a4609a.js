define(["exports","./Matrix4-c57ffbd8","./defaultValue-f6d5e6da"],(function(n,t,e){"use strict";function r(n,t,r,u){this[0]=e.defaultValue(n,0),this[1]=e.defaultValue(r,0),this[2]=e.defaultValue(t,0),this[3]=e.defaultValue(u,0)}r.packedLength=4,r.pack=function(n,t,r){return r=e.defaultValue(r,0),t[r++]=n[0],t[r++]=n[1],t[r++]=n[2],t[r++]=n[3],t},r.unpack=function(n,t,u){return t=e.defaultValue(t,0),e.defined(u)||(u=new r),u[0]=n[t++],u[1]=n[t++],u[2]=n[t++],u[3]=n[t++],u},r.packArray=function(n,t){const u=n.length,o=4*u;e.defined(t)?(Array.isArray(t)||t.length===o)&&t.length!==o&&(t.length=o):t=new Array(o);for(let e=0;e<u;++e)r.pack(n[e],t,4*e);return t},r.unpackArray=function(n,t){const u=n.length;e.defined(t)?t.length=u/4:t=new Array(u/4);for(let e=0;e<u;e+=4){const u=e/4;t[u]=r.unpack(n,e,t[u])}return t},r.clone=function(n,t){if(e.defined(n))return e.defined(t)?(t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t):new r(n[0],n[2],n[1],n[3])},r.fromArray=r.unpack,r.fromColumnMajorArray=function(n,t){return r.clone(n,t)},r.fromRowMajorArray=function(n,t){return e.defined(t)?(t[0]=n[0],t[1]=n[2],t[2]=n[1],t[3]=n[3],t):new r(n[0],n[1],n[2],n[3])},r.fromScale=function(n,t){return e.defined(t)?(t[0]=n.x,t[1]=0,t[2]=0,t[3]=n.y,t):new r(n.x,0,0,n.y)},r.fromUniformScale=function(n,t){return e.defined(t)?(t[0]=n,t[1]=0,t[2]=0,t[3]=n,t):new r(n,0,0,n)},r.fromRotation=function(n,t){const u=Math.cos(n),o=Math.sin(n);return e.defined(t)?(t[0]=u,t[1]=o,t[2]=-o,t[3]=u,t):new r(u,-o,o,u)},r.toArray=function(n,t){return e.defined(t)?(t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t):[n[0],n[1],n[2],n[3]]},r.getElementIndex=function(n,t){return 2*n+t},r.getColumn=function(n,t,e){const r=2*t,u=n[r],o=n[r+1];return e.x=u,e.y=o,e},r.setColumn=function(n,t,e,u){const o=2*t;return(u=r.clone(n,u))[o]=e.x,u[o+1]=e.y,u},r.getRow=function(n,t,e){const r=n[t],u=n[t+2];return e.x=r,e.y=u,e},r.setRow=function(n,t,e,u){return(u=r.clone(n,u))[t]=e.x,u[t+2]=e.y,u};const u=new t.Cartesian2;r.setScale=function(n,t,e){const o=r.getScale(n,u),a=t.x/o.x,i=t.y/o.y;return e[0]=n[0]*a,e[1]=n[1]*a,e[2]=n[2]*i,e[3]=n[3]*i,e};const o=new t.Cartesian2;r.setUniformScale=function(n,t,e){const u=r.getScale(n,o),a=t/u.x,i=t/u.y;return e[0]=n[0]*a,e[1]=n[1]*a,e[2]=n[2]*i,e[3]=n[3]*i,e};const a=new t.Cartesian2;r.getScale=function(n,e){return e.x=t.Cartesian2.magnitude(t.Cartesian2.fromElements(n[0],n[1],a)),e.y=t.Cartesian2.magnitude(t.Cartesian2.fromElements(n[2],n[3],a)),e};const i=new t.Cartesian2;r.getMaximumScale=function(n){return r.getScale(n,i),t.Cartesian2.maximumComponent(i)};const c=new t.Cartesian2;r.setRotation=function(n,t,e){const u=r.getScale(n,c);return e[0]=t[0]*u.x,e[1]=t[1]*u.x,e[2]=t[2]*u.y,e[3]=t[3]*u.y,e};const f=new t.Cartesian2;r.getRotation=function(n,t){const e=r.getScale(n,f);return t[0]=n[0]/e.x,t[1]=n[1]/e.x,t[2]=n[2]/e.y,t[3]=n[3]/e.y,t},r.multiply=function(n,t,e){const r=n[0]*t[0]+n[2]*t[1],u=n[0]*t[2]+n[2]*t[3],o=n[1]*t[0]+n[3]*t[1],a=n[1]*t[2]+n[3]*t[3];return e[0]=r,e[1]=o,e[2]=u,e[3]=a,e},r.add=function(n,t,e){return e[0]=n[0]+t[0],e[1]=n[1]+t[1],e[2]=n[2]+t[2],e[3]=n[3]+t[3],e},r.subtract=function(n,t,e){return e[0]=n[0]-t[0],e[1]=n[1]-t[1],e[2]=n[2]-t[2],e[3]=n[3]-t[3],e},r.multiplyByVector=function(n,t,e){const r=n[0]*t.x+n[2]*t.y,u=n[1]*t.x+n[3]*t.y;return e.x=r,e.y=u,e},r.multiplyByScalar=function(n,t,e){return e[0]=n[0]*t,e[1]=n[1]*t,e[2]=n[2]*t,e[3]=n[3]*t,e},r.multiplyByScale=function(n,t,e){return e[0]=n[0]*t.x,e[1]=n[1]*t.x,e[2]=n[2]*t.y,e[3]=n[3]*t.y,e},r.multiplyByUniformScale=function(n,t,e){return e[0]=n[0]*t,e[1]=n[1]*t,e[2]=n[2]*t,e[3]=n[3]*t,e},r.negate=function(n,t){return t[0]=-n[0],t[1]=-n[1],t[2]=-n[2],t[3]=-n[3],t},r.transpose=function(n,t){const e=n[0],r=n[2],u=n[1],o=n[3];return t[0]=e,t[1]=r,t[2]=u,t[3]=o,t},r.abs=function(n,t){return t[0]=Math.abs(n[0]),t[1]=Math.abs(n[1]),t[2]=Math.abs(n[2]),t[3]=Math.abs(n[3]),t},r.equals=function(n,t){return n===t||e.defined(n)&&e.defined(t)&&n[0]===t[0]&&n[1]===t[1]&&n[2]===t[2]&&n[3]===t[3]},r.equalsArray=function(n,t,e){return n[0]===t[e]&&n[1]===t[e+1]&&n[2]===t[e+2]&&n[3]===t[e+3]},r.equalsEpsilon=function(n,t,r){return r=e.defaultValue(r,0),n===t||e.defined(n)&&e.defined(t)&&Math.abs(n[0]-t[0])<=r&&Math.abs(n[1]-t[1])<=r&&Math.abs(n[2]-t[2])<=r&&Math.abs(n[3]-t[3])<=r},r.IDENTITY=Object.freeze(new r(1,0,0,1)),r.ZERO=Object.freeze(new r(0,0,0,0)),r.COLUMN0ROW0=0,r.COLUMN0ROW1=1,r.COLUMN1ROW0=2,r.COLUMN1ROW1=3,Object.defineProperties(r.prototype,{length:{get:function(){return r.packedLength}}}),r.prototype.clone=function(n){return r.clone(this,n)},r.prototype.equals=function(n){return r.equals(this,n)},r.prototype.equalsEpsilon=function(n,t){return r.equalsEpsilon(this,n,t)},r.prototype.toString=function(){return`(${this[0]}, ${this[2]})\n(${this[1]}, ${this[3]})`},n.Matrix2=r}));
