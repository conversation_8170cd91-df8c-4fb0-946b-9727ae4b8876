define(["exports","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./EllipsoidGeodesic-8ac7b85d","./EllipsoidRhumbLine-81dc828b","./IntersectionTests-01432fe7","./Math-355606c6","./Matrix4-c57ffbd8","./Plane-06f34fae"],(function(e,t,a,n,i,r,o,s,c,l,u){"use strict";const h={numberOfPoints:function(e,a,n){const i=t.Cartesian3.distance(e,a);return Math.ceil(i/n)},numberOfPointsRhumbLine:function(e,t,a){const n=Math.pow(e.longitude-t.longitude,2)+Math.pow(e.latitude-t.latitude,2);return Math.max(1,Math.ceil(Math.sqrt(n/(a*a))))}},f=new a.Cartographic;h.extractHeights=function(e,t){const a=e.length,n=new Array(a);for(let i=0;i<a;i++){const a=e[i];n[i]=t.cartesianToCartographic(a,f).height}return n};const g=new l.Matrix4,C=new t.Cartesian3,p=new t.Cartesian3,d=new u.Plane(t.Cartesian3.UNIT_X,0),m=new t.Cartesian3,w=new u.Plane(t.Cartesian3.UNIT_X,0),P=new t.Cartesian3,T=new t.Cartesian3,y=[];function A(e,t,a){const n=y;let i;if(n.length=e,t===a){for(i=0;i<e;i++)n[i]=t;return n}const r=(a-t)/e;for(i=0;i<e;i++){const e=t+i*r;n[i]=e}return n}const E=new a.Cartographic,M=new a.Cartographic,R=new t.Cartesian3,S=new t.Cartesian3,b=new t.Cartesian3,x=new r.EllipsoidGeodesic;let D=new o.EllipsoidRhumbLine;function N(e,a,n,i,r,o,s,c){const l=i.scaleToGeodeticSurface(e,S),u=i.scaleToGeodeticSurface(a,b),f=h.numberOfPoints(e,a,n),g=i.cartesianToCartographic(l,E),C=i.cartesianToCartographic(u,M),p=A(f,r,o);x.setEndPoints(g,C);const d=x.surfaceDistance/f;let m=c;g.height=r;let w=i.cartographicToCartesian(g,R);t.Cartesian3.pack(w,s,m),m+=3;for(let e=1;e<f;e++){const a=x.interpolateUsingSurfaceDistance(e*d,M);a.height=p[e],w=i.cartographicToCartesian(a,R),t.Cartesian3.pack(w,s,m),m+=3}return m}function G(e,a,n,i,r,s,c,l){const u=i.cartesianToCartographic(e,E),f=i.cartesianToCartographic(a,M),g=h.numberOfPointsRhumbLine(u,f,n);u.height=0,f.height=0;const C=A(g,r,s);D.ellipsoid.equals(i)||(D=new o.EllipsoidRhumbLine(void 0,void 0,i)),D.setEndPoints(u,f);const p=D.surfaceDistance/g;let d=l;u.height=r;let m=i.cartographicToCartesian(u,R);t.Cartesian3.pack(m,c,d),d+=3;for(let e=1;e<g;e++){const a=D.interpolateUsingSurfaceDistance(e*p,M);a.height=C[e],m=i.cartographicToCartesian(a,R),t.Cartesian3.pack(m,c,d),d+=3}return d}h.wrapLongitude=function(e,a){const i=[],r=[];if(n.defined(e)&&e.length>0){a=n.defaultValue(a,l.Matrix4.IDENTITY);const o=l.Matrix4.inverseTransformation(a,g),c=l.Matrix4.multiplyByPoint(o,t.Cartesian3.ZERO,C),h=t.Cartesian3.normalize(l.Matrix4.multiplyByPointAsVector(o,t.Cartesian3.UNIT_Y,p),p),f=u.Plane.fromPointNormal(c,h,d),y=t.Cartesian3.normalize(l.Matrix4.multiplyByPointAsVector(o,t.Cartesian3.UNIT_X,m),m),A=u.Plane.fromPointNormal(c,y,w);let E=1;i.push(t.Cartesian3.clone(e[0]));let M=i[0];const R=e.length;for(let a=1;a<R;++a){const o=e[a];if(u.Plane.getPointDistance(A,M)<0||u.Plane.getPointDistance(A,o)<0){const e=s.IntersectionTests.lineSegmentPlane(M,o,f,P);if(n.defined(e)){const a=t.Cartesian3.multiplyByScalar(h,5e-9,T);u.Plane.getPointDistance(f,M)<0&&t.Cartesian3.negate(a,a),i.push(t.Cartesian3.add(e,a,new t.Cartesian3)),r.push(E+1),t.Cartesian3.negate(a,a),i.push(t.Cartesian3.add(e,a,new t.Cartesian3)),E=1}}i.push(t.Cartesian3.clone(e[a])),E++,M=o}r.push(E)}return{positions:i,lengths:r}},h.generateArc=function(e){n.defined(e)||(e={});const a=e.positions,r=a.length,o=n.defaultValue(e.ellipsoid,i.Ellipsoid.WGS84);let s=n.defaultValue(e.height,0);const l=Array.isArray(s);if(r<1)return[];if(1===r){const e=o.scaleToGeodeticSurface(a[0],S);if(s=l?s[0]:s,0!==s){const a=o.geodeticSurfaceNormal(e,R);t.Cartesian3.multiplyByScalar(a,s,a),t.Cartesian3.add(e,a,e)}return[e.x,e.y,e.z]}let u=e.minDistance;if(!n.defined(u)){const t=n.defaultValue(e.granularity,c.CesiumMath.RADIANS_PER_DEGREE);u=c.CesiumMath.chordLength(t,o.maximumRadius)}let f,g=0;for(f=0;f<r-1;f++)g+=h.numberOfPoints(a[f],a[f+1],u);const C=3*(g+1),p=new Array(C);let d=0;for(f=0;f<r-1;f++){d=N(a[f],a[f+1],u,o,l?s[f]:s,l?s[f+1]:s,p,d)}y.length=0;const m=a[r-1],w=o.cartesianToCartographic(m,E);w.height=l?s[r-1]:s;const P=o.cartographicToCartesian(w,R);return t.Cartesian3.pack(P,p,C-3),p};const I=new a.Cartographic,V=new a.Cartographic;h.generateRhumbArc=function(e){n.defined(e)||(e={});const r=e.positions,o=r.length,s=n.defaultValue(e.ellipsoid,i.Ellipsoid.WGS84);let l=n.defaultValue(e.height,0);const u=Array.isArray(l);if(o<1)return[];if(1===o){const e=s.scaleToGeodeticSurface(r[0],S);if(l=u?l[0]:l,0!==l){const a=s.geodeticSurfaceNormal(e,R);t.Cartesian3.multiplyByScalar(a,l,a),t.Cartesian3.add(e,a,e)}return[e.x,e.y,e.z]}const f=n.defaultValue(e.granularity,c.CesiumMath.RADIANS_PER_DEGREE);let g,C,p=0,d=s.cartesianToCartographic(r[0],I);for(g=0;g<o-1;g++)C=s.cartesianToCartographic(r[g+1],V),p+=h.numberOfPointsRhumbLine(d,C,f),d=a.Cartographic.clone(C,I);const m=3*(p+1),w=new Array(m);let P=0;for(g=0;g<o-1;g++){P=G(r[g],r[g+1],f,s,u?l[g]:l,u?l[g+1]:l,w,P)}y.length=0;const T=r[o-1],A=s.cartesianToCartographic(T,E);A.height=u?l[o-1]:l;const M=s.cartographicToCartesian(A,R);return t.Cartesian3.pack(M,w,m-3),w},h.generateCartesianArc=function(e){const a=h.generateArc(e),n=a.length/3,i=new Array(n);for(let e=0;e<n;e++)i[e]=t.Cartesian3.unpack(a,3*e);return i},h.generateCartesianRhumbArc=function(e){const a=h.generateRhumbArc(e),n=a.length/3,i=new Array(n);for(let e=0;e<n;e++)i[e]=t.Cartesian3.unpack(a,3*e);return i};var k=h;e.PolylinePipeline=k}));
