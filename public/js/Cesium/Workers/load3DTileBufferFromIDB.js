define(["./defaultValue-f6d5e6da","./RequestType-735c98f2","./createTaskProcessorWorker","./EV_IndexedDBProvider-596d1bfc","./DeveloperError-c85858c1"],(function(e,r,s,t,a){"use strict";let i;function o(e){const s=self.webkitPostMessage||self.postMessage;try{s({workerType:r.RequestType.TILES3D,dataName:e.keyName,workerTaskID:e.workerTaskID,data:void 0,meshPrimitives:[]})}catch(e){s({})}}function n(e,s){(new Date).getTime(),e.requestTime;const t=self.webkitPostMessage||self.postMessage;try{t({workerType:r.RequestType.TILES3D,dataName:e.keyName,workerTaskID:e.workerTaskID,data:s,meshPrimitives:e.meshPrimitives})}catch(e){t({})}}return s((function(r,s){const a=r.indexedDB,d=r.taskData;if(d.workerTaskID=r.workerTaskID,e.defined(i))if(e.defined(i.iDB)){const e=new Date;d.requestTime=e.getTime(),i.download3DTileArrayBuffer("EVIDB",1,"3DTile",d,o,n)}else o(d);else i=new t.EV_IndexedDBProvider(a),i.createIDB("EVIDB",1,["model","imagery","terrain","3DTile"]),o(d)}))}));
