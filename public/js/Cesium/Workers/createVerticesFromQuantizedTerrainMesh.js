define(["./AxisAlignedBoundingBox-d98e5354","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./TerrainEncoding-443b37a0","./IndexDatatype-58eb7805","./Math-355606c6","./Rectangle-98b0bef0","./DeveloperError-c85858c1","./Transforms-2afbbfb5","./WebMercatorProjection-03b5db31","./createTaskProcessorWorker","./Matrix3-31d1f01f","./RuntimeError-9b4ce3fb","./AttributeCompression-d2ca507e","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./Matrix2-e4a4609a","./TerrainQuantization-c16f42ed","./Interval-d6c8d27a","./combine-0c102d93","./RequestType-735c98f2"],(function(e,t,r,n,o,i,a,s,c,d,h,u,l,I,g,m,T,E,p,f,y,N,w,M){"use strict";function x(){h.DeveloperError.throwInstantiationError()}Object.defineProperties(x.prototype,{errorEvent:{get:h.DeveloperError.throwInstantiationError},credit:{get:h.DeveloperError.throwInstantiationError},tilingScheme:{get:h.DeveloperError.throwInstantiationError},ready:{get:h.DeveloperError.throwInstantiationError},readyPromise:{get:h.DeveloperError.throwInstantiationError},hasWaterMask:{get:h.DeveloperError.throwInstantiationError},hasVertexNormals:{get:h.DeveloperError.throwInstantiationError},availability:{get:h.DeveloperError.throwInstantiationError}});const S=[];x.getRegularGridIndices=function(e,t){let r=S[e];o.defined(r)||(S[e]=r=[]);let n=r[t];return o.defined(n)||(n=e*t<c.CesiumMath.SIXTY_FOUR_KILOBYTES?r[t]=new Uint16Array((e-1)*(t-1)*6):r[t]=new Uint32Array((e-1)*(t-1)*6),v(e,t,n,0)),n};const C=[];x.getRegularGridIndicesAndEdgeIndices=function(e,t){let r=C[e];o.defined(r)||(C[e]=r=[]);let n=r[t];if(!o.defined(n)){const o=x.getRegularGridIndices(e,t),i=b(e,t),a=i.westIndicesSouthToNorth,s=i.southIndicesEastToWest,c=i.eastIndicesNorthToSouth,d=i.northIndicesWestToEast;n=r[t]={indices:o,westIndicesSouthToNorth:a,southIndicesEastToWest:s,eastIndicesNorthToSouth:c,northIndicesWestToEast:d}}return n};const A=[];function b(e,t){const r=new Array(t),n=new Array(e),o=new Array(t),i=new Array(e);let a;for(a=0;a<e;++a)i[a]=a,n[a]=e*t-1-a;for(a=0;a<t;++a)o[a]=(a+1)*e-1,r[a]=(t-a-1)*e;return{westIndicesSouthToNorth:r,southIndicesEastToWest:n,eastIndicesNorthToSouth:o,northIndicesWestToEast:i}}function v(e,t,r,n){let o=0;for(let i=0;i<t-1;++i){for(let t=0;t<e-1;++t){const t=o,i=t+e,a=i+1,s=t+1;r[n++]=t,r[n++]=i,r[n++]=s,r[n++]=s,r[n++]=i,r[n++]=a,++o}++o}}function W(e,t,r,n){let o=e[0];const i=e.length;for(let a=1;a<i;++a){const i=e[a];r[n++]=o,r[n++]=i,r[n++]=t,r[n++]=t,r[n++]=i,r[n++]=t+1,o=i,++t}return n}x.getRegularGridAndSkirtIndicesAndEdgeIndices=function(e,t){let r=A[e];o.defined(r)||(A[e]=r=[]);let n=r[t];if(!o.defined(n)){const o=e*t,i=(e-1)*(t-1)*6,a=2*e+2*t,c=o+a,d=i+6*Math.max(0,a-4),h=b(e,t),u=h.westIndicesSouthToNorth,l=h.southIndicesEastToWest,I=h.eastIndicesNorthToSouth,g=h.northIndicesWestToEast,m=s.IndexDatatype.createTypedArray(c,d);v(e,t,m,0),x.addSkirtIndices(u,l,I,g,o,m,i),n=r[t]={indices:m,westIndicesSouthToNorth:u,southIndicesEastToWest:l,eastIndicesNorthToSouth:I,northIndicesWestToEast:g,indexCountWithoutSkirts:i}}return n},x.addSkirtIndices=function(e,t,r,n,o,i,a){let s=o;a=W(e,s,i,a),s+=e.length,a=W(t,s,i,a),s+=t.length,a=W(r,s,i,a),s+=r.length,W(n,s,i,a)},x.heightmapTerrainQuality=.25,x.getEstimatedLevelZeroGeometricErrorForAHeightmap=function(e,t,r){return 2*e.maximumRadius*Math.PI*x.heightmapTerrainQuality/(t*r)},x.prototype.requestTileGeometry=h.DeveloperError.throwInstantiationError,x.prototype.getLevelMaximumGeometricError=h.DeveloperError.throwInstantiationError,x.prototype.getTileDataAvailable=h.DeveloperError.throwInstantiationError,x.prototype.loadTileDataAvailability=h.DeveloperError.throwInstantiationError;const P=32767,D=new r.Cartesian3,k=new r.Cartesian3,F=new r.Cartesian3,H=new n.Cartographic,V=new t.Cartesian2;function _(e,n,o,i,a,s,d,h,u){let l=Number.POSITIVE_INFINITY;const I=a.north,g=a.south;let m=a.east;const T=a.west;m<T&&(m+=c.CesiumMath.TWO_PI);const E=e.length;for(let a=0;a<E;++a){const E=e[a],p=o[E],f=i[E];H.longitude=c.CesiumMath.lerp(T,m,f.x),H.latitude=c.CesiumMath.lerp(g,I,f.y),H.height=p-n;const y=s.cartographicToCartesian(H,D);t.Matrix4.multiplyByPoint(d,y,y),r.Cartesian3.minimumByComponent(y,h,h),r.Cartesian3.maximumByComponent(y,u,u),l=Math.min(l,H.height)}return l}function G(e,t,r,n,i,a,s,d,h,u,I,g,m,T){const E=o.defined(s),p=h.north,f=h.south;let y=h.east;const N=h.west;y<N&&(y+=c.CesiumMath.TWO_PI);const w=r.length;for(let o=0;o<w;++o){const h=r[o],w=i[h],M=a[h];H.longitude=c.CesiumMath.lerp(N,y,M.x)+m,H.latitude=c.CesiumMath.lerp(f,p,M.y)+T,H.height=w-u;const x=d.cartographicToCartesian(H,D);if(E){const e=2*h;V.x=s[e],V.y=s[e+1]}let S,C;n.hasWebMercatorT&&(S=(l.WebMercatorProjection.geodeticLatitudeToMercatorAngle(H.latitude)-I)*g),n.hasGeodeticSurfaceNormals&&(C=d.geodeticSurfaceNormal(x)),t=n.encode(e,t,x,M,H.height,V,S,C)}}function Y(e,t){let r;return"function"==typeof e.slice&&(r=e.slice(),"function"!=typeof r.sort&&(r=void 0)),o.defined(r)||(r=Array.prototype.slice.call(e)),r.sort(t),r}return I((function(n,h){const I=n.quantizedVertices,g=I.length/3,m=n.octEncodedNormals,T=n.westIndices.length+n.eastIndices.length+n.southIndices.length+n.northIndices.length,E=n.includeWebMercatorT,p=n.exaggeration,f=n.exaggerationRelativeHeight,y=1!==p,N=d.Rectangle.clone(n.rectangle),w=N.west,M=N.south,S=N.east,C=N.north,A=i.Ellipsoid.clone(n.ellipsoid),b=n.minimumHeight,v=n.maximumHeight,W=n.relativeToCenter,O=u.Transforms.eastNorthUpToFixedFrame(W,A),B=t.Matrix4.inverseTransformation(O,new t.Matrix4);let R,L;E&&(R=l.WebMercatorProjection.geodeticLatitudeToMercatorAngle(M),L=1/(l.WebMercatorProjection.geodeticLatitudeToMercatorAngle(C)-R));const j=I.subarray(0,g),z=I.subarray(g,2*g),U=I.subarray(2*g,3*g),q=o.defined(m),Q=new Array(g),K=new Array(g),X=new Array(g),Z=E?new Array(g):[],J=y?new Array(g):[],$=k;$.x=Number.POSITIVE_INFINITY,$.y=Number.POSITIVE_INFINITY,$.z=Number.POSITIVE_INFINITY;const ee=F;ee.x=Number.NEGATIVE_INFINITY,ee.y=Number.NEGATIVE_INFINITY,ee.z=Number.NEGATIVE_INFINITY;let te=Number.POSITIVE_INFINITY,re=Number.NEGATIVE_INFINITY,ne=Number.POSITIVE_INFINITY,oe=Number.NEGATIVE_INFINITY;for(let e=0;e<g;++e){const n=j[e],o=z[e],i=n/P,a=o/P,s=c.CesiumMath.lerp(b,v,U[e]/P);H.longitude=c.CesiumMath.lerp(w,S,i),H.latitude=c.CesiumMath.lerp(M,C,a),H.height=s,te=Math.min(H.longitude,te),re=Math.max(H.longitude,re),ne=Math.min(H.latitude,ne),oe=Math.max(H.latitude,oe);const d=A.cartographicToCartesian(H);Q[e]=new t.Cartesian2(i,a),K[e]=s,X[e]=d,E&&(Z[e]=(l.WebMercatorProjection.geodeticLatitudeToMercatorAngle(H.latitude)-R)*L),y&&(J[e]=A.geodeticSurfaceNormal(d)),t.Matrix4.multiplyByPoint(B,d,D),r.Cartesian3.minimumByComponent(D,$,$),r.Cartesian3.maximumByComponent(D,ee,ee)}const ie=Y(n.westIndices,(function(e,t){return Q[e].y-Q[t].y})),ae=Y(n.eastIndices,(function(e,t){return Q[t].y-Q[e].y})),se=Y(n.southIndices,(function(e,t){return Q[t].x-Q[e].x})),ce=Y(n.northIndices,(function(e,t){return Q[e].x-Q[t].x}));let de;if(b<0){de=new a.EllipsoidalOccluder(A).computeHorizonCullingPointPossiblyUnderEllipsoid(W,X,b)}let he=b;he=Math.min(he,_(n.westIndices,n.westSkirtHeight,K,Q,N,A,B,$,ee)),he=Math.min(he,_(n.southIndices,n.southSkirtHeight,K,Q,N,A,B,$,ee)),he=Math.min(he,_(n.eastIndices,n.eastSkirtHeight,K,Q,N,A,B,$,ee)),he=Math.min(he,_(n.northIndices,n.northSkirtHeight,K,Q,N,A,B,$,ee));const ue=new e.AxisAlignedBoundingBox($,ee,W),le=new a.TerrainEncoding(W,ue,he,v,O,q,E,y,p,f),Ie=le.stride,ge=new Float32Array(g*Ie+T*Ie);let me=0;for(let e=0;e<g;++e){if(q){const t=2*e;V.x=m[t],V.y=m[t+1]}me=le.encode(ge,me,X[e],Q[e],K[e],V,Z[e],J[e])}const Te=Math.max(0,2*(T-4)),Ee=n.indices.length+3*Te,pe=s.IndexDatatype.createTypedArray(g+T,Ee);pe.set(n.indices,0);const fe=1e-4,ye=(re-te)*fe,Ne=(oe-ne)*fe,we=-ye,Me=ye,xe=Ne,Se=-Ne;let Ce=g*Ie;return G(ge,Ce,ie,le,K,Q,m,A,N,n.westSkirtHeight,R,L,we,0),Ce+=n.westIndices.length*Ie,G(ge,Ce,se,le,K,Q,m,A,N,n.southSkirtHeight,R,L,0,Se),Ce+=n.southIndices.length*Ie,G(ge,Ce,ae,le,K,Q,m,A,N,n.eastSkirtHeight,R,L,Me,0),Ce+=n.eastIndices.length*Ie,G(ge,Ce,ce,le,K,Q,m,A,N,n.northSkirtHeight,R,L,0,xe),x.addSkirtIndices(ie,se,ae,ce,g,pe,n.indices.length),h.push(ge.buffer,pe.buffer),{vertices:ge.buffer,indices:pe.buffer,westIndicesSouthToNorth:ie,southIndicesEastToWest:se,eastIndicesNorthToSouth:ae,northIndicesWestToEast:ce,vertexStride:Ie,center:W,minimumHeight:b,maximumHeight:v,occludeePointInScaledSpace:de,encoding:le,indexCountWithoutSkirts:n.indices.length}}))}));
