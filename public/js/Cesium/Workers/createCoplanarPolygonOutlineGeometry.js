define(["./arrayRemoveDuplicates-0d8dde26","./Transforms-2afbbfb5","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./CoplanarPolygonGeometryLibrary-5cd2e1e7","./defaultValue-f6d5e6da","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryInstance-1d11f88d","./GeometryPipeline-69b47aa9","./IndexDatatype-58eb7805","./PolygonGeometryLibrary-b9a68ba7","./Ellipsoid-8e26549b","./Math-355606c6","./Cartographic-dbefb6fa","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./WebGLConstants-7f557f93","./OrientedBoundingBox-5f5e4f4c","./EllipsoidTangentPlane-a6ea67fb","./AxisAlignedBoundingBox-d98e5354","./IntersectionTests-01432fe7","./Plane-06f34fae","./Matrix2-e4a4609a","./AttributeCompression-d2ca507e","./EncodedCartesian3-94199dac","./ArcType-26a3f38d","./EllipsoidRhumbLine-6774fec3","./PolygonPipeline-39b84ada"],(function(e,t,n,r,o,i,a,l,y,s,c,p,u,m,d,g,h,f,P,b,G,C,E,L,T,A,H,k,x,I,v,D,w,_,B){"use strict";function O(e){const t=e.length,n=new Float64Array(3*t),o=c.IndexDatatype.createTypedArray(t,2*t);let i=0,y=0;for(let r=0;r<t;r++){const a=e[r];n[i++]=a.x,n[i++]=a.y,n[i++]=a.z,o[y++]=r,o[y++]=(r+1)%t}const s=new l.GeometryAttributes({position:new a.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:n})});return new a.Geometry({attributes:s,indices:o,primitiveType:a.PrimitiveType.LINES})}function V(e){const t=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).polygonHierarchy;this._polygonHierarchy=t,this._workerName="createCoplanarPolygonOutlineGeometry",this.packedLength=p.PolygonGeometryLibrary.computeHierarchyPackedLength(t,n.Cartesian3)+1}V.fromPositions=function(e){return new V({polygonHierarchy:{positions:(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).positions}})},V.pack=function(e,t,r){return r=i.defaultValue(r,0),t[r=p.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,r,n.Cartesian3)]=e.packedLength,t};const M={polygonHierarchy:{}};return V.unpack=function(e,t,r){t=i.defaultValue(t,0);const o=p.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t,n.Cartesian3);t=o.startingIndex,delete o.startingIndex;const a=e[t];return i.defined(r)||(r=new V(M)),r._polygonHierarchy=o,r.packedLength=a,r},V.createGeometry=function(r){const i=r._polygonHierarchy;let l=i.positions;if(l=e.arrayRemoveDuplicates(l,n.Cartesian3.equalsEpsilon,!0),l.length<3)return;if(!o.CoplanarPolygonGeometryLibrary.validOutline(l))return;const c=p.PolygonGeometryLibrary.polygonOutlinesFromHierarchy(i,!1);if(0===c.length)return;const u=[];for(let e=0;e<c.length;e++){const t=new y.GeometryInstance({geometry:O(c[e])});u.push(t)}const m=s.GeometryPipeline.combineInstances(u)[0],d=t.BoundingSphere.fromPoints(i.positions);return new a.Geometry({attributes:m.attributes,indices:m.indices,primitiveType:m.primitiveType,boundingSphere:d})},function(e,t){return i.defined(t)&&(e=V.unpack(e,t)),e._ellipsoid=u.Ellipsoid.clone(e._ellipsoid),V.createGeometry(e)}}));
