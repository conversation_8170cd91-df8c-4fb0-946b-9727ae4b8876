define(["./createTaskProcessorWorker","./Transforms-2afbbfb5","./defaultValue-f6d5e6da","./Cartesian3-529c236c","./Matrix4-c57ffbd8","./IndexDatatype-58eb7805","./Color-e24f904b","./Math-355606c6","./EncodedCartesian3-94199dac","./Cartographic-dbefb6fa","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./RuntimeError-9b4ce3fb","./WebGLConstants-7f557f93"],(function(a,n,t,e,r,i,o,s,l,f,u,p,w,c,C,h,d,m,y){"use strict";return a((function(a,f){for(var u=a.ribbons,p=n.Transforms.localFrameToFixedFrameGenerator("north","west"),w=new r.Matrix4,c=u.length,C=[],h=0,d=0,m=0;m<c;m++)if(t.defined(u[m])&&u[m].trackPoints.length>1){u[m].scale=s.CesiumMath.equalsEpsilon(u[m].scale,0,s.CesiumMath.EPSILON7)?1:u[m].scale;var y=1/u[m].scale,x=u[m].scale,P=u[m].trackPoints,g=[],M=P.length;if(M>300){for(var B=M-100,v=Math.floor(B/200)+1,b=M-100-v,T=0;T<b;T+=v){var O=n.Quaternion.fromHeadingPitchRoll(P[T].headingPitchRoll,new n.Quaternion),R=r.Matrix4.fromTranslationQuaternionRotationScale(new e.Cartesian3(0,0,0),O,new e.Cartesian3(x,x,x));w=p(P[T].wcPosition,void 0,w),w=r.Matrix4.multiply(w,R,w);var Q=r.Matrix4.multiplyByPoint(w,new e.Cartesian3(0,0,1),new e.Cartesian3);Q=e.Cartesian3.subtract(Q,P[T].wcPosition,Q);var E=r.Matrix4.multiplyByPoint(w,new e.Cartesian3(u[m].forwardOffset*y,u[m].upOffset,0),new e.Cartesian3),S=r.Matrix4.multiplyByPoint(w,new e.Cartesian3(u[m].forwardOffset*y,-u[m].upOffset,0),new e.Cartesian3);g.push({position:E,normal:Q}),g.push({position:S,normal:Q}),h+=2}for(;T<M-1;T++){O=n.Quaternion.fromHeadingPitchRoll(P[T].headingPitchRoll,new n.Quaternion),R=r.Matrix4.fromTranslationQuaternionRotationScale(new e.Cartesian3(0,0,0),O,new e.Cartesian3(x,x,x));w=p(P[T].wcPosition,void 0,w),w=r.Matrix4.multiply(w,R,w);Q=r.Matrix4.multiplyByPoint(w,new e.Cartesian3(0,0,1),new e.Cartesian3);Q=e.Cartesian3.subtract(Q,P[T].wcPosition,Q);E=r.Matrix4.multiplyByPoint(w,new e.Cartesian3(u[m].forwardOffset*y,u[m].upOffset,0),new e.Cartesian3),S=r.Matrix4.multiplyByPoint(w,new e.Cartesian3(u[m].forwardOffset*y,-u[m].upOffset,0),new e.Cartesian3);g.push({position:E,normal:Q}),g.push({position:S,normal:Q}),h+=2}}else for(var F=0;F<P.length;F++){O=n.Quaternion.fromHeadingPitchRoll(P[F].headingPitchRoll,new n.Quaternion),R=r.Matrix4.fromTranslationQuaternionRotationScale(new e.Cartesian3(0,0,0),O,new e.Cartesian3(x,x,x));w=p(P[F].wcPosition,void 0,w),w=r.Matrix4.multiply(w,R,w);Q=r.Matrix4.multiplyByPoint(w,new e.Cartesian3(0,0,1),new e.Cartesian3);Q=e.Cartesian3.subtract(Q,P[F].wcPosition,Q);E=r.Matrix4.multiplyByPoint(w,new e.Cartesian3(u[m].forwardOffset*y,u[m].upOffset,0),new e.Cartesian3),S=r.Matrix4.multiplyByPoint(w,new e.Cartesian3(u[m].forwardOffset*y,-u[m].upOffset,0),new e.Cartesian3);g.push({position:E,normal:Q}),g.push({position:S,normal:Q}),h+=2}C.push(g),d+=6*(g.length/2-1)}if(0!=C.length){var k=new n.BoundingSphere,A=i.IndexDatatype.createTypedArray(h,d),D=new Float64Array(3*h),H=new Float64Array(3*h),I=(Q=new Float64Array(3*h),new Uint8Array(4*h)),q=new l.EncodedCartesian3,z=0,L=0;for(m=0;m<C.length;m++){for(var G=u[m].color,W=(g=C[m]).length,j=0;j<W;j++){k=n.BoundingSphere.expand(k,g[j].position),l.EncodedCartesian3.fromCartesian(g[j].position,q);var N=q.high,U=q.low;D[3*(L+j)]=N.x,D[3*(L+j)+1]=N.y,D[3*(L+j)+2]=N.z,H[3*(L+j)]=U.x,H[3*(L+j)+1]=U.y,H[3*(L+j)+2]=U.z,Q[3*(L+j)]=g[j].normal.x,Q[3*(L+j)+1]=g[j].normal.y,Q[3*(L+j)+2]=g[j].normal.z;var V=1-(W-j)/W*1;I[4*(L+j)]=o.Color.floatToByte(G.red),I[4*(L+j)+1]=o.Color.floatToByte(G.green),I[4*(L+j)+2]=o.Color.floatToByte(G.blue),I[4*(L+j)+3]=o.Color.floatToByte(G.alpha*V)}for(var J=0;J+3<W;J+=2)A[z++]=L+J,A[z++]=L+J+1,A[z++]=L+J+2,A[z++]=L+J+1,A[z++]=L+J+3,A[z++]=L+J+2;L+=W}return a.ribbons=void 0,{position3DHighBuffer:D,position3DLowBuffer:H,normalBuffer:Q.buffer,colorBuffer:I.buffer,indices:A.buffer,boundingSphere:k}}}))}));
