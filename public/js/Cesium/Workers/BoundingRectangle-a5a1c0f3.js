define(["exports","./Matrix4-c57ffbd8","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Transforms-2afbbfb5","./Rectangle-98b0bef0"],(function(t,e,n,i,h,r){"use strict";function a(t,e,n,h){this.x=i.defaultValue(t,0),this.y=i.defaultValue(e,0),this.width=i.defaultValue(n,0),this.height=i.defaultValue(h,0)}a.packedLength=4,a.pack=function(t,e,n){return n=i.defaultValue(n,0),e[n++]=t.x,e[n++]=t.y,e[n++]=t.width,e[n]=t.height,e},a.unpack=function(t,e,n){return e=i.defaultValue(e,0),i.defined(n)||(n=new a),n.x=t[e++],n.y=t[e++],n.width=t[e++],n.height=t[e],n},a.fromPoints=function(t,e){if(i.defined(e)||(e=new a),!i.defined(t)||0===t.length)return e.x=0,e.y=0,e.width=0,e.height=0,e;const n=t.length;let h=t[0].x,r=t[0].y,u=t[0].x,d=t[0].y;for(let e=1;e<n;e++){const n=t[e],i=n.x,a=n.y;h=Math.min(i,h),u=Math.max(i,u),r=Math.min(a,r),d=Math.max(a,d)}return e.x=h,e.y=r,e.width=u-h,e.height=d-r,e};const u=new h.GeographicProjection,d=new n.Cartographic,o=new n.Cartographic;a.fromRectangle=function(t,n,h){if(i.defined(h)||(h=new a),!i.defined(t))return h.x=0,h.y=0,h.width=0,h.height=0,h;const c=(n=i.defaultValue(n,u)).project(r.Rectangle.southwest(t,d)),f=n.project(r.Rectangle.northeast(t,o));return e.Cartesian2.subtract(f,c,f),h.x=c.x,h.y=c.y,h.width=f.x,h.height=f.y,h},a.clone=function(t,e){if(i.defined(t))return i.defined(e)?(e.x=t.x,e.y=t.y,e.width=t.width,e.height=t.height,e):new a(t.x,t.y,t.width,t.height)},a.union=function(t,e,n){i.defined(n)||(n=new a);const h=Math.min(t.x,e.x),r=Math.min(t.y,e.y),u=Math.max(t.x+t.width,e.x+e.width),d=Math.max(t.y+t.height,e.y+e.height);return n.x=h,n.y=r,n.width=u-h,n.height=d-r,n},a.expand=function(t,e,n){n=a.clone(t,n);const i=e.x-n.x,h=e.y-n.y;return i>n.width?n.width=i:i<0&&(n.width-=i,n.x=e.x),h>n.height?n.height=h:h<0&&(n.height-=h,n.y=e.y),n},a.intersect=function(t,e){const n=t.x,i=t.y,r=e.x,a=e.y;return n>r+e.width||n+t.width<r||i+t.height<a||i>a+e.height?h.Intersect.OUTSIDE:h.Intersect.INTERSECTING},a.equals=function(t,e){return t===e||i.defined(t)&&i.defined(e)&&t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height},a.prototype.clone=function(t){return a.clone(this,t)},a.prototype.intersect=function(t){return a.intersect(this,t)},a.prototype.equals=function(t){return a.equals(this,t)},t.BoundingRectangle=a}));
