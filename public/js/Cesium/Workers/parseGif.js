/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.105.2
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

onmessage=function(h){var l=h.data,v=l.url,B=l.width,E=l.height,u=new XMLHttpRequest;u.open("GET",v,!0),"overrideMimeType"in u?u.overrideMimeType("text/plain; charset=x-user-defined"):"responseType"in u&&(u.responseType="arraybuffer"),u.onprogress=function(y){if(y.lengthComputable)var C=y.loaded/y.total},u.onload=function(y){if(this.status>=200&&this.status<300||this.status===304){var C=this.response;C.toString().indexOf("ArrayBuffer")>0&&(C=new Uint8Array(C)),parseGIF(C,B,E)}},u.send()};function parseGIF(h,l,v){var B=new OffscreenCanvas(l||100,v||100),E=new OffscreenCanvas(l||100,v||100),u=B.getContext("2d"),y=E.getContext("2d"),C=[],T=null,U=!1,a=new Stream(h),i={};if(i.sig=a.read(3),i.sig!=="GIF")throw new Error("Not a GIF file.");i.ver=a.read(3),i.width=a.readUnsigned(),i.height=a.readUnsigned();var z=G(a.readByte());i.gctFlag=z.shift(),i.colorRes=P(z.splice(0,3)),i.sorted=z.shift(),i.gctSize=P(z.splice(0,3)),i.bgColor=a.readByte(),i.pixelAspectRatio=a.readByte(),i.gctFlag&&(i.gct=X(1<<i.gctSize+1)),B.width=l||i.width,B.height=v||i.height,E.width=i.width,E.height=i.height,y.setTransform(1,0,0,1,0,0);var w=null,A=null,M=null,m=null,q=null;do switch(w={},w.sentinel=a.readByte(),String.fromCharCode(w.sentinel)){case"!":w.type="ext",j(w);break;case",":w.type="img",J(w);break;case";":w.type="eof",Q(w);break;default:throw new Error("Unknown block: 0x"+w.sentinel.toString(16))}while(w.type!=="eof");postMessage({finish:!0}),close();function j(e){switch(e.label=a.readByte(),e.label){case 249:e.extType="gce",o(e);break;case 254:e.extType="com",c(e);break;case 1:e.extType="pte",R(e);break;case 255:e.extType="app",b(e);break;default:e.extType="unknown",N(e);break}function o(t){var p=a.readByte(),s=G(a.readByte());t.reserved=s.splice(0,3),t.disposalMethod=P(s.splice(0,3)),t.userInput=s.shift(),t.transparencyGiven=s.shift(),t.delayTime=a.readUnsigned(),t.transparencyIndex=a.readByte(),t.terminator=a.readByte(),f(t);function f(n){q=n.delayTime*10,W(),K(),A=n.transparencyGiven?n.transparencyIndex:null,M=n.disposalMethod}}function c(t){t.comment=D(),handler.com&&handler.com(t)}function R(t){var p=a.readByte();t.ptHeader=a.readBytes(12),t.ptData=D(),handler.pte&&handler.pte(t)}function b(t){var p=a.readByte();switch(t.identifier=a.read(8),t.authCode=a.read(3),t.identifier){case"NETSCAPE":s(t);break;default:f(t);break}function s(n){var I=a.readByte();n.unknown=a.readByte(),n.iterations=a.readUnsigned(),n.terminator=a.readByte()}function f(n){n.appData=D()}}function N(t){t.data=D(),handler.unknown&&handler.unknown(t)}}function J(e){e.leftPos=a.readUnsigned(),e.topPos=a.readUnsigned(),e.width=a.readUnsigned(),e.height=a.readUnsigned();var o=G(a.readByte());e.lctFlag=o.shift(),e.interlaced=o.shift(),e.sorted=o.shift(),e.reserved=o.splice(0,2),e.lctSize=P(o.splice(0,3)),e.lctFlag&&(e.lct=X(1<<e.lctSize+1)),e.lzwMinCodeSize=a.readByte();var c=D();e.pixels=R(e.lzwMinCodeSize,c),e.interlaced&&(e.pixels=b(e.pixels,e.width)),N(e);function R(t,p){for(var s=0,f=[],n=1<<t,I=n+1,d=t+1,r=[],g=null,x,F,O=0;;){if(F=x,x=H(d),x===n){V();continue}else if(x===I)break;if(x<r.length)F!==n&&r.push(r[F].concat(r[x][0]));else{if(x!==r.length)throw new Error("Invalid LZW code.");r.push(r[F].concat(r[F][0]))}f.push.apply(f,r[x]),r.length===1<<d&&d<12&&d++}return f;function H(S){for(var Z=0,L=0;L<S;L++)p.charCodeAt(s>>3)&1<<(s&7)&&(Z|=1<<L),s++;return Z}function V(){if(d=t+1,!g){g=new Array(I+1);for(var S=0;S<n;S++)g[S]=[S];g[n]=[],g[I]=null}r=g.slice()}}function b(t,p){for(var s=new Array(t.length),f=t.length/p,n=[0,4,2,1],I=[8,8,4,2],d=0,r=0;r<4;r++)for(var g=n[r];g<f;g+=I[r])x(g,d),d++;return s;function x(F,O){var H=t.slice(O*p,(O+1)*p);s.splice.apply(s,[F*p,p].concat(H))}}function N(t){var p=C.length;p>0&&(lastDisposalMethod===3?m!==null?y.putImageData(C[m],0,0):y.clearRect(T.leftPos,T.topPos,T.width,T.height):m=p-1,lastDisposalMethod===2&&y.clearRect(T.leftPos,T.topPos,T.width,T.height));for(var s=y.getImageData(t.leftPos,t.topPos,t.width,t.height),f=t.lctFlag?t.lct:i.gct,n=t.pixels,I=n.length,d=null,r=0;r<I;++r)d=n[r],d!==A&&(s.data[r*4+0]=f[d][0],s.data[r*4+1]=f[d][1],s.data[r*4+2]=f[d][2],s.data[r*4+3]=255);y.putImageData(s,t.leftPos,t.topPos),T=t,U||(U=!0)}}function W(){if(U){u.globalCompositeOperation="copy",u.drawImage(E,0,0,E.width,E.height,0,0,B.width,B.height);var e=u.getImageData(0,0,B.width,B.height);C.push(e),postMessage({data:e,delay:q})}}function D(){var e,o="";do e=a.readByte(),o+=a.read(e);while(e!==0);return o}function K(){A=null,lastDisposalMethod=M,M=null}function Q(e){W()}function P(e){return e.reduce(function(o,c){return o*2+c},0)}function G(e){for(var o=[],c=7;c>=0;c--)o.push(!!(e&1<<c));return o}function X(e){for(var o=[],c=0;c<e;c++)o.push(a.readBytes(3));return o}}function Stream(h){this.data=h,this.len=this.data.length,this.pos=0}Stream.prototype.readByte=function(){if(this.pos>=this.len)throw new Error("Attempted to read past end of stream.");return this.data instanceof Uint8Array?this.data[this.pos++]:this.data.charCodeAt(this.pos++)&255},Stream.prototype.readBytes=function(h){for(var l=[],v=0;v<h;v++)l.push(this.readByte());return l},Stream.prototype.read=function(h){for(var l="",v=0;v<h;v++)l+=String.fromCharCode(this.readByte());return l},Stream.prototype.readUnsigned=function(){var h=this.readBytes(2);return(h[1]<<8)+h[0]};
