define(["./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./Rectangle-98b0bef0","./Transforms-2afbbfb5","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./ComponentDatatype-ab629b88","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-58eb7805","./Math-355606c6","./PolygonPipeline-39b84ada","./RectangleGeometryLibrary-4ef13be5","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./combine-0c102d93","./RequestType-735c98f2","./WebGLConstants-7f557f93","./Matrix2-e4a4609a","./EllipsoidRhumbLine-6774fec3"],(function(e,t,i,n,o,r,a,l,s,u,c,p,d,f,g,h,y,m,b,_,E,A,G,R){"use strict";const P=new n.BoundingSphere,v=new n.BoundingSphere,L=new o.Cartesian3,w=new i.Rectangle;function C(e,t){const i=e._ellipsoid,n=t.height,o=t.width,r=t.northCap,u=t.southCap;let p=n,d=2,g=0,h=4;r&&(d-=1,p-=1,g+=1,h-=2),u&&(d-=1,p-=1,g+=1,h-=2),g+=d*o+2*p-h;const y=new Float64Array(3*g);let m,b=0,_=0;const E=L;if(r)f.RectangleGeometryLibrary.computePosition(t,i,!1,_,0,E),y[b++]=E.x,y[b++]=E.y,y[b++]=E.z;else for(m=0;m<o;m++)f.RectangleGeometryLibrary.computePosition(t,i,!1,_,m,E),y[b++]=E.x,y[b++]=E.y,y[b++]=E.z;for(m=o-1,_=1;_<n;_++)f.RectangleGeometryLibrary.computePosition(t,i,!1,_,m,E),y[b++]=E.x,y[b++]=E.y,y[b++]=E.z;if(_=n-1,!u)for(m=o-2;m>=0;m--)f.RectangleGeometryLibrary.computePosition(t,i,!1,_,m,E),y[b++]=E.x,y[b++]=E.y,y[b++]=E.z;for(m=0,_=n-2;_>0;_--)f.RectangleGeometryLibrary.computePosition(t,i,!1,_,m,E),y[b++]=E.x,y[b++]=E.y,y[b++]=E.z;const A=y.length/3*2,G=c.IndexDatatype.createTypedArray(y.length/3,A);let R=0;for(let e=0;e<y.length/3-1;e++)G[R++]=e,G[R++]=e+1;G[R++]=y.length/3-1,G[R++]=0;const P=new l.Geometry({attributes:new s.GeometryAttributes,primitiveType:l.PrimitiveType.LINES});return P.attributes.position=new l.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:y}),P.indices=G,P}function x(n){const o=(n=e.defaultValue(n,e.defaultValue.EMPTY_OBJECT)).rectangle,r=e.defaultValue(n.granularity,p.CesiumMath.RADIANS_PER_DEGREE),a=e.defaultValue(n.ellipsoid,t.Ellipsoid.WGS84),l=e.defaultValue(n.rotation,0),s=e.defaultValue(n.height,0),u=e.defaultValue(n.extrudedHeight,s);this._rectangle=i.Rectangle.clone(o),this._granularity=r,this._ellipsoid=a,this._surfaceHeight=Math.max(s,u),this._rotation=l,this._extrudedHeight=Math.min(s,u),this._offsetAttribute=n.offsetAttribute,this._workerName="createRectangleOutlineGeometry"}x.packedLength=i.Rectangle.packedLength+t.Ellipsoid.packedLength+5,x.pack=function(n,o,r){return r=e.defaultValue(r,0),i.Rectangle.pack(n._rectangle,o,r),r+=i.Rectangle.packedLength,t.Ellipsoid.pack(n._ellipsoid,o,r),r+=t.Ellipsoid.packedLength,o[r++]=n._granularity,o[r++]=n._surfaceHeight,o[r++]=n._rotation,o[r++]=n._extrudedHeight,o[r]=e.defaultValue(n._offsetAttribute,-1),o};const D=new i.Rectangle,H=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),S={rectangle:D,ellipsoid:H,granularity:void 0,height:void 0,rotation:void 0,extrudedHeight:void 0,offsetAttribute:void 0};x.unpack=function(n,o,r){o=e.defaultValue(o,0);const a=i.Rectangle.unpack(n,o,D);o+=i.Rectangle.packedLength;const l=t.Ellipsoid.unpack(n,o,H);o+=t.Ellipsoid.packedLength;const s=n[o++],u=n[o++],c=n[o++],p=n[o++],d=n[o];return e.defined(r)?(r._rectangle=i.Rectangle.clone(a,r._rectangle),r._ellipsoid=t.Ellipsoid.clone(l,r._ellipsoid),r._surfaceHeight=u,r._rotation=c,r._extrudedHeight=p,r._offsetAttribute=-1===d?void 0:d,r):(S.granularity=s,S.height=u,S.rotation=c,S.extrudedHeight=p,S.offsetAttribute=-1===d?void 0:d,new x(S))};const T=new r.Cartographic;return x.createGeometry=function(t){const i=t._rectangle,o=t._ellipsoid,r=f.RectangleGeometryLibrary.computeOptions(i,t._granularity,t._rotation,0,w,T);let s,g;if(p.CesiumMath.equalsEpsilon(i.north,i.south,p.CesiumMath.EPSILON10)||p.CesiumMath.equalsEpsilon(i.east,i.west,p.CesiumMath.EPSILON10))return;const h=t._surfaceHeight,y=t._extrudedHeight;let m;if(!p.CesiumMath.equalsEpsilon(h,y,0,p.CesiumMath.EPSILON2)){if(s=function(e,t){const i=e._surfaceHeight,n=e._extrudedHeight,o=e._ellipsoid,r=n,a=i,l=C(e,t),s=t.height,u=t.width,p=d.PolygonPipeline.scaleToGeodeticHeight(l.attributes.position.values,a,o,!1);let f=p.length;const g=new Float64Array(2*f);g.set(p);const h=d.PolygonPipeline.scaleToGeodeticHeight(l.attributes.position.values,r,o);g.set(h,f),l.attributes.position.values=g;const y=t.northCap,m=t.southCap;let b=4;y&&(b-=1),m&&(b-=1);const _=2*(g.length/3+b),E=c.IndexDatatype.createTypedArray(g.length/3,_);f=g.length/6;let A,G=0;for(let e=0;e<f-1;e++)E[G++]=e,E[G++]=e+1,E[G++]=e+f,E[G++]=e+f+1;if(E[G++]=f-1,E[G++]=0,E[G++]=f+f-1,E[G++]=f,E[G++]=0,E[G++]=f,y)A=s-1;else{const e=u-1;E[G++]=e,E[G++]=e+f,A=u+s-2}if(E[G++]=A,E[G++]=A+f,!m){const e=u+A-1;E[G++]=e,E[G]=e+f}return l.indices=E,l}(t,r),e.defined(t._offsetAttribute)){const e=s.attributes.position.values.length/3;let i=new Uint8Array(e);t._offsetAttribute===u.GeometryOffsetAttribute.TOP?i=i.fill(1,0,e/2):(m=t._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,i=i.fill(m)),s.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:a.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}const p=n.BoundingSphere.fromRectangle3D(i,o,h,v),f=n.BoundingSphere.fromRectangle3D(i,o,y,P);g=n.BoundingSphere.union(p,f)}else{if(s=C(t,r),s.attributes.position.values=d.PolygonPipeline.scaleToGeodeticHeight(s.attributes.position.values,h,o,!1),e.defined(t._offsetAttribute)){const e=s.attributes.position.values.length;m=t._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1;const i=new Uint8Array(e/3).fill(m);s.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:a.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}g=n.BoundingSphere.fromRectangle3D(i,o,h)}return new l.Geometry({attributes:s.attributes,indices:s.indices,primitiveType:l.PrimitiveType.LINES,boundingSphere:g,offsetAttribute:t._offsetAttribute})},function(n,o){return e.defined(o)&&(n=x.unpack(n,o)),n._ellipsoid=t.Ellipsoid.clone(n._ellipsoid),n._rectangle=i.Rectangle.clone(n._rectangle),x.createGeometry(n)}}));
