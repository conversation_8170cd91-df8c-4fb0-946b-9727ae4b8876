define(["./PrimitivePipeline-9699a537","./createTaskProcessorWorker","./Transforms-2afbbfb5","./Cartesian3-529c236c","./defaultValue-f6d5e6da","./Math-355606c6","./Cartographic-dbefb6fa","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./GeometryAttribute-f7a0845b","./Matrix2-e4a4609a","./GeometryAttributes-1e4ddcd2","./GeometryPipeline-69b47aa9","./AttributeCompression-d2ca507e","./EncodedCartesian3-94199dac","./IndexDatatype-58eb7805","./IntersectionTests-01432fe7","./Plane-06f34fae","./WebMercatorProjection-03b5db31"],(function(e,t,r,i,n,o,a,s,m,c,p,l,u,P,b,y,C,d,G,f,v,k,M,x,E,R,T,g){"use strict";return t((function(t,r){const i=e.PrimitivePipeline.unpackCombineGeometryParameters(t),n=e.PrimitivePipeline.combineGeometry(i);return e.PrimitivePipeline.packCombineGeometryResults(n,r)}))}));
