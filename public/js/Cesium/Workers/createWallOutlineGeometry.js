define(["./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./Transforms-2afbbfb5","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./IndexDatatype-58eb7805","./Math-355606c6","./WallGeometryLibrary-88a3cb50","./Cartographic-dbefb6fa","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./WebGLConstants-7f557f93","./Matrix2-e4a4609a","./arrayRemoveDuplicates-0d8dde26","./PolylinePipeline-07b67faf","./EllipsoidGeodesic-8ac7b85d","./EllipsoidRhumbLine-6774fec3","./IntersectionTests-01432fe7","./Plane-06f34fae"],(function(e,i,t,n,o,a,r,s,l,m,u,d,p,c,f,h,g,y,E,_,C,H,A,k,x,w,b){"use strict";const G=new n.Cartesian3,L=new n.Cartesian3;function P(t){const o=(t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT)).positions,a=t.maximumHeights,r=t.minimumHeights,s=e.defaultValue(t.granularity,l.CesiumMath.RADIANS_PER_DEGREE),m=e.defaultValue(t.ellipsoid,i.Ellipsoid.WGS84);this._positions=o,this._minimumHeights=r,this._maximumHeights=a,this._granularity=s,this._ellipsoid=i.Ellipsoid.clone(m),this._workerName="createWallOutlineGeometry";let u=1+o.length*n.Cartesian3.packedLength+2;e.defined(r)&&(u+=r.length),e.defined(a)&&(u+=a.length),this.packedLength=u+i.Ellipsoid.packedLength+1}P.pack=function(t,o,a){let r;a=e.defaultValue(a,0);const s=t._positions;let l=s.length;for(o[a++]=l,r=0;r<l;++r,a+=n.Cartesian3.packedLength)n.Cartesian3.pack(s[r],o,a);const m=t._minimumHeights;if(l=e.defined(m)?m.length:0,o[a++]=l,e.defined(m))for(r=0;r<l;++r)o[a++]=m[r];const u=t._maximumHeights;if(l=e.defined(u)?u.length:0,o[a++]=l,e.defined(u))for(r=0;r<l;++r)o[a++]=u[r];return i.Ellipsoid.pack(t._ellipsoid,o,a),o[a+=i.Ellipsoid.packedLength]=t._granularity,o};const v=i.Ellipsoid.clone(i.Ellipsoid.UNIT_SPHERE),T={positions:void 0,minimumHeights:void 0,maximumHeights:void 0,ellipsoid:v,granularity:void 0};return P.unpack=function(t,o,a){let r;o=e.defaultValue(o,0);let s=t[o++];const l=new Array(s);for(r=0;r<s;++r,o+=n.Cartesian3.packedLength)l[r]=n.Cartesian3.unpack(t,o);let m,u;if(s=t[o++],s>0)for(m=new Array(s),r=0;r<s;++r)m[r]=t[o++];if(s=t[o++],s>0)for(u=new Array(s),r=0;r<s;++r)u[r]=t[o++];const d=i.Ellipsoid.unpack(t,o,v),p=t[o+=i.Ellipsoid.packedLength];return e.defined(a)?(a._positions=l,a._minimumHeights=m,a._maximumHeights=u,a._ellipsoid=i.Ellipsoid.clone(d,a._ellipsoid),a._granularity=p,a):(T.positions=l,T.minimumHeights=m,T.maximumHeights=u,T.granularity=p,new P(T))},P.fromConstantHeights=function(i){const t=(i=e.defaultValue(i,e.defaultValue.EMPTY_OBJECT)).positions;let n,o;const a=i.minimumHeight,r=i.maximumHeight,s=e.defined(a),l=e.defined(r);if(s||l){const e=t.length;n=s?new Array(e):void 0,o=l?new Array(e):void 0;for(let i=0;i<e;++i)s&&(n[i]=a),l&&(o[i]=r)}return new P({positions:t,maximumHeights:o,minimumHeights:n,ellipsoid:i.ellipsoid})},P.createGeometry=function(i){const u=i._positions,d=i._minimumHeights,p=i._maximumHeights,c=i._granularity,f=i._ellipsoid,h=m.WallGeometryLibrary.computePositions(f,u,p,d,c,!1);if(!e.defined(h))return;const g=h.bottomPositions,y=h.topPositions;let E=y.length,_=2*E;const C=new Float64Array(_);let H,A=0;for(E/=3,H=0;H<E;++H){const e=3*H,i=n.Cartesian3.fromArray(y,e,G),t=n.Cartesian3.fromArray(g,e,L);C[A++]=t.x,C[A++]=t.y,C[A++]=t.z,C[A++]=i.x,C[A++]=i.y,C[A++]=i.z}const k=new r.GeometryAttributes({position:new a.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:C})}),x=_/3;_=2*x-4+x;const w=s.IndexDatatype.createTypedArray(x,_);let b=0;for(H=0;H<x-2;H+=2){const e=H,i=H+2,t=n.Cartesian3.fromArray(C,3*e,G),o=n.Cartesian3.fromArray(C,3*i,L);if(n.Cartesian3.equalsEpsilon(t,o,l.CesiumMath.EPSILON10))continue;const a=H+1,r=H+3;w[b++]=a,w[b++]=e,w[b++]=a,w[b++]=r,w[b++]=e,w[b++]=i}return w[b++]=x-2,w[b++]=x-1,new a.Geometry({attributes:k,indices:w,primitiveType:a.PrimitiveType.LINES,boundingSphere:new t.BoundingSphere.fromVertices(C)})},function(t,n){return e.defined(n)&&(t=P.unpack(t,n)),t._ellipsoid=i.Ellipsoid.clone(t._ellipsoid),P.createGeometry(t)}}));
