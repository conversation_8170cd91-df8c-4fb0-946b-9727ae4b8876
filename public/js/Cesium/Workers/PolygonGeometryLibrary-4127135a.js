define(["exports","./ArcType-26a3f38d","./arrayRemoveDuplicates-0d8dde26","./Matrix4-c57ffbd8","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./ComponentDatatype-ab629b88","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./EllipsoidRhumbLine-81dc828b","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryPipeline-69b47aa9","./IndexDatatype-58eb7805","./Math-355606c6","./Matrix3-31d1f01f","./PolygonPipeline-068e320c","./Transforms-2afbbfb5"],(function(e,t,n,i,o,r,a,s,c,l,u,h,p,d,f,y,g,m){"use strict";function C(){this._array=[],this._offset=0,this._length=0}Object.defineProperties(C.prototype,{length:{get:function(){return this._length}}}),C.prototype.enqueue=function(e){this._array.push(e),this._length++},C.prototype.dequeue=function(){if(0===this._length)return;const e=this._array;let t=this._offset;const n=e[t];return e[t]=void 0,t++,t>10&&2*t>e.length&&(this._array=e.slice(t),t=0),this._offset=t,this._length--,n},C.prototype.peek=function(){if(0!==this._length)return this._array[this._offset]},C.prototype.contains=function(e){return-1!==this._array.indexOf(e)},C.prototype.clear=function(){this._array.length=this._offset=this._length=0},C.prototype.sort=function(e){this._offset>0&&(this._array=this._array.slice(this._offset),this._offset=0),this._array.sort(e)};const b={computeHierarchyPackedLength:function(e,t){let n=0;const i=[e];for(;i.length>0;){const e=i.pop();if(!s.defined(e))continue;n+=2;const o=e.positions,r=e.holes;if(s.defined(o)&&o.length>0&&(n+=o.length*t.packedLength),s.defined(r)){const e=r.length;for(let t=0;t<e;++t)i.push(r[t])}}return n},packPolygonHierarchy:function(e,t,n,i){const o=[e];for(;o.length>0;){const e=o.pop();if(!s.defined(e))continue;const r=e.positions,a=e.holes;if(t[n++]=s.defined(r)?r.length:0,t[n++]=s.defined(a)?a.length:0,s.defined(r)){const e=r.length;for(let o=0;o<e;++o,n+=i.packedLength)i.pack(r[o],t,n)}if(s.defined(a)){const e=a.length;for(let t=0;t<e;++t)o.push(a[t])}}return n},unpackPolygonHierarchy:function(e,t,n){const i=e[t++],o=e[t++],r=new Array(i),a=o>0?new Array(o):void 0;for(let o=0;o<i;++o,t+=n.packedLength)r[o]=n.unpack(e,t);for(let i=0;i<o;++i)a[i]=b.unpackPolygonHierarchy(e,t,n),t=a[i].startingIndex,delete a[i].startingIndex;return{positions:r,holes:a,startingIndex:t}}},T=new i.Cartesian2;function A(e,t,n,o){return i.Cartesian2.subtract(t,e,T),i.Cartesian2.multiplyByScalar(T,n/o,T),i.Cartesian2.add(e,T,T),[T.x,T.y]}const x=new o.Cartesian3;function v(e,t,n,i){return o.Cartesian3.subtract(t,e,x),o.Cartesian3.multiplyByScalar(x,n/i,x),o.Cartesian3.add(e,x,x),[x.x,x.y,x.z]}b.subdivideLineCount=function(e,t,n){const i=o.Cartesian3.distance(e,t)/n,r=Math.max(0,Math.ceil(f.CesiumMath.log2(i)));return Math.pow(2,r)};const w=new r.Cartographic,E=new r.Cartographic,L=new r.Cartographic,P=new o.Cartesian3,I=new l.EllipsoidRhumbLine;b.subdivideRhumbLineCount=function(e,t,n,i){const o=e.cartesianToCartographic(t,w),r=e.cartesianToCartographic(n,E),a=new l.EllipsoidRhumbLine(o,r,e).surfaceDistance/i,s=Math.max(0,Math.ceil(f.CesiumMath.log2(a)));return Math.pow(2,s)},b.subdivideTexcoordLine=function(e,t,n,o,r,a){const s=b.subdivideLineCount(n,o,r),c=i.Cartesian2.distance(e,t),l=c/s,u=a;u.length=2*s;let h=0;for(let n=0;n<s;n++){const i=A(e,t,n*l,c);u[h++]=i[0],u[h++]=i[1]}return u},b.subdivideLine=function(e,t,n,i){const r=b.subdivideLineCount(e,t,n),a=o.Cartesian3.distance(e,t),c=a/r;s.defined(i)||(i=[]);const l=i;l.length=3*r;let u=0;for(let n=0;n<r;n++){const i=v(e,t,n*c,a);l[u++]=i[0],l[u++]=i[1],l[u++]=i[2]}return l},b.subdivideTexcoordRhumbLine=function(e,t,n,o,r,a,s){const c=n.cartesianToCartographic(o,w),l=n.cartesianToCartographic(r,E);I.setEndPoints(c,l);const u=I.surfaceDistance/a,h=Math.max(0,Math.ceil(f.CesiumMath.log2(u))),p=Math.pow(2,h),d=i.Cartesian2.distance(e,t),y=d/p,g=s;g.length=2*p;let m=0;for(let n=0;n<p;n++){const i=A(e,t,n*y,d);g[m++]=i[0],g[m++]=i[1]}return g},b.subdivideRhumbLine=function(e,t,n,i,o){const r=e.cartesianToCartographic(t,w),a=e.cartesianToCartographic(n,E),c=new l.EllipsoidRhumbLine(r,a,e),u=c.surfaceDistance/i,h=Math.max(0,Math.ceil(f.CesiumMath.log2(u))),p=Math.pow(2,h),d=c.surfaceDistance/p;s.defined(o)||(o=[]);const y=o;y.length=3*p;let g=0;for(let t=0;t<p;t++){const n=c.interpolateUsingSurfaceDistance(t*d,L),i=e.cartographicToCartesian(n,P);y[g++]=i.x,y[g++]=i.y,y[g++]=i.z}return y};const M=new o.Cartesian3,D=new o.Cartesian3,_=new o.Cartesian3,G=new o.Cartesian3;b.scaleToGeodeticHeightExtruded=function(e,t,n,i,r){i=s.defaultValue(i,c.Ellipsoid.WGS84);const a=M;let l=D;const u=_;let h=G;if(s.defined(e)&&s.defined(e.attributes)&&s.defined(e.attributes.position)){const s=e.attributes.position.values,c=s.length/2;for(let e=0;e<c;e+=3)o.Cartesian3.fromArray(s,e,u),i.geodeticSurfaceNormal(u,a),h=i.scaleToGeodeticSurface(u,h),l=o.Cartesian3.multiplyByScalar(a,n,l),l=o.Cartesian3.add(h,l,l),s[e+c]=l.x,s[e+1+c]=l.y,s[e+2+c]=l.z,r&&(h=o.Cartesian3.clone(u,h)),l=o.Cartesian3.multiplyByScalar(a,t,l),l=o.Cartesian3.add(h,l,l),s[e]=l.x,s[e+1]=l.y,s[e+2]=l.z}return e},b.polygonOutlinesFromHierarchy=function(e,t,i){const r=[],a=new C;let c,l,u;for(a.enqueue(e);0!==a.length;){const e=a.dequeue();let h=e.positions;if(t)for(u=h.length,c=0;c<u;c++)i.scaleToGeodeticSurface(h[c],h[c]);if(h=n.arrayRemoveDuplicates(h,o.Cartesian3.equalsEpsilon,!0),h.length<3)continue;const p=e.holes?e.holes.length:0;for(c=0;c<p;c++){const h=e.holes[c];let p=h.positions;if(t)for(u=p.length,l=0;l<u;++l)i.scaleToGeodeticSurface(p[l],p[l]);if(p=n.arrayRemoveDuplicates(p,o.Cartesian3.equalsEpsilon,!0),p.length<3)continue;r.push(p);let d=0;for(s.defined(h.holes)&&(d=h.holes.length),l=0;l<d;l++)a.enqueue(h.holes[l])}r.push(h)}return r},b.polygonsFromHierarchy=function(e,t,i,r,a){const c=[],l=[],u=new C;for(u.enqueue(e);0!==u.length;){const e=u.dequeue();let h=e.positions;const p=e.holes;let d,f;if(r)for(f=h.length,d=0;d<f;d++)a.scaleToGeodeticSurface(h[d],h[d]);if(t||(h=n.arrayRemoveDuplicates(h,o.Cartesian3.equalsEpsilon,!0)),h.length<3)continue;let y=i(h);if(!s.defined(y))continue;const m=[];let C=g.PolygonPipeline.computeWindingOrder2D(y);C===g.WindingOrder.CLOCKWISE&&(y.reverse(),h=h.slice().reverse());let b=h.slice();const T=s.defined(p)?p.length:0,A=[];let x;for(d=0;d<T;d++){const e=p[d];let c=e.positions;if(r)for(f=c.length,x=0;x<f;++x)a.scaleToGeodeticSurface(c[x],c[x]);if(t||(c=n.arrayRemoveDuplicates(c,o.Cartesian3.equalsEpsilon,!0)),c.length<3)continue;const l=i(c);if(!s.defined(l))continue;C=g.PolygonPipeline.computeWindingOrder2D(l),C===g.WindingOrder.CLOCKWISE&&(l.reverse(),c=c.slice().reverse()),A.push(c),m.push(b.length),b=b.concat(c),y=y.concat(l);let h=0;for(s.defined(e.holes)&&(h=e.holes.length),x=0;x<h;x++)u.enqueue(e.holes[x])}c.push({outerRing:h,holes:A}),l.push({positions:b,positions2D:y,holes:m})}return{hierarchy:c,polygons:l}};const N=new i.Cartesian2,S=new o.Cartesian3,R=new m.Quaternion,O=new y.Matrix3;b.computeBoundingRectangle=function(e,t,n,i,r){const a=m.Quaternion.fromAxisAngle(e,i,R),c=y.Matrix3.fromQuaternion(a,O);let l=Number.POSITIVE_INFINITY,u=Number.NEGATIVE_INFINITY,h=Number.POSITIVE_INFINITY,p=Number.NEGATIVE_INFINITY;const d=n.length;for(let e=0;e<d;++e){const i=o.Cartesian3.clone(n[e],S);y.Matrix3.multiplyByVector(c,i,i);const r=t(i,N);s.defined(r)&&(l=Math.min(l,r.x),u=Math.max(u,r.x),h=Math.min(h,r.y),p=Math.max(p,r.y))}return r.x=l,r.y=h,r.width=u-l,r.height=p-h,r},b.createGeometryFromPositions=function(e,n,o,r,c,l,h){let d=g.PolygonPipeline.triangulate(n.positions2D,n.holes);d.length<3&&(d=[0,1,2]);const f=n.positions,y=s.defined(o),m=y?o.positions:void 0;if(c){const e=f.length,t=new Array(3*e);let n=0;for(let i=0;i<e;i++){const e=f[i];t[n++]=e.x,t[n++]=e.y,t[n++]=e.z}const o={attributes:{position:new u.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:t})},indices:d,primitiveType:u.PrimitiveType.TRIANGLES};y&&(o.attributes.st=new u.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:i.Cartesian2.packArray(m)}));const r=new u.Geometry(o);return l.normal?p.GeometryPipeline.computeNormal(r):r}return h===t.ArcType.GEODESIC?g.PolygonPipeline.computeSubdivision(e,f,d,m,r):h===t.ArcType.RHUMB?g.PolygonPipeline.computeRhumbLineSubdivision(e,f,d,m,r):h===t.ArcType.NONE?g.PolygonPipeline.computeNonedivision(e,f,d,r):void 0};const q=[],k=[],B=new o.Cartesian3,H=new o.Cartesian3;b.computeWallGeometry=function(e,n,i,r,c,l){let p,y,g,m,C,T,A,x,v,w=e.length,E=0,L=0;const P=s.defined(n),I=P?n.positions:void 0;if(c)for(y=3*w*2,p=new Array(2*y),P&&(v=2*w*2,x=new Array(2*v)),g=0;g<w;g++)m=e[g],C=e[(g+1)%w],p[E]=p[E+y]=m.x,++E,p[E]=p[E+y]=m.y,++E,p[E]=p[E+y]=m.z,++E,p[E]=p[E+y]=C.x,++E,p[E]=p[E+y]=C.y,++E,p[E]=p[E+y]=C.z,++E,P&&(T=I[g],A=I[(g+1)%w],x[L]=x[L+v]=T.x,++L,x[L]=x[L+v]=T.y,++L,x[L]=x[L+v]=A.x,++L,x[L]=x[L+v]=A.y,++L);else{const n=f.CesiumMath.chordLength(r,i.maximumRadius);let a=0;if(l===t.ArcType.GEODESIC)for(g=0;g<w;g++)a+=b.subdivideLineCount(e[g],e[(g+1)%w],n);else if(l===t.ArcType.RHUMB)for(g=0;g<w;g++)a+=b.subdivideRhumbLineCount(i,e[g],e[(g+1)%w],n);else l==t.ArcType.NONE&&(a=w);for(y=3*(a+w),p=new Array(2*y),P&&(v=2*(a+w),x=new Array(2*v)),g=0;g<w;g++){let r,a;m=e[g],C=e[(g+1)%w],P&&(T=I[g],A=I[(g+1)%w]),l===t.ArcType.GEODESIC?(r=b.subdivideLine(m,C,n,k),P&&(a=b.subdivideTexcoordLine(T,A,m,C,n,q))):l===t.ArcType.RHUMB?(r=b.subdivideRhumbLine(i,m,C,n,k),P&&(a=b.subdivideTexcoordRhumbLine(T,A,i,m,C,n,q))):l===t.ArcType.NONE&&(r=new Array(3),o.Cartesian3.packArray([e[g]],r));const s=r.length;for(let e=0;e<s;++e,++E)p[E]=r[e],p[E+y]=r[e];if(p[E]=C.x,p[E+y]=C.x,++E,p[E]=C.y,p[E+y]=C.y,++E,p[E]=C.z,p[E+y]=C.z,++E,P){const e=a.length;for(let t=0;t<e;++t,++L)x[L]=a[t],x[L+v]=a[t];x[L]=A.x,x[L+v]=A.x,++L,x[L]=A.y,x[L+v]=A.y,++L}}}w=p.length;const M=d.IndexDatatype.createTypedArray(w/3,w-6*e.length);let D=0;for(w/=6,g=0;g<w;g++){const e=g,t=e+1,n=e+w,i=n+1;m=o.Cartesian3.fromArray(p,3*e,B),C=o.Cartesian3.fromArray(p,3*t,H),o.Cartesian3.equalsEpsilon(m,C,f.CesiumMath.EPSILON10,f.CesiumMath.EPSILON10)||(M[D++]=e,M[D++]=n,M[D++]=t,M[D++]=t,M[D++]=n,M[D++]=i)}const _={attributes:new h.GeometryAttributes({position:new u.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:p})}),indices:M,primitiveType:u.PrimitiveType.TRIANGLES};P&&(_.attributes.st=new u.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:x}));return new u.Geometry(_)};var z=b;e.PolygonGeometryLibrary=z}));
