define(["./Cartesian3-529c236c","./combine-0c102d93","./AttributeCompression-d2ca507e","./Cartographic-dbefb6fa","./Math-355606c6","./Ellipsoid-8e26549b","./IndexDatatype-58eb7805","./Rectangle-98b0bef0","./createTaskProcessorWorker","./defaultValue-f6d5e6da","./Matrix4-c57ffbd8","./Matrix3-31d1f01f","./RuntimeError-9b4ce3fb","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./Matrix2-e4a4609a"],(function(e,t,a,n,r,s,i,o,c,u,f,p,l,d,C,b){"use strict";const h=32767,w=new n.Cartographic,y=new e.<PERSON><PERSON>ian3;const k=new o.Rectangle,g=new s.Ellipsoid,m=new e.Cartesian3,A={min:void 0,max:void 0};const x=new e.Cartesian3,E=new e.Cartesian3,D=new e.Cartesian3,I=new e.Cartesian3,M=new e.Cartesian3;return c((function(c,u){const f=new Uint16Array(c.positions),p=new Uint16Array(c.widths),l=new Uint32Array(c.counts),d=new Uint16Array(c.batchIds);!function(t){t=new Float64Array(t);let a=0;A.min=t[a++],A.max=t[a++],o.Rectangle.unpack(t,a,k),a+=o.Rectangle.packedLength,s.Ellipsoid.unpack(t,a,g),a+=s.Ellipsoid.packedLength,e.Cartesian3.unpack(t,a,m)}(c.packedBuffer);const C=g,b=m,P=function(t,s,i,o,c){const u=t.length/3,f=t.subarray(0,u),p=t.subarray(u,2*u),l=t.subarray(2*u,3*u);a.AttributeCompression.zigZagDeltaDecode(f,p,l);const d=new Float64Array(t.length);for(let t=0;t<u;++t){const a=f[t],u=p[t],C=l[t],b=r.CesiumMath.lerp(s.west,s.east,a/h),k=r.CesiumMath.lerp(s.south,s.north,u/h),g=r.CesiumMath.lerp(i,o,C/h),m=n.Cartographic.fromRadians(b,k,g,w),A=c.cartographicToCartesian(m,y);e.Cartesian3.pack(A,d,3*t)}return d}(f,k,A.min,A.max,C),R=P.length/3,U=4*R-4,T=new Float32Array(3*U),F=new Float32Array(3*U),N=new Float32Array(3*U),L=new Float32Array(2*U),S=new Uint16Array(U);let _,v=0,G=0,W=0,B=0,O=l.length;for(_=0;_<O;++_){const t=l[_],a=p[_],n=d[_];for(let r=0;r<t;++r){let s;if(0===r){const t=e.Cartesian3.unpack(P,3*B,x),a=e.Cartesian3.unpack(P,3*(B+1),E);s=e.Cartesian3.subtract(t,a,D),e.Cartesian3.add(t,s,s)}else s=e.Cartesian3.unpack(P,3*(B+r-1),D);const i=e.Cartesian3.unpack(P,3*(B+r),I);let o;if(r===t-1){const a=e.Cartesian3.unpack(P,3*(B+t-1),x),n=e.Cartesian3.unpack(P,3*(B+t-2),E);o=e.Cartesian3.subtract(a,n,M),e.Cartesian3.add(a,o,o)}else o=e.Cartesian3.unpack(P,3*(B+r+1),M);e.Cartesian3.subtract(s,b,s),e.Cartesian3.subtract(i,b,i),e.Cartesian3.subtract(o,b,o);const c=r===t-1?2:4;for(let t=0===r?2:0;t<c;++t){e.Cartesian3.pack(i,T,v),e.Cartesian3.pack(s,F,v),e.Cartesian3.pack(o,N,v),v+=3;const r=t-2<0?-1:1;L[G++]=t%2*2-1,L[G++]=r*a,S[W++]=n}}B+=t}const j=i.IndexDatatype.createTypedArray(U,6*R-6);let q=0,z=0;for(O=R-1,_=0;_<O;++_)j[z++]=q,j[z++]=q+2,j[z++]=q+1,j[z++]=q+1,j[z++]=q+2,j[z++]=q+3,q+=4;u.push(T.buffer,F.buffer,N.buffer),u.push(L.buffer,S.buffer,j.buffer);let H={indexDatatype:2===j.BYTES_PER_ELEMENT?i.IndexDatatype.UNSIGNED_SHORT:i.IndexDatatype.UNSIGNED_INT,currentPositions:T.buffer,previousPositions:F.buffer,nextPositions:N.buffer,expandAndWidth:L.buffer,batchIds:S.buffer,indices:j.buffer};if(c.keepDecodedPositions){const e=function(e){const t=e.length,a=new Uint32Array(t+1);let n=0;for(let r=0;r<t;++r)a[r]=n,n+=e[r];return a[t]=n,a}(l);u.push(P.buffer,e.buffer),H=t.combine(H,{decodedPositions:P.buffer,decodedPositionOffsets:e.buffer})}return H}))}));
