define(["exports","./Transforms-2afbbfb5","./ComponentDatatype-ab629b88","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryPipeline-69b47aa9","./IndexDatatype-58eb7805","./Matrix4-c57ffbd8","./WebMercatorProjection-03b5db31"],(function(e,t,n,o,r,i,s,c,a,d,p){"use strict";function u(e,t,n){e=o.defaultValue(e,0),t=o.defaultValue(t,0),n=o.defaultValue(n,0),this.value=new Float32Array([e,t,n])}function f(e,t){const o=e.attributes,r=o.position,s=r.values.length/r.componentsPerAttribute;o.batchId=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:1,values:new Float32Array(s)});const c=o.batchId.values;for(let e=0;e<s;++e)c[e]=t}function l(e){const r=e.instances,i=e.projection,s=e.elementIndexUintSupported,a=e.scene3DOnly,p=e.vertexCacheOptimize,u=e.compressVertices,l=e.modelMatrix;let m,h,g=r.length;for(m=0;m<g;++m)if(o.defined(r[m].geometry)){r[m].geometry.primitiveType;break}if(function(e,t,n){let r=!n;const i=e.length;let s;if(!r&&i>1){const t=e[0].modelMatrix;for(s=1;s<i;++s)if(!d.Matrix4.equals(t,e[s].modelMatrix)){r=!0;break}}if(r)for(s=0;s<i;++s)o.defined(e[s].geometry)&&c.GeometryPipeline.transformToWorldCoordinates(e[s]);else d.Matrix4.multiplyTransformation(t,e[0].modelMatrix,t)}(r,l,a),!a)for(m=0;m<g;++m)o.defined(r[m].geometry)&&c.GeometryPipeline.splitLongitude(r[m]);if(function(e){const t=e.length;for(let n=0;n<t;++n){const t=e[n];o.defined(t.geometry)?f(t.geometry,n):o.defined(t.westHemisphereGeometry)&&o.defined(t.eastHemisphereGeometry)&&(f(t.westHemisphereGeometry,n),f(t.eastHemisphereGeometry,n))}}(r),p)for(m=0;m<g;++m){const e=r[m];o.defined(e.geometry)?(c.GeometryPipeline.reorderForPostVertexCache(e.geometry),c.GeometryPipeline.reorderForPreVertexCache(e.geometry)):o.defined(e.westHemisphereGeometry)&&o.defined(e.eastHemisphereGeometry)&&(c.GeometryPipeline.reorderForPostVertexCache(e.westHemisphereGeometry),c.GeometryPipeline.reorderForPreVertexCache(e.westHemisphereGeometry),c.GeometryPipeline.reorderForPostVertexCache(e.eastHemisphereGeometry),c.GeometryPipeline.reorderForPreVertexCache(e.eastHemisphereGeometry))}let y=c.GeometryPipeline.combineInstances(r);for(g=y.length,m=0;m<g;++m){h=y[m];const e=h.attributes;if(a)for(const t in e)e.hasOwnProperty(t)&&e[t].componentDatatype===n.ComponentDatatype.DOUBLE&&c.GeometryPipeline.encodeAttribute(h,t,`${t}3DHigh`,`${t}3DLow`);else for(const r in e)if(e.hasOwnProperty(r)&&e[r].componentDatatype===n.ComponentDatatype.DOUBLE){const e=`${r}3D`,n=`${r}2D`;c.GeometryPipeline.projectTo2D(h,r,e,n,i),o.defined(h.boundingSphere)&&"position"===r&&(h.boundingSphereCV=t.BoundingSphere.fromVertices(h.attributes.position2D.values)),c.GeometryPipeline.encodeAttribute(h,e,`${e}High`,`${e}Low`),c.GeometryPipeline.encodeAttribute(h,n,`${n}High`,`${n}Low`)}u&&c.GeometryPipeline.compressVertices(h)}if(!s){let e=[];for(g=y.length,m=0;m<g;++m)h=y[m],e=e.concat(c.GeometryPipeline.fitToUnsignedShortIndices(h));y=e}return y}function m(e,t,n,r){let i,s,c;const a=r.length-1;if(a>=0){const e=r[a];i=e.offset+e.count,c=e.index,s=n[c].indices.length}else i=0,c=0,s=n[c].indices.length;const d=e.length;for(let a=0;a<d;++a){const d=e[a][t];if(!o.defined(d))continue;const p=d.indices.length;i+p>s&&(i=0,s=n[++c].indices.length),r.push({index:c,offset:i,count:p}),i+=p}}Object.defineProperties(u.prototype,{componentDatatype:{get:function(){return n.ComponentDatatype.FLOAT}},componentsPerAttribute:{get:function(){return 3}},normalize:{get:function(){return!1}}}),u.fromCartesian3=function(e){return new u(e.x,e.y,e.z)},u.toValue=function(e,t){return o.defined(t)||(t=new Float32Array([e.x,e.y,e.z])),t[0]=e.x,t[1]=e.y,t[2]=e.z,t};const h={};function g(e,t){const n=e.attributes;for(const e in n)if(n.hasOwnProperty(e)){const r=n[e];o.defined(r)&&o.defined(r.values)&&t.push(r.values.buffer)}o.defined(e.indices)&&t.push(e.indices.buffer)}function y(e,t){const n=e.length,r=new Float64Array(1+19*n);let i=0;r[i++]=n;for(let t=0;t<n;t++){const n=e[t];if(d.Matrix4.pack(n.modelMatrix,r,i),i+=d.Matrix4.packedLength,o.defined(n.attributes)&&o.defined(n.attributes.offset)){const e=n.attributes.offset.value;r[i]=e[0],r[i+1]=e[1],r[i+2]=e[2]}i+=3}return t.push(r.buffer),r}function b(e){const n=e.length,r=1+(t.BoundingSphere.packedLength+1)*n,i=new Float32Array(r);let s=0;i[s++]=n;for(let r=0;r<n;++r){const n=e[r];o.defined(n)?(i[s++]=1,t.BoundingSphere.pack(e[r],i,s)):i[s++]=0,s+=t.BoundingSphere.packedLength}return i}function x(e){const n=new Array(e[0]);let o=0,r=1;for(;r<e.length;)1===e[r++]&&(n[o]=t.BoundingSphere.unpack(e,r)),++o,r+=t.BoundingSphere.packedLength;return n}h.combineGeometry=function(e){let n,r;const i=e.instances,s=i.length;let a,d,p=!1;s>0&&(n=l(e),n.length>0&&(r=c.GeometryPipeline.createAttributeLocations(n[0]),e.createPickOffsets&&(a=function(e,t){const n=[];return m(e,"geometry",t,n),m(e,"westHemisphereGeometry",t,n),m(e,"eastHemisphereGeometry",t,n),n}(i,n))),o.defined(i[0].attributes)&&o.defined(i[0].attributes.offset)&&(d=new Array(s),p=!0));const u=new Array(s),f=new Array(s);for(let e=0;e<s;++e){const n=i[e],r=n.geometry;o.defined(r)&&(u[e]=r.boundingSphere,f[e]=r.boundingSphereCV,p&&(d[e]=n.geometry.offsetAttribute));const s=n.eastHemisphereGeometry,c=n.westHemisphereGeometry;o.defined(s)&&o.defined(c)&&(o.defined(s.boundingSphere)&&o.defined(c.boundingSphere)&&(u[e]=t.BoundingSphere.union(s.boundingSphere,c.boundingSphere)),o.defined(s.boundingSphereCV)&&o.defined(c.boundingSphereCV)&&(f[e]=t.BoundingSphere.union(s.boundingSphereCV,c.boundingSphereCV)))}return{geometries:n,modelMatrix:e.modelMatrix,attributeLocations:r,pickOffsets:a,offsetInstanceExtend:d,boundingSpheres:u,boundingSpheresCV:f}},h.packCreateGeometryResults=function(e,n){const r=new Float64Array(function(e){let n=1;const r=e.length;for(let i=0;i<r;i++){const r=e[i];if(++n,!o.defined(r))continue;const s=r.attributes;n+=7+2*t.BoundingSphere.packedLength+(o.defined(r.indices)?r.indices.length:0);for(const e in s)s.hasOwnProperty(e)&&o.defined(s[e])&&(n+=5+s[e].values.length)}return n}(e)),i=[],s={},c=e.length;let a=0;r[a++]=c;for(let n=0;n<c;n++){const c=e[n],d=o.defined(c);if(r[a++]=d?1:0,!d)continue;r[a++]=c.primitiveType,r[a++]=c.geometryType,r[a++]=o.defaultValue(c.offsetAttribute,-1);const p=o.defined(c.boundingSphere)?1:0;r[a++]=p,p&&t.BoundingSphere.pack(c.boundingSphere,r,a),a+=t.BoundingSphere.packedLength;const u=o.defined(c.boundingSphereCV)?1:0;r[a++]=u,u&&t.BoundingSphere.pack(c.boundingSphereCV,r,a),a+=t.BoundingSphere.packedLength;const f=c.attributes,l=[];for(const e in f)f.hasOwnProperty(e)&&o.defined(f[e])&&(l.push(e),o.defined(s[e])||(s[e]=i.length,i.push(e)));r[a++]=l.length;for(let e=0;e<l.length;e++){const t=l[e],n=f[t];r[a++]=s[t],r[a++]=n.componentDatatype,r[a++]=n.componentsPerAttribute,r[a++]=n.normalize?1:0,r[a++]=n.values.length,r.set(n.values,a),a+=n.values.length}const m=o.defined(c.indices)?c.indices.length:0;r[a++]=m,m>0&&(r.set(c.indices,a),a+=m)}return n.push(r.buffer),{stringTable:i,packedData:r}},h.unpackCreateGeometryResults=function(e){const o=e.stringTable,r=e.packedData;let c;const d=new Array(r[0]);let p=0,u=1;for(;u<r.length;){if(!(1===r[u++])){d[p++]=void 0;continue}const e=r[u++],f=r[u++];let l,m,h=r[u++];-1===h&&(h=void 0);1===r[u++]&&(l=t.BoundingSphere.unpack(r,u)),u+=t.BoundingSphere.packedLength;let g,y,b;1===r[u++]&&(m=t.BoundingSphere.unpack(r,u)),u+=t.BoundingSphere.packedLength;const x=new s.GeometryAttributes,G=r[u++];for(c=0;c<G;c++){const e=o[r[u++]],t=r[u++];b=r[u++];const s=0!==r[u++];g=r[u++],y=n.ComponentDatatype.createTypedArray(t,g);for(let e=0;e<g;e++)y[e]=r[u++];x[e]=new i.GeometryAttribute({componentDatatype:t,componentsPerAttribute:b,normalize:s,values:y})}let S;if(g=r[u++],g>0){const e=y.length/b;for(S=a.IndexDatatype.createTypedArray(e,g),c=0;c<g;c++)S[c]=r[u++]}d[p++]=new i.Geometry({primitiveType:e,geometryType:f,boundingSphere:l,boundingSphereCV:m,indices:S,attributes:x,offsetAttribute:h})}return d},h.packCombineGeometryParameters=function(e,n){const o=e.createGeometryResults,r=o.length;for(let e=0;e<r;e++)n.push(o[e].packedData.buffer);return{createGeometryResults:e.createGeometryResults,packedInstances:y(e.instances,n),ellipsoid:e.ellipsoid,isGeographic:e.projection instanceof t.GeographicProjection,elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:e.modelMatrix,createPickOffsets:e.createPickOffsets}},h.unpackCombineGeometryParameters=function(e){const n=function(e){const t=e,n=new Array(t[0]);let r=0,i=1;for(;i<t.length;){const e=d.Matrix4.unpack(t,i);let s;i+=d.Matrix4.packedLength,o.defined(t[i])&&(s={offset:new u(t[i],t[i+1],t[i+2])}),i+=3,n[r++]={modelMatrix:e,attributes:s}}return n}(e.packedInstances),i=e.createGeometryResults,s=i.length;let c=0;for(let e=0;e<s;e++){const t=h.unpackCreateGeometryResults(i[e]),o=t.length;for(let e=0;e<o;e++){const o=t[e];n[c].geometry=o,++c}}const a=r.Ellipsoid.clone(e.ellipsoid);return{instances:n,ellipsoid:a,projection:e.isGeographic?new t.GeographicProjection(a):new p.WebMercatorProjection(a),elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:d.Matrix4.clone(e.modelMatrix),createPickOffsets:e.createPickOffsets}},h.packCombineGeometryResults=function(e,t){o.defined(e.geometries)&&function(e,t){const n=e.length;for(let o=0;o<n;++o)g(e[o],t)}(e.geometries,t);const n=b(e.boundingSpheres),r=b(e.boundingSpheresCV);return t.push(n.buffer,r.buffer),{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:n,boundingSpheresCV:r}},h.unpackCombineGeometryResults=function(e){return{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:x(e.boundingSpheres),boundingSpheresCV:x(e.boundingSpheresCV)}};var G=h;e.PrimitivePipeline=G}));
