define(["exports","./Cartesian3-529c236c","./Cartographic-dbefb6fa","./defaultValue-f6d5e6da","./Interval-d6c8d27a","./Math-355606c6","./Matrix3-31d1f01f"],(function(t,n,e,a,i,r,s){"use strict";function o(t,e){e=n.Cartesian3.clone(a.defaultValue(e,n.Cartesian3.ZERO)),n.Cartesian3.equals(e,n.Cartesian3.ZERO)||n.Cartesian3.normalize(e,e),this.origin=n.Cartesian3.clone(a.defaultValue(t,n.Cartesian3.ZERO)),this.direction=e}o.clone=function(t,e){if(a.defined(t))return a.defined(e)?(e.origin=n.Cartesian3.clone(t.origin),e.direction=n.Cartesian3.clone(t.direction),e):new o(t.origin,t.direction)},o.getPoint=function(t,e,i){return a.defined(i)||(i=new n.Cartesian3),i=n.Cartesian3.multiplyByScalar(t.direction,e,i),n.Cartesian3.add(t.origin,i,i)};const c={};function u(t,n,e){const a=t+n;return r.CesiumMath.sign(t)!==r.CesiumMath.sign(n)&&Math.abs(a/Math.max(Math.abs(t),Math.abs(n)))<e?0:a}c.computeDiscriminant=function(t,n,e){return n*n-4*t*e},c.computeRealRoots=function(t,n,e){let a;if(0===t)return 0===n?[]:[-e/n];if(0===n){if(0===e)return[0,0];const n=Math.abs(e),i=Math.abs(t);if(n<i&&n/i<r.CesiumMath.EPSILON14)return[0,0];if(n>i&&i/n<r.CesiumMath.EPSILON14)return[];if(a=-e/t,a<0)return[];const s=Math.sqrt(a);return[-s,s]}if(0===e)return a=-n/t,a<0?[a,0]:[0,a];const i=u(n*n,-(4*t*e),r.CesiumMath.EPSILON14);if(i<0)return[];const s=-.5*u(n,r.CesiumMath.sign(n)*Math.sqrt(i),r.CesiumMath.EPSILON14);return n>0?[s/t,e/s]:[e/s,s/t]};var l=c;const C={};function h(t,n,e,a){const i=t,r=n/3,s=e/3,o=a,c=i*s,u=r*o,l=r*r,C=s*s,h=i*s-l,M=i*o-r*s,m=r*o-C,f=4*h*m-M*M;let d,g;if(f<0){let t,n,e;l*u>=c*C?(t=i,n=h,e=-2*r*h+i*M):(t=o,n=m,e=-o*M+2*s*m);const a=-(e<0?-1:1)*Math.abs(t)*Math.sqrt(-f);g=-e+a;const p=g/2,w=p<0?-Math.pow(-p,1/3):Math.pow(p,1/3),R=g===a?-w:-n/w;return d=n<=0?w+R:-e/(w*w+R*R+n),l*u>=c*C?[(d-r)/i]:[-o/(d+s)]}const p=h,w=-2*r*h+i*M,R=m,S=-o*M+2*s*m,O=Math.sqrt(f),x=Math.sqrt(3)/2;let y=Math.abs(Math.atan2(i*O,-w)/3);d=2*Math.sqrt(-p);let P=Math.cos(y);g=d*P;let N=d*(-P/2-x*Math.sin(y));const b=g+N>2*r?g-r:N-r,q=i,I=b/q;y=Math.abs(Math.atan2(o*O,-S)/3),d=2*Math.sqrt(-R),P=Math.cos(y),g=d*P,N=d*(-P/2-x*Math.sin(y));const L=-o,E=g+N<2*s?g+s:N+s,v=L/E,z=-b*E-q*L,T=(s*z-r*(b*L))/(-r*z+s*(q*E));return I<=T?I<=v?T<=v?[I,T,v]:[I,v,T]:[v,I,T]:I<=v?[T,I,v]:T<=v?[T,v,I]:[v,T,I]}C.computeDiscriminant=function(t,n,e,a){const i=n*n,r=e*e;return 18*t*n*e*a+i*r-27*(t*t)*(a*a)-4*(t*r*e+i*n*a)},C.computeRealRoots=function(t,n,e,a){let i,r;if(0===t)return l.computeRealRoots(n,e,a);if(0===n){if(0===e){if(0===a)return[0,0,0];r=-a/t;const n=r<0?-Math.pow(-r,1/3):Math.pow(r,1/3);return[n,n,n]}return 0===a?(i=l.computeRealRoots(t,0,e),0===i.Length?[0]:[i[0],0,i[1]]):h(t,0,e,a)}return 0===e?0===a?(r=-n/t,r<0?[r,0,0]:[0,0,r]):h(t,n,0,a):0===a?(i=l.computeRealRoots(t,n,e),0===i.length?[0]:i[1]<=0?[i[0],i[1],0]:i[0]>=0?[0,i[0],i[1]]:[i[0],0,i[1]]):h(t,n,e,a)};var M=C;const m={};function f(t,n,e,a){const i=t*t,s=n-3*i/8,o=e-n*t/2+i*t/8,c=a-e*t/4+n*i/16-3*i*i/256,u=M.computeRealRoots(1,2*s,s*s-4*c,-o*o);if(u.length>0){const n=-t/4,e=u[u.length-1];if(Math.abs(e)<r.CesiumMath.EPSILON14){const t=l.computeRealRoots(1,s,c);if(2===t.length){const e=t[0],a=t[1];let i;if(e>=0&&a>=0){const t=Math.sqrt(e),i=Math.sqrt(a);return[n-i,n-t,n+t,n+i]}if(e>=0&&a<0)return i=Math.sqrt(e),[n-i,n+i];if(e<0&&a>=0)return i=Math.sqrt(a),[n-i,n+i]}return[]}if(e>0){const t=Math.sqrt(e),a=(s+e-o/t)/2,i=(s+e+o/t)/2,r=l.computeRealRoots(1,t,a),c=l.computeRealRoots(1,-t,i);return 0!==r.length?(r[0]+=n,r[1]+=n,0!==c.length?(c[0]+=n,c[1]+=n,r[1]<=c[0]?[r[0],r[1],c[0],c[1]]:c[1]<=r[0]?[c[0],c[1],r[0],r[1]]:r[0]>=c[0]&&r[1]<=c[1]?[c[0],r[0],r[1],c[1]]:c[0]>=r[0]&&c[1]<=r[1]?[r[0],c[0],c[1],r[1]]:r[0]>c[0]&&r[0]<c[1]?[c[0],r[0],c[1],r[1]]:[r[0],c[0],r[1],c[1]]):r):0!==c.length?(c[0]+=n,c[1]+=n,c):[]}}return[]}function d(t,n,e,a){const i=t*t,s=-2*n,o=e*t+n*n-4*a,c=i*a-e*n*t+e*e,u=M.computeRealRoots(1,s,o,c);if(u.length>0){const s=u[0],o=n-s,c=o*o,C=t/2,h=o/2,M=c-4*a,m=c+4*Math.abs(a),f=i-4*s,d=i+4*Math.abs(s);let g,p,w,R,S,O;if(s<0||M*d<f*m){const n=Math.sqrt(f);g=n/2,p=0===n?0:(t*h-e)/n}else{const n=Math.sqrt(M);g=0===n?0:(t*h-e)/n,p=n/2}0===C&&0===g?(w=0,R=0):r.CesiumMath.sign(C)===r.CesiumMath.sign(g)?(w=C+g,R=s/w):(R=C-g,w=s/R),0===h&&0===p?(S=0,O=0):r.CesiumMath.sign(h)===r.CesiumMath.sign(p)?(S=h+p,O=a/S):(O=h-p,S=a/O);const x=l.computeRealRoots(1,w,S),y=l.computeRealRoots(1,R,O);if(0!==x.length)return 0!==y.length?x[1]<=y[0]?[x[0],x[1],y[0],y[1]]:y[1]<=x[0]?[y[0],y[1],x[0],x[1]]:x[0]>=y[0]&&x[1]<=y[1]?[y[0],x[0],x[1],y[1]]:y[0]>=x[0]&&y[1]<=x[1]?[x[0],y[0],y[1],x[1]]:x[0]>y[0]&&x[0]<y[1]?[y[0],x[0],y[1],x[1]]:[x[0],y[0],x[1],y[1]]:x;if(0!==y.length)return y}return[]}m.computeDiscriminant=function(t,n,e,a,i){const r=t*t,s=n*n,o=s*n,c=e*e,u=c*e,l=a*a,C=l*a,h=i*i;return s*c*l-4*o*C-4*t*u*l+18*t*n*e*C-27*r*l*l+256*(r*t)*(h*i)+i*(18*o*e*a-4*s*u+16*t*c*c-80*t*n*c*a-6*t*s*l+144*r*e*l)+h*(144*t*s*e-27*s*s-128*r*c-192*r*n*a)},m.computeRealRoots=function(t,n,e,a,i){if(Math.abs(t)<r.CesiumMath.EPSILON15)return M.computeRealRoots(n,e,a,i);const s=n/t,o=e/t,c=a/t,u=i/t;let l=s<0?1:0;switch(l+=o<0?l+1:l,l+=c<0?l+1:l,l+=u<0?l+1:l,l){case 0:case 3:case 4:case 6:case 7:case 9:case 10:case 12:case 13:case 14:case 15:return f(s,o,c,u);case 1:case 2:case 5:case 8:case 11:return d(s,o,c,u);default:return}};var g=m;const p={rayPlane:function(t,e,i){a.defined(i)||(i=new n.Cartesian3);const s=t.origin,o=t.direction,c=e.normal,u=n.Cartesian3.dot(c,o);if(Math.abs(u)<r.CesiumMath.EPSILON15)return;const l=(-e.distance-n.Cartesian3.dot(c,s))/u;return l<0?void 0:(i=n.Cartesian3.multiplyByScalar(o,l,i),n.Cartesian3.add(s,i,i))}},w=new n.Cartesian3,R=new n.Cartesian3,S=new n.Cartesian3,O=new n.Cartesian3,x=new n.Cartesian3;p.rayTriangleParametric=function(t,e,i,s,o){o=a.defaultValue(o,!1);const c=t.origin,u=t.direction,l=n.Cartesian3.subtract(i,e,w),C=n.Cartesian3.subtract(s,e,R),h=n.Cartesian3.cross(u,C,S),M=n.Cartesian3.dot(l,h);let m,f,d,g,p;if(o){if(M<r.CesiumMath.EPSILON6)return;if(m=n.Cartesian3.subtract(c,e,O),d=n.Cartesian3.dot(m,h),d<0||d>M)return;if(f=n.Cartesian3.cross(m,l,x),g=n.Cartesian3.dot(u,f),g<0||d+g>M)return;p=n.Cartesian3.dot(C,f)/M}else{if(Math.abs(M)<r.CesiumMath.EPSILON6)return;const t=1/M;if(m=n.Cartesian3.subtract(c,e,O),d=n.Cartesian3.dot(m,h)*t,d<0||d>1)return;if(f=n.Cartesian3.cross(m,l,x),g=n.Cartesian3.dot(u,f)*t,g<0||d+g>1)return;p=n.Cartesian3.dot(C,f)*t}return p},p.rayTriangle=function(t,e,i,r,s,o){const c=p.rayTriangleParametric(t,e,i,r,s);if(a.defined(c)&&!(c<0))return a.defined(o)||(o=new n.Cartesian3),n.Cartesian3.multiplyByScalar(t.direction,c,o),n.Cartesian3.add(t.origin,o,o)};const y=new o;p.lineSegmentTriangle=function(t,e,i,r,s,o,c){const u=y;n.Cartesian3.clone(t,u.origin),n.Cartesian3.subtract(e,t,u.direction),n.Cartesian3.normalize(u.direction,u.direction);const l=p.rayTriangleParametric(u,i,r,s,o);if(!(!a.defined(l)||l<0||l>n.Cartesian3.distance(t,e)))return a.defined(c)||(c=new n.Cartesian3),n.Cartesian3.multiplyByScalar(u.direction,l,c),n.Cartesian3.add(u.origin,c,c)};const P={root0:0,root1:0};function N(t,e,r){a.defined(r)||(r=new i.Interval);const s=t.origin,o=t.direction,c=e.center,u=e.radius*e.radius,l=n.Cartesian3.subtract(s,c,S),C=function(t,n,e,a){const i=n*n-4*t*e;if(i<0)return;if(i>0){const e=1/(2*t),r=Math.sqrt(i),s=(-n+r)*e,o=(-n-r)*e;return s<o?(a.root0=s,a.root1=o):(a.root0=o,a.root1=s),a}const r=-n/(2*t);return 0!==r?(a.root0=a.root1=r,a):void 0}(n.Cartesian3.dot(o,o),2*n.Cartesian3.dot(o,l),n.Cartesian3.magnitudeSquared(l)-u,P);if(a.defined(C))return r.start=C.root0,r.stop=C.root1,r}p.raySphere=function(t,n,e){if(e=N(t,n,e),a.defined(e)&&!(e.stop<0))return e.start=Math.max(e.start,0),e};const b=new o;p.lineSegmentSphere=function(t,e,i,r){const s=b;n.Cartesian3.clone(t,s.origin);const o=n.Cartesian3.subtract(e,t,s.direction),c=n.Cartesian3.magnitude(o);if(n.Cartesian3.normalize(o,o),r=N(s,i,r),!(!a.defined(r)||r.stop<0||r.start>c))return r.start=Math.max(r.start,0),r.stop=Math.min(r.stop,c),r};const q=new n.Cartesian3,I=new n.Cartesian3;function L(t,n,e){const a=t+n;return r.CesiumMath.sign(t)!==r.CesiumMath.sign(n)&&Math.abs(a/Math.max(Math.abs(t),Math.abs(n)))<e?0:a}p.rayEllipsoid=function(t,e){const a=e.oneOverRadii,r=n.Cartesian3.multiplyComponents(a,t.origin,q),s=n.Cartesian3.multiplyComponents(a,t.direction,I),o=n.Cartesian3.magnitudeSquared(r),c=n.Cartesian3.dot(r,s);let u,l,C,h,M;if(o>1){if(c>=0)return;const t=c*c;if(u=o-1,l=n.Cartesian3.magnitudeSquared(s),C=l*u,t<C)return;if(t>C){h=c*c-C,M=-c+Math.sqrt(h);const t=M/l,n=u/M;return t<n?new i.Interval(t,n):{start:n,stop:t}}const e=Math.sqrt(u/l);return new i.Interval(e,e)}return o<1?(u=o-1,l=n.Cartesian3.magnitudeSquared(s),C=l*u,h=c*c-C,M=-c+Math.sqrt(h),new i.Interval(0,M/l)):c<0?(l=n.Cartesian3.magnitudeSquared(s),new i.Interval(0,-c/l)):void 0};const E=new n.Cartesian3,v=new n.Cartesian3,z=new n.Cartesian3,T=new n.Cartesian3,U=new n.Cartesian3,W=new s.Matrix3,B=new s.Matrix3,V=new s.Matrix3,Z=new s.Matrix3,A=new s.Matrix3,D=new s.Matrix3,F=new s.Matrix3,G=new n.Cartesian3,Y=new n.Cartesian3,_=new e.Cartographic;p.grazingAltitudeLocation=function(t,e){const i=t.origin,o=t.direction;if(!n.Cartesian3.equals(i,n.Cartesian3.ZERO)){const t=e.geodeticSurfaceNormal(i,E);if(n.Cartesian3.dot(o,t)>=0)return i}const c=a.defined(this.rayEllipsoid(t,e)),u=e.transformPositionToScaledSpace(o,E),C=n.Cartesian3.normalize(u,u),h=n.Cartesian3.mostOrthogonalAxis(u,T),M=n.Cartesian3.normalize(n.Cartesian3.cross(h,C,v),v),m=n.Cartesian3.normalize(n.Cartesian3.cross(C,M,z),z),f=W;f[0]=C.x,f[1]=C.y,f[2]=C.z,f[3]=M.x,f[4]=M.y,f[5]=M.z,f[6]=m.x,f[7]=m.y,f[8]=m.z;const d=s.Matrix3.transpose(f,B),p=s.Matrix3.fromScale(e.radii,V),w=s.Matrix3.fromScale(e.oneOverRadii,Z),R=A;R[0]=0,R[1]=-o.z,R[2]=o.y,R[3]=o.z,R[4]=0,R[5]=-o.x,R[6]=-o.y,R[7]=o.x,R[8]=0;const S=s.Matrix3.multiply(s.Matrix3.multiply(d,w,D),R,D),O=s.Matrix3.multiply(s.Matrix3.multiply(S,p,F),f,F),x=s.Matrix3.multiplyByVector(S,i,U),y=function(t,e,a,i,o){const c=i*i,u=o*o,C=(t[s.Matrix3.COLUMN1ROW1]-t[s.Matrix3.COLUMN2ROW2])*u,h=o*(i*L(t[s.Matrix3.COLUMN1ROW0],t[s.Matrix3.COLUMN0ROW1],r.CesiumMath.EPSILON15)+e.y),M=t[s.Matrix3.COLUMN0ROW0]*c+t[s.Matrix3.COLUMN2ROW2]*u+i*e.x+a,m=u*L(t[s.Matrix3.COLUMN2ROW1],t[s.Matrix3.COLUMN1ROW2],r.CesiumMath.EPSILON15),f=o*(i*L(t[s.Matrix3.COLUMN2ROW0],t[s.Matrix3.COLUMN0ROW2])+e.z);let d;const p=[];if(0===f&&0===m){if(d=l.computeRealRoots(C,h,M),0===d.length)return p;const t=d[0],e=Math.sqrt(Math.max(1-t*t,0));if(p.push(new n.Cartesian3(i,o*t,o*-e)),p.push(new n.Cartesian3(i,o*t,o*e)),2===d.length){const t=d[1],e=Math.sqrt(Math.max(1-t*t,0));p.push(new n.Cartesian3(i,o*t,o*-e)),p.push(new n.Cartesian3(i,o*t,o*e))}return p}const w=f*f,R=m*m,S=f*m,O=C*C+R,x=2*(h*C+S),y=2*M*C+h*h-R+w,P=2*(M*h-S),N=M*M-w;if(0===O&&0===x&&0===y&&0===P)return p;d=g.computeRealRoots(O,x,y,P,N);const b=d.length;if(0===b)return p;for(let t=0;t<b;++t){const e=d[t],a=e*e,s=Math.max(1-a,0),c=Math.sqrt(s);let u;u=r.CesiumMath.sign(C)===r.CesiumMath.sign(M)?L(C*a+M,h*e,r.CesiumMath.EPSILON12):r.CesiumMath.sign(M)===r.CesiumMath.sign(h*e)?L(C*a,h*e+M,r.CesiumMath.EPSILON12):L(C*a+h*e,M,r.CesiumMath.EPSILON12);const l=u*L(m*e,f,r.CesiumMath.EPSILON15);l<0?p.push(new n.Cartesian3(i,o*e,o*c)):l>0?p.push(new n.Cartesian3(i,o*e,o*-c)):0!==c?(p.push(new n.Cartesian3(i,o*e,o*-c)),p.push(new n.Cartesian3(i,o*e,o*c)),++t):p.push(new n.Cartesian3(i,o*e,o*c))}return p}(O,n.Cartesian3.negate(x,E),0,0,1);let P,N;const b=y.length;if(b>0){let t=n.Cartesian3.clone(n.Cartesian3.ZERO,Y),a=Number.NEGATIVE_INFINITY;for(let e=0;e<b;++e){P=s.Matrix3.multiplyByVector(p,s.Matrix3.multiplyByVector(f,y[e],G),G);const r=n.Cartesian3.normalize(n.Cartesian3.subtract(P,i,T),T),c=n.Cartesian3.dot(r,o);c>a&&(a=c,t=n.Cartesian3.clone(P,t))}const u=e.cartesianToCartographic(t,_);return a=r.CesiumMath.clamp(a,0,1),N=n.Cartesian3.magnitude(n.Cartesian3.subtract(t,i,T))*Math.sqrt(1-a*a),N=c?-N:N,u.height=N,e.cartographicToCartesian(u,new n.Cartesian3)}};const j=new n.Cartesian3;p.lineSegmentPlane=function(t,e,i,s){a.defined(s)||(s=new n.Cartesian3);const o=n.Cartesian3.subtract(e,t,j),c=i.normal,u=n.Cartesian3.dot(c,o);if(Math.abs(u)<r.CesiumMath.EPSILON6)return;const l=n.Cartesian3.dot(c,t),C=-(i.distance+l)/u;return C<0||C>1?void 0:(n.Cartesian3.multiplyByScalar(o,C,s),n.Cartesian3.add(t,s,s),s)},p.trianglePlaneIntersection=function(t,e,a,i){const r=i.normal,s=i.distance,o=n.Cartesian3.dot(r,t)+s<0,c=n.Cartesian3.dot(r,e)+s<0,u=n.Cartesian3.dot(r,a)+s<0;let l,C,h=0;if(h+=o?1:0,h+=c?1:0,h+=u?1:0,1!==h&&2!==h||(l=new n.Cartesian3,C=new n.Cartesian3),1===h){if(o)return p.lineSegmentPlane(t,e,i,l),p.lineSegmentPlane(t,a,i,C),{positions:[t,e,a,l,C],indices:[0,3,4,1,2,4,1,4,3]};if(c)return p.lineSegmentPlane(e,a,i,l),p.lineSegmentPlane(e,t,i,C),{positions:[t,e,a,l,C],indices:[1,3,4,2,0,4,2,4,3]};if(u)return p.lineSegmentPlane(a,t,i,l),p.lineSegmentPlane(a,e,i,C),{positions:[t,e,a,l,C],indices:[2,3,4,0,1,4,0,4,3]}}else if(2===h){if(!o)return p.lineSegmentPlane(e,t,i,l),p.lineSegmentPlane(a,t,i,C),{positions:[t,e,a,l,C],indices:[1,2,4,1,4,3,0,3,4]};if(!c)return p.lineSegmentPlane(a,e,i,l),p.lineSegmentPlane(t,e,i,C),{positions:[t,e,a,l,C],indices:[2,0,4,2,4,3,1,3,4]};if(!u)return p.lineSegmentPlane(t,a,i,l),p.lineSegmentPlane(e,a,i,C),{positions:[t,e,a,l,C],indices:[0,1,4,0,4,3,2,3,4]}}};var k=p;t.IntersectionTests=k,t.Ray=o}));
