define(["./defaultValue-f6d5e6da","./createTaskProcessorWorker","./EV_IndexedDBProvider-596d1bfc","./DeveloperError-c85858c1"],(function(e,a,r,s){"use strict";let n;function t(e,a,r){const s=self.webkitPostMessage||self.postMessage,n={workerType:e,success:a};r&&(n.dataName=r.keyName);try{s(n)}catch(e){s(n)}}return a((function(a,s){const o=a.indexedDB;let c=a.taskData;if(c.workerTaskID=a.workerTaskID,e.defined(n))if(e.defined(n.iDB))switch(c.workerType){case r.EV_IndexDBType.TERRAIN:n.saveTerrainData(c,t);break;case r.EV_IndexDBType.IMAGERY:n.saveImagery(c,t);break;case r.EV_IndexDBType.TILES3D:n.save3DTileArrayBuffer(c,t);break;case r.EV_IndexDBType.BIN:n.saveGltfBin(c,t);break;case r.EV_IndexDBType.IMAGE:n.saveImage(c,t);break;case r.EV_IndexDBType.FILE:n.saveFile(c,t);break;default:console.log(" ")}else t(c.workerType,0);else n=new r.EV_IndexedDBProvider(o),n.createIDB((function(){})),t(c.workerType,0)}))}));
