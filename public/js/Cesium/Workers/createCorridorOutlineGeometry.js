define(["./arrayRemoveDuplicates-0d8dde26","./Transforms-2afbbfb5","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./PolylineVolumeGeometryLibrary-e8146b77","./CorridorGeometryLibrary-b2575ddf","./defaultValue-f6d5e6da","./Ellipsoid-8e26549b","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-58eb7805","./Math-355606c6","./PolygonPipeline-39b84ada","./Cartographic-dbefb6fa","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./WebGLConstants-7f557f93","./EllipsoidTangentPlane-a6ea67fb","./AxisAlignedBoundingBox-d98e5354","./IntersectionTests-01432fe7","./Plane-06f34fae","./PolylinePipeline-07b67faf","./EllipsoidGeodesic-8ac7b85d","./EllipsoidRhumbLine-6774fec3","./Matrix2-e4a4609a"],(function(e,t,i,r,o,n,s,a,l,u,d,p,f,h,c,y,g,b,m,A,_,E,C,G,T,v,P,w,L,D,x,k){"use strict";const N=new i.Cartesian3,V=new i.Cartesian3,H=new i.Cartesian3;function O(e,t){const a=[],d=e.positions,f=e.corners,h=e.endPositions,c=new u.GeometryAttributes;let y,g,b,m=0,A=0,_=0;for(g=0;g<d.length;g+=2)b=d[g].length-3,m+=b,_+=b/3*4,A+=d[g+1].length-3;for(m+=3,A+=3,g=0;g<f.length;g++){y=f[g];const e=f[g].leftPositions;s.defined(e)?(b=e.length,m+=b,_+=b/3*2):(b=f[g].rightPositions.length,A+=b,_+=b/3*2)}const E=s.defined(h);let C;E&&(C=h[0].length-3,m+=C,A+=C,C/=3,_+=4*C);const G=m+A,T=new Float64Array(G);let v,P,w,L,D,x,k=0,O=G-1;const I=C/2,M=p.IndexDatatype.createTypedArray(G/3,_+4);let R=0;if(M[R++]=k/3,M[R++]=(O-2)/3,E){a.push(k/3),x=N,D=V;const e=h[0];for(g=0;g<I;g++)x=i.Cartesian3.fromArray(e,3*(I-1-g),x),D=i.Cartesian3.fromArray(e,3*(I+g),D),n.CorridorGeometryLibrary.addAttribute(T,D,k),n.CorridorGeometryLibrary.addAttribute(T,x,void 0,O),P=k/3,L=P+1,v=(O-2)/3,w=v-1,M[R++]=v,M[R++]=w,M[R++]=P,M[R++]=L,k+=3,O-=3}let S=0,B=d[S++],U=d[S++];for(T.set(B,k),T.set(U,O-U.length+1),b=U.length-3,a.push(k/3,(O-2)/3),g=0;g<b;g+=3)P=k/3,L=P+1,v=(O-2)/3,w=v-1,M[R++]=v,M[R++]=w,M[R++]=P,M[R++]=L,k+=3,O-=3;for(g=0;g<f.length;g++){let e;y=f[g];const r=y.leftPositions,l=y.rightPositions;let u,p=H;if(s.defined(r)){for(O-=3,u=w,a.push(L),e=0;e<r.length/3;e++)p=i.Cartesian3.fromArray(r,3*e,p),M[R++]=u-e-1,M[R++]=u-e,n.CorridorGeometryLibrary.addAttribute(T,p,void 0,O),O-=3;a.push(u-Math.floor(r.length/6)),t===o.CornerType.BEVELED&&a.push((O-2)/3+1),k+=3}else{for(k+=3,u=L,a.push(w),e=0;e<l.length/3;e++)p=i.Cartesian3.fromArray(l,3*e,p),M[R++]=u+e,M[R++]=u+e+1,n.CorridorGeometryLibrary.addAttribute(T,p,k),k+=3;a.push(u+Math.floor(l.length/6)),t===o.CornerType.BEVELED&&a.push(k/3-1),O-=3}for(B=d[S++],U=d[S++],B.splice(0,3),U.splice(U.length-3,3),T.set(B,k),T.set(U,O-U.length+1),b=U.length-3,e=0;e<U.length;e+=3)L=k/3,P=L-1,w=(O-2)/3,v=w+1,M[R++]=v,M[R++]=w,M[R++]=P,M[R++]=L,k+=3,O-=3;k-=3,O+=3,a.push(k/3,(O-2)/3)}if(E){k+=3,O-=3,x=N,D=V;const e=h[1];for(g=0;g<I;g++)x=i.Cartesian3.fromArray(e,3*(C-g-1),x),D=i.Cartesian3.fromArray(e,3*g,D),n.CorridorGeometryLibrary.addAttribute(T,x,void 0,O),n.CorridorGeometryLibrary.addAttribute(T,D,k),L=k/3,P=L-1,w=(O-2)/3,v=w+1,M[R++]=v,M[R++]=w,M[R++]=P,M[R++]=L,k+=3,O-=3;a.push(k/3)}else a.push(k/3,(O-2)/3);return M[R++]=k/3,M[R++]=(O-2)/3,c.position=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:T}),{attributes:c,indices:M,wallIndices:a}}function I(e){const t=(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).positions,r=e.width,n=s.defaultValue(e.height,0),l=s.defaultValue(e.extrudedHeight,n);this._positions=t,this._ellipsoid=a.Ellipsoid.clone(s.defaultValue(e.ellipsoid,a.Ellipsoid.WGS84)),this._width=r,this._height=Math.max(n,l),this._extrudedHeight=Math.min(n,l),this._cornerType=s.defaultValue(e.cornerType,o.CornerType.ROUNDED),this._granularity=s.defaultValue(e.granularity,f.CesiumMath.RADIANS_PER_DEGREE),this._offsetAttribute=e.offsetAttribute,this._workerName="createCorridorOutlineGeometry",this.packedLength=1+t.length*i.Cartesian3.packedLength+a.Ellipsoid.packedLength+6}I.pack=function(e,t,r){r=s.defaultValue(r,0);const o=e._positions,n=o.length;t[r++]=n;for(let e=0;e<n;++e,r+=i.Cartesian3.packedLength)i.Cartesian3.pack(o[e],t,r);return a.Ellipsoid.pack(e._ellipsoid,t,r),r+=a.Ellipsoid.packedLength,t[r++]=e._width,t[r++]=e._height,t[r++]=e._extrudedHeight,t[r++]=e._cornerType,t[r++]=e._granularity,t[r]=s.defaultValue(e._offsetAttribute,-1),t};const M=a.Ellipsoid.clone(a.Ellipsoid.UNIT_SPHERE),R={positions:void 0,ellipsoid:M,width:void 0,height:void 0,extrudedHeight:void 0,cornerType:void 0,granularity:void 0,offsetAttribute:void 0};return I.unpack=function(e,t,r){t=s.defaultValue(t,0);const o=e[t++],n=new Array(o);for(let r=0;r<o;++r,t+=i.Cartesian3.packedLength)n[r]=i.Cartesian3.unpack(e,t);const l=a.Ellipsoid.unpack(e,t,M);t+=a.Ellipsoid.packedLength;const u=e[t++],d=e[t++],p=e[t++],f=e[t++],h=e[t++],c=e[t];return s.defined(r)?(r._positions=n,r._ellipsoid=a.Ellipsoid.clone(l,r._ellipsoid),r._width=u,r._height=d,r._extrudedHeight=p,r._cornerType=f,r._granularity=h,r._offsetAttribute=-1===c?void 0:c,r):(R.positions=n,R.width=u,R.height=d,R.extrudedHeight=p,R.cornerType=f,R.granularity=h,R.offsetAttribute=-1===c?void 0:c,new I(R))},I.createGeometry=function(o){let a=o._positions;const u=o._width,c=o._ellipsoid;a=function(e,t){for(let i=0;i<e.length;i++)e[i]=t.scaleToGeodeticSurface(e[i],e[i]);return e}(a,c);const y=e.arrayRemoveDuplicates(a,i.Cartesian3.equalsEpsilon);if(y.length<2||u<=0)return;const g=o._height,b=o._extrudedHeight,m=!f.CesiumMath.equalsEpsilon(g,b,0,f.CesiumMath.EPSILON2),A={ellipsoid:c,positions:y,width:u,cornerType:o._cornerType,granularity:o._granularity,saveAttributes:!1};let _;if(m)A.height=g,A.extrudedHeight=b,A.offsetAttribute=o._offsetAttribute,_=function(e){const t=e.ellipsoid,i=O(n.CorridorGeometryLibrary.computePositions(e),e.cornerType),o=i.wallIndices,a=e.height,u=e.extrudedHeight,f=i.attributes,c=i.indices;let y=f.position.values,g=y.length,b=new Float64Array(g);b.set(y);const m=new Float64Array(2*g);if(y=h.PolygonPipeline.scaleToGeodeticHeight(y,a,t),b=h.PolygonPipeline.scaleToGeodeticHeight(b,u,t),m.set(y),m.set(b,g),f.position.values=m,g/=3,s.defined(e.offsetAttribute)){let t=new Uint8Array(2*g);if(e.offsetAttribute===d.GeometryOffsetAttribute.TOP)t=t.fill(1,0,g);else{const i=e.offsetAttribute===d.GeometryOffsetAttribute.NONE?0:1;t=t.fill(i)}f.applyOffset=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:t})}let A;const _=c.length,E=p.IndexDatatype.createTypedArray(m.length/3,2*(_+o.length));E.set(c);let C,G,T=_;for(A=0;A<_;A+=2){const e=c[A],t=c[A+1];E[T++]=e+g,E[T++]=t+g}for(A=0;A<o.length;A++)C=o[A],G=C+g,E[T++]=C,E[T++]=G;return{attributes:f,indices:E}}(A);else{if(_=O(n.CorridorGeometryLibrary.computePositions(A),A.cornerType),_.attributes.position.values=h.PolygonPipeline.scaleToGeodeticHeight(_.attributes.position.values,g,c),s.defined(o._offsetAttribute)){const e=_.attributes.position.values.length,t=o._offsetAttribute===d.GeometryOffsetAttribute.NONE?0:1,i=new Uint8Array(e/3).fill(t);_.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}}const E=_.attributes,C=t.BoundingSphere.fromVertices(E.position.values,void 0,3);return new l.Geometry({attributes:E,indices:_.indices,primitiveType:l.PrimitiveType.LINES,boundingSphere:C,offsetAttribute:o._offsetAttribute})},function(e,t){return s.defined(t)&&(e=I.unpack(e,t)),e._ellipsoid=a.Ellipsoid.clone(e._ellipsoid),I.createGeometry(e)}}));
