define(["./defaultValue-f6d5e6da","./Transforms-2afbbfb5","./Cartesian3-529c236c","./ComponentDatatype-ab629b88","./GeometryAttribute-f7a0845b","./GeometryAttributes-1e4ddcd2","./Cartographic-dbefb6fa","./Math-355606c6","./Ellipsoid-8e26549b","./Interval-d6c8d27a","./DeveloperError-c85858c1","./Matrix3-31d1f01f","./Matrix4-c57ffbd8","./RuntimeError-9b4ce3fb","./Rectangle-98b0bef0","./combine-0c102d93","./RequestType-735c98f2","./WebGLConstants-7f557f93","./Matrix2-e4a4609a"],(function(e,t,n,r,i,a,o,u,c,s,p,y,m,d,f,l,b,w,G){"use strict";function h(){this._workerName="createPlaneOutlineGeometry"}h.packedLength=0,h.pack=function(e,t){return t},h.unpack=function(t,n,r){return e.defined(r)?r:new h};const C=new n.Cartesian3(-.5,-.5,0),x=new n.Cartesian3(.5,.5,0);return h.createGeometry=function(){const e=new a.GeometryAttributes,o=new Uint16Array(8),u=new Float64Array(12);return u[0]=C.x,u[1]=C.y,u[2]=C.z,u[3]=x.x,u[4]=C.y,u[5]=C.z,u[6]=x.x,u[7]=x.y,u[8]=C.z,u[9]=C.x,u[10]=x.y,u[11]=C.z,e.position=new i.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:u}),o[0]=0,o[1]=1,o[2]=1,o[3]=2,o[4]=2,o[5]=3,o[6]=3,o[7]=0,new i.Geometry({attributes:e,indices:o,primitiveType:i.PrimitiveType.LINES,boundingSphere:new t.BoundingSphere(n.Cartesian3.ZERO,Math.sqrt(2))})},function(t,n){return e.defined(n)&&(t=h.unpack(t,n)),h.createGeometry(t)}}));
