/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.105.2
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

var $t=Object.create;var ke=Object.defineProperty;var Wt=Object.getOwnPropertyDescriptor;var Jt=Object.getOwnPropertyNames;var Kt=Object.getPrototypeOf,Yt=Object.prototype.hasOwnProperty;var Se=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports),Xt=(e,r)=>{for(var i in r)ke(e,i,{get:r[i],enumerable:!0})},rt=(e,r,i,a)=>{if(r&&typeof r=="object"||typeof r=="function")for(let c of Jt(r))!Yt.call(e,c)&&c!==i&&ke(e,c,{get:()=>r[c],enumerable:!(a=Wt(r,c))||a.enumerable});return e};var Z=(e,r,i)=>(i=e!=null?$t(Kt(e)):{},rt(r||!e||!e.__esModule?ke(i,"default",{value:e,enumerable:!0}):i,e)),Gt=e=>rt(ke({},"__esModule",{value:!0}),e);var ot=Se((ge,ye)=>{/*! https://mths.be/punycode v1.4.0 by @mathias */(function(e){var r=typeof ge=="object"&&ge&&!ge.nodeType&&ge,i=typeof ye=="object"&&ye&&!ye.nodeType&&ye,a=typeof global=="object"&&global;(a.global===a||a.window===a||a.self===a)&&(e=a);var c,o=**********,h=36,f=1,_=26,S=38,w=700,v=72,U=128,q="-",L=/^xn--/,D=/[^\x20-\x7E]/,K=/[\x2E\u3002\uFF0E\uFF61]/g,H={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},$=h-f,N=Math.floor,ne=String.fromCharCode,ie;function W(l){throw new RangeError(H[l])}function Ae(l,b){for(var x=l.length,I=[];x--;)I[x]=b(l[x]);return I}function Te(l,b){var x=l.split("@"),I="";x.length>1&&(I=x[0]+"@",l=x[1]),l=l.replace(K,".");var R=l.split("."),k=Ae(R,b).join(".");return I+k}function qe(l){for(var b=[],x=0,I=l.length,R,k;x<I;)R=l.charCodeAt(x++),R>=55296&&R<=56319&&x<I?(k=l.charCodeAt(x++),(k&64512)==56320?b.push(((R&1023)<<10)+(k&1023)+65536):(b.push(R),x--)):b.push(R);return b}function Ue(l){return Ae(l,function(b){var x="";return b>65535&&(b-=65536,x+=ne(b>>>10&1023|55296),b=56320|b&1023),x+=ne(b),x}).join("")}function We(l){return l-48<10?l-22:l-65<26?l-65:l-97<26?l-97:h}function t(l,b){return l+22+75*(l<26)-((b!=0)<<5)}function n(l,b,x){var I=0;for(l=x?N(l/w):l>>1,l+=N(l/b);l>$*_>>1;I+=h)l=N(l/$);return N(I+($+1)*l/(l+S))}function s(l){var b=[],x=l.length,I,R=0,k=U,O=v,M,V,Y,Q,j,X,G,ae,fe;for(M=l.lastIndexOf(q),M<0&&(M=0),V=0;V<M;++V)l.charCodeAt(V)>=128&&W("not-basic"),b.push(l.charCodeAt(V));for(Y=M>0?M+1:0;Y<x;){for(Q=R,j=1,X=h;Y>=x&&W("invalid-input"),G=We(l.charCodeAt(Y++)),(G>=h||G>N((o-R)/j))&&W("overflow"),R+=G*j,ae=X<=O?f:X>=O+_?_:X-O,!(G<ae);X+=h)fe=h-ae,j>N(o/fe)&&W("overflow"),j*=fe;I=b.length+1,O=n(R-Q,I,Q==0),N(R/I)>o-k&&W("overflow"),k+=N(R/I),R%=I,b.splice(R++,0,k)}return Ue(b)}function u(l){var b,x,I,R,k,O,M,V,Y,Q,j,X=[],G,ae,fe,Je;for(l=qe(l),G=l.length,b=U,x=0,k=v,O=0;O<G;++O)j=l[O],j<128&&X.push(ne(j));for(I=R=X.length,R&&X.push(q);I<G;){for(M=o,O=0;O<G;++O)j=l[O],j>=b&&j<M&&(M=j);for(ae=I+1,M-b>N((o-x)/ae)&&W("overflow"),x+=(M-b)*ae,b=M,O=0;O<G;++O)if(j=l[O],j<b&&++x>o&&W("overflow"),j==b){for(V=x,Y=h;Q=Y<=k?f:Y>=k+_?_:Y-k,!(V<Q);Y+=h)Je=V-Q,fe=h-Q,X.push(ne(t(Q+Je%fe,0))),V=N(Je/fe);X.push(ne(t(V,0))),k=n(x,ae,I==R),x=0,++I}++x,++b}return X.join("")}function m(l){return Te(l,function(b){return L.test(b)?s(b.slice(4).toLowerCase()):b})}function d(l){return Te(l,function(b){return D.test(b)?"xn--"+u(b):b})}if(c={version:"1.3.2",ucs2:{decode:qe,encode:Ue},decode:s,encode:u,toASCII:d,toUnicode:m},typeof define=="function"&&typeof define.amd=="object"&&define.amd)define("punycode",function(){return c});else if(r&&i)if(ye.exports==r)i.exports=c;else for(ie in c)c.hasOwnProperty(ie)&&(r[ie]=c[ie]);else e.punycode=c})(ge)});var at=Se((st,ze)=>{/*!
 * URI.js - Mutating URLs
 * IPv6 Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,r){"use strict";typeof ze=="object"&&ze.exports?ze.exports=r():typeof define=="function"&&define.amd?define(r):e.IPv6=r(e)})(st,function(e){"use strict";var r=e&&e.IPv6;function i(c){var o=c.toLowerCase(),h=o.split(":"),f=h.length,_=8;h[0]===""&&h[1]===""&&h[2]===""?(h.shift(),h.shift()):h[0]===""&&h[1]===""?h.shift():h[f-1]===""&&h[f-2]===""&&h.pop(),f=h.length,h[f-1].indexOf(".")!==-1&&(_=7);var S;for(S=0;S<f&&h[S]!=="";S++);if(S<_)for(h.splice(S,1,"0000");h.length<_;)h.splice(S,0,"0000");for(var w,v=0;v<_;v++){w=h[v].split("");for(var U=0;U<3&&(w[0]==="0"&&w.length>1);U++)w.splice(0,1);h[v]=w.join("")}var q=-1,L=0,D=0,K=-1,H=!1;for(v=0;v<_;v++)H?h[v]==="0"?D+=1:(H=!1,D>L&&(q=K,L=D)):h[v]==="0"&&(H=!0,K=v,D=1);D>L&&(q=K,L=D),L>1&&h.splice(q,L,""),f=h.length;var $="";for(h[0]===""&&($=":"),v=0;v<f&&($+=h[v],v!==f-1);v++)$+=":";return h[f-1]===""&&($+=":"),$}function a(){return e.IPv6===this&&(e.IPv6=r),this}return{best:i,noConflict:a}})});var ct=Se((ut,Le)=>{/*!
 * URI.js - Mutating URLs
 * Second Level Domain (SLD) Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,r){"use strict";typeof Le=="object"&&Le.exports?Le.exports=r():typeof define=="function"&&define.amd?define(r):e.SecondLevelDomains=r(e)})(ut,function(e){"use strict";var r=e&&e.SecondLevelDomains,i={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(a){var c=a.lastIndexOf(".");if(c<=0||c>=a.length-1)return!1;var o=a.lastIndexOf(".",c-1);if(o<=0||o>=c-1)return!1;var h=i.list[a.slice(c+1)];return h?h.indexOf(" "+a.slice(o+1,c)+" ")>=0:!1},is:function(a){var c=a.lastIndexOf(".");if(c<=0||c>=a.length-1)return!1;var o=a.lastIndexOf(".",c-1);if(o>=0)return!1;var h=i.list[a.slice(c+1)];return h?h.indexOf(" "+a.slice(0,c)+" ")>=0:!1},get:function(a){var c=a.lastIndexOf(".");if(c<=0||c>=a.length-1)return null;var o=a.lastIndexOf(".",c-1);if(o<=0||o>=c-1)return null;var h=i.list[a.slice(c+1)];return!h||h.indexOf(" "+a.slice(o+1,c)+" ")<0?null:a.slice(o+1)},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=r),this}};return i})});var le=Se((ft,Fe)=>{/*!
 * URI.js - Mutating URLs
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,r){"use strict";typeof Fe=="object"&&Fe.exports?Fe.exports=r(ot(),at(),ct()):typeof define=="function"&&define.amd?define(["./punycode","./IPv6","./SecondLevelDomains"],r):e.URI=r(e.punycode,e.IPv6,e.SecondLevelDomains,e)})(ft,function(e,r,i,a){"use strict";var c=a&&a.URI;function o(t,n){var s=arguments.length>=1,u=arguments.length>=2;if(!(this instanceof o))return s?u?new o(t,n):new o(t):new o;if(t===void 0){if(s)throw new TypeError("undefined is not a valid argument for URI");typeof location<"u"?t=location.href+"":t=""}if(t===null&&s)throw new TypeError("null is not a valid argument for URI");return this.href(t),n!==void 0?this.absoluteTo(n):this}function h(t){return/^[0-9]+$/.test(t)}o.version="1.19.11";var f=o.prototype,_=Object.prototype.hasOwnProperty;function S(t){return t.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function w(t){return t===void 0?"Undefined":String(Object.prototype.toString.call(t)).slice(8,-1)}function v(t){return w(t)==="Array"}function U(t,n){var s={},u,m;if(w(n)==="RegExp")s=null;else if(v(n))for(u=0,m=n.length;u<m;u++)s[n[u]]=!0;else s[n]=!0;for(u=0,m=t.length;u<m;u++){var d=s&&s[t[u]]!==void 0||!s&&n.test(t[u]);d&&(t.splice(u,1),m--,u--)}return t}function q(t,n){var s,u;if(v(n)){for(s=0,u=n.length;s<u;s++)if(!q(t,n[s]))return!1;return!0}var m=w(n);for(s=0,u=t.length;s<u;s++)if(m==="RegExp"){if(typeof t[s]=="string"&&t[s].match(n))return!0}else if(t[s]===n)return!0;return!1}function L(t,n){if(!v(t)||!v(n)||t.length!==n.length)return!1;t.sort(),n.sort();for(var s=0,u=t.length;s<u;s++)if(t[s]!==n[s])return!1;return!0}function D(t){var n=/^\/+|\/+$/g;return t.replace(n,"")}o._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:o.preventInvalidHostname,duplicateQueryParameters:o.duplicateQueryParameters,escapeQuerySpace:o.escapeQuerySpace}},o.preventInvalidHostname=!1,o.duplicateQueryParameters=!1,o.escapeQuerySpace=!0,o.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,o.idn_expression=/[^a-z0-9\._-]/i,o.punycode_expression=/(xn--)/i,o.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,o.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,o.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/ig,o.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},o.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,o.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,o.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},o.hostProtocols=["http","https"],o.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,o.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},o.getDomAttribute=function(t){if(!(!t||!t.nodeName)){var n=t.nodeName.toLowerCase();if(!(n==="input"&&t.type!=="image"))return o.domAttributes[n]}};function K(t){return escape(t)}function H(t){return encodeURIComponent(t).replace(/[!'()*]/g,K).replace(/\*/g,"%2A")}o.encode=H,o.decode=decodeURIComponent,o.iso8859=function(){o.encode=escape,o.decode=unescape},o.unicode=function(){o.encode=H,o.decode=decodeURIComponent},o.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/ig,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/ig,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/ig,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},o.encodeQuery=function(t,n){var s=o.encode(t+"");return n===void 0&&(n=o.escapeQuerySpace),n?s.replace(/%20/g,"+"):s},o.decodeQuery=function(t,n){t+="",n===void 0&&(n=o.escapeQuerySpace);try{return o.decode(n?t.replace(/\+/g,"%20"):t)}catch{return t}};var $={encode:"encode",decode:"decode"},N,ne=function(t,n){return function(s){try{return o[n](s+"").replace(o.characters[t][n].expression,function(u){return o.characters[t][n].map[u]})}catch{return s}}};for(N in $)o[N+"PathSegment"]=ne("pathname",$[N]),o[N+"UrnPathSegment"]=ne("urnpath",$[N]);var ie=function(t,n,s){return function(u){var m;s?m=function(x){return o[n](o[s](x))}:m=o[n];for(var d=(u+"").split(t),l=0,b=d.length;l<b;l++)d[l]=m(d[l]);return d.join(t)}};o.decodePath=ie("/","decodePathSegment"),o.decodeUrnPath=ie(":","decodeUrnPathSegment"),o.recodePath=ie("/","encodePathSegment","decode"),o.recodeUrnPath=ie(":","encodeUrnPathSegment","decode"),o.encodeReserved=ne("reserved","encode"),o.parse=function(t,n){var s;return n||(n={preventInvalidHostname:o.preventInvalidHostname}),t=t.replace(o.leading_whitespace_expression,""),t=t.replace(o.ascii_tab_whitespace,""),s=t.indexOf("#"),s>-1&&(n.fragment=t.substring(s+1)||null,t=t.substring(0,s)),s=t.indexOf("?"),s>-1&&(n.query=t.substring(s+1)||null,t=t.substring(0,s)),t=t.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://"),t=t.replace(/^[/\\]{2,}/i,"//"),t.substring(0,2)==="//"?(n.protocol=null,t=t.substring(2),t=o.parseAuthority(t,n)):(s=t.indexOf(":"),s>-1&&(n.protocol=t.substring(0,s)||null,n.protocol&&!n.protocol.match(o.protocol_expression)?n.protocol=void 0:t.substring(s+1,s+3).replace(/\\/g,"/")==="//"?(t=t.substring(s+3),t=o.parseAuthority(t,n)):(t=t.substring(s+1),n.urn=!0))),n.path=t,n},o.parseHost=function(t,n){t||(t=""),t=t.replace(/\\/g,"/");var s=t.indexOf("/"),u,m;if(s===-1&&(s=t.length),t.charAt(0)==="[")u=t.indexOf("]"),n.hostname=t.substring(1,u)||null,n.port=t.substring(u+2,s)||null,n.port==="/"&&(n.port=null);else{var d=t.indexOf(":"),l=t.indexOf("/"),b=t.indexOf(":",d+1);b!==-1&&(l===-1||b<l)?(n.hostname=t.substring(0,s)||null,n.port=null):(m=t.substring(0,s).split(":"),n.hostname=m[0]||null,n.port=m[1]||null)}return n.hostname&&t.substring(s).charAt(0)!=="/"&&(s++,t="/"+t),n.preventInvalidHostname&&o.ensureValidHostname(n.hostname,n.protocol),n.port&&o.ensureValidPort(n.port),t.substring(s)||"/"},o.parseAuthority=function(t,n){return t=o.parseUserinfo(t,n),o.parseHost(t,n)},o.parseUserinfo=function(t,n){var s=t,u=t.indexOf("\\");u!==-1&&(t=t.replace(/\\/g,"/"));var m=t.indexOf("/"),d=t.lastIndexOf("@",m>-1?m:t.length-1),l;return d>-1&&(m===-1||d<m)?(l=t.substring(0,d).split(":"),n.username=l[0]?o.decode(l[0]):null,l.shift(),n.password=l[0]?o.decode(l.join(":")):null,t=s.substring(d+1)):(n.username=null,n.password=null),t},o.parseQuery=function(t,n){if(!t)return{};if(t=t.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,""),!t)return{};for(var s={},u=t.split("&"),m=u.length,d,l,b,x=0;x<m;x++)d=u[x].split("="),l=o.decodeQuery(d.shift(),n),b=d.length?o.decodeQuery(d.join("="),n):null,l!=="__proto__"&&(_.call(s,l)?((typeof s[l]=="string"||s[l]===null)&&(s[l]=[s[l]]),s[l].push(b)):s[l]=b);return s},o.build=function(t){var n="",s=!1;return t.protocol&&(n+=t.protocol+":"),!t.urn&&(n||t.hostname)&&(n+="//",s=!0),n+=o.buildAuthority(t)||"",typeof t.path=="string"&&(t.path.charAt(0)!=="/"&&s&&(n+="/"),n+=t.path),typeof t.query=="string"&&t.query&&(n+="?"+t.query),typeof t.fragment=="string"&&t.fragment&&(n+="#"+t.fragment),n},o.buildHost=function(t){var n="";if(t.hostname)o.ip6_expression.test(t.hostname)?n+="["+t.hostname+"]":n+=t.hostname;else return"";return t.port&&(n+=":"+t.port),n},o.buildAuthority=function(t){return o.buildUserinfo(t)+o.buildHost(t)},o.buildUserinfo=function(t){var n="";return t.username&&(n+=o.encode(t.username)),t.password&&(n+=":"+o.encode(t.password)),n&&(n+="@"),n},o.buildQuery=function(t,n,s){var u="",m,d,l,b;for(d in t)if(d!=="__proto__"&&_.call(t,d))if(v(t[d]))for(m={},l=0,b=t[d].length;l<b;l++)t[d][l]!==void 0&&m[t[d][l]+""]===void 0&&(u+="&"+o.buildQueryParameter(d,t[d][l],s),n!==!0&&(m[t[d][l]+""]=!0));else t[d]!==void 0&&(u+="&"+o.buildQueryParameter(d,t[d],s));return u.substring(1)},o.buildQueryParameter=function(t,n,s){return o.encodeQuery(t,s)+(n!==null?"="+o.encodeQuery(n,s):"")},o.addQuery=function(t,n,s){if(typeof n=="object")for(var u in n)_.call(n,u)&&o.addQuery(t,u,n[u]);else if(typeof n=="string"){if(t[n]===void 0){t[n]=s;return}else typeof t[n]=="string"&&(t[n]=[t[n]]);v(s)||(s=[s]),t[n]=(t[n]||[]).concat(s)}else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter")},o.setQuery=function(t,n,s){if(typeof n=="object")for(var u in n)_.call(n,u)&&o.setQuery(t,u,n[u]);else if(typeof n=="string")t[n]=s===void 0?null:s;else throw new TypeError("URI.setQuery() accepts an object, string as the name parameter")},o.removeQuery=function(t,n,s){var u,m,d;if(v(n))for(u=0,m=n.length;u<m;u++)t[n[u]]=void 0;else if(w(n)==="RegExp")for(d in t)n.test(d)&&(t[d]=void 0);else if(typeof n=="object")for(d in n)_.call(n,d)&&o.removeQuery(t,d,n[d]);else if(typeof n=="string")s!==void 0?w(s)==="RegExp"?!v(t[n])&&s.test(t[n])?t[n]=void 0:t[n]=U(t[n],s):t[n]===String(s)&&(!v(s)||s.length===1)?t[n]=void 0:v(t[n])&&(t[n]=U(t[n],s)):t[n]=void 0;else throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter")},o.hasQuery=function(t,n,s,u){switch(w(n)){case"String":break;case"RegExp":for(var m in t)if(_.call(t,m)&&n.test(m)&&(s===void 0||o.hasQuery(t,m,s)))return!0;return!1;case"Object":for(var d in n)if(_.call(n,d)&&!o.hasQuery(t,d,n[d]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(w(s)){case"Undefined":return n in t;case"Boolean":var l=!!(v(t[n])?t[n].length:t[n]);return s===l;case"Function":return!!s(t[n],n,t);case"Array":if(!v(t[n]))return!1;var b=u?q:L;return b(t[n],s);case"RegExp":return v(t[n])?u?q(t[n],s):!1:!!(t[n]&&t[n].match(s));case"Number":s=String(s);case"String":return v(t[n])?u?q(t[n],s):!1:t[n]===s;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},o.joinPaths=function(){for(var t=[],n=[],s=0,u=0;u<arguments.length;u++){var m=new o(arguments[u]);t.push(m);for(var d=m.segment(),l=0;l<d.length;l++)typeof d[l]=="string"&&n.push(d[l]),d[l]&&s++}if(!n.length||!s)return new o("");var b=new o("").segment(n);return(t[0].path()===""||t[0].path().slice(0,1)==="/")&&b.path("/"+b.path()),b.normalize()},o.commonPath=function(t,n){var s=Math.min(t.length,n.length),u;for(u=0;u<s;u++)if(t.charAt(u)!==n.charAt(u)){u--;break}return u<1?t.charAt(0)===n.charAt(0)&&t.charAt(0)==="/"?"/":"":((t.charAt(u)!=="/"||n.charAt(u)!=="/")&&(u=t.substring(0,u).lastIndexOf("/")),t.substring(0,u+1))},o.withinString=function(t,n,s){s||(s={});var u=s.start||o.findUri.start,m=s.end||o.findUri.end,d=s.trim||o.findUri.trim,l=s.parens||o.findUri.parens,b=/[a-z0-9-]=["']?$/i;for(u.lastIndex=0;;){var x=u.exec(t);if(!x)break;var I=x.index;if(s.ignoreHtml){var R=t.slice(Math.max(I-3,0),I);if(R&&b.test(R))continue}for(var k=I+t.slice(I).search(m),O=t.slice(I,k),M=-1;;){var V=l.exec(O);if(!V)break;var Y=V.index+V[0].length;M=Math.max(M,Y)}if(M>-1?O=O.slice(0,M)+O.slice(M).replace(d,""):O=O.replace(d,""),!(O.length<=x[0].length)&&!(s.ignore&&s.ignore.test(O))){k=I+O.length;var Q=n(O,I,k,t);if(Q===void 0){u.lastIndex=k;continue}Q=String(Q),t=t.slice(0,I)+Q+t.slice(k),u.lastIndex=I+Q.length}}return u.lastIndex=0,t},o.ensureValidHostname=function(t,n){var s=!!t,u=!!n,m=!1;if(u&&(m=q(o.hostProtocols,n)),m&&!s)throw new TypeError("Hostname cannot be empty, if protocol is "+n);if(t&&t.match(o.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(t).match(o.invalid_hostname_characters))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_]')}},o.ensureValidPort=function(t){if(t){var n=Number(t);if(!(h(n)&&n>0&&n<65536))throw new TypeError('Port "'+t+'" is not a valid port')}},o.noConflict=function(t){if(t){var n={URI:this.noConflict()};return a.URITemplate&&typeof a.URITemplate.noConflict=="function"&&(n.URITemplate=a.URITemplate.noConflict()),a.IPv6&&typeof a.IPv6.noConflict=="function"&&(n.IPv6=a.IPv6.noConflict()),a.SecondLevelDomains&&typeof a.SecondLevelDomains.noConflict=="function"&&(n.SecondLevelDomains=a.SecondLevelDomains.noConflict()),n}else a.URI===this&&(a.URI=c);return this},f.build=function(t){return t===!0?this._deferred_build=!0:(t===void 0||this._deferred_build)&&(this._string=o.build(this._parts),this._deferred_build=!1),this},f.clone=function(){return new o(this)},f.valueOf=f.toString=function(){return this.build(!1)._string};function W(t){return function(n,s){return n===void 0?this._parts[t]||"":(this._parts[t]=n||null,this.build(!s),this)}}function Ae(t,n){return function(s,u){return s===void 0?this._parts[t]||"":(s!==null&&(s=s+"",s.charAt(0)===n&&(s=s.substring(1))),this._parts[t]=s,this.build(!u),this)}}f.protocol=W("protocol"),f.username=W("username"),f.password=W("password"),f.hostname=W("hostname"),f.port=W("port"),f.query=Ae("query","?"),f.fragment=Ae("fragment","#"),f.search=function(t,n){var s=this.query(t,n);return typeof s=="string"&&s.length?"?"+s:s},f.hash=function(t,n){var s=this.fragment(t,n);return typeof s=="string"&&s.length?"#"+s:s},f.pathname=function(t,n){if(t===void 0||t===!0){var s=this._parts.path||(this._parts.hostname?"/":"");return t?(this._parts.urn?o.decodeUrnPath:o.decodePath)(s):s}else return this._parts.urn?this._parts.path=t?o.recodeUrnPath(t):"":this._parts.path=t?o.recodePath(t):"/",this.build(!n),this},f.path=f.pathname,f.href=function(t,n){var s;if(t===void 0)return this.toString();this._string="",this._parts=o._parts();var u=t instanceof o,m=typeof t=="object"&&(t.hostname||t.path||t.pathname);if(t.nodeName){var d=o.getDomAttribute(t);t=t[d]||"",m=!1}if(!u&&m&&t.pathname!==void 0&&(t=t.toString()),typeof t=="string"||t instanceof String)this._parts=o.parse(String(t),this._parts);else if(u||m){var l=u?t._parts:t;for(s in l)s!=="query"&&_.call(this._parts,s)&&(this._parts[s]=l[s]);l.query&&this.query(l.query,!1)}else throw new TypeError("invalid input");return this.build(!n),this},f.is=function(t){var n=!1,s=!1,u=!1,m=!1,d=!1,l=!1,b=!1,x=!this._parts.urn;switch(this._parts.hostname&&(x=!1,s=o.ip4_expression.test(this._parts.hostname),u=o.ip6_expression.test(this._parts.hostname),n=s||u,m=!n,d=m&&i&&i.has(this._parts.hostname),l=m&&o.idn_expression.test(this._parts.hostname),b=m&&o.punycode_expression.test(this._parts.hostname)),t.toLowerCase()){case"relative":return x;case"absolute":return!x;case"domain":case"name":return m;case"sld":return d;case"ip":return n;case"ip4":case"ipv4":case"inet4":return s;case"ip6":case"ipv6":case"inet6":return u;case"idn":return l;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return b}return null};var Te=f.protocol,qe=f.port,Ue=f.hostname;f.protocol=function(t,n){if(t&&(t=t.replace(/:(\/\/)?$/,""),!t.match(o.protocol_expression)))throw new TypeError('Protocol "'+t+`" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]`);return Te.call(this,t,n)},f.scheme=f.protocol,f.port=function(t,n){return this._parts.urn?t===void 0?"":this:(t!==void 0&&(t===0&&(t=null),t&&(t+="",t.charAt(0)===":"&&(t=t.substring(1)),o.ensureValidPort(t))),qe.call(this,t,n))},f.hostname=function(t,n){if(this._parts.urn)return t===void 0?"":this;if(t!==void 0){var s={preventInvalidHostname:this._parts.preventInvalidHostname},u=o.parseHost(t,s);if(u!=="/")throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');t=s.hostname,this._parts.preventInvalidHostname&&o.ensureValidHostname(t,this._parts.protocol)}return Ue.call(this,t,n)},f.origin=function(t,n){if(this._parts.urn)return t===void 0?"":this;if(t===void 0){var s=this.protocol(),u=this.authority();return u?(s?s+"://":"")+this.authority():""}else{var m=o(t);return this.protocol(m.protocol()).authority(m.authority()).build(!n),this}},f.host=function(t,n){if(this._parts.urn)return t===void 0?"":this;if(t===void 0)return this._parts.hostname?o.buildHost(this._parts):"";var s=o.parseHost(t,this._parts);if(s!=="/")throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');return this.build(!n),this},f.authority=function(t,n){if(this._parts.urn)return t===void 0?"":this;if(t===void 0)return this._parts.hostname?o.buildAuthority(this._parts):"";var s=o.parseAuthority(t,this._parts);if(s!=="/")throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');return this.build(!n),this},f.userinfo=function(t,n){if(this._parts.urn)return t===void 0?"":this;if(t===void 0){var s=o.buildUserinfo(this._parts);return s&&s.substring(0,s.length-1)}else return t[t.length-1]!=="@"&&(t+="@"),o.parseUserinfo(t,this._parts),this.build(!n),this},f.resource=function(t,n){var s;return t===void 0?this.path()+this.search()+this.hash():(s=o.parse(t),this._parts.path=s.path,this._parts.query=s.query,this._parts.fragment=s.fragment,this.build(!n),this)},f.subdomain=function(t,n){if(this._parts.urn)return t===void 0?"":this;if(t===void 0){if(!this._parts.hostname||this.is("IP"))return"";var s=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,s)||""}else{var u=this._parts.hostname.length-this.domain().length,m=this._parts.hostname.substring(0,u),d=new RegExp("^"+S(m));if(t&&t.charAt(t.length-1)!=="."&&(t+="."),t.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");return t&&o.ensureValidHostname(t,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(d,t),this.build(!n),this}},f.domain=function(t,n){if(this._parts.urn)return t===void 0?"":this;if(typeof t=="boolean"&&(n=t,t=void 0),t===void 0){if(!this._parts.hostname||this.is("IP"))return"";var s=this._parts.hostname.match(/\./g);if(s&&s.length<2)return this._parts.hostname;var u=this._parts.hostname.length-this.tld(n).length-1;return u=this._parts.hostname.lastIndexOf(".",u-1)+1,this._parts.hostname.substring(u)||""}else{if(!t)throw new TypeError("cannot set domain empty");if(t.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");if(o.ensureValidHostname(t,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=t;else{var m=new RegExp(S(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(m,t)}return this.build(!n),this}},f.tld=function(t,n){if(this._parts.urn)return t===void 0?"":this;if(typeof t=="boolean"&&(n=t,t=void 0),t===void 0){if(!this._parts.hostname||this.is("IP"))return"";var s=this._parts.hostname.lastIndexOf("."),u=this._parts.hostname.substring(s+1);return n!==!0&&i&&i.list[u.toLowerCase()]&&i.get(this._parts.hostname)||u}else{var m;if(t)if(t.match(/[^a-zA-Z0-9-]/))if(i&&i.is(t))m=new RegExp(S(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(m,t);else throw new TypeError('TLD "'+t+'" contains characters other than [A-Z0-9]');else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");m=new RegExp(S(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(m,t)}else throw new TypeError("cannot set TLD empty");return this.build(!n),this}},f.directory=function(t,n){if(this._parts.urn)return t===void 0?"":this;if(t===void 0||t===!0){if(!this._parts.path&&!this._parts.hostname)return"";if(this._parts.path==="/")return"/";var s=this._parts.path.length-this.filename().length-1,u=this._parts.path.substring(0,s)||(this._parts.hostname?"/":"");return t?o.decodePath(u):u}else{var m=this._parts.path.length-this.filename().length,d=this._parts.path.substring(0,m),l=new RegExp("^"+S(d));return this.is("relative")||(t||(t="/"),t.charAt(0)!=="/"&&(t="/"+t)),t&&t.charAt(t.length-1)!=="/"&&(t+="/"),t=o.recodePath(t),this._parts.path=this._parts.path.replace(l,t),this.build(!n),this}},f.filename=function(t,n){if(this._parts.urn)return t===void 0?"":this;if(typeof t!="string"){if(!this._parts.path||this._parts.path==="/")return"";var s=this._parts.path.lastIndexOf("/"),u=this._parts.path.substring(s+1);return t?o.decodePathSegment(u):u}else{var m=!1;t.charAt(0)==="/"&&(t=t.substring(1)),t.match(/\.?\//)&&(m=!0);var d=new RegExp(S(this.filename())+"$");return t=o.recodePath(t),this._parts.path=this._parts.path.replace(d,t),m?this.normalizePath(n):this.build(!n),this}},f.suffix=function(t,n){if(this._parts.urn)return t===void 0?"":this;if(t===void 0||t===!0){if(!this._parts.path||this._parts.path==="/")return"";var s=this.filename(),u=s.lastIndexOf("."),m,d;return u===-1?"":(m=s.substring(u+1),d=/^[a-z0-9%]+$/i.test(m)?m:"",t?o.decodePathSegment(d):d)}else{t.charAt(0)==="."&&(t=t.substring(1));var l=this.suffix(),b;if(l)t?b=new RegExp(S(l)+"$"):b=new RegExp(S("."+l)+"$");else{if(!t)return this;this._parts.path+="."+o.recodePath(t)}return b&&(t=o.recodePath(t),this._parts.path=this._parts.path.replace(b,t)),this.build(!n),this}},f.segment=function(t,n,s){var u=this._parts.urn?":":"/",m=this.path(),d=m.substring(0,1)==="/",l=m.split(u);if(t!==void 0&&typeof t!="number"&&(s=n,n=t,t=void 0),t!==void 0&&typeof t!="number")throw new Error('Bad segment "'+t+'", must be 0-based integer');if(d&&l.shift(),t<0&&(t=Math.max(l.length+t,0)),n===void 0)return t===void 0?l:l[t];if(t===null||l[t]===void 0)if(v(n)){l=[];for(var b=0,x=n.length;b<x;b++)!n[b].length&&(!l.length||!l[l.length-1].length)||(l.length&&!l[l.length-1].length&&l.pop(),l.push(D(n[b])))}else(n||typeof n=="string")&&(n=D(n),l[l.length-1]===""?l[l.length-1]=n:l.push(n));else n?l[t]=D(n):l.splice(t,1);return d&&l.unshift(""),this.path(l.join(u),s)},f.segmentCoded=function(t,n,s){var u,m,d;if(typeof t!="number"&&(s=n,n=t,t=void 0),n===void 0){if(u=this.segment(t,n,s),!v(u))u=u!==void 0?o.decode(u):void 0;else for(m=0,d=u.length;m<d;m++)u[m]=o.decode(u[m]);return u}if(!v(n))n=typeof n=="string"||n instanceof String?o.encode(n):n;else for(m=0,d=n.length;m<d;m++)n[m]=o.encode(n[m]);return this.segment(t,n,s)};var We=f.query;return f.query=function(t,n){if(t===!0)return o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof t=="function"){var s=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace),u=t.call(this,s);return this._parts.query=o.buildQuery(u||s,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!n),this}else return t!==void 0&&typeof t!="string"?(this._parts.query=o.buildQuery(t,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!n),this):We.call(this,t,n)},f.setQuery=function(t,n,s){var u=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof t=="string"||t instanceof String)u[t]=n!==void 0?n:null;else if(typeof t=="object")for(var m in t)_.call(t,m)&&(u[m]=t[m]);else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");return this._parts.query=o.buildQuery(u,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof t!="string"&&(s=n),this.build(!s),this},f.addQuery=function(t,n,s){var u=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.addQuery(u,t,n===void 0?null:n),this._parts.query=o.buildQuery(u,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof t!="string"&&(s=n),this.build(!s),this},f.removeQuery=function(t,n,s){var u=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.removeQuery(u,t,n),this._parts.query=o.buildQuery(u,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof t!="string"&&(s=n),this.build(!s),this},f.hasQuery=function(t,n,s){var u=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.hasQuery(u,t,n,s)},f.setSearch=f.setQuery,f.addSearch=f.addQuery,f.removeSearch=f.removeQuery,f.hasSearch=f.hasQuery,f.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},f.normalizeProtocol=function(t){return typeof this._parts.protocol=="string"&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!t)),this},f.normalizeHostname=function(t){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&r&&(this._parts.hostname=r.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!t)),this},f.normalizePort=function(t){return typeof this._parts.protocol=="string"&&this._parts.port===o.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!t)),this},f.normalizePath=function(t){var n=this._parts.path;if(!n)return this;if(this._parts.urn)return this._parts.path=o.recodeUrnPath(this._parts.path),this.build(!t),this;if(this._parts.path==="/")return this;n=o.recodePath(n);var s,u="",m,d;for(n.charAt(0)!=="/"&&(s=!0,n="/"+n),(n.slice(-3)==="/.."||n.slice(-2)==="/.")&&(n+="/"),n=n.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),s&&(u=n.substring(1).match(/^(\.\.\/)+/)||"",u&&(u=u[0]));m=n.search(/\/\.\.(\/|$)/),m!==-1;){if(m===0){n=n.substring(3);continue}d=n.substring(0,m).lastIndexOf("/"),d===-1&&(d=m),n=n.substring(0,d)+n.substring(m+3)}return s&&this.is("relative")&&(n=u+n.substring(1)),this._parts.path=n,this.build(!t),this},f.normalizePathname=f.normalizePath,f.normalizeQuery=function(t){return typeof this._parts.query=="string"&&(this._parts.query.length?this.query(o.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!t)),this},f.normalizeFragment=function(t){return this._parts.fragment||(this._parts.fragment=null,this.build(!t)),this},f.normalizeSearch=f.normalizeQuery,f.normalizeHash=f.normalizeFragment,f.iso8859=function(){var t=o.encode,n=o.decode;o.encode=escape,o.decode=decodeURIComponent;try{this.normalize()}finally{o.encode=t,o.decode=n}return this},f.unicode=function(){var t=o.encode,n=o.decode;o.encode=H,o.decode=unescape;try{this.normalize()}finally{o.encode=t,o.decode=n}return this},f.readable=function(){var t=this.clone();t.username("").password("").normalize();var n="";if(t._parts.protocol&&(n+=t._parts.protocol+"://"),t._parts.hostname&&(t.is("punycode")&&e?(n+=e.toUnicode(t._parts.hostname),t._parts.port&&(n+=":"+t._parts.port)):n+=t.host()),t._parts.hostname&&t._parts.path&&t._parts.path.charAt(0)!=="/"&&(n+="/"),n+=t.path(!0),t._parts.query){for(var s="",u=0,m=t._parts.query.split("&"),d=m.length;u<d;u++){var l=(m[u]||"").split("=");s+="&"+o.decodeQuery(l[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),l[1]!==void 0&&(s+="="+o.decodeQuery(l[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}n+="?"+s.substring(1)}return n+=o.decodeQuery(t.hash(),!0),n},f.absoluteTo=function(t){var n=this.clone(),s=["protocol","username","password","hostname","port"],u,m,d;if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(t instanceof o||(t=new o(t)),n._parts.protocol||(n._parts.protocol=t._parts.protocol,this._parts.hostname))return n;for(m=0;d=s[m];m++)n._parts[d]=t._parts[d];return n._parts.path?(n._parts.path.substring(-2)===".."&&(n._parts.path+="/"),n.path().charAt(0)!=="/"&&(u=t.directory(),u=u||(t.path().indexOf("/")===0?"/":""),n._parts.path=(u?u+"/":"")+n._parts.path,n.normalizePath())):(n._parts.path=t._parts.path,n._parts.query||(n._parts.query=t._parts.query)),n.build(),n},f.relativeTo=function(t){var n=this.clone().normalize(),s,u,m,d,l;if(n._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(t=new o(t).normalize(),s=n._parts,u=t._parts,d=n.path(),l=t.path(),d.charAt(0)!=="/")throw new Error("URI is already relative");if(l.charAt(0)!=="/")throw new Error("Cannot calculate a URI relative to another relative URI");if(s.protocol===u.protocol&&(s.protocol=null),s.username!==u.username||s.password!==u.password||s.protocol!==null||s.username!==null||s.password!==null)return n.build();if(s.hostname===u.hostname&&s.port===u.port)s.hostname=null,s.port=null;else return n.build();if(d===l)return s.path="",n.build();if(m=o.commonPath(d,l),!m)return n.build();var b=u.path.substring(m.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return s.path=b+s.path.substring(m.length)||"./",n.build()},f.equals=function(t){var n=this.clone(),s=new o(t),u={},m={},d={},l,b,x;if(n.normalize(),s.normalize(),n.toString()===s.toString())return!0;if(l=n.query(),b=s.query(),n.query(""),s.query(""),n.toString()!==s.toString()||l.length!==b.length)return!1;u=o.parseQuery(l,this._parts.escapeQuerySpace),m=o.parseQuery(b,this._parts.escapeQuerySpace);for(x in u)if(_.call(u,x)){if(v(u[x])){if(!L(u[x],m[x]))return!1}else if(u[x]!==m[x])return!1;d[x]=!0}for(x in m)if(_.call(m,x)&&!d[x])return!1;return!0},f.preventInvalidHostname=function(t){return this._parts.preventInvalidHostname=!!t,this},f.duplicateQueryParameters=function(t){return this._parts.duplicateQueryParameters=!!t,this},f.escapeQuerySpace=function(t){return this._parts.escapeQuerySpace=!!t,this},o})});var St=Se((ln,At)=>{var oe=function(e){e==null&&(e=new Date().getTime()),this.N=624,this.M=397,this.MATRIX_A=2567483615,this.UPPER_MASK=2147483648,this.LOWER_MASK=**********,this.mt=new Array(this.N),this.mti=this.N+1,e.constructor==Array?this.init_by_array(e,e.length):this.init_seed(e)};oe.prototype.init_seed=function(e){for(this.mt[0]=e>>>0,this.mti=1;this.mti<this.N;this.mti++){var e=this.mt[this.mti-1]^this.mt[this.mti-1]>>>30;this.mt[this.mti]=(((e&4294901760)>>>16)*1812433253<<16)+(e&65535)*1812433253+this.mti,this.mt[this.mti]>>>=0}};oe.prototype.init_by_array=function(e,r){var i,a,c;for(this.init_seed(19650218),i=1,a=0,c=this.N>r?this.N:r;c;c--){var o=this.mt[i-1]^this.mt[i-1]>>>30;this.mt[i]=(this.mt[i]^(((o&4294901760)>>>16)*1664525<<16)+(o&65535)*1664525)+e[a]+a,this.mt[i]>>>=0,i++,a++,i>=this.N&&(this.mt[0]=this.mt[this.N-1],i=1),a>=r&&(a=0)}for(c=this.N-1;c;c--){var o=this.mt[i-1]^this.mt[i-1]>>>30;this.mt[i]=(this.mt[i]^(((o&4294901760)>>>16)*1566083941<<16)+(o&65535)*1566083941)-i,this.mt[i]>>>=0,i++,i>=this.N&&(this.mt[0]=this.mt[this.N-1],i=1)}this.mt[0]=2147483648};oe.prototype.random_int=function(){var e,r=new Array(0,this.MATRIX_A);if(this.mti>=this.N){var i;for(this.mti==this.N+1&&this.init_seed(5489),i=0;i<this.N-this.M;i++)e=this.mt[i]&this.UPPER_MASK|this.mt[i+1]&this.LOWER_MASK,this.mt[i]=this.mt[i+this.M]^e>>>1^r[e&1];for(;i<this.N-1;i++)e=this.mt[i]&this.UPPER_MASK|this.mt[i+1]&this.LOWER_MASK,this.mt[i]=this.mt[i+(this.M-this.N)]^e>>>1^r[e&1];e=this.mt[this.N-1]&this.UPPER_MASK|this.mt[0]&this.LOWER_MASK,this.mt[this.N-1]=this.mt[this.M-1]^e>>>1^r[e&1],this.mti=0}return e=this.mt[this.mti++],e^=e>>>11,e^=e<<7&2636928640,e^=e<<15&4022730752,e^=e>>>18,e>>>0};oe.prototype.random_int31=function(){return this.random_int()>>>1};oe.prototype.random_incl=function(){return this.random_int()*(1/4294967295)};oe.prototype.random=function(){return this.random_int()*(1/4294967296)};oe.prototype.random_excl=function(){return(this.random_int()+.5)*(1/4294967296)};oe.prototype.random_long=function(){var e=this.random_int()>>>5,r=this.random_int()>>>6;return(e*67108864+r)*(1/9007199254740992)};At.exports=oe});var Ur={};Xt(Ur,{EMExtensionsMixin:()=>nt,EV_SeviceAnalysis:()=>Ht});module.exports=Gt(Ur);var T={_maximumCombinedTextureImageUnits:0,_maximumCubeMapSize:0,_maximumFragmentUniformVectors:0,_maximumTextureImageUnits:0,_maximumRenderbufferSize:0,_maximumTextureSize:0,_maximumVaryingVectors:0,_maximumVertexAttributes:0,_maximumVertexTextureImageUnits:0,_maximumVertexUniformVectors:0,_minimumAliasedLineWidth:0,_maximumAliasedLineWidth:0,_minimumAliasedPointSize:0,_maximumAliasedPointSize:0,_maximumViewportWidth:0,_maximumViewportHeight:0,_maximumTextureFilterAnisotropy:0,_maximumDrawBuffers:0,_maximumColorAttachments:0,_maximumSamples:0,_highpFloatSupported:!1,_highpIntSupported:!1};Object.defineProperties(T,{maximumCombinedTextureImageUnits:{get:function(){return T._maximumCombinedTextureImageUnits}},maximumCubeMapSize:{get:function(){return T._maximumCubeMapSize}},maximumFragmentUniformVectors:{get:function(){return T._maximumFragmentUniformVectors}},maximumTextureImageUnits:{get:function(){return T._maximumTextureImageUnits}},maximumRenderbufferSize:{get:function(){return T._maximumRenderbufferSize}},maximumTextureSize:{get:function(){return T._maximumTextureSize}},maximumVaryingVectors:{get:function(){return T._maximumVaryingVectors}},maximumVertexAttributes:{get:function(){return T._maximumVertexAttributes}},maximumVertexTextureImageUnits:{get:function(){return T._maximumVertexTextureImageUnits}},maximumVertexUniformVectors:{get:function(){return T._maximumVertexUniformVectors}},minimumAliasedLineWidth:{get:function(){return T._minimumAliasedLineWidth}},maximumAliasedLineWidth:{get:function(){return T._maximumAliasedLineWidth}},minimumAliasedPointSize:{get:function(){return T._minimumAliasedPointSize}},maximumAliasedPointSize:{get:function(){return T._maximumAliasedPointSize}},maximumViewportWidth:{get:function(){return T._maximumViewportWidth}},maximumViewportHeight:{get:function(){return T._maximumViewportHeight}},maximumTextureFilterAnisotropy:{get:function(){return T._maximumTextureFilterAnisotropy}},maximumDrawBuffers:{get:function(){return T._maximumDrawBuffers}},maximumColorAttachments:{get:function(){return T._maximumColorAttachments}},maximumSamples:{get:function(){return T._maximumSamples}},highpFloatSupported:{get:function(){return T._highpFloatSupported}},highpIntSupported:{get:function(){return T._highpIntSupported}}});var z=T;function Zt(e,r){z._maximumCombinedTextureImageUnits=Cesium.ContextLimits._maximumCombinedTextureImageUnits,z._maximumCubeMapSize=Cesium.ContextLimits._maximumCubeMapSize,z._maximumFragmentUniformVectors=Cesium.ContextLimits._maximumFragmentUniformVectors,z._maximumTextureImageUnits=Cesium.ContextLimits._maximumTextureImageUnits,z._maximumRenderbufferSize=Cesium.ContextLimits._maximumRenderbufferSize,z._maximumTextureSize=Cesium.ContextLimits._maximumTextureSize,z._maximumVertexAttributes=Cesium.ContextLimits._maximumVertexAttributes,z._maximumVertexTextureImageUnits=Cesium.ContextLimits._maximumVertexTextureImageUnits,z._maximumVertexUniformVectors=Cesium.ContextLimits._maximumVertexUniformVectors,z._minimumAliasedLineWidth=Cesium.ContextLimits._minimumAliasedLineWidth,z._maximumAliasedLineWidth=Cesium.ContextLimits._maximumAliasedLineWidth,z._minimumAliasedPointSize=Cesium.ContextLimits._minimumAliasedPointSize,z._maximumAliasedPointSize=Cesium.ContextLimits._maximumAliasedPointSize,z._maximumViewportWidth=Cesium.ContextLimits._maximumViewportWidth,z._maximumViewportHeight=Cesium.ContextLimits._maximumViewportHeight,z._highpFloatSupported=Cesium.ContextLimits._highpFloatSupported,z._highpIntSupported=Cesium.ContextLimits._highpIntSupported,z._maximumTextureFilterAnisotropy=Cesium.ContextLimits._maximumTextureFilterAnisotropy,z._maximumDrawBuffers=Cesium.ContextLimits._maximumDrawBuffers,z._maximumColorAttachments=Cesium.ContextLimits._maximumColorAttachments}var nt=Zt;function it(e,r){return e??r}it.EMPTY_OBJECT=Object.freeze({});var A=it;var jt=Z(le(),1);function er(e){return(e.length===0||e[e.length-1]!=="/")&&(e=`${e}/`),e}var lt=er;function tr(e){return e!=null}var y=tr;function he(e){this.name="DeveloperError",this.message=e;let r;try{throw new Error}catch(i){r=i.stack}this.stack=r}y(Object.create)&&(he.prototype=Object.create(Error.prototype),he.prototype.constructor=he);he.prototype.toString=function(){let e=`${this.name}: ${this.message}`;return y(this.stack)&&(e+=`
${this.stack.toString()}`),e};he.throwInstantiationError=function(){throw new he("This function defines an interface and should not be called directly.")};var J=he;var F={};F.typeOf={};function rr(e){return`${e} is required, actual value was undefined`}function _e(e,r,i){return`Expected ${i} to be typeof ${r}, actual typeof was ${e}`}F.defined=function(e,r){if(!y(r))throw new J(rr(e))};F.typeOf.func=function(e,r){if(typeof r!="function")throw new J(_e(typeof r,"function",e))};F.typeOf.string=function(e,r){if(typeof r!="string")throw new J(_e(typeof r,"string",e))};F.typeOf.number=function(e,r){if(typeof r!="number")throw new J(_e(typeof r,"number",e))};F.typeOf.number.lessThan=function(e,r,i){if(F.typeOf.number(e,r),r>=i)throw new J(`Expected ${e} to be less than ${i}, actual value was ${r}`)};F.typeOf.number.lessThanOrEquals=function(e,r,i){if(F.typeOf.number(e,r),r>i)throw new J(`Expected ${e} to be less than or equal to ${i}, actual value was ${r}`)};F.typeOf.number.greaterThan=function(e,r,i){if(F.typeOf.number(e,r),r<=i)throw new J(`Expected ${e} to be greater than ${i}, actual value was ${r}`)};F.typeOf.number.greaterThanOrEquals=function(e,r,i){if(F.typeOf.number(e,r),r<i)throw new J(`Expected ${e} to be greater than or equal to ${i}, actual value was ${r}`)};F.typeOf.object=function(e,r){if(typeof r!="object")throw new J(_e(typeof r,"object",e))};F.typeOf.bool=function(e,r){if(typeof r!="boolean")throw new J(_e(typeof r,"boolean",e))};F.typeOf.bigint=function(e,r){if(typeof r!="bigint")throw new J(_e(typeof r,"bigint",e))};F.typeOf.number.equals=function(e,r,i,a){if(F.typeOf.number(e,i),F.typeOf.number(r,a),i!==a)throw new J(`${e} must be equal to ${r}, the actual values are ${i} and ${a}`)};var ue=F;function ht(e,r){if(e===null||typeof e!="object")return e;r=A(r,!1);let i=new e.constructor;for(let a in e)if(e.hasOwnProperty(a)){let c=e[a];r&&(c=ht(c,r)),i[a]=c}return i}var be=ht;function mt(e,r,i){i=A(i,!1);let a={},c=y(e),o=y(r),h,f,_;if(c)for(h in e)e.hasOwnProperty(h)&&(f=e[h],o&&i&&typeof f=="object"&&r.hasOwnProperty(h)?(_=r[h],typeof _=="object"?a[h]=mt(f,_,i):a[h]=f):a[h]=f);if(o)for(h in r)r.hasOwnProperty(h)&&!a.hasOwnProperty(h)&&(_=r[h],a[h]=_);return a}var ce=mt;function nr(){let e,r,i=new Promise(function(a,c){e=a,r=c});return{resolve:e,reject:r,promise:i}}var me=nr;var pt=Z(le(),1);function Ke(e,r){let i;return typeof document<"u"&&(i=document),Ke._implementation(e,r,i)}Ke._implementation=function(e,r,i){if(!y(r)){if(typeof i>"u")return e;r=A(i.baseURI,i.location.href)}let a=new pt.default(e);return a.scheme()!==""?a.toString():a.absoluteTo(r).toString()};var dt=Ke;var gt=Z(le(),1);function ir(e,r){let i="",a=e.lastIndexOf("/");return a!==-1&&(i=e.substring(0,a+1)),r&&(e=new gt.default(e),e.query().length!==0&&(i+=`?${e.query()}`),e.fragment().length!==0&&(i+=`#${e.fragment()}`)),i}var yt=ir;var _t=Z(le(),1);function or(e){let r=new _t.default(e);r.normalize();let i=r.path(),a=i.lastIndexOf("/");return a!==-1&&(i=i.substr(a+1)),a=i.lastIndexOf("."),a===-1?i="":i=i.substr(a+1),i}var bt=or;var vt={};function sr(e,r,i){y(r)||(r=e.width),y(i)||(i=e.height);let a=vt[r];y(a)||(a={},vt[r]=a);let c=a[i];if(!y(c)){let o=document.createElement("canvas");o.width=r,o.height=i,c=o.getContext("2d",{willReadFrequently:!0}),c.globalCompositeOperation="copy",a[i]=c}return c.drawImage(e,0,0,r,i),c.getImageData(0,0,r,i).data}var Ye=sr;var ar=/^blob:/i;function ur(e){return ar.test(e)}var Me=ur;var ee;function cr(e){y(ee)||(ee=document.createElement("a")),ee.href=window.location.href;let r=ee.host,i=ee.protocol;return ee.href=e,ee.href=ee.href,i!==ee.protocol||r!==ee.host}var xt=cr;var fr=/^data:/i;function lr(e){return fr.test(e)}var De=lr;function hr(e){let r=document.createElement("script");return r.async=!0,r.src=e,new Promise((i,a)=>{window.crossOriginIsolated&&r.setAttribute("crossorigin","anonymous");let c=document.getElementsByTagName("head")[0];r.onload=function(){r.onload=void 0,c.removeChild(r),i()},r.onerror=function(o){a(o)},c.appendChild(r)})}var wt=hr;var Xe=Z(St(),1);var p={};p.EPSILON1=.1;p.EPSILON2=.01;p.EPSILON3=.001;p.EPSILON4=1e-4;p.EPSILON5=1e-5;p.EPSILON6=1e-6;p.EPSILON7=1e-7;p.EPSILON8=1e-8;p.EPSILON9=1e-9;p.EPSILON10=1e-10;p.EPSILON11=1e-11;p.EPSILON12=1e-12;p.EPSILON13=1e-13;p.EPSILON14=1e-14;p.EPSILON15=1e-15;p.EPSILON16=1e-16;p.EPSILON17=1e-17;p.EPSILON18=1e-18;p.EPSILON19=1e-19;p.EPSILON20=1e-20;p.EPSILON21=1e-21;p.GRAVITATIONALPARAMETER=3986004418e5;p.SOLAR_RADIUS=6955e5;p.LUNAR_RADIUS=1737400;p.SIXTY_FOUR_KILOBYTES=64*1024;p.FOUR_GIGABYTES=4*1024*1024*1024;p.sign=A(Math.sign,function(r){return r=+r,r===0||r!==r?r:r>0?1:-1});p.signNotZero=function(e){return e<0?-1:1};p.toSNorm=function(e,r){return r=A(r,255),Math.round((p.clamp(e,-1,1)*.5+.5)*r)};p.fromSNorm=function(e,r){return r=A(r,255),p.clamp(e,0,r)/r*2-1};p.normalize=function(e,r,i){return i=Math.max(i-r,0),i===0?0:p.clamp((e-r)/i,0,1)};p.sinh=A(Math.sinh,function(r){return(Math.exp(r)-Math.exp(-r))/2});p.cosh=A(Math.cosh,function(r){return(Math.exp(r)+Math.exp(-r))/2});p.lerp=function(e,r,i){return(1-i)*e+i*r};p.PI=Math.PI;p.ONE_OVER_PI=1/Math.PI;p.PI_OVER_TWO=Math.PI/2;p.PI_OVER_THREE=Math.PI/3;p.PI_OVER_FOUR=Math.PI/4;p.PI_OVER_SIX=Math.PI/6;p.THREE_PI_OVER_TWO=3*Math.PI/2;p.TWO_PI=2*Math.PI;p.ONE_OVER_TWO_PI=1/(2*Math.PI);p.RADIANS_PER_DEGREE=Math.PI/180;p.DEGREES_PER_RADIAN=180/Math.PI;p.RADIANS_PER_ARCSECOND=p.RADIANS_PER_DEGREE/3600;p.toRadians=function(e){return e*p.RADIANS_PER_DEGREE};p.toDegrees=function(e){return e*p.DEGREES_PER_RADIAN};p.convertLongitudeRange=function(e){let r=p.TWO_PI,i=e-Math.floor(e/r)*r;return i<-Math.PI?i+r:i>=Math.PI?i-r:i};p.clampToLatitudeRange=function(e){return p.clamp(e,-1*p.PI_OVER_TWO,p.PI_OVER_TWO)};p.negativePiToPi=function(e){return e>=-p.PI&&e<=p.PI?e:p.zeroToTwoPi(e+p.PI)-p.PI};p.zeroToTwoPi=function(e){if(e>=0&&e<=p.TWO_PI)return e;let r=p.mod(e,p.TWO_PI);return Math.abs(r)<p.EPSILON14&&Math.abs(e)>p.EPSILON14?p.TWO_PI:r};p.mod=function(e,r){return p.sign(e)===p.sign(r)&&Math.abs(e)<Math.abs(r)?e:(e%r+r)%r};p.equalsEpsilon=function(e,r,i,a){i=A(i,0),a=A(a,i);let c=Math.abs(e-r);return c<=a||c<=i*Math.max(Math.abs(e),Math.abs(r))};p.lessThan=function(e,r,i){return e-r<-i};p.lessThanOrEquals=function(e,r,i){return e-r<i};p.greaterThan=function(e,r,i){return e-r>i};p.greaterThanOrEquals=function(e,r,i){return e-r>-i};var Ne=[1];p.factorial=function(e){let r=Ne.length;if(e>=r){let i=Ne[r-1];for(let a=r;a<=e;a++){let c=i*a;Ne.push(c),i=c}}return Ne[e]};p.incrementWrap=function(e,r,i){return i=A(i,0),++e,e>r&&(e=i),e};p.isPowerOfTwo=function(e){return e!==0&&(e&e-1)===0};p.nextPowerOfTwo=function(e){return--e,e|=e>>1,e|=e>>2,e|=e>>4,e|=e>>8,e|=e>>16,++e,e};p.previousPowerOfTwo=function(e){return e|=e>>1,e|=e>>2,e|=e>>4,e|=e>>8,e|=e>>16,e|=e>>32,e=(e>>>0)-(e>>>1),e};p.clamp=function(e,r,i){return e<r?r:e>i?i:e};var It=new Xe.default;p.setRandomNumberSeed=function(e){It=new Xe.default(e)};p.nextRandomNumber=function(){return It.random()};p.randomBetween=function(e,r){return p.nextRandomNumber()*(r-e)+e};p.acosClamped=function(e){return Math.acos(p.clamp(e,-1,1))};p.asinClamped=function(e){return Math.asin(p.clamp(e,-1,1))};p.chordLength=function(e,r){return 2*r*Math.sin(e*.5)};p.logBase=function(e,r){return Math.log(e)/Math.log(r)};p.cbrt=A(Math.cbrt,function(r){let i=Math.pow(Math.abs(r),.3333333333333333);return r<0?-i:i});p.log2=A(Math.log2,function(r){return Math.log(r)*Math.LOG2E});p.fog=function(e,r){let i=e*r;return 1-Math.exp(-(i*i))};p.fastApproximateAtan=function(e){return e*(-.1784*Math.abs(e)-.0663*e*e+1.0301)};p.fastApproximateAtan2=function(e,r){let i,a=Math.abs(e);i=Math.abs(r);let c=Math.max(a,i);i=Math.min(a,i);let o=i/c;return a=p.fastApproximateAtan(o),a=Math.abs(r)>Math.abs(e)?p.PI_OVER_TWO-a:a,a=e<0?p.PI-a:a,a=r<0?-a:a,a};var Et=p;function mr(e){let r="";for(let i in e)if(e.hasOwnProperty(i)){let a=e[i],c=`${encodeURIComponent(i)}=`;if(Array.isArray(a))for(let o=0,h=a.length;o<h;++o)r+=`${c+encodeURIComponent(a[o])}&`;else r+=`${c+encodeURIComponent(a)}&`}return r=r.slice(0,-1),r}var Ot=mr;function pr(e){let r={};if(e==="")return r;let i=e.replace(/\+/g,"%20").split(/[&;]/);for(let a=0,c=i.length;a<c;++a){let o=i[a].split("="),h=decodeURIComponent(o[0]),f=o[1];y(f)?f=decodeURIComponent(f):f="";let _=r[h];typeof _=="string"?r[h]=[_,f]:Array.isArray(_)?_.push(f):r[h]=f}return r}var Pt=pr;var dr={UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5},C=Object.freeze(dr);var gr={TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3},Ct=Object.freeze(gr);function je(e){e=A(e,A.EMPTY_OBJECT);let r=A(e.throttleByServer,!1),i=A(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=A(e.priority,0),this.throttle=i,this.throttleByServer=r,this.type=A(e.type,Ct.OTHER),this.serverKey=e.serverKey,this.state=C.UNISSUED,this.deferred=void 0,this.cancelled=!1}je.prototype.cancel=function(){this.cancelled=!0};je.prototype.clone=function(e){return y(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=C.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new je(this)};var Rt=je;function yr(e){let r={};if(!e)return r;let i=e.split(`\r
`);for(let a=0;a<i.length;++a){let c=i[a],o=c.indexOf(": ");if(o>0){let h=c.substring(0,o),f=c.substring(o+2);r[h]=f}}return r}var Tt=yr;function qt(e,r,i){this.statusCode=e,this.response=r,this.responseHeaders=i,typeof this.responseHeaders=="string"&&(this.responseHeaders=Tt(this.responseHeaders))}qt.prototype.toString=function(){let e="Request has failed.";return y(this.statusCode)&&(e+=` Status Code: ${this.statusCode}`),e};var Ie=qt;var Be=Z(le(),1);function Ee(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}Object.defineProperties(Ee.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}});Ee.prototype.addEventListener=function(e,r){this._listeners.push(e),this._scopes.push(r);let i=this;return function(){i.removeEventListener(e,r)}};Ee.prototype.removeEventListener=function(e,r){let i=this._listeners,a=this._scopes,c=-1;for(let o=0;o<i.length;o++)if(i[o]===e&&a[o]===r){c=o;break}return c!==-1?(this._insideRaiseEvent?(this._toRemove.push(c),i[c]=void 0,a[c]=void 0):(i.splice(c,1),a.splice(c,1)),!0):!1};function _r(e,r){return r-e}Ee.prototype.raiseEvent=function(){this._insideRaiseEvent=!0;let e,r=this._listeners,i=this._scopes,a=r.length;for(e=0;e<a;e++){let o=r[e];y(o)&&r[e].apply(i[e],arguments)}let c=this._toRemove;if(a=c.length,a>0){for(c.sort(_r),e=0;e<a;e++){let o=c[e];r.splice(o,1),i.splice(o,1)}c.length=0}this._insideRaiseEvent=!1};var Ut=Ee;function pe(e){this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}Object.defineProperties(pe.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){let r=this._length;if(e<r){let i=this._array;for(let a=e;a<r;++a)i[a]=void 0;this._length=e,i.length=e}this._maximumLength=e}},comparator:{get:function(){return this._comparator}}});function Ge(e,r,i){let a=e[r];e[r]=e[i],e[i]=a}pe.prototype.reserve=function(e){e=A(e,this._length),this._array.length=e};pe.prototype.heapify=function(e){e=A(e,0);let r=this._length,i=this._comparator,a=this._array,c=-1,o=!0;for(;o;){let h=2*(e+1),f=h-1;f<r&&i(a[f],a[e])<0?c=f:c=e,h<r&&i(a[h],a[c])<0&&(c=h),c!==e?(Ge(a,c,e),e=c):o=!1}};pe.prototype.resort=function(){let e=this._length;for(let r=Math.ceil(e/2);r>=0;--r)this.heapify(r)};pe.prototype.insert=function(e){let r=this._array,i=this._comparator,a=this._maximumLength,c=this._length++;for(c<r.length?r[c]=e:r.push(e);c!==0;){let h=Math.floor((c-1)/2);if(i(r[c],r[h])<0)Ge(r,c,h),c=h;else break}let o;return y(a)&&this._length>a&&(o=r[a],this._length=a),o};pe.prototype.pop=function(e){if(e=A(e,0),this._length===0)return;let r=this._array,i=r[e];return Ge(r,e,--this._length),this.heapify(e),r[this._length]=void 0,i};var kt=pe;function br(e,r){return e.priority-r.priority}var E={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0},ve=20,B=new kt({comparator:br});B.maximumLength=ve;B.reserve(ve);var te=[],se={},vr=typeof document<"u"?new Be.default(document.location.href):new Be.default,Ve=new Ut;function P(){}P.maximumRequests=250;P.maximumRequestsPerServer=30;P.requestsByServer={"api.cesium.com:443":18,"assets.ion.cesium.com:443":18,"ibasemaps-api.arcgis.com:443":18,"tile.googleapis.com:443":18};P.throttleRequests=!0;P.debugShowStatistics=!1;P.requestCompletedEvent=Ve;Object.defineProperties(P,{statistics:{get:function(){return E}},priorityHeapLength:{get:function(){return ve},set:function(e){if(e<ve)for(;B.length>e;){let r=B.pop();de(r)}ve=e,B.maximumLength=e,B.reserve(e)}}});function zt(e){y(e.priorityFunction)&&(e.priority=e.priorityFunction())}P.serverHasOpenSlots=function(e,r){r=A(r,1);let i=A(P.requestsByServer[e],P.maximumRequestsPerServer);return se[e]+r<=i};P.heapHasOpenSlots=function(e){return B.length+e<=ve};function Lt(e){return e.state===C.UNISSUED&&(e.state=C.ISSUED,e.deferred=me()),e.deferred.promise}function xr(e){return function(r){if(e.state===C.CANCELLED)return;let i=e.deferred;--E.numberOfActiveRequests,--se[e.serverKey],Ve.raiseEvent(),e.state=C.RECEIVED,e.deferred=void 0,i.resolve(r)}}function wr(e){return function(r){e.state!==C.CANCELLED&&(++E.numberOfFailedRequests,--E.numberOfActiveRequests,--se[e.serverKey],Ve.raiseEvent(r),e.state=C.FAILED,e.deferred.reject(r))}}function Ft(e){let r=Lt(e);return e.state=C.ACTIVE,te.push(e),++E.numberOfActiveRequests,++E.numberOfActiveRequestsEver,++se[e.serverKey],e.requestFunction().then(xr(e)).catch(wr(e)),r}function de(e){let r=e.state===C.ACTIVE;if(e.state=C.CANCELLED,++E.numberOfCancelledRequests,y(e.deferred)){let i=e.deferred;e.deferred=void 0,i.reject()}r&&(--E.numberOfActiveRequests,--se[e.serverKey],++E.numberOfCancelledActiveRequests),y(e.cancelFunction)&&e.cancelFunction()}P.update=function(){let e,r,i=0,a=te.length;for(e=0;e<a;++e){if(r=te[e],r.cancelled&&de(r),r.state!==C.ACTIVE){++i;continue}i>0&&(te[e-i]=r)}te.length-=i;let c=B.internalArray,o=B.length;for(e=0;e<o;++e)zt(c[e]);B.resort();let h=Math.max(P.maximumRequests-te.length,0),f=0;for(;f<h&&B.length>0;){if(r=B.pop(),r.cancelled){de(r);continue}if(r.throttleByServer&&!P.serverHasOpenSlots(r.serverKey)){de(r);continue}Ft(r),++f}Ar()};P.getServerKey=function(e){let r=new Be.default(e);r.scheme()===""&&(r=r.absoluteTo(vr),r.normalize());let i=r.authority();/:/.test(i)||(i=`${i}:${r.scheme()==="https"?"443":"80"}`);let a=se[i];return y(a)||(se[i]=0),i};P.request=function(e){if(De(e.url)||Me(e.url))return Ve.raiseEvent(),e.state=C.RECEIVED,e.requestFunction();if(++E.numberOfAttemptedRequests,y(e.serverKey)||(e.serverKey=P.getServerKey(e.url)),P.throttleRequests&&e.throttleByServer&&!P.serverHasOpenSlots(e.serverKey))return;if(!P.throttleRequests||!e.throttle)return Ft(e);if(te.length>=P.maximumRequests)return;zt(e);let r=B.insert(e);if(y(r)){if(r===e)return;de(r)}return Lt(e)};function Ar(){P.debugShowStatistics&&(E.numberOfActiveRequests===0&&E.lastNumberOfActiveRequests>0&&(E.numberOfAttemptedRequests>0&&(console.log(`Number of attempted requests: ${E.numberOfAttemptedRequests}`),E.numberOfAttemptedRequests=0),E.numberOfCancelledRequests>0&&(console.log(`Number of cancelled requests: ${E.numberOfCancelledRequests}`),E.numberOfCancelledRequests=0),E.numberOfCancelledActiveRequests>0&&(console.log(`Number of cancelled active requests: ${E.numberOfCancelledActiveRequests}`),E.numberOfCancelledActiveRequests=0),E.numberOfFailedRequests>0&&(console.log(`Number of failed requests: ${E.numberOfFailedRequests}`),E.numberOfFailedRequests=0)),E.lastNumberOfActiveRequests=E.numberOfActiveRequests)}P.clearForSpecs=function(){for(;B.length>0;){let r=B.pop();de(r)}let e=te.length;for(let r=0;r<e;++r)de(te[r]);te.length=0,se={},E.numberOfAttemptedRequests=0,E.numberOfActiveRequests=0,E.numberOfCancelledRequests=0,E.numberOfCancelledActiveRequests=0,E.numberOfFailedRequests=0,E.numberOfActiveRequestsEver=0,E.lastNumberOfActiveRequests=0};P.numberOfActiveRequestsByServer=function(e){return se[e]};P.requestHeap=B;var Qe=P;function Oe(e){this.name="RuntimeError",this.message=e;let r;try{throw new Error}catch(i){r=i.stack}this.stack=r}y(Object.create)&&(Oe.prototype=Object.create(Error.prototype),Oe.prototype.constructor=Oe);Oe.prototype.toString=function(){let e=`${this.name}: ${this.message}`;return y(this.stack)&&(e+=`
${this.stack.toString()}`),e};var Pe=Oe;var Mt=Z(le(),1);var Ce={},xe={};Ce.add=function(e,r){let i=`${e.toLowerCase()}:${r}`;y(xe[i])||(xe[i]=!0)};Ce.remove=function(e,r){let i=`${e.toLowerCase()}:${r}`;y(xe[i])&&delete xe[i]};function Sr(e){let r=new Mt.default(e);r.normalize();let i=r.authority();if(i.length!==0){if(r.authority(i),i.indexOf("@")!==-1&&(i=i.split("@")[1]),i.indexOf(":")===-1){let a=r.scheme();if(a.length===0&&(a=window.location.protocol,a=a.substring(0,a.length-1)),a==="http")i+=":80";else if(a==="https")i+=":443";else return}return i}}Ce.contains=function(e){let r=Sr(e);return!!(y(r)&&y(xe[r]))};Ce.clear=function(){xe={}};var Ze=Ce;var Bt=function(){try{let e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob",e.responseType==="blob"}catch{return!1}}();function g(e){e=A(e,A.EMPTY_OBJECT),typeof e=="string"&&(e={url:e}),this._url=void 0,this._templateValues=re(e.templateValues,{}),this._queryParameters=re(e.queryParameters,{}),this.headers=re(e.headers,{}),this.request=A(e.request,new Rt),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=A(e.retryAttempts,0),this._retryCount=0,A(e.parseUrl,!0)?this.parseUrl(e.url,!0,!0):this._url=e.url,this._credits=e.credits,this.useEVServer=!0}function re(e,r){return y(e)?be(e):r}g.createIfNeeded=function(e){return e instanceof g?e.getDerivedResource({request:e.request}):typeof e!="string"?e:new g({url:e})};var we;g.supportsImageBitmapOptions=function(){if(y(we))return we;if(typeof createImageBitmap!="function")return we=Promise.resolve(!1),we;let e="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAABGdBTUEAAE4g3rEiDgAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAADElEQVQI12Ng6GAAAAEUAIngE3ZiAAAAAElFTkSuQmCC";return we=g.fetchBlob({url:e}).then(function(r){let i={imageOrientation:"flipY",premultiplyAlpha:"none",colorSpaceConversion:"none"};return Promise.all([createImageBitmap(r,i),createImageBitmap(r)])}).then(function(r){let i=Ye(r[0]),a=Ye(r[1]);return i[1]!==a[1]}).catch(function(){return!1}),we};Object.defineProperties(g,{isBlobSupported:{get:function(){return Bt}}});Object.defineProperties(g.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){this.parseUrl(e,!1,!1)}},extension:{get:function(){return bt(this._url)}},isDataUri:{get:function(){return De(this._url)}},isBlobUri:{get:function(){return Me(this._url)}},isCrossOriginUrl:{get:function(){return xt(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}},credits:{get:function(){return this._credits}}});g.prototype.toString=function(){return this.getUrlComponent(!0,!0)};g.prototype.parseUrl=function(e,r,i,a){let c=new jt.default(e),o=Ir(c.query());this._queryParameters=r?$e(o,this.queryParameters,i):o,c.search(""),c.fragment(""),y(a)&&c.scheme()===""&&(c=c.absoluteTo(dt(a))),this._url=c.toString()};function Ir(e){return e.length===0?{}:e.indexOf("=")===-1?{[e]:void 0}:Pt(e)}function $e(e,r,i){if(!i)return ce(e,r);let a=be(e,!0);for(let c in r)if(r.hasOwnProperty(c)){let o=a[c],h=r[c];y(o)?(Array.isArray(o)||(o=a[c]=[o]),a[c]=o.concat(h)):a[c]=Array.isArray(h)?h.slice():h}return a}g.prototype.getUrlComponent=function(e,r){if(this.isDataUri)return this._url;let i=this._url;e&&(i=`${i}${Er(this.queryParameters)}`),i=i.replace(/%7B/g,"{").replace(/%7D/g,"}");let a=this._templateValues;return Object.keys(a).length>0&&(i=i.replace(/{(.*?)}/g,function(c,o){let h=a[o];return y(h)?encodeURIComponent(h):c})),r&&y(this.proxy)&&(i=this.proxy.getURL(i)),i};function Er(e){let r=Object.keys(e);return r.length===0?"":r.length===1&&!y(e[r[0]])?`?${r[0]}`:`?${Ot(e)}`}g.prototype.setQueryParameters=function(e,r){r?this._queryParameters=$e(this._queryParameters,e,!1):this._queryParameters=$e(e,this._queryParameters,!1)};g.prototype.appendQueryParameters=function(e){this._queryParameters=$e(e,this._queryParameters,!0)};g.prototype.setTemplateValues=function(e,r){r?this._templateValues=ce(this._templateValues,e):this._templateValues=ce(e,this._templateValues)};g.prototype.getDerivedResource=function(e){let r=this.clone();if(r._retryCount=0,y(e.url)){let i=A(e.preserveQueryParameters,!1);r.parseUrl(e.url,!0,i,this._url)}return r.useEVServer&&(y(e.queryParameters)||(e.queryParameters={}),!y(e.queryParameters.token)&&(this._url.indexOf("earthview")!==-1||this._url.indexOf("EV_WebService")!==-1)&&(e.queryParameters.token="f584b6bdee30490")),y(e.queryParameters)&&(r._queryParameters=ce(e.queryParameters,r.queryParameters)),y(e.templateValues)&&(r._templateValues=ce(e.templateValues,r.templateValues)),y(e.headers)&&(r.headers=ce(e.headers,r.headers)),y(e.proxy)&&(r.proxy=e.proxy),y(e.request)&&(r.request=e.request),y(e.retryCallback)&&(r.retryCallback=e.retryCallback),y(e.retryAttempts)&&(r.retryAttempts=e.retryAttempts),r};g.prototype.retryOnError=function(e){let r=this.retryCallback;if(typeof r!="function"||this._retryCount>=this.retryAttempts)return Promise.resolve(!1);let i=this;return Promise.resolve(r(this,e)).then(function(a){return++i._retryCount,a})};g.prototype.clone=function(e){return y(e)?(e._url=this._url,e._queryParameters=be(this._queryParameters),e._templateValues=be(this._templateValues),e.headers=be(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e):new g({url:this._url,queryParameters:this.queryParameters,templateValues:this.templateValues,headers:this.headers,proxy:this.proxy,retryCallback:this.retryCallback,retryAttempts:this.retryAttempts,request:this.request.clone(),parseUrl:!1,credits:y(this.credits)?this.credits.slice():void 0})};g.prototype.getBaseUri=function(e){return yt(this.getUrlComponent(e),e)};g.prototype.appendForwardSlash=function(){this._url=lt(this._url)};g.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})};g.fetchArrayBuffer=function(e){return new g(e).fetchArrayBuffer()};g.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})};g.fetchBlob=function(e){return new g(e).fetchBlob()};g.prototype.fetchImage=function(e){e=A(e,A.EMPTY_OBJECT);let r=A(e.preferImageBitmap,!1),i=A(e.preferBlob,!1),a=A(e.flipY,!1),c=A(e.skipColorSpaceConversion,!1);if(tt(this.request),!Bt||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!i)return et({resource:this,flipY:a,skipColorSpaceConversion:c,preferImageBitmap:r});let o=this.fetchBlob();if(!y(o))return;let h,f,_,S;return g.supportsImageBitmapOptions().then(function(w){return h=w,f=h&&r,o}).then(function(w){if(!y(w))return;if(S=w,f)return g.createImageBitmapFromBlob(w,{flipY:a,premultiplyAlpha:!1,skipColorSpaceConversion:c});let v=window.URL.createObjectURL(w);return _=new g({url:v}),et({resource:_,flipY:a,skipColorSpaceConversion:c,preferImageBitmap:!1})}).then(function(w){if(y(w))return w.blob=S,f||window.URL.revokeObjectURL(_.url),w}).catch(function(w){return y(_)&&window.URL.revokeObjectURL(_.url),w.blob=S,Promise.reject(w)})};function et(e){let r=e.resource,i=e.flipY,a=e.skipColorSpaceConversion,c=e.preferImageBitmap,o=r.request;o.url=r.url,o.requestFunction=function(){let f=!1;!r.isDataUri&&!r.isBlobUri&&(f=r.isCrossOriginUrl);let _=me();return g._Implementations.createImage(o,f,_,i,a,c),_.promise};let h=Qe.request(o);if(y(h))return h.catch(function(f){return o.state!==C.FAILED?Promise.reject(f):r.retryOnError(f).then(function(_){return _?(o.state=C.UNISSUED,o.deferred=void 0,et({resource:r,flipY:i,skipColorSpaceConversion:a,preferImageBitmap:c})):Promise.reject(f)})})}g.fetchImage=function(e){return new g(e).fetchImage({flipY:e.flipY,skipColorSpaceConversion:e.skipColorSpaceConversion,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})};g.prototype.fetchText=function(){return this.fetch({responseType:"text"})};g.fetchText=function(e){return new g(e).fetchText()};g.prototype.fetchJson=function(){let e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(y(e))return e.then(function(r){if(y(r))return JSON.parse(r)})};g.fetchJson=function(e){return new g(e).fetchJson()};g.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})};g.fetchXML=function(e){return new g(e).fetchXML()};g.prototype.fetchJsonp=function(e){e=A(e,"callback"),tt(this.request);let r;do r=`loadJsonp${Et.nextRandomNumber().toString().substring(2,8)}`;while(y(window[r]));return Vt(this,e,r)};function Vt(e,r,i){let a={};a[r]=i,e.setQueryParameters(a);let c=e.request,o=e.url;c.url=o,c.requestFunction=function(){let f=me();return window[i]=function(_){f.resolve(_);try{delete window[i]}catch{window[i]=void 0}},g._Implementations.loadAndExecuteScript(o,i,f),f.promise};let h=Qe.request(c);if(y(h))return h.catch(function(f){return c.state!==C.FAILED?Promise.reject(f):e.retryOnError(f).then(function(_){return _?(c.state=C.UNISSUED,c.deferred=void 0,Vt(e,r,i)):Promise.reject(f)})})}g.fetchJsonp=function(e){return new g(e).fetchJsonp(e.callbackParameterName)};g.prototype._makeRequest=function(e){let r=this;tt(r.request);let i=r.request,a=r.url;i.url=a,i.requestFunction=function(){let o=e.responseType,h=ce(e.headers,r.headers),f=e.overrideMimeType,_=e.method,S=e.data,w=me(),v=g._Implementations.loadWithXhr(a,o,_,S,h,w,f);return y(v)&&y(v.abort)&&(i.cancelFunction=function(){v.abort()}),w.promise};let c=Qe.request(i);if(y(c))return c.then(function(o){return i.cancelFunction=void 0,o}).catch(function(o){return i.cancelFunction=void 0,i.state!==C.FAILED?Promise.reject(o):r.retryOnError(o).then(function(h){return h?(i.state=C.UNISSUED,i.deferred=void 0,r.fetch(e)):Promise.reject(o)})})};function tt(e){if(e.state===C.ISSUED||e.state===C.ACTIVE)throw new Pe("The Resource is already being fetched.");e.state=C.UNISSUED,e.deferred=void 0}var Or=/^data:(.*?)(;base64)?,(.*)$/;function He(e,r){let i=decodeURIComponent(r);return e?atob(i):i}function Dt(e,r){let i=He(e,r),a=new ArrayBuffer(i.length),c=new Uint8Array(a);for(let o=0;o<i.length;o++)c[o]=i.charCodeAt(o);return a}function Pr(e,r){r=A(r,"");let i=e[1],a=!!e[2],c=e[3],o,h;switch(r){case"":case"text":return He(a,c);case"arraybuffer":return Dt(a,c);case"blob":return o=Dt(a,c),new Blob([o],{type:i});case"document":return h=new DOMParser,h.parseFromString(He(a,c),i);case"json":return JSON.parse(He(a,c));default:}}g.prototype.fetch=function(e){return e=re(e,{}),e.method="GET",this._makeRequest(e)};g.fetch=function(e){return new g(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};g.prototype.delete=function(e){return e=re(e,{}),e.method="DELETE",this._makeRequest(e)};g.delete=function(e){return new g(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})};g.prototype.head=function(e){return e=re(e,{}),e.method="HEAD",this._makeRequest(e)};g.head=function(e){return new g(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};g.prototype.options=function(e){return e=re(e,{}),e.method="OPTIONS",this._makeRequest(e)};g.options=function(e){return new g(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};g.prototype.post=function(e,r){return ue.defined("data",e),r=re(r,{}),r.method="POST",r.data=e,this._makeRequest(r)};g.post=function(e){return new g(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};g.prototype.put=function(e,r){return ue.defined("data",e),r=re(r,{}),r.method="PUT",r.data=e,this._makeRequest(r)};g.put=function(e){return new g(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};g.prototype.patch=function(e,r){return ue.defined("data",e),r=re(r,{}),r.method="PATCH",r.data=e,this._makeRequest(r)};g.patch=function(e){return new g(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};g._Implementations={};g._Implementations.loadImageElement=function(e,r,i){let a=new Image;a.crossOrigin="anonymous",a.onload=function(){a.naturalWidth===0&&a.naturalHeight===0&&a.width===0&&a.height===0&&(a.width=300,a.height=150),i.resolve(a)},a.onerror=function(c){i.reject(c)},r&&(Ze.contains(e)?a.crossOrigin="use-credentials":a.crossOrigin=""),a.src=e};g._Implementations.createImage=function(e,r,i,a,c,o){let h=e.url;g.supportsImageBitmapOptions().then(function(f){if(!(f&&o)){g._Implementations.loadImageElement(h,r,i);return}let _="blob",S="GET",w=me(),v=g._Implementations.loadWithXhr(h,_,S,void 0,void 0,w,void 0,void 0,void 0);return y(v)&&y(v.abort)&&(e.cancelFunction=function(){v.abort()}),w.promise.then(function(U){if(!y(U)){i.reject(new Pe(`Successfully retrieved ${h} but it contained no content.`));return}return g.createImageBitmapFromBlob(U,{flipY:a,premultiplyAlpha:!1,skipColorSpaceConversion:c})}).then(function(U){i.resolve(U)})}).catch(function(f){i.reject(f)})};g.createImageBitmapFromBlob=function(e,r){return ue.defined("options",r),ue.typeOf.bool("options.flipY",r.flipY),ue.typeOf.bool("options.premultiplyAlpha",r.premultiplyAlpha),ue.typeOf.bool("options.skipColorSpaceConversion",r.skipColorSpaceConversion),createImageBitmap(e,{imageOrientation:r.flipY?"flipY":"none",premultiplyAlpha:r.premultiplyAlpha?"premultiply":"none",colorSpaceConversion:r.skipColorSpaceConversion?"none":"default"})};function Nt(e,r){switch(r){case"text":return e.toString("utf8");case"json":return JSON.parse(e.toString("utf8"));default:return new Uint8Array(e).buffer}}function Cr(e,r,i,a,c,o,h){let f,_;Promise.all([import("url"),import("zlib")]).then(([S,w])=>(f=S.parse(e),_=w,f.protocol==="https:"?import("https"):import("http"))).then(S=>{let w={protocol:f.protocol,hostname:f.hostname,port:f.port,path:f.path,query:f.query,method:i,headers:c};S.request(w).on("response",function(v){if(v.statusCode<200||v.statusCode>=300){o.reject(new Ie(v.statusCode,v,v.headers));return}let U=[];v.on("data",function(q){U.push(q)}),v.on("end",function(){let q=Buffer.concat(U);v.headers["content-encoding"]==="gzip"?_.gunzip(q,function(L,D){L?o.reject(new Pe("Error decompressing response.")):o.resolve(Nt(D,r))}):o.resolve(Nt(q,r))})}).on("error",function(v){o.reject(new Ie)}).end()})}var Rr=typeof XMLHttpRequest>"u";g._Implementations.loadWithXhr=function(e,r,i,a,c,o,h){let f=Or.exec(e);if(f!==null){o.resolve(Pr(f,r));return}if(Rr){Cr(e,r,i,a,c,o,h);return}let _=new XMLHttpRequest;if(Ze.contains(e)&&(_.withCredentials=!0),_.open(i,e,!0),y(h)&&y(_.overrideMimeType)&&_.overrideMimeType(h),y(c))for(let w in c)c.hasOwnProperty(w)&&_.setRequestHeader(w,c[w]);y(r)&&(_.responseType=r);let S=!1;return typeof e=="string"&&(S=e.indexOf("file://")===0||typeof window<"u"&&window.location.origin==="file://"),_.onload=function(){if((_.status<200||_.status>=300)&&!(S&&_.status===0)){o.reject(new Ie(_.status,_.response,_.getAllResponseHeaders()));return}let w=_.response,v=_.responseType;if(i==="HEAD"||i==="OPTIONS"){let q=_.getAllResponseHeaders().trim().split(/[\r\n]+/),L={};q.forEach(function(D){let K=D.split(": "),H=K.shift();L[H]=K.join(": ")}),o.resolve(L);return}if(_.status===204)o.resolve();else if(y(w)&&(!y(r)||v===r))o.resolve(w);else if(r==="json"&&typeof w=="string")try{o.resolve(JSON.parse(w))}catch(U){o.reject(U)}else(v===""||v==="document")&&y(_.responseXML)&&_.responseXML.hasChildNodes()?o.resolve(_.responseXML):(v===""||v==="text")&&y(_.responseText)?o.resolve(_.responseText):o.reject(new Pe("Invalid XMLHttpRequest response type."))},_.onerror=function(w){o.reject(new Ie)},_.send(a),_};g._Implementations.loadAndExecuteScript=function(e,r,i){return wt(e,r).catch(function(a){i.reject(a)})};g._DefaultImplementations={};g._DefaultImplementations.createImage=g._Implementations.createImage;g._DefaultImplementations.loadWithXhr=g._Implementations.loadWithXhr;g._DefaultImplementations.loadAndExecuteScript=g._Implementations.loadAndExecuteScript;g.DEFAULT=Object.freeze(new g({url:typeof document>"u"?"":document.location.href.split("?")[0]}));var Qt=g;function Re(e){let r=this;this.ip=A(e.ip,"127.0.0.1:8080"),r._url=`http://${r.ip}/earthview/rest/services/mapserver/networkAnalysis`,r._resource=new Qt({url:r._url,headers:{"Content-Type":"application/json"}})}Re.prototype.getRoutePositions=function(e,r,i,a,c,o){let h=Tr(a),f=qr(c),_={serviceName:e,datasetName:r,stops:h,barriers:f,cost:i,fields:[]},S=`http://${this.ip}/earthview/rest/services/mapserver/networkAnalysis`;new Cesium.Resource({url:S,headers:{"Content-Type":"application/json"}}).post(JSON.stringify(_)).then(function(v){let U=JSON.parse(v),q=[];for(let L=0;L<U.length;L++){let K=U[L].line.match(/\(([^)]*)\)/);q=q.concat(K[1].replace(/ /g,",").split(",").map(function(H){return parseFloat(H)}))}o&&o(q)}).catch(function(v){console.log(v)})};function Tr(e){let r=[];for(let i=0;i<e.length;i++){let a="";a+="Point(",a+=Cesium.Math.toDegrees(e[i].longitude),a+=" ",a+=Cesium.Math.toDegrees(e[i].latitude),a+=")",r.push(a)}return r}function qr(e){let r=[];for(let i=0;i<e.length;i++){let a="";a+="Point(",a+=Cesium.Math.toDegrees(e[i].longitude),a+=" ",a+=Cesium.Math.toDegrees(e[i].latitude),a+=")",r.push(a)}return r}Re.prototype.queryPlaceName=function(e,r,i,a){let c=JSON.stringify({mapName:e,layerName:r,queryFileds:"*",cliped:"false",start:1,maxFeatures:1e3,filter:{type:"And",condition:[{queryType:"PropertyIsLike",propertyName:"NAME",value:i}]}}),o=`http://${this.ip}/earthview/rest/services/mapserver/wfs/getFeatureByJson`,h=this._resource.clone();h.url=o,h.post(c).then(function(f){a&&a(JSON.parse(f).result)}).catch(function(f){console.log(f)})};Re.prototype.queryPlaceNameServer7=async function(e,r,i){let a=`http://${this.ip}/earthview/rest/services/vectorserver/geoCodeQuery`,c=JSON.stringify({batch:!0,query:[{address:e,dataName:r,field:i}]}),h=await new Cesium.Resource({url:a,headers:{"Content-Type":"application/json"}}).post(c);return JSON.parse(h).data.results};Re.prototype.reQueryPlaceNameServer7=async function(e,r,i){let a=`http://${this.ip}/earthview/rest/services/vectorserver/reGeoCodeQuery`,c=JSON.stringify({batch:!0,query:[{dataName:e,locations:[{location:r,radius:i}]}]}),h=await new Cesium.Resource({url:a,headers:{"Content-Type":"application/json"}}).post(c);return JSON.parse(h).data.results};var Ht=Re;globalThis.CESIUM_VERSION="1.105.2";0&&(module.exports={EMExtensionsMixin,EV_SeviceAnalysis});
