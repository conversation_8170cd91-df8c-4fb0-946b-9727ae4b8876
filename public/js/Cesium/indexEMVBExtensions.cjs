/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.105.2
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

var sl=Object.create;var zA=Object.defineProperty;var al=Object.getOwnPropertyDescriptor;var rl=Object.getOwnPropertyNames;var cl=Object.getPrototypeOf,ll=Object.prototype.hasOwnProperty;var I=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),pl=(e,t)=>{for(var A in t)zA(e,A,{get:t[A],enumerable:!0})},Is=(e,t,A,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of rl(t))!ll.call(e,i)&&i!==A&&zA(e,i,{get:()=>t[i],enumerable:!(n=al(t,i))||n.enumerable});return e};var Be=(e,t,A)=>(A=e!=null?sl(cl(e)):{},Is(t||!e||!e.__esModule?zA(A,"default",{value:e,enumerable:!0}):A,e)),ul=e=>Is(zA({},"__esModule",{value:!0}),e);var qn=I(ke=>{"use strict";var _s=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",Bl=_s+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040",Ss="["+_s+"]["+Bl+"]*",hl=new RegExp("^"+Ss+"$"),gl=function(e,t){let A=[],n=t.exec(e);for(;n;){let i=[];i.startIndex=t.lastIndex-n[0].length;let s=n.length;for(let o=0;o<s;o++)i.push(n[o]);A.push(i),n=t.exec(e)}return A},xl=function(e){let t=hl.exec(e);return!(t===null||typeof t>"u")};ke.isExist=function(e){return typeof e<"u"};ke.isEmptyObject=function(e){return Object.keys(e).length===0};ke.merge=function(e,t,A){if(t){let n=Object.keys(t),i=n.length;for(let s=0;s<i;s++)A==="strict"?e[n[s]]=[t[n[s]]]:e[n[s]]=t[n[s]]}};ke.getValue=function(e){return ke.isExist(e)?e:""};ke.isName=xl;ke.getAllMatches=gl;ke.nameRegexp=Ss});var Zn=I(zs=>{"use strict";var $n=qn(),wl={allowBooleanAttributes:!1,unpairedTags:[]};zs.validate=function(e,t){t=Object.assign({},wl,t);let A=[],n=!1,i=!1;e[0]==="\uFEFF"&&(e=e.substr(1));for(let s=0;s<e.length;s++)if(e[s]==="<"&&e[s+1]==="?"){if(s+=2,s=Ls(e,s),s.err)return s}else if(e[s]==="<"){let o=s;if(s++,e[s]==="!"){s=Ms(e,s);continue}else{let r=!1;e[s]==="/"&&(r=!0,s++);let a="";for(;s<e.length&&e[s]!==">"&&e[s]!==" "&&e[s]!=="	"&&e[s]!==`
`&&e[s]!=="\r";s++)a+=e[s];if(a=a.trim(),a[a.length-1]==="/"&&(a=a.substring(0,a.length-1),s--),!Hl(a)){let d;return a.trim().length===0?d="Invalid space after '<'.":d="Tag '"+a+"' is an invalid name.",$("InvalidTag",d,he(e,s))}let c=El(e,s);if(c===!1)return $("InvalidAttr","Attributes for '"+a+"' have open quote.",he(e,s));let l=c.value;if(s=c.index,l[l.length-1]==="/"){let d=s-l.length;l=l.substring(0,l.length-1);let f=Ds(l,t);if(f===!0)n=!0;else return $(f.err.code,f.err.msg,he(e,d+f.err.line))}else if(r)if(c.tagClosed){if(l.trim().length>0)return $("InvalidTag","Closing tag '"+a+"' can't have attributes or invalid starting.",he(e,o));{let d=A.pop();if(a!==d.tagName){let f=he(e,d.tagStartPos);return $("InvalidTag","Expected closing tag '"+d.tagName+"' (opened in line "+f.line+", col "+f.col+") instead of closing tag '"+a+"'.",he(e,o))}A.length==0&&(i=!0)}}else return $("InvalidTag","Closing tag '"+a+"' doesn't have proper closing.",he(e,s));else{let d=Ds(l,t);if(d!==!0)return $(d.err.code,d.err.msg,he(e,s-l.length+d.err.line));if(i===!0)return $("InvalidXml","Multiple possible root nodes found.",he(e,s));t.unpairedTags.indexOf(a)!==-1||A.push({tagName:a,tagStartPos:o}),n=!0}for(s++;s<e.length;s++)if(e[s]==="<")if(e[s+1]==="!"){s++,s=Ms(e,s);continue}else if(e[s+1]==="?"){if(s=Ls(e,++s),s.err)return s}else break;else if(e[s]==="&"){let d=vl(e,s);if(d==-1)return $("InvalidChar","char '&' is not expected.",he(e,s));s=d}else if(i===!0&&!Ts(e[s]))return $("InvalidXml","Extra text at the end",he(e,s));e[s]==="<"&&s--}}else{if(Ts(e[s]))continue;return $("InvalidChar","char '"+e[s]+"' is not expected.",he(e,s))}if(n){if(A.length==1)return $("InvalidTag","Unclosed tag '"+A[0].tagName+"'.",he(e,A[0].tagStartPos));if(A.length>0)return $("InvalidXml","Invalid '"+JSON.stringify(A.map(s=>s.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1})}else return $("InvalidXml","Start tag expected.",1);return!0};function Ts(e){return e===" "||e==="	"||e===`
`||e==="\r"}function Ls(e,t){let A=t;for(;t<e.length;t++)if(e[t]=="?"||e[t]==" "){let n=e.substr(A,t-A);if(t>5&&n==="xml")return $("InvalidXml","XML declaration allowed only at the start of the document.",he(e,t));if(e[t]=="?"&&e[t+1]==">"){t++;break}else continue}return t}function Ms(e,t){if(e.length>t+5&&e[t+1]==="-"&&e[t+2]==="-"){for(t+=3;t<e.length;t++)if(e[t]==="-"&&e[t+1]==="-"&&e[t+2]===">"){t+=2;break}}else if(e.length>t+8&&e[t+1]==="D"&&e[t+2]==="O"&&e[t+3]==="C"&&e[t+4]==="T"&&e[t+5]==="Y"&&e[t+6]==="P"&&e[t+7]==="E"){let A=1;for(t+=8;t<e.length;t++)if(e[t]==="<")A++;else if(e[t]===">"&&(A--,A===0))break}else if(e.length>t+9&&e[t+1]==="["&&e[t+2]==="C"&&e[t+3]==="D"&&e[t+4]==="A"&&e[t+5]==="T"&&e[t+6]==="A"&&e[t+7]==="["){for(t+=8;t<e.length;t++)if(e[t]==="]"&&e[t+1]==="]"&&e[t+2]===">"){t+=2;break}}return t}var Ql='"',Cl="'";function El(e,t){let A="",n="",i=!1;for(;t<e.length;t++){if(e[t]===Ql||e[t]===Cl)n===""?n=e[t]:n!==e[t]||(n="");else if(e[t]===">"&&n===""){i=!0;break}A+=e[t]}return n!==""?!1:{value:A,index:t,tagClosed:i}}var Ul=new RegExp(`(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['"])(([\\s\\S])*?)\\5)?`,"g");function Ds(e,t){let A=$n.getAllMatches(e,Ul),n={};for(let i=0;i<A.length;i++){if(A[i][1].length===0)return $("InvalidAttr","Attribute '"+A[i][2]+"' has no space in starting.",eA(A[i]));if(A[i][3]!==void 0&&A[i][4]===void 0)return $("InvalidAttr","Attribute '"+A[i][2]+"' is without value.",eA(A[i]));if(A[i][3]===void 0&&!t.allowBooleanAttributes)return $("InvalidAttr","boolean attribute '"+A[i][2]+"' is not allowed.",eA(A[i]));let s=A[i][2];if(!Fl(s))return $("InvalidAttr","Attribute '"+s+"' is an invalid name.",eA(A[i]));if(!n.hasOwnProperty(s))n[s]=1;else return $("InvalidAttr","Attribute '"+s+"' is repeated.",eA(A[i]))}return!0}function bl(e,t){let A=/\d/;for(e[t]==="x"&&(t++,A=/[\da-fA-F]/);t<e.length;t++){if(e[t]===";")return t;if(!e[t].match(A))break}return-1}function vl(e,t){if(t++,e[t]===";")return-1;if(e[t]==="#")return t++,bl(e,t);let A=0;for(;t<e.length;t++,A++)if(!(e[t].match(/\w/)&&A<20)){if(e[t]===";")break;return-1}return t}function $(e,t,A){return{err:{code:e,msg:t,line:A.line||A,col:A.col}}}function Fl(e){return $n.isName(e)}function Hl(e){return $n.isName(e)}function he(e,t){let A=e.substring(0,t).split(/\r?\n/);return{line:A.length,col:A[A.length-1].length+1}}function eA(e){return e.startIndex+e[1].length}});var ks=I(Yn=>{var Ps={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1},Nl=function(e){return Object.assign({},Ps,e)};Yn.buildOptions=Nl;Yn.defaultOptions=Ps});var Xs=I((Dg,Vs)=>{"use strict";var ei=class{constructor(t){this.tagname=t,this.child=[],this[":@"]={}}add(t,A){t==="__proto__"&&(t="#__proto__"),this.child.push({[t]:A})}addChild(t){t.tagname==="__proto__"&&(t.tagname="#__proto__"),t[":@"]&&Object.keys(t[":@"]).length>0?this.child.push({[t.tagname]:t.child,[":@"]:t[":@"]}):this.child.push({[t.tagname]:t.child})}};Vs.exports=ei});var Js=I((zg,Gs)=>{function yl(e,t){let A={};if(e[t+3]==="O"&&e[t+4]==="C"&&e[t+5]==="T"&&e[t+6]==="Y"&&e[t+7]==="P"&&e[t+8]==="E"){t=t+9;let n=1,i=!1,s=!1,o=!1,r="";for(;t<e.length;t++)if(e[t]==="<"&&!o){if(i&&e[t+1]==="!"&&e[t+2]==="E"&&e[t+3]==="N"&&e[t+4]==="T"&&e[t+5]==="I"&&e[t+6]==="T"&&e[t+7]==="Y")t+=7,s=!0;else if(i&&e[t+1]==="!"&&e[t+2]==="E"&&e[t+3]==="L"&&e[t+4]==="E"&&e[t+5]==="M"&&e[t+6]==="E"&&e[t+7]==="N"&&e[t+8]==="T")t+=8;else if(i&&e[t+1]==="!"&&e[t+2]==="A"&&e[t+3]==="T"&&e[t+4]==="T"&&e[t+5]==="L"&&e[t+6]==="I"&&e[t+7]==="S"&&e[t+8]==="T")t+=8;else if(i&&e[t+1]==="!"&&e[t+2]==="N"&&e[t+3]==="O"&&e[t+4]==="T"&&e[t+5]==="A"&&e[t+6]==="T"&&e[t+7]==="I"&&e[t+8]==="O"&&e[t+9]==="N")t+=9;else if(e[t+1]==="!"&&e[t+2]==="-"&&e[t+3]==="-")o=!0;else throw new Error("Invalid DOCTYPE");n++,r=""}else if(e[t]===">"){if(o?e[t-1]==="-"&&e[t-2]==="-"&&(o=!1,n--):(s&&(Kl(r,A),s=!1),n--),n===0)break}else e[t]==="["?i=!0:r+=e[t];if(n!==0)throw new Error("Unclosed DOCTYPE")}else throw new Error("Invalid Tag instead of DOCTYPE");return{entities:A,i:t}}var Rl=RegExp(`^\\s([a-zA-z0-0]+)[ 	](['"])([^&]+)\\2`);function Kl(e,t){let A=Rl.exec(e);A&&(t[A[1]]={regx:RegExp(`&${A[1]};`,"g"),val:A[3]})}Gs.exports=yl});var Ws=I((Pg,js)=>{var Il=/^[-+]?0x[a-fA-F0-9]+$/,Ol=/^([\-\+])?(0*)(\.[0-9]+([eE]\-?[0-9]+)?|[0-9]+(\.[0-9]+([eE]\-?[0-9]+)?)?)$/;!Number.parseInt&&window.parseInt&&(Number.parseInt=window.parseInt);!Number.parseFloat&&window.parseFloat&&(Number.parseFloat=window.parseFloat);var _l={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};function Sl(e,t={}){if(t=Object.assign({},_l,t),!e||typeof e!="string")return e;let A=e.trim();if(t.skipLike!==void 0&&t.skipLike.test(A))return e;if(t.hex&&Il.test(A))return Number.parseInt(A,16);{let n=Ol.exec(A);if(n){let i=n[1],s=n[2],o=Tl(n[3]),r=n[4]||n[6];if(!t.leadingZeros&&s.length>0&&i&&A[2]!==".")return e;if(!t.leadingZeros&&s.length>0&&!i&&A[1]!==".")return e;{let a=Number(A),c=""+a;return c.search(/[eE]/)!==-1||r?t.eNotation?a:e:A.indexOf(".")!==-1?c==="0"&&o===""||c===o||i&&c==="-"+o?a:e:s?o===c||i+o===c?a:e:A===c||A===i+c?a:e}}else return e}}function Tl(e){return e&&e.indexOf(".")!==-1&&(e=e.replace(/0+$/,""),e==="."?e="0":e[0]==="."?e="0"+e:e[e.length-1]==="."&&(e=e.substr(0,e.length-1))),e}js.exports=Sl});var $s=I((Vg,qs)=>{"use strict";var ii=qn(),tA=Xs(),Ll=Js(),Ml=Ws(),kg="<((!\\[CDATA\\[([\\s\\S]*?)(]]>))|((NAME:)?(NAME))([^>]*)>|((\\/)(NAME)\\s*>))([^<]*)".replace(/NAME/g,ii.nameRegexp),ti=class{constructor(t){this.options=t,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"\xA2"},pound:{regex:/&(pound|#163);/g,val:"\xA3"},yen:{regex:/&(yen|#165);/g,val:"\xA5"},euro:{regex:/&(euro|#8364);/g,val:"\u20AC"},copyright:{regex:/&(copy|#169);/g,val:"\xA9"},reg:{regex:/&(reg|#174);/g,val:"\xAE"},inr:{regex:/&(inr|#8377);/g,val:"\u20B9"}},this.addExternalEntities=Dl,this.parseXml=Xl,this.parseTextData=zl,this.resolveNameSpace=Pl,this.buildAttributesMap=Vl,this.isItStopNode=jl,this.replaceEntitiesValue=Gl,this.readStopNodeData=ql,this.saveTextToParentTag=Jl}};function Dl(e){let t=Object.keys(e);for(let A=0;A<t.length;A++){let n=t[A];this.lastEntities[n]={regex:new RegExp("&"+n+";","g"),val:e[n]}}}function zl(e,t,A,n,i,s,o){if(e!==void 0&&(this.options.trimValues&&!n&&(e=e.trim()),e.length>0)){o||(e=this.replaceEntitiesValue(e));let r=this.options.tagValueProcessor(t,e,A,i,s);return r==null?e:typeof r!=typeof e||r!==e?r:this.options.trimValues?ni(e,this.options.parseTagValue,this.options.numberParseOptions):e.trim()===e?ni(e,this.options.parseTagValue,this.options.numberParseOptions):e}}function Pl(e){if(this.options.removeNSPrefix){let t=e.split(":"),A=e.charAt(0)==="/"?"/":"";if(t[0]==="xmlns")return"";t.length===2&&(e=A+t[1])}return e}var kl=new RegExp(`([^\\s=]+)\\s*(=\\s*(['"])([\\s\\S]*?)\\3)?`,"gm");function Vl(e,t){if(!this.options.ignoreAttributes&&typeof e=="string"){let A=ii.getAllMatches(e,kl),n=A.length,i={};for(let s=0;s<n;s++){let o=this.resolveNameSpace(A[s][1]),r=A[s][4],a=this.options.attributeNamePrefix+o;if(o.length)if(this.options.transformAttributeName&&(a=this.options.transformAttributeName(a)),a==="__proto__"&&(a="#__proto__"),r!==void 0){this.options.trimValues&&(r=r.trim()),r=this.replaceEntitiesValue(r);let c=this.options.attributeValueProcessor(o,r,t);c==null?i[a]=r:typeof c!=typeof r||c!==r?i[a]=c:i[a]=ni(r,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(i[a]=!0)}if(!Object.keys(i).length)return;if(this.options.attributesGroupName){let s={};return s[this.options.attributesGroupName]=i,s}return i}}var Xl=function(e){e=e.replace(/\r\n?/g,`
`);let t=new tA("!xml"),A=t,n="",i="";for(let s=0;s<e.length;s++)if(e[s]==="<")if(e[s+1]==="/"){let r=wt(e,">",s,"Closing Tag is not closed."),a=e.substring(s+2,r).trim();if(this.options.removeNSPrefix){let c=a.indexOf(":");c!==-1&&(a=a.substr(c+1))}this.options.transformTagName&&(a=this.options.transformTagName(a)),A&&(n=this.saveTextToParentTag(n,A,i)),i=i.substr(0,i.lastIndexOf(".")),A=this.tagsNodeStack.pop(),n="",s=r}else if(e[s+1]==="?"){let r=Ai(e,s,!1,"?>");if(!r)throw new Error("Pi Tag is not closed.");if(n=this.saveTextToParentTag(n,A,i),!(this.options.ignoreDeclaration&&r.tagName==="?xml"||this.options.ignorePiTags)){let a=new tA(r.tagName);a.add(this.options.textNodeName,""),r.tagName!==r.tagExp&&r.attrExpPresent&&(a[":@"]=this.buildAttributesMap(r.tagExp,i)),A.addChild(a)}s=r.closeIndex+1}else if(e.substr(s+1,3)==="!--"){let r=wt(e,"-->",s+4,"Comment is not closed.");if(this.options.commentPropName){let a=e.substring(s+4,r-2);n=this.saveTextToParentTag(n,A,i),A.add(this.options.commentPropName,[{[this.options.textNodeName]:a}])}s=r}else if(e.substr(s+1,2)==="!D"){let r=Ll(e,s);this.docTypeEntities=r.entities,s=r.i}else if(e.substr(s+1,2)==="!["){let r=wt(e,"]]>",s,"CDATA is not closed.")-2,a=e.substring(s+9,r);if(n=this.saveTextToParentTag(n,A,i),this.options.cdataPropName)A.add(this.options.cdataPropName,[{[this.options.textNodeName]:a}]);else{let c=this.parseTextData(a,A.tagname,i,!0,!1,!0);c==null&&(c=""),A.add(this.options.textNodeName,c)}s=r+2}else{let r=Ai(e,s,this.options.removeNSPrefix),a=r.tagName,c=r.tagExp,l=r.attrExpPresent,d=r.closeIndex;this.options.transformTagName&&(a=this.options.transformTagName(a)),A&&n&&A.tagname!=="!xml"&&(n=this.saveTextToParentTag(n,A,i,!1)),a!==t.tagname&&(i+=i?"."+a:a);let f=A;if(f&&this.options.unpairedTags.indexOf(f.tagname)!==-1&&(A=this.tagsNodeStack.pop()),this.isItStopNode(this.options.stopNodes,i,a)){let u="";if(c.length>0&&c.lastIndexOf("/")===c.length-1)s=r.closeIndex;else if(this.options.unpairedTags.indexOf(a)!==-1)s=r.closeIndex;else{let B=this.readStopNodeData(e,a,d+1);if(!B)throw new Error(`Unexpected end of ${a}`);s=B.i,u=B.tagContent}let p=new tA(a);a!==c&&l&&(p[":@"]=this.buildAttributesMap(c,i)),u&&(u=this.parseTextData(u,a,i,!0,l,!0,!0)),i=i.substr(0,i.lastIndexOf(".")),p.add(this.options.textNodeName,u),A.addChild(p)}else{if(c.length>0&&c.lastIndexOf("/")===c.length-1){a[a.length-1]==="/"?(a=a.substr(0,a.length-1),c=a):c=c.substr(0,c.length-1),this.options.transformTagName&&(a=this.options.transformTagName(a));let u=new tA(a);a!==c&&l&&(u[":@"]=this.buildAttributesMap(c,i)),i=i.substr(0,i.lastIndexOf(".")),A.addChild(u)}else{let u=new tA(a);this.tagsNodeStack.push(A),a!==c&&l&&(u[":@"]=this.buildAttributesMap(c,i)),A.addChild(u),A=u}n="",s=d}}else n+=e[s];return t.child},Gl=function(e){if(this.options.processEntities){for(let t in this.docTypeEntities){let A=this.docTypeEntities[t];e=e.replace(A.regx,A.val)}for(let t in this.lastEntities){let A=this.lastEntities[t];e=e.replace(A.regex,A.val)}if(this.options.htmlEntities)for(let t in this.htmlEntities){let A=this.htmlEntities[t];e=e.replace(A.regex,A.val)}e=e.replace(this.ampEntity.regex,this.ampEntity.val)}return e};function Jl(e,t,A,n){return e&&(n===void 0&&(n=Object.keys(t.child).length===0),e=this.parseTextData(e,t.tagname,A,!1,t[":@"]?Object.keys(t[":@"]).length!==0:!1,n),e!==void 0&&e!==""&&t.add(this.options.textNodeName,e),e=""),e}function jl(e,t,A){let n="*."+A;for(let i in e){let s=e[i];if(n===s||t===s)return!0}return!1}function Wl(e,t,A=">"){let n,i="";for(let s=t;s<e.length;s++){let o=e[s];if(n)o===n&&(n="");else if(o==='"'||o==="'")n=o;else if(o===A[0])if(A[1]){if(e[s+1]===A[1])return{data:i,index:s}}else return{data:i,index:s};else o==="	"&&(o=" ");i+=o}}function wt(e,t,A,n){let i=e.indexOf(t,A);if(i===-1)throw new Error(n);return i+t.length-1}function Ai(e,t,A,n=">"){let i=Wl(e,t+1,n);if(!i)return;let s=i.data,o=i.index,r=s.search(/\s/),a=s,c=!0;if(r!==-1&&(a=s.substr(0,r).replace(/\s\s*$/,""),s=s.substr(r+1)),A){let l=a.indexOf(":");l!==-1&&(a=a.substr(l+1),c=a!==i.data.substr(l+1))}return{tagName:a,tagExp:s,closeIndex:o,attrExpPresent:c}}function ql(e,t,A){let n=A,i=1;for(;A<e.length;A++)if(e[A]==="<")if(e[A+1]==="/"){let s=wt(e,">",A,`${t} is not closed`);if(e.substring(A+2,s).trim()===t&&(i--,i===0))return{tagContent:e.substring(n,A),i:s};A=s}else if(e[A+1]==="?")A=wt(e,"?>",A+1,"StopNode is not closed.");else if(e.substr(A+1,3)==="!--")A=wt(e,"-->",A+3,"StopNode is not closed.");else if(e.substr(A+1,2)==="![")A=wt(e,"]]>",A,"StopNode is not closed.")-2;else{let s=Ai(e,A,">");s&&((s&&s.tagName)===t&&s.tagExp[s.tagExp.length-1]!=="/"&&i++,A=s.closeIndex)}}function ni(e,t,A){if(t&&typeof e=="string"){let n=e.trim();return n==="true"?!0:n==="false"?!1:Ml(e,A)}else return ii.isExist(e)?e:""}qs.exports=ti});var eo=I(Ys=>{"use strict";function $l(e,t){return Zs(e,t)}function Zs(e,t,A){let n,i={};for(let s=0;s<e.length;s++){let o=e[s],r=Zl(o),a="";if(A===void 0?a=r:a=A+"."+r,r===t.textNodeName)n===void 0?n=o[r]:n+=""+o[r];else{if(r===void 0)continue;if(o[r]){let c=Zs(o[r],t,a),l=ep(c,t);o[":@"]?Yl(c,o[":@"],a,t):Object.keys(c).length===1&&c[t.textNodeName]!==void 0&&!t.alwaysCreateTextNode?c=c[t.textNodeName]:Object.keys(c).length===0&&(t.alwaysCreateTextNode?c[t.textNodeName]="":c=""),i[r]!==void 0&&i.hasOwnProperty(r)?(Array.isArray(i[r])||(i[r]=[i[r]]),i[r].push(c)):t.isArray(r,a,l)?i[r]=[c]:i[r]=c}}}return typeof n=="string"?n.length>0&&(i[t.textNodeName]=n):n!==void 0&&(i[t.textNodeName]=n),i}function Zl(e){let t=Object.keys(e);for(let A=0;A<t.length;A++){let n=t[A];if(n!==":@")return n}}function Yl(e,t,A,n){if(t){let i=Object.keys(t),s=i.length;for(let o=0;o<s;o++){let r=i[o];n.isArray(r,A+"."+r,!0,!0)?e[r]=[t[r]]:e[r]=t[r]}}}function ep(e,t){let A=Object.keys(e).length;return!!(A===0||A===1&&e[t.textNodeName])}Ys.prettify=$l});var Ao=I((Gg,to)=>{var{buildOptions:tp}=ks(),Ap=$s(),{prettify:np}=eo(),ip=Zn(),si=class{constructor(t){this.externalEntities={},this.options=tp(t)}parse(t,A){if(typeof t!="string")if(t.toString)t=t.toString();else throw new Error("XML data is accepted in String or Bytes[] form.");if(A){A===!0&&(A={});let s=ip.validate(t,A);if(s!==!0)throw Error(`${s.err.msg}:${s.err.line}:${s.err.col}`)}let n=new Ap(this.options);n.addExternalEntities(this.externalEntities);let i=n.parseXml(t);return this.options.preserveOrder||i===void 0?i:np(i,this.options)}addEntity(t,A){if(A.indexOf("&")!==-1)throw new Error("Entity value can't have '&'");if(t.indexOf("&")!==-1||t.indexOf(";")!==-1)throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if(A==="&")throw new Error("An entity with value '&' is not permitted");this.externalEntities[t]=A}};to.exports=si});var ao=I((Jg,oo)=>{var sp=`
`;function op(e,t){let A="";return t.format&&t.indentBy.length>0&&(A=sp),io(e,t,"",A)}function io(e,t,A,n){let i="",s=!1;for(let o=0;o<e.length;o++){let r=e[o],a=ap(r),c="";if(A.length===0?c=a:c=`${A}.${a}`,a===t.textNodeName){let p=r[a];rp(c,t)||(p=t.tagValueProcessor(a,p),p=so(p,t)),s&&(i+=n),i+=p,s=!1;continue}else if(a===t.cdataPropName){s&&(i+=n),i+=`<![CDATA[${r[a][0][t.textNodeName]}]]>`,s=!1;continue}else if(a===t.commentPropName){i+=n+`<!--${r[a][0][t.textNodeName]}-->`,s=!0;continue}else if(a[0]==="?"){let p=no(r[":@"],t),B=a==="?xml"?"":n,w=r[a][0][t.textNodeName];w=w.length!==0?" "+w:"",i+=B+`<${a}${w}${p}?>`,s=!0;continue}let l=n;l!==""&&(l+=t.indentBy);let d=no(r[":@"],t),f=n+`<${a}${d}`,u=io(r[a],t,c,l);t.unpairedTags.indexOf(a)!==-1?t.suppressUnpairedNode?i+=f+">":i+=f+"/>":(!u||u.length===0)&&t.suppressEmptyNode?i+=f+"/>":u&&u.endsWith(">")?i+=f+`>${u}${n}</${a}>`:(i+=f+">",u&&n!==""&&(u.includes("/>")||u.includes("</"))?i+=n+t.indentBy+u+n:i+=u,i+=`</${a}>`),s=!0}return i}function ap(e){let t=Object.keys(e);for(let A=0;A<t.length;A++){let n=t[A];if(n!==":@")return n}}function no(e,t){let A="";if(e&&!t.ignoreAttributes)for(let n in e){let i=t.attributeValueProcessor(n,e[n]);i=so(i,t),i===!0&&t.suppressBooleanAttributes?A+=` ${n.substr(t.attributeNamePrefix.length)}`:A+=` ${n.substr(t.attributeNamePrefix.length)}="${i}"`}return A}function rp(e,t){e=e.substr(0,e.length-t.textNodeName.length-1);let A=e.substr(e.lastIndexOf(".")+1);for(let n in t.stopNodes)if(t.stopNodes[n]===e||t.stopNodes[n]==="*."+A)return!0;return!1}function so(e,t){if(e&&e.length>0&&t.processEntities)for(let A=0;A<t.entities.length;A++){let n=t.entities[A];e=e.replace(n.regex,n.val)}return e}oo.exports=op});var co=I((jg,ro)=>{"use strict";var cp=ao(),lp={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:new RegExp("&","g"),val:"&amp;"},{regex:new RegExp(">","g"),val:"&gt;"},{regex:new RegExp("<","g"),val:"&lt;"},{regex:new RegExp("'","g"),val:"&apos;"},{regex:new RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[]};function tt(e){this.options=Object.assign({},lp,e),this.options.ignoreAttributes||this.options.attributesGroupName?this.isAttribute=function(){return!1}:(this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=dp),this.processTextOrObjNode=pp,this.options.format?(this.indentate=up,this.tagEndChar=`>
`,this.newLine=`
`):(this.indentate=function(){return""},this.tagEndChar=">",this.newLine="")}tt.prototype.build=function(e){return this.options.preserveOrder?cp(e,this.options):(Array.isArray(e)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1&&(e={[this.options.arrayNodeName]:e}),this.j2x(e,0).val)};tt.prototype.j2x=function(e,t){let A="",n="";for(let i in e)if(!(typeof e[i]>"u"))if(e[i]===null)i[0]==="?"?n+=this.indentate(t)+"<"+i+"?"+this.tagEndChar:n+=this.indentate(t)+"<"+i+"/"+this.tagEndChar;else if(e[i]instanceof Date)n+=this.buildTextValNode(e[i],i,"",t);else if(typeof e[i]!="object"){let s=this.isAttribute(i);if(s)A+=this.buildAttrPairStr(s,""+e[i]);else if(i===this.options.textNodeName){let o=this.options.tagValueProcessor(i,""+e[i]);n+=this.replaceEntitiesValue(o)}else n+=this.buildTextValNode(e[i],i,"",t)}else if(Array.isArray(e[i])){let s=e[i].length;for(let o=0;o<s;o++){let r=e[i][o];typeof r>"u"||(r===null?i[0]==="?"?n+=this.indentate(t)+"<"+i+"?"+this.tagEndChar:n+=this.indentate(t)+"<"+i+"/"+this.tagEndChar:typeof r=="object"?n+=this.processTextOrObjNode(r,i,t):n+=this.buildTextValNode(r,i,"",t))}}else if(this.options.attributesGroupName&&i===this.options.attributesGroupName){let s=Object.keys(e[i]),o=s.length;for(let r=0;r<o;r++)A+=this.buildAttrPairStr(s[r],""+e[i][s[r]])}else n+=this.processTextOrObjNode(e[i],i,t);return{attrStr:A,val:n}};tt.prototype.buildAttrPairStr=function(e,t){return t=this.options.attributeValueProcessor(e,""+t),t=this.replaceEntitiesValue(t),this.options.suppressBooleanAttributes&&t==="true"?" "+e:" "+e+'="'+t+'"'};function pp(e,t,A){let n=this.j2x(e,A+1);return e[this.options.textNodeName]!==void 0&&Object.keys(e).length===1?this.buildTextValNode(e[this.options.textNodeName],t,n.attrStr,A):this.buildObjectNode(n.val,t,n.attrStr,A)}tt.prototype.buildObjectNode=function(e,t,A,n){if(e==="")return t[0]==="?"?this.indentate(n)+"<"+t+A+"?"+this.tagEndChar:this.indentate(n)+"<"+t+A+this.closeTag(t)+this.tagEndChar;{let i="</"+t+this.tagEndChar,s="";return t[0]==="?"&&(s="?",i=""),A&&e.indexOf("<")===-1?this.indentate(n)+"<"+t+A+s+">"+e+i:this.options.commentPropName!==!1&&t===this.options.commentPropName&&s.length===0?this.indentate(n)+`<!--${e}-->`+this.newLine:this.indentate(n)+"<"+t+A+s+this.tagEndChar+e+this.indentate(n)+i}};tt.prototype.closeTag=function(e){let t="";return this.options.unpairedTags.indexOf(e)!==-1?this.options.suppressUnpairedNode||(t="/"):this.options.suppressEmptyNode?t="/":t=`></${e}`,t};tt.prototype.buildTextValNode=function(e,t,A,n){if(this.options.cdataPropName!==!1&&t===this.options.cdataPropName)return this.indentate(n)+`<![CDATA[${e}]]>`+this.newLine;if(this.options.commentPropName!==!1&&t===this.options.commentPropName)return this.indentate(n)+`<!--${e}-->`+this.newLine;if(t[0]==="?")return this.indentate(n)+"<"+t+A+"?"+this.tagEndChar;{let i=this.options.tagValueProcessor(t,e);return i=this.replaceEntitiesValue(i),i===""?this.indentate(n)+"<"+t+A+this.closeTag(t)+this.tagEndChar:this.indentate(n)+"<"+t+A+">"+i+"</"+t+this.tagEndChar}};tt.prototype.replaceEntitiesValue=function(e){if(e&&e.length>0&&this.options.processEntities)for(let t=0;t<this.options.entities.length;t++){let A=this.options.entities[t];e=e.replace(A.regex,A.val)}return e};function up(e){return this.options.indentBy.repeat(e)}function dp(e){return e.startsWith(this.options.attributeNamePrefix)?e.substr(this.attrPrefixLen):!1}ro.exports=tt});var oi=I((Wg,lo)=>{"use strict";var fp=Zn(),mp=Ao(),Bp=co();lo.exports={XMLParser:mp,XMLValidator:fp,XMLBuilder:Bp}});var ur=I((ix,pr)=>{var lr=require("stream").Stream,IB=require("util");pr.exports=Ke;function Ke(){this.source=null,this.dataSize=0,this.maxDataSize=1024*1024,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}IB.inherits(Ke,lr);Ke.create=function(e,t){var A=new this;t=t||{};for(var n in t)A[n]=t[n];A.source=e;var i=e.emit;return e.emit=function(){return A._handleEmit(arguments),i.apply(e,arguments)},e.on("error",function(){}),A.pauseStream&&e.pause(),A};Object.defineProperty(Ke.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}});Ke.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)};Ke.prototype.resume=function(){this._released||this.release(),this.source.resume()};Ke.prototype.pause=function(){this.source.pause()};Ke.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach(function(e){this.emit.apply(this,e)}.bind(this)),this._bufferedEvents=[]};Ke.prototype.pipe=function(){var e=lr.prototype.pipe.apply(this,arguments);return this.resume(),e};Ke.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}e[0]==="data"&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)};Ke.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",new Error(e))}}});var Br=I((sx,mr)=>{var OB=require("util"),fr=require("stream").Stream,dr=ur();mr.exports=J;function J(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2*1024*1024,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}OB.inherits(J,fr);J.create=function(e){var t=new this;e=e||{};for(var A in e)t[A]=e[A];return t};J.isStreamLike=function(e){return typeof e!="function"&&typeof e!="string"&&typeof e!="boolean"&&typeof e!="number"&&!Buffer.isBuffer(e)};J.prototype.append=function(e){var t=J.isStreamLike(e);if(t){if(!(e instanceof dr)){var A=dr.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=A}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this};J.prototype.pipe=function(e,t){return fr.prototype.pipe.call(this,e,t),this.resume(),e};J.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}};J.prototype._realGetNext=function(){var e=this._streams.shift();if(typeof e>"u"){this.end();return}if(typeof e!="function"){this._pipeNext(e);return}var t=e;t(function(A){var n=J.isStreamLike(A);n&&(A.on("data",this._checkDataSize.bind(this)),this._handleErrors(A)),this._pipeNext(A)}.bind(this))};J.prototype._pipeNext=function(e){this._currentStream=e;var t=J.isStreamLike(e);if(t){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}var A=e;this.write(A),this._getNext()};J.prototype._handleErrors=function(e){var t=this;e.on("error",function(A){t._emitError(A)})};J.prototype.write=function(e){this.emit("data",e)};J.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&typeof this._currentStream.pause=="function"&&this._currentStream.pause(),this.emit("pause"))};J.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&typeof this._currentStream.resume=="function"&&this._currentStream.resume(),this.emit("resume")};J.prototype.end=function(){this._reset(),this.emit("end")};J.prototype.destroy=function(){this._reset(),this.emit("close")};J.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null};J.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(new Error(e))}};J.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(t){t.dataSize&&(e.dataSize+=t.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)};J.prototype._emitError=function(e){this._reset(),this.emit("error",e)}});var hr=I((ox,_B)=>{_B.exports={"application/1d-interleaved-parityfec":{source:"iana"},"application/3gpdash-qoe-report+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/3gpp-ims+xml":{source:"iana",compressible:!0},"application/3gpphal+json":{source:"iana",compressible:!0},"application/3gpphalforms+json":{source:"iana",compressible:!0},"application/a2l":{source:"iana"},"application/ace+cbor":{source:"iana"},"application/activemessage":{source:"iana"},"application/activity+json":{source:"iana",compressible:!0},"application/alto-costmap+json":{source:"iana",compressible:!0},"application/alto-costmapfilter+json":{source:"iana",compressible:!0},"application/alto-directory+json":{source:"iana",compressible:!0},"application/alto-endpointcost+json":{source:"iana",compressible:!0},"application/alto-endpointcostparams+json":{source:"iana",compressible:!0},"application/alto-endpointprop+json":{source:"iana",compressible:!0},"application/alto-endpointpropparams+json":{source:"iana",compressible:!0},"application/alto-error+json":{source:"iana",compressible:!0},"application/alto-networkmap+json":{source:"iana",compressible:!0},"application/alto-networkmapfilter+json":{source:"iana",compressible:!0},"application/alto-updatestreamcontrol+json":{source:"iana",compressible:!0},"application/alto-updatestreamparams+json":{source:"iana",compressible:!0},"application/aml":{source:"iana"},"application/andrew-inset":{source:"iana",extensions:["ez"]},"application/applefile":{source:"iana"},"application/applixware":{source:"apache",extensions:["aw"]},"application/at+jwt":{source:"iana"},"application/atf":{source:"iana"},"application/atfx":{source:"iana"},"application/atom+xml":{source:"iana",compressible:!0,extensions:["atom"]},"application/atomcat+xml":{source:"iana",compressible:!0,extensions:["atomcat"]},"application/atomdeleted+xml":{source:"iana",compressible:!0,extensions:["atomdeleted"]},"application/atomicmail":{source:"iana"},"application/atomsvc+xml":{source:"iana",compressible:!0,extensions:["atomsvc"]},"application/atsc-dwd+xml":{source:"iana",compressible:!0,extensions:["dwd"]},"application/atsc-dynamic-event-message":{source:"iana"},"application/atsc-held+xml":{source:"iana",compressible:!0,extensions:["held"]},"application/atsc-rdt+json":{source:"iana",compressible:!0},"application/atsc-rsat+xml":{source:"iana",compressible:!0,extensions:["rsat"]},"application/atxml":{source:"iana"},"application/auth-policy+xml":{source:"iana",compressible:!0},"application/bacnet-xdd+zip":{source:"iana",compressible:!1},"application/batch-smtp":{source:"iana"},"application/bdoc":{compressible:!1,extensions:["bdoc"]},"application/beep+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/calendar+json":{source:"iana",compressible:!0},"application/calendar+xml":{source:"iana",compressible:!0,extensions:["xcs"]},"application/call-completion":{source:"iana"},"application/cals-1840":{source:"iana"},"application/captive+json":{source:"iana",compressible:!0},"application/cbor":{source:"iana"},"application/cbor-seq":{source:"iana"},"application/cccex":{source:"iana"},"application/ccmp+xml":{source:"iana",compressible:!0},"application/ccxml+xml":{source:"iana",compressible:!0,extensions:["ccxml"]},"application/cdfx+xml":{source:"iana",compressible:!0,extensions:["cdfx"]},"application/cdmi-capability":{source:"iana",extensions:["cdmia"]},"application/cdmi-container":{source:"iana",extensions:["cdmic"]},"application/cdmi-domain":{source:"iana",extensions:["cdmid"]},"application/cdmi-object":{source:"iana",extensions:["cdmio"]},"application/cdmi-queue":{source:"iana",extensions:["cdmiq"]},"application/cdni":{source:"iana"},"application/cea":{source:"iana"},"application/cea-2018+xml":{source:"iana",compressible:!0},"application/cellml+xml":{source:"iana",compressible:!0},"application/cfw":{source:"iana"},"application/city+json":{source:"iana",compressible:!0},"application/clr":{source:"iana"},"application/clue+xml":{source:"iana",compressible:!0},"application/clue_info+xml":{source:"iana",compressible:!0},"application/cms":{source:"iana"},"application/cnrp+xml":{source:"iana",compressible:!0},"application/coap-group+json":{source:"iana",compressible:!0},"application/coap-payload":{source:"iana"},"application/commonground":{source:"iana"},"application/conference-info+xml":{source:"iana",compressible:!0},"application/cose":{source:"iana"},"application/cose-key":{source:"iana"},"application/cose-key-set":{source:"iana"},"application/cpl+xml":{source:"iana",compressible:!0,extensions:["cpl"]},"application/csrattrs":{source:"iana"},"application/csta+xml":{source:"iana",compressible:!0},"application/cstadata+xml":{source:"iana",compressible:!0},"application/csvm+json":{source:"iana",compressible:!0},"application/cu-seeme":{source:"apache",extensions:["cu"]},"application/cwt":{source:"iana"},"application/cybercash":{source:"iana"},"application/dart":{compressible:!0},"application/dash+xml":{source:"iana",compressible:!0,extensions:["mpd"]},"application/dash-patch+xml":{source:"iana",compressible:!0,extensions:["mpp"]},"application/dashdelta":{source:"iana"},"application/davmount+xml":{source:"iana",compressible:!0,extensions:["davmount"]},"application/dca-rft":{source:"iana"},"application/dcd":{source:"iana"},"application/dec-dx":{source:"iana"},"application/dialog-info+xml":{source:"iana",compressible:!0},"application/dicom":{source:"iana"},"application/dicom+json":{source:"iana",compressible:!0},"application/dicom+xml":{source:"iana",compressible:!0},"application/dii":{source:"iana"},"application/dit":{source:"iana"},"application/dns":{source:"iana"},"application/dns+json":{source:"iana",compressible:!0},"application/dns-message":{source:"iana"},"application/docbook+xml":{source:"apache",compressible:!0,extensions:["dbk"]},"application/dots+cbor":{source:"iana"},"application/dskpp+xml":{source:"iana",compressible:!0},"application/dssc+der":{source:"iana",extensions:["dssc"]},"application/dssc+xml":{source:"iana",compressible:!0,extensions:["xdssc"]},"application/dvcs":{source:"iana"},"application/ecmascript":{source:"iana",compressible:!0,extensions:["es","ecma"]},"application/edi-consent":{source:"iana"},"application/edi-x12":{source:"iana",compressible:!1},"application/edifact":{source:"iana",compressible:!1},"application/efi":{source:"iana"},"application/elm+json":{source:"iana",charset:"UTF-8",compressible:!0},"application/elm+xml":{source:"iana",compressible:!0},"application/emergencycalldata.cap+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/emergencycalldata.comment+xml":{source:"iana",compressible:!0},"application/emergencycalldata.control+xml":{source:"iana",compressible:!0},"application/emergencycalldata.deviceinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.ecall.msd":{source:"iana"},"application/emergencycalldata.providerinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.serviceinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.subscriberinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.veds+xml":{source:"iana",compressible:!0},"application/emma+xml":{source:"iana",compressible:!0,extensions:["emma"]},"application/emotionml+xml":{source:"iana",compressible:!0,extensions:["emotionml"]},"application/encaprtp":{source:"iana"},"application/epp+xml":{source:"iana",compressible:!0},"application/epub+zip":{source:"iana",compressible:!1,extensions:["epub"]},"application/eshop":{source:"iana"},"application/exi":{source:"iana",extensions:["exi"]},"application/expect-ct-report+json":{source:"iana",compressible:!0},"application/express":{source:"iana",extensions:["exp"]},"application/fastinfoset":{source:"iana"},"application/fastsoap":{source:"iana"},"application/fdt+xml":{source:"iana",compressible:!0,extensions:["fdt"]},"application/fhir+json":{source:"iana",charset:"UTF-8",compressible:!0},"application/fhir+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/fido.trusted-apps+json":{compressible:!0},"application/fits":{source:"iana"},"application/flexfec":{source:"iana"},"application/font-sfnt":{source:"iana"},"application/font-tdpfr":{source:"iana",extensions:["pfr"]},"application/font-woff":{source:"iana",compressible:!1},"application/framework-attributes+xml":{source:"iana",compressible:!0},"application/geo+json":{source:"iana",compressible:!0,extensions:["geojson"]},"application/geo+json-seq":{source:"iana"},"application/geopackage+sqlite3":{source:"iana"},"application/geoxacml+xml":{source:"iana",compressible:!0},"application/gltf-buffer":{source:"iana"},"application/gml+xml":{source:"iana",compressible:!0,extensions:["gml"]},"application/gpx+xml":{source:"apache",compressible:!0,extensions:["gpx"]},"application/gxf":{source:"apache",extensions:["gxf"]},"application/gzip":{source:"iana",compressible:!1,extensions:["gz"]},"application/h224":{source:"iana"},"application/held+xml":{source:"iana",compressible:!0},"application/hjson":{extensions:["hjson"]},"application/http":{source:"iana"},"application/hyperstudio":{source:"iana",extensions:["stk"]},"application/ibe-key-request+xml":{source:"iana",compressible:!0},"application/ibe-pkg-reply+xml":{source:"iana",compressible:!0},"application/ibe-pp-data":{source:"iana"},"application/iges":{source:"iana"},"application/im-iscomposing+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/index":{source:"iana"},"application/index.cmd":{source:"iana"},"application/index.obj":{source:"iana"},"application/index.response":{source:"iana"},"application/index.vnd":{source:"iana"},"application/inkml+xml":{source:"iana",compressible:!0,extensions:["ink","inkml"]},"application/iotp":{source:"iana"},"application/ipfix":{source:"iana",extensions:["ipfix"]},"application/ipp":{source:"iana"},"application/isup":{source:"iana"},"application/its+xml":{source:"iana",compressible:!0,extensions:["its"]},"application/java-archive":{source:"apache",compressible:!1,extensions:["jar","war","ear"]},"application/java-serialized-object":{source:"apache",compressible:!1,extensions:["ser"]},"application/java-vm":{source:"apache",compressible:!1,extensions:["class"]},"application/javascript":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["js","mjs"]},"application/jf2feed+json":{source:"iana",compressible:!0},"application/jose":{source:"iana"},"application/jose+json":{source:"iana",compressible:!0},"application/jrd+json":{source:"iana",compressible:!0},"application/jscalendar+json":{source:"iana",compressible:!0},"application/json":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["json","map"]},"application/json-patch+json":{source:"iana",compressible:!0},"application/json-seq":{source:"iana"},"application/json5":{extensions:["json5"]},"application/jsonml+json":{source:"apache",compressible:!0,extensions:["jsonml"]},"application/jwk+json":{source:"iana",compressible:!0},"application/jwk-set+json":{source:"iana",compressible:!0},"application/jwt":{source:"iana"},"application/kpml-request+xml":{source:"iana",compressible:!0},"application/kpml-response+xml":{source:"iana",compressible:!0},"application/ld+json":{source:"iana",compressible:!0,extensions:["jsonld"]},"application/lgr+xml":{source:"iana",compressible:!0,extensions:["lgr"]},"application/link-format":{source:"iana"},"application/load-control+xml":{source:"iana",compressible:!0},"application/lost+xml":{source:"iana",compressible:!0,extensions:["lostxml"]},"application/lostsync+xml":{source:"iana",compressible:!0},"application/lpf+zip":{source:"iana",compressible:!1},"application/lxf":{source:"iana"},"application/mac-binhex40":{source:"iana",extensions:["hqx"]},"application/mac-compactpro":{source:"apache",extensions:["cpt"]},"application/macwriteii":{source:"iana"},"application/mads+xml":{source:"iana",compressible:!0,extensions:["mads"]},"application/manifest+json":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["webmanifest"]},"application/marc":{source:"iana",extensions:["mrc"]},"application/marcxml+xml":{source:"iana",compressible:!0,extensions:["mrcx"]},"application/mathematica":{source:"iana",extensions:["ma","nb","mb"]},"application/mathml+xml":{source:"iana",compressible:!0,extensions:["mathml"]},"application/mathml-content+xml":{source:"iana",compressible:!0},"application/mathml-presentation+xml":{source:"iana",compressible:!0},"application/mbms-associated-procedure-description+xml":{source:"iana",compressible:!0},"application/mbms-deregister+xml":{source:"iana",compressible:!0},"application/mbms-envelope+xml":{source:"iana",compressible:!0},"application/mbms-msk+xml":{source:"iana",compressible:!0},"application/mbms-msk-response+xml":{source:"iana",compressible:!0},"application/mbms-protection-description+xml":{source:"iana",compressible:!0},"application/mbms-reception-report+xml":{source:"iana",compressible:!0},"application/mbms-register+xml":{source:"iana",compressible:!0},"application/mbms-register-response+xml":{source:"iana",compressible:!0},"application/mbms-schedule+xml":{source:"iana",compressible:!0},"application/mbms-user-service-description+xml":{source:"iana",compressible:!0},"application/mbox":{source:"iana",extensions:["mbox"]},"application/media-policy-dataset+xml":{source:"iana",compressible:!0,extensions:["mpf"]},"application/media_control+xml":{source:"iana",compressible:!0},"application/mediaservercontrol+xml":{source:"iana",compressible:!0,extensions:["mscml"]},"application/merge-patch+json":{source:"iana",compressible:!0},"application/metalink+xml":{source:"apache",compressible:!0,extensions:["metalink"]},"application/metalink4+xml":{source:"iana",compressible:!0,extensions:["meta4"]},"application/mets+xml":{source:"iana",compressible:!0,extensions:["mets"]},"application/mf4":{source:"iana"},"application/mikey":{source:"iana"},"application/mipc":{source:"iana"},"application/missing-blocks+cbor-seq":{source:"iana"},"application/mmt-aei+xml":{source:"iana",compressible:!0,extensions:["maei"]},"application/mmt-usd+xml":{source:"iana",compressible:!0,extensions:["musd"]},"application/mods+xml":{source:"iana",compressible:!0,extensions:["mods"]},"application/moss-keys":{source:"iana"},"application/moss-signature":{source:"iana"},"application/mosskey-data":{source:"iana"},"application/mosskey-request":{source:"iana"},"application/mp21":{source:"iana",extensions:["m21","mp21"]},"application/mp4":{source:"iana",extensions:["mp4s","m4p"]},"application/mpeg4-generic":{source:"iana"},"application/mpeg4-iod":{source:"iana"},"application/mpeg4-iod-xmt":{source:"iana"},"application/mrb-consumer+xml":{source:"iana",compressible:!0},"application/mrb-publish+xml":{source:"iana",compressible:!0},"application/msc-ivr+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/msc-mixer+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/msword":{source:"iana",compressible:!1,extensions:["doc","dot"]},"application/mud+json":{source:"iana",compressible:!0},"application/multipart-core":{source:"iana"},"application/mxf":{source:"iana",extensions:["mxf"]},"application/n-quads":{source:"iana",extensions:["nq"]},"application/n-triples":{source:"iana",extensions:["nt"]},"application/nasdata":{source:"iana"},"application/news-checkgroups":{source:"iana",charset:"US-ASCII"},"application/news-groupinfo":{source:"iana",charset:"US-ASCII"},"application/news-transmission":{source:"iana"},"application/nlsml+xml":{source:"iana",compressible:!0},"application/node":{source:"iana",extensions:["cjs"]},"application/nss":{source:"iana"},"application/oauth-authz-req+jwt":{source:"iana"},"application/oblivious-dns-message":{source:"iana"},"application/ocsp-request":{source:"iana"},"application/ocsp-response":{source:"iana"},"application/octet-stream":{source:"iana",compressible:!1,extensions:["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{source:"iana",extensions:["oda"]},"application/odm+xml":{source:"iana",compressible:!0},"application/odx":{source:"iana"},"application/oebps-package+xml":{source:"iana",compressible:!0,extensions:["opf"]},"application/ogg":{source:"iana",compressible:!1,extensions:["ogx"]},"application/omdoc+xml":{source:"apache",compressible:!0,extensions:["omdoc"]},"application/onenote":{source:"apache",extensions:["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{source:"iana",compressible:!0},"application/oscore":{source:"iana"},"application/oxps":{source:"iana",extensions:["oxps"]},"application/p21":{source:"iana"},"application/p21+zip":{source:"iana",compressible:!1},"application/p2p-overlay+xml":{source:"iana",compressible:!0,extensions:["relo"]},"application/parityfec":{source:"iana"},"application/passport":{source:"iana"},"application/patch-ops-error+xml":{source:"iana",compressible:!0,extensions:["xer"]},"application/pdf":{source:"iana",compressible:!1,extensions:["pdf"]},"application/pdx":{source:"iana"},"application/pem-certificate-chain":{source:"iana"},"application/pgp-encrypted":{source:"iana",compressible:!1,extensions:["pgp"]},"application/pgp-keys":{source:"iana",extensions:["asc"]},"application/pgp-signature":{source:"iana",extensions:["asc","sig"]},"application/pics-rules":{source:"apache",extensions:["prf"]},"application/pidf+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/pidf-diff+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/pkcs10":{source:"iana",extensions:["p10"]},"application/pkcs12":{source:"iana"},"application/pkcs7-mime":{source:"iana",extensions:["p7m","p7c"]},"application/pkcs7-signature":{source:"iana",extensions:["p7s"]},"application/pkcs8":{source:"iana",extensions:["p8"]},"application/pkcs8-encrypted":{source:"iana"},"application/pkix-attr-cert":{source:"iana",extensions:["ac"]},"application/pkix-cert":{source:"iana",extensions:["cer"]},"application/pkix-crl":{source:"iana",extensions:["crl"]},"application/pkix-pkipath":{source:"iana",extensions:["pkipath"]},"application/pkixcmp":{source:"iana",extensions:["pki"]},"application/pls+xml":{source:"iana",compressible:!0,extensions:["pls"]},"application/poc-settings+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/postscript":{source:"iana",compressible:!0,extensions:["ai","eps","ps"]},"application/ppsp-tracker+json":{source:"iana",compressible:!0},"application/problem+json":{source:"iana",compressible:!0},"application/problem+xml":{source:"iana",compressible:!0},"application/provenance+xml":{source:"iana",compressible:!0,extensions:["provx"]},"application/prs.alvestrand.titrax-sheet":{source:"iana"},"application/prs.cww":{source:"iana",extensions:["cww"]},"application/prs.cyn":{source:"iana",charset:"7-BIT"},"application/prs.hpub+zip":{source:"iana",compressible:!1},"application/prs.nprend":{source:"iana"},"application/prs.plucker":{source:"iana"},"application/prs.rdf-xml-crypt":{source:"iana"},"application/prs.xsf+xml":{source:"iana",compressible:!0},"application/pskc+xml":{source:"iana",compressible:!0,extensions:["pskcxml"]},"application/pvd+json":{source:"iana",compressible:!0},"application/qsig":{source:"iana"},"application/raml+yaml":{compressible:!0,extensions:["raml"]},"application/raptorfec":{source:"iana"},"application/rdap+json":{source:"iana",compressible:!0},"application/rdf+xml":{source:"iana",compressible:!0,extensions:["rdf","owl"]},"application/reginfo+xml":{source:"iana",compressible:!0,extensions:["rif"]},"application/relax-ng-compact-syntax":{source:"iana",extensions:["rnc"]},"application/remote-printing":{source:"iana"},"application/reputon+json":{source:"iana",compressible:!0},"application/resource-lists+xml":{source:"iana",compressible:!0,extensions:["rl"]},"application/resource-lists-diff+xml":{source:"iana",compressible:!0,extensions:["rld"]},"application/rfc+xml":{source:"iana",compressible:!0},"application/riscos":{source:"iana"},"application/rlmi+xml":{source:"iana",compressible:!0},"application/rls-services+xml":{source:"iana",compressible:!0,extensions:["rs"]},"application/route-apd+xml":{source:"iana",compressible:!0,extensions:["rapd"]},"application/route-s-tsid+xml":{source:"iana",compressible:!0,extensions:["sls"]},"application/route-usd+xml":{source:"iana",compressible:!0,extensions:["rusd"]},"application/rpki-ghostbusters":{source:"iana",extensions:["gbr"]},"application/rpki-manifest":{source:"iana",extensions:["mft"]},"application/rpki-publication":{source:"iana"},"application/rpki-roa":{source:"iana",extensions:["roa"]},"application/rpki-updown":{source:"iana"},"application/rsd+xml":{source:"apache",compressible:!0,extensions:["rsd"]},"application/rss+xml":{source:"apache",compressible:!0,extensions:["rss"]},"application/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"application/rtploopback":{source:"iana"},"application/rtx":{source:"iana"},"application/samlassertion+xml":{source:"iana",compressible:!0},"application/samlmetadata+xml":{source:"iana",compressible:!0},"application/sarif+json":{source:"iana",compressible:!0},"application/sarif-external-properties+json":{source:"iana",compressible:!0},"application/sbe":{source:"iana"},"application/sbml+xml":{source:"iana",compressible:!0,extensions:["sbml"]},"application/scaip+xml":{source:"iana",compressible:!0},"application/scim+json":{source:"iana",compressible:!0},"application/scvp-cv-request":{source:"iana",extensions:["scq"]},"application/scvp-cv-response":{source:"iana",extensions:["scs"]},"application/scvp-vp-request":{source:"iana",extensions:["spq"]},"application/scvp-vp-response":{source:"iana",extensions:["spp"]},"application/sdp":{source:"iana",extensions:["sdp"]},"application/secevent+jwt":{source:"iana"},"application/senml+cbor":{source:"iana"},"application/senml+json":{source:"iana",compressible:!0},"application/senml+xml":{source:"iana",compressible:!0,extensions:["senmlx"]},"application/senml-etch+cbor":{source:"iana"},"application/senml-etch+json":{source:"iana",compressible:!0},"application/senml-exi":{source:"iana"},"application/sensml+cbor":{source:"iana"},"application/sensml+json":{source:"iana",compressible:!0},"application/sensml+xml":{source:"iana",compressible:!0,extensions:["sensmlx"]},"application/sensml-exi":{source:"iana"},"application/sep+xml":{source:"iana",compressible:!0},"application/sep-exi":{source:"iana"},"application/session-info":{source:"iana"},"application/set-payment":{source:"iana"},"application/set-payment-initiation":{source:"iana",extensions:["setpay"]},"application/set-registration":{source:"iana"},"application/set-registration-initiation":{source:"iana",extensions:["setreg"]},"application/sgml":{source:"iana"},"application/sgml-open-catalog":{source:"iana"},"application/shf+xml":{source:"iana",compressible:!0,extensions:["shf"]},"application/sieve":{source:"iana",extensions:["siv","sieve"]},"application/simple-filter+xml":{source:"iana",compressible:!0},"application/simple-message-summary":{source:"iana"},"application/simplesymbolcontainer":{source:"iana"},"application/sipc":{source:"iana"},"application/slate":{source:"iana"},"application/smil":{source:"iana"},"application/smil+xml":{source:"iana",compressible:!0,extensions:["smi","smil"]},"application/smpte336m":{source:"iana"},"application/soap+fastinfoset":{source:"iana"},"application/soap+xml":{source:"iana",compressible:!0},"application/sparql-query":{source:"iana",extensions:["rq"]},"application/sparql-results+xml":{source:"iana",compressible:!0,extensions:["srx"]},"application/spdx+json":{source:"iana",compressible:!0},"application/spirits-event+xml":{source:"iana",compressible:!0},"application/sql":{source:"iana"},"application/srgs":{source:"iana",extensions:["gram"]},"application/srgs+xml":{source:"iana",compressible:!0,extensions:["grxml"]},"application/sru+xml":{source:"iana",compressible:!0,extensions:["sru"]},"application/ssdl+xml":{source:"apache",compressible:!0,extensions:["ssdl"]},"application/ssml+xml":{source:"iana",compressible:!0,extensions:["ssml"]},"application/stix+json":{source:"iana",compressible:!0},"application/swid+xml":{source:"iana",compressible:!0,extensions:["swidtag"]},"application/tamp-apex-update":{source:"iana"},"application/tamp-apex-update-confirm":{source:"iana"},"application/tamp-community-update":{source:"iana"},"application/tamp-community-update-confirm":{source:"iana"},"application/tamp-error":{source:"iana"},"application/tamp-sequence-adjust":{source:"iana"},"application/tamp-sequence-adjust-confirm":{source:"iana"},"application/tamp-status-query":{source:"iana"},"application/tamp-status-response":{source:"iana"},"application/tamp-update":{source:"iana"},"application/tamp-update-confirm":{source:"iana"},"application/tar":{compressible:!0},"application/taxii+json":{source:"iana",compressible:!0},"application/td+json":{source:"iana",compressible:!0},"application/tei+xml":{source:"iana",compressible:!0,extensions:["tei","teicorpus"]},"application/tetra_isi":{source:"iana"},"application/thraud+xml":{source:"iana",compressible:!0,extensions:["tfi"]},"application/timestamp-query":{source:"iana"},"application/timestamp-reply":{source:"iana"},"application/timestamped-data":{source:"iana",extensions:["tsd"]},"application/tlsrpt+gzip":{source:"iana"},"application/tlsrpt+json":{source:"iana",compressible:!0},"application/tnauthlist":{source:"iana"},"application/token-introspection+jwt":{source:"iana"},"application/toml":{compressible:!0,extensions:["toml"]},"application/trickle-ice-sdpfrag":{source:"iana"},"application/trig":{source:"iana",extensions:["trig"]},"application/ttml+xml":{source:"iana",compressible:!0,extensions:["ttml"]},"application/tve-trigger":{source:"iana"},"application/tzif":{source:"iana"},"application/tzif-leap":{source:"iana"},"application/ubjson":{compressible:!1,extensions:["ubj"]},"application/ulpfec":{source:"iana"},"application/urc-grpsheet+xml":{source:"iana",compressible:!0},"application/urc-ressheet+xml":{source:"iana",compressible:!0,extensions:["rsheet"]},"application/urc-targetdesc+xml":{source:"iana",compressible:!0,extensions:["td"]},"application/urc-uisocketdesc+xml":{source:"iana",compressible:!0},"application/vcard+json":{source:"iana",compressible:!0},"application/vcard+xml":{source:"iana",compressible:!0},"application/vemmi":{source:"iana"},"application/vividence.scriptfile":{source:"apache"},"application/vnd.1000minds.decision-model+xml":{source:"iana",compressible:!0,extensions:["1km"]},"application/vnd.3gpp-prose+xml":{source:"iana",compressible:!0},"application/vnd.3gpp-prose-pc3ch+xml":{source:"iana",compressible:!0},"application/vnd.3gpp-v2x-local-service-information":{source:"iana"},"application/vnd.3gpp.5gnas":{source:"iana"},"application/vnd.3gpp.access-transfer-events+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.bsf+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.gmop+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.gtpc":{source:"iana"},"application/vnd.3gpp.interworking-data":{source:"iana"},"application/vnd.3gpp.lpp":{source:"iana"},"application/vnd.3gpp.mc-signalling-ear":{source:"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-payload":{source:"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-signalling":{source:"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-floor-request+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-location-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-signed+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-ue-init-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-location-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-transmission-request+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mid-call+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.ngap":{source:"iana"},"application/vnd.3gpp.pfcp":{source:"iana"},"application/vnd.3gpp.pic-bw-large":{source:"iana",extensions:["plb"]},"application/vnd.3gpp.pic-bw-small":{source:"iana",extensions:["psb"]},"application/vnd.3gpp.pic-bw-var":{source:"iana",extensions:["pvb"]},"application/vnd.3gpp.s1ap":{source:"iana"},"application/vnd.3gpp.sms":{source:"iana"},"application/vnd.3gpp.sms+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.srvcc-ext+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.srvcc-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.state-and-event-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.ussd+xml":{source:"iana",compressible:!0},"application/vnd.3gpp2.bcmcsinfo+xml":{source:"iana",compressible:!0},"application/vnd.3gpp2.sms":{source:"iana"},"application/vnd.3gpp2.tcap":{source:"iana",extensions:["tcap"]},"application/vnd.3lightssoftware.imagescal":{source:"iana"},"application/vnd.3m.post-it-notes":{source:"iana",extensions:["pwn"]},"application/vnd.accpac.simply.aso":{source:"iana",extensions:["aso"]},"application/vnd.accpac.simply.imp":{source:"iana",extensions:["imp"]},"application/vnd.acucobol":{source:"iana",extensions:["acu"]},"application/vnd.acucorp":{source:"iana",extensions:["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{source:"apache",compressible:!1,extensions:["air"]},"application/vnd.adobe.flash.movie":{source:"iana"},"application/vnd.adobe.formscentral.fcdt":{source:"iana",extensions:["fcdt"]},"application/vnd.adobe.fxp":{source:"iana",extensions:["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{source:"iana"},"application/vnd.adobe.xdp+xml":{source:"iana",compressible:!0,extensions:["xdp"]},"application/vnd.adobe.xfdf":{source:"iana",extensions:["xfdf"]},"application/vnd.aether.imp":{source:"iana"},"application/vnd.afpc.afplinedata":{source:"iana"},"application/vnd.afpc.afplinedata-pagedef":{source:"iana"},"application/vnd.afpc.cmoca-cmresource":{source:"iana"},"application/vnd.afpc.foca-charset":{source:"iana"},"application/vnd.afpc.foca-codedfont":{source:"iana"},"application/vnd.afpc.foca-codepage":{source:"iana"},"application/vnd.afpc.modca":{source:"iana"},"application/vnd.afpc.modca-cmtable":{source:"iana"},"application/vnd.afpc.modca-formdef":{source:"iana"},"application/vnd.afpc.modca-mediummap":{source:"iana"},"application/vnd.afpc.modca-objectcontainer":{source:"iana"},"application/vnd.afpc.modca-overlay":{source:"iana"},"application/vnd.afpc.modca-pagesegment":{source:"iana"},"application/vnd.age":{source:"iana",extensions:["age"]},"application/vnd.ah-barcode":{source:"iana"},"application/vnd.ahead.space":{source:"iana",extensions:["ahead"]},"application/vnd.airzip.filesecure.azf":{source:"iana",extensions:["azf"]},"application/vnd.airzip.filesecure.azs":{source:"iana",extensions:["azs"]},"application/vnd.amadeus+json":{source:"iana",compressible:!0},"application/vnd.amazon.ebook":{source:"apache",extensions:["azw"]},"application/vnd.amazon.mobi8-ebook":{source:"iana"},"application/vnd.americandynamics.acc":{source:"iana",extensions:["acc"]},"application/vnd.amiga.ami":{source:"iana",extensions:["ami"]},"application/vnd.amundsen.maze+xml":{source:"iana",compressible:!0},"application/vnd.android.ota":{source:"iana"},"application/vnd.android.package-archive":{source:"apache",compressible:!1,extensions:["apk"]},"application/vnd.anki":{source:"iana"},"application/vnd.anser-web-certificate-issue-initiation":{source:"iana",extensions:["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{source:"apache",extensions:["fti"]},"application/vnd.antix.game-component":{source:"iana",extensions:["atx"]},"application/vnd.apache.arrow.file":{source:"iana"},"application/vnd.apache.arrow.stream":{source:"iana"},"application/vnd.apache.thrift.binary":{source:"iana"},"application/vnd.apache.thrift.compact":{source:"iana"},"application/vnd.apache.thrift.json":{source:"iana"},"application/vnd.api+json":{source:"iana",compressible:!0},"application/vnd.aplextor.warrp+json":{source:"iana",compressible:!0},"application/vnd.apothekende.reservation+json":{source:"iana",compressible:!0},"application/vnd.apple.installer+xml":{source:"iana",compressible:!0,extensions:["mpkg"]},"application/vnd.apple.keynote":{source:"iana",extensions:["key"]},"application/vnd.apple.mpegurl":{source:"iana",extensions:["m3u8"]},"application/vnd.apple.numbers":{source:"iana",extensions:["numbers"]},"application/vnd.apple.pages":{source:"iana",extensions:["pages"]},"application/vnd.apple.pkpass":{compressible:!1,extensions:["pkpass"]},"application/vnd.arastra.swi":{source:"iana"},"application/vnd.aristanetworks.swi":{source:"iana",extensions:["swi"]},"application/vnd.artisan+json":{source:"iana",compressible:!0},"application/vnd.artsquare":{source:"iana"},"application/vnd.astraea-software.iota":{source:"iana",extensions:["iota"]},"application/vnd.audiograph":{source:"iana",extensions:["aep"]},"application/vnd.autopackage":{source:"iana"},"application/vnd.avalon+json":{source:"iana",compressible:!0},"application/vnd.avistar+xml":{source:"iana",compressible:!0},"application/vnd.balsamiq.bmml+xml":{source:"iana",compressible:!0,extensions:["bmml"]},"application/vnd.balsamiq.bmpr":{source:"iana"},"application/vnd.banana-accounting":{source:"iana"},"application/vnd.bbf.usp.error":{source:"iana"},"application/vnd.bbf.usp.msg":{source:"iana"},"application/vnd.bbf.usp.msg+json":{source:"iana",compressible:!0},"application/vnd.bekitzur-stech+json":{source:"iana",compressible:!0},"application/vnd.bint.med-content":{source:"iana"},"application/vnd.biopax.rdf+xml":{source:"iana",compressible:!0},"application/vnd.blink-idb-value-wrapper":{source:"iana"},"application/vnd.blueice.multipass":{source:"iana",extensions:["mpm"]},"application/vnd.bluetooth.ep.oob":{source:"iana"},"application/vnd.bluetooth.le.oob":{source:"iana"},"application/vnd.bmi":{source:"iana",extensions:["bmi"]},"application/vnd.bpf":{source:"iana"},"application/vnd.bpf3":{source:"iana"},"application/vnd.businessobjects":{source:"iana",extensions:["rep"]},"application/vnd.byu.uapi+json":{source:"iana",compressible:!0},"application/vnd.cab-jscript":{source:"iana"},"application/vnd.canon-cpdl":{source:"iana"},"application/vnd.canon-lips":{source:"iana"},"application/vnd.capasystems-pg+json":{source:"iana",compressible:!0},"application/vnd.cendio.thinlinc.clientconf":{source:"iana"},"application/vnd.century-systems.tcp_stream":{source:"iana"},"application/vnd.chemdraw+xml":{source:"iana",compressible:!0,extensions:["cdxml"]},"application/vnd.chess-pgn":{source:"iana"},"application/vnd.chipnuts.karaoke-mmd":{source:"iana",extensions:["mmd"]},"application/vnd.ciedi":{source:"iana"},"application/vnd.cinderella":{source:"iana",extensions:["cdy"]},"application/vnd.cirpack.isdn-ext":{source:"iana"},"application/vnd.citationstyles.style+xml":{source:"iana",compressible:!0,extensions:["csl"]},"application/vnd.claymore":{source:"iana",extensions:["cla"]},"application/vnd.cloanto.rp9":{source:"iana",extensions:["rp9"]},"application/vnd.clonk.c4group":{source:"iana",extensions:["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{source:"iana",extensions:["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{source:"iana",extensions:["c11amz"]},"application/vnd.coffeescript":{source:"iana"},"application/vnd.collabio.xodocuments.document":{source:"iana"},"application/vnd.collabio.xodocuments.document-template":{source:"iana"},"application/vnd.collabio.xodocuments.presentation":{source:"iana"},"application/vnd.collabio.xodocuments.presentation-template":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{source:"iana"},"application/vnd.collection+json":{source:"iana",compressible:!0},"application/vnd.collection.doc+json":{source:"iana",compressible:!0},"application/vnd.collection.next+json":{source:"iana",compressible:!0},"application/vnd.comicbook+zip":{source:"iana",compressible:!1},"application/vnd.comicbook-rar":{source:"iana"},"application/vnd.commerce-battelle":{source:"iana"},"application/vnd.commonspace":{source:"iana",extensions:["csp"]},"application/vnd.contact.cmsg":{source:"iana",extensions:["cdbcmsg"]},"application/vnd.coreos.ignition+json":{source:"iana",compressible:!0},"application/vnd.cosmocaller":{source:"iana",extensions:["cmc"]},"application/vnd.crick.clicker":{source:"iana",extensions:["clkx"]},"application/vnd.crick.clicker.keyboard":{source:"iana",extensions:["clkk"]},"application/vnd.crick.clicker.palette":{source:"iana",extensions:["clkp"]},"application/vnd.crick.clicker.template":{source:"iana",extensions:["clkt"]},"application/vnd.crick.clicker.wordbank":{source:"iana",extensions:["clkw"]},"application/vnd.criticaltools.wbs+xml":{source:"iana",compressible:!0,extensions:["wbs"]},"application/vnd.cryptii.pipe+json":{source:"iana",compressible:!0},"application/vnd.crypto-shade-file":{source:"iana"},"application/vnd.cryptomator.encrypted":{source:"iana"},"application/vnd.cryptomator.vault":{source:"iana"},"application/vnd.ctc-posml":{source:"iana",extensions:["pml"]},"application/vnd.ctct.ws+xml":{source:"iana",compressible:!0},"application/vnd.cups-pdf":{source:"iana"},"application/vnd.cups-postscript":{source:"iana"},"application/vnd.cups-ppd":{source:"iana",extensions:["ppd"]},"application/vnd.cups-raster":{source:"iana"},"application/vnd.cups-raw":{source:"iana"},"application/vnd.curl":{source:"iana"},"application/vnd.curl.car":{source:"apache",extensions:["car"]},"application/vnd.curl.pcurl":{source:"apache",extensions:["pcurl"]},"application/vnd.cyan.dean.root+xml":{source:"iana",compressible:!0},"application/vnd.cybank":{source:"iana"},"application/vnd.cyclonedx+json":{source:"iana",compressible:!0},"application/vnd.cyclonedx+xml":{source:"iana",compressible:!0},"application/vnd.d2l.coursepackage1p0+zip":{source:"iana",compressible:!1},"application/vnd.d3m-dataset":{source:"iana"},"application/vnd.d3m-problem":{source:"iana"},"application/vnd.dart":{source:"iana",compressible:!0,extensions:["dart"]},"application/vnd.data-vision.rdz":{source:"iana",extensions:["rdz"]},"application/vnd.datapackage+json":{source:"iana",compressible:!0},"application/vnd.dataresource+json":{source:"iana",compressible:!0},"application/vnd.dbf":{source:"iana",extensions:["dbf"]},"application/vnd.debian.binary-package":{source:"iana"},"application/vnd.dece.data":{source:"iana",extensions:["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{source:"iana",compressible:!0,extensions:["uvt","uvvt"]},"application/vnd.dece.unspecified":{source:"iana",extensions:["uvx","uvvx"]},"application/vnd.dece.zip":{source:"iana",extensions:["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{source:"iana",extensions:["fe_launch"]},"application/vnd.desmume.movie":{source:"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{source:"iana"},"application/vnd.dm.delegation+xml":{source:"iana",compressible:!0},"application/vnd.dna":{source:"iana",extensions:["dna"]},"application/vnd.document+json":{source:"iana",compressible:!0},"application/vnd.dolby.mlp":{source:"apache",extensions:["mlp"]},"application/vnd.dolby.mobile.1":{source:"iana"},"application/vnd.dolby.mobile.2":{source:"iana"},"application/vnd.doremir.scorecloud-binary-document":{source:"iana"},"application/vnd.dpgraph":{source:"iana",extensions:["dpg"]},"application/vnd.dreamfactory":{source:"iana",extensions:["dfac"]},"application/vnd.drive+json":{source:"iana",compressible:!0},"application/vnd.ds-keypoint":{source:"apache",extensions:["kpxx"]},"application/vnd.dtg.local":{source:"iana"},"application/vnd.dtg.local.flash":{source:"iana"},"application/vnd.dtg.local.html":{source:"iana"},"application/vnd.dvb.ait":{source:"iana",extensions:["ait"]},"application/vnd.dvb.dvbisl+xml":{source:"iana",compressible:!0},"application/vnd.dvb.dvbj":{source:"iana"},"application/vnd.dvb.esgcontainer":{source:"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess2":{source:"iana"},"application/vnd.dvb.ipdcesgpdd":{source:"iana"},"application/vnd.dvb.ipdcroaming":{source:"iana"},"application/vnd.dvb.iptv.alfec-base":{source:"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{source:"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-container+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-generic+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-msglist+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-registration-request+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-registration-response+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-init+xml":{source:"iana",compressible:!0},"application/vnd.dvb.pfr":{source:"iana"},"application/vnd.dvb.service":{source:"iana",extensions:["svc"]},"application/vnd.dxr":{source:"iana"},"application/vnd.dynageo":{source:"iana",extensions:["geo"]},"application/vnd.dzr":{source:"iana"},"application/vnd.easykaraoke.cdgdownload":{source:"iana"},"application/vnd.ecdis-update":{source:"iana"},"application/vnd.ecip.rlp":{source:"iana"},"application/vnd.eclipse.ditto+json":{source:"iana",compressible:!0},"application/vnd.ecowin.chart":{source:"iana",extensions:["mag"]},"application/vnd.ecowin.filerequest":{source:"iana"},"application/vnd.ecowin.fileupdate":{source:"iana"},"application/vnd.ecowin.series":{source:"iana"},"application/vnd.ecowin.seriesrequest":{source:"iana"},"application/vnd.ecowin.seriesupdate":{source:"iana"},"application/vnd.efi.img":{source:"iana"},"application/vnd.efi.iso":{source:"iana"},"application/vnd.emclient.accessrequest+xml":{source:"iana",compressible:!0},"application/vnd.enliven":{source:"iana",extensions:["nml"]},"application/vnd.enphase.envoy":{source:"iana"},"application/vnd.eprints.data+xml":{source:"iana",compressible:!0},"application/vnd.epson.esf":{source:"iana",extensions:["esf"]},"application/vnd.epson.msf":{source:"iana",extensions:["msf"]},"application/vnd.epson.quickanime":{source:"iana",extensions:["qam"]},"application/vnd.epson.salt":{source:"iana",extensions:["slt"]},"application/vnd.epson.ssf":{source:"iana",extensions:["ssf"]},"application/vnd.ericsson.quickcall":{source:"iana"},"application/vnd.espass-espass+zip":{source:"iana",compressible:!1},"application/vnd.eszigno3+xml":{source:"iana",compressible:!0,extensions:["es3","et3"]},"application/vnd.etsi.aoc+xml":{source:"iana",compressible:!0},"application/vnd.etsi.asic-e+zip":{source:"iana",compressible:!1},"application/vnd.etsi.asic-s+zip":{source:"iana",compressible:!1},"application/vnd.etsi.cug+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvcommand+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvdiscovery+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvprofile+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-bc+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-cod+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-npvr+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvservice+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsync+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvueprofile+xml":{source:"iana",compressible:!0},"application/vnd.etsi.mcid+xml":{source:"iana",compressible:!0},"application/vnd.etsi.mheg5":{source:"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{source:"iana",compressible:!0},"application/vnd.etsi.pstn+xml":{source:"iana",compressible:!0},"application/vnd.etsi.sci+xml":{source:"iana",compressible:!0},"application/vnd.etsi.simservs+xml":{source:"iana",compressible:!0},"application/vnd.etsi.timestamp-token":{source:"iana"},"application/vnd.etsi.tsl+xml":{source:"iana",compressible:!0},"application/vnd.etsi.tsl.der":{source:"iana"},"application/vnd.eu.kasparian.car+json":{source:"iana",compressible:!0},"application/vnd.eudora.data":{source:"iana"},"application/vnd.evolv.ecig.profile":{source:"iana"},"application/vnd.evolv.ecig.settings":{source:"iana"},"application/vnd.evolv.ecig.theme":{source:"iana"},"application/vnd.exstream-empower+zip":{source:"iana",compressible:!1},"application/vnd.exstream-package":{source:"iana"},"application/vnd.ezpix-album":{source:"iana",extensions:["ez2"]},"application/vnd.ezpix-package":{source:"iana",extensions:["ez3"]},"application/vnd.f-secure.mobile":{source:"iana"},"application/vnd.familysearch.gedcom+zip":{source:"iana",compressible:!1},"application/vnd.fastcopy-disk-image":{source:"iana"},"application/vnd.fdf":{source:"iana",extensions:["fdf"]},"application/vnd.fdsn.mseed":{source:"iana",extensions:["mseed"]},"application/vnd.fdsn.seed":{source:"iana",extensions:["seed","dataless"]},"application/vnd.ffsns":{source:"iana"},"application/vnd.ficlab.flb+zip":{source:"iana",compressible:!1},"application/vnd.filmit.zfc":{source:"iana"},"application/vnd.fints":{source:"iana"},"application/vnd.firemonkeys.cloudcell":{source:"iana"},"application/vnd.flographit":{source:"iana",extensions:["gph"]},"application/vnd.fluxtime.clip":{source:"iana",extensions:["ftc"]},"application/vnd.font-fontforge-sfd":{source:"iana"},"application/vnd.framemaker":{source:"iana",extensions:["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{source:"iana",extensions:["fnc"]},"application/vnd.frogans.ltf":{source:"iana",extensions:["ltf"]},"application/vnd.fsc.weblaunch":{source:"iana",extensions:["fsc"]},"application/vnd.fujifilm.fb.docuworks":{source:"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{source:"iana"},"application/vnd.fujifilm.fb.docuworks.container":{source:"iana"},"application/vnd.fujifilm.fb.jfi+xml":{source:"iana",compressible:!0},"application/vnd.fujitsu.oasys":{source:"iana",extensions:["oas"]},"application/vnd.fujitsu.oasys2":{source:"iana",extensions:["oa2"]},"application/vnd.fujitsu.oasys3":{source:"iana",extensions:["oa3"]},"application/vnd.fujitsu.oasysgp":{source:"iana",extensions:["fg5"]},"application/vnd.fujitsu.oasysprs":{source:"iana",extensions:["bh2"]},"application/vnd.fujixerox.art-ex":{source:"iana"},"application/vnd.fujixerox.art4":{source:"iana"},"application/vnd.fujixerox.ddd":{source:"iana",extensions:["ddd"]},"application/vnd.fujixerox.docuworks":{source:"iana",extensions:["xdw"]},"application/vnd.fujixerox.docuworks.binder":{source:"iana",extensions:["xbd"]},"application/vnd.fujixerox.docuworks.container":{source:"iana"},"application/vnd.fujixerox.hbpl":{source:"iana"},"application/vnd.fut-misnet":{source:"iana"},"application/vnd.futoin+cbor":{source:"iana"},"application/vnd.futoin+json":{source:"iana",compressible:!0},"application/vnd.fuzzysheet":{source:"iana",extensions:["fzs"]},"application/vnd.genomatix.tuxedo":{source:"iana",extensions:["txd"]},"application/vnd.gentics.grd+json":{source:"iana",compressible:!0},"application/vnd.geo+json":{source:"iana",compressible:!0},"application/vnd.geocube+xml":{source:"iana",compressible:!0},"application/vnd.geogebra.file":{source:"iana",extensions:["ggb"]},"application/vnd.geogebra.slides":{source:"iana"},"application/vnd.geogebra.tool":{source:"iana",extensions:["ggt"]},"application/vnd.geometry-explorer":{source:"iana",extensions:["gex","gre"]},"application/vnd.geonext":{source:"iana",extensions:["gxt"]},"application/vnd.geoplan":{source:"iana",extensions:["g2w"]},"application/vnd.geospace":{source:"iana",extensions:["g3w"]},"application/vnd.gerber":{source:"iana"},"application/vnd.globalplatform.card-content-mgt":{source:"iana"},"application/vnd.globalplatform.card-content-mgt-response":{source:"iana"},"application/vnd.gmx":{source:"iana",extensions:["gmx"]},"application/vnd.google-apps.document":{compressible:!1,extensions:["gdoc"]},"application/vnd.google-apps.presentation":{compressible:!1,extensions:["gslides"]},"application/vnd.google-apps.spreadsheet":{compressible:!1,extensions:["gsheet"]},"application/vnd.google-earth.kml+xml":{source:"iana",compressible:!0,extensions:["kml"]},"application/vnd.google-earth.kmz":{source:"iana",compressible:!1,extensions:["kmz"]},"application/vnd.gov.sk.e-form+xml":{source:"iana",compressible:!0},"application/vnd.gov.sk.e-form+zip":{source:"iana",compressible:!1},"application/vnd.gov.sk.xmldatacontainer+xml":{source:"iana",compressible:!0},"application/vnd.grafeq":{source:"iana",extensions:["gqf","gqs"]},"application/vnd.gridmp":{source:"iana"},"application/vnd.groove-account":{source:"iana",extensions:["gac"]},"application/vnd.groove-help":{source:"iana",extensions:["ghf"]},"application/vnd.groove-identity-message":{source:"iana",extensions:["gim"]},"application/vnd.groove-injector":{source:"iana",extensions:["grv"]},"application/vnd.groove-tool-message":{source:"iana",extensions:["gtm"]},"application/vnd.groove-tool-template":{source:"iana",extensions:["tpl"]},"application/vnd.groove-vcard":{source:"iana",extensions:["vcg"]},"application/vnd.hal+json":{source:"iana",compressible:!0},"application/vnd.hal+xml":{source:"iana",compressible:!0,extensions:["hal"]},"application/vnd.handheld-entertainment+xml":{source:"iana",compressible:!0,extensions:["zmm"]},"application/vnd.hbci":{source:"iana",extensions:["hbci"]},"application/vnd.hc+json":{source:"iana",compressible:!0},"application/vnd.hcl-bireports":{source:"iana"},"application/vnd.hdt":{source:"iana"},"application/vnd.heroku+json":{source:"iana",compressible:!0},"application/vnd.hhe.lesson-player":{source:"iana",extensions:["les"]},"application/vnd.hl7cda+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.hl7v2+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.hp-hpgl":{source:"iana",extensions:["hpgl"]},"application/vnd.hp-hpid":{source:"iana",extensions:["hpid"]},"application/vnd.hp-hps":{source:"iana",extensions:["hps"]},"application/vnd.hp-jlyt":{source:"iana",extensions:["jlt"]},"application/vnd.hp-pcl":{source:"iana",extensions:["pcl"]},"application/vnd.hp-pclxl":{source:"iana",extensions:["pclxl"]},"application/vnd.httphone":{source:"iana"},"application/vnd.hydrostatix.sof-data":{source:"iana",extensions:["sfd-hdstx"]},"application/vnd.hyper+json":{source:"iana",compressible:!0},"application/vnd.hyper-item+json":{source:"iana",compressible:!0},"application/vnd.hyperdrive+json":{source:"iana",compressible:!0},"application/vnd.hzn-3d-crossword":{source:"iana"},"application/vnd.ibm.afplinedata":{source:"iana"},"application/vnd.ibm.electronic-media":{source:"iana"},"application/vnd.ibm.minipay":{source:"iana",extensions:["mpy"]},"application/vnd.ibm.modcap":{source:"iana",extensions:["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{source:"iana",extensions:["irm"]},"application/vnd.ibm.secure-container":{source:"iana",extensions:["sc"]},"application/vnd.iccprofile":{source:"iana",extensions:["icc","icm"]},"application/vnd.ieee.1905":{source:"iana"},"application/vnd.igloader":{source:"iana",extensions:["igl"]},"application/vnd.imagemeter.folder+zip":{source:"iana",compressible:!1},"application/vnd.imagemeter.image+zip":{source:"iana",compressible:!1},"application/vnd.immervision-ivp":{source:"iana",extensions:["ivp"]},"application/vnd.immervision-ivu":{source:"iana",extensions:["ivu"]},"application/vnd.ims.imsccv1p1":{source:"iana"},"application/vnd.ims.imsccv1p2":{source:"iana"},"application/vnd.ims.imsccv1p3":{source:"iana"},"application/vnd.ims.lis.v2.result+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy.id+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings.simple+json":{source:"iana",compressible:!0},"application/vnd.informedcontrol.rms+xml":{source:"iana",compressible:!0},"application/vnd.informix-visionary":{source:"iana"},"application/vnd.infotech.project":{source:"iana"},"application/vnd.infotech.project+xml":{source:"iana",compressible:!0},"application/vnd.innopath.wamp.notification":{source:"iana"},"application/vnd.insors.igm":{source:"iana",extensions:["igm"]},"application/vnd.intercon.formnet":{source:"iana",extensions:["xpw","xpx"]},"application/vnd.intergeo":{source:"iana",extensions:["i2g"]},"application/vnd.intertrust.digibox":{source:"iana"},"application/vnd.intertrust.nncp":{source:"iana"},"application/vnd.intu.qbo":{source:"iana",extensions:["qbo"]},"application/vnd.intu.qfx":{source:"iana",extensions:["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.conceptitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.knowledgeitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.newsitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.newsmessage+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.packageitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.planningitem+xml":{source:"iana",compressible:!0},"application/vnd.ipunplugged.rcprofile":{source:"iana",extensions:["rcprofile"]},"application/vnd.irepository.package+xml":{source:"iana",compressible:!0,extensions:["irp"]},"application/vnd.is-xpr":{source:"iana",extensions:["xpr"]},"application/vnd.isac.fcs":{source:"iana",extensions:["fcs"]},"application/vnd.iso11783-10+zip":{source:"iana",compressible:!1},"application/vnd.jam":{source:"iana",extensions:["jam"]},"application/vnd.japannet-directory-service":{source:"iana"},"application/vnd.japannet-jpnstore-wakeup":{source:"iana"},"application/vnd.japannet-payment-wakeup":{source:"iana"},"application/vnd.japannet-registration":{source:"iana"},"application/vnd.japannet-registration-wakeup":{source:"iana"},"application/vnd.japannet-setstore-wakeup":{source:"iana"},"application/vnd.japannet-verification":{source:"iana"},"application/vnd.japannet-verification-wakeup":{source:"iana"},"application/vnd.jcp.javame.midlet-rms":{source:"iana",extensions:["rms"]},"application/vnd.jisp":{source:"iana",extensions:["jisp"]},"application/vnd.joost.joda-archive":{source:"iana",extensions:["joda"]},"application/vnd.jsk.isdn-ngn":{source:"iana"},"application/vnd.kahootz":{source:"iana",extensions:["ktz","ktr"]},"application/vnd.kde.karbon":{source:"iana",extensions:["karbon"]},"application/vnd.kde.kchart":{source:"iana",extensions:["chrt"]},"application/vnd.kde.kformula":{source:"iana",extensions:["kfo"]},"application/vnd.kde.kivio":{source:"iana",extensions:["flw"]},"application/vnd.kde.kontour":{source:"iana",extensions:["kon"]},"application/vnd.kde.kpresenter":{source:"iana",extensions:["kpr","kpt"]},"application/vnd.kde.kspread":{source:"iana",extensions:["ksp"]},"application/vnd.kde.kword":{source:"iana",extensions:["kwd","kwt"]},"application/vnd.kenameaapp":{source:"iana",extensions:["htke"]},"application/vnd.kidspiration":{source:"iana",extensions:["kia"]},"application/vnd.kinar":{source:"iana",extensions:["kne","knp"]},"application/vnd.koan":{source:"iana",extensions:["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{source:"iana",extensions:["sse"]},"application/vnd.las":{source:"iana"},"application/vnd.las.las+json":{source:"iana",compressible:!0},"application/vnd.las.las+xml":{source:"iana",compressible:!0,extensions:["lasxml"]},"application/vnd.laszip":{source:"iana"},"application/vnd.leap+json":{source:"iana",compressible:!0},"application/vnd.liberty-request+xml":{source:"iana",compressible:!0},"application/vnd.llamagraphics.life-balance.desktop":{source:"iana",extensions:["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{source:"iana",compressible:!0,extensions:["lbe"]},"application/vnd.logipipe.circuit+zip":{source:"iana",compressible:!1},"application/vnd.loom":{source:"iana"},"application/vnd.lotus-1-2-3":{source:"iana",extensions:["123"]},"application/vnd.lotus-approach":{source:"iana",extensions:["apr"]},"application/vnd.lotus-freelance":{source:"iana",extensions:["pre"]},"application/vnd.lotus-notes":{source:"iana",extensions:["nsf"]},"application/vnd.lotus-organizer":{source:"iana",extensions:["org"]},"application/vnd.lotus-screencam":{source:"iana",extensions:["scm"]},"application/vnd.lotus-wordpro":{source:"iana",extensions:["lwp"]},"application/vnd.macports.portpkg":{source:"iana",extensions:["portpkg"]},"application/vnd.mapbox-vector-tile":{source:"iana",extensions:["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.conftoken+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.license+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.mdcf":{source:"iana"},"application/vnd.mason+json":{source:"iana",compressible:!0},"application/vnd.maxar.archive.3tz+zip":{source:"iana",compressible:!1},"application/vnd.maxmind.maxmind-db":{source:"iana"},"application/vnd.mcd":{source:"iana",extensions:["mcd"]},"application/vnd.medcalcdata":{source:"iana",extensions:["mc1"]},"application/vnd.mediastation.cdkey":{source:"iana",extensions:["cdkey"]},"application/vnd.meridian-slingshot":{source:"iana"},"application/vnd.mfer":{source:"iana",extensions:["mwf"]},"application/vnd.mfmp":{source:"iana",extensions:["mfm"]},"application/vnd.micro+json":{source:"iana",compressible:!0},"application/vnd.micrografx.flo":{source:"iana",extensions:["flo"]},"application/vnd.micrografx.igx":{source:"iana",extensions:["igx"]},"application/vnd.microsoft.portable-executable":{source:"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{source:"iana"},"application/vnd.miele+json":{source:"iana",compressible:!0},"application/vnd.mif":{source:"iana",extensions:["mif"]},"application/vnd.minisoft-hp3000-save":{source:"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{source:"iana"},"application/vnd.mobius.daf":{source:"iana",extensions:["daf"]},"application/vnd.mobius.dis":{source:"iana",extensions:["dis"]},"application/vnd.mobius.mbk":{source:"iana",extensions:["mbk"]},"application/vnd.mobius.mqy":{source:"iana",extensions:["mqy"]},"application/vnd.mobius.msl":{source:"iana",extensions:["msl"]},"application/vnd.mobius.plc":{source:"iana",extensions:["plc"]},"application/vnd.mobius.txf":{source:"iana",extensions:["txf"]},"application/vnd.mophun.application":{source:"iana",extensions:["mpn"]},"application/vnd.mophun.certificate":{source:"iana",extensions:["mpc"]},"application/vnd.motorola.flexsuite":{source:"iana"},"application/vnd.motorola.flexsuite.adsi":{source:"iana"},"application/vnd.motorola.flexsuite.fis":{source:"iana"},"application/vnd.motorola.flexsuite.gotap":{source:"iana"},"application/vnd.motorola.flexsuite.kmr":{source:"iana"},"application/vnd.motorola.flexsuite.ttc":{source:"iana"},"application/vnd.motorola.flexsuite.wem":{source:"iana"},"application/vnd.motorola.iprm":{source:"iana"},"application/vnd.mozilla.xul+xml":{source:"iana",compressible:!0,extensions:["xul"]},"application/vnd.ms-3mfdocument":{source:"iana"},"application/vnd.ms-artgalry":{source:"iana",extensions:["cil"]},"application/vnd.ms-asf":{source:"iana"},"application/vnd.ms-cab-compressed":{source:"iana",extensions:["cab"]},"application/vnd.ms-color.iccprofile":{source:"apache"},"application/vnd.ms-excel":{source:"iana",compressible:!1,extensions:["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{source:"iana",extensions:["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{source:"iana",extensions:["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{source:"iana",extensions:["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{source:"iana",extensions:["xltm"]},"application/vnd.ms-fontobject":{source:"iana",compressible:!0,extensions:["eot"]},"application/vnd.ms-htmlhelp":{source:"iana",extensions:["chm"]},"application/vnd.ms-ims":{source:"iana",extensions:["ims"]},"application/vnd.ms-lrm":{source:"iana",extensions:["lrm"]},"application/vnd.ms-office.activex+xml":{source:"iana",compressible:!0},"application/vnd.ms-officetheme":{source:"iana",extensions:["thmx"]},"application/vnd.ms-opentype":{source:"apache",compressible:!0},"application/vnd.ms-outlook":{compressible:!1,extensions:["msg"]},"application/vnd.ms-package.obfuscated-opentype":{source:"apache"},"application/vnd.ms-pki.seccat":{source:"apache",extensions:["cat"]},"application/vnd.ms-pki.stl":{source:"apache",extensions:["stl"]},"application/vnd.ms-playready.initiator+xml":{source:"iana",compressible:!0},"application/vnd.ms-powerpoint":{source:"iana",compressible:!1,extensions:["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{source:"iana",extensions:["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{source:"iana",extensions:["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{source:"iana",extensions:["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{source:"iana",extensions:["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{source:"iana",extensions:["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{source:"iana",compressible:!0},"application/vnd.ms-printing.printticket+xml":{source:"apache",compressible:!0},"application/vnd.ms-printschematicket+xml":{source:"iana",compressible:!0},"application/vnd.ms-project":{source:"iana",extensions:["mpp","mpt"]},"application/vnd.ms-tnef":{source:"iana"},"application/vnd.ms-windows.devicepairing":{source:"iana"},"application/vnd.ms-windows.nwprinting.oob":{source:"iana"},"application/vnd.ms-windows.printerpairing":{source:"iana"},"application/vnd.ms-windows.wsd.oob":{source:"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.lic-resp":{source:"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.meter-resp":{source:"iana"},"application/vnd.ms-word.document.macroenabled.12":{source:"iana",extensions:["docm"]},"application/vnd.ms-word.template.macroenabled.12":{source:"iana",extensions:["dotm"]},"application/vnd.ms-works":{source:"iana",extensions:["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{source:"iana",extensions:["wpl"]},"application/vnd.ms-xpsdocument":{source:"iana",compressible:!1,extensions:["xps"]},"application/vnd.msa-disk-image":{source:"iana"},"application/vnd.mseq":{source:"iana",extensions:["mseq"]},"application/vnd.msign":{source:"iana"},"application/vnd.multiad.creator":{source:"iana"},"application/vnd.multiad.creator.cif":{source:"iana"},"application/vnd.music-niff":{source:"iana"},"application/vnd.musician":{source:"iana",extensions:["mus"]},"application/vnd.muvee.style":{source:"iana",extensions:["msty"]},"application/vnd.mynfc":{source:"iana",extensions:["taglet"]},"application/vnd.nacamar.ybrid+json":{source:"iana",compressible:!0},"application/vnd.ncd.control":{source:"iana"},"application/vnd.ncd.reference":{source:"iana"},"application/vnd.nearst.inv+json":{source:"iana",compressible:!0},"application/vnd.nebumind.line":{source:"iana"},"application/vnd.nervana":{source:"iana"},"application/vnd.netfpx":{source:"iana"},"application/vnd.neurolanguage.nlu":{source:"iana",extensions:["nlu"]},"application/vnd.nimn":{source:"iana"},"application/vnd.nintendo.nitro.rom":{source:"iana"},"application/vnd.nintendo.snes.rom":{source:"iana"},"application/vnd.nitf":{source:"iana",extensions:["ntf","nitf"]},"application/vnd.noblenet-directory":{source:"iana",extensions:["nnd"]},"application/vnd.noblenet-sealer":{source:"iana",extensions:["nns"]},"application/vnd.noblenet-web":{source:"iana",extensions:["nnw"]},"application/vnd.nokia.catalogs":{source:"iana"},"application/vnd.nokia.conml+wbxml":{source:"iana"},"application/vnd.nokia.conml+xml":{source:"iana",compressible:!0},"application/vnd.nokia.iptv.config+xml":{source:"iana",compressible:!0},"application/vnd.nokia.isds-radio-presets":{source:"iana"},"application/vnd.nokia.landmark+wbxml":{source:"iana"},"application/vnd.nokia.landmark+xml":{source:"iana",compressible:!0},"application/vnd.nokia.landmarkcollection+xml":{source:"iana",compressible:!0},"application/vnd.nokia.n-gage.ac+xml":{source:"iana",compressible:!0,extensions:["ac"]},"application/vnd.nokia.n-gage.data":{source:"iana",extensions:["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{source:"iana",extensions:["n-gage"]},"application/vnd.nokia.ncd":{source:"iana"},"application/vnd.nokia.pcd+wbxml":{source:"iana"},"application/vnd.nokia.pcd+xml":{source:"iana",compressible:!0},"application/vnd.nokia.radio-preset":{source:"iana",extensions:["rpst"]},"application/vnd.nokia.radio-presets":{source:"iana",extensions:["rpss"]},"application/vnd.novadigm.edm":{source:"iana",extensions:["edm"]},"application/vnd.novadigm.edx":{source:"iana",extensions:["edx"]},"application/vnd.novadigm.ext":{source:"iana",extensions:["ext"]},"application/vnd.ntt-local.content-share":{source:"iana"},"application/vnd.ntt-local.file-transfer":{source:"iana"},"application/vnd.ntt-local.ogw_remote-access":{source:"iana"},"application/vnd.ntt-local.sip-ta_remote":{source:"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{source:"iana"},"application/vnd.oasis.opendocument.chart":{source:"iana",extensions:["odc"]},"application/vnd.oasis.opendocument.chart-template":{source:"iana",extensions:["otc"]},"application/vnd.oasis.opendocument.database":{source:"iana",extensions:["odb"]},"application/vnd.oasis.opendocument.formula":{source:"iana",extensions:["odf"]},"application/vnd.oasis.opendocument.formula-template":{source:"iana",extensions:["odft"]},"application/vnd.oasis.opendocument.graphics":{source:"iana",compressible:!1,extensions:["odg"]},"application/vnd.oasis.opendocument.graphics-template":{source:"iana",extensions:["otg"]},"application/vnd.oasis.opendocument.image":{source:"iana",extensions:["odi"]},"application/vnd.oasis.opendocument.image-template":{source:"iana",extensions:["oti"]},"application/vnd.oasis.opendocument.presentation":{source:"iana",compressible:!1,extensions:["odp"]},"application/vnd.oasis.opendocument.presentation-template":{source:"iana",extensions:["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{source:"iana",compressible:!1,extensions:["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{source:"iana",extensions:["ots"]},"application/vnd.oasis.opendocument.text":{source:"iana",compressible:!1,extensions:["odt"]},"application/vnd.oasis.opendocument.text-master":{source:"iana",extensions:["odm"]},"application/vnd.oasis.opendocument.text-template":{source:"iana",extensions:["ott"]},"application/vnd.oasis.opendocument.text-web":{source:"iana",extensions:["oth"]},"application/vnd.obn":{source:"iana"},"application/vnd.ocf+cbor":{source:"iana"},"application/vnd.oci.image.manifest.v1+json":{source:"iana",compressible:!0},"application/vnd.oftn.l10n+json":{source:"iana",compressible:!0},"application/vnd.oipf.contentaccessdownload+xml":{source:"iana",compressible:!0},"application/vnd.oipf.contentaccessstreaming+xml":{source:"iana",compressible:!0},"application/vnd.oipf.cspg-hexbinary":{source:"iana"},"application/vnd.oipf.dae.svg+xml":{source:"iana",compressible:!0},"application/vnd.oipf.dae.xhtml+xml":{source:"iana",compressible:!0},"application/vnd.oipf.mippvcontrolmessage+xml":{source:"iana",compressible:!0},"application/vnd.oipf.pae.gem":{source:"iana"},"application/vnd.oipf.spdiscovery+xml":{source:"iana",compressible:!0},"application/vnd.oipf.spdlist+xml":{source:"iana",compressible:!0},"application/vnd.oipf.ueprofile+xml":{source:"iana",compressible:!0},"application/vnd.oipf.userprofile+xml":{source:"iana",compressible:!0},"application/vnd.olpc-sugar":{source:"iana",extensions:["xo"]},"application/vnd.oma-scws-config":{source:"iana"},"application/vnd.oma-scws-http-request":{source:"iana"},"application/vnd.oma-scws-http-response":{source:"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.drm-trigger+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.imd+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.ltkm":{source:"iana"},"application/vnd.oma.bcast.notification+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.provisioningtrigger":{source:"iana"},"application/vnd.oma.bcast.sgboot":{source:"iana"},"application/vnd.oma.bcast.sgdd+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.sgdu":{source:"iana"},"application/vnd.oma.bcast.simple-symbol-container":{source:"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.sprov+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.stkm":{source:"iana"},"application/vnd.oma.cab-address-book+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-feature-handler+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-pcc+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-subs-invite+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-user-prefs+xml":{source:"iana",compressible:!0},"application/vnd.oma.dcd":{source:"iana"},"application/vnd.oma.dcdc":{source:"iana"},"application/vnd.oma.dd2+xml":{source:"iana",compressible:!0,extensions:["dd2"]},"application/vnd.oma.drm.risd+xml":{source:"iana",compressible:!0},"application/vnd.oma.group-usage-list+xml":{source:"iana",compressible:!0},"application/vnd.oma.lwm2m+cbor":{source:"iana"},"application/vnd.oma.lwm2m+json":{source:"iana",compressible:!0},"application/vnd.oma.lwm2m+tlv":{source:"iana"},"application/vnd.oma.pal+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.detailed-progress-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.final-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.groups+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.invocation-descriptor+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.optimized-progress-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.push":{source:"iana"},"application/vnd.oma.scidm.messages+xml":{source:"iana",compressible:!0},"application/vnd.oma.xcap-directory+xml":{source:"iana",compressible:!0},"application/vnd.omads-email+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omads-file+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omads-folder+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omaloc-supl-init":{source:"iana"},"application/vnd.onepager":{source:"iana"},"application/vnd.onepagertamp":{source:"iana"},"application/vnd.onepagertamx":{source:"iana"},"application/vnd.onepagertat":{source:"iana"},"application/vnd.onepagertatp":{source:"iana"},"application/vnd.onepagertatx":{source:"iana"},"application/vnd.openblox.game+xml":{source:"iana",compressible:!0,extensions:["obgx"]},"application/vnd.openblox.game-binary":{source:"iana"},"application/vnd.openeye.oeb":{source:"iana"},"application/vnd.openofficeorg.extension":{source:"apache",extensions:["oxt"]},"application/vnd.openstreetmap.data+xml":{source:"iana",compressible:!0,extensions:["osm"]},"application/vnd.opentimestamps.ots":{source:"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawing+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{source:"iana",compressible:!1,extensions:["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slide":{source:"iana",extensions:["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{source:"iana",extensions:["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.template":{source:"iana",extensions:["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{source:"iana",compressible:!1,extensions:["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{source:"iana",extensions:["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.theme+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.vmldrawing":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{source:"iana",compressible:!1,extensions:["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{source:"iana",extensions:["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.core-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.relationships+xml":{source:"iana",compressible:!0},"application/vnd.oracle.resource+json":{source:"iana",compressible:!0},"application/vnd.orange.indata":{source:"iana"},"application/vnd.osa.netdeploy":{source:"iana"},"application/vnd.osgeo.mapguide.package":{source:"iana",extensions:["mgp"]},"application/vnd.osgi.bundle":{source:"iana"},"application/vnd.osgi.dp":{source:"iana",extensions:["dp"]},"application/vnd.osgi.subsystem":{source:"iana",extensions:["esa"]},"application/vnd.otps.ct-kip+xml":{source:"iana",compressible:!0},"application/vnd.oxli.countgraph":{source:"iana"},"application/vnd.pagerduty+json":{source:"iana",compressible:!0},"application/vnd.palm":{source:"iana",extensions:["pdb","pqa","oprc"]},"application/vnd.panoply":{source:"iana"},"application/vnd.paos.xml":{source:"iana"},"application/vnd.patentdive":{source:"iana"},"application/vnd.patientecommsdoc":{source:"iana"},"application/vnd.pawaafile":{source:"iana",extensions:["paw"]},"application/vnd.pcos":{source:"iana"},"application/vnd.pg.format":{source:"iana",extensions:["str"]},"application/vnd.pg.osasli":{source:"iana",extensions:["ei6"]},"application/vnd.piaccess.application-licence":{source:"iana"},"application/vnd.picsel":{source:"iana",extensions:["efif"]},"application/vnd.pmi.widget":{source:"iana",extensions:["wg"]},"application/vnd.poc.group-advertisement+xml":{source:"iana",compressible:!0},"application/vnd.pocketlearn":{source:"iana",extensions:["plf"]},"application/vnd.powerbuilder6":{source:"iana",extensions:["pbd"]},"application/vnd.powerbuilder6-s":{source:"iana"},"application/vnd.powerbuilder7":{source:"iana"},"application/vnd.powerbuilder7-s":{source:"iana"},"application/vnd.powerbuilder75":{source:"iana"},"application/vnd.powerbuilder75-s":{source:"iana"},"application/vnd.preminet":{source:"iana"},"application/vnd.previewsystems.box":{source:"iana",extensions:["box"]},"application/vnd.proteus.magazine":{source:"iana",extensions:["mgz"]},"application/vnd.psfs":{source:"iana"},"application/vnd.publishare-delta-tree":{source:"iana",extensions:["qps"]},"application/vnd.pvi.ptid1":{source:"iana",extensions:["ptid"]},"application/vnd.pwg-multiplexed":{source:"iana"},"application/vnd.pwg-xhtml-print+xml":{source:"iana",compressible:!0},"application/vnd.qualcomm.brew-app-res":{source:"iana"},"application/vnd.quarantainenet":{source:"iana"},"application/vnd.quark.quarkxpress":{source:"iana",extensions:["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{source:"iana"},"application/vnd.radisys.moml+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-conf+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-conn+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-dialog+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-stream+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-conf+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-base+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-fax-detect+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-group+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-speech+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-transform+xml":{source:"iana",compressible:!0},"application/vnd.rainstor.data":{source:"iana"},"application/vnd.rapid":{source:"iana"},"application/vnd.rar":{source:"iana",extensions:["rar"]},"application/vnd.realvnc.bed":{source:"iana",extensions:["bed"]},"application/vnd.recordare.musicxml":{source:"iana",extensions:["mxl"]},"application/vnd.recordare.musicxml+xml":{source:"iana",compressible:!0,extensions:["musicxml"]},"application/vnd.renlearn.rlprint":{source:"iana"},"application/vnd.resilient.logic":{source:"iana"},"application/vnd.restful+json":{source:"iana",compressible:!0},"application/vnd.rig.cryptonote":{source:"iana",extensions:["cryptonote"]},"application/vnd.rim.cod":{source:"apache",extensions:["cod"]},"application/vnd.rn-realmedia":{source:"apache",extensions:["rm"]},"application/vnd.rn-realmedia-vbr":{source:"apache",extensions:["rmvb"]},"application/vnd.route66.link66+xml":{source:"iana",compressible:!0,extensions:["link66"]},"application/vnd.rs-274x":{source:"iana"},"application/vnd.ruckus.download":{source:"iana"},"application/vnd.s3sms":{source:"iana"},"application/vnd.sailingtracker.track":{source:"iana",extensions:["st"]},"application/vnd.sar":{source:"iana"},"application/vnd.sbm.cid":{source:"iana"},"application/vnd.sbm.mid2":{source:"iana"},"application/vnd.scribus":{source:"iana"},"application/vnd.sealed.3df":{source:"iana"},"application/vnd.sealed.csf":{source:"iana"},"application/vnd.sealed.doc":{source:"iana"},"application/vnd.sealed.eml":{source:"iana"},"application/vnd.sealed.mht":{source:"iana"},"application/vnd.sealed.net":{source:"iana"},"application/vnd.sealed.ppt":{source:"iana"},"application/vnd.sealed.tiff":{source:"iana"},"application/vnd.sealed.xls":{source:"iana"},"application/vnd.sealedmedia.softseal.html":{source:"iana"},"application/vnd.sealedmedia.softseal.pdf":{source:"iana"},"application/vnd.seemail":{source:"iana",extensions:["see"]},"application/vnd.seis+json":{source:"iana",compressible:!0},"application/vnd.sema":{source:"iana",extensions:["sema"]},"application/vnd.semd":{source:"iana",extensions:["semd"]},"application/vnd.semf":{source:"iana",extensions:["semf"]},"application/vnd.shade-save-file":{source:"iana"},"application/vnd.shana.informed.formdata":{source:"iana",extensions:["ifm"]},"application/vnd.shana.informed.formtemplate":{source:"iana",extensions:["itp"]},"application/vnd.shana.informed.interchange":{source:"iana",extensions:["iif"]},"application/vnd.shana.informed.package":{source:"iana",extensions:["ipk"]},"application/vnd.shootproof+json":{source:"iana",compressible:!0},"application/vnd.shopkick+json":{source:"iana",compressible:!0},"application/vnd.shp":{source:"iana"},"application/vnd.shx":{source:"iana"},"application/vnd.sigrok.session":{source:"iana"},"application/vnd.simtech-mindmapper":{source:"iana",extensions:["twd","twds"]},"application/vnd.siren+json":{source:"iana",compressible:!0},"application/vnd.smaf":{source:"iana",extensions:["mmf"]},"application/vnd.smart.notebook":{source:"iana"},"application/vnd.smart.teacher":{source:"iana",extensions:["teacher"]},"application/vnd.snesdev-page-table":{source:"iana"},"application/vnd.software602.filler.form+xml":{source:"iana",compressible:!0,extensions:["fo"]},"application/vnd.software602.filler.form-xml-zip":{source:"iana"},"application/vnd.solent.sdkm+xml":{source:"iana",compressible:!0,extensions:["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{source:"iana",extensions:["dxp"]},"application/vnd.spotfire.sfs":{source:"iana",extensions:["sfs"]},"application/vnd.sqlite3":{source:"iana"},"application/vnd.sss-cod":{source:"iana"},"application/vnd.sss-dtf":{source:"iana"},"application/vnd.sss-ntf":{source:"iana"},"application/vnd.stardivision.calc":{source:"apache",extensions:["sdc"]},"application/vnd.stardivision.draw":{source:"apache",extensions:["sda"]},"application/vnd.stardivision.impress":{source:"apache",extensions:["sdd"]},"application/vnd.stardivision.math":{source:"apache",extensions:["smf"]},"application/vnd.stardivision.writer":{source:"apache",extensions:["sdw","vor"]},"application/vnd.stardivision.writer-global":{source:"apache",extensions:["sgl"]},"application/vnd.stepmania.package":{source:"iana",extensions:["smzip"]},"application/vnd.stepmania.stepchart":{source:"iana",extensions:["sm"]},"application/vnd.street-stream":{source:"iana"},"application/vnd.sun.wadl+xml":{source:"iana",compressible:!0,extensions:["wadl"]},"application/vnd.sun.xml.calc":{source:"apache",extensions:["sxc"]},"application/vnd.sun.xml.calc.template":{source:"apache",extensions:["stc"]},"application/vnd.sun.xml.draw":{source:"apache",extensions:["sxd"]},"application/vnd.sun.xml.draw.template":{source:"apache",extensions:["std"]},"application/vnd.sun.xml.impress":{source:"apache",extensions:["sxi"]},"application/vnd.sun.xml.impress.template":{source:"apache",extensions:["sti"]},"application/vnd.sun.xml.math":{source:"apache",extensions:["sxm"]},"application/vnd.sun.xml.writer":{source:"apache",extensions:["sxw"]},"application/vnd.sun.xml.writer.global":{source:"apache",extensions:["sxg"]},"application/vnd.sun.xml.writer.template":{source:"apache",extensions:["stw"]},"application/vnd.sus-calendar":{source:"iana",extensions:["sus","susp"]},"application/vnd.svd":{source:"iana",extensions:["svd"]},"application/vnd.swiftview-ics":{source:"iana"},"application/vnd.sycle+xml":{source:"iana",compressible:!0},"application/vnd.syft+json":{source:"iana",compressible:!0},"application/vnd.symbian.install":{source:"apache",extensions:["sis","sisx"]},"application/vnd.syncml+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["xsm"]},"application/vnd.syncml.dm+wbxml":{source:"iana",charset:"UTF-8",extensions:["bdm"]},"application/vnd.syncml.dm+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["xdm"]},"application/vnd.syncml.dm.notification":{source:"iana"},"application/vnd.syncml.dmddf+wbxml":{source:"iana"},"application/vnd.syncml.dmddf+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{source:"iana"},"application/vnd.syncml.dmtnds+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.syncml.ds.notification":{source:"iana"},"application/vnd.tableschema+json":{source:"iana",compressible:!0},"application/vnd.tao.intent-module-archive":{source:"iana",extensions:["tao"]},"application/vnd.tcpdump.pcap":{source:"iana",extensions:["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{source:"iana",compressible:!0},"application/vnd.tmd.mediaflex.api+xml":{source:"iana",compressible:!0},"application/vnd.tml":{source:"iana"},"application/vnd.tmobile-livetv":{source:"iana",extensions:["tmo"]},"application/vnd.tri.onesource":{source:"iana"},"application/vnd.trid.tpt":{source:"iana",extensions:["tpt"]},"application/vnd.triscape.mxs":{source:"iana",extensions:["mxs"]},"application/vnd.trueapp":{source:"iana",extensions:["tra"]},"application/vnd.truedoc":{source:"iana"},"application/vnd.ubisoft.webplayer":{source:"iana"},"application/vnd.ufdl":{source:"iana",extensions:["ufd","ufdl"]},"application/vnd.uiq.theme":{source:"iana",extensions:["utz"]},"application/vnd.umajin":{source:"iana",extensions:["umj"]},"application/vnd.unity":{source:"iana",extensions:["unityweb"]},"application/vnd.uoml+xml":{source:"iana",compressible:!0,extensions:["uoml"]},"application/vnd.uplanet.alert":{source:"iana"},"application/vnd.uplanet.alert-wbxml":{source:"iana"},"application/vnd.uplanet.bearer-choice":{source:"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{source:"iana"},"application/vnd.uplanet.cacheop":{source:"iana"},"application/vnd.uplanet.cacheop-wbxml":{source:"iana"},"application/vnd.uplanet.channel":{source:"iana"},"application/vnd.uplanet.channel-wbxml":{source:"iana"},"application/vnd.uplanet.list":{source:"iana"},"application/vnd.uplanet.list-wbxml":{source:"iana"},"application/vnd.uplanet.listcmd":{source:"iana"},"application/vnd.uplanet.listcmd-wbxml":{source:"iana"},"application/vnd.uplanet.signal":{source:"iana"},"application/vnd.uri-map":{source:"iana"},"application/vnd.valve.source.material":{source:"iana"},"application/vnd.vcx":{source:"iana",extensions:["vcx"]},"application/vnd.vd-study":{source:"iana"},"application/vnd.vectorworks":{source:"iana"},"application/vnd.vel+json":{source:"iana",compressible:!0},"application/vnd.verimatrix.vcas":{source:"iana"},"application/vnd.veritone.aion+json":{source:"iana",compressible:!0},"application/vnd.veryant.thin":{source:"iana"},"application/vnd.ves.encrypted":{source:"iana"},"application/vnd.vidsoft.vidconference":{source:"iana"},"application/vnd.visio":{source:"iana",extensions:["vsd","vst","vss","vsw"]},"application/vnd.visionary":{source:"iana",extensions:["vis"]},"application/vnd.vividence.scriptfile":{source:"iana"},"application/vnd.vsf":{source:"iana",extensions:["vsf"]},"application/vnd.wap.sic":{source:"iana"},"application/vnd.wap.slc":{source:"iana"},"application/vnd.wap.wbxml":{source:"iana",charset:"UTF-8",extensions:["wbxml"]},"application/vnd.wap.wmlc":{source:"iana",extensions:["wmlc"]},"application/vnd.wap.wmlscriptc":{source:"iana",extensions:["wmlsc"]},"application/vnd.webturbo":{source:"iana",extensions:["wtb"]},"application/vnd.wfa.dpp":{source:"iana"},"application/vnd.wfa.p2p":{source:"iana"},"application/vnd.wfa.wsc":{source:"iana"},"application/vnd.windows.devicepairing":{source:"iana"},"application/vnd.wmc":{source:"iana"},"application/vnd.wmf.bootstrap":{source:"iana"},"application/vnd.wolfram.mathematica":{source:"iana"},"application/vnd.wolfram.mathematica.package":{source:"iana"},"application/vnd.wolfram.player":{source:"iana",extensions:["nbp"]},"application/vnd.wordperfect":{source:"iana",extensions:["wpd"]},"application/vnd.wqd":{source:"iana",extensions:["wqd"]},"application/vnd.wrq-hp3000-labelled":{source:"iana"},"application/vnd.wt.stf":{source:"iana",extensions:["stf"]},"application/vnd.wv.csp+wbxml":{source:"iana"},"application/vnd.wv.csp+xml":{source:"iana",compressible:!0},"application/vnd.wv.ssp+xml":{source:"iana",compressible:!0},"application/vnd.xacml+json":{source:"iana",compressible:!0},"application/vnd.xara":{source:"iana",extensions:["xar"]},"application/vnd.xfdl":{source:"iana",extensions:["xfdl"]},"application/vnd.xfdl.webform":{source:"iana"},"application/vnd.xmi+xml":{source:"iana",compressible:!0},"application/vnd.xmpie.cpkg":{source:"iana"},"application/vnd.xmpie.dpkg":{source:"iana"},"application/vnd.xmpie.plan":{source:"iana"},"application/vnd.xmpie.ppkg":{source:"iana"},"application/vnd.xmpie.xlim":{source:"iana"},"application/vnd.yamaha.hv-dic":{source:"iana",extensions:["hvd"]},"application/vnd.yamaha.hv-script":{source:"iana",extensions:["hvs"]},"application/vnd.yamaha.hv-voice":{source:"iana",extensions:["hvp"]},"application/vnd.yamaha.openscoreformat":{source:"iana",extensions:["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{source:"iana",compressible:!0,extensions:["osfpvg"]},"application/vnd.yamaha.remote-setup":{source:"iana"},"application/vnd.yamaha.smaf-audio":{source:"iana",extensions:["saf"]},"application/vnd.yamaha.smaf-phrase":{source:"iana",extensions:["spf"]},"application/vnd.yamaha.through-ngn":{source:"iana"},"application/vnd.yamaha.tunnel-udpencap":{source:"iana"},"application/vnd.yaoweme":{source:"iana"},"application/vnd.yellowriver-custom-menu":{source:"iana",extensions:["cmp"]},"application/vnd.youtube.yt":{source:"iana"},"application/vnd.zul":{source:"iana",extensions:["zir","zirz"]},"application/vnd.zzazz.deck+xml":{source:"iana",compressible:!0,extensions:["zaz"]},"application/voicexml+xml":{source:"iana",compressible:!0,extensions:["vxml"]},"application/voucher-cms+json":{source:"iana",compressible:!0},"application/vq-rtcpxr":{source:"iana"},"application/wasm":{source:"iana",compressible:!0,extensions:["wasm"]},"application/watcherinfo+xml":{source:"iana",compressible:!0,extensions:["wif"]},"application/webpush-options+json":{source:"iana",compressible:!0},"application/whoispp-query":{source:"iana"},"application/whoispp-response":{source:"iana"},"application/widget":{source:"iana",extensions:["wgt"]},"application/winhlp":{source:"apache",extensions:["hlp"]},"application/wita":{source:"iana"},"application/wordperfect5.1":{source:"iana"},"application/wsdl+xml":{source:"iana",compressible:!0,extensions:["wsdl"]},"application/wspolicy+xml":{source:"iana",compressible:!0,extensions:["wspolicy"]},"application/x-7z-compressed":{source:"apache",compressible:!1,extensions:["7z"]},"application/x-abiword":{source:"apache",extensions:["abw"]},"application/x-ace-compressed":{source:"apache",extensions:["ace"]},"application/x-amf":{source:"apache"},"application/x-apple-diskimage":{source:"apache",extensions:["dmg"]},"application/x-arj":{compressible:!1,extensions:["arj"]},"application/x-authorware-bin":{source:"apache",extensions:["aab","x32","u32","vox"]},"application/x-authorware-map":{source:"apache",extensions:["aam"]},"application/x-authorware-seg":{source:"apache",extensions:["aas"]},"application/x-bcpio":{source:"apache",extensions:["bcpio"]},"application/x-bdoc":{compressible:!1,extensions:["bdoc"]},"application/x-bittorrent":{source:"apache",extensions:["torrent"]},"application/x-blorb":{source:"apache",extensions:["blb","blorb"]},"application/x-bzip":{source:"apache",compressible:!1,extensions:["bz"]},"application/x-bzip2":{source:"apache",compressible:!1,extensions:["bz2","boz"]},"application/x-cbr":{source:"apache",extensions:["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{source:"apache",extensions:["vcd"]},"application/x-cfs-compressed":{source:"apache",extensions:["cfs"]},"application/x-chat":{source:"apache",extensions:["chat"]},"application/x-chess-pgn":{source:"apache",extensions:["pgn"]},"application/x-chrome-extension":{extensions:["crx"]},"application/x-cocoa":{source:"nginx",extensions:["cco"]},"application/x-compress":{source:"apache"},"application/x-conference":{source:"apache",extensions:["nsc"]},"application/x-cpio":{source:"apache",extensions:["cpio"]},"application/x-csh":{source:"apache",extensions:["csh"]},"application/x-deb":{compressible:!1},"application/x-debian-package":{source:"apache",extensions:["deb","udeb"]},"application/x-dgc-compressed":{source:"apache",extensions:["dgc"]},"application/x-director":{source:"apache",extensions:["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{source:"apache",extensions:["wad"]},"application/x-dtbncx+xml":{source:"apache",compressible:!0,extensions:["ncx"]},"application/x-dtbook+xml":{source:"apache",compressible:!0,extensions:["dtb"]},"application/x-dtbresource+xml":{source:"apache",compressible:!0,extensions:["res"]},"application/x-dvi":{source:"apache",compressible:!1,extensions:["dvi"]},"application/x-envoy":{source:"apache",extensions:["evy"]},"application/x-eva":{source:"apache",extensions:["eva"]},"application/x-font-bdf":{source:"apache",extensions:["bdf"]},"application/x-font-dos":{source:"apache"},"application/x-font-framemaker":{source:"apache"},"application/x-font-ghostscript":{source:"apache",extensions:["gsf"]},"application/x-font-libgrx":{source:"apache"},"application/x-font-linux-psf":{source:"apache",extensions:["psf"]},"application/x-font-pcf":{source:"apache",extensions:["pcf"]},"application/x-font-snf":{source:"apache",extensions:["snf"]},"application/x-font-speedo":{source:"apache"},"application/x-font-sunos-news":{source:"apache"},"application/x-font-type1":{source:"apache",extensions:["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{source:"apache"},"application/x-freearc":{source:"apache",extensions:["arc"]},"application/x-futuresplash":{source:"apache",extensions:["spl"]},"application/x-gca-compressed":{source:"apache",extensions:["gca"]},"application/x-glulx":{source:"apache",extensions:["ulx"]},"application/x-gnumeric":{source:"apache",extensions:["gnumeric"]},"application/x-gramps-xml":{source:"apache",extensions:["gramps"]},"application/x-gtar":{source:"apache",extensions:["gtar"]},"application/x-gzip":{source:"apache"},"application/x-hdf":{source:"apache",extensions:["hdf"]},"application/x-httpd-php":{compressible:!0,extensions:["php"]},"application/x-install-instructions":{source:"apache",extensions:["install"]},"application/x-iso9660-image":{source:"apache",extensions:["iso"]},"application/x-iwork-keynote-sffkey":{extensions:["key"]},"application/x-iwork-numbers-sffnumbers":{extensions:["numbers"]},"application/x-iwork-pages-sffpages":{extensions:["pages"]},"application/x-java-archive-diff":{source:"nginx",extensions:["jardiff"]},"application/x-java-jnlp-file":{source:"apache",compressible:!1,extensions:["jnlp"]},"application/x-javascript":{compressible:!0},"application/x-keepass2":{extensions:["kdbx"]},"application/x-latex":{source:"apache",compressible:!1,extensions:["latex"]},"application/x-lua-bytecode":{extensions:["luac"]},"application/x-lzh-compressed":{source:"apache",extensions:["lzh","lha"]},"application/x-makeself":{source:"nginx",extensions:["run"]},"application/x-mie":{source:"apache",extensions:["mie"]},"application/x-mobipocket-ebook":{source:"apache",extensions:["prc","mobi"]},"application/x-mpegurl":{compressible:!1},"application/x-ms-application":{source:"apache",extensions:["application"]},"application/x-ms-shortcut":{source:"apache",extensions:["lnk"]},"application/x-ms-wmd":{source:"apache",extensions:["wmd"]},"application/x-ms-wmz":{source:"apache",extensions:["wmz"]},"application/x-ms-xbap":{source:"apache",extensions:["xbap"]},"application/x-msaccess":{source:"apache",extensions:["mdb"]},"application/x-msbinder":{source:"apache",extensions:["obd"]},"application/x-mscardfile":{source:"apache",extensions:["crd"]},"application/x-msclip":{source:"apache",extensions:["clp"]},"application/x-msdos-program":{extensions:["exe"]},"application/x-msdownload":{source:"apache",extensions:["exe","dll","com","bat","msi"]},"application/x-msmediaview":{source:"apache",extensions:["mvb","m13","m14"]},"application/x-msmetafile":{source:"apache",extensions:["wmf","wmz","emf","emz"]},"application/x-msmoney":{source:"apache",extensions:["mny"]},"application/x-mspublisher":{source:"apache",extensions:["pub"]},"application/x-msschedule":{source:"apache",extensions:["scd"]},"application/x-msterminal":{source:"apache",extensions:["trm"]},"application/x-mswrite":{source:"apache",extensions:["wri"]},"application/x-netcdf":{source:"apache",extensions:["nc","cdf"]},"application/x-ns-proxy-autoconfig":{compressible:!0,extensions:["pac"]},"application/x-nzb":{source:"apache",extensions:["nzb"]},"application/x-perl":{source:"nginx",extensions:["pl","pm"]},"application/x-pilot":{source:"nginx",extensions:["prc","pdb"]},"application/x-pkcs12":{source:"apache",compressible:!1,extensions:["p12","pfx"]},"application/x-pkcs7-certificates":{source:"apache",extensions:["p7b","spc"]},"application/x-pkcs7-certreqresp":{source:"apache",extensions:["p7r"]},"application/x-pki-message":{source:"iana"},"application/x-rar-compressed":{source:"apache",compressible:!1,extensions:["rar"]},"application/x-redhat-package-manager":{source:"nginx",extensions:["rpm"]},"application/x-research-info-systems":{source:"apache",extensions:["ris"]},"application/x-sea":{source:"nginx",extensions:["sea"]},"application/x-sh":{source:"apache",compressible:!0,extensions:["sh"]},"application/x-shar":{source:"apache",extensions:["shar"]},"application/x-shockwave-flash":{source:"apache",compressible:!1,extensions:["swf"]},"application/x-silverlight-app":{source:"apache",extensions:["xap"]},"application/x-sql":{source:"apache",extensions:["sql"]},"application/x-stuffit":{source:"apache",compressible:!1,extensions:["sit"]},"application/x-stuffitx":{source:"apache",extensions:["sitx"]},"application/x-subrip":{source:"apache",extensions:["srt"]},"application/x-sv4cpio":{source:"apache",extensions:["sv4cpio"]},"application/x-sv4crc":{source:"apache",extensions:["sv4crc"]},"application/x-t3vm-image":{source:"apache",extensions:["t3"]},"application/x-tads":{source:"apache",extensions:["gam"]},"application/x-tar":{source:"apache",compressible:!0,extensions:["tar"]},"application/x-tcl":{source:"apache",extensions:["tcl","tk"]},"application/x-tex":{source:"apache",extensions:["tex"]},"application/x-tex-tfm":{source:"apache",extensions:["tfm"]},"application/x-texinfo":{source:"apache",extensions:["texinfo","texi"]},"application/x-tgif":{source:"apache",extensions:["obj"]},"application/x-ustar":{source:"apache",extensions:["ustar"]},"application/x-virtualbox-hdd":{compressible:!0,extensions:["hdd"]},"application/x-virtualbox-ova":{compressible:!0,extensions:["ova"]},"application/x-virtualbox-ovf":{compressible:!0,extensions:["ovf"]},"application/x-virtualbox-vbox":{compressible:!0,extensions:["vbox"]},"application/x-virtualbox-vbox-extpack":{compressible:!1,extensions:["vbox-extpack"]},"application/x-virtualbox-vdi":{compressible:!0,extensions:["vdi"]},"application/x-virtualbox-vhd":{compressible:!0,extensions:["vhd"]},"application/x-virtualbox-vmdk":{compressible:!0,extensions:["vmdk"]},"application/x-wais-source":{source:"apache",extensions:["src"]},"application/x-web-app-manifest+json":{compressible:!0,extensions:["webapp"]},"application/x-www-form-urlencoded":{source:"iana",compressible:!0},"application/x-x509-ca-cert":{source:"iana",extensions:["der","crt","pem"]},"application/x-x509-ca-ra-cert":{source:"iana"},"application/x-x509-next-ca-cert":{source:"iana"},"application/x-xfig":{source:"apache",extensions:["fig"]},"application/x-xliff+xml":{source:"apache",compressible:!0,extensions:["xlf"]},"application/x-xpinstall":{source:"apache",compressible:!1,extensions:["xpi"]},"application/x-xz":{source:"apache",extensions:["xz"]},"application/x-zmachine":{source:"apache",extensions:["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{source:"iana"},"application/xacml+xml":{source:"iana",compressible:!0},"application/xaml+xml":{source:"apache",compressible:!0,extensions:["xaml"]},"application/xcap-att+xml":{source:"iana",compressible:!0,extensions:["xav"]},"application/xcap-caps+xml":{source:"iana",compressible:!0,extensions:["xca"]},"application/xcap-diff+xml":{source:"iana",compressible:!0,extensions:["xdf"]},"application/xcap-el+xml":{source:"iana",compressible:!0,extensions:["xel"]},"application/xcap-error+xml":{source:"iana",compressible:!0},"application/xcap-ns+xml":{source:"iana",compressible:!0,extensions:["xns"]},"application/xcon-conference-info+xml":{source:"iana",compressible:!0},"application/xcon-conference-info-diff+xml":{source:"iana",compressible:!0},"application/xenc+xml":{source:"iana",compressible:!0,extensions:["xenc"]},"application/xhtml+xml":{source:"iana",compressible:!0,extensions:["xhtml","xht"]},"application/xhtml-voice+xml":{source:"apache",compressible:!0},"application/xliff+xml":{source:"iana",compressible:!0,extensions:["xlf"]},"application/xml":{source:"iana",compressible:!0,extensions:["xml","xsl","xsd","rng"]},"application/xml-dtd":{source:"iana",compressible:!0,extensions:["dtd"]},"application/xml-external-parsed-entity":{source:"iana"},"application/xml-patch+xml":{source:"iana",compressible:!0},"application/xmpp+xml":{source:"iana",compressible:!0},"application/xop+xml":{source:"iana",compressible:!0,extensions:["xop"]},"application/xproc+xml":{source:"apache",compressible:!0,extensions:["xpl"]},"application/xslt+xml":{source:"iana",compressible:!0,extensions:["xsl","xslt"]},"application/xspf+xml":{source:"apache",compressible:!0,extensions:["xspf"]},"application/xv+xml":{source:"iana",compressible:!0,extensions:["mxml","xhvml","xvml","xvm"]},"application/yang":{source:"iana",extensions:["yang"]},"application/yang-data+json":{source:"iana",compressible:!0},"application/yang-data+xml":{source:"iana",compressible:!0},"application/yang-patch+json":{source:"iana",compressible:!0},"application/yang-patch+xml":{source:"iana",compressible:!0},"application/yin+xml":{source:"iana",compressible:!0,extensions:["yin"]},"application/zip":{source:"iana",compressible:!1,extensions:["zip"]},"application/zlib":{source:"iana"},"application/zstd":{source:"iana"},"audio/1d-interleaved-parityfec":{source:"iana"},"audio/32kadpcm":{source:"iana"},"audio/3gpp":{source:"iana",compressible:!1,extensions:["3gpp"]},"audio/3gpp2":{source:"iana"},"audio/aac":{source:"iana"},"audio/ac3":{source:"iana"},"audio/adpcm":{source:"apache",extensions:["adp"]},"audio/amr":{source:"iana",extensions:["amr"]},"audio/amr-wb":{source:"iana"},"audio/amr-wb+":{source:"iana"},"audio/aptx":{source:"iana"},"audio/asc":{source:"iana"},"audio/atrac-advanced-lossless":{source:"iana"},"audio/atrac-x":{source:"iana"},"audio/atrac3":{source:"iana"},"audio/basic":{source:"iana",compressible:!1,extensions:["au","snd"]},"audio/bv16":{source:"iana"},"audio/bv32":{source:"iana"},"audio/clearmode":{source:"iana"},"audio/cn":{source:"iana"},"audio/dat12":{source:"iana"},"audio/dls":{source:"iana"},"audio/dsr-es201108":{source:"iana"},"audio/dsr-es202050":{source:"iana"},"audio/dsr-es202211":{source:"iana"},"audio/dsr-es202212":{source:"iana"},"audio/dv":{source:"iana"},"audio/dvi4":{source:"iana"},"audio/eac3":{source:"iana"},"audio/encaprtp":{source:"iana"},"audio/evrc":{source:"iana"},"audio/evrc-qcp":{source:"iana"},"audio/evrc0":{source:"iana"},"audio/evrc1":{source:"iana"},"audio/evrcb":{source:"iana"},"audio/evrcb0":{source:"iana"},"audio/evrcb1":{source:"iana"},"audio/evrcnw":{source:"iana"},"audio/evrcnw0":{source:"iana"},"audio/evrcnw1":{source:"iana"},"audio/evrcwb":{source:"iana"},"audio/evrcwb0":{source:"iana"},"audio/evrcwb1":{source:"iana"},"audio/evs":{source:"iana"},"audio/flexfec":{source:"iana"},"audio/fwdred":{source:"iana"},"audio/g711-0":{source:"iana"},"audio/g719":{source:"iana"},"audio/g722":{source:"iana"},"audio/g7221":{source:"iana"},"audio/g723":{source:"iana"},"audio/g726-16":{source:"iana"},"audio/g726-24":{source:"iana"},"audio/g726-32":{source:"iana"},"audio/g726-40":{source:"iana"},"audio/g728":{source:"iana"},"audio/g729":{source:"iana"},"audio/g7291":{source:"iana"},"audio/g729d":{source:"iana"},"audio/g729e":{source:"iana"},"audio/gsm":{source:"iana"},"audio/gsm-efr":{source:"iana"},"audio/gsm-hr-08":{source:"iana"},"audio/ilbc":{source:"iana"},"audio/ip-mr_v2.5":{source:"iana"},"audio/isac":{source:"apache"},"audio/l16":{source:"iana"},"audio/l20":{source:"iana"},"audio/l24":{source:"iana",compressible:!1},"audio/l8":{source:"iana"},"audio/lpc":{source:"iana"},"audio/melp":{source:"iana"},"audio/melp1200":{source:"iana"},"audio/melp2400":{source:"iana"},"audio/melp600":{source:"iana"},"audio/mhas":{source:"iana"},"audio/midi":{source:"apache",extensions:["mid","midi","kar","rmi"]},"audio/mobile-xmf":{source:"iana",extensions:["mxmf"]},"audio/mp3":{compressible:!1,extensions:["mp3"]},"audio/mp4":{source:"iana",compressible:!1,extensions:["m4a","mp4a"]},"audio/mp4a-latm":{source:"iana"},"audio/mpa":{source:"iana"},"audio/mpa-robust":{source:"iana"},"audio/mpeg":{source:"iana",compressible:!1,extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{source:"iana"},"audio/musepack":{source:"apache"},"audio/ogg":{source:"iana",compressible:!1,extensions:["oga","ogg","spx","opus"]},"audio/opus":{source:"iana"},"audio/parityfec":{source:"iana"},"audio/pcma":{source:"iana"},"audio/pcma-wb":{source:"iana"},"audio/pcmu":{source:"iana"},"audio/pcmu-wb":{source:"iana"},"audio/prs.sid":{source:"iana"},"audio/qcelp":{source:"iana"},"audio/raptorfec":{source:"iana"},"audio/red":{source:"iana"},"audio/rtp-enc-aescm128":{source:"iana"},"audio/rtp-midi":{source:"iana"},"audio/rtploopback":{source:"iana"},"audio/rtx":{source:"iana"},"audio/s3m":{source:"apache",extensions:["s3m"]},"audio/scip":{source:"iana"},"audio/silk":{source:"apache",extensions:["sil"]},"audio/smv":{source:"iana"},"audio/smv-qcp":{source:"iana"},"audio/smv0":{source:"iana"},"audio/sofa":{source:"iana"},"audio/sp-midi":{source:"iana"},"audio/speex":{source:"iana"},"audio/t140c":{source:"iana"},"audio/t38":{source:"iana"},"audio/telephone-event":{source:"iana"},"audio/tetra_acelp":{source:"iana"},"audio/tetra_acelp_bb":{source:"iana"},"audio/tone":{source:"iana"},"audio/tsvcis":{source:"iana"},"audio/uemclip":{source:"iana"},"audio/ulpfec":{source:"iana"},"audio/usac":{source:"iana"},"audio/vdvi":{source:"iana"},"audio/vmr-wb":{source:"iana"},"audio/vnd.3gpp.iufp":{source:"iana"},"audio/vnd.4sb":{source:"iana"},"audio/vnd.audiokoz":{source:"iana"},"audio/vnd.celp":{source:"iana"},"audio/vnd.cisco.nse":{source:"iana"},"audio/vnd.cmles.radio-events":{source:"iana"},"audio/vnd.cns.anp1":{source:"iana"},"audio/vnd.cns.inf1":{source:"iana"},"audio/vnd.dece.audio":{source:"iana",extensions:["uva","uvva"]},"audio/vnd.digital-winds":{source:"iana",extensions:["eol"]},"audio/vnd.dlna.adts":{source:"iana"},"audio/vnd.dolby.heaac.1":{source:"iana"},"audio/vnd.dolby.heaac.2":{source:"iana"},"audio/vnd.dolby.mlp":{source:"iana"},"audio/vnd.dolby.mps":{source:"iana"},"audio/vnd.dolby.pl2":{source:"iana"},"audio/vnd.dolby.pl2x":{source:"iana"},"audio/vnd.dolby.pl2z":{source:"iana"},"audio/vnd.dolby.pulse.1":{source:"iana"},"audio/vnd.dra":{source:"iana",extensions:["dra"]},"audio/vnd.dts":{source:"iana",extensions:["dts"]},"audio/vnd.dts.hd":{source:"iana",extensions:["dtshd"]},"audio/vnd.dts.uhd":{source:"iana"},"audio/vnd.dvb.file":{source:"iana"},"audio/vnd.everad.plj":{source:"iana"},"audio/vnd.hns.audio":{source:"iana"},"audio/vnd.lucent.voice":{source:"iana",extensions:["lvp"]},"audio/vnd.ms-playready.media.pya":{source:"iana",extensions:["pya"]},"audio/vnd.nokia.mobile-xmf":{source:"iana"},"audio/vnd.nortel.vbk":{source:"iana"},"audio/vnd.nuera.ecelp4800":{source:"iana",extensions:["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{source:"iana",extensions:["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{source:"iana",extensions:["ecelp9600"]},"audio/vnd.octel.sbc":{source:"iana"},"audio/vnd.presonus.multitrack":{source:"iana"},"audio/vnd.qcelp":{source:"iana"},"audio/vnd.rhetorex.32kadpcm":{source:"iana"},"audio/vnd.rip":{source:"iana",extensions:["rip"]},"audio/vnd.rn-realaudio":{compressible:!1},"audio/vnd.sealedmedia.softseal.mpeg":{source:"iana"},"audio/vnd.vmx.cvsd":{source:"iana"},"audio/vnd.wave":{compressible:!1},"audio/vorbis":{source:"iana",compressible:!1},"audio/vorbis-config":{source:"iana"},"audio/wav":{compressible:!1,extensions:["wav"]},"audio/wave":{compressible:!1,extensions:["wav"]},"audio/webm":{source:"apache",compressible:!1,extensions:["weba"]},"audio/x-aac":{source:"apache",compressible:!1,extensions:["aac"]},"audio/x-aiff":{source:"apache",extensions:["aif","aiff","aifc"]},"audio/x-caf":{source:"apache",compressible:!1,extensions:["caf"]},"audio/x-flac":{source:"apache",extensions:["flac"]},"audio/x-m4a":{source:"nginx",extensions:["m4a"]},"audio/x-matroska":{source:"apache",extensions:["mka"]},"audio/x-mpegurl":{source:"apache",extensions:["m3u"]},"audio/x-ms-wax":{source:"apache",extensions:["wax"]},"audio/x-ms-wma":{source:"apache",extensions:["wma"]},"audio/x-pn-realaudio":{source:"apache",extensions:["ram","ra"]},"audio/x-pn-realaudio-plugin":{source:"apache",extensions:["rmp"]},"audio/x-realaudio":{source:"nginx",extensions:["ra"]},"audio/x-tta":{source:"apache"},"audio/x-wav":{source:"apache",extensions:["wav"]},"audio/xm":{source:"apache",extensions:["xm"]},"chemical/x-cdx":{source:"apache",extensions:["cdx"]},"chemical/x-cif":{source:"apache",extensions:["cif"]},"chemical/x-cmdf":{source:"apache",extensions:["cmdf"]},"chemical/x-cml":{source:"apache",extensions:["cml"]},"chemical/x-csml":{source:"apache",extensions:["csml"]},"chemical/x-pdb":{source:"apache"},"chemical/x-xyz":{source:"apache",extensions:["xyz"]},"font/collection":{source:"iana",extensions:["ttc"]},"font/otf":{source:"iana",compressible:!0,extensions:["otf"]},"font/sfnt":{source:"iana"},"font/ttf":{source:"iana",compressible:!0,extensions:["ttf"]},"font/woff":{source:"iana",extensions:["woff"]},"font/woff2":{source:"iana",extensions:["woff2"]},"image/aces":{source:"iana",extensions:["exr"]},"image/apng":{compressible:!1,extensions:["apng"]},"image/avci":{source:"iana",extensions:["avci"]},"image/avcs":{source:"iana",extensions:["avcs"]},"image/avif":{source:"iana",compressible:!1,extensions:["avif"]},"image/bmp":{source:"iana",compressible:!0,extensions:["bmp"]},"image/cgm":{source:"iana",extensions:["cgm"]},"image/dicom-rle":{source:"iana",extensions:["drle"]},"image/emf":{source:"iana",extensions:["emf"]},"image/fits":{source:"iana",extensions:["fits"]},"image/g3fax":{source:"iana",extensions:["g3"]},"image/gif":{source:"iana",compressible:!1,extensions:["gif"]},"image/heic":{source:"iana",extensions:["heic"]},"image/heic-sequence":{source:"iana",extensions:["heics"]},"image/heif":{source:"iana",extensions:["heif"]},"image/heif-sequence":{source:"iana",extensions:["heifs"]},"image/hej2k":{source:"iana",extensions:["hej2"]},"image/hsj2":{source:"iana",extensions:["hsj2"]},"image/ief":{source:"iana",extensions:["ief"]},"image/jls":{source:"iana",extensions:["jls"]},"image/jp2":{source:"iana",compressible:!1,extensions:["jp2","jpg2"]},"image/jpeg":{source:"iana",compressible:!1,extensions:["jpeg","jpg","jpe"]},"image/jph":{source:"iana",extensions:["jph"]},"image/jphc":{source:"iana",extensions:["jhc"]},"image/jpm":{source:"iana",compressible:!1,extensions:["jpm"]},"image/jpx":{source:"iana",compressible:!1,extensions:["jpx","jpf"]},"image/jxr":{source:"iana",extensions:["jxr"]},"image/jxra":{source:"iana",extensions:["jxra"]},"image/jxrs":{source:"iana",extensions:["jxrs"]},"image/jxs":{source:"iana",extensions:["jxs"]},"image/jxsc":{source:"iana",extensions:["jxsc"]},"image/jxsi":{source:"iana",extensions:["jxsi"]},"image/jxss":{source:"iana",extensions:["jxss"]},"image/ktx":{source:"iana",extensions:["ktx"]},"image/ktx2":{source:"iana",extensions:["ktx2"]},"image/naplps":{source:"iana"},"image/pjpeg":{compressible:!1},"image/png":{source:"iana",compressible:!1,extensions:["png"]},"image/prs.btif":{source:"iana",extensions:["btif"]},"image/prs.pti":{source:"iana",extensions:["pti"]},"image/pwg-raster":{source:"iana"},"image/sgi":{source:"apache",extensions:["sgi"]},"image/svg+xml":{source:"iana",compressible:!0,extensions:["svg","svgz"]},"image/t38":{source:"iana",extensions:["t38"]},"image/tiff":{source:"iana",compressible:!1,extensions:["tif","tiff"]},"image/tiff-fx":{source:"iana",extensions:["tfx"]},"image/vnd.adobe.photoshop":{source:"iana",compressible:!0,extensions:["psd"]},"image/vnd.airzip.accelerator.azv":{source:"iana",extensions:["azv"]},"image/vnd.cns.inf2":{source:"iana"},"image/vnd.dece.graphic":{source:"iana",extensions:["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{source:"iana",extensions:["djvu","djv"]},"image/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"image/vnd.dwg":{source:"iana",extensions:["dwg"]},"image/vnd.dxf":{source:"iana",extensions:["dxf"]},"image/vnd.fastbidsheet":{source:"iana",extensions:["fbs"]},"image/vnd.fpx":{source:"iana",extensions:["fpx"]},"image/vnd.fst":{source:"iana",extensions:["fst"]},"image/vnd.fujixerox.edmics-mmr":{source:"iana",extensions:["mmr"]},"image/vnd.fujixerox.edmics-rlc":{source:"iana",extensions:["rlc"]},"image/vnd.globalgraphics.pgb":{source:"iana"},"image/vnd.microsoft.icon":{source:"iana",compressible:!0,extensions:["ico"]},"image/vnd.mix":{source:"iana"},"image/vnd.mozilla.apng":{source:"iana"},"image/vnd.ms-dds":{compressible:!0,extensions:["dds"]},"image/vnd.ms-modi":{source:"iana",extensions:["mdi"]},"image/vnd.ms-photo":{source:"apache",extensions:["wdp"]},"image/vnd.net-fpx":{source:"iana",extensions:["npx"]},"image/vnd.pco.b16":{source:"iana",extensions:["b16"]},"image/vnd.radiance":{source:"iana"},"image/vnd.sealed.png":{source:"iana"},"image/vnd.sealedmedia.softseal.gif":{source:"iana"},"image/vnd.sealedmedia.softseal.jpg":{source:"iana"},"image/vnd.svf":{source:"iana"},"image/vnd.tencent.tap":{source:"iana",extensions:["tap"]},"image/vnd.valve.source.texture":{source:"iana",extensions:["vtf"]},"image/vnd.wap.wbmp":{source:"iana",extensions:["wbmp"]},"image/vnd.xiff":{source:"iana",extensions:["xif"]},"image/vnd.zbrush.pcx":{source:"iana",extensions:["pcx"]},"image/webp":{source:"apache",extensions:["webp"]},"image/wmf":{source:"iana",extensions:["wmf"]},"image/x-3ds":{source:"apache",extensions:["3ds"]},"image/x-cmu-raster":{source:"apache",extensions:["ras"]},"image/x-cmx":{source:"apache",extensions:["cmx"]},"image/x-freehand":{source:"apache",extensions:["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{source:"apache",compressible:!0,extensions:["ico"]},"image/x-jng":{source:"nginx",extensions:["jng"]},"image/x-mrsid-image":{source:"apache",extensions:["sid"]},"image/x-ms-bmp":{source:"nginx",compressible:!0,extensions:["bmp"]},"image/x-pcx":{source:"apache",extensions:["pcx"]},"image/x-pict":{source:"apache",extensions:["pic","pct"]},"image/x-portable-anymap":{source:"apache",extensions:["pnm"]},"image/x-portable-bitmap":{source:"apache",extensions:["pbm"]},"image/x-portable-graymap":{source:"apache",extensions:["pgm"]},"image/x-portable-pixmap":{source:"apache",extensions:["ppm"]},"image/x-rgb":{source:"apache",extensions:["rgb"]},"image/x-tga":{source:"apache",extensions:["tga"]},"image/x-xbitmap":{source:"apache",extensions:["xbm"]},"image/x-xcf":{compressible:!1},"image/x-xpixmap":{source:"apache",extensions:["xpm"]},"image/x-xwindowdump":{source:"apache",extensions:["xwd"]},"message/cpim":{source:"iana"},"message/delivery-status":{source:"iana"},"message/disposition-notification":{source:"iana",extensions:["disposition-notification"]},"message/external-body":{source:"iana"},"message/feedback-report":{source:"iana"},"message/global":{source:"iana",extensions:["u8msg"]},"message/global-delivery-status":{source:"iana",extensions:["u8dsn"]},"message/global-disposition-notification":{source:"iana",extensions:["u8mdn"]},"message/global-headers":{source:"iana",extensions:["u8hdr"]},"message/http":{source:"iana",compressible:!1},"message/imdn+xml":{source:"iana",compressible:!0},"message/news":{source:"iana"},"message/partial":{source:"iana",compressible:!1},"message/rfc822":{source:"iana",compressible:!0,extensions:["eml","mime"]},"message/s-http":{source:"iana"},"message/sip":{source:"iana"},"message/sipfrag":{source:"iana"},"message/tracking-status":{source:"iana"},"message/vnd.si.simp":{source:"iana"},"message/vnd.wfa.wsc":{source:"iana",extensions:["wsc"]},"model/3mf":{source:"iana",extensions:["3mf"]},"model/e57":{source:"iana"},"model/gltf+json":{source:"iana",compressible:!0,extensions:["gltf"]},"model/gltf-binary":{source:"iana",compressible:!0,extensions:["glb"]},"model/iges":{source:"iana",compressible:!1,extensions:["igs","iges"]},"model/mesh":{source:"iana",compressible:!1,extensions:["msh","mesh","silo"]},"model/mtl":{source:"iana",extensions:["mtl"]},"model/obj":{source:"iana",extensions:["obj"]},"model/step":{source:"iana"},"model/step+xml":{source:"iana",compressible:!0,extensions:["stpx"]},"model/step+zip":{source:"iana",compressible:!1,extensions:["stpz"]},"model/step-xml+zip":{source:"iana",compressible:!1,extensions:["stpxz"]},"model/stl":{source:"iana",extensions:["stl"]},"model/vnd.collada+xml":{source:"iana",compressible:!0,extensions:["dae"]},"model/vnd.dwf":{source:"iana",extensions:["dwf"]},"model/vnd.flatland.3dml":{source:"iana"},"model/vnd.gdl":{source:"iana",extensions:["gdl"]},"model/vnd.gs-gdl":{source:"apache"},"model/vnd.gs.gdl":{source:"iana"},"model/vnd.gtw":{source:"iana",extensions:["gtw"]},"model/vnd.moml+xml":{source:"iana",compressible:!0},"model/vnd.mts":{source:"iana",extensions:["mts"]},"model/vnd.opengex":{source:"iana",extensions:["ogex"]},"model/vnd.parasolid.transmit.binary":{source:"iana",extensions:["x_b"]},"model/vnd.parasolid.transmit.text":{source:"iana",extensions:["x_t"]},"model/vnd.pytha.pyox":{source:"iana"},"model/vnd.rosette.annotated-data-model":{source:"iana"},"model/vnd.sap.vds":{source:"iana",extensions:["vds"]},"model/vnd.usdz+zip":{source:"iana",compressible:!1,extensions:["usdz"]},"model/vnd.valve.source.compiled-map":{source:"iana",extensions:["bsp"]},"model/vnd.vtu":{source:"iana",extensions:["vtu"]},"model/vrml":{source:"iana",compressible:!1,extensions:["wrl","vrml"]},"model/x3d+binary":{source:"apache",compressible:!1,extensions:["x3db","x3dbz"]},"model/x3d+fastinfoset":{source:"iana",extensions:["x3db"]},"model/x3d+vrml":{source:"apache",compressible:!1,extensions:["x3dv","x3dvz"]},"model/x3d+xml":{source:"iana",compressible:!0,extensions:["x3d","x3dz"]},"model/x3d-vrml":{source:"iana",extensions:["x3dv"]},"multipart/alternative":{source:"iana",compressible:!1},"multipart/appledouble":{source:"iana"},"multipart/byteranges":{source:"iana"},"multipart/digest":{source:"iana"},"multipart/encrypted":{source:"iana",compressible:!1},"multipart/form-data":{source:"iana",compressible:!1},"multipart/header-set":{source:"iana"},"multipart/mixed":{source:"iana"},"multipart/multilingual":{source:"iana"},"multipart/parallel":{source:"iana"},"multipart/related":{source:"iana",compressible:!1},"multipart/report":{source:"iana"},"multipart/signed":{source:"iana",compressible:!1},"multipart/vnd.bint.med-plus":{source:"iana"},"multipart/voice-message":{source:"iana"},"multipart/x-mixed-replace":{source:"iana"},"text/1d-interleaved-parityfec":{source:"iana"},"text/cache-manifest":{source:"iana",compressible:!0,extensions:["appcache","manifest"]},"text/calendar":{source:"iana",extensions:["ics","ifb"]},"text/calender":{compressible:!0},"text/cmd":{compressible:!0},"text/coffeescript":{extensions:["coffee","litcoffee"]},"text/cql":{source:"iana"},"text/cql-expression":{source:"iana"},"text/cql-identifier":{source:"iana"},"text/css":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["css"]},"text/csv":{source:"iana",compressible:!0,extensions:["csv"]},"text/csv-schema":{source:"iana"},"text/directory":{source:"iana"},"text/dns":{source:"iana"},"text/ecmascript":{source:"iana"},"text/encaprtp":{source:"iana"},"text/enriched":{source:"iana"},"text/fhirpath":{source:"iana"},"text/flexfec":{source:"iana"},"text/fwdred":{source:"iana"},"text/gff3":{source:"iana"},"text/grammar-ref-list":{source:"iana"},"text/html":{source:"iana",compressible:!0,extensions:["html","htm","shtml"]},"text/jade":{extensions:["jade"]},"text/javascript":{source:"iana",compressible:!0},"text/jcr-cnd":{source:"iana"},"text/jsx":{compressible:!0,extensions:["jsx"]},"text/less":{compressible:!0,extensions:["less"]},"text/markdown":{source:"iana",compressible:!0,extensions:["markdown","md"]},"text/mathml":{source:"nginx",extensions:["mml"]},"text/mdx":{compressible:!0,extensions:["mdx"]},"text/mizar":{source:"iana"},"text/n3":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["n3"]},"text/parameters":{source:"iana",charset:"UTF-8"},"text/parityfec":{source:"iana"},"text/plain":{source:"iana",compressible:!0,extensions:["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{source:"iana",charset:"UTF-8"},"text/prs.fallenstein.rst":{source:"iana"},"text/prs.lines.tag":{source:"iana",extensions:["dsc"]},"text/prs.prop.logic":{source:"iana"},"text/raptorfec":{source:"iana"},"text/red":{source:"iana"},"text/rfc822-headers":{source:"iana"},"text/richtext":{source:"iana",compressible:!0,extensions:["rtx"]},"text/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"text/rtp-enc-aescm128":{source:"iana"},"text/rtploopback":{source:"iana"},"text/rtx":{source:"iana"},"text/sgml":{source:"iana",extensions:["sgml","sgm"]},"text/shaclc":{source:"iana"},"text/shex":{source:"iana",extensions:["shex"]},"text/slim":{extensions:["slim","slm"]},"text/spdx":{source:"iana",extensions:["spdx"]},"text/strings":{source:"iana"},"text/stylus":{extensions:["stylus","styl"]},"text/t140":{source:"iana"},"text/tab-separated-values":{source:"iana",compressible:!0,extensions:["tsv"]},"text/troff":{source:"iana",extensions:["t","tr","roff","man","me","ms"]},"text/turtle":{source:"iana",charset:"UTF-8",extensions:["ttl"]},"text/ulpfec":{source:"iana"},"text/uri-list":{source:"iana",compressible:!0,extensions:["uri","uris","urls"]},"text/vcard":{source:"iana",compressible:!0,extensions:["vcard"]},"text/vnd.a":{source:"iana"},"text/vnd.abc":{source:"iana"},"text/vnd.ascii-art":{source:"iana"},"text/vnd.curl":{source:"iana",extensions:["curl"]},"text/vnd.curl.dcurl":{source:"apache",extensions:["dcurl"]},"text/vnd.curl.mcurl":{source:"apache",extensions:["mcurl"]},"text/vnd.curl.scurl":{source:"apache",extensions:["scurl"]},"text/vnd.debian.copyright":{source:"iana",charset:"UTF-8"},"text/vnd.dmclientscript":{source:"iana"},"text/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"text/vnd.esmertec.theme-descriptor":{source:"iana",charset:"UTF-8"},"text/vnd.familysearch.gedcom":{source:"iana",extensions:["ged"]},"text/vnd.ficlab.flt":{source:"iana"},"text/vnd.fly":{source:"iana",extensions:["fly"]},"text/vnd.fmi.flexstor":{source:"iana",extensions:["flx"]},"text/vnd.gml":{source:"iana"},"text/vnd.graphviz":{source:"iana",extensions:["gv"]},"text/vnd.hans":{source:"iana"},"text/vnd.hgl":{source:"iana"},"text/vnd.in3d.3dml":{source:"iana",extensions:["3dml"]},"text/vnd.in3d.spot":{source:"iana",extensions:["spot"]},"text/vnd.iptc.newsml":{source:"iana"},"text/vnd.iptc.nitf":{source:"iana"},"text/vnd.latex-z":{source:"iana"},"text/vnd.motorola.reflex":{source:"iana"},"text/vnd.ms-mediapackage":{source:"iana"},"text/vnd.net2phone.commcenter.command":{source:"iana"},"text/vnd.radisys.msml-basic-layout":{source:"iana"},"text/vnd.senx.warpscript":{source:"iana"},"text/vnd.si.uricatalogue":{source:"iana"},"text/vnd.sosi":{source:"iana"},"text/vnd.sun.j2me.app-descriptor":{source:"iana",charset:"UTF-8",extensions:["jad"]},"text/vnd.trolltech.linguist":{source:"iana",charset:"UTF-8"},"text/vnd.wap.si":{source:"iana"},"text/vnd.wap.sl":{source:"iana"},"text/vnd.wap.wml":{source:"iana",extensions:["wml"]},"text/vnd.wap.wmlscript":{source:"iana",extensions:["wmls"]},"text/vtt":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["vtt"]},"text/x-asm":{source:"apache",extensions:["s","asm"]},"text/x-c":{source:"apache",extensions:["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{source:"nginx",extensions:["htc"]},"text/x-fortran":{source:"apache",extensions:["f","for","f77","f90"]},"text/x-gwt-rpc":{compressible:!0},"text/x-handlebars-template":{extensions:["hbs"]},"text/x-java-source":{source:"apache",extensions:["java"]},"text/x-jquery-tmpl":{compressible:!0},"text/x-lua":{extensions:["lua"]},"text/x-markdown":{compressible:!0,extensions:["mkd"]},"text/x-nfo":{source:"apache",extensions:["nfo"]},"text/x-opml":{source:"apache",extensions:["opml"]},"text/x-org":{compressible:!0,extensions:["org"]},"text/x-pascal":{source:"apache",extensions:["p","pas"]},"text/x-processing":{compressible:!0,extensions:["pde"]},"text/x-sass":{extensions:["sass"]},"text/x-scss":{extensions:["scss"]},"text/x-setext":{source:"apache",extensions:["etx"]},"text/x-sfv":{source:"apache",extensions:["sfv"]},"text/x-suse-ymp":{compressible:!0,extensions:["ymp"]},"text/x-uuencode":{source:"apache",extensions:["uu"]},"text/x-vcalendar":{source:"apache",extensions:["vcs"]},"text/x-vcard":{source:"apache",extensions:["vcf"]},"text/xml":{source:"iana",compressible:!0,extensions:["xml"]},"text/xml-external-parsed-entity":{source:"iana"},"text/yaml":{compressible:!0,extensions:["yaml","yml"]},"video/1d-interleaved-parityfec":{source:"iana"},"video/3gpp":{source:"iana",extensions:["3gp","3gpp"]},"video/3gpp-tt":{source:"iana"},"video/3gpp2":{source:"iana",extensions:["3g2"]},"video/av1":{source:"iana"},"video/bmpeg":{source:"iana"},"video/bt656":{source:"iana"},"video/celb":{source:"iana"},"video/dv":{source:"iana"},"video/encaprtp":{source:"iana"},"video/ffv1":{source:"iana"},"video/flexfec":{source:"iana"},"video/h261":{source:"iana",extensions:["h261"]},"video/h263":{source:"iana",extensions:["h263"]},"video/h263-1998":{source:"iana"},"video/h263-2000":{source:"iana"},"video/h264":{source:"iana",extensions:["h264"]},"video/h264-rcdo":{source:"iana"},"video/h264-svc":{source:"iana"},"video/h265":{source:"iana"},"video/iso.segment":{source:"iana",extensions:["m4s"]},"video/jpeg":{source:"iana",extensions:["jpgv"]},"video/jpeg2000":{source:"iana"},"video/jpm":{source:"apache",extensions:["jpm","jpgm"]},"video/jxsv":{source:"iana"},"video/mj2":{source:"iana",extensions:["mj2","mjp2"]},"video/mp1s":{source:"iana"},"video/mp2p":{source:"iana"},"video/mp2t":{source:"iana",extensions:["ts"]},"video/mp4":{source:"iana",compressible:!1,extensions:["mp4","mp4v","mpg4"]},"video/mp4v-es":{source:"iana"},"video/mpeg":{source:"iana",compressible:!1,extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{source:"iana"},"video/mpv":{source:"iana"},"video/nv":{source:"iana"},"video/ogg":{source:"iana",compressible:!1,extensions:["ogv"]},"video/parityfec":{source:"iana"},"video/pointer":{source:"iana"},"video/quicktime":{source:"iana",compressible:!1,extensions:["qt","mov"]},"video/raptorfec":{source:"iana"},"video/raw":{source:"iana"},"video/rtp-enc-aescm128":{source:"iana"},"video/rtploopback":{source:"iana"},"video/rtx":{source:"iana"},"video/scip":{source:"iana"},"video/smpte291":{source:"iana"},"video/smpte292m":{source:"iana"},"video/ulpfec":{source:"iana"},"video/vc1":{source:"iana"},"video/vc2":{source:"iana"},"video/vnd.cctv":{source:"iana"},"video/vnd.dece.hd":{source:"iana",extensions:["uvh","uvvh"]},"video/vnd.dece.mobile":{source:"iana",extensions:["uvm","uvvm"]},"video/vnd.dece.mp4":{source:"iana"},"video/vnd.dece.pd":{source:"iana",extensions:["uvp","uvvp"]},"video/vnd.dece.sd":{source:"iana",extensions:["uvs","uvvs"]},"video/vnd.dece.video":{source:"iana",extensions:["uvv","uvvv"]},"video/vnd.directv.mpeg":{source:"iana"},"video/vnd.directv.mpeg-tts":{source:"iana"},"video/vnd.dlna.mpeg-tts":{source:"iana"},"video/vnd.dvb.file":{source:"iana",extensions:["dvb"]},"video/vnd.fvt":{source:"iana",extensions:["fvt"]},"video/vnd.hns.video":{source:"iana"},"video/vnd.iptvforum.1dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.1dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.2dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.2dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.ttsavc":{source:"iana"},"video/vnd.iptvforum.ttsmpeg2":{source:"iana"},"video/vnd.motorola.video":{source:"iana"},"video/vnd.motorola.videop":{source:"iana"},"video/vnd.mpegurl":{source:"iana",extensions:["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{source:"iana",extensions:["pyv"]},"video/vnd.nokia.interleaved-multimedia":{source:"iana"},"video/vnd.nokia.mp4vr":{source:"iana"},"video/vnd.nokia.videovoip":{source:"iana"},"video/vnd.objectvideo":{source:"iana"},"video/vnd.radgamettools.bink":{source:"iana"},"video/vnd.radgamettools.smacker":{source:"iana"},"video/vnd.sealed.mpeg1":{source:"iana"},"video/vnd.sealed.mpeg4":{source:"iana"},"video/vnd.sealed.swf":{source:"iana"},"video/vnd.sealedmedia.softseal.mov":{source:"iana"},"video/vnd.uvvu.mp4":{source:"iana",extensions:["uvu","uvvu"]},"video/vnd.vivo":{source:"iana",extensions:["viv"]},"video/vnd.youtube.yt":{source:"iana"},"video/vp8":{source:"iana"},"video/vp9":{source:"iana"},"video/webm":{source:"apache",compressible:!1,extensions:["webm"]},"video/x-f4v":{source:"apache",extensions:["f4v"]},"video/x-fli":{source:"apache",extensions:["fli"]},"video/x-flv":{source:"apache",compressible:!1,extensions:["flv"]},"video/x-m4v":{source:"apache",extensions:["m4v"]},"video/x-matroska":{source:"apache",compressible:!1,extensions:["mkv","mk3d","mks"]},"video/x-mng":{source:"apache",extensions:["mng"]},"video/x-ms-asf":{source:"apache",extensions:["asf","asx"]},"video/x-ms-vob":{source:"apache",extensions:["vob"]},"video/x-ms-wm":{source:"apache",extensions:["wm"]},"video/x-ms-wmv":{source:"apache",compressible:!1,extensions:["wmv"]},"video/x-ms-wmx":{source:"apache",extensions:["wmx"]},"video/x-ms-wvx":{source:"apache",extensions:["wvx"]},"video/x-msvideo":{source:"apache",extensions:["avi"]},"video/x-sgi-movie":{source:"apache",extensions:["movie"]},"video/x-smv":{source:"apache",extensions:["smv"]},"x-conference/x-cooltalk":{source:"apache",extensions:["ice"]},"x-shader/x-fragment":{compressible:!0},"x-shader/x-vertex":{compressible:!0}}});var xr=I((ax,gr)=>{/*!
 * mime-db
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2015-2022 Douglas Christopher Wilson
 * MIT Licensed
 */gr.exports=hr()});var Cr=I(Qe=>{"use strict";/*!
 * mime-types
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var On=xr(),SB=require("path").extname,wr=/^\s*([^;\s]*)(?:;|\s|$)/,TB=/^text\//i;Qe.charset=Qr;Qe.charsets={lookup:Qr};Qe.contentType=LB;Qe.extension=MB;Qe.extensions=Object.create(null);Qe.lookup=DB;Qe.types=Object.create(null);zB(Qe.extensions,Qe.types);function Qr(e){if(!e||typeof e!="string")return!1;var t=wr.exec(e),A=t&&On[t[1].toLowerCase()];return A&&A.charset?A.charset:t&&TB.test(t[1])?"UTF-8":!1}function LB(e){if(!e||typeof e!="string")return!1;var t=e.indexOf("/")===-1?Qe.lookup(e):e;if(!t)return!1;if(t.indexOf("charset")===-1){var A=Qe.charset(t);A&&(t+="; charset="+A.toLowerCase())}return t}function MB(e){if(!e||typeof e!="string")return!1;var t=wr.exec(e),A=t&&Qe.extensions[t[1].toLowerCase()];return!A||!A.length?!1:A[0]}function DB(e){if(!e||typeof e!="string")return!1;var t=SB("x."+e).toLowerCase().substr(1);return t&&Qe.types[t]||!1}function zB(e,t){var A=["nginx","apache",void 0,"iana"];Object.keys(On).forEach(function(i){var s=On[i],o=s.extensions;if(!(!o||!o.length)){e[i]=o;for(var r=0;r<o.length;r++){var a=o[r];if(t[a]){var c=A.indexOf(On[t[a]].source),l=A.indexOf(s.source);if(t[a]!=="application/octet-stream"&&(c>l||c===l&&t[a].substr(0,12)==="application/"))continue}t[a]=i}}})}});var Ur=I((cx,Er)=>{Er.exports=PB;function PB(e){var t=typeof setImmediate=="function"?setImmediate:typeof process=="object"&&typeof process.nextTick=="function"?process.nextTick:null;t?t(e):setTimeout(e,0)}});var qi=I((lx,vr)=>{var br=Ur();vr.exports=kB;function kB(e){var t=!1;return br(function(){t=!0}),function(n,i){t?e(n,i):br(function(){e(n,i)})}}});var $i=I((px,Fr)=>{Fr.exports=VB;function VB(e){Object.keys(e.jobs).forEach(XB.bind(e)),e.jobs={}}function XB(e){typeof this.jobs[e]=="function"&&this.jobs[e]()}});var Zi=I((ux,Nr)=>{var Hr=qi(),GB=$i();Nr.exports=JB;function JB(e,t,A,n){var i=A.keyedList?A.keyedList[A.index]:A.index;A.jobs[i]=jB(t,i,e[i],function(s,o){i in A.jobs&&(delete A.jobs[i],s?GB(A):A.results[i]=o,n(s,A.results))})}function jB(e,t,A,n){var i;return e.length==2?i=e(A,Hr(n)):i=e(A,t,Hr(n)),i}});var Yi=I((dx,yr)=>{yr.exports=WB;function WB(e,t){var A=!Array.isArray(e),n={index:0,keyedList:A||t?Object.keys(e):null,jobs:{},results:A?{}:[],size:A?Object.keys(e).length:e.length};return t&&n.keyedList.sort(A?t:function(i,s){return t(e[i],e[s])}),n}});var es=I((fx,Rr)=>{var qB=$i(),$B=qi();Rr.exports=ZB;function ZB(e){Object.keys(this.jobs).length&&(this.index=this.size,qB(this),$B(e)(null,this.results))}});var Ir=I((mx,Kr)=>{var YB=Zi(),eh=Yi(),th=es();Kr.exports=Ah;function Ah(e,t,A){for(var n=eh(e);n.index<(n.keyedList||e).length;)YB(e,t,n,function(i,s){if(i){A(i,s);return}if(Object.keys(n.jobs).length===0){A(null,n.results);return}}),n.index++;return th.bind(n,A)}});var ts=I((Bx,_n)=>{var Or=Zi(),nh=Yi(),ih=es();_n.exports=sh;_n.exports.ascending=_r;_n.exports.descending=oh;function sh(e,t,A,n){var i=nh(e,A);return Or(e,t,i,function s(o,r){if(o){n(o,r);return}if(i.index++,i.index<(i.keyedList||e).length){Or(e,t,i,s);return}n(null,i.results)}),ih.bind(i,n)}function _r(e,t){return e<t?-1:e>t?1:0}function oh(e,t){return-1*_r(e,t)}});var Tr=I((hx,Sr)=>{var ah=ts();Sr.exports=rh;function rh(e,t,A){return ah(e,t,null,A)}});var Mr=I((gx,Lr)=>{Lr.exports={parallel:Ir(),serial:Tr(),serialOrdered:ts()}});var zr=I((xx,Dr)=>{Dr.exports=function(e,t){return Object.keys(t).forEach(function(A){e[A]=e[A]||t[A]}),e}});var Vr=I((wx,kr)=>{var ss=Br(),Pr=require("util"),As=require("path"),ch=require("http"),lh=require("https"),ph=require("url").parse,uh=require("fs"),dh=require("stream").Stream,ns=Cr(),fh=Mr(),is=zr();kr.exports=T;Pr.inherits(T,ss);function T(e){if(!(this instanceof T))return new T(e);this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],ss.call(this),e=e||{};for(var t in e)this[t]=e[t]}T.LINE_BREAK=`\r
`;T.DEFAULT_CONTENT_TYPE="application/octet-stream";T.prototype.append=function(e,t,A){A=A||{},typeof A=="string"&&(A={filename:A});var n=ss.prototype.append.bind(this);if(typeof t=="number"&&(t=""+t),Pr.isArray(t)){this._error(new Error("Arrays are not supported."));return}var i=this._multiPartHeader(e,t,A),s=this._multiPartFooter();n(i),n(t),n(s),this._trackLength(i,t,A)};T.prototype._trackLength=function(e,t,A){var n=0;A.knownLength!=null?n+=+A.knownLength:Buffer.isBuffer(t)?n=t.length:typeof t=="string"&&(n=Buffer.byteLength(t)),this._valueLength+=n,this._overheadLength+=Buffer.byteLength(e)+T.LINE_BREAK.length,!(!t||!t.path&&!(t.readable&&t.hasOwnProperty("httpVersion"))&&!(t instanceof dh))&&(A.knownLength||this._valuesToMeasure.push(t))};T.prototype._lengthRetriever=function(e,t){e.hasOwnProperty("fd")?e.end!=null&&e.end!=1/0&&e.start!=null?t(null,e.end+1-(e.start?e.start:0)):uh.stat(e.path,function(A,n){var i;if(A){t(A);return}i=n.size-(e.start?e.start:0),t(null,i)}):e.hasOwnProperty("httpVersion")?t(null,+e.headers["content-length"]):e.hasOwnProperty("httpModule")?(e.on("response",function(A){e.pause(),t(null,+A.headers["content-length"])}),e.resume()):t("Unknown stream")};T.prototype._multiPartHeader=function(e,t,A){if(typeof A.header=="string")return A.header;var n=this._getContentDisposition(t,A),i=this._getContentType(t,A),s="",o={"Content-Disposition":["form-data",'name="'+e+'"'].concat(n||[]),"Content-Type":[].concat(i||[])};typeof A.header=="object"&&is(o,A.header);var r;for(var a in o)o.hasOwnProperty(a)&&(r=o[a],r!=null&&(Array.isArray(r)||(r=[r]),r.length&&(s+=a+": "+r.join("; ")+T.LINE_BREAK)));return"--"+this.getBoundary()+T.LINE_BREAK+s+T.LINE_BREAK};T.prototype._getContentDisposition=function(e,t){var A,n;return typeof t.filepath=="string"?A=As.normalize(t.filepath).replace(/\\/g,"/"):t.filename||e.name||e.path?A=As.basename(t.filename||e.name||e.path):e.readable&&e.hasOwnProperty("httpVersion")&&(A=As.basename(e.client._httpMessage.path||"")),A&&(n='filename="'+A+'"'),n};T.prototype._getContentType=function(e,t){var A=t.contentType;return!A&&e.name&&(A=ns.lookup(e.name)),!A&&e.path&&(A=ns.lookup(e.path)),!A&&e.readable&&e.hasOwnProperty("httpVersion")&&(A=e.headers["content-type"]),!A&&(t.filepath||t.filename)&&(A=ns.lookup(t.filepath||t.filename)),!A&&typeof e=="object"&&(A=T.DEFAULT_CONTENT_TYPE),A};T.prototype._multiPartFooter=function(){return function(e){var t=T.LINE_BREAK,A=this._streams.length===0;A&&(t+=this._lastBoundary()),e(t)}.bind(this)};T.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+T.LINE_BREAK};T.prototype.getHeaders=function(e){var t,A={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(t in e)e.hasOwnProperty(t)&&(A[t.toLowerCase()]=e[t]);return A};T.prototype.setBoundary=function(e){this._boundary=e};T.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary};T.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),t=this.getBoundary(),A=0,n=this._streams.length;A<n;A++)typeof this._streams[A]!="function"&&(Buffer.isBuffer(this._streams[A])?e=Buffer.concat([e,this._streams[A]]):e=Buffer.concat([e,Buffer.from(this._streams[A])]),(typeof this._streams[A]!="string"||this._streams[A].substring(2,t.length+2)!==t)&&(e=Buffer.concat([e,Buffer.from(T.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])};T.prototype._generateBoundary=function(){for(var e="--------------------------",t=0;t<24;t++)e+=Math.floor(Math.random()*10).toString(16);this._boundary=e};T.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(new Error("Cannot calculate proper length in synchronous way.")),e};T.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e};T.prototype.getLength=function(e){var t=this._overheadLength+this._valueLength;if(this._streams.length&&(t+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,t));return}fh.parallel(this._valuesToMeasure,this._lengthRetriever,function(A,n){if(A){e(A);return}n.forEach(function(i){t+=i}),e(null,t)})};T.prototype.submit=function(e,t){var A,n,i={method:"post"};return typeof e=="string"?(e=ph(e),n=is({port:e.port,path:e.pathname,host:e.hostname,protocol:e.protocol},i)):(n=is(e,i),n.port||(n.port=n.protocol=="https:"?443:80)),n.headers=this.getHeaders(e.headers),n.protocol=="https:"?A=lh.request(n):A=ch.request(n),this.getLength(function(s,o){if(s&&s!=="Unknown stream"){this._error(s);return}if(o&&A.setHeader("Content-Length",o),this.pipe(A),t){var r,a=function(c,l){return A.removeListener("error",a),A.removeListener("response",r),t.call(this,c,l)};r=a.bind(this,null),A.on("error",a),A.on("response",r)}}.bind(this)),A};T.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))};T.prototype.toString=function(){return"[object FormData]"}});var ic=I(nc=>{"use strict";var Nh=require("url").parse,yh={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},Rh=String.prototype.endsWith||function(e){return e.length<=this.length&&this.indexOf(e,this.length-e.length)!==-1};function Kh(e){var t=typeof e=="string"?Nh(e):e||{},A=t.protocol,n=t.host,i=t.port;if(typeof n!="string"||!n||typeof A!="string"||(A=A.split(":",1)[0],n=n.replace(/:\d*$/,""),i=parseInt(i)||yh[A]||0,!Ih(n,i)))return"";var s=$t("npm_config_"+A+"_proxy")||$t(A+"_proxy")||$t("npm_config_proxy")||$t("all_proxy");return s&&s.indexOf("://")===-1&&(s=A+"://"+s),s}function Ih(e,t){var A=($t("npm_config_no_proxy")||$t("no_proxy")).toLowerCase();return A?A==="*"?!1:A.split(/[,\s]/).every(function(n){if(!n)return!0;var i=n.match(/^(.+):(\d+)$/),s=i?i[1]:n,o=i?parseInt(i[2]):0;return o&&o!==t?!0:/^[.*]/.test(s)?(s.charAt(0)==="*"&&(s=s.slice(1)),!Rh.call(e,s)):e!==s}):!0}function $t(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}nc.getProxyForUrl=Kh});var oc=I((Qw,sc)=>{var NA=1e3,yA=NA*60,RA=yA*60,KA=RA*24,Oh=KA*365.25;sc.exports=function(e,t){t=t||{};var A=typeof e;if(A==="string"&&e.length>0)return _h(e);if(A==="number"&&isNaN(e)===!1)return t.long?Th(e):Sh(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function _h(e){if(e=String(e),!(e.length>100)){var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(t){var A=parseFloat(t[1]),n=(t[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return A*Oh;case"days":case"day":case"d":return A*KA;case"hours":case"hour":case"hrs":case"hr":case"h":return A*RA;case"minutes":case"minute":case"mins":case"min":case"m":return A*yA;case"seconds":case"second":case"secs":case"sec":case"s":return A*NA;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return A;default:return}}}}function Sh(e){return e>=KA?Math.round(e/KA)+"d":e>=RA?Math.round(e/RA)+"h":e>=yA?Math.round(e/yA)+"m":e>=NA?Math.round(e/NA)+"s":e+"ms"}function Th(e){return Dn(e,KA,"day")||Dn(e,RA,"hour")||Dn(e,yA,"minute")||Dn(e,NA,"second")||e+" ms"}function Dn(e,t,A){if(!(e<t))return e<t*1.5?Math.floor(e/t)+" "+A:Math.ceil(e/t)+" "+A+"s"}});var ms=I((M,ac)=>{M=ac.exports=fs.debug=fs.default=fs;M.coerce=Ph;M.disable=Dh;M.enable=Mh;M.enabled=zh;M.humanize=oc();M.names=[];M.skips=[];M.formatters={};var ds;function Lh(e){var t=0,A;for(A in e)t=(t<<5)-t+e.charCodeAt(A),t|=0;return M.colors[Math.abs(t)%M.colors.length]}function fs(e){function t(){if(t.enabled){var A=t,n=+new Date,i=n-(ds||n);A.diff=i,A.prev=ds,A.curr=n,ds=n;for(var s=new Array(arguments.length),o=0;o<s.length;o++)s[o]=arguments[o];s[0]=M.coerce(s[0]),typeof s[0]!="string"&&s.unshift("%O");var r=0;s[0]=s[0].replace(/%([a-zA-Z%])/g,function(c,l){if(c==="%%")return c;r++;var d=M.formatters[l];if(typeof d=="function"){var f=s[r];c=d.call(A,f),s.splice(r,1),r--}return c}),M.formatArgs.call(A,s);var a=t.log||M.log||console.log.bind(console);a.apply(A,s)}}return t.namespace=e,t.enabled=M.enabled(e),t.useColors=M.useColors(),t.color=Lh(e),typeof M.init=="function"&&M.init(t),t}function Mh(e){M.save(e),M.names=[],M.skips=[];for(var t=(typeof e=="string"?e:"").split(/[\s,]+/),A=t.length,n=0;n<A;n++)t[n]&&(e=t[n].replace(/\*/g,".*?"),e[0]==="-"?M.skips.push(new RegExp("^"+e.substr(1)+"$")):M.names.push(new RegExp("^"+e+"$")))}function Dh(){M.enable("")}function zh(e){var t,A;for(t=0,A=M.skips.length;t<A;t++)if(M.skips[t].test(e))return!1;for(t=0,A=M.names.length;t<A;t++)if(M.names[t].test(e))return!0;return!1}function Ph(e){return e instanceof Error?e.stack||e.message:e}});var lc=I((me,cc)=>{me=cc.exports=ms();me.log=Xh;me.formatArgs=Vh;me.save=Gh;me.load=rc;me.useColors=kh;me.storage=typeof chrome<"u"&&typeof chrome.storage<"u"?chrome.storage.local:Jh();me.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"];function kh(){return typeof window<"u"&&window.process&&window.process.type==="renderer"?!0:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}me.formatters.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}};function Vh(e){var t=this.useColors;if(e[0]=(t?"%c":"")+this.namespace+(t?" %c":" ")+e[0]+(t?"%c ":" ")+"+"+me.humanize(this.diff),!!t){var A="color: "+this.color;e.splice(1,0,A,"color: inherit");var n=0,i=0;e[0].replace(/%[a-zA-Z%]/g,function(s){s!=="%%"&&(n++,s==="%c"&&(i=n))}),e.splice(i,0,A)}}function Xh(){return typeof console=="object"&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}function Gh(e){try{e==null?me.storage.removeItem("debug"):me.storage.debug=e}catch{}}function rc(){var e;try{e=me.storage.debug}catch{}return!e&&typeof process<"u"&&"env"in process&&(e=process.env.DEBUG),e}me.enable(rc());function Jh(){try{return window.localStorage}catch{}}});var fc=I((se,dc)=>{var pc=require("tty"),IA=require("util");se=dc.exports=ms();se.init=eg;se.log=$h;se.formatArgs=qh;se.save=Zh;se.load=uc;se.useColors=Wh;se.colors=[6,2,3,4,5,1];se.inspectOpts=Object.keys(process.env).filter(function(e){return/^debug_/i.test(e)}).reduce(function(e,t){var A=t.substring(6).toLowerCase().replace(/_([a-z])/g,function(i,s){return s.toUpperCase()}),n=process.env[t];return/^(yes|on|true|enabled)$/i.test(n)?n=!0:/^(no|off|false|disabled)$/i.test(n)?n=!1:n==="null"?n=null:n=Number(n),e[A]=n,e},{});var Zt=parseInt(process.env.DEBUG_FD,10)||2;Zt!==1&&Zt!==2&&IA.deprecate(function(){},"except for stderr(2) and stdout(1), any other usage of DEBUG_FD is deprecated. Override debug.log if you want to use a different log function (https://git.io/debug_fd)")();var jh=Zt===1?process.stdout:Zt===2?process.stderr:Yh(Zt);function Wh(){return"colors"in se.inspectOpts?!!se.inspectOpts.colors:pc.isatty(Zt)}se.formatters.o=function(e){return this.inspectOpts.colors=this.useColors,IA.inspect(e,this.inspectOpts).split(`
`).map(function(t){return t.trim()}).join(" ")};se.formatters.O=function(e){return this.inspectOpts.colors=this.useColors,IA.inspect(e,this.inspectOpts)};function qh(e){var t=this.namespace,A=this.useColors;if(A){var n=this.color,i="  \x1B[3"+n+";1m"+t+" \x1B[0m";e[0]=i+e[0].split(`
`).join(`
`+i),e.push("\x1B[3"+n+"m+"+se.humanize(this.diff)+"\x1B[0m")}else e[0]=new Date().toUTCString()+" "+t+" "+e[0]}function $h(){return jh.write(IA.format.apply(IA,arguments)+`
`)}function Zh(e){e==null?delete process.env.DEBUG:process.env.DEBUG=e}function uc(){return process.env.DEBUG}function Yh(e){var t,A=process.binding("tty_wrap");switch(A.guessHandleType(e)){case"TTY":t=new pc.WriteStream(e),t._type="tty",t._handle&&t._handle.unref&&t._handle.unref();break;case"FILE":var n=require("fs");t=new n.SyncWriteStream(e,{autoClose:!1}),t._type="fs";break;case"PIPE":case"TCP":var i=require("net");t=new i.Socket({fd:e,readable:!1,writable:!0}),t.readable=!1,t.read=null,t._type="pipe",t._handle&&t._handle.unref&&t._handle.unref();break;default:throw new Error("Implement me. Unknown stream file type!")}return t.fd=e,t._isStdio=!0,t}function eg(e){e.inspectOpts={};for(var t=Object.keys(se.inspectOpts),A=0;A<t.length;A++)e.inspectOpts[t[A]]=se.inspectOpts[t[A]]}se.enable(uc())});var mc=I((Cw,Bs)=>{typeof process<"u"&&process.type==="renderer"?Bs.exports=lc():Bs.exports=fc()});var hc=I((Ew,Bc)=>{var OA;Bc.exports=function(){if(!OA){try{OA=mc()("follow-redirects")}catch{}typeof OA!="function"&&(OA=function(){})}OA.apply(null,arguments)}});var bc=I((Uw,Qs)=>{var Ht=require("url"),hs=Ht.URL,tg=require("http"),Ag=require("https"),wc=require("stream").Writable,Qc=require("assert"),Cc=hc(),xs=["abort","aborted","connect","error","socket","timeout"],ws=Object.create(null);xs.forEach(function(e){ws[e]=function(t,A,n){this._redirectable.emit(e,t,A,n)}});var ng=SA("ERR_INVALID_URL","Invalid URL",TypeError),gc=SA("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),ig=SA("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded"),sg=SA("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),og=SA("ERR_STREAM_WRITE_AFTER_END","write after end");function Ue(e,t){wc.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],t&&this.on("response",t);var A=this;this._onNativeResponse=function(n){A._processResponse(n)},this._performRequest()}Ue.prototype=Object.create(wc.prototype);Ue.prototype.abort=function(){Uc(this._currentRequest),this.emit("abort")};Ue.prototype.write=function(e,t,A){if(this._ending)throw new og;if(!Nt(e)&&!cg(e))throw new TypeError("data should be a string, Buffer or Uint8Array");if(_A(t)&&(A=t,t=null),e.length===0){A&&A();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:t}),this._currentRequest.write(e,t,A)):(this.emit("error",new sg),this.abort())};Ue.prototype.end=function(e,t,A){if(_A(e)?(A=e,e=t=null):_A(t)&&(A=t,t=null),!e)this._ended=this._ending=!0,this._currentRequest.end(null,null,A);else{var n=this,i=this._currentRequest;this.write(e,t,function(){n._ended=!0,i.end(null,null,A)}),this._ending=!0}};Ue.prototype.setHeader=function(e,t){this._options.headers[e]=t,this._currentRequest.setHeader(e,t)};Ue.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)};Ue.prototype.setTimeout=function(e,t){var A=this;function n(o){o.setTimeout(e),o.removeListener("timeout",o.destroy),o.addListener("timeout",o.destroy)}function i(o){A._timeout&&clearTimeout(A._timeout),A._timeout=setTimeout(function(){A.emit("timeout"),s()},e),n(o)}function s(){A._timeout&&(clearTimeout(A._timeout),A._timeout=null),A.removeListener("abort",s),A.removeListener("error",s),A.removeListener("response",s),t&&A.removeListener("timeout",t),A.socket||A._currentRequest.removeListener("socket",i)}return t&&this.on("timeout",t),this.socket?i(this.socket):this._currentRequest.once("socket",i),this.on("socket",n),this.on("abort",s),this.on("error",s),this.on("response",s),this};["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){Ue.prototype[e]=function(t,A){return this._currentRequest[e](t,A)}});["aborted","connection","socket"].forEach(function(e){Object.defineProperty(Ue.prototype,e,{get:function(){return this._currentRequest[e]}})});Ue.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var t=e.path.indexOf("?");t<0?e.pathname=e.path:(e.pathname=e.path.substring(0,t),e.search=e.path.substring(t))}};Ue.prototype._performRequest=function(){var e=this._options.protocol,t=this._options.nativeProtocols[e];if(!t){this.emit("error",new TypeError("Unsupported protocol "+e));return}if(this._options.agents){var A=e.slice(0,-1);this._options.agent=this._options.agents[A]}var n=this._currentRequest=t.request(this._options,this._onNativeResponse);n._redirectable=this;for(var i of xs)n.on(i,ws[i]);if(this._currentUrl=/^\//.test(this._options.path)?Ht.format(this._options):this._options.path,this._isRedirect){var s=0,o=this,r=this._requestBodyBuffers;(function a(c){if(n===o._currentRequest)if(c)o.emit("error",c);else if(s<r.length){var l=r[s++];n.finished||n.write(l.data,l.encoding,a)}else o._ended&&n.end()})()}};Ue.prototype._processResponse=function(e){var t=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:t});var A=e.headers.location;if(!A||this._options.followRedirects===!1||t<300||t>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(Uc(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects){this.emit("error",new ig);return}var n,i=this._options.beforeRedirect;i&&(n=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var s=this._options.method;((t===301||t===302)&&this._options.method==="POST"||t===303&&!/^(?:GET|HEAD)$/.test(this._options.method))&&(this._options.method="GET",this._requestBodyBuffers=[],gs(/^content-/i,this._options.headers));var o=gs(/^host$/i,this._options.headers),r=Ht.parse(this._currentUrl),a=o||r.host,c=/^\w+:/.test(A)?this._currentUrl:Ht.format(Object.assign(r,{host:a})),l;try{l=Ht.resolve(c,A)}catch(p){this.emit("error",new gc({cause:p}));return}Cc("redirecting to",l),this._isRedirect=!0;var d=Ht.parse(l);if(Object.assign(this._options,d),(d.protocol!==r.protocol&&d.protocol!=="https:"||d.host!==a&&!rg(d.host,a))&&gs(/^(?:authorization|cookie)$/i,this._options.headers),_A(i)){var f={headers:e.headers,statusCode:t},u={url:c,method:s,headers:n};try{i(this._options,f,u)}catch(p){this.emit("error",p);return}this._sanitizeOptions(this._options)}try{this._performRequest()}catch(p){this.emit("error",new gc({cause:p}))}};function Ec(e){var t={maxRedirects:21,maxBodyLength:10485760},A={};return Object.keys(e).forEach(function(n){var i=n+":",s=A[i]=e[n],o=t[n]=Object.create(s);function r(c,l,d){if(Nt(c)){var f;try{f=xc(new hs(c))}catch{f=Ht.parse(c)}if(!Nt(f.protocol))throw new ng({input:c});c=f}else hs&&c instanceof hs?c=xc(c):(d=l,l=c,c={protocol:i});return _A(l)&&(d=l,l=null),l=Object.assign({maxRedirects:t.maxRedirects,maxBodyLength:t.maxBodyLength},c,l),l.nativeProtocols=A,!Nt(l.host)&&!Nt(l.hostname)&&(l.hostname="::1"),Qc.equal(l.protocol,i,"protocol mismatch"),Cc("options",l),new Ue(l,d)}function a(c,l,d){var f=o.request(c,l,d);return f.end(),f}Object.defineProperties(o,{request:{value:r,configurable:!0,enumerable:!0,writable:!0},get:{value:a,configurable:!0,enumerable:!0,writable:!0}})}),t}function ag(){}function xc(e){var t={protocol:e.protocol,hostname:e.hostname.startsWith("[")?e.hostname.slice(1,-1):e.hostname,hash:e.hash,search:e.search,pathname:e.pathname,path:e.pathname+e.search,href:e.href};return e.port!==""&&(t.port=Number(e.port)),t}function gs(e,t){var A;for(var n in t)e.test(n)&&(A=t[n],delete t[n]);return A===null||typeof A>"u"?void 0:String(A).trim()}function SA(e,t,A){function n(i){Error.captureStackTrace(this,this.constructor),Object.assign(this,i||{}),this.code=e,this.message=this.cause?t+": "+this.cause.message:t}return n.prototype=new(A||Error),n.prototype.constructor=n,n.prototype.name="Error ["+e+"]",n}function Uc(e){for(var t of xs)e.removeListener(t,ws[t]);e.on("error",ag),e.abort()}function rg(e,t){Qc(Nt(e)&&Nt(t));var A=e.length-t.length-1;return A>0&&e[A]==="."&&e.endsWith(t)}function Nt(e){return typeof e=="string"||e instanceof String}function _A(e){return typeof e=="function"}function cg(e){return typeof e=="object"&&"length"in e}Qs.exports=Ec({http:tg,https:Ag});Qs.exports.wrap=Ec});var Rg={};pl(Rg,{EMExtensionsMixin:()=>Os,EV_CameraManager:()=>PA,EV_GroupManager:()=>kA,EV_ViewPointManager:()=>il});module.exports=ul(Rg);var X={_maximumCombinedTextureImageUnits:0,_maximumCubeMapSize:0,_maximumFragmentUniformVectors:0,_maximumTextureImageUnits:0,_maximumRenderbufferSize:0,_maximumTextureSize:0,_maximumVaryingVectors:0,_maximumVertexAttributes:0,_maximumVertexTextureImageUnits:0,_maximumVertexUniformVectors:0,_minimumAliasedLineWidth:0,_maximumAliasedLineWidth:0,_minimumAliasedPointSize:0,_maximumAliasedPointSize:0,_maximumViewportWidth:0,_maximumViewportHeight:0,_maximumTextureFilterAnisotropy:0,_maximumDrawBuffers:0,_maximumColorAttachments:0,_maximumSamples:0,_highpFloatSupported:!1,_highpIntSupported:!1};Object.defineProperties(X,{maximumCombinedTextureImageUnits:{get:function(){return X._maximumCombinedTextureImageUnits}},maximumCubeMapSize:{get:function(){return X._maximumCubeMapSize}},maximumFragmentUniformVectors:{get:function(){return X._maximumFragmentUniformVectors}},maximumTextureImageUnits:{get:function(){return X._maximumTextureImageUnits}},maximumRenderbufferSize:{get:function(){return X._maximumRenderbufferSize}},maximumTextureSize:{get:function(){return X._maximumTextureSize}},maximumVaryingVectors:{get:function(){return X._maximumVaryingVectors}},maximumVertexAttributes:{get:function(){return X._maximumVertexAttributes}},maximumVertexTextureImageUnits:{get:function(){return X._maximumVertexTextureImageUnits}},maximumVertexUniformVectors:{get:function(){return X._maximumVertexUniformVectors}},minimumAliasedLineWidth:{get:function(){return X._minimumAliasedLineWidth}},maximumAliasedLineWidth:{get:function(){return X._maximumAliasedLineWidth}},minimumAliasedPointSize:{get:function(){return X._minimumAliasedPointSize}},maximumAliasedPointSize:{get:function(){return X._maximumAliasedPointSize}},maximumViewportWidth:{get:function(){return X._maximumViewportWidth}},maximumViewportHeight:{get:function(){return X._maximumViewportHeight}},maximumTextureFilterAnisotropy:{get:function(){return X._maximumTextureFilterAnisotropy}},maximumDrawBuffers:{get:function(){return X._maximumDrawBuffers}},maximumColorAttachments:{get:function(){return X._maximumColorAttachments}},maximumSamples:{get:function(){return X._maximumSamples}},highpFloatSupported:{get:function(){return X._highpFloatSupported}},highpIntSupported:{get:function(){return X._highpIntSupported}}});var W=X;function dl(e,t){W._maximumCombinedTextureImageUnits=Cesium.ContextLimits._maximumCombinedTextureImageUnits,W._maximumCubeMapSize=Cesium.ContextLimits._maximumCubeMapSize,W._maximumFragmentUniformVectors=Cesium.ContextLimits._maximumFragmentUniformVectors,W._maximumTextureImageUnits=Cesium.ContextLimits._maximumTextureImageUnits,W._maximumRenderbufferSize=Cesium.ContextLimits._maximumRenderbufferSize,W._maximumTextureSize=Cesium.ContextLimits._maximumTextureSize,W._maximumVertexAttributes=Cesium.ContextLimits._maximumVertexAttributes,W._maximumVertexTextureImageUnits=Cesium.ContextLimits._maximumVertexTextureImageUnits,W._maximumVertexUniformVectors=Cesium.ContextLimits._maximumVertexUniformVectors,W._minimumAliasedLineWidth=Cesium.ContextLimits._minimumAliasedLineWidth,W._maximumAliasedLineWidth=Cesium.ContextLimits._maximumAliasedLineWidth,W._minimumAliasedPointSize=Cesium.ContextLimits._minimumAliasedPointSize,W._maximumAliasedPointSize=Cesium.ContextLimits._maximumAliasedPointSize,W._maximumViewportWidth=Cesium.ContextLimits._maximumViewportWidth,W._maximumViewportHeight=Cesium.ContextLimits._maximumViewportHeight,W._highpFloatSupported=Cesium.ContextLimits._highpFloatSupported,W._highpIntSupported=Cesium.ContextLimits._highpIntSupported,W._maximumTextureFilterAnisotropy=Cesium.ContextLimits._maximumTextureFilterAnisotropy,W._maximumDrawBuffers=Cesium.ContextLimits._maximumDrawBuffers,W._maximumColorAttachments=Cesium.ContextLimits._maximumColorAttachments}var Os=dl;var Wn=class{constructor(t,A){this.viewer=t,this.olMap=A}cameraJump(t,A){let n=fl(t,A),i=Cesium.Cartesian3.fromDegrees(n.CamLon,n.CamLat,n.CamAltitude),s=Cesium.Math.toRadians(n.CamHeading),o=Cesium.Math.toRadians(n.CamTilt-90),r=Cesium.Math.toRadians(n.CamRoll),{heading:a,pitch:c,roll:l,position:d}=this.viewer.camera;Cesium.Math.equalsEpsilon(c,o,Cesium.Math.EPSILON7)&&Cesium.Math.equalsEpsilon(a,s,Cesium.Math.EPSILON9)&&Cesium.Math.equalsEpsilon(l,r,Cesium.Math.EPSILON9)&&Cesium.Math.equalsEpsilon(d.x,i.x,Cesium.Math.EPSILON12)&&Cesium.Math.equalsEpsilon(d.y,i.y,Cesium.Math.EPSILON12)&&Cesium.Math.equalsEpsilon(d.z,i.z,Cesium.Math.EPSILON12)||this.viewer.camera.flyTo({destination:i,orientation:{heading:s,pitch:o,roll:r},duration:6})}setView(t,A){let n=ml(this.olMap,t,A);this.olMap.getView().setZoom(n.scale),this.olMap.getView().setCenter(n.coordinate)}setClickEvent(){this.viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(()=>{this.viewer.camera.cancelFlight()},Cesium.ScreenSpaceEventType.LEFT_CLICK),this.viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(()=>{this.viewer.camera.cancelFlight()},Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(()=>{this.viewer.camera.cancelFlight()},Cesium.ScreenSpaceEventType.MIDDLE_DOWN)}destoryClickEvent(){this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.MIDDLE_DOWN)}};function fl(e,t){let A={};if(e.hasOwnProperty("ViewPoint")){let l=e.ViewPoint;l instanceof Array?A=l.find(d=>d.guid==t):A=l}else A=e;let n={},{CamLon:i,CamLat:s,CamAltitude:o,CamHeading:r,CamTilt:a,CamRoll:c}=A;return s=parseFloat(s),i=parseFloat(i),o=parseFloat(o),r=parseFloat(r),a=parseFloat(a),c=parseFloat(c),n={CamLat:s,CamLon:i,CamAltitude:o,CamHeading:r,CamTilt:a,CamRoll:c},n}function ml(e,t,A){let n={};if(t.hasOwnProperty("ViewPoint")){let l=t.ViewPoint;l instanceof Array?n=l.find(d=>d.guid==A):n=l}else n=t;let{CenterX:i,CenterY:s,CurrentScale:o}=n.Layer2D,r=[parseFloat(i),parseFloat(s)],a=parseFloat(o),c=e.getView().getProjection();return c.code_==="EPSG:3857"?(a=e.getView().getZoomForResolution(a/(96*39.37001*11e4)*111319),r=ol.proj.transform(r,"EPSG:4326",c)):a=e.getView().getZoomForResolution(a/(96*39.37001*11e4)),{coordinate:r,scale:a}}var PA=Wn;var po=Be(oi(),1),ai=class{handleGroup(t){let A={ViewPointProjectList:{Project:[]}},n=t.map(s=>{let o={"@link_foldername":"","@project_name":"","@project_id":"","@createTime":"","@modifyTime":"","@isExpanded":""};return o["@link_foldername"]=s.link_foldername,o["@project_name"]=s.project_name,o["@project_id"]=s.project_id,o["@createTime"]=s.createTime,o["@modifyTime"]=s.modifyTime,o["@isExpanded"]=s.isExpanded,o});return A.ViewPointProjectList.Project=n,hp(A)}};function hp(e,t="@"){let A={ignoreAttributes:!1,attributeNamePrefix:t,format:!0};return new po.XMLBuilder(A).build(e)}var kA=ai;/*!
 * html2canvas 1.0.0-rc.5 <https://html2canvas.hertzen.com>
 * Copyright (c) 2020 Niklas von Hertzen <https://hertzen.com>
 * Released under MIT License
 *//*! *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
    ***************************************************************************** */var Qi=function(e,t){return Qi=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,n){A.__proto__=n}||function(A,n){for(let i in n)n.hasOwnProperty(i)&&(A[i]=n[i])},Qi(e,t)};function qe(e,t){Qi(e,t);function A(){this.constructor=e}e.prototype=t===null?Object.create(t):(A.prototype=t.prototype,new A)}var rn=function(){return rn=Object.assign||function(t){for(let A,n=1,i=arguments.length;n<i;n++){A=arguments[n];for(let s in A)Object.prototype.hasOwnProperty.call(A,s)&&(t[s]=A[s])}return t},rn.apply(this,arguments)};function Ee(e,t,A,n){return new(A||(A=Promise))(function(i,s){function o(c){try{a(n.next(c))}catch(l){s(l)}}function r(c){try{a(n.throw(c))}catch(l){s(l)}}function a(c){c.done?i(c.value):new A(function(l){l(c.value)}).then(o,r)}a((n=n.apply(e,t||[])).next())})}function xe(e,t){let A,n,i,s,o={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]};return s={next:r(0),throw:r(1),return:r(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function r(c){return function(l){return a([c,l])}}function a(c){if(A)throw new TypeError("Generator is already executing.");for(;o;)try{if(A=1,n&&(i=c[0]&2?n.return:c[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,c[1])).done)return i;switch(n=0,i&&(c=[c[0]&2,i.value]),c[0]){case 0:case 1:i=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,n=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(i=o.trys,!(i=i.length>0&&i[i.length-1])&&(c[0]===6||c[0]===2)){o=0;continue}if(c[0]===3&&(!i||c[1]>i[0]&&c[1]<i[3])){o.label=c[1];break}if(c[0]===6&&o.label<i[1]){o.label=i[1],i=c;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(c);break}i[2]&&o.ops.pop(),o.trys.pop();continue}c=t.call(e,o)}catch(l){c=[6,l],n=0}finally{A=i=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}var ut=function(){function e(t,A,n,i){this.left=t,this.top=A,this.width=n,this.height=i}return e.prototype.add=function(t,A,n,i){return new e(this.left+t,this.top+A,this.width+n,this.height+i)},e.fromClientRect=function(t){return new e(t.left,t.top,t.width,t.height)},e}(),Mi=function(e){return ut.fromClientRect(e.getBoundingClientRect())},gp=function(e){let t=e.body,A=e.documentElement;if(!t||!A)throw new Error("Unable to get document size");let n=Math.max(Math.max(t.scrollWidth,A.scrollWidth),Math.max(t.offsetWidth,A.offsetWidth),Math.max(t.clientWidth,A.clientWidth)),i=Math.max(Math.max(t.scrollHeight,A.scrollHeight),Math.max(t.offsetHeight,A.offsetHeight),Math.max(t.clientHeight,A.clientHeight));return new ut(0,0,n,i)},Qn=function(e){let t=[],A=0,n=e.length;for(;A<n;){let i=e.charCodeAt(A++);if(i>=55296&&i<=56319&&A<n){let s=e.charCodeAt(A++);(s&64512)===56320?t.push(((i&1023)<<10)+(s&1023)+65536):(t.push(i),A--)}else t.push(i)}return t},Z=function(){let e=[];for(let s=0;s<arguments.length;s++)e[s]=arguments[s];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);let t=e.length;if(!t)return"";let A=[],n=-1,i="";for(;++n<t;){let s=e[n];s<=65535?A.push(s):(s-=65536,A.push((s>>10)+55296,s%1024+56320)),(n+1===t||A.length>16384)&&(i+=String.fromCharCode.apply(String,A),A.length=0)}return i},uo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",rA=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<uo.length;e++)rA[uo.charCodeAt(e)]=e;var xp=function(e){let t=e.length*.75,A,n=0,i,s,o,r,a=e.length;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);let c=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(t):new Array(t),l=Array.isArray(c)?c:new Uint8Array(c);for(A=0;A<a;A+=4)i=rA[e.charCodeAt(A)],s=rA[e.charCodeAt(A+1)],o=rA[e.charCodeAt(A+2)],r=rA[e.charCodeAt(A+3)],l[n++]=i<<2|s>>4,l[n++]=(s&15)<<4|o>>2,l[n++]=(o&3)<<6|r&63;return c},wp=function(e){let t=e.length,A=[];for(let n=0;n<t;n+=2)A.push(e[n+1]<<8|e[n]);return A},Qp=function(e){let t=e.length,A=[];for(let n=0;n<t;n+=4)A.push(e[n+3]<<24|e[n+2]<<16|e[n+1]<<8|e[n]);return A},bt=5,Di=6+5,ri=2,Cp=Di-bt,$o=65536>>bt,Ep=1<<bt,ci=Ep-1,Up=1024>>bt,bp=$o+Up,vp=bp,Fp=32,Hp=vp+Fp,Np=65536>>Di,yp=1<<Cp,Rp=yp-1,fo=function(e,t,A){return e.slice?e.slice(t,A):new Uint16Array(Array.prototype.slice.call(e,t,A))},Kp=function(e,t,A){return e.slice?e.slice(t,A):new Uint32Array(Array.prototype.slice.call(e,t,A))},Ip=function(e){let t=xp(e),A=Array.isArray(t)?Qp(t):new Uint32Array(t),n=Array.isArray(t)?wp(t):new Uint16Array(t),i=24,s=fo(n,i/2,A[4]/2),o=A[5]===2?fo(n,(i+A[4])/2):Kp(A,Math.ceil((i+A[4])/4));return new Op(A[0],A[1],A[2],A[3],s,o)},Op=function(){function e(t,A,n,i,s,o){this.initialValue=t,this.errorValue=A,this.highStart=n,this.highValueIndex=i,this.index=s,this.data=o}return e.prototype.get=function(t){let A;if(t>=0){if(t<55296||t>56319&&t<=65535)return A=this.index[t>>bt],A=(A<<ri)+(t&ci),this.data[A];if(t<=65535)return A=this.index[$o+(t-55296>>bt)],A=(A<<ri)+(t&ci),this.data[A];if(t<this.highStart)return A=Hp-Np+(t>>Di),A=this.index[A],A+=t>>bt&Rp,A=this.index[A],A=(A<<ri)+(t&ci),this.data[A];if(t<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),_p="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",mo=50,Sp=1,Zo=2,Yo=3,Tp=4,Lp=5,Bo=7,ea=8,ho=9,at=10,ta=11,go=12,Ci=13,Mp=14,cA=15,Ei=16,VA=17,AA=18,xo=19,li=20,Ui=21,nA=22,pi=23,Kt=24,ge=25,lA=26,pA=27,It=28,Dp=29,Ct=30,zp=31,iA=32,sA=33,bi=34,vi=35,Fi=36,Et=37,Hi=38,on=39,an=40,ui=41,Aa=42,Pp=43,na="!",_="\xD7",XA="\xF7",ia=Ip(_p),_e=[Ct,Fi],Ni=[Sp,Zo,Yo,Lp],sa=[at,ea],wo=[pA,lA],kp=Ni.concat(sa),Qo=[Hi,on,an,bi,vi],Vp=[cA,Ci],Xp=function(e,t){t===void 0&&(t="strict");let A=[],n=[],i=[];return e.forEach(function(s,o){let r=ia.get(s);if(r>mo?(i.push(!0),r-=mo):i.push(!1),["normal","auto","loose"].indexOf(t)!==-1&&[8208,8211,12316,12448].indexOf(s)!==-1)return n.push(o),A.push(Ei);if(r===Tp||r===ta){if(o===0)return n.push(o),A.push(Ct);let a=A[o-1];return kp.indexOf(a)===-1?(n.push(n[o-1]),A.push(a)):(n.push(o),A.push(Ct))}if(n.push(o),r===zp)return A.push(t==="strict"?Ui:Et);if(r===Aa||r===Dp)return A.push(Ct);if(r===Pp)return s>=131072&&s<=196605||s>=196608&&s<=262141?A.push(Et):A.push(Ct);A.push(r)}),[n,A,i]},di=function(e,t,A,n){let i=n[A];if(Array.isArray(e)?e.indexOf(i)!==-1:e===i){let r=A;for(;r<=n.length;){r++;let a=n[r];if(a===t)return!0;if(a!==at)break}}if(i===at)for(var s=A;s>0;){s--;let r=n[s];if(Array.isArray(e)?e.indexOf(r)!==-1:e===r){let a=A;for(;a<=n.length;){a++;var o=n[a];if(o===t)return!0;if(o!==at)break}}if(r!==at)break}return!1},Co=function(e,t){let A=e;for(;A>=0;){let n=t[A];if(n===at)A--;else return n}return 0},Gp=function(e,t,A,n,i){if(A[n]===0)return _;let s=n-1;if(Array.isArray(i)&&i[s]===!0)return _;let o=s-1,r=s+1,a=t[s],c=o>=0?t[o]:0,l=t[r];if(a===Zo&&l===Yo)return _;if(Ni.indexOf(a)!==-1)return na;if(Ni.indexOf(l)!==-1||sa.indexOf(l)!==-1)return _;if(Co(s,t)===ea)return XA;if(ia.get(e[s])===ta&&(l===Et||l===iA||l===sA)||a===Bo||l===Bo||a===ho||[at,Ci,cA].indexOf(a)===-1&&l===ho||[VA,AA,xo,Kt,It].indexOf(l)!==-1||Co(s,t)===nA||di(pi,nA,s,t)||di([VA,AA],Ui,s,t)||di(go,go,s,t))return _;if(a===at)return XA;if(a===pi||l===pi)return _;if(l===Ei||a===Ei)return XA;if([Ci,cA,Ui].indexOf(l)!==-1||a===Mp||c===Fi&&Vp.indexOf(a)!==-1||a===It&&l===Fi||l===li&&_e.concat(li,xo,ge,Et,iA,sA).indexOf(a)!==-1||_e.indexOf(l)!==-1&&a===ge||_e.indexOf(a)!==-1&&l===ge||a===pA&&[Et,iA,sA].indexOf(l)!==-1||[Et,iA,sA].indexOf(a)!==-1&&l===lA||_e.indexOf(a)!==-1&&wo.indexOf(l)!==-1||wo.indexOf(a)!==-1&&_e.indexOf(l)!==-1||[pA,lA].indexOf(a)!==-1&&(l===ge||[nA,cA].indexOf(l)!==-1&&t[r+1]===ge)||[nA,cA].indexOf(a)!==-1&&l===ge||a===ge&&[ge,It,Kt].indexOf(l)!==-1)return _;if([ge,It,Kt,VA,AA].indexOf(l)!==-1){let u=s;for(;u>=0;){let p=t[u];if(p===ge)return _;if([It,Kt].indexOf(p)!==-1)u--;else break}}if([pA,lA].indexOf(l)!==-1)for(var d=[VA,AA].indexOf(a)!==-1?o:s;d>=0;){var f=t[d];if(f===ge)return _;if([It,Kt].indexOf(f)!==-1)d--;else break}if(Hi===a&&[Hi,on,bi,vi].indexOf(l)!==-1||[on,bi].indexOf(a)!==-1&&[on,an].indexOf(l)!==-1||[an,vi].indexOf(a)!==-1&&l===an||Qo.indexOf(a)!==-1&&[li,lA].indexOf(l)!==-1||Qo.indexOf(l)!==-1&&a===pA||_e.indexOf(a)!==-1&&_e.indexOf(l)!==-1||a===Kt&&_e.indexOf(l)!==-1||_e.concat(ge).indexOf(a)!==-1&&l===nA||_e.concat(ge).indexOf(l)!==-1&&a===AA)return _;if(a===ui&&l===ui){let u=A[s],p=1;for(;u>0&&(u--,t[u]===ui);)p++;if(p%2!==0)return _}return a===iA&&l===sA?_:XA},Jp=function(e,t){t||(t={lineBreak:"normal",wordBreak:"normal"});let A=Xp(e,t.lineBreak),n=A[0],i=A[2],s=A[1];(t.wordBreak==="break-all"||t.wordBreak==="break-word")&&(s=s.map(function(r){return[ge,Ct,Aa].indexOf(r)!==-1?Et:r}));let o=t.wordBreak==="keep-all"?i.map(function(r,a){return r&&e[a]>=19968&&e[a]<=40959}):void 0;return[n,s,o]},jp=function(){function e(t,A,n,i){this.codePoints=t,this.required=A===na,this.start=n,this.end=i}return e.prototype.slice=function(){return Z.apply(void 0,this.codePoints.slice(this.start,this.end))},e}(),Wp=function(e,t){let A=Qn(e),n=Jp(A,t),i=n[0],s=n[1],o=n[2],r=A.length,a=0,c=0;return{next:function(){if(c>=r)return{done:!0,value:null};let l=_;for(;c<r&&(l=Gp(A,s,i,++c,o))===_;);if(l!==_||c===r){let d=new jp(A,l,a,c);return a=c,{value:d,done:!1}}return{done:!0,value:null}}}},g;(function(e){e[e.STRING_TOKEN=0]="STRING_TOKEN",e[e.BAD_STRING_TOKEN=1]="BAD_STRING_TOKEN",e[e.LEFT_PARENTHESIS_TOKEN=2]="LEFT_PARENTHESIS_TOKEN",e[e.RIGHT_PARENTHESIS_TOKEN=3]="RIGHT_PARENTHESIS_TOKEN",e[e.COMMA_TOKEN=4]="COMMA_TOKEN",e[e.HASH_TOKEN=5]="HASH_TOKEN",e[e.DELIM_TOKEN=6]="DELIM_TOKEN",e[e.AT_KEYWORD_TOKEN=7]="AT_KEYWORD_TOKEN",e[e.PREFIX_MATCH_TOKEN=8]="PREFIX_MATCH_TOKEN",e[e.DASH_MATCH_TOKEN=9]="DASH_MATCH_TOKEN",e[e.INCLUDE_MATCH_TOKEN=10]="INCLUDE_MATCH_TOKEN",e[e.LEFT_CURLY_BRACKET_TOKEN=11]="LEFT_CURLY_BRACKET_TOKEN",e[e.RIGHT_CURLY_BRACKET_TOKEN=12]="RIGHT_CURLY_BRACKET_TOKEN",e[e.SUFFIX_MATCH_TOKEN=13]="SUFFIX_MATCH_TOKEN",e[e.SUBSTRING_MATCH_TOKEN=14]="SUBSTRING_MATCH_TOKEN",e[e.DIMENSION_TOKEN=15]="DIMENSION_TOKEN",e[e.PERCENTAGE_TOKEN=16]="PERCENTAGE_TOKEN",e[e.NUMBER_TOKEN=17]="NUMBER_TOKEN",e[e.FUNCTION=18]="FUNCTION",e[e.FUNCTION_TOKEN=19]="FUNCTION_TOKEN",e[e.IDENT_TOKEN=20]="IDENT_TOKEN",e[e.COLUMN_TOKEN=21]="COLUMN_TOKEN",e[e.URL_TOKEN=22]="URL_TOKEN",e[e.BAD_URL_TOKEN=23]="BAD_URL_TOKEN",e[e.CDC_TOKEN=24]="CDC_TOKEN",e[e.CDO_TOKEN=25]="CDO_TOKEN",e[e.COLON_TOKEN=26]="COLON_TOKEN",e[e.SEMICOLON_TOKEN=27]="SEMICOLON_TOKEN",e[e.LEFT_SQUARE_BRACKET_TOKEN=28]="LEFT_SQUARE_BRACKET_TOKEN",e[e.RIGHT_SQUARE_BRACKET_TOKEN=29]="RIGHT_SQUARE_BRACKET_TOKEN",e[e.UNICODE_RANGE_TOKEN=30]="UNICODE_RANGE_TOKEN",e[e.WHITESPACE_TOKEN=31]="WHITESPACE_TOKEN",e[e.EOF_TOKEN=32]="EOF_TOKEN"})(g||(g={}));var qp=1,$p=2,CA=4,Eo=8,cn=10,Uo=47,fA=92,Zp=9,Yp=32,GA=34,oA=61,eu=35,tu=36,Au=37,JA=39,jA=40,aA=41,nu=95,we=45,iu=33,su=60,ou=62,au=64,ru=91,cu=93,lu=61,pu=123,WA=63,uu=125,bo=124,du=126,fu=128,vo=65533,fi=42,Ut=43,mu=44,Bu=58,hu=59,gA=46,gu=0,xu=8,wu=11,Qu=14,Cu=31,Eu=127,Se=-1,oa=48,aa=97,ra=101,Uu=102,bu=117,vu=122,ca=65,la=69,pa=70,Fu=85,Hu=90,pe=function(e){return e>=oa&&e<=57},Nu=function(e){return e>=55296&&e<=57343},Ot=function(e){return pe(e)||e>=ca&&e<=pa||e>=aa&&e<=Uu},yu=function(e){return e>=aa&&e<=vu},Ru=function(e){return e>=ca&&e<=Hu},Ku=function(e){return yu(e)||Ru(e)},Iu=function(e){return e>=fu},qA=function(e){return e===cn||e===Zp||e===Yp},ln=function(e){return Ku(e)||Iu(e)||e===nu},Fo=function(e){return ln(e)||pe(e)||e===we},Ou=function(e){return e>=gu&&e<=xu||e===wu||e>=Qu&&e<=Cu||e===Eu},it=function(e,t){return e!==fA?!1:t!==cn},$A=function(e,t,A){return e===we?ln(t)||it(t,A):ln(e)?!0:!!(e===fA&&it(e,t))},mi=function(e,t,A){return e===Ut||e===we?pe(t)?!0:t===gA&&pe(A):pe(e===gA?t:e)},_u=function(e){let t=0,A=1;(e[t]===Ut||e[t]===we)&&(e[t]===we&&(A=-1),t++);let n=[];for(;pe(e[t]);)n.push(e[t++]);let i=n.length?parseInt(Z.apply(void 0,n),10):0;e[t]===gA&&t++;let s=[];for(;pe(e[t]);)s.push(e[t++]);let o=s.length,r=o?parseInt(Z.apply(void 0,s),10):0;(e[t]===la||e[t]===ra)&&t++;let a=1;(e[t]===Ut||e[t]===we)&&(e[t]===we&&(a=-1),t++);let c=[];for(;pe(e[t]);)c.push(e[t++]);let l=c.length?parseInt(Z.apply(void 0,c),10):0;return A*(i+r*Math.pow(10,-o))*Math.pow(10,a*l)},Su={type:g.LEFT_PARENTHESIS_TOKEN},Tu={type:g.RIGHT_PARENTHESIS_TOKEN},Lu={type:g.COMMA_TOKEN},Mu={type:g.SUFFIX_MATCH_TOKEN},Du={type:g.PREFIX_MATCH_TOKEN},zu={type:g.COLUMN_TOKEN},Pu={type:g.DASH_MATCH_TOKEN},ku={type:g.INCLUDE_MATCH_TOKEN},Vu={type:g.LEFT_CURLY_BRACKET_TOKEN},Xu={type:g.RIGHT_CURLY_BRACKET_TOKEN},Gu={type:g.SUBSTRING_MATCH_TOKEN},ZA={type:g.BAD_URL_TOKEN},Ju={type:g.BAD_STRING_TOKEN},ju={type:g.CDO_TOKEN},Wu={type:g.CDC_TOKEN},qu={type:g.COLON_TOKEN},$u={type:g.SEMICOLON_TOKEN},Zu={type:g.LEFT_SQUARE_BRACKET_TOKEN},Yu={type:g.RIGHT_SQUARE_BRACKET_TOKEN},ed={type:g.WHITESPACE_TOKEN},yi={type:g.EOF_TOKEN},ua=function(){function e(){this._value=[]}return e.prototype.write=function(t){this._value=this._value.concat(Qn(t))},e.prototype.read=function(){let t=[],A=this.consumeToken();for(;A!==yi;)t.push(A),A=this.consumeToken();return t},e.prototype.consumeToken=function(){let t=this.consumeCodePoint();switch(t){case GA:return this.consumeStringToken(GA);case eu:var A=this.peekCodePoint(0),n=this.peekCodePoint(1),i=this.peekCodePoint(2);if(Fo(A)||it(n,i)){let p=$A(A,n,i)?$p:qp,B=this.consumeName();return{type:g.HASH_TOKEN,value:B,flags:p}}break;case tu:if(this.peekCodePoint(0)===oA)return this.consumeCodePoint(),Mu;break;case JA:return this.consumeStringToken(JA);case jA:return Su;case aA:return Tu;case fi:if(this.peekCodePoint(0)===oA)return this.consumeCodePoint(),Gu;break;case Ut:if(mi(t,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(t),this.consumeNumericToken();break;case mu:return Lu;case we:var s=t,o=this.peekCodePoint(0),r=this.peekCodePoint(1);if(mi(s,o,r))return this.reconsumeCodePoint(t),this.consumeNumericToken();if($A(s,o,r))return this.reconsumeCodePoint(t),this.consumeIdentLikeToken();if(o===we&&r===ou)return this.consumeCodePoint(),this.consumeCodePoint(),Wu;break;case gA:if(mi(t,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(t),this.consumeNumericToken();break;case Uo:if(this.peekCodePoint(0)===fi)for(this.consumeCodePoint();;){let p=this.consumeCodePoint();if(p===fi&&(p=this.consumeCodePoint(),p===Uo))return this.consumeToken();if(p===Se)return this.consumeToken()}break;case Bu:return qu;case hu:return $u;case su:if(this.peekCodePoint(0)===iu&&this.peekCodePoint(1)===we&&this.peekCodePoint(2)===we)return this.consumeCodePoint(),this.consumeCodePoint(),ju;break;case au:var a=this.peekCodePoint(0),c=this.peekCodePoint(1),l=this.peekCodePoint(2);if($A(a,c,l)){var d=this.consumeName();return{type:g.AT_KEYWORD_TOKEN,value:d}}break;case ru:return Zu;case fA:if(it(t,this.peekCodePoint(0)))return this.reconsumeCodePoint(t),this.consumeIdentLikeToken();break;case cu:return Yu;case lu:if(this.peekCodePoint(0)===oA)return this.consumeCodePoint(),Du;break;case pu:return Vu;case uu:return Xu;case bu:case Fu:var f=this.peekCodePoint(0),u=this.peekCodePoint(1);return f===Ut&&(Ot(u)||u===WA)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(t),this.consumeIdentLikeToken();case bo:if(this.peekCodePoint(0)===oA)return this.consumeCodePoint(),Pu;if(this.peekCodePoint(0)===bo)return this.consumeCodePoint(),zu;break;case du:if(this.peekCodePoint(0)===oA)return this.consumeCodePoint(),ku;break;case Se:return yi}return qA(t)?(this.consumeWhiteSpace(),ed):pe(t)?(this.reconsumeCodePoint(t),this.consumeNumericToken()):ln(t)?(this.reconsumeCodePoint(t),this.consumeIdentLikeToken()):{type:g.DELIM_TOKEN,value:Z(t)}},e.prototype.consumeCodePoint=function(){let t=this._value.shift();return typeof t>"u"?-1:t},e.prototype.reconsumeCodePoint=function(t){this._value.unshift(t)},e.prototype.peekCodePoint=function(t){return t>=this._value.length?-1:this._value[t]},e.prototype.consumeUnicodeRangeToken=function(){let t=[],A=this.consumeCodePoint();for(;Ot(A)&&t.length<6;)t.push(A),A=this.consumeCodePoint();let n=!1;for(;A===WA&&t.length<6;)t.push(A),A=this.consumeCodePoint(),n=!0;if(n){let o=parseInt(Z.apply(void 0,t.map(function(a){return a===WA?oa:a})),16),r=parseInt(Z.apply(void 0,t.map(function(a){return a===WA?pa:a})),16);return{type:g.UNICODE_RANGE_TOKEN,start:o,end:r}}let i=parseInt(Z.apply(void 0,t),16);if(this.peekCodePoint(0)===we&&Ot(this.peekCodePoint(1))){this.consumeCodePoint(),A=this.consumeCodePoint();let o=[];for(;Ot(A)&&o.length<6;)o.push(A),A=this.consumeCodePoint();var s=parseInt(Z.apply(void 0,o),16);return{type:g.UNICODE_RANGE_TOKEN,start:i,end:s}}return{type:g.UNICODE_RANGE_TOKEN,start:i,end:i}},e.prototype.consumeIdentLikeToken=function(){let t=this.consumeName();return t.toLowerCase()==="url"&&this.peekCodePoint(0)===jA?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===jA?(this.consumeCodePoint(),{type:g.FUNCTION_TOKEN,value:t}):{type:g.IDENT_TOKEN,value:t}},e.prototype.consumeUrlToken=function(){let t=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===Se)return{type:g.URL_TOKEN,value:""};let A=this.peekCodePoint(0);if(A===JA||A===GA){let n=this.consumeStringToken(this.consumeCodePoint());return n.type===g.STRING_TOKEN&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===Se||this.peekCodePoint(0)===aA)?(this.consumeCodePoint(),{type:g.URL_TOKEN,value:n.value}):(this.consumeBadUrlRemnants(),ZA)}for(;;){let n=this.consumeCodePoint();if(n===Se||n===aA)return{type:g.URL_TOKEN,value:Z.apply(void 0,t)};if(qA(n))return this.consumeWhiteSpace(),this.peekCodePoint(0)===Se||this.peekCodePoint(0)===aA?(this.consumeCodePoint(),{type:g.URL_TOKEN,value:Z.apply(void 0,t)}):(this.consumeBadUrlRemnants(),ZA);if(n===GA||n===JA||n===jA||Ou(n))return this.consumeBadUrlRemnants(),ZA;if(n===fA)if(it(n,this.peekCodePoint(0)))t.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),ZA;else t.push(n)}},e.prototype.consumeWhiteSpace=function(){for(;qA(this.peekCodePoint(0));)this.consumeCodePoint()},e.prototype.consumeBadUrlRemnants=function(){for(;;){let t=this.consumeCodePoint();if(t===aA||t===Se)return;it(t,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},e.prototype.consumeStringSlice=function(t){let n="";for(;t>0;){let i=Math.min(6e4,t);n+=Z.apply(void 0,this._value.splice(0,i)),t-=i}return this._value.shift(),n},e.prototype.consumeStringToken=function(t){let A="",n=0;do{let i=this._value[n];if(i===Se||i===void 0||i===t)return A+=this.consumeStringSlice(n),{type:g.STRING_TOKEN,value:A};if(i===cn)return this._value.splice(0,n),Ju;if(i===fA){let s=this._value[n+1];s!==Se&&s!==void 0&&(s===cn?(A+=this.consumeStringSlice(n),n=-1,this._value.shift()):it(i,s)&&(A+=this.consumeStringSlice(n),A+=Z(this.consumeEscapedCodePoint()),n=-1))}n++}while(!0)},e.prototype.consumeNumber=function(){let t=[],A=CA,n=this.peekCodePoint(0);for((n===Ut||n===we)&&t.push(this.consumeCodePoint());pe(this.peekCodePoint(0));)t.push(this.consumeCodePoint());n=this.peekCodePoint(0);let i=this.peekCodePoint(1);if(n===gA&&pe(i))for(t.push(this.consumeCodePoint(),this.consumeCodePoint()),A=Eo;pe(this.peekCodePoint(0));)t.push(this.consumeCodePoint());n=this.peekCodePoint(0),i=this.peekCodePoint(1);let s=this.peekCodePoint(2);if((n===la||n===ra)&&((i===Ut||i===we)&&pe(s)||pe(i)))for(t.push(this.consumeCodePoint(),this.consumeCodePoint()),A=Eo;pe(this.peekCodePoint(0));)t.push(this.consumeCodePoint());return[_u(t),A]},e.prototype.consumeNumericToken=function(){let t=this.consumeNumber(),A=t[0],n=t[1],i=this.peekCodePoint(0),s=this.peekCodePoint(1),o=this.peekCodePoint(2);if($A(i,s,o)){let r=this.consumeName();return{type:g.DIMENSION_TOKEN,number:A,flags:n,unit:r}}return i===Au?(this.consumeCodePoint(),{type:g.PERCENTAGE_TOKEN,number:A,flags:n}):{type:g.NUMBER_TOKEN,number:A,flags:n}},e.prototype.consumeEscapedCodePoint=function(){let t=this.consumeCodePoint();if(Ot(t)){let A=Z(t);for(;Ot(this.peekCodePoint(0))&&A.length<6;)A+=Z(this.consumeCodePoint());qA(this.peekCodePoint(0))&&this.consumeCodePoint();let n=parseInt(A,16);return n===0||Nu(n)||n>1114111?vo:n}return t===Se?vo:t},e.prototype.consumeName=function(){let t="";for(;;){let A=this.consumeCodePoint();if(Fo(A))t+=Z(A);else if(it(A,this.peekCodePoint(0)))t+=Z(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(A),t}},e}(),zi=function(){function e(t){this._tokens=t}return e.create=function(t){let A=new ua;return A.write(t),new e(A.read())},e.parseValue=function(t){return e.create(t).parseComponentValue()},e.parseValues=function(t){return e.create(t).parseComponentValues()},e.prototype.parseComponentValue=function(){let t=this.consumeToken();for(;t.type===g.WHITESPACE_TOKEN;)t=this.consumeToken();if(t.type===g.EOF_TOKEN)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(t);let A=this.consumeComponentValue();do t=this.consumeToken();while(t.type===g.WHITESPACE_TOKEN);if(t.type===g.EOF_TOKEN)return A;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},e.prototype.parseComponentValues=function(){let t=[];for(;;){let A=this.consumeComponentValue();if(A.type===g.EOF_TOKEN)return t;t.push(A),t.push()}},e.prototype.consumeComponentValue=function(){let t=this.consumeToken();switch(t.type){case g.LEFT_CURLY_BRACKET_TOKEN:case g.LEFT_SQUARE_BRACKET_TOKEN:case g.LEFT_PARENTHESIS_TOKEN:return this.consumeSimpleBlock(t.type);case g.FUNCTION_TOKEN:return this.consumeFunction(t)}return t},e.prototype.consumeSimpleBlock=function(t){let A={type:t,values:[]},n=function(s,o){return o===g.LEFT_CURLY_BRACKET_TOKEN&&s.type===g.RIGHT_CURLY_BRACKET_TOKEN||o===g.LEFT_SQUARE_BRACKET_TOKEN&&s.type===g.RIGHT_SQUARE_BRACKET_TOKEN?!0:o===g.LEFT_PARENTHESIS_TOKEN&&s.type===g.RIGHT_PARENTHESIS_TOKEN},i=this.consumeToken();for(;;){if(i.type===g.EOF_TOKEN||n(i,t))return A;this.reconsumeToken(i),A.values.push(this.consumeComponentValue()),i=this.consumeToken()}},e.prototype.consumeFunction=function(t){let A={name:t.value,values:[],type:g.FUNCTION};for(;;){let n=this.consumeToken();if(n.type===g.EOF_TOKEN||n.type===g.RIGHT_PARENTHESIS_TOKEN)return A;this.reconsumeToken(n),A.values.push(this.consumeComponentValue())}},e.prototype.consumeToken=function(){let t=this._tokens.shift();return typeof t>"u"?yi:t},e.prototype.reconsumeToken=function(t){this._tokens.unshift(t)},e}(),Pi=function(e){return e.type===g.DIMENSION_TOKEN},Xt=function(e){return e.type===g.NUMBER_TOKEN},z=function(e){return e.type===g.IDENT_TOKEN},td=function(e){return e.type===g.STRING_TOKEN},Ri=function(e,t){return z(e)&&e.value===t},da=function(e){return e.type!==g.WHITESPACE_TOKEN},kt=function(e){return e.type!==g.WHITESPACE_TOKEN&&e.type!==g.COMMA_TOKEN},Le=function(e){let t=[],A=[];return e.forEach(function(n){if(n.type===g.COMMA_TOKEN){if(A.length===0)throw new Error("Error parsing function args, zero tokens for arg");t.push(A),A=[];return}n.type!==g.WHITESPACE_TOKEN&&A.push(n)}),A.length&&t.push(A),t},dt=function(e){return e.type===g.NUMBER_TOKEN||e.type===g.DIMENSION_TOKEN},Y=function(e){return e.type===g.PERCENTAGE_TOKEN||dt(e)},fa=function(e){return e.length>1?[e[0],e[1]]:[e[0]]},ce={type:g.NUMBER_TOKEN,number:0,flags:CA},ki={type:g.PERCENTAGE_TOKEN,number:50,flags:CA},rt={type:g.PERCENTAGE_TOKEN,number:100,flags:CA},P=function(e,t){if(e.type===g.PERCENTAGE_TOKEN)return e.number/100*t;if(Pi(e))switch(e.unit){case"rem":case"em":return 16*e.number;case"px":default:return e.number}return e.number},uA=function(e,t,A){let n=e[0],i=e[1];return[P(n,t),P(typeof i<"u"?i:n,A)]},ma="deg",Fe=function(e){return Math.PI*e/180},Ba="grad",ha="rad",ga="turn",Cn={name:"angle",parse:function(e){if(e.type===g.DIMENSION_TOKEN)switch(e.unit){case ma:return Math.PI*e.number/180;case Ba:return Math.PI/200*e.number;case ha:return e.number;case ga:return Math.PI*2*e.number}throw new Error("Unsupported angle type")}},xa=function(e){return e.type===g.DIMENSION_TOKEN&&(e.unit===ma||e.unit===Ba||e.unit===ha||e.unit===ga)},wa=function(e){switch(e.filter(z).map(function(A){return A.value}).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[ce,ce];case"to top":case"bottom":return Fe(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[ce,rt];case"to right":case"left":return Fe(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[rt,rt];case"to bottom":case"top":return Fe(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[rt,ce];case"to left":case"right":return Fe(270)}return 0},We={name:"color",parse:function(e){if(e.type===g.FUNCTION){let t=Ad[e.name];if(typeof t>"u")throw new Error(`Attempting to parse an unsupported color function "${e.name}"`);return t(e.values)}if(e.type===g.HASH_TOKEN){if(e.value.length===3){let t=e.value.substring(0,1),A=e.value.substring(1,2),n=e.value.substring(2,3);return ct(parseInt(t+t,16),parseInt(A+A,16),parseInt(n+n,16),1)}if(e.value.length===4){let t=e.value.substring(0,1),A=e.value.substring(1,2),n=e.value.substring(2,3),i=e.value.substring(3,4);return ct(parseInt(t+t,16),parseInt(A+A,16),parseInt(n+n,16),parseInt(i+i,16)/255)}if(e.value.length===6){let t=e.value.substring(0,2),A=e.value.substring(2,4),n=e.value.substring(4,6);return ct(parseInt(t,16),parseInt(A,16),parseInt(n,16),1)}if(e.value.length===8){let t=e.value.substring(0,2),A=e.value.substring(2,4),n=e.value.substring(4,6),i=e.value.substring(6,8);return ct(parseInt(t,16),parseInt(A,16),parseInt(n,16),parseInt(i,16)/255)}}if(e.type===g.IDENT_TOKEN){let t=Xe[e.value.toUpperCase()];if(typeof t<"u")return t}return Xe.TRANSPARENT}},pt=function(e){return(255&e)===0},de=function(e){let t=255&e,A=255&e>>8,n=255&e>>16,i=255&e>>24;return t<255?`rgba(${i},${n},${A},${t/255})`:`rgb(${i},${n},${A})`},ct=function(e,t,A,n){return(e<<24|t<<16|A<<8|Math.round(n*255)<<0)>>>0},Ho=function(e,t){if(e.type===g.NUMBER_TOKEN)return e.number;if(e.type===g.PERCENTAGE_TOKEN){let A=t===3?1:255;return t===3?e.number/100*A:Math.round(e.number/100*A)}return 0},No=function(e){let t=e.filter(kt);if(t.length===3){let A=t.map(Ho),n=A[0],i=A[1],s=A[2];return ct(n,i,s,1)}if(t.length===4){let A=t.map(Ho),n=A[0],i=A[1],s=A[2],o=A[3];return ct(n,i,s,o)}return 0};function Bi(e,t,A){return A<0&&(A+=1),A>=1&&(A-=1),A<1/6?(t-e)*A*6+e:A<1/2?t:A<2/3?(t-e)*6*(2/3-A)+e:e}var yo=function(e){let t=e.filter(kt),A=t[0],n=t[1],i=t[2],s=t[3],o=(A.type===g.NUMBER_TOKEN?Fe(A.number):Cn.parse(A))/(Math.PI*2),r=Y(n)?n.number/100:0,a=Y(i)?i.number/100:0,c=typeof s<"u"&&Y(s)?P(s,1):1;if(r===0)return ct(a*255,a*255,a*255,1);let l=a<=.5?a*(r+1):a+r-a*r,d=a*2-l,f=Bi(d,l,o+1/3),u=Bi(d,l,o),p=Bi(d,l,o-1/3);return ct(f*255,u*255,p*255,c)},Ad={hsl:yo,hsla:yo,rgb:No,rgba:No},Xe={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},N;(function(e){e[e.VALUE=0]="VALUE",e[e.LIST=1]="LIST",e[e.IDENT_VALUE=2]="IDENT_VALUE",e[e.TYPE_VALUE=3]="TYPE_VALUE",e[e.TOKEN_VALUE=4]="TOKEN_VALUE"})(N||(N={}));var Re;(function(e){e[e.BORDER_BOX=0]="BORDER_BOX",e[e.PADDING_BOX=1]="PADDING_BOX",e[e.CONTENT_BOX=2]="CONTENT_BOX"})(Re||(Re={}));var nd={name:"background-clip",initialValue:"border-box",prefix:!1,type:N.LIST,parse:function(e){return e.map(function(t){if(z(t))switch(t.value){case"padding-box":return Re.PADDING_BOX;case"content-box":return Re.CONTENT_BOX}return Re.BORDER_BOX})}},id={name:"background-color",initialValue:"transparent",prefix:!1,type:N.TYPE_VALUE,format:"color"},En=function(e){let t=We.parse(e[0]),A=e[1];return A&&Y(A)?{color:t,stop:A}:{color:t,stop:null}},Ro=function(e,t){let A=e[0],n=e[e.length-1];A.stop===null&&(A.stop=ce),n.stop===null&&(n.stop=rt);let i=[],s=0;for(let a=0;a<e.length;a++){let c=e[a].stop;if(c!==null){let l=P(c,t);l>s?i.push(l):i.push(s),s=l}else i.push(null)}let o=null;for(var r=0;r<i.length;r++){let a=i[r];if(a===null)o===null&&(o=r);else if(o!==null){let c=r-o,l=i[o-1],d=(a-l)/(c+1);for(let f=1;f<=c;f++)i[o+f-1]=d*f;o=null}}return e.map(function(a,c){return{color:a.color,stop:Math.max(Math.min(1,i[c]/t),0)}})},sd=function(e,t,A){let n=t/2,i=A/2,s=P(e[0],t)-n,o=i-P(e[1],A);return(Math.atan2(o,s)+Math.PI*2)%(Math.PI*2)},od=function(e,t,A){let n=typeof e=="number"?e:sd(e,t,A),i=Math.abs(t*Math.sin(n))+Math.abs(A*Math.cos(n)),s=t/2,o=A/2,r=i/2,a=Math.sin(n-Math.PI/2)*r,c=Math.cos(n-Math.PI/2)*r;return[i,s-c,s+c,o-a,o+a]},ye=function(e,t){return Math.sqrt(e*e+t*t)},Ko=function(e,t,A,n,i){return[[0,0],[0,t],[e,0],[e,t]].reduce(function(o,r){let a=r[0],c=r[1],l=ye(A-a,n-c);return(i?l<o.optimumDistance:l>o.optimumDistance)?{optimumCorner:r,optimumDistance:l}:o},{optimumDistance:i?1/0:-1/0,optimumCorner:null}).optimumCorner},ad=function(e,t,A,n,i){let s=0,o=0;switch(e.size){case re.CLOSEST_SIDE:e.shape===oe.CIRCLE?s=o=Math.min(Math.abs(t),Math.abs(t-n),Math.abs(A),Math.abs(A-i)):e.shape===oe.ELLIPSE&&(s=Math.min(Math.abs(t),Math.abs(t-n)),o=Math.min(Math.abs(A),Math.abs(A-i)));break;case re.CLOSEST_CORNER:if(e.shape===oe.CIRCLE)s=o=Math.min(ye(t,A),ye(t,A-i),ye(t-n,A),ye(t-n,A-i));else if(e.shape===oe.ELLIPSE){let d=Math.min(Math.abs(A),Math.abs(A-i))/Math.min(Math.abs(t),Math.abs(t-n)),f=Ko(n,i,t,A,!0),u=f[0],p=f[1];s=ye(u-t,(p-A)/d),o=d*s}break;case re.FARTHEST_SIDE:e.shape===oe.CIRCLE?s=o=Math.max(Math.abs(t),Math.abs(t-n),Math.abs(A),Math.abs(A-i)):e.shape===oe.ELLIPSE&&(s=Math.max(Math.abs(t),Math.abs(t-n)),o=Math.max(Math.abs(A),Math.abs(A-i)));break;case re.FARTHEST_CORNER:if(e.shape===oe.CIRCLE)s=o=Math.max(ye(t,A),ye(t,A-i),ye(t-n,A),ye(t-n,A-i));else if(e.shape===oe.ELLIPSE){var r=Math.max(Math.abs(A),Math.abs(A-i))/Math.max(Math.abs(t),Math.abs(t-n)),a=Ko(n,i,t,A,!1),c=a[0],l=a[1];s=ye(c-t,(l-A)/r),o=r*s}break}return Array.isArray(e.size)&&(s=P(e.size[0],n),o=e.size.length===2?P(e.size[1],i):s),[s,o]},rd=function(e){let t=Fe(180),A=[];return Le(e).forEach(function(n,i){if(i===0){let o=n[0];if(o.type===g.IDENT_TOKEN&&o.value==="to"){t=wa(n);return}else if(xa(o)){t=Cn.parse(o);return}}let s=En(n);A.push(s)}),{angle:t,stops:A,type:fe.LINEAR_GRADIENT}},YA=function(e){let t=Fe(180),A=[];return Le(e).forEach(function(n,i){if(i===0){let o=n[0];if(o.type===g.IDENT_TOKEN&&["top","left","right","bottom"].indexOf(o.value)!==-1){t=wa(n);return}else if(xa(o)){t=(Cn.parse(o)+Fe(270))%Fe(360);return}}let s=En(n);A.push(s)}),{angle:t,stops:A,type:fe.LINEAR_GRADIENT}},cd=function(e){if(e.createRange){let A=e.createRange();if(A.getBoundingClientRect){let n=e.createElement("boundtest");n.style.height="123px",n.style.display="block",e.body.appendChild(n),A.selectNode(n);let i=A.getBoundingClientRect(),s=Math.round(i.height);if(e.body.removeChild(n),s===123)return!0}}return!1},ld=function(){return typeof new Image().crossOrigin<"u"},pd=function(){return typeof new XMLHttpRequest().responseType=="string"},ud=function(e){let t=new Image,A=e.createElement("canvas"),n=A.getContext("2d");if(!n)return!1;t.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{n.drawImage(t,0,0),A.toDataURL()}catch{return!1}return!0},Io=function(e){return e[0]===0&&e[1]===255&&e[2]===0&&e[3]===255},dd=function(e){let t=e.createElement("canvas"),A=100;t.width=A,t.height=A;let n=t.getContext("2d");if(!n)return Promise.reject(!1);n.fillStyle="rgb(0, 255, 0)",n.fillRect(0,0,A,A);let i=new Image,s=t.toDataURL();i.src=s;let o=Ki(A,A,0,0,i);return n.fillStyle="red",n.fillRect(0,0,A,A),Oo(o).then(function(r){n.drawImage(r,0,0);let a=n.getImageData(0,0,A,A).data;n.fillStyle="red",n.fillRect(0,0,A,A);let c=e.createElement("div");return c.style.backgroundImage=`url(${s})`,c.style.height=`${A}px`,Io(a)?Oo(Ki(A,A,0,0,c)):Promise.reject(!1)}).then(function(r){return n.drawImage(r,0,0),Io(n.getImageData(0,0,A,A).data)}).catch(function(){return!1})},Ki=function(e,t,A,n,i){let s="http://www.w3.org/2000/svg",o=document.createElementNS(s,"svg"),r=document.createElementNS(s,"foreignObject");return o.setAttributeNS(null,"width",e.toString()),o.setAttributeNS(null,"height",t.toString()),r.setAttributeNS(null,"width","100%"),r.setAttributeNS(null,"height","100%"),r.setAttributeNS(null,"x",A.toString()),r.setAttributeNS(null,"y",n.toString()),r.setAttributeNS(null,"externalResourcesRequired","true"),o.appendChild(r),r.appendChild(i),o},Oo=function(e){return new Promise(function(t,A){let n=new Image;n.onload=function(){return t(n)},n.onerror=A,n.src=`data:image/svg+xml;charset=utf-8,${encodeURIComponent(new XMLSerializer().serializeToString(e))}`})},ve={get SUPPORT_RANGE_BOUNDS(){let e=cd(document);return Object.defineProperty(ve,"SUPPORT_RANGE_BOUNDS",{value:e}),e},get SUPPORT_SVG_DRAWING(){let e=ud(document);return Object.defineProperty(ve,"SUPPORT_SVG_DRAWING",{value:e}),e},get SUPPORT_FOREIGNOBJECT_DRAWING(){let e=typeof Array.from=="function"&&typeof window.fetch=="function"?dd(document):Promise.resolve(!1);return Object.defineProperty(ve,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:e}),e},get SUPPORT_CORS_IMAGES(){let e=ld();return Object.defineProperty(ve,"SUPPORT_CORS_IMAGES",{value:e}),e},get SUPPORT_RESPONSE_TYPE(){let e=pd();return Object.defineProperty(ve,"SUPPORT_RESPONSE_TYPE",{value:e}),e},get SUPPORT_CORS_XHR(){let e="withCredentials"in new XMLHttpRequest;return Object.defineProperty(ve,"SUPPORT_CORS_XHR",{value:e}),e}},ie=function(){function e(t){let A=t.id,n=t.enabled;this.id=A,this.enabled=n,this.start=Date.now()}return e.prototype.debug=function(){let t=[];for(let A=0;A<arguments.length;A++)t[A]=arguments[A];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,[this.id,`${this.getTime()}ms`].concat(t)):this.info.apply(this,t))},e.prototype.getTime=function(){return Date.now()-this.start},e.create=function(t){e.instances[t.id]=new e(t)},e.destroy=function(t){delete e.instances[t]},e.getInstance=function(t){let A=e.instances[t];if(typeof A>"u")throw new Error(`No logger instance found with id ${t}`);return A},e.prototype.info=function(){let t=[];for(let A=0;A<arguments.length;A++)t[A]=arguments[A];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,[this.id,`${this.getTime()}ms`].concat(t))},e.prototype.error=function(){let t=[];for(let A=0;A<arguments.length;A++)t[A]=arguments[A];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,[this.id,`${this.getTime()}ms`].concat(t)):this.info.apply(this,t))},e.instances={},e}(),Ge=function(){function e(){}return e.create=function(t,A){return e._caches[t]=new fd(t,A)},e.destroy=function(t){delete e._caches[t]},e.open=function(t){let A=e._caches[t];if(typeof A<"u")return A;throw new Error(`Cache with key "${t}" not found`)},e.getOrigin=function(t){let A=e._link;return A?(A.href=t,A.href=A.href,A.protocol+A.hostname+A.port):"about:blank"},e.isSameOrigin=function(t){return e.getOrigin(t)===e._origin},e.setContext=function(t){e._link=t.document.createElement("a"),e._origin=e.getOrigin(t.location.href)},e.getInstance=function(){let t=e._current;if(t===null)throw new Error("No cache instance attached");return t},e.attachInstance=function(t){e._current=t},e.detachInstance=function(){e._current=null},e._caches={},e._origin="about:blank",e._current=null,e}(),fd=function(){function e(t,A){this.id=t,this._options=A,this._cache={}}return e.prototype.addImage=function(t){let A=Promise.resolve();return this.has(t)||(wd(t)||gd(t))&&(this._cache[t]=this.loadImage(t)),A},e.prototype.match=function(t){return this._cache[t]},e.prototype.loadImage=function(t){return Ee(this,void 0,void 0,function(){let A,n,i,s,o=this;return xe(this,function(r){switch(r.label){case 0:return A=Ge.isSameOrigin(t),n=!hi(t)&&this._options.useCORS===!0&&ve.SUPPORT_CORS_IMAGES&&!A,i=!hi(t)&&!A&&typeof this._options.proxy=="string"&&ve.SUPPORT_CORS_XHR&&!n,!A&&this._options.allowTaint===!1&&!hi(t)&&!i&&!n?[2]:(s=t,i?[4,this.proxy(s)]:[3,2]);case 1:s=r.sent(),r.label=2;case 2:return ie.getInstance(this.id).debug(`Added image ${t.substring(0,256)}`),[4,new Promise(function(a,c){let l=new Image;l.onload=function(){return a(l)},l.onerror=c,(xd(s)||n)&&(l.crossOrigin="anonymous"),l.src=s,l.complete===!0&&setTimeout(function(){return a(l)},500),o._options.imageTimeout>0&&setTimeout(function(){return c(`Timed out (${o._options.imageTimeout}ms) loading image`)},o._options.imageTimeout)})];case 3:return[2,r.sent()]}})})},e.prototype.has=function(t){return typeof this._cache[t]<"u"},e.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},e.prototype.proxy=function(t){let A=this,n=this._options.proxy;if(!n)throw new Error("No proxy defined");let i=t.substring(0,256);return new Promise(function(s,o){let r=ve.SUPPORT_RESPONSE_TYPE?"blob":"text",a=new XMLHttpRequest;if(a.onload=function(){if(a.status===200)if(r==="text")s(a.response);else{let c=new FileReader;c.addEventListener("load",function(){return s(c.result)},!1),c.addEventListener("error",function(l){return o(l)},!1),c.readAsDataURL(a.response)}else o(`Failed to proxy resource ${i} with status code ${a.status}`)},a.onerror=o,a.open("GET",`${n}?url=${encodeURIComponent(t)}&responseType=${r}`),r!=="text"&&a instanceof XMLHttpRequest&&(a.responseType=r),A._options.imageTimeout){let c=A._options.imageTimeout;a.timeout=c,a.ontimeout=function(){return o(`Timed out (${c}ms) proxying ${i}`)}}a.send()})},e}(),md=/^data:image\/svg\+xml/i,Bd=/^data:image\/.*;base64,/i,hd=/^data:image\/.*/i,gd=function(e){return ve.SUPPORT_SVG_DRAWING||!Qd(e)},hi=function(e){return hd.test(e)},xd=function(e){return Bd.test(e)},wd=function(e){return e.substr(0,4)==="blob"},Qd=function(e){return e.substr(-3).toLowerCase()==="svg"||md.test(e)},Cd=function(e){let t=Fe(180),A=[],n=fe.LINEAR_GRADIENT,i=oe.CIRCLE,s=re.FARTHEST_CORNER,o=[];return Le(e).forEach(function(r,a){let c=r[0];if(a===0){if(z(c)&&c.value==="linear"){n=fe.LINEAR_GRADIENT;return}else if(z(c)&&c.value==="radial"){n=fe.RADIAL_GRADIENT;return}}if(c.type===g.FUNCTION){if(c.name==="from"){let l=We.parse(c.values[0]);A.push({stop:ce,color:l})}else if(c.name==="to"){let l=We.parse(c.values[0]);A.push({stop:rt,color:l})}else if(c.name==="color-stop"){let l=c.values.filter(kt);if(l.length===2){let d=We.parse(l[1]),f=l[0];Xt(f)&&A.push({stop:{type:g.PERCENTAGE_TOKEN,number:f.number*100,flags:f.flags},color:d})}}}}),n===fe.LINEAR_GRADIENT?{angle:(t+Fe(180))%Fe(360),stops:A,type:n}:{size:s,shape:i,stops:A,position:o,type:n}},Qa="closest-side",Ca="farthest-side",Ea="closest-corner",Ua="farthest-corner",ba="circle",va="ellipse",Fa="cover",Ha="contain",Ed=function(e){let t=oe.CIRCLE,A=re.FARTHEST_CORNER,n=[],i=[];return Le(e).forEach(function(s,o){let r=!0;if(o===0){let a=!1;r=s.reduce(function(c,l){if(a)if(z(l))switch(l.value){case"center":return i.push(ki),c;case"top":case"left":return i.push(ce),c;case"right":case"bottom":return i.push(rt),c}else(Y(l)||dt(l))&&i.push(l);else if(z(l))switch(l.value){case ba:return t=oe.CIRCLE,!1;case va:return t=oe.ELLIPSE,!1;case"at":return a=!0,!1;case Qa:return A=re.CLOSEST_SIDE,!1;case Fa:case Ca:return A=re.FARTHEST_SIDE,!1;case Ha:case Ea:return A=re.CLOSEST_CORNER,!1;case Ua:return A=re.FARTHEST_CORNER,!1}else if(dt(l)||Y(l))return Array.isArray(A)||(A=[]),A.push(l),!1;return c},r)}if(r){let a=En(s);n.push(a)}}),{size:A,shape:t,stops:n,position:i,type:fe.RADIAL_GRADIENT}},en=function(e){let t=oe.CIRCLE,A=re.FARTHEST_CORNER,n=[],i=[];return Le(e).forEach(function(s,o){let r=!0;if(o===0?r=s.reduce(function(a,c){if(z(c))switch(c.value){case"center":return i.push(ki),!1;case"top":case"left":return i.push(ce),!1;case"right":case"bottom":return i.push(rt),!1}else if(Y(c)||dt(c))return i.push(c),!1;return a},r):o===1&&(r=s.reduce(function(a,c){if(z(c))switch(c.value){case ba:return t=oe.CIRCLE,!1;case va:return t=oe.ELLIPSE,!1;case Ha:case Qa:return A=re.CLOSEST_SIDE,!1;case Ca:return A=re.FARTHEST_SIDE,!1;case Ea:return A=re.CLOSEST_CORNER,!1;case Fa:case Ua:return A=re.FARTHEST_CORNER,!1}else if(dt(c)||Y(c))return Array.isArray(A)||(A=[]),A.push(c),!1;return a},r)),r){let a=En(s);n.push(a)}}),{size:A,shape:t,stops:n,position:i,type:fe.RADIAL_GRADIENT}},fe;(function(e){e[e.URL=0]="URL",e[e.LINEAR_GRADIENT=1]="LINEAR_GRADIENT",e[e.RADIAL_GRADIENT=2]="RADIAL_GRADIENT"})(fe||(fe={}));var Ud=function(e){return e.type===fe.LINEAR_GRADIENT},bd=function(e){return e.type===fe.RADIAL_GRADIENT},oe;(function(e){e[e.CIRCLE=0]="CIRCLE",e[e.ELLIPSE=1]="ELLIPSE"})(oe||(oe={}));var re;(function(e){e[e.CLOSEST_SIDE=0]="CLOSEST_SIDE",e[e.FARTHEST_SIDE=1]="FARTHEST_SIDE",e[e.CLOSEST_CORNER=2]="CLOSEST_CORNER",e[e.FARTHEST_CORNER=3]="FARTHEST_CORNER"})(re||(re={}));var Vi={name:"image",parse:function(e){if(e.type===g.URL_TOKEN){let t={url:e.value,type:fe.URL};return Ge.getInstance().addImage(e.value),t}if(e.type===g.FUNCTION){let t=Na[e.name];if(typeof t>"u")throw new Error(`Attempting to parse an unsupported image function "${e.name}"`);return t(e.values)}throw new Error("Unsupported image type")}};function vd(e){return e.type!==g.FUNCTION||Na[e.name]}var Na={"linear-gradient":rd,"-moz-linear-gradient":YA,"-ms-linear-gradient":YA,"-o-linear-gradient":YA,"-webkit-linear-gradient":YA,"radial-gradient":Ed,"-moz-radial-gradient":en,"-ms-radial-gradient":en,"-o-radial-gradient":en,"-webkit-radial-gradient":en,"-webkit-gradient":Cd},Fd={name:"background-image",initialValue:"none",type:N.LIST,prefix:!1,parse:function(e){if(e.length===0)return[];let t=e[0];return t.type===g.IDENT_TOKEN&&t.value==="none"?[]:e.filter(function(A){return kt(A)&&vd(A)}).map(Vi.parse)}},Hd={name:"background-origin",initialValue:"border-box",prefix:!1,type:N.LIST,parse:function(e){return e.map(function(t){if(z(t))switch(t.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Nd={name:"background-position",initialValue:"0% 0%",type:N.LIST,prefix:!1,parse:function(e){return Le(e).map(function(t){return t.filter(Y)}).map(fa)}},Je;(function(e){e[e.REPEAT=0]="REPEAT",e[e.NO_REPEAT=1]="NO_REPEAT",e[e.REPEAT_X=2]="REPEAT_X",e[e.REPEAT_Y=3]="REPEAT_Y"})(Je||(Je={}));var yd={name:"background-repeat",initialValue:"repeat",prefix:!1,type:N.LIST,parse:function(e){return Le(e).map(function(t){return t.filter(z).map(function(A){return A.value}).join(" ")}).map(Rd)}},Rd=function(e){switch(e){case"no-repeat":return Je.NO_REPEAT;case"repeat-x":case"repeat no-repeat":return Je.REPEAT_X;case"repeat-y":case"no-repeat repeat":return Je.REPEAT_Y;case"repeat":default:return Je.REPEAT}},zt;(function(e){e.AUTO="auto",e.CONTAIN="contain",e.COVER="cover"})(zt||(zt={}));var Kd={name:"background-size",initialValue:"0",prefix:!1,type:N.LIST,parse:function(e){return Le(e).map(function(t){return t.filter(Id)})}},Id=function(e){return z(e)||Y(e)},Un=function(e){return{name:`border-${e}-color`,initialValue:"transparent",prefix:!1,type:N.TYPE_VALUE,format:"color"}},Od=Un("top"),_d=Un("right"),Sd=Un("bottom"),Td=Un("left"),bn=function(e){return{name:`border-radius-${e}`,initialValue:"0 0",prefix:!1,type:N.LIST,parse:function(t){return fa(t.filter(Y))}}},Ld=bn("top-left"),Md=bn("top-right"),Dd=bn("bottom-right"),zd=bn("bottom-left"),Vt;(function(e){e[e.NONE=0]="NONE",e[e.SOLID=1]="SOLID"})(Vt||(Vt={}));var vn=function(e){return{name:`border-${e}-style`,initialValue:"solid",prefix:!1,type:N.IDENT_VALUE,parse:function(t){switch(t){case"none":return Vt.NONE}return Vt.SOLID}}},Pd=vn("top"),kd=vn("right"),Vd=vn("bottom"),Xd=vn("left"),Fn=function(e){return{name:`border-${e}-width`,initialValue:"0",type:N.VALUE,prefix:!1,parse:function(t){return Pi(t)?t.number:0}}},Gd=Fn("top"),Jd=Fn("right"),jd=Fn("bottom"),Wd=Fn("left"),qd={name:"color",initialValue:"transparent",prefix:!1,type:N.TYPE_VALUE,format:"color"},$d={name:"display",initialValue:"inline-block",prefix:!1,type:N.LIST,parse:function(e){return e.filter(z).reduce(function(t,A){return t|Zd(A.value)},0)}},Zd=function(e){switch(e){case"block":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},st;(function(e){e[e.NONE=0]="NONE",e[e.LEFT=1]="LEFT",e[e.RIGHT=2]="RIGHT",e[e.INLINE_START=3]="INLINE_START",e[e.INLINE_END=4]="INLINE_END"})(st||(st={}));var Yd={name:"float",initialValue:"none",prefix:!1,type:N.IDENT_VALUE,parse:function(e){switch(e){case"left":return st.LEFT;case"right":return st.RIGHT;case"inline-start":return st.INLINE_START;case"inline-end":return st.INLINE_END}return st.NONE}},ef={name:"letter-spacing",initialValue:"0",prefix:!1,type:N.VALUE,parse:function(e){return e.type===g.IDENT_TOKEN&&e.value==="normal"?0:e.type===g.NUMBER_TOKEN||e.type===g.DIMENSION_TOKEN?e.number:0}},pn;(function(e){e.NORMAL="normal",e.STRICT="strict"})(pn||(pn={}));var tf={name:"line-break",initialValue:"normal",prefix:!1,type:N.IDENT_VALUE,parse:function(e){switch(e){case"strict":return pn.STRICT;case"normal":default:return pn.NORMAL}}},Af={name:"line-height",initialValue:"normal",prefix:!1,type:N.TOKEN_VALUE},nf=function(e,t){return z(e)&&e.value==="normal"?1.2*t:e.type===g.NUMBER_TOKEN?t*e.number:Y(e)?P(e,t):t},sf={name:"list-style-image",initialValue:"none",type:N.VALUE,prefix:!1,parse:function(e){return e.type===g.IDENT_TOKEN&&e.value==="none"?null:Vi.parse(e)}},un;(function(e){e[e.INSIDE=0]="INSIDE",e[e.OUTSIDE=1]="OUTSIDE"})(un||(un={}));var of={name:"list-style-position",initialValue:"outside",prefix:!1,type:N.IDENT_VALUE,parse:function(e){switch(e){case"inside":return un.INSIDE;case"outside":default:return un.OUTSIDE}}},h;(function(e){e[e.NONE=-1]="NONE",e[e.DISC=0]="DISC",e[e.CIRCLE=1]="CIRCLE",e[e.SQUARE=2]="SQUARE",e[e.DECIMAL=3]="DECIMAL",e[e.CJK_DECIMAL=4]="CJK_DECIMAL",e[e.DECIMAL_LEADING_ZERO=5]="DECIMAL_LEADING_ZERO",e[e.LOWER_ROMAN=6]="LOWER_ROMAN",e[e.UPPER_ROMAN=7]="UPPER_ROMAN",e[e.LOWER_GREEK=8]="LOWER_GREEK",e[e.LOWER_ALPHA=9]="LOWER_ALPHA",e[e.UPPER_ALPHA=10]="UPPER_ALPHA",e[e.ARABIC_INDIC=11]="ARABIC_INDIC",e[e.ARMENIAN=12]="ARMENIAN",e[e.BENGALI=13]="BENGALI",e[e.CAMBODIAN=14]="CAMBODIAN",e[e.CJK_EARTHLY_BRANCH=15]="CJK_EARTHLY_BRANCH",e[e.CJK_HEAVENLY_STEM=16]="CJK_HEAVENLY_STEM",e[e.CJK_IDEOGRAPHIC=17]="CJK_IDEOGRAPHIC",e[e.DEVANAGARI=18]="DEVANAGARI",e[e.ETHIOPIC_NUMERIC=19]="ETHIOPIC_NUMERIC",e[e.GEORGIAN=20]="GEORGIAN",e[e.GUJARATI=21]="GUJARATI",e[e.GURMUKHI=22]="GURMUKHI",e[e.HEBREW=22]="HEBREW",e[e.HIRAGANA=23]="HIRAGANA",e[e.HIRAGANA_IROHA=24]="HIRAGANA_IROHA",e[e.JAPANESE_FORMAL=25]="JAPANESE_FORMAL",e[e.JAPANESE_INFORMAL=26]="JAPANESE_INFORMAL",e[e.KANNADA=27]="KANNADA",e[e.KATAKANA=28]="KATAKANA",e[e.KATAKANA_IROHA=29]="KATAKANA_IROHA",e[e.KHMER=30]="KHMER",e[e.KOREAN_HANGUL_FORMAL=31]="KOREAN_HANGUL_FORMAL",e[e.KOREAN_HANJA_FORMAL=32]="KOREAN_HANJA_FORMAL",e[e.KOREAN_HANJA_INFORMAL=33]="KOREAN_HANJA_INFORMAL",e[e.LAO=34]="LAO",e[e.LOWER_ARMENIAN=35]="LOWER_ARMENIAN",e[e.MALAYALAM=36]="MALAYALAM",e[e.MONGOLIAN=37]="MONGOLIAN",e[e.MYANMAR=38]="MYANMAR",e[e.ORIYA=39]="ORIYA",e[e.PERSIAN=40]="PERSIAN",e[e.SIMP_CHINESE_FORMAL=41]="SIMP_CHINESE_FORMAL",e[e.SIMP_CHINESE_INFORMAL=42]="SIMP_CHINESE_INFORMAL",e[e.TAMIL=43]="TAMIL",e[e.TELUGU=44]="TELUGU",e[e.THAI=45]="THAI",e[e.TIBETAN=46]="TIBETAN",e[e.TRAD_CHINESE_FORMAL=47]="TRAD_CHINESE_FORMAL",e[e.TRAD_CHINESE_INFORMAL=48]="TRAD_CHINESE_INFORMAL",e[e.UPPER_ARMENIAN=49]="UPPER_ARMENIAN",e[e.DISCLOSURE_OPEN=50]="DISCLOSURE_OPEN",e[e.DISCLOSURE_CLOSED=51]="DISCLOSURE_CLOSED"})(h||(h={}));var Ii={name:"list-style-type",initialValue:"none",prefix:!1,type:N.IDENT_VALUE,parse:function(e){switch(e){case"disc":return h.DISC;case"circle":return h.CIRCLE;case"square":return h.SQUARE;case"decimal":return h.DECIMAL;case"cjk-decimal":return h.CJK_DECIMAL;case"decimal-leading-zero":return h.DECIMAL_LEADING_ZERO;case"lower-roman":return h.LOWER_ROMAN;case"upper-roman":return h.UPPER_ROMAN;case"lower-greek":return h.LOWER_GREEK;case"lower-alpha":return h.LOWER_ALPHA;case"upper-alpha":return h.UPPER_ALPHA;case"arabic-indic":return h.ARABIC_INDIC;case"armenian":return h.ARMENIAN;case"bengali":return h.BENGALI;case"cambodian":return h.CAMBODIAN;case"cjk-earthly-branch":return h.CJK_EARTHLY_BRANCH;case"cjk-heavenly-stem":return h.CJK_HEAVENLY_STEM;case"cjk-ideographic":return h.CJK_IDEOGRAPHIC;case"devanagari":return h.DEVANAGARI;case"ethiopic-numeric":return h.ETHIOPIC_NUMERIC;case"georgian":return h.GEORGIAN;case"gujarati":return h.GUJARATI;case"gurmukhi":return h.GURMUKHI;case"hebrew":return h.HEBREW;case"hiragana":return h.HIRAGANA;case"hiragana-iroha":return h.HIRAGANA_IROHA;case"japanese-formal":return h.JAPANESE_FORMAL;case"japanese-informal":return h.JAPANESE_INFORMAL;case"kannada":return h.KANNADA;case"katakana":return h.KATAKANA;case"katakana-iroha":return h.KATAKANA_IROHA;case"khmer":return h.KHMER;case"korean-hangul-formal":return h.KOREAN_HANGUL_FORMAL;case"korean-hanja-formal":return h.KOREAN_HANJA_FORMAL;case"korean-hanja-informal":return h.KOREAN_HANJA_INFORMAL;case"lao":return h.LAO;case"lower-armenian":return h.LOWER_ARMENIAN;case"malayalam":return h.MALAYALAM;case"mongolian":return h.MONGOLIAN;case"myanmar":return h.MYANMAR;case"oriya":return h.ORIYA;case"persian":return h.PERSIAN;case"simp-chinese-formal":return h.SIMP_CHINESE_FORMAL;case"simp-chinese-informal":return h.SIMP_CHINESE_INFORMAL;case"tamil":return h.TAMIL;case"telugu":return h.TELUGU;case"thai":return h.THAI;case"tibetan":return h.TIBETAN;case"trad-chinese-formal":return h.TRAD_CHINESE_FORMAL;case"trad-chinese-informal":return h.TRAD_CHINESE_INFORMAL;case"upper-armenian":return h.UPPER_ARMENIAN;case"disclosure-open":return h.DISCLOSURE_OPEN;case"disclosure-closed":return h.DISCLOSURE_CLOSED;case"none":default:return h.NONE}}},Hn=function(e){return{name:`margin-${e}`,initialValue:"0",prefix:!1,type:N.TOKEN_VALUE}},af=Hn("top"),rf=Hn("right"),cf=Hn("bottom"),lf=Hn("left"),lt;(function(e){e[e.VISIBLE=0]="VISIBLE",e[e.HIDDEN=1]="HIDDEN",e[e.SCROLL=2]="SCROLL",e[e.AUTO=3]="AUTO"})(lt||(lt={}));var pf={name:"overflow",initialValue:"visible",prefix:!1,type:N.LIST,parse:function(e){return e.filter(z).map(function(t){switch(t.value){case"hidden":return lt.HIDDEN;case"scroll":return lt.SCROLL;case"auto":return lt.AUTO;case"visible":default:return lt.VISIBLE}})}},xA;(function(e){e.NORMAL="normal",e.BREAK_WORD="break-word"})(xA||(xA={}));var uf={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:N.IDENT_VALUE,parse:function(e){switch(e){case"break-word":return xA.BREAK_WORD;case"normal":default:return xA.NORMAL}}},Nn=function(e){return{name:`padding-${e}`,initialValue:"0",prefix:!1,type:N.TYPE_VALUE,format:"length-percentage"}},df=Nn("top"),ff=Nn("right"),mf=Nn("bottom"),Bf=Nn("left"),Te;(function(e){e[e.LEFT=0]="LEFT",e[e.CENTER=1]="CENTER",e[e.RIGHT=2]="RIGHT"})(Te||(Te={}));var hf={name:"text-align",initialValue:"left",prefix:!1,type:N.IDENT_VALUE,parse:function(e){switch(e){case"right":return Te.RIGHT;case"center":case"justify":return Te.CENTER;case"left":default:return Te.LEFT}}},ot;(function(e){e[e.STATIC=0]="STATIC",e[e.RELATIVE=1]="RELATIVE",e[e.ABSOLUTE=2]="ABSOLUTE",e[e.FIXED=3]="FIXED",e[e.STICKY=4]="STICKY"})(ot||(ot={}));var gf={name:"position",initialValue:"static",prefix:!1,type:N.IDENT_VALUE,parse:function(e){switch(e){case"relative":return ot.RELATIVE;case"absolute":return ot.ABSOLUTE;case"fixed":return ot.FIXED;case"sticky":return ot.STICKY}return ot.STATIC}},xf={name:"text-shadow",initialValue:"none",type:N.LIST,prefix:!1,parse:function(e){return e.length===1&&Ri(e[0],"none")?[]:Le(e).map(function(t){let A={color:Xe.TRANSPARENT,offsetX:ce,offsetY:ce,blur:ce},n=0;for(let i=0;i<t.length;i++){let s=t[i];dt(s)?(n===0?A.offsetX=s:n===1?A.offsetY=s:A.blur=s,n++):A.color=We.parse(s)}return A})}},je;(function(e){e[e.NONE=0]="NONE",e[e.LOWERCASE=1]="LOWERCASE",e[e.UPPERCASE=2]="UPPERCASE",e[e.CAPITALIZE=3]="CAPITALIZE"})(je||(je={}));var wf={name:"text-transform",initialValue:"none",prefix:!1,type:N.IDENT_VALUE,parse:function(e){switch(e){case"uppercase":return je.UPPERCASE;case"lowercase":return je.LOWERCASE;case"capitalize":return je.CAPITALIZE}return je.NONE}},Qf={name:"transform",initialValue:"none",prefix:!0,type:N.VALUE,parse:function(e){if(e.type===g.IDENT_TOKEN&&e.value==="none")return null;if(e.type===g.FUNCTION){let t=Uf[e.name];if(typeof t>"u")throw new Error(`Attempting to parse an unsupported transform function "${e.name}"`);return t(e.values)}return null}},Cf=function(e){let t=e.filter(function(A){return A.type===g.NUMBER_TOKEN}).map(function(A){return A.number});return t.length===6?t:null},Ef=function(e){let t=e.filter(function(Q){return Q.type===g.NUMBER_TOKEN}).map(function(Q){return Q.number}),A=t[0],n=t[1],i=t[2],s=t[3],o=t[4],r=t[5],a=t[6],c=t[7],l=t[8],d=t[9],f=t[10],u=t[11],p=t[12],B=t[13],w=t[14],x=t[15];return t.length===16?[A,n,o,r,p,B]:null},Uf={matrix:Cf,matrix3d:Ef},_o={type:g.PERCENTAGE_TOKEN,number:50,flags:CA},bf=[_o,_o],vf={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:N.LIST,parse:function(e){let t=e.filter(Y);return t.length!==2?bf:[t[0],t[1]]}},Pt;(function(e){e[e.VISIBLE=0]="VISIBLE",e[e.HIDDEN=1]="HIDDEN",e[e.COLLAPSE=2]="COLLAPSE"})(Pt||(Pt={}));var Ff={name:"visible",initialValue:"none",prefix:!1,type:N.IDENT_VALUE,parse:function(e){switch(e){case"hidden":return Pt.HIDDEN;case"collapse":return Pt.COLLAPSE;case"visible":default:return Pt.VISIBLE}}},mA;(function(e){e.NORMAL="normal",e.BREAK_ALL="break-all",e.KEEP_ALL="keep-all"})(mA||(mA={}));var Hf={name:"word-break",initialValue:"normal",prefix:!1,type:N.IDENT_VALUE,parse:function(e){switch(e){case"break-all":return mA.BREAK_ALL;case"keep-all":return mA.KEEP_ALL;case"normal":default:return mA.NORMAL}}},Nf={name:"z-index",initialValue:"auto",prefix:!1,type:N.VALUE,parse:function(e){if(e.type===g.IDENT_TOKEN)return{auto:!0,order:0};if(Xt(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},yf={name:"opacity",initialValue:"1",type:N.VALUE,prefix:!1,parse:function(e){return Xt(e)?e.number:1}},Rf={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:N.TYPE_VALUE,format:"color"},Kf={name:"text-decoration-line",initialValue:"none",prefix:!1,type:N.LIST,parse:function(e){return e.filter(z).map(function(t){switch(t.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(t){return t!==0})}},If={name:"font-family",initialValue:"",prefix:!1,type:N.LIST,parse:function(e){return e.filter(Of).map(function(t){return t.value})}},Of=function(e){return e.type===g.STRING_TOKEN||e.type===g.IDENT_TOKEN},_f={name:"font-size",initialValue:"0",prefix:!1,type:N.TYPE_VALUE,format:"length"},Sf={name:"font-weight",initialValue:"normal",type:N.VALUE,prefix:!1,parse:function(e){if(Xt(e))return e.number;if(z(e))switch(e.value){case"bold":return 700;case"normal":default:return 400}return 400}},Tf={name:"font-variant",initialValue:"none",type:N.LIST,prefix:!1,parse:function(e){return e.filter(z).map(function(t){return t.value})}},BA;(function(e){e.NORMAL="normal",e.ITALIC="italic",e.OBLIQUE="oblique"})(BA||(BA={}));var Lf={name:"font-style",initialValue:"normal",prefix:!1,type:N.IDENT_VALUE,parse:function(e){switch(e){case"oblique":return BA.OBLIQUE;case"italic":return BA.ITALIC;case"normal":default:return BA.NORMAL}}},ae=function(e,t){return(e&t)!==0},Mf={name:"content",initialValue:"none",type:N.LIST,prefix:!1,parse:function(e){if(e.length===0)return[];let t=e[0];return t.type===g.IDENT_TOKEN&&t.value==="none"?[]:e}},Df={name:"counter-increment",initialValue:"none",prefix:!0,type:N.LIST,parse:function(e){if(e.length===0)return null;let t=e[0];if(t.type===g.IDENT_TOKEN&&t.value==="none")return null;let A=[],n=e.filter(da);for(let i=0;i<n.length;i++){let s=n[i],o=n[i+1];if(s.type===g.IDENT_TOKEN){let r=o&&Xt(o)?o.number:1;A.push({counter:s.value,increment:r})}}return A}},zf={name:"counter-reset",initialValue:"none",prefix:!0,type:N.LIST,parse:function(e){if(e.length===0)return[];let t=[],A=e.filter(da);for(let n=0;n<A.length;n++){let i=A[n],s=A[n+1];if(z(i)&&i.value!=="none"){let o=s&&Xt(s)?s.number:0;t.push({counter:i.value,reset:o})}}return t}},Pf={name:"quotes",initialValue:"none",prefix:!0,type:N.LIST,parse:function(e){if(e.length===0)return null;let t=e[0];if(t.type===g.IDENT_TOKEN&&t.value==="none")return null;let A=[],n=e.filter(td);if(n.length%2!==0)return null;for(let i=0;i<n.length;i+=2){let s=n[i].value,o=n[i+1].value;A.push({open:s,close:o})}return A}},So=function(e,t,A){if(!e)return"";let n=e[Math.min(t,e.length-1)];return n?A?n.open:n.close:""},kf={name:"box-shadow",initialValue:"none",type:N.LIST,prefix:!1,parse:function(e){return e.length===1&&Ri(e[0],"none")?[]:Le(e).map(function(t){let A={color:255,offsetX:ce,offsetY:ce,blur:ce,spread:ce,inset:!1},n=0;for(let i=0;i<t.length;i++){let s=t[i];Ri(s,"inset")?A.inset=!0:dt(s)?(n===0?A.offsetX=s:n===1?A.offsetY=s:n===2?A.blur=s:A.spread=s,n++):A.color=We.parse(s)}return A})}},Vf=function(){function e(t){this.backgroundClip=C(nd,t.backgroundClip),this.backgroundColor=C(id,t.backgroundColor),this.backgroundImage=C(Fd,t.backgroundImage),this.backgroundOrigin=C(Hd,t.backgroundOrigin),this.backgroundPosition=C(Nd,t.backgroundPosition),this.backgroundRepeat=C(yd,t.backgroundRepeat),this.backgroundSize=C(Kd,t.backgroundSize),this.borderTopColor=C(Od,t.borderTopColor),this.borderRightColor=C(_d,t.borderRightColor),this.borderBottomColor=C(Sd,t.borderBottomColor),this.borderLeftColor=C(Td,t.borderLeftColor),this.borderTopLeftRadius=C(Ld,t.borderTopLeftRadius),this.borderTopRightRadius=C(Md,t.borderTopRightRadius),this.borderBottomRightRadius=C(Dd,t.borderBottomRightRadius),this.borderBottomLeftRadius=C(zd,t.borderBottomLeftRadius),this.borderTopStyle=C(Pd,t.borderTopStyle),this.borderRightStyle=C(kd,t.borderRightStyle),this.borderBottomStyle=C(Vd,t.borderBottomStyle),this.borderLeftStyle=C(Xd,t.borderLeftStyle),this.borderTopWidth=C(Gd,t.borderTopWidth),this.borderRightWidth=C(Jd,t.borderRightWidth),this.borderBottomWidth=C(jd,t.borderBottomWidth),this.borderLeftWidth=C(Wd,t.borderLeftWidth),this.boxShadow=C(kf,t.boxShadow),this.color=C(qd,t.color),this.display=C($d,t.display),this.float=C(Yd,t.cssFloat),this.fontFamily=C(If,t.fontFamily),this.fontSize=C(_f,t.fontSize),this.fontStyle=C(Lf,t.fontStyle),this.fontVariant=C(Tf,t.fontVariant),this.fontWeight=C(Sf,t.fontWeight),this.letterSpacing=C(ef,t.letterSpacing),this.lineBreak=C(tf,t.lineBreak),this.lineHeight=C(Af,t.lineHeight),this.listStyleImage=C(sf,t.listStyleImage),this.listStylePosition=C(of,t.listStylePosition),this.listStyleType=C(Ii,t.listStyleType),this.marginTop=C(af,t.marginTop),this.marginRight=C(rf,t.marginRight),this.marginBottom=C(cf,t.marginBottom),this.marginLeft=C(lf,t.marginLeft),this.opacity=C(yf,t.opacity);let A=C(pf,t.overflow);this.overflowX=A[0],this.overflowY=A[A.length>1?1:0],this.overflowWrap=C(uf,t.overflowWrap),this.paddingTop=C(df,t.paddingTop),this.paddingRight=C(ff,t.paddingRight),this.paddingBottom=C(mf,t.paddingBottom),this.paddingLeft=C(Bf,t.paddingLeft),this.position=C(gf,t.position),this.textAlign=C(hf,t.textAlign),this.textDecorationColor=C(Rf,t.textDecorationColor||t.color),this.textDecorationLine=C(Kf,t.textDecorationLine),this.textShadow=C(xf,t.textShadow),this.textTransform=C(wf,t.textTransform),this.transform=C(Qf,t.transform),this.transformOrigin=C(vf,t.transformOrigin),this.visibility=C(Ff,t.visibility),this.wordBreak=C(Hf,t.wordBreak),this.zIndex=C(Nf,t.zIndex)}return e.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===Pt.VISIBLE},e.prototype.isTransparent=function(){return pt(this.backgroundColor)},e.prototype.isTransformed=function(){return this.transform!==null},e.prototype.isPositioned=function(){return this.position!==ot.STATIC},e.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},e.prototype.isFloating=function(){return this.float!==st.NONE},e.prototype.isInlineLevel=function(){return ae(this.display,4)||ae(this.display,33554432)||ae(this.display,268435456)||ae(this.display,536870912)||ae(this.display,67108864)||ae(this.display,134217728)},e}(),Xf=function(){function e(t){this.content=C(Mf,t.content),this.quotes=C(Pf,t.quotes)}return e}(),To=function(){function e(t){this.counterIncrement=C(Df,t.counterIncrement),this.counterReset=C(zf,t.counterReset)}return e}(),C=function(e,t){let A=new ua,n=t!==null&&typeof t<"u"?t.toString():e.initialValue;A.write(n);let i=new zi(A.read());switch(e.type){case N.IDENT_VALUE:var s=i.parseComponentValue();return e.parse(z(s)?s.value:e.initialValue);case N.VALUE:return e.parse(i.parseComponentValue());case N.LIST:return e.parse(i.parseComponentValues());case N.TOKEN_VALUE:return i.parseComponentValue();case N.TYPE_VALUE:switch(e.format){case"angle":return Cn.parse(i.parseComponentValue());case"color":return We.parse(i.parseComponentValue());case"image":return Vi.parse(i.parseComponentValue());case"length":var o=i.parseComponentValue();return dt(o)?o:ce;case"length-percentage":var r=i.parseComponentValue();return Y(r)?r:ce}}throw new Error(`Attempting to parse unsupported css format type ${e.format}`)},Me=function(){function e(t){this.styles=new Vf(window.getComputedStyle(t,null)),this.textNodes=[],this.elements=[],this.styles.transform!==null&&za(t)&&(t.style.transform="none"),this.bounds=Mi(t),this.flags=0}return e}(),dn=function(){function e(t,A){this.text=t,this.bounds=A}return e}(),Gf=function(e,t,A){let n=Wf(e,t),i=[],s=0;return n.forEach(function(o){if(t.textDecorationLine.length||o.trim().length>0)if(ve.SUPPORT_RANGE_BOUNDS)i.push(new dn(o,jf(A,s,o.length)));else{let r=A.splitText(o.length);i.push(new dn(o,Jf(A))),A=r}else ve.SUPPORT_RANGE_BOUNDS||(A=A.splitText(o.length));s+=o.length}),i},Jf=function(e){let t=e.ownerDocument;if(t){let A=t.createElement("html2canvaswrapper");A.appendChild(e.cloneNode(!0));let n=e.parentNode;if(n){n.replaceChild(A,e);let i=Mi(A);return A.firstChild&&n.replaceChild(A.firstChild,A),i}}return new ut(0,0,0,0)},jf=function(e,t,A){let n=e.ownerDocument;if(!n)throw new Error("Node has no owner document");let i=n.createRange();return i.setStart(e,t),i.setEnd(e,t+A),ut.fromClientRect(i.getBoundingClientRect())},Wf=function(e,t){return t.letterSpacing!==0?Qn(e).map(function(A){return Z(A)}):qf(e,t)},qf=function(e,t){let A=Wp(e,{lineBreak:t.lineBreak,wordBreak:t.overflowWrap===xA.BREAK_WORD?"break-word":t.wordBreak}),n=[],i;for(;!(i=A.next()).done;)i.value&&n.push(i.value.slice());return n},$f=function(){function e(t,A){this.text=Zf(t.data,A.textTransform),this.textBounds=Gf(this.text,A,t)}return e}(),Zf=function(e,t){switch(t){case je.LOWERCASE:return e.toLowerCase();case je.CAPITALIZE:return e.replace(Yf,em);case je.UPPERCASE:return e.toUpperCase();default:return e}},Yf=/(^|\s|:|-|\(|\))([a-z])/g,em=function(e,t,A){return e.length>0?t+A.toUpperCase():e},ya=function(e){qe(t,e);function t(A){let n=e.call(this,A)||this;return n.src=A.currentSrc||A.src,n.intrinsicWidth=A.naturalWidth,n.intrinsicHeight=A.naturalHeight,Ge.getInstance().addImage(n.src),n}return t}(Me),Ra=function(e){qe(t,e);function t(A){let n=e.call(this,A)||this;return n.canvas=A,n.intrinsicWidth=A.width,n.intrinsicHeight=A.height,n}return t}(Me),Ka=function(e){qe(t,e);function t(A){let n=e.call(this,A)||this,i=new XMLSerializer;return n.svg=`data:image/svg+xml,${encodeURIComponent(i.serializeToString(A))}`,n.intrinsicWidth=A.width.baseVal.value,n.intrinsicHeight=A.height.baseVal.value,Ge.getInstance().addImage(n.svg),n}return t}(Me),Ia=function(e){qe(t,e);function t(A){let n=e.call(this,A)||this;return n.value=A.value,n}return t}(Me),Oi=function(e){qe(t,e);function t(A){let n=e.call(this,A)||this;return n.start=A.start,n.reversed=typeof A.reversed=="boolean"&&A.reversed===!0,n}return t}(Me),tm=[{type:g.DIMENSION_TOKEN,flags:0,unit:"px",number:3}],Am=[{type:g.PERCENTAGE_TOKEN,flags:0,number:50}],nm=function(e){return e.width>e.height?new ut(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new ut(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e},im=function(e){let t=e.type===sm?new Array(e.value.length+1).join("\u2022"):e.value;return t.length===0?e.placeholder||"":t},fn="checkbox",mn="radio",sm="password",Lo=707406591,Xi=function(e){qe(t,e);function t(A){let n=e.call(this,A)||this;switch(n.type=A.type.toLowerCase(),n.checked=A.checked,n.value=im(A),(n.type===fn||n.type===mn)&&(n.styles.backgroundColor=3739148031,n.styles.borderTopColor=n.styles.borderRightColor=n.styles.borderBottomColor=n.styles.borderLeftColor=2779096575,n.styles.borderTopWidth=n.styles.borderRightWidth=n.styles.borderBottomWidth=n.styles.borderLeftWidth=1,n.styles.borderTopStyle=n.styles.borderRightStyle=n.styles.borderBottomStyle=n.styles.borderLeftStyle=Vt.SOLID,n.styles.backgroundClip=[Re.BORDER_BOX],n.styles.backgroundOrigin=[0],n.bounds=nm(n.bounds)),n.type){case fn:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=tm;break;case mn:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=Am;break}return n}return t}(Me),Oa=function(e){qe(t,e);function t(A){let n=e.call(this,A)||this,i=A.options[A.selectedIndex||0];return n.value=i&&i.text||"",n}return t}(Me),_a=function(e){qe(t,e);function t(A){let n=e.call(this,A)||this;return n.value=A.value,n}return t}(Me),Mo=function(e){return We.parse(zi.create(e).parseComponentValue())},Sa=function(e){qe(t,e);function t(A){let n=e.call(this,A)||this;n.src=A.src,n.width=parseInt(A.width,10)||0,n.height=parseInt(A.height,10)||0,n.backgroundColor=n.styles.backgroundColor;try{if(A.contentWindow&&A.contentWindow.document&&A.contentWindow.document.documentElement){n.tree=Ma(A.contentWindow.document.documentElement);let i=A.contentWindow.document.documentElement?Mo(getComputedStyle(A.contentWindow.document.documentElement).backgroundColor):Xe.TRANSPARENT,s=A.contentWindow.document.body?Mo(getComputedStyle(A.contentWindow.document.body).backgroundColor):Xe.TRANSPARENT;n.backgroundColor=pt(i)?pt(s)?n.styles.backgroundColor:s:i}}catch{}return n}return t}(Me),om=["OL","UL","MENU"],Ta=function(e,t,A){for(let n=e.firstChild,i=void 0;n;n=i)if(i=n.nextSibling,Da(n)&&n.data.trim().length>0)t.textNodes.push(new $f(n,t.styles));else if(_i(n)){let s=La(n);s.styles.isVisible()&&(am(n,s,A)?s.flags|=4:rm(s.styles)&&(s.flags|=2),om.indexOf(n.tagName)!==-1&&(s.flags|=8),t.elements.push(s),!Bn(n)&&!Pa(n)&&!hn(n)&&Ta(n,s,A))}},La=function(e){return fm(e)?new ya(e):ka(e)?new Ra(e):Pa(e)?new Ka(e):lm(e)?new Ia(e):pm(e)?new Oi(e):um(e)?new Xi(e):hn(e)?new Oa(e):Bn(e)?new _a(e):Va(e)?new Sa(e):new Me(e)},Ma=function(e){let t=La(e);return t.flags|=4,Ta(e,t,t),t},am=function(e,t,A){return t.styles.isPositionedWithZIndex()||t.styles.opacity<1||t.styles.isTransformed()||Gi(e)&&A.styles.isTransparent()},rm=function(e){return e.isPositioned()||e.isFloating()},Da=function(e){return e.nodeType===Node.TEXT_NODE},_i=function(e){return e.nodeType===Node.ELEMENT_NODE},za=function(e){return typeof e.style<"u"},cm=function(e){return typeof e.className=="object"},lm=function(e){return e.tagName==="LI"},pm=function(e){return e.tagName==="OL"},um=function(e){return e.tagName==="INPUT"},dm=function(e){return e.tagName==="HTML"},Pa=function(e){return e.tagName==="svg"},Gi=function(e){return e.tagName==="BODY"},ka=function(e){return e.tagName==="CANVAS"},fm=function(e){return e.tagName==="IMG"},Va=function(e){return e.tagName==="IFRAME"},Do=function(e){return e.tagName==="STYLE"},mm=function(e){return e.tagName==="SCRIPT"},Bn=function(e){return e.tagName==="TEXTAREA"},hn=function(e){return e.tagName==="SELECT"},Bm=function(){function e(){this.counters={}}return e.prototype.getCounterValue=function(t){let A=this.counters[t];return A&&A.length?A[A.length-1]:1},e.prototype.getCounterValues=function(t){let A=this.counters[t];return A||[]},e.prototype.pop=function(t){let A=this;t.forEach(function(n){return A.counters[n].pop()})},e.prototype.parse=function(t){let A=this,n=t.counterIncrement,i=t.counterReset,s=!0;n!==null&&n.forEach(function(r){let a=A.counters[r.counter];a&&r.increment!==0&&(s=!1,a[Math.max(0,a.length-1)]+=r.increment)});let o=[];return s&&i.forEach(function(r){let a=A.counters[r.counter];o.push(r.counter),a||(a=A.counters[r.counter]=[]),a.push(r.reset)}),o},e}(),zo={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},Po={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u0554","\u0553","\u0552","\u0551","\u0550","\u054F","\u054E","\u054D","\u054C","\u054B","\u054A","\u0549","\u0548","\u0547","\u0546","\u0545","\u0544","\u0543","\u0542","\u0541","\u0540","\u053F","\u053E","\u053D","\u053C","\u053B","\u053A","\u0539","\u0538","\u0537","\u0536","\u0535","\u0534","\u0533","\u0532","\u0531"]},hm={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["\u05D9\u05F3","\u05D8\u05F3","\u05D7\u05F3","\u05D6\u05F3","\u05D5\u05F3","\u05D4\u05F3","\u05D3\u05F3","\u05D2\u05F3","\u05D1\u05F3","\u05D0\u05F3","\u05EA","\u05E9","\u05E8","\u05E7","\u05E6","\u05E4","\u05E2","\u05E1","\u05E0","\u05DE","\u05DC","\u05DB","\u05D9\u05D8","\u05D9\u05D7","\u05D9\u05D6","\u05D8\u05D6","\u05D8\u05D5","\u05D9","\u05D8","\u05D7","\u05D6","\u05D5","\u05D4","\u05D3","\u05D2","\u05D1","\u05D0"]},gm={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u10F5","\u10F0","\u10EF","\u10F4","\u10EE","\u10ED","\u10EC","\u10EB","\u10EA","\u10E9","\u10E8","\u10E7","\u10E6","\u10E5","\u10E4","\u10F3","\u10E2","\u10E1","\u10E0","\u10DF","\u10DE","\u10DD","\u10F2","\u10DC","\u10DB","\u10DA","\u10D9","\u10D8","\u10D7","\u10F1","\u10D6","\u10D5","\u10D4","\u10D3","\u10D2","\u10D1","\u10D0"]},_t=function(e,t,A,n,i,s){return e<t||e>A?wA(e,i,s.length>0):n.integers.reduce(function(o,r,a){for(;e>=r;)e-=r,o+=n.values[a];return o},"")+s},Xa=function(e,t,A,n){let i="";do A||e--,i=n(e)+i,e/=t;while(e*t>=t);return i},q=function(e,t,A,n,i){let s=A-t+1;return(e<0?"-":"")+(Xa(Math.abs(e),s,n,function(o){return Z(Math.floor(o%s)+t)})+i)},Qt=function(e,t,A){A===void 0&&(A=". ");let n=t.length;return Xa(Math.abs(e),n,!1,function(i){return t[Math.floor(i%n)]})+A},Lt=1,At=2,nt=4,dA=8,Ve=function(e,t,A,n,i,s){if(e<-9999||e>9999)return wA(e,h.CJK_DECIMAL,i.length>0);let o=Math.abs(e),r=i;if(o===0)return t[0]+r;for(let a=0;o>0&&a<=4;a++){let c=o%10;c===0&&ae(s,Lt)&&r!==""?r=t[c]+r:c>1||c===1&&a===0||c===1&&a===1&&ae(s,At)||c===1&&a===1&&ae(s,nt)&&e>100||c===1&&a>1&&ae(s,dA)?r=t[c]+(a>0?A[a-1]:"")+r:c===1&&a>0&&(r=A[a-1]+r),o=Math.floor(o/10)}return(e<0?n:"")+r},ko="\u5341\u767E\u5343\u842C",Vo="\u62FE\u4F70\u4EDF\u842C",Xo="\u30DE\u30A4\u30CA\u30B9",gi="\uB9C8\uC774\uB108\uC2A4",wA=function(e,t,A){let n=A?". ":"",i=A?"\u3001":"",s=A?", ":"",o=A?" ":"";switch(t){case h.DISC:return`\u2022${o}`;case h.CIRCLE:return`\u25E6${o}`;case h.SQUARE:return`\u25FE${o}`;case h.DECIMAL_LEADING_ZERO:var r=q(e,48,57,!0,n);return r.length<4?`0${r}`:r;case h.CJK_DECIMAL:return Qt(e,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",i);case h.LOWER_ROMAN:return _t(e,1,3999,zo,h.DECIMAL,n).toLowerCase();case h.UPPER_ROMAN:return _t(e,1,3999,zo,h.DECIMAL,n);case h.LOWER_GREEK:return q(e,945,969,!1,n);case h.LOWER_ALPHA:return q(e,97,122,!1,n);case h.UPPER_ALPHA:return q(e,65,90,!1,n);case h.ARABIC_INDIC:return q(e,1632,1641,!0,n);case h.ARMENIAN:case h.UPPER_ARMENIAN:return _t(e,1,9999,Po,h.DECIMAL,n);case h.LOWER_ARMENIAN:return _t(e,1,9999,Po,h.DECIMAL,n).toLowerCase();case h.BENGALI:return q(e,2534,2543,!0,n);case h.CAMBODIAN:case h.KHMER:return q(e,6112,6121,!0,n);case h.CJK_EARTHLY_BRANCH:return Qt(e,"\u5B50\u4E11\u5BC5\u536F\u8FB0\u5DF3\u5348\u672A\u7533\u9149\u620C\u4EA5",i);case h.CJK_HEAVENLY_STEM:return Qt(e,"\u7532\u4E59\u4E19\u4E01\u620A\u5DF1\u5E9A\u8F9B\u58EC\u7678",i);case h.CJK_IDEOGRAPHIC:case h.TRAD_CHINESE_INFORMAL:return Ve(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",ko,"\u8CA0",i,At|nt|dA);case h.TRAD_CHINESE_FORMAL:return Ve(e,"\u96F6\u58F9\u8CB3\u53C3\u8086\u4F0D\u9678\u67D2\u634C\u7396",Vo,"\u8CA0",i,Lt|At|nt|dA);case h.SIMP_CHINESE_INFORMAL:return Ve(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",ko,"\u8D1F",i,At|nt|dA);case h.SIMP_CHINESE_FORMAL:return Ve(e,"\u96F6\u58F9\u8D30\u53C1\u8086\u4F0D\u9646\u67D2\u634C\u7396",Vo,"\u8D1F",i,Lt|At|nt|dA);case h.JAPANESE_INFORMAL:return Ve(e,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u4E07",Xo,i,0);case h.JAPANESE_FORMAL:return Ve(e,"\u96F6\u58F1\u5F10\u53C2\u56DB\u4F0D\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343\u4E07",Xo,i,Lt|At|nt);case h.KOREAN_HANGUL_FORMAL:return Ve(e,"\uC601\uC77C\uC774\uC0BC\uC0AC\uC624\uC721\uCE60\uD314\uAD6C","\uC2ED\uBC31\uCC9C\uB9CC",gi,s,Lt|At|nt);case h.KOREAN_HANJA_INFORMAL:return Ve(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u842C",gi,s,0);case h.KOREAN_HANJA_FORMAL:return Ve(e,"\u96F6\u58F9\u8CB3\u53C3\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343",gi,s,Lt|At|nt);case h.DEVANAGARI:return q(e,2406,2415,!0,n);case h.GEORGIAN:return _t(e,1,19999,gm,h.DECIMAL,n);case h.GUJARATI:return q(e,2790,2799,!0,n);case h.GURMUKHI:return q(e,2662,2671,!0,n);case h.HEBREW:return _t(e,1,10999,hm,h.DECIMAL,n);case h.HIRAGANA:return Qt(e,"\u3042\u3044\u3046\u3048\u304A\u304B\u304D\u304F\u3051\u3053\u3055\u3057\u3059\u305B\u305D\u305F\u3061\u3064\u3066\u3068\u306A\u306B\u306C\u306D\u306E\u306F\u3072\u3075\u3078\u307B\u307E\u307F\u3080\u3081\u3082\u3084\u3086\u3088\u3089\u308A\u308B\u308C\u308D\u308F\u3090\u3091\u3092\u3093");case h.HIRAGANA_IROHA:return Qt(e,"\u3044\u308D\u306F\u306B\u307B\u3078\u3068\u3061\u308A\u306C\u308B\u3092\u308F\u304B\u3088\u305F\u308C\u305D\u3064\u306D\u306A\u3089\u3080\u3046\u3090\u306E\u304A\u304F\u3084\u307E\u3051\u3075\u3053\u3048\u3066\u3042\u3055\u304D\u3086\u3081\u307F\u3057\u3091\u3072\u3082\u305B\u3059");case h.KANNADA:return q(e,3302,3311,!0,n);case h.KATAKANA:return Qt(e,"\u30A2\u30A4\u30A6\u30A8\u30AA\u30AB\u30AD\u30AF\u30B1\u30B3\u30B5\u30B7\u30B9\u30BB\u30BD\u30BF\u30C1\u30C4\u30C6\u30C8\u30CA\u30CB\u30CC\u30CD\u30CE\u30CF\u30D2\u30D5\u30D8\u30DB\u30DE\u30DF\u30E0\u30E1\u30E2\u30E4\u30E6\u30E8\u30E9\u30EA\u30EB\u30EC\u30ED\u30EF\u30F0\u30F1\u30F2\u30F3",i);case h.KATAKANA_IROHA:return Qt(e,"\u30A4\u30ED\u30CF\u30CB\u30DB\u30D8\u30C8\u30C1\u30EA\u30CC\u30EB\u30F2\u30EF\u30AB\u30E8\u30BF\u30EC\u30BD\u30C4\u30CD\u30CA\u30E9\u30E0\u30A6\u30F0\u30CE\u30AA\u30AF\u30E4\u30DE\u30B1\u30D5\u30B3\u30A8\u30C6\u30A2\u30B5\u30AD\u30E6\u30E1\u30DF\u30B7\u30F1\u30D2\u30E2\u30BB\u30B9",i);case h.LAO:return q(e,3792,3801,!0,n);case h.MONGOLIAN:return q(e,6160,6169,!0,n);case h.MYANMAR:return q(e,4160,4169,!0,n);case h.ORIYA:return q(e,2918,2927,!0,n);case h.PERSIAN:return q(e,1776,1785,!0,n);case h.TAMIL:return q(e,3046,3055,!0,n);case h.TELUGU:return q(e,3174,3183,!0,n);case h.THAI:return q(e,3664,3673,!0,n);case h.TIBETAN:return q(e,3872,3881,!0,n);case h.DECIMAL:default:return q(e,48,57,!0,n)}},Ga="data-html2canvas-ignore",Go=function(){function e(t,A){if(this.options=A,this.scrolledElements=[],this.referenceElement=t,this.counters=new Bm,this.quoteDepth=0,!t.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(t.ownerDocument.documentElement)}return e.prototype.toIFrame=function(t,A){let n=this,i=xm(t,A);if(!i.contentWindow)return Promise.reject("Unable to find iframe window");let s=t.defaultView.pageXOffset,o=t.defaultView.pageYOffset,r=i.contentWindow,a=r.document,c=wm(i).then(function(){return Ee(n,void 0,void 0,function(){let l;return xe(this,function(d){switch(d.label){case 0:return this.scrolledElements.forEach(Em),r&&(r.scrollTo(A.left,A.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(r.scrollY!==A.top||r.scrollX!==A.left)&&(a.documentElement.style.top=`${-A.top}px`,a.documentElement.style.left=`${-A.left}px`,a.documentElement.style.position="absolute")),l=this.options.onclone,typeof this.clonedReferenceElement>"u"?[2,Promise.reject(`Error finding the ${this.referenceElement.nodeName} in the cloned document`)]:a.fonts&&a.fonts.ready?[4,a.fonts.ready]:[3,2];case 1:d.sent(),d.label=2;case 2:return typeof l=="function"?[2,Promise.resolve().then(function(){return l(a)}).then(function(){return i})]:[2,i]}})})});return a.open(),a.write(`${Qm(document.doctype)}<html></html>`),Cm(this.referenceElement.ownerDocument,s,o),a.replaceChild(a.adoptNode(this.documentElement),a.documentElement),a.close(),c},e.prototype.createElementClone=function(t){return ka(t)?this.createCanvasClone(t):Do(t)?this.createStyleClone(t):t.cloneNode(!1)},e.prototype.createStyleClone=function(t){try{let A=t.sheet;if(A&&A.cssRules){let n=[].slice.call(A.cssRules,0).reduce(function(s,o){return o&&typeof o.cssText=="string"?s+o.cssText:s},""),i=t.cloneNode(!1);return i.textContent=n,i}}catch(A){if(ie.getInstance(this.options.id).error("Unable to access cssRules property",A),A.name!=="SecurityError")throw A}return t.cloneNode(!1)},e.prototype.createCanvasClone=function(t){if(this.options.inlineImages&&t.ownerDocument){let n=t.ownerDocument.createElement("img");try{return n.src=t.toDataURL(),n}catch{ie.getInstance(this.options.id).info("Unable to clone canvas contents, canvas is tainted")}}let A=t.cloneNode(!1);try{A.width=t.width,A.height=t.height;let n=t.getContext("2d"),i=A.getContext("2d");return i&&(n?i.putImageData(n.getImageData(0,0,t.width,t.height),0,0):i.drawImage(t,0,0)),A}catch{}return A},e.prototype.cloneNode=function(t){if(Da(t))return document.createTextNode(t.data);if(!t.ownerDocument)return t.cloneNode(!1);let A=t.ownerDocument.defaultView;if(za(t)&&A){let n=this.createElementClone(t),i=A.getComputedStyle(t),s=A.getComputedStyle(t,":before"),o=A.getComputedStyle(t,":after");this.referenceElement===t&&(this.clonedReferenceElement=n),Gi(n)&&vm(n);let r=this.counters.parse(new To(i)),a=this.resolvePseudoContent(t,n,s,hA.BEFORE);for(let l=t.firstChild;l;l=l.nextSibling)(!_i(l)||!mm(l)&&!l.hasAttribute(Ga)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(l)))&&(!this.options.copyStyles||!_i(l)||!Do(l))&&n.appendChild(this.cloneNode(l));a&&n.insertBefore(a,n.firstChild);let c=this.resolvePseudoContent(t,n,o,hA.AFTER);return c&&n.appendChild(c),this.counters.pop(r),i&&this.options.copyStyles&&!Va(t)&&Jo(i,n),(t.scrollTop!==0||t.scrollLeft!==0)&&this.scrolledElements.push([n,t.scrollLeft,t.scrollTop]),(Bn(t)||hn(t))&&(Bn(n)||hn(n))&&(n.value=t.value),n}return t.cloneNode(!1)},e.prototype.resolvePseudoContent=function(t,A,n,i){let s=this;if(!n)return;let o=n.content,r=A.ownerDocument;if(!r||!o||o==="none"||o==="-moz-alt-content"||n.display==="none")return;this.counters.parse(new To(n));let a=new Xf(n),c=r.createElement("html2canvaspseudoelement");Jo(n,c),a.content.forEach(function(d){if(d.type===g.STRING_TOKEN)c.appendChild(r.createTextNode(d.value));else if(d.type===g.URL_TOKEN){let w=r.createElement("img");w.src=d.value,w.style.opacity="1",c.appendChild(w)}else if(d.type===g.FUNCTION){if(d.name==="attr"){let w=d.values.filter(z);w.length&&c.appendChild(r.createTextNode(t.getAttribute(w[0].value)||""))}else if(d.name==="counter"){let w=d.values.filter(kt),x=w[0],Q=w[1];if(x&&z(x)){let F=s.counters.getCounterValue(x.value),U=Q&&z(Q)?Ii.parse(Q.value):h.DECIMAL;c.appendChild(r.createTextNode(wA(F,U,!1)))}}else if(d.name==="counters"){var f=d.values.filter(kt),u=f[0],p=f[1],B=f[2];if(u&&z(u)){let w=s.counters.getCounterValues(u.value),x=B&&z(B)?Ii.parse(B.value):h.DECIMAL,Q=p&&p.type===g.STRING_TOKEN?p.value:"",F=w.map(function(U){return wA(U,x,!1)}).join(Q);c.appendChild(r.createTextNode(F))}}}else if(d.type===g.IDENT_TOKEN)switch(d.value){case"open-quote":c.appendChild(r.createTextNode(So(a.quotes,s.quoteDepth++,!0)));break;case"close-quote":c.appendChild(r.createTextNode(So(a.quotes,--s.quoteDepth,!1)));break;default:c.appendChild(r.createTextNode(d.value))}}),c.className=`${Si} ${Ti}`;let l=i===hA.BEFORE?` ${Si}`:` ${Ti}`;return cm(A)?A.className.baseValue+=l:A.className+=l,c},e.destroy=function(t){return t.parentNode?(t.parentNode.removeChild(t),!0):!1},e}(),hA;(function(e){e[e.BEFORE=0]="BEFORE",e[e.AFTER=1]="AFTER"})(hA||(hA={}));var xm=function(e,t){let A=e.createElement("iframe");return A.className="html2canvas-container",A.style.visibility="hidden",A.style.position="fixed",A.style.left="-10000px",A.style.top="0px",A.style.border="0",A.width=t.width.toString(),A.height=t.height.toString(),A.scrolling="no",A.setAttribute(Ga,"true"),e.body.appendChild(A),A},wm=function(e){return new Promise(function(t,A){let n=e.contentWindow;if(!n)return A("No window assigned for iframe");let i=n.document;n.onload=e.onload=i.onreadystatechange=function(){n.onload=e.onload=i.onreadystatechange=null;var s=setInterval(function(){i.body.childNodes.length>0&&i.readyState==="complete"&&(clearInterval(s),t(e))},50)}})},Jo=function(e,t){for(let A=e.length-1;A>=0;A--){let n=e.item(A);n!=="content"&&t.style.setProperty(n,e.getPropertyValue(n))}return t},Qm=function(e){let t="";return e&&(t+="<!DOCTYPE ",e.name&&(t+=e.name),e.internalSubset&&(t+=e.internalSubset),e.publicId&&(t+=`"${e.publicId}"`),e.systemId&&(t+=`"${e.systemId}"`),t+=">"),t},Cm=function(e,t,A){e&&e.defaultView&&(t!==e.defaultView.pageXOffset||A!==e.defaultView.pageYOffset)&&e.defaultView.scrollTo(t,A)},Em=function(e){let t=e[0],A=e[1],n=e[2];t.scrollLeft=A,t.scrollTop=n},Um=":before",bm=":after",Si="___html2canvas___pseudoelement_before",Ti="___html2canvas___pseudoelement_after",jo=`{
    content: "" !important;
    display: none !important;
}`,vm=function(e){Fm(e,`.${Si}${Um}${jo}
         .${Ti}${bm}${jo}`)},Fm=function(e,t){let A=e.ownerDocument;if(A){let n=A.createElement("style");n.textContent=t,e.appendChild(n)}},QA;(function(e){e[e.VECTOR=0]="VECTOR",e[e.BEZIER_CURVE=1]="BEZIER_CURVE"})(QA||(QA={}));var Wo=function(e,t){return e.length===t.length?e.some(function(A,n){return A===t[n]}):!1},Hm=function(e,t,A,n,i){return e.map(function(s,o){switch(o){case 0:return s.add(t,A);case 1:return s.add(t+n,A);case 2:return s.add(t+n,A+i);case 3:return s.add(t,A+i)}return s})},b=function(){function e(t,A){this.type=QA.VECTOR,this.x=t,this.y=A}return e.prototype.add=function(t,A){return new e(this.x+t,this.y+A)},e}(),St=function(e,t,A){return new b(e.x+(t.x-e.x)*A,e.y+(t.y-e.y)*A)},tn=function(){function e(t,A,n,i){this.type=QA.BEZIER_CURVE,this.start=t,this.startControl=A,this.endControl=n,this.end=i}return e.prototype.subdivide=function(t,A){let n=St(this.start,this.startControl,t),i=St(this.startControl,this.endControl,t),s=St(this.endControl,this.end,t),o=St(n,i,t),r=St(i,s,t),a=St(o,r,t);return A?new e(this.start,n,o,a):new e(a,r,s,this.end)},e.prototype.add=function(t,A){return new e(this.start.add(t,A),this.startControl.add(t,A),this.endControl.add(t,A),this.end.add(t,A))},e.prototype.reverse=function(){return new e(this.end,this.endControl,this.startControl,this.start)},e}(),Dt=function(e){return e.type===QA.BEZIER_CURVE},Nm=function(){function e(t){let A=t.styles,n=t.bounds,i=uA(A.borderTopLeftRadius,n.width,n.height),s=uA(A.borderTopRightRadius,n.width,n.height),o=uA(A.borderBottomRightRadius,n.width,n.height),r=uA(A.borderBottomLeftRadius,n.width,n.height),a=i[0],c=i[1],l=s[0],d=s[1],f=o[0],u=o[1],p=r[0],B=r[1],w=[];w.push((a+l)/n.width),w.push((p+f)/n.width),w.push((c+B)/n.height),w.push((d+u)/n.height);let x=Math.max.apply(Math,w);x>1&&(a/=x,c/=x,l/=x,d/=x,f/=x,u/=x,p/=x,B/=x);let Q=n.width-l,F=n.height-u,U=n.width-f,y=n.height-B,O=A.borderTopWidth,L=A.borderRightWidth,S=A.borderBottomWidth,v=A.borderLeftWidth,k=P(A.paddingTop,t.bounds.width),D=P(A.paddingRight,t.bounds.width),G=P(A.paddingBottom,t.bounds.width),R=P(A.paddingLeft,t.bounds.width);this.topLeftBorderBox=a>0||c>0?be(n.left,n.top,a,c,ne.TOP_LEFT):new b(n.left,n.top),this.topRightBorderBox=l>0||d>0?be(n.left+Q,n.top,l,d,ne.TOP_RIGHT):new b(n.left+n.width,n.top),this.bottomRightBorderBox=f>0||u>0?be(n.left+U,n.top+F,f,u,ne.BOTTOM_RIGHT):new b(n.left+n.width,n.top+n.height),this.bottomLeftBorderBox=p>0||B>0?be(n.left,n.top+y,p,B,ne.BOTTOM_LEFT):new b(n.left,n.top+n.height),this.topLeftPaddingBox=a>0||c>0?be(n.left+v,n.top+O,Math.max(0,a-v),Math.max(0,c-O),ne.TOP_LEFT):new b(n.left+v,n.top+O),this.topRightPaddingBox=l>0||d>0?be(n.left+Math.min(Q,n.width+v),n.top+O,Q>n.width+v?0:l-v,d-O,ne.TOP_RIGHT):new b(n.left+n.width-L,n.top+O),this.bottomRightPaddingBox=f>0||u>0?be(n.left+Math.min(U,n.width-v),n.top+Math.min(F,n.height+O),Math.max(0,f-L),u-S,ne.BOTTOM_RIGHT):new b(n.left+n.width-L,n.top+n.height-S),this.bottomLeftPaddingBox=p>0||B>0?be(n.left+v,n.top+y,Math.max(0,p-v),B-S,ne.BOTTOM_LEFT):new b(n.left+v,n.top+n.height-S),this.topLeftContentBox=a>0||c>0?be(n.left+v+R,n.top+O+k,Math.max(0,a-(v+R)),Math.max(0,c-(O+k)),ne.TOP_LEFT):new b(n.left+v+R,n.top+O+k),this.topRightContentBox=l>0||d>0?be(n.left+Math.min(Q,n.width+v+R),n.top+O+k,Q>n.width+v+R?0:l-v+R,d-(O+k),ne.TOP_RIGHT):new b(n.left+n.width-(L+D),n.top+O+k),this.bottomRightContentBox=f>0||u>0?be(n.left+Math.min(U,n.width-(v+R)),n.top+Math.min(F,n.height+O+k),Math.max(0,f-(L+D)),u-(S+G),ne.BOTTOM_RIGHT):new b(n.left+n.width-(L+D),n.top+n.height-(S+G)),this.bottomLeftContentBox=p>0||B>0?be(n.left+v+R,n.top+y,Math.max(0,p-(v+R)),B-(S+G),ne.BOTTOM_LEFT):new b(n.left+v+R,n.top+n.height-(S+G))}return e}(),ne;(function(e){e[e.TOP_LEFT=0]="TOP_LEFT",e[e.TOP_RIGHT=1]="TOP_RIGHT",e[e.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",e[e.BOTTOM_LEFT=3]="BOTTOM_LEFT"})(ne||(ne={}));var be=function(e,t,A,n,i){let s=4*((Math.sqrt(2)-1)/3),o=A*s,r=n*s,a=e+A,c=t+n;switch(i){case ne.TOP_LEFT:return new tn(new b(e,c),new b(e,c-r),new b(a-o,t),new b(a,t));case ne.TOP_RIGHT:return new tn(new b(e,t),new b(e+o,t),new b(a,c-r),new b(a,c));case ne.BOTTOM_RIGHT:return new tn(new b(a,t),new b(a,t+r),new b(e+o,c),new b(e,c));case ne.BOTTOM_LEFT:default:return new tn(new b(a,c),new b(a-o,c),new b(e,t+r),new b(e,t))}},gn=function(e){return[e.topLeftBorderBox,e.topRightBorderBox,e.bottomRightBorderBox,e.bottomLeftBorderBox]},ym=function(e){return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox]},xn=function(e){return[e.topLeftPaddingBox,e.topRightPaddingBox,e.bottomRightPaddingBox,e.bottomLeftPaddingBox]},Rm=function(){function e(t,A,n){this.type=0,this.offsetX=t,this.offsetY=A,this.matrix=n,this.target=6}return e}(),An=function(){function e(t,A){this.type=1,this.target=A,this.path=t}return e}(),Km=function(e){return e.type===0},Im=function(e){return e.type===1},Ja=function(){function e(t){this.element=t,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return e}(),ja=function(){function e(t,A){if(this.container=t,this.effects=A.slice(0),this.curves=new Nm(t),t.styles.transform!==null){let n=t.bounds.left+t.styles.transformOrigin[0].number,i=t.bounds.top+t.styles.transformOrigin[1].number,s=t.styles.transform;this.effects.push(new Rm(n,i,s))}if(t.styles.overflowX!==lt.VISIBLE){let n=gn(this.curves),i=xn(this.curves);Wo(n,i)?this.effects.push(new An(n,6)):(this.effects.push(new An(n,2)),this.effects.push(new An(i,4)))}}return e.prototype.getParentEffects=function(){let t=this.effects.slice(0);if(this.container.styles.overflowX!==lt.VISIBLE){let A=gn(this.curves),n=xn(this.curves);Wo(A,n)||t.push(new An(n,6))}return t},e}(),Li=function(e,t,A,n){e.container.elements.forEach(function(i){let s=ae(i.flags,4),o=ae(i.flags,2),r=new ja(i,e.getParentEffects());ae(i.styles.display,2048)&&n.push(r);let a=ae(i.flags,8)?[]:n;if(s||o){let c=s||i.styles.isPositioned()?A:t,l=new Ja(r);if(i.styles.isPositioned()||i.styles.opacity<1||i.styles.isTransformed()){let d=i.styles.zIndex.order;if(d<0){let f=0;c.negativeZIndex.some(function(u,p){return d>u.element.container.styles.zIndex.order?(f=p,!1):f>0}),c.negativeZIndex.splice(f,0,l)}else if(d>0){let f=0;c.positiveZIndex.some(function(u,p){return d>u.element.container.styles.zIndex.order?(f=p+1,!1):f>0}),c.positiveZIndex.splice(f,0,l)}else c.zeroOrAutoZIndexOrTransformedOrOpacity.push(l)}else i.styles.isFloating()?c.nonPositionedFloats.push(l):c.nonPositionedInlineLevel.push(l);Li(r,l,s?l:A,a)}else i.styles.isInlineLevel()?t.inlineLevel.push(r):t.nonInlineLevel.push(r),Li(r,t,A,a);ae(i.flags,8)&&Wa(i,a)})},Wa=function(e,t){let A=e instanceof Oi?e.start:1,n=e instanceof Oi?e.reversed:!1;for(let i=0;i<t.length;i++){let s=t[i];s.container instanceof Ia&&typeof s.container.value=="number"&&s.container.value!==0&&(A=s.container.value),s.listValue=wA(A,s.container.styles.listStyleType,!0),A+=n?-1:1}},Om=function(e){let t=new ja(e,[]),A=new Ja(t),n=[];return Li(t,A,A,n),Wa(t.container,n),A},_m=function(e,t){switch(t){case 0:return nn(e.topLeftBorderBox,e.topLeftPaddingBox,e.topRightBorderBox,e.topRightPaddingBox);case 1:return nn(e.topRightBorderBox,e.topRightPaddingBox,e.bottomRightBorderBox,e.bottomRightPaddingBox);case 2:return nn(e.bottomRightBorderBox,e.bottomRightPaddingBox,e.bottomLeftBorderBox,e.bottomLeftPaddingBox);case 3:default:return nn(e.bottomLeftBorderBox,e.bottomLeftPaddingBox,e.topLeftBorderBox,e.topLeftPaddingBox)}},nn=function(e,t,A,n){let i=[];return Dt(e)?i.push(e.subdivide(.5,!1)):i.push(e),Dt(A)?i.push(A.subdivide(.5,!0)):i.push(A),Dt(n)?i.push(n.subdivide(.5,!0).reverse()):i.push(n),Dt(t)?i.push(t.subdivide(.5,!1).reverse()):i.push(t),i},qa=function(e){let t=e.bounds,A=e.styles;return t.add(A.borderLeftWidth,A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth),-(A.borderTopWidth+A.borderBottomWidth))},wn=function(e){let t=e.styles,A=e.bounds,n=P(t.paddingLeft,A.width),i=P(t.paddingRight,A.width),s=P(t.paddingTop,A.width),o=P(t.paddingBottom,A.width);return A.add(n+t.borderLeftWidth,s+t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth+n+i),-(t.borderTopWidth+t.borderBottomWidth+s+o))},Sm=function(e,t){return e===0?t.bounds:e===2?wn(t):qa(t)},Tm=function(e,t){return e===Re.BORDER_BOX?t.bounds:e===Re.CONTENT_BOX?wn(t):qa(t)},xi=function(e,t,A){let n=Sm(Mt(e.styles.backgroundOrigin,t),e),i=Tm(Mt(e.styles.backgroundClip,t),e),s=Lm(Mt(e.styles.backgroundSize,t),A,n),o=s[0],r=s[1],a=uA(Mt(e.styles.backgroundPosition,t),n.width-o,n.height-r),c=Mm(Mt(e.styles.backgroundRepeat,t),a,s,n,i),l=Math.round(n.left+a[0]),d=Math.round(n.top+a[1]);return[c,l,d,o,r]},Tt=function(e){return z(e)&&e.value===zt.AUTO},sn=function(e){return typeof e=="number"},Lm=function(e,t,A){let n=t[0],i=t[1],s=t[2],o=e[0],r=e[1];if(Y(o)&&r&&Y(r))return[P(o,A.width),P(r,A.height)];let a=sn(s);if(z(o)&&(o.value===zt.CONTAIN||o.value===zt.COVER))return sn(s)?A.width/A.height<s!=(o.value===zt.COVER)?[A.width,A.width/s]:[A.height*s,A.height]:[A.width,A.height];let c=sn(n),l=sn(i),d=c||l;if(Tt(o)&&(!r||Tt(r))){if(c&&l)return[n,i];if(!a&&!d)return[A.width,A.height];if(d&&a){let w=c?n:i*s,x=l?i:n/s;return[w,x]}let p=c?n:A.width,B=l?i:A.height;return[p,B]}if(a){let p=0,B=0;return Y(o)?p=P(o,A.width):Y(r)&&(B=P(r,A.height)),Tt(o)?p=B*s:(!r||Tt(r))&&(B=p/s),[p,B]}let f=null,u=null;if(Y(o)?f=P(o,A.width):r&&Y(r)&&(u=P(r,A.height)),f!==null&&(!r||Tt(r))&&(u=c&&l?f/n*i:A.height),u!==null&&Tt(o)&&(f=c&&l?u/i*n:A.width),f!==null&&u!==null)return[f,u];throw new Error("Unable to calculate background-size for element")},Mt=function(e,t){let A=e[t];return typeof A>"u"?e[0]:A},Mm=function(e,t,A,n,i){let s=t[0],o=t[1],r=A[0],a=A[1];switch(e){case Je.REPEAT_X:return[new b(Math.round(n.left),Math.round(n.top+o)),new b(Math.round(n.left+n.width),Math.round(n.top+o)),new b(Math.round(n.left+n.width),Math.round(a+n.top+o)),new b(Math.round(n.left),Math.round(a+n.top+o))];case Je.REPEAT_Y:return[new b(Math.round(n.left+s),Math.round(n.top)),new b(Math.round(n.left+s+r),Math.round(n.top)),new b(Math.round(n.left+s+r),Math.round(n.height+n.top)),new b(Math.round(n.left+s),Math.round(n.height+n.top))];case Je.NO_REPEAT:return[new b(Math.round(n.left+s),Math.round(n.top+o)),new b(Math.round(n.left+s+r),Math.round(n.top+o)),new b(Math.round(n.left+s+r),Math.round(n.top+o+a)),new b(Math.round(n.left+s),Math.round(n.top+o+a))];default:return[new b(Math.round(i.left),Math.round(i.top)),new b(Math.round(i.left+i.width),Math.round(i.top)),new b(Math.round(i.left+i.width),Math.round(i.height+i.top)),new b(Math.round(i.left),Math.round(i.height+i.top))]}},Dm="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",qo="Hidden Text",zm=function(){function e(t){this._data={},this._document=t}return e.prototype.parseMetrics=function(t,A){let n=this._document.createElement("div"),i=this._document.createElement("img"),s=this._document.createElement("span"),o=this._document.body;n.style.visibility="hidden",n.style.fontFamily=t,n.style.fontSize=A,n.style.margin="0",n.style.padding="0",o.appendChild(n),i.src=Dm,i.width=1,i.height=1,i.style.margin="0",i.style.padding="0",i.style.verticalAlign="baseline",s.style.fontFamily=t,s.style.fontSize=A,s.style.margin="0",s.style.padding="0",s.appendChild(this._document.createTextNode(qo)),n.appendChild(s),n.appendChild(i);let r=i.offsetTop-s.offsetTop+2;n.removeChild(s),n.appendChild(this._document.createTextNode(qo)),n.style.lineHeight="normal",i.style.verticalAlign="super";let a=i.offsetTop-n.offsetTop+2;return o.removeChild(n),{baseline:r,middle:a}},e.prototype.getMetrics=function(t,A){let n=`${t} ${A}`;return typeof this._data[n]>"u"&&(this._data[n]=this.parseMetrics(t,A)),this._data[n]},e}(),Pm=1e4,km=function(){function e(t){this._activeEffects=[],this.canvas=t.canvas?t.canvas:document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),this.options=t,t.canvas||(this.canvas.width=Math.floor(t.width*t.scale),this.canvas.height=Math.floor(t.height*t.scale),this.canvas.style.width=`${t.width}px`,this.canvas.style.height=`${t.height}px`),this.fontMetrics=new zm(document),this.ctx.scale(this.options.scale,this.options.scale),this.ctx.translate(-t.x+t.scrollX,-t.y+t.scrollY),this.ctx.textBaseline="bottom",this._activeEffects=[],ie.getInstance(t.id).debug(`Canvas renderer initialized (${t.width}x${t.height} at ${t.x},${t.y}) with scale ${t.scale}`)}return e.prototype.applyEffects=function(t,A){let n=this;for(;this._activeEffects.length;)this.popEffect();t.filter(function(i){return ae(i.target,A)}).forEach(function(i){return n.applyEffect(i)})},e.prototype.applyEffect=function(t){this.ctx.save(),Km(t)&&(this.ctx.translate(t.offsetX,t.offsetY),this.ctx.transform(t.matrix[0],t.matrix[1],t.matrix[2],t.matrix[3],t.matrix[4],t.matrix[5]),this.ctx.translate(-t.offsetX,-t.offsetY)),Im(t)&&(this.path(t.path),this.ctx.clip()),this._activeEffects.push(t)},e.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},e.prototype.renderStack=function(t){return Ee(this,void 0,void 0,function(){let A;return xe(this,function(n){switch(n.label){case 0:return A=t.element.container.styles,A.isVisible()?(this.ctx.globalAlpha=A.opacity,[4,this.renderStackContent(t)]):[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}})})},e.prototype.renderNode=function(t){return Ee(this,void 0,void 0,function(){return xe(this,function(A){switch(A.label){case 0:return t.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(t)]:[3,3];case 1:return A.sent(),[4,this.renderNodeContent(t)];case 2:A.sent(),A.label=3;case 3:return[2]}})})},e.prototype.renderTextWithLetterSpacing=function(t,A){let n=this;A===0?this.ctx.fillText(t.text,t.bounds.left,t.bounds.top+t.bounds.height):Qn(t.text).map(function(s){return Z(s)}).reduce(function(s,o){return n.ctx.fillText(o,s,t.bounds.top+t.bounds.height),s+n.ctx.measureText(o).width},t.bounds.left)},e.prototype.createFontStyle=function(t){let A=t.fontVariant.filter(function(s){return s==="normal"||s==="small-caps"}).join(""),n=t.fontFamily.join(", "),i=Pi(t.fontSize)?`${t.fontSize.number}${t.fontSize.unit}`:`${t.fontSize.number}px`;return[[t.fontStyle,A,t.fontWeight,i,n].join(" "),n,i]},e.prototype.renderTextNode=function(t,A){return Ee(this,void 0,void 0,function(){let n,i,s,o,r=this;return xe(this,function(a){return n=this.createFontStyle(A),i=n[0],s=n[1],o=n[2],this.ctx.font=i,t.textBounds.forEach(function(c){r.ctx.fillStyle=de(A.color),r.renderTextWithLetterSpacing(c,A.letterSpacing);let l=A.textShadow;l.length&&c.text.trim().length&&(l.slice(0).reverse().forEach(function(d){r.ctx.shadowColor=de(d.color),r.ctx.shadowOffsetX=d.offsetX.number*r.options.scale,r.ctx.shadowOffsetY=d.offsetY.number*r.options.scale,r.ctx.shadowBlur=d.blur.number,r.ctx.fillText(c.text,c.bounds.left,c.bounds.top+c.bounds.height)}),r.ctx.shadowColor="",r.ctx.shadowOffsetX=0,r.ctx.shadowOffsetY=0,r.ctx.shadowBlur=0),A.textDecorationLine.length&&(r.ctx.fillStyle=de(A.textDecorationColor||A.color),A.textDecorationLine.forEach(function(d){switch(d){case 1:var f=r.fontMetrics.getMetrics(s,o).baseline;r.ctx.fillRect(c.bounds.left,Math.round(c.bounds.top+f),c.bounds.width,1);break;case 2:r.ctx.fillRect(c.bounds.left,Math.round(c.bounds.top),c.bounds.width,1);break;case 3:var u=r.fontMetrics.getMetrics(s,o).middle;r.ctx.fillRect(c.bounds.left,Math.ceil(c.bounds.top+u),c.bounds.width,1);break}}))}),[2]})})},e.prototype.renderReplacedElement=function(t,A,n){if(n&&t.intrinsicWidth>0&&t.intrinsicHeight>0){let i=wn(t),s=xn(A);this.path(s),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(n,0,0,t.intrinsicWidth,t.intrinsicHeight,i.left,i.top,i.width,i.height),this.ctx.restore()}},e.prototype.renderNodeContent=function(t){return Ee(this,void 0,void 0,function(){var A,n,i,s,o,r,a,c,l,d,f,u,p,B,w,x,Q,F;return xe(this,function(U){switch(U.label){case 0:this.applyEffects(t.effects,4),A=t.container,n=t.curves,i=A.styles,s=0,o=A.textNodes,U.label=1;case 1:return s<o.length?(r=o[s],[4,this.renderTextNode(r,i)]):[3,4];case 2:U.sent(),U.label=3;case 3:return s++,[3,1];case 4:if(!(A instanceof ya))return[3,8];U.label=5;case 5:return U.trys.push([5,7,,8]),[4,this.options.cache.match(A.src)];case 6:return a=U.sent(),this.renderReplacedElement(A,n,a),[3,8];case 7:return c=U.sent(),ie.getInstance(this.options.id).error(`Error loading image ${A.src}`),[3,8];case 8:if(A instanceof Ra&&this.renderReplacedElement(A,n,A.canvas),!(A instanceof Ka))return[3,12];U.label=9;case 9:return U.trys.push([9,11,,12]),[4,this.options.cache.match(A.svg)];case 10:return a=U.sent(),this.renderReplacedElement(A,n,a),[3,12];case 11:return l=U.sent(),ie.getInstance(this.options.id).error(`Error loading svg ${A.svg.substring(0,255)}`),[3,12];case 12:return A instanceof Sa&&A.tree?(d=new e({id:this.options.id,scale:this.options.scale,backgroundColor:A.backgroundColor,x:0,y:0,scrollX:0,scrollY:0,width:A.width,height:A.height,cache:this.options.cache,windowWidth:A.width,windowHeight:A.height}),[4,d.render(A.tree)]):[3,14];case 13:f=U.sent(),A.width&&A.height&&this.ctx.drawImage(f,0,0,A.width,A.height,A.bounds.left,A.bounds.top,A.bounds.width,A.bounds.height),U.label=14;case 14:if(A instanceof Xi&&(u=Math.min(A.bounds.width,A.bounds.height),A.type===fn?A.checked&&(this.ctx.save(),this.path([new b(A.bounds.left+u*.39363,A.bounds.top+u*.79),new b(A.bounds.left+u*.16,A.bounds.top+u*.5549),new b(A.bounds.left+u*.27347,A.bounds.top+u*.44071),new b(A.bounds.left+u*.39694,A.bounds.top+u*.5649),new b(A.bounds.left+u*.72983,A.bounds.top+u*.23),new b(A.bounds.left+u*.84,A.bounds.top+u*.34085),new b(A.bounds.left+u*.39363,A.bounds.top+u*.79)]),this.ctx.fillStyle=de(Lo),this.ctx.fill(),this.ctx.restore()):A.type===mn&&A.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(A.bounds.left+u/2,A.bounds.top+u/2,u/4,0,Math.PI*2,!0),this.ctx.fillStyle=de(Lo),this.ctx.fill(),this.ctx.restore())),Vm(A)&&A.value.length){switch(this.ctx.font=this.createFontStyle(i)[0],this.ctx.fillStyle=de(i.color),this.ctx.textBaseline="middle",this.ctx.textAlign=Gm(A.styles.textAlign),p=wn(A),B=0,A.styles.textAlign){case Te.CENTER:B+=p.width/2;break;case Te.RIGHT:B+=p.width;break}w=p.add(B,0,0,-p.height/2+1),this.ctx.save(),this.path([new b(p.left,p.top),new b(p.left+p.width,p.top),new b(p.left+p.width,p.top+p.height),new b(p.left,p.top+p.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new dn(A.value,w),i.letterSpacing),this.ctx.restore(),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"}if(!ae(A.styles.display,2048))return[3,20];if(A.styles.listStyleImage===null)return[3,19];if(x=A.styles.listStyleImage,x.type!==fe.URL)return[3,18];a=void 0,Q=x.url,U.label=15;case 15:return U.trys.push([15,17,,18]),[4,this.options.cache.match(Q)];case 16:return a=U.sent(),this.ctx.drawImage(a,A.bounds.left-(a.width+10),A.bounds.top),[3,18];case 17:return F=U.sent(),ie.getInstance(this.options.id).error(`Error loading list-style-image ${Q}`),[3,18];case 18:return[3,20];case 19:t.listValue&&A.styles.listStyleType!==h.NONE&&(this.ctx.font=this.createFontStyle(i)[0],this.ctx.fillStyle=de(i.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",p=new ut(A.bounds.left,A.bounds.top+P(A.styles.paddingTop,A.bounds.width),A.bounds.width,nf(i.lineHeight,i.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new dn(t.listValue,p),i.letterSpacing),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),U.label=20;case 20:return[2]}})})},e.prototype.renderStackContent=function(t){return Ee(this,void 0,void 0,function(){var A,n,i,s,o,r,a,c,l,d,f,u,p,B,w;return xe(this,function(x){switch(x.label){case 0:return[4,this.renderNodeBackgroundAndBorders(t.element)];case 1:x.sent(),A=0,n=t.negativeZIndex,x.label=2;case 2:return A<n.length?(i=n[A],[4,this.renderStack(i)]):[3,5];case 3:x.sent(),x.label=4;case 4:return A++,[3,2];case 5:return[4,this.renderNodeContent(t.element)];case 6:x.sent(),s=0,o=t.nonInlineLevel,x.label=7;case 7:return s<o.length?(i=o[s],[4,this.renderNode(i)]):[3,10];case 8:x.sent(),x.label=9;case 9:return s++,[3,7];case 10:r=0,a=t.nonPositionedFloats,x.label=11;case 11:return r<a.length?(i=a[r],[4,this.renderStack(i)]):[3,14];case 12:x.sent(),x.label=13;case 13:return r++,[3,11];case 14:c=0,l=t.nonPositionedInlineLevel,x.label=15;case 15:return c<l.length?(i=l[c],[4,this.renderStack(i)]):[3,18];case 16:x.sent(),x.label=17;case 17:return c++,[3,15];case 18:d=0,f=t.inlineLevel,x.label=19;case 19:return d<f.length?(i=f[d],[4,this.renderNode(i)]):[3,22];case 20:x.sent(),x.label=21;case 21:return d++,[3,19];case 22:u=0,p=t.zeroOrAutoZIndexOrTransformedOrOpacity,x.label=23;case 23:return u<p.length?(i=p[u],[4,this.renderStack(i)]):[3,26];case 24:x.sent(),x.label=25;case 25:return u++,[3,23];case 26:B=0,w=t.positiveZIndex,x.label=27;case 27:return B<w.length?(i=w[B],[4,this.renderStack(i)]):[3,30];case 28:x.sent(),x.label=29;case 29:return B++,[3,27];case 30:return[2]}})})},e.prototype.mask=function(t){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(t.slice(0).reverse()),this.ctx.closePath()},e.prototype.path=function(t){this.ctx.beginPath(),this.formatPath(t),this.ctx.closePath()},e.prototype.formatPath=function(t){let A=this;t.forEach(function(n,i){let s=Dt(n)?n.start:n;i===0?A.ctx.moveTo(s.x,s.y):A.ctx.lineTo(s.x,s.y),Dt(n)&&A.ctx.bezierCurveTo(n.startControl.x,n.startControl.y,n.endControl.x,n.endControl.y,n.end.x,n.end.y)})},e.prototype.renderRepeat=function(t,A,n,i){this.path(t),this.ctx.fillStyle=A,this.ctx.translate(n,i),this.ctx.fill(),this.ctx.translate(-n,-i)},e.prototype.resizeImage=function(t,A,n){if(t.width===A&&t.height===n)return t;let i=this.canvas.ownerDocument.createElement("canvas");return i.width=A,i.height=n,i.getContext("2d").drawImage(t,0,0,t.width,t.height,0,0,A,n),i},e.prototype.renderBackgroundImage=function(t){return Ee(this,void 0,void 0,function(){let A,n,i,s,o,r;return xe(this,function(a){switch(a.label){case 0:A=t.styles.backgroundImage.length-1,n=function(c){var l,d,f,u,p,B,w,x,Q,F,U,y,O,L,S,v,k,D,G,R,V,ze,Ye,H,K,j,gt,le,et,xt,Oe,Ce;return xe(this,function(Ne){switch(Ne.label){case 0:if(c.type!==fe.URL)return[3,5];l=void 0,d=c.url,Ne.label=1;case 1:return Ne.trys.push([1,3,,4]),[4,i.options.cache.match(d)];case 2:return l=Ne.sent(),[3,4];case 3:return f=Ne.sent(),ie.getInstance(i.options.id).error(`Error loading background-image ${d}`),[3,4];case 4:return l&&(u=xi(t,A,[l.width,l.height,l.width/l.height]),p=u[0],B=u[1],w=u[2],x=u[3],Q=u[4],F=i.ctx.createPattern(i.resizeImage(l,x,Q),"repeat"),i.renderRepeat(p,F,B,w)),[3,6];case 5:Ud(c)?(U=xi(t,A,[null,null,null]),p=U[0],B=U[1],w=U[2],x=U[3],Q=U[4],y=od(c.angle,x,Q),O=y[0],L=y[1],S=y[2],v=y[3],k=y[4],D=document.createElement("canvas"),D.width=x,D.height=Q,G=D.getContext("2d"),R=G.createLinearGradient(L,v,S,k),Ro(c.stops,O).forEach(function(Pe){return R.addColorStop(Pe.stop,de(Pe.color))}),G.fillStyle=R,G.fillRect(0,0,x,Q),x>0&&Q>0&&(F=i.ctx.createPattern(D,"repeat"),i.renderRepeat(p,F,B,w))):bd(c)&&(V=xi(t,A,[null,null,null]),p=V[0],ze=V[1],Ye=V[2],x=V[3],Q=V[4],H=c.position.length===0?[ki]:c.position,B=P(H[0],x),w=P(H[H.length-1],Q),K=ad(c,B,w,x,Q),j=K[0],gt=K[1],j>0&&j>0&&(le=i.ctx.createRadialGradient(ze+B,Ye+w,0,ze+B,Ye+w,j),Ro(c.stops,j*2).forEach(function(Pe){return le.addColorStop(Pe.stop,de(Pe.color))}),i.path(p),i.ctx.fillStyle=le,j!==gt?(et=t.bounds.left+.5*t.bounds.width,xt=t.bounds.top+.5*t.bounds.height,Oe=gt/j,Ce=1/Oe,i.ctx.save(),i.ctx.translate(et,xt),i.ctx.transform(1,0,0,Oe,0,0),i.ctx.translate(-et,-xt),i.ctx.fillRect(ze,Ce*(Ye-xt)+xt,x,Q*Ce),i.ctx.restore()):i.ctx.fill())),Ne.label=6;case 6:return A--,[2]}})},i=this,s=0,o=t.styles.backgroundImage.slice(0).reverse(),a.label=1;case 1:return s<o.length?(r=o[s],[5,n(r)]):[3,4];case 2:a.sent(),a.label=3;case 3:return s++,[3,1];case 4:return[2]}})})},e.prototype.renderBorder=function(t,A,n){return Ee(this,void 0,void 0,function(){return xe(this,function(i){return this.path(_m(n,A)),this.ctx.fillStyle=de(t),this.ctx.fill(),[2]})})},e.prototype.renderNodeBackgroundAndBorders=function(t){return Ee(this,void 0,void 0,function(){let A,n,i,s,o,r,a,c,l=this;return xe(this,function(d){switch(d.label){case 0:return this.applyEffects(t.effects,2),A=t.container.styles,n=!pt(A.backgroundColor)||A.backgroundImage.length,i=[{style:A.borderTopStyle,color:A.borderTopColor},{style:A.borderRightStyle,color:A.borderRightColor},{style:A.borderBottomStyle,color:A.borderBottomColor},{style:A.borderLeftStyle,color:A.borderLeftColor}],s=Xm(Mt(A.backgroundClip,0),t.curves),n||A.boxShadow.length?(this.ctx.save(),this.path(s),this.ctx.clip(),pt(A.backgroundColor)||(this.ctx.fillStyle=de(A.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(t.container)]):[3,2];case 1:d.sent(),this.ctx.restore(),A.boxShadow.slice(0).reverse().forEach(function(f){l.ctx.save();let u=gn(t.curves),p=f.inset?0:Pm,B=Hm(u,-p+(f.inset?1:-1)*f.spread.number,(f.inset?1:-1)*f.spread.number,f.spread.number*(f.inset?-2:2),f.spread.number*(f.inset?-2:2));f.inset?(l.path(u),l.ctx.clip(),l.mask(B)):(l.mask(u),l.ctx.clip(),l.path(B)),l.ctx.shadowOffsetX=f.offsetX.number+p,l.ctx.shadowOffsetY=f.offsetY.number,l.ctx.shadowColor=de(f.color),l.ctx.shadowBlur=f.blur.number,l.ctx.fillStyle=f.inset?de(f.color):"rgba(0,0,0,1)",l.ctx.fill(),l.ctx.restore()}),d.label=2;case 2:o=0,r=0,a=i,d.label=3;case 3:return r<a.length?(c=a[r],c.style!==Vt.NONE&&!pt(c.color)?[4,this.renderBorder(c.color,o,t.curves)]:[3,5]):[3,7];case 4:d.sent(),d.label=5;case 5:o++,d.label=6;case 6:return r++,[3,3];case 7:return[2]}})})},e.prototype.render=function(t){return Ee(this,void 0,void 0,function(){let A;return xe(this,function(n){switch(n.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=de(this.options.backgroundColor),this.ctx.fillRect(this.options.x-this.options.scrollX,this.options.y-this.options.scrollY,this.options.width,this.options.height)),A=Om(t),[4,this.renderStack(A)];case 1:return n.sent(),this.applyEffects([],2),[2,this.canvas]}})})},e}(),Vm=function(e){return e instanceof _a||e instanceof Oa?!0:e instanceof Xi&&e.type!==mn&&e.type!==fn},Xm=function(e,t){switch(e){case Re.BORDER_BOX:return gn(t);case Re.CONTENT_BOX:return ym(t);case Re.PADDING_BOX:default:return xn(t)}},Gm=function(e){switch(e){case Te.CENTER:return"center";case Te.RIGHT:return"right";case Te.LEFT:default:return"left"}},Jm=function(){function e(t){this.canvas=t.canvas?t.canvas:document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),this.options=t,this.canvas.width=Math.floor(t.width*t.scale),this.canvas.height=Math.floor(t.height*t.scale),this.canvas.style.width=`${t.width}px`,this.canvas.style.height=`${t.height}px`,this.ctx.scale(this.options.scale,this.options.scale),this.ctx.translate(-t.x+t.scrollX,-t.y+t.scrollY),ie.getInstance(t.id).debug(`EXPERIMENTAL ForeignObject renderer initialized (${t.width}x${t.height} at ${t.x},${t.y}) with scale ${t.scale}`)}return e.prototype.render=function(t){return Ee(this,void 0,void 0,function(){let A,n;return xe(this,function(i){switch(i.label){case 0:return A=Ki(Math.max(this.options.windowWidth,this.options.width)*this.options.scale,Math.max(this.options.windowHeight,this.options.height)*this.options.scale,this.options.scrollX*this.options.scale,this.options.scrollY*this.options.scale,t),[4,jm(A)];case 1:return n=i.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=de(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(n,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},e}(),jm=function(e){return new Promise(function(t,A){let n=new Image;n.onload=function(){t(n)},n.onerror=A,n.src=`data:image/svg+xml;charset=utf-8,${encodeURIComponent(new XMLSerializer().serializeToString(e))}`})},Wm=void 0,wi=function(e){return We.parse(zi.create(e).parseComponentValue())},qm=function(e,t){return t===void 0&&(t={}),$m(e,t)};typeof window<"u"&&Ge.setContext(window);var $m=function(e,t){return Ee(Wm,void 0,void 0,function(){var A,n,i,s,o,r,a,c,l,d,f,u,p,B,w,x,Q,F,U,y,O,L,S,v,k;return xe(this,function(D){switch(D.label){case 0:if(A=e.ownerDocument,!A)throw new Error("Element is not attached to a Document");if(n=A.defaultView,!n)throw new Error("Document is not attached to a Window");return i=(Math.round(Math.random()*1e3)+Date.now()).toString(16),s=Gi(e)||dm(e)?gp(A):Mi(e),o=s.width,r=s.height,a=s.left,c=s.top,l={allowTaint:!1,imageTimeout:15e3,proxy:void 0,useCORS:!1},d=rn({},l,t),f={backgroundColor:"#ffffff",cache:t.cache?t.cache:Ge.create(i,d),logging:!0,removeContainer:!0,foreignObjectRendering:!1,scale:n.devicePixelRatio||1,windowWidth:n.innerWidth,windowHeight:n.innerHeight,scrollX:n.pageXOffset,scrollY:n.pageYOffset,x:a,y:c,width:Math.ceil(o),height:Math.ceil(r),id:i},u=rn({},f,d,t),p=new ut(u.scrollX,u.scrollY,u.windowWidth,u.windowHeight),ie.create({id:i,enabled:u.logging}),ie.getInstance(i).debug("Starting document clone"),B=new Go(e,{id:i,onclone:u.onclone,ignoreElements:u.ignoreElements,inlineImages:u.foreignObjectRendering,copyStyles:u.foreignObjectRendering}),w=B.clonedReferenceElement,w?[4,B.toIFrame(A,p)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return x=D.sent(),Q=A.documentElement?wi(getComputedStyle(A.documentElement).backgroundColor):Xe.TRANSPARENT,F=A.body?wi(getComputedStyle(A.body).backgroundColor):Xe.TRANSPARENT,U=t.backgroundColor,y=typeof U=="string"?wi(U):U===null?Xe.TRANSPARENT:4294967295,O=e===A.documentElement?pt(Q)?pt(F)?y:F:Q:y,L={id:i,cache:u.cache,canvas:u.canvas,backgroundColor:O,scale:u.scale,x:u.x,y:u.y,scrollX:u.scrollX,scrollY:u.scrollY,width:u.width,height:u.height,windowWidth:u.windowWidth,windowHeight:u.windowHeight},u.foreignObjectRendering?(ie.getInstance(i).debug("Document cloned, using foreign object rendering"),v=new Jm(L),[4,v.render(w)]):[3,3];case 2:return S=D.sent(),[3,5];case 3:return ie.getInstance(i).debug("Document cloned, using computed rendering"),Ge.attachInstance(u.cache),ie.getInstance(i).debug("Starting DOM parsing"),k=Ma(w),Ge.detachInstance(),O===k.styles.backgroundColor&&(k.styles.backgroundColor=Xe.TRANSPARENT),ie.getInstance(i).debug("Starting renderer"),v=new km(L),[4,v.render(k)];case 4:S=D.sent(),D.label=5;case 5:return u.removeContainer===!0&&(Go.destroy(x)||ie.getInstance(i).error("Cannot detach cloned iframe as it is not in the DOM anymore")),ie.getInstance(i).debug("Finished rendering"),ie.destroy(i),Ge.destroy(i),[2,S]}})})},$a=qm;var Zm=function(){let e=function(){let u=document.createElement("canvas"),p=u.getContext("2d");return{canvas:!!p,imageData:!!p.getImageData,dataURL:!!u.toDataURL,btoa:!!window.btoa}}(),t="image/octet-stream";function A(u,p,B){let w=u.width,x=u.height;p===void 0&&(p=w),B===void 0&&(B=x);let Q=document.createElement("canvas"),F=Q.getContext("2d");return Q.width=p,Q.height=B,F.drawImage(u,0,0,w,x,0,0,p,B),Q}function n(u,p,B,w){return u=A(u,B,w),u.toDataURL(p)}function i(u){document.location.href=u}function s(u){let p=document.createElement("img");return p.src=u,p}function o(u){return u=u.toLowerCase().replace(/jpg/i,"jpeg"),`image/${u.match(/png|jpeg|bmp|gif/)[0]}`}function r(u){if(!window.btoa)throw"btoa undefined";let p="";if(typeof u=="string")p=u;else for(let B=0;B<u.length;B++)p+=String.fromCharCode(u[B]);return btoa(p)}function a(u){let p=u.width,B=u.height;return u.getContext("2d").getImageData(0,0,p,B)}function c(u,p){return`data:${p};base64,${u}`}let l=function(u){let p=u.width,B=u.height,w=p*B*3,x=w+54,Q=[66,77,x&255,x>>8&255,x>>16&255,x>>24&255,0,0,0,0,54,0,0,0],F=[40,0,0,0,p&255,p>>8&255,p>>16&255,p>>24&255,B&255,B>>8&255,B>>16&255,B>>24&255,1,0,24,0,0,0,0,0,w&255,w>>8&255,w>>16&255,w>>24&255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],U=(4-p*3%4)%4,y=u.data,O="",L=p<<2,S=B,v=String.fromCharCode;do{let D=L*(S-1),G="";for(let R=0;R<p;R++){let V=R<<2;G+=v(y[D+V+2])+v(y[D+V+1])+v(y[D+V])}for(let R=0;R<U;R++)G+=String.fromCharCode(0);O+=G}while(--S);return r(Q.concat(F))+r(O)},d=function(u,p,B,w){if(e.canvas&&e.dataURL)if(typeof u=="string"&&(u=document.getElementById(u)),w===void 0&&(w="png"),w=o(w),/bmp/.test(w)){let x=a(A(u,p,B)),Q=l(x);i(c(Q,t))}else{let x=n(u,w,p,B);i(x.replace(w,t))}},f=function(u,p,B,w){if(e.canvas&&e.dataURL){if(typeof u=="string"&&(u=document.getElementById(u)),w===void 0&&(w="png"),w=o(w),/bmp/.test(w)){let Q=a(A(u,p,B)),F=l(Q);return s(c(F,"image/bmp"))}let x=n(u,w,p,B);return s(x)}};return{saveAsImage:d,saveAsPNG:function(u,p,B){return d(u,p,B,"png")},saveAsJPEG:function(u,p,B){return d(u,p,B,"jpeg")},saveAsGIF:function(u,p,B){return d(u,p,B,"gif")},saveAsBMP:function(u,p,B){return d(u,p,B,"bmp")},convertToImage:f,convertToPNG:function(u,p,B){return f(u,p,B,"png")},convertToJPEG:function(u,p,B){return f(u,p,B,"jpeg")},convertToGIF:function(u,p,B){return f(u,p,B,"gif")},convertToBMP:function(u,p,B){return f(u,p,B,"bmp")}}}(),Za=Zm;var jn=Be(oi(),1);function EA(e,t){return function(){return e.apply(t,arguments)}}var{toString:Ym}=Object.prototype,{getPrototypeOf:Wi}=Object,Rn=(e=>t=>{let A=Ym.call(t);return e[A]||(e[A]=A.slice(8,-1).toLowerCase())})(Object.create(null)),De=e=>(e=e.toLowerCase(),t=>Rn(t)===e),Kn=e=>t=>typeof t===e,{isArray:Gt}=Array,UA=Kn("undefined");function eB(e){return e!==null&&!UA(e)&&e.constructor!==null&&!UA(e.constructor)&&He(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}var tr=De("ArrayBuffer");function tB(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&tr(e.buffer),t}var AB=Kn("string"),He=Kn("function"),Ar=Kn("number"),In=e=>e!==null&&typeof e=="object",nB=e=>e===!0||e===!1,yn=e=>{if(Rn(e)!=="object")return!1;let t=Wi(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},iB=De("Date"),sB=De("File"),oB=De("Blob"),aB=De("FileList"),rB=e=>In(e)&&He(e.pipe),cB=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||He(e.append)&&((t=Rn(e))==="formdata"||t==="object"&&He(e.toString)&&e.toString()==="[object FormData]"))},lB=De("URLSearchParams"),pB=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function bA(e,t,{allOwnKeys:A=!1}={}){if(e===null||typeof e>"u")return;let n,i;if(typeof e!="object"&&(e=[e]),Gt(e))for(n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else{let s=A?Object.getOwnPropertyNames(e):Object.keys(e),o=s.length,r;for(n=0;n<o;n++)r=s[n],t.call(null,e[r],r,e)}}function nr(e,t){t=t.toLowerCase();let A=Object.keys(e),n=A.length,i;for(;n-- >0;)if(i=A[n],t===i.toLowerCase())return i;return null}var ir=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),sr=e=>!UA(e)&&e!==ir;function ji(){let{caseless:e}=sr(this)&&this||{},t={},A=(n,i)=>{let s=e&&nr(t,i)||i;yn(t[s])&&yn(n)?t[s]=ji(t[s],n):yn(n)?t[s]=ji({},n):Gt(n)?t[s]=n.slice():t[s]=n};for(let n=0,i=arguments.length;n<i;n++)arguments[n]&&bA(arguments[n],A);return t}var uB=(e,t,A,{allOwnKeys:n}={})=>(bA(t,(i,s)=>{A&&He(i)?e[s]=EA(i,A):e[s]=i},{allOwnKeys:n}),e),dB=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),fB=(e,t,A,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),A&&Object.assign(e.prototype,A)},mB=(e,t,A,n)=>{let i,s,o,r={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),s=i.length;s-- >0;)o=i[s],(!n||n(o,e,t))&&!r[o]&&(t[o]=e[o],r[o]=!0);e=A!==!1&&Wi(e)}while(e&&(!A||A(e,t))&&e!==Object.prototype);return t},BB=(e,t,A)=>{e=String(e),(A===void 0||A>e.length)&&(A=e.length),A-=t.length;let n=e.indexOf(t,A);return n!==-1&&n===A},hB=e=>{if(!e)return null;if(Gt(e))return e;let t=e.length;if(!Ar(t))return null;let A=new Array(t);for(;t-- >0;)A[t]=e[t];return A},gB=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Wi(Uint8Array)),xB=(e,t)=>{let n=(e&&e[Symbol.iterator]).call(e),i;for(;(i=n.next())&&!i.done;){let s=i.value;t.call(e,s[0],s[1])}},wB=(e,t)=>{let A,n=[];for(;(A=e.exec(t))!==null;)n.push(A);return n},QB=De("HTMLFormElement"),CB=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(A,n,i){return n.toUpperCase()+i}),Ya=(({hasOwnProperty:e})=>(t,A)=>e.call(t,A))(Object.prototype),EB=De("RegExp"),or=(e,t)=>{let A=Object.getOwnPropertyDescriptors(e),n={};bA(A,(i,s)=>{t(i,s,e)!==!1&&(n[s]=i)}),Object.defineProperties(e,n)},UB=e=>{or(e,(t,A)=>{if(He(e)&&["arguments","caller","callee"].indexOf(A)!==-1)return!1;let n=e[A];if(He(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+A+"'")})}})},bB=(e,t)=>{let A={},n=i=>{i.forEach(s=>{A[s]=!0})};return Gt(e)?n(e):n(String(e).split(t)),A},vB=()=>{},FB=(e,t)=>(e=+e,Number.isFinite(e)?e:t),Ji="abcdefghijklmnopqrstuvwxyz",er="0123456789",ar={DIGIT:er,ALPHA:Ji,ALPHA_DIGIT:Ji+Ji.toUpperCase()+er},HB=(e=16,t=ar.ALPHA_DIGIT)=>{let A="",{length:n}=t;for(;e--;)A+=t[Math.random()*n|0];return A};function NB(e){return!!(e&&He(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}var yB=e=>{let t=new Array(10),A=(n,i)=>{if(In(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[i]=n;let s=Gt(n)?[]:{};return bA(n,(o,r)=>{let a=A(o,i+1);!UA(a)&&(s[r]=a)}),t[i]=void 0,s}}return n};return A(e,0)},RB=De("AsyncFunction"),KB=e=>e&&(In(e)||He(e))&&He(e.then)&&He(e.catch),m={isArray:Gt,isArrayBuffer:tr,isBuffer:eB,isFormData:cB,isArrayBufferView:tB,isString:AB,isNumber:Ar,isBoolean:nB,isObject:In,isPlainObject:yn,isUndefined:UA,isDate:iB,isFile:sB,isBlob:oB,isRegExp:EB,isFunction:He,isStream:rB,isURLSearchParams:lB,isTypedArray:gB,isFileList:aB,forEach:bA,merge:ji,extend:uB,trim:pB,stripBOM:dB,inherits:fB,toFlatObject:mB,kindOf:Rn,kindOfTest:De,endsWith:BB,toArray:hB,forEachEntry:xB,matchAll:wB,isHTMLForm:QB,hasOwnProperty:Ya,hasOwnProp:Ya,reduceDescriptors:or,freezeMethods:UB,toObjectSet:bB,toCamelCase:CB,noop:vB,toFiniteNumber:FB,findKey:nr,global:ir,isContextDefined:sr,ALPHABET:ar,generateString:HB,isSpecCompliantForm:NB,toJSONObject:yB,isAsyncFn:RB,isThenable:KB};function Jt(e,t,A,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),A&&(this.config=A),n&&(this.request=n),i&&(this.response=i)}m.inherits(Jt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:m.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var rr=Jt.prototype,cr={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{cr[e]={value:e}});Object.defineProperties(Jt,cr);Object.defineProperty(rr,"isAxiosError",{value:!0});Jt.from=(e,t,A,n,i,s)=>{let o=Object.create(rr);return m.toFlatObject(e,o,function(a){return a!==Error.prototype},r=>r!=="isAxiosError"),Jt.call(o,e.message,t,A,n,i),o.cause=e,o.name=e.name,s&&Object.assign(o,s),o};var E=Jt;var Xr=Be(Vr(),1),Sn=Xr.default;function os(e){return m.isPlainObject(e)||m.isArray(e)}function Jr(e){return m.endsWith(e,"[]")?e.slice(0,-2):e}function Gr(e,t,A){return e?e.concat(t).map(function(i,s){return i=Jr(i),!A&&s?"["+i+"]":i}).join(A?".":""):t}function mh(e){return m.isArray(e)&&!e.some(os)}var Bh=m.toFlatObject(m,{},null,function(t){return/^is[A-Z]/.test(t)});function hh(e,t,A){if(!m.isObject(e))throw new TypeError("target must be an object");t=t||new(Sn||FormData),A=m.toFlatObject(A,{metaTokens:!0,dots:!1,indexes:!1},!1,function(B,w){return!m.isUndefined(w[B])});let n=A.metaTokens,i=A.visitor||l,s=A.dots,o=A.indexes,a=(A.Blob||typeof Blob<"u"&&Blob)&&m.isSpecCompliantForm(t);if(!m.isFunction(i))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(m.isDate(p))return p.toISOString();if(!a&&m.isBlob(p))throw new E("Blob is not supported. Use a Buffer instead.");return m.isArrayBuffer(p)||m.isTypedArray(p)?a&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function l(p,B,w){let x=p;if(p&&!w&&typeof p=="object"){if(m.endsWith(B,"{}"))B=n?B:B.slice(0,-2),p=JSON.stringify(p);else if(m.isArray(p)&&mh(p)||(m.isFileList(p)||m.endsWith(B,"[]"))&&(x=m.toArray(p)))return B=Jr(B),x.forEach(function(F,U){!(m.isUndefined(F)||F===null)&&t.append(o===!0?Gr([B],U,s):o===null?B:B+"[]",c(F))}),!1}return os(p)?!0:(t.append(Gr(w,B,s),c(p)),!1)}let d=[],f=Object.assign(Bh,{defaultVisitor:l,convertValue:c,isVisitable:os});function u(p,B){if(!m.isUndefined(p)){if(d.indexOf(p)!==-1)throw Error("Circular reference detected in "+B.join("."));d.push(p),m.forEach(p,function(x,Q){(!(m.isUndefined(x)||x===null)&&i.call(t,x,m.isString(Q)?Q.trim():Q,B,f))===!0&&u(x,B?B.concat(Q):[Q])}),d.pop()}}if(!m.isObject(e))throw new TypeError("data must be an object");return u(e),t}var ft=hh;function jr(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function Wr(e,t){this._pairs=[],e&&ft(e,this,t)}var qr=Wr.prototype;qr.append=function(t,A){this._pairs.push([t,A])};qr.toString=function(t){let A=t?function(n){return t.call(this,n,jr)}:jr;return this._pairs.map(function(i){return A(i[0])+"="+A(i[1])},"").join("&")};var $r=Wr;function gh(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function vt(e,t,A){if(!t)return e;let n=A&&A.encode||gh,i=A&&A.serialize,s;if(i?s=i(t,A):s=m.isURLSearchParams(t)?t.toString():new $r(t,A).toString(n),s){let o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}var as=class{constructor(){this.handlers=[]}use(t,A,n){return this.handlers.push({fulfilled:t,rejected:A,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){m.forEach(this.handlers,function(n){n!==null&&t(n)})}},rs=as;var jt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var Zr=Be(require("url"),1),Yr=Zr.default.URLSearchParams;var ee={isNode:!0,classes:{URLSearchParams:Yr,FormData:Sn,Blob:typeof Blob<"u"&&Blob||null},protocols:["http","https","file","data"]};function cs(e,t){return ft(e,new ee.classes.URLSearchParams,Object.assign({visitor:function(A,n,i,s){return ee.isNode&&m.isBuffer(A)?(this.append(n,A.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function xh(e){return m.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function wh(e){let t={},A=Object.keys(e),n,i=A.length,s;for(n=0;n<i;n++)s=A[n],t[s]=e[s];return t}function Qh(e){function t(A,n,i,s){let o=A[s++],r=Number.isFinite(+o),a=s>=A.length;return o=!o&&m.isArray(i)?i.length:o,a?(m.hasOwnProp(i,o)?i[o]=[i[o],n]:i[o]=n,!r):((!i[o]||!m.isObject(i[o]))&&(i[o]=[]),t(A,n,i[o],s)&&m.isArray(i[o])&&(i[o]=wh(i[o])),!r)}if(m.isFormData(e)&&m.isFunction(e.entries)){let A={};return m.forEachEntry(e,(n,i)=>{t(xh(n),i,A,0)}),A}return null}var Tn=Qh;var Ch={"Content-Type":void 0};function Eh(e,t,A){if(m.isString(e))try{return(t||JSON.parse)(e),m.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(A||JSON.stringify)(e)}var Ln={transitional:jt,adapter:["xhr","http"],transformRequest:[function(t,A){let n=A.getContentType()||"",i=n.indexOf("application/json")>-1,s=m.isObject(t);if(s&&m.isHTMLForm(t)&&(t=new FormData(t)),m.isFormData(t))return i&&i?JSON.stringify(Tn(t)):t;if(m.isArrayBuffer(t)||m.isBuffer(t)||m.isStream(t)||m.isFile(t)||m.isBlob(t))return t;if(m.isArrayBufferView(t))return t.buffer;if(m.isURLSearchParams(t))return A.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let r;if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1)return cs(t,this.formSerializer).toString();if((r=m.isFileList(t))||n.indexOf("multipart/form-data")>-1){let a=this.env&&this.env.FormData;return ft(r?{"files[]":t}:t,a&&new a,this.formSerializer)}}return s||i?(A.setContentType("application/json",!1),Eh(t)):t}],transformResponse:[function(t){let A=this.transitional||Ln.transitional,n=A&&A.forcedJSONParsing,i=this.responseType==="json";if(t&&m.isString(t)&&(n&&!this.responseType||i)){let o=!(A&&A.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(r){if(o)throw r.name==="SyntaxError"?E.from(r,E.ERR_BAD_RESPONSE,this,null,this.response):r}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ee.classes.FormData,Blob:ee.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};m.forEach(["delete","get","head"],function(t){Ln.headers[t]={}});m.forEach(["post","put","patch"],function(t){Ln.headers[t]=m.merge(Ch)});var Wt=Ln;var Uh=m.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ec=e=>{let t={},A,n,i;return e&&e.split(`
`).forEach(function(o){i=o.indexOf(":"),A=o.substring(0,i).trim().toLowerCase(),n=o.substring(i+1).trim(),!(!A||t[A]&&Uh[A])&&(A==="set-cookie"?t[A]?t[A].push(n):t[A]=[n]:t[A]=t[A]?t[A]+", "+n:n)}),t};var tc=Symbol("internals");function vA(e){return e&&String(e).trim().toLowerCase()}function Mn(e){return e===!1||e==null?e:m.isArray(e)?e.map(Mn):String(e)}function bh(e){let t=Object.create(null),A=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g,n;for(;n=A.exec(e);)t[n[1]]=n[2];return t}var vh=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ls(e,t,A,n,i){if(m.isFunction(n))return n.call(this,t,A);if(i&&(t=A),!!m.isString(t)){if(m.isString(n))return t.indexOf(n)!==-1;if(m.isRegExp(n))return n.test(t)}}function Fh(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,A,n)=>A.toUpperCase()+n)}function Hh(e,t){let A=m.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+A,{value:function(i,s,o){return this[n].call(this,t,i,s,o)},configurable:!0})})}var qt=class{constructor(t){t&&this.set(t)}set(t,A,n){let i=this;function s(r,a,c){let l=vA(a);if(!l)throw new Error("header name must be a non-empty string");let d=m.findKey(i,l);(!d||i[d]===void 0||c===!0||c===void 0&&i[d]!==!1)&&(i[d||a]=Mn(r))}let o=(r,a)=>m.forEach(r,(c,l)=>s(c,l,a));return m.isPlainObject(t)||t instanceof this.constructor?o(t,A):m.isString(t)&&(t=t.trim())&&!vh(t)?o(ec(t),A):t!=null&&s(A,t,n),this}get(t,A){if(t=vA(t),t){let n=m.findKey(this,t);if(n){let i=this[n];if(!A)return i;if(A===!0)return bh(i);if(m.isFunction(A))return A.call(this,i,n);if(m.isRegExp(A))return A.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,A){if(t=vA(t),t){let n=m.findKey(this,t);return!!(n&&this[n]!==void 0&&(!A||ls(this,this[n],n,A)))}return!1}delete(t,A){let n=this,i=!1;function s(o){if(o=vA(o),o){let r=m.findKey(n,o);r&&(!A||ls(n,n[r],r,A))&&(delete n[r],i=!0)}}return m.isArray(t)?t.forEach(s):s(t),i}clear(t){let A=Object.keys(this),n=A.length,i=!1;for(;n--;){let s=A[n];(!t||ls(this,this[s],s,t,!0))&&(delete this[s],i=!0)}return i}normalize(t){let A=this,n={};return m.forEach(this,(i,s)=>{let o=m.findKey(n,s);if(o){A[o]=Mn(i),delete A[s];return}let r=t?Fh(s):String(s).trim();r!==s&&delete A[s],A[r]=Mn(i),n[r]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){let A=Object.create(null);return m.forEach(this,(n,i)=>{n!=null&&n!==!1&&(A[i]=t&&m.isArray(n)?n.join(", "):n)}),A}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,A])=>t+": "+A).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...A){let n=new this(t);return A.forEach(i=>n.set(i)),n}static accessor(t){let n=(this[tc]=this[tc]={accessors:{}}).accessors,i=this.prototype;function s(o){let r=vA(o);n[r]||(Hh(i,o),n[r]=!0)}return m.isArray(t)?t.forEach(s):s(t),this}};qt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);m.freezeMethods(qt.prototype);m.freezeMethods(qt);var te=qt;function FA(e,t){let A=this||Wt,n=t||A,i=te.from(n.headers),s=n.data;return m.forEach(e,function(r){s=r.call(A,s,i.normalize(),t?t.status:void 0)}),i.normalize(),s}function HA(e){return!!(e&&e.__CANCEL__)}function Ac(e,t,A){E.call(this,e??"canceled",E.ERR_CANCELED,t,A),this.name="CanceledError"}m.inherits(Ac,E,{__CANCEL__:!0});var Ie=Ac;function mt(e,t,A){let n=A.config.validateStatus;!A.status||!n||n(A.status)?e(A):t(new E("Request failed with status code "+A.status,[E.ERR_BAD_REQUEST,E.ERR_BAD_RESPONSE][Math.floor(A.status/100)-4],A.config,A.request,A))}function ps(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function us(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}function Ft(e,t){return e&&!ps(t)?us(e,t):t}var Lc=Be(ic(),1),Mc=Be(require("http"),1),Dc=Be(require("https"),1),zc=Be(require("util"),1),Pc=Be(bc(),1),$e=Be(require("zlib"),1);var yt="1.4.0";function TA(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}var lg=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;function Cs(e,t,A){let n=A&&A.Blob||ee.classes.Blob,i=TA(e);if(t===void 0&&n&&(t=!0),i==="data"){e=i.length?e.slice(i.length+1):e;let s=lg.exec(e);if(!s)throw new E("Invalid URL",E.ERR_INVALID_URL);let o=s[1],r=s[2],a=s[3],c=Buffer.from(decodeURIComponent(a),r?"base64":"utf8");if(t){if(!n)throw new E("Blob is not supported",E.ERR_NOT_SUPPORT);return new n([c],{type:o})}return c}throw new E("Unsupported protocol "+i,E.ERR_NOT_SUPPORT)}var Rt=Be(require("stream"),1);var Fc=Be(require("stream"),1);function pg(e,t){let A=0,n=1e3/t,i=null;return function(o,r){let a=Date.now();if(o||a-A>n)return i&&(clearTimeout(i),i=null),A=a,e.apply(null,r);i||(i=setTimeout(()=>(i=null,A=Date.now(),e.apply(null,r)),n-(a-A)))}}var vc=pg;function ug(e,t){e=e||10;let A=new Array(e),n=new Array(e),i=0,s=0,o;return t=t!==void 0?t:1e3,function(a){let c=Date.now(),l=n[s];o||(o=c),A[i]=a,n[i]=c;let d=s,f=0;for(;d!==i;)f+=A[d++],d=d%e;if(i=(i+1)%e,i===s&&(s=(s+1)%e),c-o<t)return;let u=l&&c-l;return u?Math.round(f*1e3/u):void 0}}var zn=ug;var Pn=Symbol("internals"),Es=class extends Fc.default.Transform{constructor(t){t=m.toFlatObject(t,{maxRate:0,chunkSize:64*1024,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(r,a)=>!m.isUndefined(a[r])),super({readableHighWaterMark:t.chunkSize});let A=this,n=this[Pn]={length:t.length,timeWindow:t.timeWindow,ticksRate:t.ticksRate,chunkSize:t.chunkSize,maxRate:t.maxRate,minChunkSize:t.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null},i=zn(n.ticksRate*t.samplesCount,n.timeWindow);this.on("newListener",r=>{r==="progress"&&(n.isCaptured||(n.isCaptured=!0))});let s=0;n.updateProgress=vc(function(){let a=n.length,c=n.bytesSeen,l=c-s;if(!l||A.destroyed)return;let d=i(l);s=c,process.nextTick(()=>{A.emit("progress",{loaded:c,total:a,progress:a?c/a:void 0,bytes:l,rate:d||void 0,estimated:d&&a&&c<=a?(a-c)/d:void 0})})},n.ticksRate);let o=()=>{n.updateProgress(!0)};this.once("end",o),this.once("error",o)}_read(t){let A=this[Pn];return A.onReadCallback&&A.onReadCallback(),super._read(t)}_transform(t,A,n){let i=this,s=this[Pn],o=s.maxRate,r=this.readableHighWaterMark,a=s.timeWindow,c=1e3/a,l=o/c,d=s.minChunkSize!==!1?Math.max(s.minChunkSize,l*.01):0;function f(p,B){let w=Buffer.byteLength(p);s.bytesSeen+=w,s.bytes+=w,s.isCaptured&&s.updateProgress(),i.push(p)?process.nextTick(B):s.onReadCallback=()=>{s.onReadCallback=null,process.nextTick(B)}}let u=(p,B)=>{let w=Buffer.byteLength(p),x=null,Q=r,F,U=0;if(o){let y=Date.now();(!s.ts||(U=y-s.ts)>=a)&&(s.ts=y,F=l-s.bytes,s.bytes=F<0?-F:0,U=0),F=l-s.bytes}if(o){if(F<=0)return setTimeout(()=>{B(null,p)},a-U);F<Q&&(Q=F)}Q&&w>Q&&w-Q>d&&(x=p.subarray(Q),p=p.subarray(0,Q)),f(p,x?()=>{process.nextTick(B,null,x)}:B)};u(t,function p(B,w){if(B)return n(B);w?u(w,p):n(null)})}setLength(t){return this[Pn].length=+t,this}},Us=Es;var kc=Be(require("events"),1);var Nc=require("util"),yc=require("stream");var{asyncIterator:Hc}=Symbol,dg=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[Hc]?yield*e[Hc]():yield e},kn=dg;var fg=m.ALPHABET.ALPHA_DIGIT+"-_",LA=new Nc.TextEncoder,Bt=`\r
`,mg=LA.encode(Bt),Bg=2,bs=class{constructor(t,A){let{escapeName:n}=this.constructor,i=m.isString(A),s=`Content-Disposition: form-data; name="${n(t)}"${!i&&A.name?`; filename="${n(A.name)}"`:""}${Bt}`;i?A=LA.encode(String(A).replace(/\r?\n|\r\n?/g,Bt)):s+=`Content-Type: ${A.type||"application/octet-stream"}${Bt}`,this.headers=LA.encode(s+Bt),this.contentLength=i?A.byteLength:A.size,this.size=this.headers.byteLength+this.contentLength+Bg,this.name=t,this.value=A}async*encode(){yield this.headers;let{value:t}=this;m.isTypedArray(t)?yield t:yield*kn(t),yield mg}static escapeName(t){return String(t).replace(/[\r\n"]/g,A=>({"\r":"%0D","\n":"%0A",'"':"%22"})[A])}},hg=(e,t,A)=>{let{tag:n="form-data-boundary",size:i=25,boundary:s=n+"-"+m.generateString(i,fg)}=A||{};if(!m.isFormData(e))throw TypeError("FormData instance required");if(s.length<1||s.length>70)throw Error("boundary must be 10-70 characters long");let o=LA.encode("--"+s+Bt),r=LA.encode("--"+s+"--"+Bt+Bt),a=r.byteLength,c=Array.from(e.entries()).map(([d,f])=>{let u=new bs(d,f);return a+=u.size,u});a+=o.byteLength*c.length,a=m.toFiniteNumber(a);let l={"Content-Type":`multipart/form-data; boundary=${s}`};return Number.isFinite(a)&&(l["Content-Length"]=a),t&&t(l),yc.Readable.from(async function*(){for(let d of c)yield o,yield*d.encode();yield r}())},Rc=hg;var Kc=Be(require("stream"),1),vs=class extends Kc.default.Transform{__transform(t,A,n){this.push(t),n()}_transform(t,A,n){if(t.length!==0&&(this._transform=this.__transform,t[0]!==120)){let i=Buffer.alloc(2);i[0]=120,i[1]=156,this.push(i,A)}this.__transform(t,A,n)}},Ic=vs;var gg=(e,t)=>m.isAsyncFn(e)?function(...A){let n=A.pop();e.apply(this,A).then(i=>{try{t?n(null,...t(i)):n(null,i)}catch(s){n(s)}},n)}:e,Oc=gg;var _c={flush:$e.default.constants.Z_SYNC_FLUSH,finishFlush:$e.default.constants.Z_SYNC_FLUSH},xg={flush:$e.default.constants.BROTLI_OPERATION_FLUSH,finishFlush:$e.default.constants.BROTLI_OPERATION_FLUSH},Sc=m.isFunction($e.default.createBrotliDecompress),{http:wg,https:Qg}=Pc.default,Cg=/https:?/,Tc=ee.protocols.map(e=>e+":");function Eg(e){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e)}function Vc(e,t,A){let n=t;if(!n&&n!==!1){let i=(0,Lc.getProxyForUrl)(A);i&&(n=new URL(i))}if(n){if(n.username&&(n.auth=(n.username||"")+":"+(n.password||"")),n.auth){(n.auth.username||n.auth.password)&&(n.auth=(n.auth.username||"")+":"+(n.auth.password||""));let s=Buffer.from(n.auth,"utf8").toString("base64");e.headers["Proxy-Authorization"]="Basic "+s}e.headers.host=e.hostname+(e.port?":"+e.port:"");let i=n.hostname||n.host;e.hostname=i,e.host=i,e.port=n.port,e.path=A,n.protocol&&(e.protocol=n.protocol.includes(":")?n.protocol:`${n.protocol}:`)}e.beforeRedirects.proxy=function(s){Vc(s,t,s.href)}}var Ug=typeof process<"u"&&m.kindOf(process)==="process",bg=e=>new Promise((t,A)=>{let n,i,s=(a,c)=>{i||(i=!0,n&&n(a,c))},o=a=>{s(a),t(a)},r=a=>{s(a,!0),A(a)};e(o,r,a=>n=a).catch(r)}),Xc=Ug&&function(t){return bg(async function(n,i,s){let{data:o,lookup:r,family:a}=t,{responseType:c,responseEncoding:l}=t,d=t.method.toUpperCase(),f,u=!1,p;r&&m.isAsyncFn(r)&&(r=Oc(r,H=>{if(m.isString(H))H=[H,H.indexOf(".")<0?6:4];else if(!m.isArray(H))throw new TypeError("lookup async function must return an array [ip: string, family: number]]");return H}));let B=new kc.default,w=()=>{t.cancelToken&&t.cancelToken.unsubscribe(x),t.signal&&t.signal.removeEventListener("abort",x),B.removeAllListeners()};s((H,K)=>{f=!0,K&&(u=!0,w())});function x(H){B.emit("abort",!H||H.type?new Ie(null,t,p):H)}B.once("abort",i),(t.cancelToken||t.signal)&&(t.cancelToken&&t.cancelToken.subscribe(x),t.signal&&(t.signal.aborted?x():t.signal.addEventListener("abort",x)));let Q=Ft(t.baseURL,t.url),F=new URL(Q,"http://localhost"),U=F.protocol||Tc[0];if(U==="data:"){let H;if(d!=="GET")return mt(n,i,{status:405,statusText:"method not allowed",headers:{},config:t});try{H=Cs(t.url,c==="blob",{Blob:t.env&&t.env.Blob})}catch(K){throw E.from(K,E.ERR_BAD_REQUEST,t)}return c==="text"?(H=H.toString(l),(!l||l==="utf8")&&(H=m.stripBOM(H))):c==="stream"&&(H=Rt.default.Readable.from(H)),mt(n,i,{data:H,status:200,statusText:"OK",headers:new te,config:t})}if(Tc.indexOf(U)===-1)return i(new E("Unsupported protocol "+U,E.ERR_BAD_REQUEST,t));let y=te.from(t.headers).normalize();y.set("User-Agent","axios/"+yt,!1);let O=t.onDownloadProgress,L=t.onUploadProgress,S=t.maxRate,v,k;if(m.isSpecCompliantForm(o)){let H=y.getContentType(/boundary=([-_\w\d]{10,70})/i);o=Rc(o,K=>{y.set(K)},{tag:`axios-${yt}-boundary`,boundary:H&&H[1]||void 0})}else if(m.isFormData(o)&&m.isFunction(o.getHeaders)){if(y.set(o.getHeaders()),!y.hasContentLength())try{let H=await zc.default.promisify(o.getLength).call(o);Number.isFinite(H)&&H>=0&&y.setContentLength(H)}catch{}}else if(m.isBlob(o))o.size&&y.setContentType(o.type||"application/octet-stream"),y.setContentLength(o.size||0),o=Rt.default.Readable.from(kn(o));else if(o&&!m.isStream(o)){if(!Buffer.isBuffer(o))if(m.isArrayBuffer(o))o=Buffer.from(new Uint8Array(o));else if(m.isString(o))o=Buffer.from(o,"utf-8");else return i(new E("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",E.ERR_BAD_REQUEST,t));if(y.setContentLength(o.length,!1),t.maxBodyLength>-1&&o.length>t.maxBodyLength)return i(new E("Request body larger than maxBodyLength limit",E.ERR_BAD_REQUEST,t))}let D=m.toFiniteNumber(y.getContentLength());m.isArray(S)?(v=S[0],k=S[1]):v=k=S,o&&(L||v)&&(m.isStream(o)||(o=Rt.default.Readable.from(o,{objectMode:!1})),o=Rt.default.pipeline([o,new Us({length:D,maxRate:m.toFiniteNumber(v)})],m.noop),L&&o.on("progress",H=>{L(Object.assign(H,{upload:!0}))}));let G;if(t.auth){let H=t.auth.username||"",K=t.auth.password||"";G=H+":"+K}if(!G&&F.username){let H=F.username,K=F.password;G=H+":"+K}G&&y.delete("authorization");let R;try{R=vt(F.pathname+F.search,t.params,t.paramsSerializer).replace(/^\?/,"")}catch(H){let K=new Error(H.message);return K.config=t,K.url=t.url,K.exists=!0,i(K)}y.set("Accept-Encoding","gzip, compress, deflate"+(Sc?", br":""),!1);let V={path:R,method:d,headers:y.toJSON(),agents:{http:t.httpAgent,https:t.httpsAgent},auth:G,protocol:U,family:a,lookup:r,beforeRedirect:Eg,beforeRedirects:{}};t.socketPath?V.socketPath=t.socketPath:(V.hostname=F.hostname,V.port=F.port,Vc(V,t.proxy,U+"//"+F.hostname+(F.port?":"+F.port:"")+V.path));let ze,Ye=Cg.test(V.protocol);if(V.agent=Ye?t.httpsAgent:t.httpAgent,t.transport?ze=t.transport:t.maxRedirects===0?ze=Ye?Dc.default:Mc.default:(t.maxRedirects&&(V.maxRedirects=t.maxRedirects),t.beforeRedirect&&(V.beforeRedirects.config=t.beforeRedirect),ze=Ye?Qg:wg),t.maxBodyLength>-1?V.maxBodyLength=t.maxBodyLength:V.maxBodyLength=1/0,t.insecureHTTPParser&&(V.insecureHTTPParser=t.insecureHTTPParser),p=ze.request(V,function(K){if(p.destroyed)return;let j=[K],gt=+K.headers["content-length"];if(O){let Ce=new Us({length:m.toFiniteNumber(gt),maxRate:m.toFiniteNumber(k)});O&&Ce.on("progress",Ne=>{O(Object.assign(Ne,{download:!0}))}),j.push(Ce)}let le=K,et=K.req||p;if(t.decompress!==!1&&K.headers["content-encoding"])switch((d==="HEAD"||K.statusCode===204)&&delete K.headers["content-encoding"],K.headers["content-encoding"]){case"gzip":case"x-gzip":case"compress":case"x-compress":j.push($e.default.createUnzip(_c)),delete K.headers["content-encoding"];break;case"deflate":j.push(new Ic),j.push($e.default.createUnzip(_c)),delete K.headers["content-encoding"];break;case"br":Sc&&(j.push($e.default.createBrotliDecompress(xg)),delete K.headers["content-encoding"])}le=j.length>1?Rt.default.pipeline(j,m.noop):j[0];let xt=Rt.default.finished(le,()=>{xt(),w()}),Oe={status:K.statusCode,statusText:K.statusMessage,headers:new te(K.headers),config:t,request:et};if(c==="stream")Oe.data=le,mt(n,i,Oe);else{let Ce=[],Ne=0;le.on("data",function(ue){Ce.push(ue),Ne+=ue.length,t.maxContentLength>-1&&Ne>t.maxContentLength&&(u=!0,le.destroy(),i(new E("maxContentLength size of "+t.maxContentLength+" exceeded",E.ERR_BAD_RESPONSE,t,et)))}),le.on("aborted",function(){if(u)return;let ue=new E("maxContentLength size of "+t.maxContentLength+" exceeded",E.ERR_BAD_RESPONSE,t,et);le.destroy(ue),i(ue)}),le.on("error",function(ue){p.destroyed||i(E.from(ue,null,t,et))}),le.on("end",function(){try{let ue=Ce.length===1?Ce[0]:Buffer.concat(Ce);c!=="arraybuffer"&&(ue=ue.toString(l),(!l||l==="utf8")&&(ue=m.stripBOM(ue))),Oe.data=ue}catch(ue){i(E.from(ue,null,t,Oe.request,Oe))}mt(n,i,Oe)})}B.once("abort",Ce=>{le.destroyed||(le.emit("error",Ce),le.destroy())})}),B.once("abort",H=>{i(H),p.destroy(H)}),p.on("error",function(K){i(E.from(K,null,t,p))}),p.on("socket",function(K){K.setKeepAlive(!0,1e3*60)}),t.timeout){let H=parseInt(t.timeout,10);if(isNaN(H)){i(new E("error trying to parse `config.timeout` to int",E.ERR_BAD_OPTION_VALUE,t,p));return}p.setTimeout(H,function(){if(f)return;let j=t.timeout?"timeout of "+t.timeout+"ms exceeded":"timeout exceeded",gt=t.transitional||jt;t.timeoutErrorMessage&&(j=t.timeoutErrorMessage),i(new E(j,gt.clarifyTimeoutError?E.ETIMEDOUT:E.ECONNABORTED,t,p)),x()})}if(m.isStream(o)){let H=!1,K=!1;o.on("end",()=>{H=!0}),o.once("error",j=>{K=!0,p.destroy(j)}),o.on("close",()=>{!H&&!K&&x(new Ie("Request stream has been aborted",t,p))}),o.pipe(p)}else p.end(o)})};var Gc=ee.isStandardBrowserEnv?function(){return{write:function(A,n,i,s,o,r){let a=[];a.push(A+"="+encodeURIComponent(n)),m.isNumber(i)&&a.push("expires="+new Date(i).toGMTString()),m.isString(s)&&a.push("path="+s),m.isString(o)&&a.push("domain="+o),r===!0&&a.push("secure"),document.cookie=a.join("; ")},read:function(A){let n=document.cookie.match(new RegExp("(^|;\\s*)("+A+")=([^;]*)"));return n?decodeURIComponent(n[3]):null},remove:function(A){this.write(A,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}();var Jc=ee.isStandardBrowserEnv?function(){let t=/(msie|trident)/i.test(navigator.userAgent),A=document.createElement("a"),n;function i(s){let o=s;return t&&(A.setAttribute("href",o),o=A.href),A.setAttribute("href",o),{href:A.href,protocol:A.protocol?A.protocol.replace(/:$/,""):"",host:A.host,search:A.search?A.search.replace(/^\?/,""):"",hash:A.hash?A.hash.replace(/^#/,""):"",hostname:A.hostname,port:A.port,pathname:A.pathname.charAt(0)==="/"?A.pathname:"/"+A.pathname}}return n=i(window.location.href),function(o){let r=m.isString(o)?i(o):o;return r.protocol===n.protocol&&r.host===n.host}}():function(){return function(){return!0}}();function jc(e,t){let A=0,n=zn(50,250);return i=>{let s=i.loaded,o=i.lengthComputable?i.total:void 0,r=s-A,a=n(r),c=s<=o;A=s;let l={loaded:s,total:o,progress:o?s/o:void 0,bytes:r,rate:a||void 0,estimated:a&&o&&c?(o-s)/a:void 0,event:i};l[t?"download":"upload"]=!0,e(l)}}var vg=typeof XMLHttpRequest<"u",Wc=vg&&function(e){return new Promise(function(A,n){let i=e.data,s=te.from(e.headers).normalize(),o=e.responseType,r;function a(){e.cancelToken&&e.cancelToken.unsubscribe(r),e.signal&&e.signal.removeEventListener("abort",r)}m.isFormData(i)&&(ee.isStandardBrowserEnv||ee.isStandardBrowserWebWorkerEnv?s.setContentType(!1):s.setContentType("multipart/form-data;",!1));let c=new XMLHttpRequest;if(e.auth){let u=e.auth.username||"",p=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";s.set("Authorization","Basic "+btoa(u+":"+p))}let l=Ft(e.baseURL,e.url);c.open(e.method.toUpperCase(),vt(l,e.params,e.paramsSerializer),!0),c.timeout=e.timeout;function d(){if(!c)return;let u=te.from("getAllResponseHeaders"in c&&c.getAllResponseHeaders()),B={data:!o||o==="text"||o==="json"?c.responseText:c.response,status:c.status,statusText:c.statusText,headers:u,config:e,request:c};mt(function(x){A(x),a()},function(x){n(x),a()},B),c=null}if("onloadend"in c?c.onloadend=d:c.onreadystatechange=function(){!c||c.readyState!==4||c.status===0&&!(c.responseURL&&c.responseURL.indexOf("file:")===0)||setTimeout(d)},c.onabort=function(){c&&(n(new E("Request aborted",E.ECONNABORTED,e,c)),c=null)},c.onerror=function(){n(new E("Network Error",E.ERR_NETWORK,e,c)),c=null},c.ontimeout=function(){let p=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",B=e.transitional||jt;e.timeoutErrorMessage&&(p=e.timeoutErrorMessage),n(new E(p,B.clarifyTimeoutError?E.ETIMEDOUT:E.ECONNABORTED,e,c)),c=null},ee.isStandardBrowserEnv){let u=(e.withCredentials||Jc(l))&&e.xsrfCookieName&&Gc.read(e.xsrfCookieName);u&&s.set(e.xsrfHeaderName,u)}i===void 0&&s.setContentType(null),"setRequestHeader"in c&&m.forEach(s.toJSON(),function(p,B){c.setRequestHeader(B,p)}),m.isUndefined(e.withCredentials)||(c.withCredentials=!!e.withCredentials),o&&o!=="json"&&(c.responseType=e.responseType),typeof e.onDownloadProgress=="function"&&c.addEventListener("progress",jc(e.onDownloadProgress,!0)),typeof e.onUploadProgress=="function"&&c.upload&&c.upload.addEventListener("progress",jc(e.onUploadProgress)),(e.cancelToken||e.signal)&&(r=u=>{c&&(n(!u||u.type?new Ie(null,e,c):u),c.abort(),c=null)},e.cancelToken&&e.cancelToken.subscribe(r),e.signal&&(e.signal.aborted?r():e.signal.addEventListener("abort",r)));let f=TA(l);if(f&&ee.protocols.indexOf(f)===-1){n(new E("Unsupported protocol "+f+":",E.ERR_BAD_REQUEST,e));return}c.send(i||null)})};var Vn={http:Xc,xhr:Wc};m.forEach(Vn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});var qc={getAdapter:e=>{e=m.isArray(e)?e:[e];let{length:t}=e,A,n;for(let i=0;i<t&&(A=e[i],!(n=m.isString(A)?Vn[A.toLowerCase()]:A));i++);if(!n)throw n===!1?new E(`Adapter ${A} is not supported by the environment`,"ERR_NOT_SUPPORT"):new Error(m.hasOwnProp(Vn,A)?`Adapter '${A}' is not available in the build`:`Unknown adapter '${A}'`);if(!m.isFunction(n))throw new TypeError("adapter is not a function");return n},adapters:Vn};function Fs(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ie(null,e)}function Xn(e){return Fs(e),e.headers=te.from(e.headers),e.data=FA.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),qc.getAdapter(e.adapter||Wt.adapter)(e).then(function(n){return Fs(e),n.data=FA.call(e,e.transformResponse,n),n.headers=te.from(n.headers),n},function(n){return HA(n)||(Fs(e),n&&n.response&&(n.response.data=FA.call(e,e.transformResponse,n.response),n.response.headers=te.from(n.response.headers))),Promise.reject(n)})}var $c=e=>e instanceof te?e.toJSON():e;function Ze(e,t){t=t||{};let A={};function n(c,l,d){return m.isPlainObject(c)&&m.isPlainObject(l)?m.merge.call({caseless:d},c,l):m.isPlainObject(l)?m.merge({},l):m.isArray(l)?l.slice():l}function i(c,l,d){if(m.isUndefined(l)){if(!m.isUndefined(c))return n(void 0,c,d)}else return n(c,l,d)}function s(c,l){if(!m.isUndefined(l))return n(void 0,l)}function o(c,l){if(m.isUndefined(l)){if(!m.isUndefined(c))return n(void 0,c)}else return n(void 0,l)}function r(c,l,d){if(d in t)return n(c,l);if(d in e)return n(void 0,c)}let a={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:r,headers:(c,l)=>i($c(c),$c(l),!0)};return m.forEach(Object.keys(Object.assign({},e,t)),function(l){let d=a[l]||i,f=d(e[l],t[l],l);m.isUndefined(f)&&d!==r||(A[l]=f)}),A}var Hs={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Hs[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});var Zc={};Hs.transitional=function(t,A,n){function i(s,o){return"[Axios v"+yt+"] Transitional option '"+s+"'"+o+(n?". "+n:"")}return(s,o,r)=>{if(t===!1)throw new E(i(o," has been removed"+(A?" in "+A:"")),E.ERR_DEPRECATED);return A&&!Zc[o]&&(Zc[o]=!0,console.warn(i(o," has been deprecated since v"+A+" and will be removed in the near future"))),t?t(s,o,r):!0}};function Fg(e,t,A){if(typeof e!="object")throw new E("options must be an object",E.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),i=n.length;for(;i-- >0;){let s=n[i],o=t[s];if(o){let r=e[s],a=r===void 0||o(r,s,e);if(a!==!0)throw new E("option "+s+" must be "+a,E.ERR_BAD_OPTION_VALUE);continue}if(A!==!0)throw new E("Unknown option "+s,E.ERR_BAD_OPTION)}}var Gn={assertOptions:Fg,validators:Hs};var ht=Gn.validators,Yt=class{constructor(t){this.defaults=t,this.interceptors={request:new rs,response:new rs}}request(t,A){typeof t=="string"?(A=A||{},A.url=t):A=t||{},A=Ze(this.defaults,A);let{transitional:n,paramsSerializer:i,headers:s}=A;n!==void 0&&Gn.assertOptions(n,{silentJSONParsing:ht.transitional(ht.boolean),forcedJSONParsing:ht.transitional(ht.boolean),clarifyTimeoutError:ht.transitional(ht.boolean)},!1),i!=null&&(m.isFunction(i)?A.paramsSerializer={serialize:i}:Gn.assertOptions(i,{encode:ht.function,serialize:ht.function},!0)),A.method=(A.method||this.defaults.method||"get").toLowerCase();let o;o=s&&m.merge(s.common,s[A.method]),o&&m.forEach(["delete","get","head","post","put","patch","common"],p=>{delete s[p]}),A.headers=te.concat(o,s);let r=[],a=!0;this.interceptors.request.forEach(function(B){typeof B.runWhen=="function"&&B.runWhen(A)===!1||(a=a&&B.synchronous,r.unshift(B.fulfilled,B.rejected))});let c=[];this.interceptors.response.forEach(function(B){c.push(B.fulfilled,B.rejected)});let l,d=0,f;if(!a){let p=[Xn.bind(this),void 0];for(p.unshift.apply(p,r),p.push.apply(p,c),f=p.length,l=Promise.resolve(A);d<f;)l=l.then(p[d++],p[d++]);return l}f=r.length;let u=A;for(d=0;d<f;){let p=r[d++],B=r[d++];try{u=p(u)}catch(w){B.call(this,w);break}}try{l=Xn.call(this,u)}catch(p){return Promise.reject(p)}for(d=0,f=c.length;d<f;)l=l.then(c[d++],c[d++]);return l}getUri(t){t=Ze(this.defaults,t);let A=Ft(t.baseURL,t.url);return vt(A,t.params,t.paramsSerializer)}};m.forEach(["delete","get","head","options"],function(t){Yt.prototype[t]=function(A,n){return this.request(Ze(n||{},{method:t,url:A,data:(n||{}).data}))}});m.forEach(["post","put","patch"],function(t){function A(n){return function(s,o,r){return this.request(Ze(r||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:s,data:o}))}}Yt.prototype[t]=A(),Yt.prototype[t+"Form"]=A(!0)});var MA=Yt;var DA=class{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let A;this.promise=new Promise(function(s){A=s});let n=this;this.promise.then(i=>{if(!n._listeners)return;let s=n._listeners.length;for(;s-- >0;)n._listeners[s](i);n._listeners=null}),this.promise.then=i=>{let s,o=new Promise(r=>{n.subscribe(r),s=r}).then(i);return o.cancel=function(){n.unsubscribe(s)},o},t(function(s,o,r){n.reason||(n.reason=new Ie(s,o,r),A(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;let A=this._listeners.indexOf(t);A!==-1&&this._listeners.splice(A,1)}static source(){let t;return{token:new DA(function(i){t=i}),cancel:t}}},Yc=DA;function Ns(e){return function(A){return e.apply(null,A)}}function ys(e){return m.isObject(e)&&e.isAxiosError===!0}var Rs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Rs).forEach(([e,t])=>{Rs[t]=e});var el=Rs;function tl(e){let t=new MA(e),A=EA(MA.prototype.request,t);return m.extend(A,MA.prototype,t,{allOwnKeys:!0}),m.extend(A,t,null,{allOwnKeys:!0}),A.create=function(i){return tl(Ze(e,i))},A}var Ae=tl(Wt);Ae.Axios=MA;Ae.CanceledError=Ie;Ae.CancelToken=Yc;Ae.isCancel=HA;Ae.VERSION=yt;Ae.toFormData=ft;Ae.AxiosError=E;Ae.Cancel=Ae.CanceledError;Ae.all=function(t){return Promise.all(t)};Ae.spread=Ns;Ae.isAxiosError=ys;Ae.mergeConfig=Ze;Ae.AxiosHeaders=te;Ae.formToJSON=e=>Tn(m.isHTMLForm(e)?new FormData(e):e);Ae.HttpStatusCode=el;Ae.default=Ae;var Jn=Ae;var{Axios:UC,AxiosError:bC,CanceledError:vC,isCancel:FC,CancelToken:HC,VERSION:NC,all:yC,Cancel:RC,isAxiosError:KC,spread:IC,toFormData:OC,AxiosHeaders:_C,HttpStatusCode:SC,formToJSON:TC,mergeConfig:LC}=Jn;var Ks=class{constructor(t){this.viewer=t.viewer,this.olMap=t.olMap,this.screenId=t.screenId,this.olMapId=t.olMapId,this.viewerId=t.viewerId,this.cameraManager=new PA(t.viewer,t.olMap),this.groupManager=new kA}async createViewPoint(t,A,n,i,s){this.viewer.render();let o=await Al(this.screenId,i,s,this.olMapId,this.viewerId),{position:r,heading:a,pitch:c,roll:l}=this.viewer.camera,{longitude:d,latitude:f,height:u}=Cesium.Cartographic.fromCartesian(r),p=Cesium.Math.toDegrees(d),B=Cesium.Math.toDegrees(f),w=Cesium.Math.toDegrees(a),x=Cesium.Math.toDegrees(c)+90,Q=Cesium.Math.toDegrees(l),F=this.olMap.getView().getZoom(),U=this.olMap.getView().getResolutionForZoom(F),y=this.olMap.getView().getProjection(),O=this.olMap.getView().getCenter(),L=0;y.code_==="EPSG:3857"?(L=U*96*39.37001*11e4/111319,O=ol.proj.transform(O,y,"EPSG:4326")):L=U*96*39.37001*11e4;let S=t.ViewPoint;if(!(S instanceof Array)){let R=t.ViewPoint;S=[],Object.keys(R).length&&S.push(R)}let v={type:"view",name:A,guid:n,CamLon:p,CamLat:B,CamAltitude:u,CamHeading:w,CamTilt:x,CamRoll:Q,GlobeVisible:s,MapVisible:i,Layer2D:{CurrentScale:L,CenterX:O[0],CenterY:O[1]},LinkPlottingPath:n+"_armyMark.sml",SituationFilePath:"",SpecialTopicsPath:""};S.push(v);let k=S,D={parent_code:t.parent_code,ViewPoint:k},G=this.modifyViewPointXML(D);return{newViewPoints:D,base64URL:o,newViewPointXML:G}}async editViewPoint(t,A,n,i,s,o){this.viewer.render();let r=await Al(this.screenId,s,o,this.olMapId,this.viewerId),{position:a,heading:c,pitch:l,roll:d}=this.viewer.camera,{longitude:f,latitude:u,height:p}=Cesium.Cartographic.fromCartesian(a),B=Cesium.Math.toDegrees(f),w=Cesium.Math.toDegrees(u),x=Cesium.Math.toDegrees(c),Q=Cesium.Math.toDegrees(l)+90,F=Cesium.Math.toDegrees(d),U=this.olMap.getView().getZoom(),y=this.olMap.getView().getResolutionForZoom(U),O=this.olMap.getView().getProjection(),L=this.olMap.getView().getCenter(),S=0;O.code_==="EPSG:3857"?(S=y*96*39.37001*11e4/111319,L=ol.proj.transform(L,O,"EPSG:4326")):S=y*96*39.37001*11e4;let v=t.ViewPoint;if(v instanceof Array){for(let R of v)if(R.guid==n){R.name=A,R.guid=i,R.CamLon=B,R.CamLat=w,R.CamAltitude=p,R.CamHeading=x,R.CamTilt=Q,R.CamRoll=F,R.GlobeVisible=o,R.MapVisible=s,R.Layer2D={CurrentScale:S,CenterX:L[0],CenterY:L[1]},R.LinkPlottingPath=i+"_armyMark.sml";break}}else v.name=A,v.guid=i,v.CamLon=B,v.CamLat=w,v.CamAltitude=p,v.CamHeading=x,v.CamTilt=Q,v.CamRoll=F,v.GlobeVisible=o,v.MapVisible=s,v.Layer2D={CurrentScale:S,CenterX:L[0],CenterY:L[1]},v.LinkPlottingPath=i+"_armyMark.sml";let k=v,D={parent_code:t.parent_code,ViewPoint:k},G=this.modifyViewPointXML(D);return{newViewPoints:D,base64URL:r,newViewPointXML:G}}deleteViewPoint(t,A){let n=t.ViewPoint,i={parent_code:t.parent_code,ViewPoint:{}};if(n instanceof Array){let o=n.findIndex(r=>r.guid==A);n.splice(o,1),n.length==1?i.ViewPoint=n[0]:i.ViewPoint=n}let s=this.modifyViewPointXML(i);return{newViewPoints:i,newViewPointXML:s}}saveViewPoint(t){return this.modifyViewPointXML(t)}modifyViewPointXML(t){if(t&&t.ViewPoint&&Object.keys(t.ViewPoint).length){let A={config:{ViewNavigation:JSON.parse(JSON.stringify(t))}},n=t.ViewPoint,i;n instanceof Array?i=n.map(r=>this.dataFormat(r)):i=this.dataFormat(n),A.config.ViewNavigation.ViewPoint=i;let s=A.config.ViewNavigation.parent_code;return delete A.config.ViewNavigation.parent_code,A.config.ViewNavigation["@parent_code"]=s,nl(A)}else return null}async loadViewPointXML(t){let A=null;return Hg(t).then(n=>{A=n}),A}saveViewPointXML(t,A){let n=t.config.ViewNavigation.ViewPoint;if(n instanceof Array)for(let s=0;s<n.length;s++){let o=this.dataFormat(n[s]);n[s]=o}else n=this.dataFormat(n);let i=t.config.ViewNavigation.parent_code;delete t.config.ViewNavigation.parent_code,t.config.ViewNavigation["@parent_code"]=i,Ng(nl(t),A)}dataFormat(t){return{"@type":t.type,"@name":t.name,"@guid":t.guid,"@CamLon":t.CamLon,"@CamLat":t.CamLat,"@CamAltitude":t.CamAltitude,"@CamHeading":t.CamHeading,"@CamTilt":t.CamTilt,"@CamRoll":t.CamRoll,"@LinkPlottingPath":t.LinkPlottingPath,"@SituationFilePath":t.SituationFilePath,"@SpecialTopicsPath":t.SpecialTopicsPath,GlobeVisible:t.GlobeVisible,MapVisible:t.MapVisible,Layer2D:{"@CurrentScale":t.Layer2D.CurrentScale,"@CenterX":t.Layer2D.CenterX,"@CenterY":t.Layer2D.CenterY}}}};async function Al(e,t,A,n,i){let s="",o=null;return t&&A?o=document.getElementById(e):A&&!t?o=document.getElementById(i):!A&&t&&(o=document.getElementById(n)),await $a(o,{useCORS:!0,allowTaint:!0}).then(function(r){let a=r.width,c=r.height;s=Za.convertToImage(r,a,c,"jpg").src}),s}async function Hg(e){try{let t=null;if(await Jn.get(e).then(A=>{t=A}).catch(A=>{t={data:""}}),t.data){let A=t.data,i=new DOMParser().parseFromString(A,"application/xml");return yg.toJSON(A,[i.documentElement.tagName,"0"])}else return""}catch(t){console.log(t)}}function nl(e,t="@"){let A={ignoreAttributes:!1,attributeNamePrefix:t,format:!0};return new jn.XMLBuilder(A).build(e)}function Ng(e,t){if(jn.XMLValidator.validate(e,{allowBooleanAttributes:!0})&&t){let n=new Blob([e],{type:"application/xml"}),i=document.createElement("a");i.href=window.URL.createObjectURL(n),i.download=t,i.click(),window.URL.revokeObjectURL(i.href)}}var yg=function(){function e(A,n,i,s){var o=new DOMParser().parseFromString(A,"application/xml"),r=function(f){var u=f.ownerDocument.createNSResolver(f),p=f.getAttribute("xmlns");return function(B){return u.lookupNamespaceURI(B)||p}}(o.documentElement),a=o.documentElement.getAttribute("xmlns")?"default:":"",c=new Array(+n[1]+1).join("/*")+"/"+a+n[0],l=o.evaluate(c,o,r,XPathResult.ORDERED_NODE_ITERATOR_TYPE,null),d=l.iterateNext();return s=typeof s>"u"?!1:!!s,t(d,s?"":"    ",i)}function t(A,n,i){i=typeof i>"u"?!0:!!i;var s=!!n.length,o={toObj:function(a){var c={};if(a.nodeType==1){if(i&&a.attributes.length)for(var l=0;l<a.attributes.length;l++)c[a.attributes[l].nodeName]=(a.attributes[l].nodeValue||"").toString();if(a.firstChild){for(var d=0,f=0,u=!1,p=a.firstChild;p;p=p.nextSibling)p.nodeType==1?u=!0:p.nodeType==3&&p.nodeValue.match(/[^ \f\n\r\t\v]/)?d++:p.nodeType==4&&f++;if(u)if(d<2&&f<2){o.removeWhite(a);for(var p=a.firstChild;p;p=p.nextSibling)p.nodeType==3?c["#text"]=o.escape(p.nodeValue):p.nodeType==4?c["#cdata"]=o.escape(p.nodeValue):c[p.nodeName]?c[p.nodeName]instanceof Array?c[p.nodeName][c[p.nodeName].length]=o.toObj(p):c[p.nodeName]=[c[p.nodeName],o.toObj(p)]:c[p.nodeName]=o.toObj(p)}else a.attributes.length?c["#text"]=o.escape(o.innerXml(a)):c=o.escape(o.innerXml(a));else if(d)i&&a.attributes.length?c["#text"]=o.escape(o.innerXml(a)):c=o.escape(o.innerXml(a));else if(f)if(f>1)c=o.escape(o.innerXml(a));else for(var p=a.firstChild;p;p=p.nextSibling)c["#cdata"]=o.escape(p.nodeValue)}!a.attributes.length&&!a.firstChild&&(c=null)}else a.nodeType==9?c=o.toObj(a.documentElement):console.log("unhandled node type: "+a.nodeType);return c},toJson:function(a,c,l){var d=c?'"'+c+'"':"";if(a instanceof Array){for(var f=0,u=a.length;f<u;f++)a[f]=o.toJson(a[f],"",l+"	");d+=(c?s?": [":":[":"[")+(a.length>1?`
`+l+"	"+a.join(`,
`+l+"	")+`
`+l:a.join(""))+"]"}else if(a==null)d+=(c&&":")+"null";else if(typeof a=="object"){var p=[];for(var B in a)p[p.length]=o.toJson(a[B],B,l+"	");d+=(c?s?": {":":{":"{")+(p.length>=1?`
`+l+"	"+p.join(`,
`+l+"	")+`
`+l:p.join(""))+"}"}else typeof a=="string"?d+=(c&&(s?": ":":"))+'"'+a.toString()+'"':d+=(c&&(s?": ":":"))+a.toString();return d},innerXml:function(a){var c="";if("innerHTML"in a)c=a.innerHTML;else for(var l=function(f){var u="";if(f.nodeType==1){u+="<"+f.nodeName;for(var p=0;p<f.attributes.length;p++)u+=" "+f.attributes[p].nodeName+'="'+(f.attributes[p].nodeValue||"").toString()+'"';if(f.firstChild){u+=">";for(var B=f.firstChild;B;B=B.nextSibling)u+=l(B);u+="</"+f.nodeName+">"}else u+="/>"}else f.nodeType==3?u+=f.nodeValue:f.nodeType==4&&(u+="<![CDATA["+f.nodeValue+"]]>");return u},d=a.firstChild;d;d=d.nextSibling)c+=l(d);return c},escape:function(a){return a.replace(/[\\]/g,"\\\\").replace(/[\"]/g,'\\"').replace(/[\n]/g,"\\n").replace(/[\r]/g,"\\r")},removeWhite:function(a){a.normalize();for(var c=a.firstChild;c;)if(c.nodeType==3)if(c.nodeValue.match(/[^ \f\n\r\t\v]/))c=c.nextSibling;else{var l=c.nextSibling;a.removeChild(c),c=l}else c.nodeType==1&&o.removeWhite(c),c=c.nextSibling;return a}};A.nodeType==9&&(A=A.documentElement);var r=o.toJson(o.toObj(o.removeWhite(A)),A.nodeName,"	");return"{"+(s?`
`:"")+n+(n?r.replace(/\t/g,n):r.replace(/\t|\n/g,""))+(s?`
`:"")+"}"}return{toJSON:e}}(),il=Ks;globalThis.CESIUM_VERSION="1.105.2";0&&(module.exports={EMExtensionsMixin,EV_CameraManager,EV_GroupManager,EV_ViewPointManager});
