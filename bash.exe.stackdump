Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAAB9F0000 ntdll.dll
7FFAAA8D0000 KERNEL32.DLL
7FFAA8D50000 KERNELBASE.dll
7FFAAAAC0000 USER32.dll
7FFAA9360000 win32u.dll
7FFAAA9A0000 GDI32.dll
7FFAA9550000 gdi32full.dll
7FFAA9390000 msvcp_win.dll
7FFAA9430000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAAA5A0000 advapi32.dll
7FFAAA430000 msvcrt.dll
7FFAAA4E0000 sechost.dll
7FFAA9330000 bcrypt.dll
7FFAAB7E0000 RPCRT4.dll
7FFAA7BF0000 CRYPTBASE.DLL
7FFAA8B60000 bcryptPrimitives.dll
7FFAAB900000 IMM32.DLL
