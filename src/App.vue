<!--
 * @Author: wlj
 * @Date: 2024-03-26 16:55:34
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-22 13:59:45
 * @FilePath: \wrxtzhglrj\src\App.vue
 * @Description: 
-->
<template>
  <router-view />
</template>
<script setup>
import { onMounted } from "vue";
import * as dataApi from "@/service/API/system/userManage.js";
import { dicHandle } from "@/utils/utils.js";
// import * as wxscoket from "@/service/wxsocket.js";
import * as websocket from "@/service/websocketFile.js";
import * as websocketTS from "@/service/websocketTS.js";

onMounted(() => {
  websocket.initWebSocket();
  websocketTS.initWebSocket();
  // window.addEventListener("onmessageTSWS", (e)=>{
  //   debugger
  // });
  let dicList = localStorage.getItem("dictTypeList");
  window.dictTypeList = {};
  if (dicList) {
    window.dictTypeList = JSON.parse(dicList);
  }
  //1.地基平台 2.空基平台 3.天基平台 4.无人机 5.无人艇 6.无人车 7.弹 8.海基平台 9.终端
  //
  window.dataTypeImg = ["./dmz1.png", "./fk.png", "./wx.png", "./fj.png", "./wrt.png", "./wrc.png", "./boom.png", "./wrt.png", "./dmz1_old.png"];
  window.dictValue = (type, value) => {
    if (value === "" || value === undefined || value === null) {
      return "";
    }
    // 业务类型  工作体制 频段 
    if (type == "businessType" || type == "workSystem" ||  type == "frequency") {
      if (!window.dictTypeList[type]) {
        return "缺少对应字典值";
      }
      let arr = value.toString().split(",");
      let str = "";
      arr.map((map, index) => {
        if (index == arr.length - 1) {
          str += window.dictTypeList[type][map];
        } else {
          str += window.dictTypeList[type][map] + ",";
        }
      });
      return str;
    } else {
      return window.dictTypeList[type]?.[value] || "缺少对应字典值";
    }
  };
  // window.wxscoket = wxscoket;
  if (!sessionStorage.getItem("userToken")) {
    return;
  }
  dataApi.getAllDict().then((res) => {
    // window.dictTypeList = {};
    if (res.data.code == 200) {
      let data = res.data.data;
      data.forEach((item) => {
        if (dictTypeList[item.dictType]) {
          dictTypeList[item.dictType][item.dictValue] = item.dictName;
        } else {
          dictTypeList[item.dictType] = {};
          dictTypeList[item.dictType][item.dictValue] = item.dictName;
        }
        // dictTypeList[key] = [];
        // dicHandle(key);
      });
      localStorage.setItem("dictTypeList", JSON.stringify(dictTypeList));
    }
  });
  // wxscoket.init();

  getUserConfig();
});
function getUserConfig() {
  let pos = localStorage.getItem("controlCenterPos");
  pos && (window.controlCenterPos = JSON.parse(pos));
  dataApi.getUserConfig().then((res) => {
    if (res.data.code == 200) {
      res.data.data.forEach((item) => {
        if (item.configType == "postions") {
          window.controlCenterPos = item.value?.split(",") || [];
          localStorage.setItem("controlCenterPos", JSON.stringify(controlCenterPos));
        }
      });
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
</script>

<style scoped lang="less"></style>
