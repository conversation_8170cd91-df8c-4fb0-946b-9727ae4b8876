<!--
 * @Author: DRZ
 * @data: 2023-09-13
 * @@Description:: 
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-06-24 09:11:20
 * @FilePath: \components\BgBorder.vue
-->
<template>
  <div class="bg-border">
    <div
      class="custmoer"
      :style="{
        height: height,
        width: width,
        top: top,
        left: left,
        right: right,
        bottom: bottom,
      }"
    >
      <div class="bg-border-top">
        <div class="bg-border-top-lt"></div>
        <div class="bg-border-top-lt1"></div>
        <div class="bg-border-top-md"></div>
        <div class="bg-border-top-rt"></div>
      </div>
      <div class="bg-border-md" style="height: calc(100% - 38px)">
        <div class="bg-border-md-lt"></div>
        <div class="bg-border-md-md"></div>
        <div class="bg-border-md-rt"></div>
      </div>
      <div class="bg-border-bottom">
        <div class="bg-border-bottom-lt"></div>
        <div class="bg-border-bottom-md"></div>
        <div class="bg-border-bottom-rt"></div>
      </div>
      <div class="custom-popup-mian">
        <div :class="heardeState == 'true' ? 'header' : 'header1'">
          <div class="header-title">
            <!-- <img style="margin-right: 10px" src="../../public/images/bgBorder/装饰一级bt_03.png" width="19" height="13" /> -->
            <span></span>
            <slot name="header">
              <h3 style="color: white">我是标题</h3>
            </slot>
          </div>
          <div class="headerLine">
            <div class="headerLine-left"></div>
            <div class="headerLine-md"></div>
            <div class="headerLine-right"></div>
          </div>
        </div>
        <div class="content">
          <slot name="content">
            <div style="color: white">我是内容主体</div>
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
// 组件接收的参数
const props = defineProps({
  height: {
    type: String,
    default: "95%",
  },
  width: {
    type: String,
    default: "98%",
  },
  top: {
    type: String,
    default: "",
  },
  left: {
    type: String,
    default: "",
  },
  right: {
    type: String,
    default: "",
  },
  bottom: {
    type: String,
    default: "",
  },
  heardeState: {
    type: String,
    default: "true",
  },
});
</script>
<style scoped lang="less">
@import "../assets/style/theme/style.less";

.bg-border {
  // box-sizing: border-box;
  // position: fixed;
  // top: 0;
  // left: 0;
  // z-index: 0;
  // padding: 20px;
  // width: 100%;
  // height: 100%;
  //   pointer-events: none;
  box-sizing: border-box;
  position: fixed;
  top: 58px;
  left: 0;
  z-index: 0;
  padding: 15px 20px;
  width: 100%;
  height: calc(100% - 58px);
}

.custmoer {
  position: absolute;
}

.bg-border-top {
  display: flex;
}

.bg-border-top-lt {
  width: 30px;
  height: 28px;
  // background: url("../../public/images/bgBorder/qb_03.png");
  background: @qb_03;

  transform: translateY(1px);
}

.bg-border-top-lt1 {
  flex: 1;
  height: 28px;
  // background: url("../../public/images/bgBorder/qb_04.png");
  background: @qb_04;

  transform: translateY(1px);
}

.bg-border-top-md {
  width: 1023px;
  height: 28px;
  // background: url("../../public/images/bgBorder/qb_05.png");
  background: @qb_05;
}

.bg-border-top-rt {
  width: 295px;
  height: 28px;
  // background: url("../../public/images/bgBorder/qb_06.png");
  background: @qb_06;
}

.bg-border-md {
  display: flex;
}

.bg-border-md-lt {
  width: 24px;
  height: 100%;
  background-repeat: repeat-y;
  // background: url("../../public/images/bgBorder/模型库DK_06.png");
  background: @DK_06;
}

.bg-border-md-md {
  flex: 1;
  height: 100%;
  background-repeat: repeat-y;
  // background: url("../../public/images/bgBorder/模型库DK_08.png");
  background: @DK_08;
}

.bg-border-md-rt {
  width: 200px;
  height: 100%;
  background-repeat: repeat-y;
  // background: url("../../public/images/bgBorder/模型库DK_09.png");
  background: @DK_09;
}

.bg-border-bottom {
  display: flex;
  position: relative;
  z-index: -1;
}

.bg-border-bottom-lt {
  width: 24px;
  height: 19px;
  // background: url("../../public/images/bgBorder/模型库DK_10.png");
  background: @DK_10;
}

.bg-border-bottom-md {
  flex: 1;
  height: 19px;
  // background: url("../../public/images/bgBorder/模型库DK_11.png");
  background: @DK_11;
}

.bg-border-bottom-rt {
  width: 200px;
  height: 19px;
  /* // background: url("../../public/images/bgBorder/模型库DK_12.png"); */
  background: @DK_12;
}

.custom-popup-mian {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 10px 20px;
  overflow: hidden;
  background-color: rgb(77, 109, 214);
  border-radius: 5px;
  box-shadow: 0px 0px 10px #b39a9a;

  .header1 {
    display: none;
  }

  .header {
    .header-title {
      display: flex;
      padding: 10px 0;
      align-items: center;

      & > span {
        margin-right: 10px;
        background-image: @popupTitleBg;
        background-size: 100% 100%;
        width: 19px;
        height: 13px;
      }
    }

    .headerLine {
      display: flex;
      width: 98%;

      .headerLine-left {
        width: 32px;
        height: 1px;
        // background: url("../../public/images/bgBorder/line_06.png");
        background: @line_06;
      }

      .headerLine-md {
        flex: 1;
        height: 1px;
        // background: url("../../public/images/bgBorder/line_07.png");
        background: @line_07;
      }

      .headerLine-right {
        width: 8px;
        height: 8px;
        transform: translateY(-7px);
        // background: url("../../public/images/bgBorder/line_03.png");
        background: @line_03;
      }
    }
  }

  .content {
  }
}
</style>
