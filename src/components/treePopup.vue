<!--
 * @Author: DRZ
 * @data: 2023-09-13
 * @@Description:: 
 * @LastEditors: 树盒子
 * @LastEditTime: 2023-09-11 13:45:01
 * @FilePath: \components\BgBorder.vue
-->
<template>
  <div class="custmoer" :style="{
      height: height,
      top: top,
      left: left,
      right: right,
      bottom: bottom,
    }">
    <div class="bg-border-top">
      <div class="bg-border-top-lt"></div>
    </div>
    <div class="bg-border-md" style="height: calc(100% - 38px)">
      <div class="bg-border-md-lt"></div>
      <div class="bg-border-md-md"></div>
      <div class="bg-border-md-rt"></div>
    </div>
    <div class="bg-border-bottom">
      <div class="bg-border-bottom-lt"></div>
      <div class="bg-border-bottom-md"></div>
      <div class="bg-border-bottom-rt"></div>
    </div>
    <div class="custom-popup-mian">
      <div class="header">
        <div class="header-title">
          <!-- <img
            style="margin-right: 10px"
            src="../../public/images/bgBorder/装饰一级bt_03.png"
            width="19"
            height="13"
          /> -->
          <span></span>
          <slot name="header">
            <h3 style="color: white">我是标题</h3>
          </slot>
        </div>
        <div class="headerLine">
          <div class="headerLine-left"></div>
          <div class="headerLine-md"></div>
          <div class="headerLine-right"></div>
        </div>
      </div>
      <div class="content">
        <slot name="content">
          <div style="color: white">我是内容主体</div>
        </slot>
      </div>
    </div>
  </div>
</template>
<script setup>

  // 组件接收的参数
  const props = defineProps({
    height: {
      type: String,
      default: "500px",
    },
    // width: {
    //   type: String,
    //   default: "280px",
    // },
    top: {
      type: String,
      default: "",
    },
    left: {
      type: String,
      default: "",
    },
    right: {
      type: String,
      default: "",
    },
    bottom: {
      type: String,
      default: "",
    },
  });
</script>
<style scoped lang="less">
  @import "../assets/style/theme/style.less";

  .custmoer {
    position: absolute;
    width: 280px;
    z-index: 1999;
  }

  .bg-border-top {
    display: flex;
  }

  .bg-border-top-lt {
    flex: 1;
    height: 11px;
    // background: url("../../public/images/bgBorder/tree-bg_01.png");
    background: @tree01;
  }

  .bg-border-md {
    display: flex;
  }

  .bg-border-md-lt {
    flex: 1;
    height: 100%;
    background-repeat: repeat-y;
    // background: url("../../public/images/bgBorder/tree-bg_02.png");
    background: @tree02;
  }

  .bg-border-bottom {
    display: flex;
  }

  .bg-border-bottom-lt {
    flex: 1;
    height: 20px;
    // background: url("../../public/images/bgBorder/tree-bg_03.png");
    background: @tree03;

  }

  .custom-popup-mian {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 10px 20px;
    overflow: hidden;
    border-radius: 5px;
    box-shadow: 0px 0px 10px #b39a9a;

    .header {
      .header-title {
        display: flex;
        padding: 10px 0;
        align-items: center;

        &>span {
          margin-right: 10px;
          background-image: @popupTitleBg;
          background-size: 100% 100%;
          width: 19px;
          height: 13px;
        }
      }

      .headerLine {
        display: flex;
        width: 100%;

        .headerLine-left {
          width: 32px;
          height: 1px;
          // background: url("../../public/images/bgBorder/line_06.png");
          background: @line_06;

        }

        .headerLine-md {
          flex: 1;
          height: 1px;
          // background: url("../../public/images/bgBorder/line_07.png");
          background: @line_07;

        }

        .headerLine-right {
          width: 8px;
          height: 8px;
          transform: translateY(-7px);
          // background: url("../../public/images/bgBorder/line_03.png");
          background: @line_03;

        }
      }
    }

    .content {}
  }
</style>