<!--
 * @Author: nameZ
 * @data: Do not edit
 * @Description: 
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-25 16:30:34
 * @FilePath: \qbmbtsweb\src\components\evmap\CesiumBox.vue
-->
<template>
  <div :id="cesiumId || 'evwebgis'" :class="boxSize ? 'evwebgis' : 'evwebgis1'"></div>
  <!-- 绘制工具 -->
  <!-- <drawTool v-if="isDrawTool" :class="isScreenClass ? 'drawToolLeft1' : 'drawToolLeft2'"></drawTool> -->
  <!-- 地理信息 -->
  <div :class="isScreenClass ? 'lonlatHeight' : 'lonlatHeight2'" v-if="longitude">
    <span class="position-item">经度：{{ handleNumber(longitude * 1, 6) }}°；</span>
    <span class="position-item">纬度：{{ handleNumber(latitude * 1, 6) }}°；</span>
    <span class="position-item">层级：{{ handleNumber(height * 1, 2) }}m</span>
    <!-- 比例尺面板 -->
    <div class="scale-container position-item">
      <div class="scale-label">{{ distanceLabel || "" }}</div>
      <div v-if="barWidth" class="scale-bar" :style="{ width: barWidth + 'px' }"></div>
    </div>
  </div>
</template>
<script setup>
import { toRefs, defineProps, defineEmits, onMounted, ref, nextTick, computed, watch } from "vue";
import { useRoute } from "vue-router";
// 在地图加载页面引入地图库
// import EVGIS from "../../evgismap/EVGISExtensions/EVGIS";
import "../../../public/js/evwebgis-debug";
// import '../../../public/js/evwebgis-debug'
import router from "@/router/index.js";
// import json_data from "/public/json/XJXZ_GEOJSON/county.json";
// import drawTool from "@/components/DrawTool.vue";

const route = useRoute();
// 父级传过来的数据
const props = defineProps({
  // 传入表单数据
  cesiumId: String,
  boxSize: Boolean,
});
const { cesiumId, boxSize } = toRefs(props);
let longitude = ref("");
let latitude = ref("");
let height = ref("");
const distanceLabel = ref(undefined);
const barWidth = ref(undefined);
let isScreenClass = ref(false);
let isDrawTool = ref(true);
//创建实体组名
const measureGroup = [
  "currentGroup", //实时目标组
  "currentLineGroup", //实时目标航迹线组
  "historyGroup", //回放目标组
  "historyLineGroup", //回放目标航迹线组
  "areaGroup", //区域组
];
/**
 * @description 初始化
 * @description 初始化地图，传入对应盒子id（若需23维同屏，则第一个和第二个参数都需传入，初始化一个地图仅需传入第一个参数，第二个为空），地图管理IP地址，需要初始化的模式
 * @param {*}  param
 * @param {String}  GISServer_ip  许可IP
 */
const initMap = (param) => {
  let _mapType = localStorage.getItem("mapType");
  if (!_mapType) {
    _mapType = "2d";
  }
  // const EVGISMAP = new EVGIS(cesiumId.value || "evwebgis", "", {
  //   GISServer_ip: param.GISServer_ip, // 处理许可IP
  //   mode: _mapType,
  //   sceneMode: _mapType == "2d" ? false : true,
  // });
  window.CesiumType = "2D"

  const EVGISMAP = new evgis_unused_var.default(cesiumId.value || "evwebgis", "", {
    GISServer_ip: param.GISServer_ip, // 处理许可IP
    mode: _mapType,
    sceneMode: _mapType == "2d" ? false : true,
    backgroundColor: "#333",
    // backgroundColor: "#e9e9e9",
    backgroundColor: "#022141c0",
  });
  window.EVGISMAP = EVGISMAP;
  window.Viewer = EVGISMAP("getViewer");

  window.EVGISMAP("flyTo", {
    center: [105.1, 36.0, 5999990],
  });
  window.EVGISMAP("getLatitudeAndLongitude", {
    callBack: (ponit) => {
      if (!ponit.lon && ponit.lon !== 0) {
        return;
      }
      longitude.value = ponit.lon;
      latitude.value = ponit.lat;
      height.value = ponit.currentZoom;
    },
  });
};

// 获取当前的鼠标位置经纬度
const getMousePosition = () => {
  var handler = new Cesium.ScreenSpaceEventHandler(Viewer.scene.canvas);
  // 鼠标移动事件
  handler.setInputAction(function (event) {
    var wp = event.endPosition;
    if (!Cesium.defined(wp)) {
      return;
    }
    var ray = Viewer.scene.camera.getPickRay(wp);
    if (!Cesium.defined(ray)) {
      return;
    }
    var cartesian = Viewer.scene.globe.pick(ray, Viewer.scene);
    if (!Cesium.defined(cartesian)) {
      return;
    }
    if (cartesian) {
      var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
      longitude.value = Cesium.Math.toDegrees(cartographic.longitude);
      latitude.value = Cesium.Math.toDegrees(cartographic.latitude);
      // height.value = Viewer.scene.globe.getHeight(cartographic)
      height.value = Math.floor(Viewer.camera.positionCartographic.height * 10) / 10; // 视角高度

      // console.log("jwd",longitude.value,latitude.value,height.value)
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  // 鼠标滚轴事件
  handler.setInputAction(function (event) {
    height.value = Math.floor(Viewer.camera.positionCartographic.height * 10) / 10; // 视角高度
  }, Cesium.ScreenSpaceEventType.WHEEL);
};

// 比例尺
const cesiumScale = () => {
  var geodesic = new Cesium.EllipsoidGeodesic();
  var distances = [
    1, 2, 3, 5, 10, 20, 30, 50, 100, 200, 300, 500, 1000, 2000, 3000, 5000, 10000, 20000, 30000, 50000, 100000, 200000,
    300000, 500000, 1000000, 2000000, 3000000, 5000000, 10000000, 20000000, 30000000, 50000000,
  ];
  // Find the distance between two pixels at the bottom center of the screen.
  let scene = Viewer.scene;
  let width = scene.canvas.clientWidth;
  let height = scene.canvas.clientHeight;

  let left = scene.camera.getPickRay(new Cesium.Cartesian2((width / 2) | 0, height - 1));
  let right = scene.camera.getPickRay(new Cesium.Cartesian2((1 + width / 2) | 0, height - 1));

  let globe = scene.globe;
  let leftPosition = globe.pick(left, scene);
  let rightPosition = globe.pick(right, scene);

  if (!Cesium.defined(leftPosition) || !Cesium.defined(rightPosition)) {
    barWidth.value = undefined;
    distanceLabel.value = undefined;
    return;
  }

  let leftCartographic = globe.ellipsoid.cartesianToCartographic(leftPosition);
  let rightCartographic = globe.ellipsoid.cartesianToCartographic(rightPosition);

  geodesic.setEndPoints(leftCartographic, rightCartographic);
  let pixelDistance = geodesic.surfaceDistance;

  // Find the first distance that makes the scale bar less than 100 pixels.
  let maxBarWidth = 100;
  let distance;
  for (let i = distances.length - 1; !Cesium.defined(distance) && i >= 0; --i) {
    if (distances[i] / pixelDistance < maxBarWidth) {
      distance = distances[i];
    }
  }

  if (Cesium.defined(distance)) {
    var label = distance >= 1000 ? (distance / 1000).toString() + " km" : distance.toString() + " m";
    barWidth.value = (distance / pixelDistance) | 0;
    distanceLabel.value = label;
  } else {
    barWidth.value = undefined;
    distanceLabel.value = undefined;
  }
};

// 数字，取小数点后几位
const handleNumber = computed(() => {
  return (value, num) => {
    return Number(value.toFixed(num));
  };
});

watch(
  () => route.path,
  (val) => {
    if (val == "/home/<USER>" || val == "/largescreen") {
      isDrawTool.value = true;
    } else {
      isDrawTool.value = false;
    }
  }
);

/**
 * @description 页面加载完成
 */
onMounted(() => {
  nextTick(() => {
    initMap({ GISServer_ip: MAPServerIp });
    ImagerLayers.forEach((layer) => {
      // 加载对应图层;
      EVGISMAP("addEVImageLayer", {
        serverIP: layer.url,
        layerName: layer.name, //图层名
        layerValue: layer.value, //服务名
        layerType: layer.tileType, //投影方式WebMercator，WGS84
        imageType: "Image", //影像类型Image、Vector等
        dataSource: 0, //数据源，0为国遥分层规则数据，1为其他规则数据
        dataSourceType: "tileserver", //
        serverSource: 0, //服务源，0为国遥服务，1为其他地图服务（若为其他地图服务时候需要传入tileRowSize，tileColSize，url，tileStartZ）
        index: 0, //数据层级
        tileRowSize: 1, //开始层级一列有多少个瓦片
        tileColSize: 2, //开始层级一行有多少个瓦片
        tileStartZ: 0, //开始层级
        tileStartPosion: "LeftTop", //瓦片起始位置：LeftTop，LeftBottom,RightTop,RightBottom
        // url:
        //   GISServer_ip +
        //   "/earthview/services/2000/RasterService/WMTS?tilematrix={tilematrix}&layer=2000&style=default&tilerow={tilerow}&tilecol={tilecol}&tilematrixset=OGC_WGS84&format=image%2Fpng&service=WMTS&version=1.0.0&request=GetTile",
      });
    });
    let routerName = router.currentRoute._value.name;
    isScreenClass.value = true;

    // if (routerName == "largescreen") {
    //   isScreenClass.value = true;
    // } else if (routerName == "homePage" || (routerName == "mapTheme" && window.Viewer)) {
    //   isScreenClass.value = false;
    // }
    if (routerName == "largescreen" || routerName == "homePage") {
      isDrawTool.value = true;
    } else {
      isDrawTool.value = false;
    }
    // getMousePosition();
    // 场景变化监听事件
    // viewer.scene.postRender.addEventListener(function () {
    //   cesiumScale();
    // });
    // window.viewer.camera.flyTo({
    //   destination: Cesium.Cartesian3.fromDegrees(86.1, 37.0, 5000000),
    //   duration: 1, // 动画执行时间
    // });
    //创建组
    EVGISMAP("createEntityGroups", { groups: measureGroup });
    window.EVGISMAP("registerClickEvent", (en) => {
      // console.log("点击实体", en);
    });
  });
});
</script>
<style lang="less" scoped>
.evwebgis {
  width: 100%;
  height: 100%;
  /* transform: translateY(-10px); */
}

.evwebgis1 {
  width: 100%;
  height: 100%;

  /* transform: translateY(-10px); */
}

.lonlatHeight {
  position: absolute;
  bottom: 8px;
  right: 10px;
  color: rgb(4, 235, 252);
  width: 100%;
  text-align: center;
  // background-color: rgba(29, 60, 107, 0.3);
  padding: 3px 6px;

  .position-item {
    margin-right: 10px;
  }

  .scale-label {
    font-size: 12px;
    color: rgb(4, 235, 252);
    text-align: center;
  }

  .scale-bar::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 10px;
    border: 1px solid rgb(4, 235, 252);
    border-top: none;
    left: 0;
    bottom: 0;
  }
}

.lonlatHeight2 {
  position: absolute;
  bottom: -28px;
  right: 12px;
  color: rgba(4, 235, 252, 0.5);
  width: 100%;
  text-align: center;
  font-size: 12px;
  // background-color: rgba(29, 60, 107, 0.3);
  padding: 3px 6px;

  .position-item {
    margin-right: 10px;
  }

  .scale-label {
    font-size: 12px;
    color: rgba(4, 235, 252, 0.5);
    text-align: center;
  }

  .scale-bar::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 10px;
    border: 1px solid rgba(4, 235, 252, 0.5);
    border-top: none;
    left: 0;
    bottom: 0;
  }
}

/**  比例尺 start  */
.scale-container {
  display: inline-block;
  margin-left: 10px;
  position: relative;
  top: -3px;
}

.scale-bar {
  position: relative;
  padding-top: 2px;
}

.drawToolLeft2 {
  right: 486px;
}
</style>
