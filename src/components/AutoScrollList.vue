<script setup>
	const props = defineProps({
		autoScroll: {
			type: <PERSON>olean,
			default: true
		},
		listData: {
			type: Array,
			default: () => []
		}
	})
	const onMouseenter = () => {

	}
</script>

<template>
	<div class="auto-scroll-list" @mouseenter="onMouseenter">
		<template
			v-for="(item,i) in props.listData"
			:key="i"
		>
			<slot :data="item"></slot>
		</template>
	</div>
</template>

<style scoped lang="less">
.auto-scroll-list{
	height: 100%;
}
</style>
