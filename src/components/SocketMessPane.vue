<template>

    <customPopup width="90%" height="80%" left="100px" top="100px">
        <template #header>
            <!-- <span class="EMail_icon"></span> -->
            <span class="title-font">生成态势时间测试</span>
            <div style="position: absolute; right: 2%">
                <!-- <el-button plain @click="state.showEmail = false">关闭</el-button> -->
            </div>
        </template>
        <template #content>
            <div style="height: 97%;width: 100%;">
                <div style="height: 40px;padding: 10px;font-weight: bold;font-size: 15px;">
                    <!-- 平均时间: {{ aveTime.toFixed(2) }} ms -->
                    <el-button @click="getMaxTime"
                        style="position: absolute;right: 230px;transform:translateY(-10px)">计算最长时间</el-button>
                    <div style="position: absolute;left: 10px;transform:translateY(-5px)"> 当前接收条数: {{
                        tableData.length }} 条 </div>
                    <div v-show="maxTime" style="position: absolute;right: 110px;transform:translateY(-5px)"> 最大时间: {{
                        maxTime }} s </div>
                    <el-popover :visible="visible" placement="right" :width="230">
                        <template #reference>
                            <el-button @click="visible = true"
                                style="position: absolute;right: 350px;transform:translateY(-10px)">接收消息</el-button>
                        </template>
                        <div style="display: flex;align-items: center;">
                            消息条数：<el-input v-model="receiveNum" style="width: 70px;"></el-input>
                            <el-icon @click="visible = false" :size="20" color="#f00"
                                style="margin-left: 10px; cursor: pointer">
                                <Close />
                            </el-icon>
                            <el-icon @click="() => { isReceive = true; visible = false }"
                                style="margin-left: 5px; cursor: pointer" color="#f00" :size="20">
                                <Check />
                            </el-icon>
                        </div>

                    </el-popover>
                </div>
                <el-table ref="dispatchTableRef" :data="tableData" class="custom-table" style="width: 100%"
                    height="100%" @selection-change="handleSelectionChange">
                    <!-- <el-table-column type="selection" width="55" /> -->
                    <el-table-column label="序号" width="50" type="index" align="center"></el-table-column>
                    <el-table-column label="消息来源" prop="source" align="center">
                    </el-table-column>
                    <el-table-column label="消息类型" prop="type" align="center">
                    </el-table-column>
                    <el-table-column label="消息接收时间" prop="receiveTime" align="center" />
                    <el-table-column label="态势呈现时间" prop="showTime" align="center" />
                    <el-table-column label="生成态势时间（s）" prop="duration" align="center" />

                </el-table>
            </div>
        </template>

    </customPopup>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { onMounted, onUnmounted, ref, reactive, watch } from 'vue'
import { useSockeMessStore } from '@/stores/index'
import customPopup from '@/components/customPopup.vue'
const sockeMessStore = useSockeMessStore()
let visible = ref(false)
let isReceive = ref(false)
let tableData = ref([])
let receiveNum = ref(100)
let aveTime = ref(0)
watch(() => sockeMessStore.tstimeSocket, (v) => {

    if (isReceive.value && tableData.value.length < receiveNum.value) {
        tableData.value.unshift(...sockeMessStore.tstimeSocket)
        console.log('tableData.value==', tableData.value)
        aveTime.value = tableData.value.reduce((pre, it) => pre + +it.duration, 0) / tableData.value.length
        if (tableData.value.length == receiveNum.value) {
            isReceive.value = false
        }
    }
}, {
    immediate: true
})
watch(() => isReceive, (v) => {
    console.log('vvvv', v);
    if (v.value == true) {
        tableData.value = []
        maxTime.value = null;
    }
}, {
    deep: true,
    immediate: true
})
let maxTime = ref(null)
const getMaxTime = () => {
    let arr = tableData.value.map(v => { return v.duration })
    maxTime.value = Math.max(...arr)
    return maxTime.value
}
const handleSelectionChange = (d) => { }
onMounted(() => {

})

</script>
<style lang="less" scoped>
* {
    color: wheat;
    box-sizing: border-box;
}

.userDispatchDesk {
    position: fixed;
    top: 100px;
    left: 150px;
    width: 80vw;
    height: 80vh;
    background: url("/public/images/bgBorder1/模型库-bg.jpg") 0 0/100% 100%;
    z-index: 30;
}

.dispatch_header {
    text-align: left;
    padding-left: 150px;
    font-size: 25px;
    font-weight: bold;
    height: 70px;
    line-height: 50px;
    background: url("/public/images/header1/模型库-top-bg.png") 0 0 / 100% 100%;
}

.dispatch_body {
    height: calc(100vh - 70px - 70px);
}

.dispatch_footer {
    height: 70px;
    text-align: center;
}

.custom-table-box {
    width: 100%;
    height: 80%;
    border: 1px solid #59c5cd !important;
    border-radius: 10px;
    padding: 20px;
}

.dispatch_button {
    background: #31be9b30 !important;

    &:hover {
        background: #31be9b !important;
    }
}
</style>