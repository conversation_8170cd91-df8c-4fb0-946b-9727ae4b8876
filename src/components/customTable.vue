<template>
  <div class="customTable">
    <slot name="search"></slot>
    <!-- :row-key="id" -->
    <slot name="table">
      <el-table ref="customTableRef" class="custom-table" :row-key="getRowKeys" show-overflow-tooltip
        @selection-change="handleSelectionChange" @select="selectRow" :row-class-name="tableRowClass"
        @row-click="nodeClick" highlight-current-row :data="tableData" default-expand-all :cell-style="cellStyle" lazy>
        <el-table-column v-if="selectionShow == 'true'" type="selection" width="50" :reserve-selection="true"
          align="center" />
        <el-table-column v-if="orderShow == 'true'" type="index" label="序号" width="80"></el-table-column>
        <!-- 循环列 -->
        <template v-for="item in tableColumn" :key="item">
          <!-- 多选 -->
          <el-table-column v-if="item.type == 'selection'" type="selection"
            :width="item.minWidth ? 'inherit' : item.width" :min-width="item.minWidth" />
          <!-- 字符 -->

          <el-table-column align="center" v-else-if="item.type == 'String' && item.prop == 'fileId'" prop="fileName"
            label="文件名称" :width="item.minWidth ? 'inherit' : item.width" :min-width="item.minWidth" />
          <el-table-column align="center" v-else-if="item.type == 'String'" :prop="item.prop" :label="item.label"
            :width="item.minWidth ? 'inherit' : item.width" :min-width="item.minWidth" />

          <!-- 时间字符 -->
          <el-table-column align="center" v-else-if="item.type == 'Time'" :prop="item.prop" :label="item.label"
            :width="item.minWidth ? 'inherit' : item.width" :min-width="item.minWidth">
            <template v-slot="scope">
              <span>{{ timeSpan(scope.row[item.prop]) }}</span>
            </template>
          </el-table-column>
          <!-- 数字 -->
          <el-table-column align="center" v-else-if="item.type == 'Numder'" :prop="item.prop" :label="item.label"
            :width="item.minWidth ? 'inherit' : item.width" :min-width="item.minWidth" />
          <!-- 布尔 -->
          <el-table-column v-else-if="item.type == 'Boolean'" align="center" :prop="item.prop" :label="item.label"
            :width="item.minWidth ? 'inherit' : item.width" :min-width="item.minWidth">
            <template v-slot="scope">
              <span>{{ item.booleanTypeName[scope.row[item.prop]] }}</span>
            </template>
          </el-table-column>
          <!-- 字典 -->
          <el-table-column v-else-if="item.type == 'Sysdic'" align="center" :prop="item.prop" :label="item.label"
            :width="item.minWidth ? 'inherit' : item.width" :min-width="item.minWidth">
            <template v-slot="scope">
              <span v-if="item.prop == 'businessType' || item.prop == 'workSystem' || item.prop == 'frequency'">
                {{ dictValue(item.prop, scope.row[item.prop]) }}
              </span>
              <span v-else-if="state.dictTypeList[item.dicName || item.prop]">
                <!-- 如果字段和字典名一致，则可以省略 -->
                {{ state.dictTypeList[item.dicName || item.prop][scope.row[item.prop]] || scope.row[item.prop] }}
              </span>
              <!-- 不是字典可以自己传入列表 -->
              <span v-else>{{ item.SysdicName ? item.SysdicName[scope.row[item.prop]] : scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
          <!-- 数组 -->
          <el-table-column v-else-if="item.type == 'Array'" align="center" :prop="item.prop" :label="item.label"
            :width="item.minWidth ? 'inherit' : item.width" :min-width="item.minWidth">
            <template v-slot="scope">
              <span v-for="it in scope.row[item.prop]" :key="it" class="ArrayClass">{{ it }}</span>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column align="center" v-else-if="item.type == 'operation'" :prop="item.prop" :label="item.label"
            :width="item.minWidth ? 'inherit' : item.width" :min-width="item.minWidth">
            <template v-slot="scope">
              <div v-if="
                item.operationTypeName &&
                item.operationTypeName.customize &&
                item.operationTypeName.customize.name == '处理'
              ">
                <el-tooltip v-if="scope.row.warningStatus == '0'" popper-class="login-tooltip" class="item"
                  effect="light" content="编辑" placement="bottom">
                  <!-- <img :src="imagesIcon.editIcon" style="width: 22px; height: 22px; cursor: pointer" @click="handleEdit(scope.row)" type="info" /> -->
                  <span :class="imagesIcon.editIcon"
                    style="display: inline-block; width: 22px; height: 22px; cursor: pointer"
                    @click.stop="handleEdit(scope.row)"></span>
                </el-tooltip>
                <span v-else-if="scope.row.warningStatus == '1'">已处理</span>
              </div>
              <div v-else>
                <el-tooltip v-if="item.operationTypeName.position" popper-class="login-tooltip" class="item"
                  effect="light" content="位置" placement="bottom">
                  <span :class="imagesIcon.positionIcon"
                    style="display: inline-block; width: 22px; height: 22px; cursor: pointer; margin-left: 10px"
                    @click.stop="handPosition(scope.row)"></span>
                </el-tooltip>
                <el-tooltip v-if="item.operationTypeName.demand" popper-class="login-tooltip" class="item"
                  effect="light" content="需求分解" placement="bottom">
                  <span :class="imagesIcon.requirementResolve"
                    style="display: inline-block; width: 22px; height: 22px; cursor: pointer; margin-left: 10px"
                    @click.stop="handDemand(scope.row)"></span>
                </el-tooltip>
                <el-tooltip v-if="item.operationTypeName.handleDetail" popper-class="login-tooltip" class="item"
                  effect="light" content="详情查看" placement="bottom">
                  <!-- <img :src="imagesIcon.detailIcon" style="width: 22px; height: 22px; cursor: pointer" @click="handleDetail(scope.row)" type="info" /> -->
                  <span :class="imagesIcon.detailIcon"
                    style="display: inline-block; width: 22px; height: 22px; cursor: pointer; margin-left: 10px"
                    @click.stop="handleDetail(scope.row)"></span>
                </el-tooltip>
                <el-tooltip v-if="item.operationTypeName.handleEdit" popper-class="login-tooltip" class="item"
                  effect="light" content="编辑" placement="bottom">
                  <!-- <img :src="imagesIcon.editIcon" style="width: 22px; height: 22px; cursor: pointer; margin-left: 10px" @click="handleEdit(scope.row)" type="info" /> -->
                  <span :class="imagesIcon.editIcon"
                    style="display: inline-block; width: 22px; height: 22px; cursor: pointer; margin-left: 10px"
                    @click.stop="handleEdit(scope.row)"></span>
                </el-tooltip>
                <el-tooltip v-if="item.operationTypeName.handleSub" popper-class="login-tooltip" class="item"
                  effect="light" content="实时-指定" placement="bottom">
                  <!-- <img :src="imagesIcon.editIcon" style="width: 22px; height: 22px; cursor: pointer; margin-left: 10px" @click="handleEdit(scope.row)" type="info" /> -->
                  <span :class="imagesIcon.zhidin"
                    style="display: inline-block; width: 22px; height: 22px; cursor: pointer; margin-left: 10px"
                    @click.stop="handleSubEmit(scope.row)"></span>
                </el-tooltip>
                <el-tooltip v-if="item.operationTypeName.trackEdit" popper-class="login-tooltip" class="item"
                  effect="light" content="轨迹配置" placement="bottom">
                  <!-- <img :src="imagesIcon.editIcon" style="width: 22px; height: 22px; cursor: pointer; margin-left: 10px" @click="handleEdit(scope.row)" type="info" /> -->
                  <span :class="imagesIcon.trackIcon"
                    style="display: inline-block; width: 22px; height: 22px; cursor: pointer; margin-left: 10px"></span>
                </el-tooltip>
                <el-popconfirm v-if="item.operationTypeName.handleDelete" confirm-button-text="确认"
                  cancel-button-text="取消" width="220px" :icon="InfoFilled" icon-color="red" title="是否确定删除当前数据?"
                  @confirm="handleDelete(scope.row)">
                  <template #reference>
                    <span @click.stop>
                      <el-tooltip popper-class="login-tooltip" class="item" effect="light" content="删除"
                        placement="bottom">
                        <!-- <img :src="imagesIcon.deleteIcon" style="width: 25px; height: 25px; margin-left: 10px; cursor: pointer; transform: translateY(2px)" type="info" /> -->
                        <span :class="imagesIcon.deleteIcon" style="
                            display: inline-block;
                            width: 25px;
                            height: 25px;
                            margin-left: 10px;
                            cursor: pointer;
                            transform: translateY(2px);
                          "></span>
                      </el-tooltip>
                    </span>
                  </template>
                </el-popconfirm>
              </div>
            </template>
          </el-table-column>
          <!-- 布尔 -->
          <el-table-column v-else :prop="item.prop" :label="item.label" :width="item.minWidth ? 'inherit' : item.width"
            :min-width="item.minWidth" />
        </template>
      </el-table>
    </slot>
    <slot name="pagination">
      <div v-if="paginationShow == 'true'" class="elPager" style="margin-top: 5px">
        <el-pagination v-model:currentPage="currentPage" v-model:page-size="pageSize" :page-sizes="pageSizes"
          layout="total, sizes, prev, pager, next, jumper" :total="total" :small="small" :disabled="disabled"
          :background="background" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </slot>
  </div>
</template>
<script setup>
import { toRefs, defineProps, defineEmits, computed, ref, reactive, onMounted, defineExpose } from "vue";
import { InfoFilled } from "@element-plus/icons-vue";
const dictValue = window.dictValue;
const timeSpan = window.timeSpan;
const props = defineProps({
  tableData: Array,
  tableColumn: Array,
  total: {
    type: Number,
    default: 100,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSizes: {
    type: Array,
    default: [10, 20, 30, 40],
  },
  pageSize: {
    type: Number,
    default: 10,
  },
  defaultCurrentRow: {
    type: Number,
    default: null,
  },
  imagesIcon: {
    type: Object,
    default: {
      // deleteIcon: "../../public/images/icon/sc.png",
      // editIcon: "../../public/images/icon/bianji.png",
      // detailIcon: "../../public/images/icon/ck.png",
      deleteIcon: "custom_header_sc",
      editIcon: "custom_header_bianji",
      zhidin: "custom_header_zhiding",
      detailIcon: "custom_header_ck",
      positionIcon: "custom_header_wz",
      trackIcon: "custom_header_track",
      requirementResolve: "custom_requirement_resolve",
    },
  },
  //是否显示序号
  orderShow: {
    type: String,
    default: "false",
  },
  selectionShow: {
    type: String,
    default: "false",
  },
  // 是否显示分页
  paginationShow: {
    type: String,
    default: "true",
  },
});

const { tableData, tableColumn, total, currentPage, pageSizes, pageSize, defaultCurrentRow, imagesIcon } =
  toRefs(props);
// 全部申明传参事件
let emits = defineEmits([
  "handleEditEmit",
  "handleSubEmit",
  "handleDemandEmit",
  "handlePositionEmit",
  "handleDetailEmit",
  "handleDeleteEmit",
  "currentPageEmit",
  "pageSizeEmit",
  "handleSelectionChangeEmit",
  "selectRow",
  "nodeClickEmit",
  "handletableRowClass",
]);
// const currentPage = ref(1);
// const pageSizes = ref([10, 20, 30, 40]);
// const pageSize = ref(pageSizes[0]);
const small = ref(false);
const background = ref(false);
const disabled = ref(false);
const state = reactive({
  dictTypeList: {},
});
const customTableRef = ref(null);

//设置默认高亮行
const setCurrentRow = (row) => {
  if (row) {
    customTableRef.value.setCurrentRow(row);
    return;
  }
  if (!isNaN(props.defaultCurrentRow)) {
    customTableRef.value.setCurrentRow(props.tableData[props.defaultCurrentRow]);
  }
};
// 每页数量变更
const handleSizeChange = (val) => {
  // console.log(`${val} items per page`);
  emits("pageSizeEmit", val);
  setCurrentRow();
};
// 页码变更
const handleCurrentChange = (val) => {
  // console.log(`current page: ${val}`);
  emits("currentPageEmit", val);
  setCurrentRow();
};
// 编辑
const handleEdit = (row) => {
  emits("handleEditEmit", row);
};
//设为实时推演
const handleSubEmit = (row) => {
  emits("handleSubEmit", row);
};
// 详情
const handleDetail = (row) => {
  emits("handleDetailEmit", row);
};
// 需求分解
const handDemand = (row) => {
  emits("handleDemandEmit", row);
};
// 位置
const handPosition = (row) => {
  emits("handlePositionEmit", row);
};
// 修改密码
const editPassword = (row) => {
  emits("editPassword", row);
};
// 多选
const handleSelectionChange = (val, index) => {
  emits("handleSelectionChangeEmit", val, customTableRef.value);
};
//checkbox切换时调用
const selectRow = (select, row) => {
  emits("selectRow", select, row);
}
//单击行
const nodeClick = (row) => {
  emits("nodeClickEmit", row);
};
// 删除数据确认
const confirmEvent = (row) => {
  emits("handleDeleteEmit", row);
};
// 删除
const handleDelete = (row) => {
  emits("handleDeleteEmit", row);
};
const getRowKeys = (row) => {
  return row.id;
};
const tableRowClass = (rowIndex) => {
  emits("handletableRowClass", rowIndex);
};
const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
  if (row.ZYCD && columnIndex == 2) {
    if (row.ZYCD == "重要") {
      return { color: "red" };
    }
  }
  if (row.DDZT && columnIndex == 4) {
    if (row.DDZT == "已分配") {
      return { color: "#0ae1ec" };
    }
  }
  if (row.TJZT && columnIndex == 5) {
    if (row.TJZT == "已提交") {
      return { color: "#0ae1ec" };
    }
  } else {
    return { color: "#285472" };
  }
};

defineExpose({ setCurrentRow });

onMounted(() => {
  state.dictTypeList = window.dictTypeList;
  tableRowClass();
  setTimeout(() => {
    setCurrentRow();
  }, 200);
});
</script>
<style lang="less" scoped>
@import "../assets/style/theme/style.less";

.customTable {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.custom-table {
  // flex: 1;
  width: 100%;
  height: calc(100% - 40px);
  // height: 680px;
}

.ArrayClass {
  margin: 5px;
  padding: 0px 5px;
  display: inline-block;
  border: 1px solid #1054a4;
}

.custom_header_sc {
  width: 24px;
  height: 24px;
  background-image: @custom_header_delete;
  background-size: cover;
}

.custom_header_bianji {
  background-image: @custom_header_Edit;
  background-size: cover;
}

.custom_header_ck {
  // background-image: @custom_header_look;
  // background-image: url("../../public/images/icon/xq.png");
  background-image: url("/images/icon/ck.png");
  background-size: cover;
}

.custom_header_wz {
  background-image: url("/images/icon/白_39.png");
  background-size: cover;
}

.custom_header_track {
  background-image: url("/images/icon/航迹绘制1.png");
  background-size: cover;
}

.custom_requirement_resolve {
  background-image: url("/images/icon/需求分解.png");
  background-size: cover;
}

.custom_header_zhiding {
  background-image: url("/images/header/资源.png");
  background-size: cover;
}

:deep(.el-table .el-checkbox:last-of-type) {
  margin-left: 10px;
}
</style>
