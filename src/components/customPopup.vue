<!--
 * @Author: DRZ
 * @data: 2023-09-13
 * @@Description:: 
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-19 13:28:43
 * @FilePath: \qbmbtsweb\src\components\customPopup.vue
-->
<template>
  <div
    :class="backgroundShow == 'false' ? 'custmoers' : 'custmoer'"
    :style="{
      height: height,
      width: width,
      top: top,
      left: left,
      right: right,
      bottom: bottom,
      zIndex: zIndex,
      position: relative ? 'relative' : '',
    }"
    :v-show="vShow"
  >
    <!-- <div class="bg-border-top">
      <div class="bg-border-top-lt"></div>
      <div class="bg-border-top-md"></div>
      <div class="bg-border-top-rt"></div>
      <div class="bg-border-top-rt1"></div>
    </div>
    <div class="bg-border-md" style="height: calc(100% - 38px)">
      <div class="bg-border-md-lt"></div>
      <div class="bg-border-md-md"></div>
      <div class="bg-border-md-rt"></div>
    </div>
    <div class="bg-border-bottom">
      <div class="bg-border-bottom-lt"></div>
      <div class="bg-border-bottom-md"></div>
      <div class="bg-border-bottom-rt"></div>
      <div class="bg-border-bottom-rt1"></div>
    </div> -->
    <div
      class="custom-popup-mian"
      :class="hover ? 'custom-popup-mian-hover' : ''"
      :style="{
        borderColor: hover ? 'transparent' : '#2c659fc0',
        cursor: hover ? 'pointer' : '',
        backgroundColor: bgColor,
      }"
    >
      <div class="header">
        <div class="header-title" v-if="headType == '1'">
          <span></span>
          <slot name="header">
            <span class="header-title-font">我是标题</span>
          </slot>
          <!-- <span class="header-line"></span> -->
        </div>
        <div class="header-title-2" v-else-if="headType == '2'">
          <slot name="header">
            <span class="header-title-font">我是标题</span>
          </slot>
          <span class="header-line"></span>
        </div>
        <!-- <img
          :class="closeShow == 'true' ? '' : 'close'"
          @click="close"
          style="position: absolute; top: 15px; right: 15px; cursor: pointer"
          src="../../public/images/icon/close.png"
          width="22"
          height="22"
        /> -->

        <span class="header-close" :class="closeShow == 'true' ? '' : 'close'" @click="close"></span>
        <!-- <div class="headerLine">
          <div class="headerLine-left"></div>
          <div class="headerLine-md"></div>
          <div class="headerLine-right"></div>
        </div> -->
      </div>
      <div :class="footerShow == 'false' ? 'content1' : 'content'">
        <slot name="content">
          <div style="color: white">我是内容主体</div>
        </slot>
      </div>
      <div class="footer" v-if="footerShow == 'true'">
        <slot name="footer">
          <div class="anniu">
            <el-button class="butt save" @click="save">确认</el-button>
            <el-button class="butt" @click="close">取消</el-button>
          </div>
        </slot>
      </div>
    </div>
  </div>
  <!-- 遮罩层 -->

  <div :v-show="vShow" v-if="backgroundShow != 'false'" :class="backgroundShow == 'false' ? '' : 'custom-popup'"></div>
</template>
<script setup>
// 组件接收的参数
const props = defineProps({
  height: {
    type: String,
    default: "500px",
  },
  width: {
    type: String,
    default: "600px",
  },
  top: {
    type: String,
    default: "",
  },
  left: {
    type: String,
    default: "",
  },
  right: {
    type: String,
    default: "",
  },
  bottom: {
    type: String,
    default: "",
  },
  zIndex: {
    type: Number,
    default: 0,
  },
  //是否需要关闭按钮
  closeShow: {
    type: String,
    default: "false",
  },
  //是否需要遮罩层
  backgroundShow: {
    type: String,
    default: "false",
  },
  //是否需底部按钮
  footerShow: {
    type: String,
    default: "false",
  },
  // 定制，hover的时候变大一点并且显示边框
  hover: {
    type: Boolean,
    default: false,
  },
  bgColor: {
    type: String,
    default: "",
  },
  headType: {
    type: String,
    default: "1",
  },
  relative: {
    type: Boolean,
    default: false,
  },
  vShow: {
    type: Boolean,
    default: true,
  },
});
const emits = defineEmits(["close", "save"]);

// 弹窗关闭的按钮触发的事件
const close = () => {
  emits("close");
};
// 保存的事件
const save = () => {
  emits("save");
};
</script>
<style scoped lang="less">
@import "../assets/style/theme/style.less";

.custom-popup {
  z-index: 1999;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: rgba(255, 255, 255);
}

.custmoer {
  position: absolute;
  z-index: 2000;
  background-color: rgb(77, 109, 214);
}

.custmoers {
  position: absolute;
  z-index: 1988;
}

.bg-border-top {
  display: flex;
}

.bg-border-top-lt {
  width: 16px;
  height: 16px;
  // background: url("../../public/images/bgBorder/forms-small_03.png");
  background: @small_03;
}

.bg-border-top-md {
  width: 299px;
  height: 16px;
  // background: url("../../public/images/bgBorder/forms-small_04.png");
  background: @small_04;
}

.bg-border-top-rt {
  flex: 1;
  height: 16px;
  // background: url("../../public/images/bgBorder/forms-small_05.png");
  background: @small_05;
}

.bg-border-top-rt1 {
  width: 16px;
  height: 16px;
  // background: url("../../public/images/bgBorder/forms-small_06.png");
  background: @small_06;
}

.bg-border-md {
  display: flex;
}

.bg-border-md-lt {
  width: 16px;
  height: 100%;
  background-repeat: repeat-y;
  // background: url("../../public/images/bgBorder/forms-small_08.png");
  background: @small_08;
}

.bg-border-md-md {
  flex: 1;
  height: 100%;
  background-repeat: repeat-y;
  // background: url("../../public/images/bgBorder/forms-small_09.png");
  background: @small_09;
}

.bg-border-md-rt {
  width: 16px;
  height: 100%;
  background-repeat: repeat-y;
  // background: url("../../public/images/bgBorder/forms-small_10.png");
  background: @small_10;
}

.bg-border-bottom {
  display: flex;
  position: relative;
}

.bg-border-bottom-lt {
  width: 16px;
  height: 16px;
  // background: url("../../public/images/bgBorder/forms-small_11.png");
  background: @small_11;
}

.bg-border-bottom-md {
  flex: 1;
  height: 16px;
  // background: url("../../public/images/bgBorder/forms-small_12.png");
  background: @small_12;
}

.bg-border-bottom-rt {
  width: 176px;
  height: 16px;
  // background: url("../../public/images/bgBorder/forms-small_13.png");
  background: @small_13;
}

.bg-border-bottom-rt1 {
  width: 16px;
  height: 16px;
  // background: url("../../public/images/bgBorder/forms-small_14.png");
  background: @small_14;
}

.close {
  display: none;
}

.custom-popup-mian {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 5px 0px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  // background-color: #fff;
  background-color: #022141c0;
  border-radius: 4px;
  border: #2c659fc0 2px solid;

  .custom-popup-mian-hover {
    &:hover {
      border-color: #2c659fc0 !important;
      // top: -0.5%;
      // left: -0.5%;
      // height: 101%;
      // width: 101%;
    }
  }
  // box-shadow: 0px 0px 10px #b39a9a;

  .header {
    .header-title {
      display: flex;
      padding: 4px 4px;
      align-items: center;
      border-bottom: 1px solid #2c669f40;
      position: relative;
      // & > span {
      //   /* margin-right: 10px;
      //     background-image: @popupTitleBg;
      //     background-size: 100% 100%;
      //     width: 19px;
      //     height: 13px; */
      //   width: 5px;
      //   height: 30px;
      //   width: 32px;
      //   height: 32px;
      //   border-radius: 10px;
      //   // background-color: #409eff;
      //   background: url("/public/images/new/icon/列表.png") no-repeat 0 0/100% 100%;
      //   margin-right: 10px;
      // }
      // .header-line {
      //   height: 2px;
      //   background: #2c659f44;
      //   flex-grow: 1;
      //   margin-left: 15px;
      // }
    }
    .header-title-2 {
      display: flex;
      padding: 4px 4px;
      align-items: center;
      border-bottom: 0px solid #2c669f40;
      position: reactive;

      & > span {
        width: 5px;
        height: 30px;
        width: 32px;
        height: 32px;
        border-radius: 10px;
        // background-color: #409eff;
        background: url("/public/images/new/icon/列表.png") no-repeat 0 0/100% 100%;
        margin-right: 10px;
      }
      .header-line {
        height: 2px;
        background: #2c659f44;
        flex-grow: 1;
        margin-left: 15px;
      }
    }

    .headerLine {
      display: flex;
      width: 100%;

      .headerLine-left {
        width: 32px;
        height: 1px;
        // background: url("../../public/images/bgBorder/line_06.png");
        background: @line_06;
      }

      .headerLine-md {
        flex: 1;
        height: 1px;
        // background: url("../../public/images/bgBorder/line_07.png");
        background: @line_07;
      }

      .headerLine-right {
        width: 8px;
        height: 8px;
        transform: translateY(-7px);
        // background: url("../../public/images/bgBorder/line_03.png");
        background: @line_03;
      }
    }
  }

  .content {
    height: calc(100% - 120px);
    overflow-y: auto;
    padding: 0 10px;
    box-sizing: border-box;
  }

  .content1 {
    height: calc(100% - 60px);
    // overflow-y: auto;
    padding: 8px 15px;
    box-sizing: border-box;
    flex: 1;
  }

  .footer {
    height: 60px;
    justify-content: center;
    display: flex;
    align-items: center;
  }
}

.header-close {
  width: 22px;
  height: 22px;
  position: absolute;
  top: 15px;
  right: 15px;
  cursor: pointer;
  background-image: @custom_header_close;
  background-size: cover;
}

.header-title-font {
  font-size: 20px;
  font-weight: bolder;
  color: rgba(@themeColor, 1);
}
</style>
