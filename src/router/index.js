/*
 * @Author: PXL
 * @Date: 2024-03-20 09:23:30
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-25 17:09:09
 * @Description: file content
 */
import { createRouter, createWebHashHistory } from "vue-router";

import Login from "@/views/login/Login.vue";
import index from "@/views/index.vue"; // 全局
import { ElMessage, ElLoading } from "element-plus";
import * as mapTool from "@/utils/mapTool.js";

const routes = [
  { path: "/",redirect:'home/earthPage' },
  { path: "/login", component: Login },

  {
    path: "/home",
    name: "Home",
    redirect: "/home/<USER>",
    component: index,
    children: [
      { path: "/home/<USER>", component: () => import("@/views/home/<USER>") },
      {
        path: "/home/<USER>",
        name: "rwtj",
        component: () => import("@/views/home/<USER>/index.vue"),
        meta: {
          title: "任务统计",
        },
      },
      {
        path: "/home/<USER>",
        name: "taskPage",
        component: () => import("@/views/home/<USER>"),
        meta: {
          title: "任务管理",
        },
      },
      {
        path: "/home/<USER>",
        name: "xqcj",
        component: () => import("@/views/home/<USER>"),
        meta: {
          title: "需求创建",
        },
      },
      {
        path: "/home/<USER>",
        name: "rwzs",
        component: () => import("@/views/tsjk/rwzs.vue"),
        meta: {
          title: "态势推演",
        },
      },
      // {
      //   path: "/home/<USER>",
      //   name: "panorama",
      //   // component: () => import("@/views/tsjk/tsty.vue"),//2024/7/1
      //   // component: import("@/views/tsjk/tsty2.vue"),//2024/8/1前
      //   component: () => import("@/views/tsjk/panorama.vue"),
      //   meta: {
      //     title: "全景态势",
      //   },
      // },
      {
        path: "/home/<USER>",
        name: "panorama1",
        // component: () => import("@/views/tsjk/tsty.vue"),//2024/7/1
        // component: import("@/views/tsjk/tsty2.vue"),//2024/8/1前
        component: () => import("@/views/tsjk/panorama1.vue"),
        meta: {
          title: "测控全景态势",
        },
      }, {
        path: "/home/<USER>",
        name: "panorama2",
        // component: () => import("@/views/tsjk/tsty.vue"),//2024/7/1
        // component: import("@/views/tsjk/tsty2.vue"),//2024/8/1前
        component: () => import("@/views/tsjk/panorama2.vue"),
        meta: {
          title: "测控任务态势",
        },
      }, {
        path: "/home/<USER>",
        name: "panorama3",
        // component: () => import("@/views/tsjk/tsty.vue"),//2024/7/1
        // component: import("@/views/tsjk/tsty2.vue"),//2024/8/1前
        component: () => import("@/views/tsjk/panorama3.vue"),
        meta: {
          title: "测控资源态势",
        },
      }, {
        path: "/home/<USER>",
        name: "panorama4",
        // component: () => import("@/views/tsjk/tsty.vue"),//2024/7/1
        // component: import("@/views/tsjk/tsty2.vue"),//2024/8/1前
        component: () => import("@/views/tsjk/panorama4.vue"),
        meta: {
          title: "测控目标态势",
        },
      },
      {
        path: "/home/<USER>",
        name: "panorama5",
        // component: () => import("@/views/tsjk/tsty.vue"),//2024/7/1
        // component: import("@/views/tsjk/tsty2.vue"),//2024/8/1前
        component: () => import("@/views/tsjk/userDispatchDesk1.vue"),
        meta: {
          title: "生成态势时间测试",
        },
      },
      {
        path: "/home/<USER>",
        name: "zydd",
        component: () => import("@/views/resource/zydd.vue"),
        meta: {
          title: "资源调度",
        },
      },
      {
        path: "/home/<USER>",
        name: "resourceallocation",
        component: () => import("@/views/resource/ResourceAllocation.vue"),
        meta: {
          title: "资源调度计划",
        },
      },
      {
        path: "/home/<USER>",
        name: "resourcedistribte",
        component: () => import("@/views/resource/ResourceDistribte.vue"),
        meta: {
          title: "资源分配情况",
        },
      },
      {
        path: "/home/<USER>",
        name: "resourceDomain",
        component: () => import("@/views/baseDataPage/resourceDomain.vue"),
        meta: {
          title: "资源域数据管理",
        },
      },
      {
        path: "/home/<USER>",
        name: "baseDataPage",
        component: () => import("@/views/baseDataPage/index.vue"),
        meta: {
          title: "资源数据管理",
        },
      },
      {
        path: "/home/<USER>",
        name: "targetManage",
        // component: () => import("@/views/baseDataPage/wrptglIndex.vue"),
        component: () => import("@/views/baseDataPage/targetDomain.vue"),
        meta: {
          title: "目标数据管理",
        },
      },
      {
        path: "/home/<USER>",
        name: "trackManager",
        // component: () => import("@/views/baseDataPage/wrptglIndex.vue"),
        component: () => import("@/views/baseDataPage/trackManager.vue"),
        meta: {
          title: "航迹数据管理",
        },
      },
      {
        path: "/home/<USER>",
        name: "wrptgl",
        component: () => import("@/views/baseDataPage/wrptgl.vue"),
        meta: {
          title: "无人平台",
        },
      },
      {
        path: "/home/<USER>",
        name: "homePage",
        component: () => import("@/views/home/<USER>"),
        meta: {
          title: "接收数据管理",
        },
      },
      {
        path: "/system/userManage",
        name: "userManage",
        component: () => import("@/views/system/userManage/userManage.vue"),
        meta: {
          title: "用户管理",
        },
      },
      {
        path: "/userDispatchDesk",
        name: "userDispatchDesk",
        component: () => import("@/views/system/userDispatchDesk/userDispatchDesk.vue"),
        meta: {
          title: "用户管理",
        },
      },
    ]
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: "/404",
    hidden: true,
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});
const getSession = () => {
  let session = sessionStorage.getItem("userToken");
  if (session) {
    return true;
  } else {
    return false;
  }
};
router.beforeEach((to, from, next) => {
  // console.log(to, "to");
  //清除mapTool里的数据
  mapTool.removeAll();
  window.lineShow = true
  window.radarShow = true
  window.nameShow = true
  let loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  setTimeout(() => {
    loading.close()
  }, 1000);
  if (getSession() || to.path == "/login" || to.path == "userDispatchDesk") {
    next();
  } else {
    ElMessage.error("请先进行登录后进行操作");
    next("/login");
    // router.push({
    //   path: "/login",
    //   replace: true,
    // });
  }
});
export default router;
