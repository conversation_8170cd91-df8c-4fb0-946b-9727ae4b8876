/*
 * @Author: wlj
 * @Date: 2024-05-29 17:56:27
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-02 13:37:50
 * @FilePath: \wrxtzhglrj\public\js\utils\utils.js
 * @Description:
 */
import * as dataApi from "@/service/API/system/areaManage.js";

export const dicHandle = (dicName) => {
  if (window.dictTypeList[dicName].length > 0) {
    // 查过的字典无需再查
    return;
  }
  dataApi.getDictType({ type: dicName }).then((res) => {
    if (res.data.code == 200) {
      window.dictTypeList[dicName] = {};

      let data = res.data.data;
      data.forEach((item) => {
        window.dictTypeList[dicName][item.dictValue] = item.dictName;
        // { label: item.dictName, row: item };
      });
    } else {
      console.log("获取字典出错", res.data.message);
    }
  });
};

//绘制gis的方法
export const drawMap = (option) => {
  switch (option.type) {
    case "天基":
    case "天基平台":
      window.EVGISMAP("drawPointByAttr", {
        id: option.id + "_point",
        img: "./wx.png",
        group: option.group || "DRAWLAYERGROUP",
        position: option.position, //[120, 30,10000000],option
        name: option.name,
        color: option.color || "#ffff00",
        outlineColor: option.color || "#59E870",
        outlineWidth: "1",
        textColor: option.color || "#59E870",
        textOutlineColor: "black",
        textScale: 1,
        scale: 1,
        direction: 90,
      });
      // window.EVGISMAP("drawCylinderByAttr", {
      //   id: option.id + "_cyLine",
      //   group: "DRAWLAYERGROUP",
      //   position: option.position, //几何体中心坐标位置
      //   length: option.position[2],
      //   bottomRadius: +option.bottomRadius,
      //   // topRadius: +option.topRadius,
      //   color: "rgba(255,0,0,0.5)",
      // });

      break;
    case "地基":
    case "地基平台":
      console.log("地基平台");
      window.EVGISMAP("drawPointByAttr", {
        id: option.id + "_point",
        img: "./dmz1.png",
        group: option.group || "DRAWLAYERGROUP",
        position: option.position,
        name: option.name,
        color: option.color || "#ffff00",
        outlineColor: option.color || "#59E870",
        outlineWidth: "1",
        textColor: option.color || "#59E870",
        textOutlineColor: "black",
        textScale: 1,
        scale: 1,
        direction: 360,
      });
      // window.EVGISMAP("drawCircleByAttr", {
      //   id: option.id + "_circle",
      //   color: "rgba(255,0,0,0.3)",
      //   position: option.position,
      //   radius: option.radius,
      // });
      break;
    case "空基":
    case "空基平台":
      window.EVGISMAP("drawPointByAttr", {
        id: option.id + "_point",
        img: "./fj.png",
        position: option.position,
        group: option.group || "DRAWLAYERGROUP",
        name: option.name,
        color: option.color || "#ffff00",
        outlineColor: option.color || "#59E870",
        outlineWidth: "1",
        textColor: option.color || "#59E870",
        textOutlineColor: "black",
        textScale: 1,
        scale: 1,
        direction: 90,
      });
      break;
    case "无人机":
      console.log("无人机", option);
      window.EVGISMAP("drawPointByAttr", {
        id: option.id + "_point",
        img: "./fj.png",
        position: option.position,
        group: option.group || "DRAWLAYERGROUP",
        name: option.name,
        color: option.color || "#59E870",
        // color: "#fff",
        outlineColor: option.color || "#59E870",
        outlineWidth: "1",
        textColor: option.color || "#59E870",
        textOutlineColor: "black",
        textScale: 1,
        scale: 1,
        direction: 90,
      });

      break;
    case "航迹":
      window.EVGISMAP("drawPolylineByAttr", {
        id: option.id + "_line",
        group: option.group || "DRAWLAYERGROUP",
        position: option.position, // 格式：[[120, 30, 5000],[120, 30, 5000]]
        outlineColor: option.outlineColor || "#59E870",
        outlineWidth: option.outlineWidth || "1",
      });
      break;
  }
  if (option.scope) {
    let pos = [option.position[0], option.position[1], option.position[2] / 2];
    window.EVGISMAP("drawCylinderByAttr", {
      id: option.id + "_cyLine",
      group: option.group || "DRAWLAYERGROUP",
      position: pos, //几何体中心坐标位置
      length: option.cone_height || 10000,
      bottomRadius: +option.bottomRadius || 10000,
      topRadius: +option.topRadius || 10000,
      color: "rgba(255,0,0,0.5)",
    });
  } else if (option.radius) {
    // let pos = [option.position[0], option.position[1], option.position[2]];
    window.EVGISMAP("drawCircleByAttr", {
      id: option.id + "_circle",
      group: option.group || "DRAWLAYERGROUP",
      position: option.position, //几何体中心坐标位置
      radius: option.radius,
      color: "rgba(255,0,0,0.5)",
    });
    if (window.circleList) {
      window.circleList.push(option.id + "_circle");
    } else {
      window.circleList = [option.id + "_circle"];
    }
  }
};

// export const uploadMap = (option)=>{
//   drawPointByAttr

// }
