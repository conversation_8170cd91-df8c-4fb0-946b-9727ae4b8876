import { initSatllite, satlliteTest, getSatllite } from '@/utils/mySatellite.js'
window.entityList = [] //实体管理器
var effectsList = [] //特效管理器
window.flowLineList = [] //流动线管理器
import * as areaApi from '@/service/API/system/areaManage.js'
import { ElMessage } from 'element-plus'
var wxList = {} //需要更新的卫星

let getting = false //是否需要进行卫星具体点请求
let ids = []
function getSatelliteCoordinate() {
  setTimeout(() => {
    areaApi
      .getSatelliteCoordinate(ids)
      .then((res) => {
        if (res.data.code == 200) {
          let data = res.data.data
          if (getting) {
            getSatelliteCoordinate()
          }
          for (const key in data) {
            let item = data[key][0]
            let option = getById(key)?.optionParams
            if (!option) {
              return
            }
            option.longitude = item.degLon
            option.latitude = item.degLat
            option.altitude = item.alt * 1000
            option.time = item.time
            updateMap(option)
          }
        } else {
          ElMessage.error(res.data.message)
        }
      })
      .catch((err) => {
        getting = false
      })
  }, 1000)
  //请求位置
}
setInterval(() => {
  let time = moment().format('YYYY-MM-DD HH:mm:ss')
  ids = []
  for (const key in wxList) {
    let item = wxList[key]
    ids.push({
      generaId: item.generalId,
      equipmentId: item.equipmentId,
      dateTime: time,
    })
  }
  if (ids.length == 0) {
    getting = false
    return
  } else {
    if (!getting) {
      getting = true
      getSatelliteCoordinate()
    }
  }
}, 1000)
/**
 *  1.资源：地基平台 2.资源：空基平台 3.资源：天基平台 4.目标：无人机 5.无人艇 6.目标：无人车 7.目标：弹
 * 无人平台定制绘制方法，其他软件慎用
 */
// 结构
/**
[{
  id:"实体模型ID",
  group:"分组ID",
  entity:{},//实体
  optionParams:{},//参数对象
  effectsList:[{
   type: "radar",//radar雷达 satellite卫星轨道 cylinder圆锥体 billBoard标牌
   // option:{},//对应的参数对象
   entity:{},//实体对象
   id:option.id + '_radar',//特效ID
  }]
}]

 */
// window.entityLists = entityList;
/**
 * @description 更新
 */
export const updateMap = (option) => {
  drawMap(option)
}
/**
 *
 * @param {*} option 地图目标配置参数 包括修改
 */
export const drawMap = async (option) => {
  console.log('option',option);

  let funName = 'draw'
  if (!option.position) {
    option.position = [
      option.longitude || 0,
      option.latitude || 0,
      option.altitude || 0,
    ]
  }
  if(option.position[0] == undefined || option.position[1] == undefined){
	return
  }
  let entityObj = getById(option.id)
  if (entityObj) {
    funName = 'update'
    option = Object.assign({ ...option.optionParams }, { ...option })
  } else {
    entityObj = {
      id: option.id,
      group: option.group || 'DRAWLAYERGROUP',
      entity: null,
      optionParams: option,
      effectsList: [],
    }
  }
  if ((option.dataType == 3 && funName == 'update') || option.dataType != 3) {
    //根据不同的参数绘制不同的实体和标牌 如果实体存在则进行修改
    let pointEntity = drawPoint(option, funName)
    //存入管理器
    entityObj.entity = pointEntity
  }

  // 雷达新增或修改
  // 地面站 将绘制地面站雷达
  // console.log("地面站雷达:",option);
  if (option.dataType == 1 || option.dataType == 8) {
    const circleColor = option.dataType == 8 ? 'rgba(0,255,0,0.2)' : ''
    option.radius = option.radius || option.bottomRadius
    let entity = drawRadar({
      ...option,
      circleColor,
      EntityOption: {
        definitionLabel: {
          customInfo: option.customInfo || null,
          title: option.name || option.targetName,
          showContent: [
            { label: '经度', value: option.position[0] },
            { label: '纬度', value: option.position[1] },
          ],
        }
      }
    }, funName)
    if (funName == 'draw') {
      entityObj.effectsList.push({
        type: 'radar',
        entity: entity,
        id: option.id + '_radar',
      })
    } else {
      for (let item of entityObj.effectsList) {
        if (item.id == option.id + '_radar') {
          item.entity = entity
          break
        }
      }
    }
  }

  entityObj.optionParams = option
  // 卫星新增或修改
  // 地面站 将通过卫星文件调用接口绘制卫星轨道和一个卫星点
  if (option.dataType == 3) {
	  option.bottomRadius = option.bottomRadius || option.radius
    //新增将返回第一个点，修改的时候没有传点，用option里的点
    // let { entity, position } = await drawSatellite(option, funName)

    // if (!position) {
    //   if (option.position) {
    //     position = [option.position]
    //   } else {
    //     position = [[option.longitude, option.latitude, option.altitude]]
    //   }
    // }

    // let entity2 = drawCylinder({ ...option, position: position[0] }, funName);
    if (funName == 'draw') {
      //只在新增时绘制卫星模型点，更新卫星点在websoket中进行特殊处理，不在此处更新
      let pointEntity = drawPoint(option, funName,option.position)
      // let pointEntity = drawPoint(option, funName, [
      //   position[0][0],
      //   position[0][1],
      //   position[0][2],
      // ])
      entityObj.entity = pointEntity
      /*
      return
      //wxManual 该参设为true后需要自动更新卫星实时位置
      if (option.wxAutoPos) {
        moveWx(option.generalId, option.equipmentId)
      }

      //添加卫星轨道
      entityObj.effectsList.push({
        type: 'satellite',
        entity: entity,
        id: option.id + '_satellite',
      })
      //添加卫星圆锥
      // entityObj.effectsList.push({
      //   type: "cylinder",
      //   entity: entity2,
      //   id: option.id + "_cylinder",
      // });
      */
    } else {
      /*
      return
      //修改轨道
      for (let item of entityObj.effectsList) {
        if (item.id == option.id + '_satellite') {
          item.entity = entity
          break
        }
      }
      //修改圆锥
      // for (let item of entityObj.effectsList) {
      //   if (item.id == option.id + "_cylinder") {
      //     item.entity = entity2;
      //     break;
      //   }
      // }
      */
    }
  }
  // 轨迹线新增或修改
  if (option.targetTrack) {
    let entity = drawLine(option, funName)
	  if(!entity) {
		  debugger
	  }
    if (funName == 'draw') {
      entityObj.effectsList.push({
        type: 'line',
        entity: entity,
        id: option.id + '_line',
      })
    } else {
      for (let item of entityObj.effectsList) {
        if (item.id == option.id + '_line') {
          item.entity = entity
          break
        }
      }
    }
  }
  // 圆锥新增或修改
  if (option.dataType == 2 || option.dataType == 3) {
    let entity = drawCylinder(option, funName)
    if (funName == 'draw') {
      entityObj.effectsList.push({
        type: 'cylinder',
        entity: entity,
        id: option.id + '_cylinder',
      })
    } else {
      for (let item of entityObj.effectsList) {
        if (item.id == option.id + '_cylinder') {
          item.entity = entity
          break
        }
      }
    }
  }
  // 绘制标牌 暂无
  if (option.billBoard) {
    drawBillBoard(option, billBoardOption, funName)
  }
  let has = false
  for (let item of entityList) {
    if (item.id == option.id) {
      item.id = entityObj.id
      item.group = entityObj.group
      item.entity = entityObj.entity
      item.optionParams = entityObj.optionParams
      item.effectsList = entityObj.effectsList
      has = true
      break
    }
  }
  !has && entityList.push(entityObj)
  //如果有包含圆锥、固定站半圆雷达 则根据 各自的参数进行绘制
  //所有的绘制了的数据要存入entityList进行管理
  //同时特效要包含在effectsList中
}
//绘制点
function drawPoint(option, funName, position) {
  // console.log("option.name", option.name);
  let nameTxt
  if (window.nameShow === undefined || window.nameShow) {
    nameTxt = option.name || option.targetName
  } else {
    nameTxt = ''
  }
  let params = {
    id: option.id + '_point',
    //1.地基平台 2.空基平台 3.天基平台 4.无人机 5.无人艇 6.无人车 7.弹 8.海基平台 9.终端
    img: window.dataTypeImg[option.dataType - 1],
    group: option.group || 'DRAWLAYERGROUP',
    position: position ||
      option.position || [option.longitude, option.latitude, option.altitude], //[120, 30,10000000],option
    // name: option.name || option.targetName,
    name: nameTxt,
    color: option.color || '#59E870',
    outlineColor: option.outlineColor || option.color || '#59E870',
    outlineWidth: option.outlineWidth || '1',
    // textColor:  option.textColor || option.color || "#59E870",
    textColor: option.textColor || option.color || '#fff',
    // textOutlineColor: option.color || "#59E870",
    textOutlineColor: option.textOutlineColor || 'black',
    textScale: 0.8,
    width: 30,
    height: 34,
    scale: window.CesiumType == '2D' ? 0.3 : 1,
    direction: option.direction || 0,
    definitionLabel: {
      customInfo: option.customInfo || null,
      title: option.name || option.targetName,
      showContent: [{ label: '经度', value: '2222' }],
    },
  }
  params.definitionLabel = {
    customInfo: option.customInfo || null,
    title: params.name,
    showContent:
      option.dataType == 3
        ? [
          { label: '经度', value: params.position[0] },
          { label: '纬度', value: params.position[1] },
          { label: "高度", value: params.position[2]?params.position[2]:0 },
          // {
          //   label: '时间',
          //   value: option.time || moment().format('YYYY-MM-DD HH:mm:ss'),
          // },
        ]
        : [
          { label: '经度', value: params.position[0] },
          { label: '纬度', value: params.position[1] },
          { label: "高度", value: params.position[2]?params.position[2]:0 },
        ],
  }
  let pointEntity = window.EVGISMAP(funName + 'PointByAttr', params)
  return pointEntity
}
//绘制轨迹线
function drawLine(option, funName) {
  // if (funName == 'update') {
  //   return
  // }
  let entity
  let position = []
  option.targetTrack.forEach((item) => {
    position.push([item.longitude, item.latitude, item.altitude || 0])
  })
  console.log('option.id', option.id)
  entity = window.EVGISMAP(funName + 'PolylineByAttr', {
    id: option.id + '_line',
    // group: "DRAWLAYERGROUP",
    position: position, //[120, 30,10000000],option
    group: option.group || 'DRAWLAYERGROUP',
    outlineColor: option.outlineColor || 'red',
    outlineWidth: option.outlineWidth || '1',
    zIndex: 1,
  	show: option.lineShow==undefined?true:option.lineShow
  })
  return entity
}
/**
 * @description 创建流动线
 * @param {*} id1 主实体ID(必填)
 * @param {*} id2 目标实体ID(必填)
 * @returns
 */
export function drawflowLine(id1, id2, color) {
  let funName = 'draw'
  if (!id1 || !id2) {
    console.log('id不能为空')
    return
  }
  if (id1 === id2) {
    console.log('id不能相同')
    return
  }
  let entity1 = getById(id1)
  if (!entity1) {
    console.log('不存在实体' + id1)
    return
  }
  let entity2 = getById(id2)
  if (!entity2) {
    console.log('不存在实体' + id2)
    return
  }
  let pos1 = entity1.optionParams.position
  let pos2 = entity2.optionParams.position
  if(pos1[0] == undefined || pos1[1] == undefined || pos2[0] == undefined || pos2[1] == undefined){
	return
  }

  // let pos1 = entity1?.entity?.position?.getValue()
  // let pos2 = entity2?.entity?.position?.getValue()
  if (!pos1 || !pos2) {
    return
  }
  let obj = {}
  //找到对应的流动线参数
  for (let item of flowLineList) {
    // let a = item.localId == id1 || item.targetId == id1;
    // let b = item.localId == id2 || item.targetId == id2;
    let a = item.localId == id1
    let b = item.targetId == id2
    if (a && b) {
      funName = 'update'
      item.position = [pos1, pos2]
      obj = item
      break
    }
  }
  if (funName == 'draw') {
    obj = {
      id: id1 + '_' + id2 + '_flowLine',
      position: [pos1, pos2],
      group: entity1.group || 'DRAWLAYERGROUP',
      localId: id1,
      targetId: id2,
    }
    flowLineList.push({ ...obj })
  }
  obj.position.forEach(v=>{
    v=[v[0],v[1]]
  })
  console.log("funName", obj.position);
  window.EVGISMAP(funName + 'PolylineByAttr', {
    id: id1 + '_' + id2 + '_flowLine',
    position: obj.position,
    group: obj.group,
    trailLink: true,
    istraiLink: true,
    trailLinkTime: 3000,
    outlineColor: color || 'red',
    outlineWidth: '3',
  })
}
//删除所有流动线
export function removeAllFlowLine() {
  for (let item of flowLineList) {
    window.EVGISMAP('removeGroupEntityById', {
      id: item.id,
      group: item.group || 'DRAWLAYERGROUP',
    })
  }
  flowLineList = []
}
/**
 * @description 删除流动线(传两个ID删除指定线，传第一个ID删除所有有关线)
 * @param {*} id1 主实体ID
 * @param {*} id2 目标实体ID(选填)
 * @returns
 */
export function removeFlowLine(id1, id2) {
  for (let item of flowLineList) {
    // let a = item.localId == id1 || item.targetId == id1;
    // let b = item.localId == id2 || item.targetId == id2;
    let a
    if (id2) {
      a = item.localId == id1
    } else {
      a = item.localId == id1 || item.targetId == id1
    }
    let b = item.targetId == id2
    if (a && (b || !id2)) {
      item.delflag = true
      window.EVGISMAP('removeGroupEntityById', {
        id: item.id,
        group: item.group || 'DRAWLAYERGROUP',
      })
      // if (!id2) {
      //   break;
      // }
    }
  }

  flowLineList = flowLineList.filter((item) => {
    return !item.delflag
  })
}
//绘制雷达
function drawRadar(option, funName) {
  // console.log({
  //   id: option.id + "_radar",
  //   positions: option.position || [option.longitude, option.latitude, option.altitude],
  //   radius: option.radius || 30000,
  // });
  let radius = undefined
  if (funName == 'draw') {
    radius = option.radius || 0
  }
  let entity = window.EVGISMAP(funName + 'RadarFusionByAttr', {
    id: option.id + '_radar',
    position: option.position || [
      option.longitude,
      option.latitude,
      option.altitude,
    ],
    radius: radius,
    // circleColor: "0, 1, 1, 0.3",
    // circleColor: new Cesium.Color(0,1, 1, 0.3),
    circleColor: option.circleColor || 'rgba(198,147,72,0.3)',
    sectorColor: option.sectorColor || 'rgba(255,72,26,0.5)',
    show: !option.noShowRadar,
  })
  // let entity = {};
  return entity
}
//绘制卫星轨道 后台接口
async function drawSatellite(option, funName) {
  return new Promise((resolve, reject) => {
    let entity
    if (funName == 'draw') {
      areaApi
        .calculateOrbit({
          generaId: option.generalId,
          equipmentId: option.equipmentId,
          startTime: option.startTime,
        })
        .then((res) => {
          if (res.data.code == 200) {
            let resPos = []
            // console.log("xx", new Date().getTime());
            res.data.data.forEach((item, index) => {
              // if (index % 10 === 0) {
              resPos.push([item.degLon, item.degLat, item.alt * 1000])
              // }
            })
            // console.log(new Date().getTime());

            entity = window.EVGISMAP(funName + 'PolylineByAttr', {
              id: option.id + '_satellite',
              // group: "DRAWLAYERGROUP",
              position: resPos, //[120, 30,10000000],option
              group: option.group || 'DRAWLAYERGROUP',
              outlineColor: option.outlineColor || 'red',
              outlineWidth: option.outlineWidth || '1',
            })
            // moveWx(option.generalId, option.equipmentId);
            resolve({ entity, position: resPos })
          } else {
            ElMessage.error(res.data.message)
            reject()
          }
        })
    } else {
      /**
       * TODO 暂无修改轨道，考虑是使用定时器
       */
      entity = window.EVGISMAP('getGroupEntityById', {
        id: option.id + '_satellite',
        group: option.group || 'DRAWLAYERGROUP',
      })
      resolve({ entity })
    }
  })
}
//移动卫星位置及特效位置
function moveWx(generalId, equipmentId) {
  wxList[equipmentId] = { generalId: generalId, equipmentId: equipmentId }
}
function stopWx(generalId, equipmentId) {
  delete wxList[equipmentId]
  // console.log("wxList", wxList);
}
//绘制卫星轨道 读取文件
async function drawSatellite2(option, funName) {
  return new Promise((resolve, reject) => {
    let entity
    if (funName == 'draw') {
      areaApi.getFile(option.fileId).then((res) => {
        if (res.data.code == 200) {
          let txtUrl = AdminServerApi + '/file/' + res.data.data
          fetch(txtUrl, {
            headers: { Authorization: sessionStorage.getItem('userToken') },
          }).then((res) =>
            res.text().then((text) => {
              const lines = text
                .split('\n')
                .map((t) => t && t.trim())
                .filter((it) => it)

              if (!lines) {
                return
              }

              let { positionProperty, startTime, endTime } = getSatllite(
                { tleLine1: lines[1], tleLine2: lines[2], name: lines[0] },
                option.name,
                option.group
              )
              entity = Viewer.entities.add({
                id: option.id + '_satellite',
                name: option.name || '卫星',
                availability: new Cesium.TimeIntervalCollection([
                  new Cesium.TimeInterval({
                    start: startTime,
                    stop: endTime,
                  }),
                ]),
                position: positionProperty,
                orientation: new Cesium.VelocityOrientationProperty(
                  positionProperty
                ),
                // 卫星模型
                model: {
                  uri: '/gltf/dsp卫星.gltf', // 模型uri
                  minimumPixelSize: 32,
                  //   maximumScale: 20000,
                },
                path: {
                  resolution: 60,
                  material: new Cesium.PolylineGlowMaterialProperty({
                    glowPower: 0.5,
                    color: Cesium.Color.RED,
                  }),
                  width: 1,
                  // leadTime: calcTByTle2(tleLine2),
                  // trailTime: calcTByTle2(tleLine2),
                },
              })
              entity.position.setInterpolationOptions({
                interpolationDegree: 5,
                interpolationAlgorithm: Cesium.LagrangePolynomialApproximation,
              })
              resolve(entity)
            })
          )
        } else {
          ElMessage.error(res.data.message)
        }
      })
    } else {
      entity = Viewer.entities.getById(option.id)
      //修改颜色
      //  entity
      resolve(entity)
    }
  })
}
//绘制圆锥
function drawCylinder(option, funName) {
  let pos = option.position || [
    option.longitude,
    option.latitude,
    option.altitude,
  ]
  pos = JSON.parse(JSON.stringify(pos))
  let length = pos[2]
  pos[2] = pos[2] / 2
  // pos[2] = pos[2] / 2
  if (option.theta) {
    option.bottomRadius = length * Math.tan((option.theta * Math.PI) / 360)
  }
  let params = {
    id: option.id + '_cylinder',
    group: option.group || 'DRAWLAYERGROUP',
    position: pos, //几何体中心坐标位置
    length: length,
    // length: 0,
    bottomRadius: option.bottomRadius,
    topRadius: option.topRadius,
    color: option.cyLineColor || 'rgba(255,0,0,0.1)',
    show: !option.noShowCylinder,
  }

  let entity = window.EVGISMAP(funName + 'CylinderByAttr', params)
  entity && (entity.show = params.show)
  return entity
}
//绘制标牌
function drawBillBoard(option, txOption, funName) {
  let entity = {}
  return entity
}
/**
 *
 * @param {*} id 根据id获取实体信息和对应的标牌、特效
 */
export const getById = (id) => {
  let entity = null
  for (let item of entityList) {
    if (item.id == id) {
      entity = item
      break
    }
  }
  return entity
}
/**
 *
 * @param {*} id 需要删除的实体ID
 */
export const removeById = (id, toGroup) => {
  // 根据ID和组删除实体
  let index = entityList.findIndex((item) => {
    return item.id == id
  })

  if (index == -1) {
    console.log('没有找到对应实体，无法删除')
    return
  }
  let obj = entityList[index]
  window.EVGISMAP('removeGroupEntityById', {
    id: obj.id + '_point',
    group: obj.group || 'DRAWLAYERGROUP',
  })
  obj.effectsList.forEach((item) => {
    switch (item.type) {
      case 'line':
        // 删除航迹
        window.EVGISMAP('removeGroupEntityById', {
          id: item.id,
          group: obj.group || 'DRAWLAYERGROUP',
        })
        break
      case 'radar':
        //删除雷达
        window.EVGISMAP('removeRadarFusionById', {
          id: item.id,
          group: obj.group || 'DRAWLAYERGROUP',
        })
        break
      case 'satellite':
        if (item.type == 'satellite') {
          stopWx(obj.optionParams.generalId, obj.optionParams.equipmentId)
        }
        //删除卫星轨道
        window.EVGISMAP('removeGroupEntityById', {
          id: item.id,
          group: obj.group || 'DRAWLAYERGROUP',
        })
        break
      case 'cylinder':
        //删除圆锥
        window.EVGISMAP('removeGroupEntityById', {
          id: item.id,
          group: obj.group || 'DRAWLAYERGROUP',
        })
        break
      case 'billBoard':
        //删除标牌
        break
      default:
        break
    }
  })

  //从管理器中移除数据
  if (!toGroup) {
    entityList.splice(index, 1)
  }

  for (let item of flowLineList) {
    item.delflag = true
    let flag = item.localId == id || item.targetId == id
    flag &&
      window.EVGISMAP('removeGroupEntityById', {
        id: item.id,
        group: item.group || 'DRAWLAYERGROUP',
      })
  }
  flowLineList = flowLineList.filter((item) => {
    return !item.delflag
  })
}
/**
 *
 * @param {*} id 需要删除的组ID
 */
export const deleteByGroup = (groupId) => {
  entityList.forEach((item) => {
    if (item.group == groupId) {
      removeById(item.id, true)
      item.delete = true
    }
  })
  entityList = entityList.filter((item) => {
    if (!item.delete) {
      return item
    }
  })
  // window.entityLists = entityList;
  // 根据组删除所有实体
  //从管理器中移除数据
}

/**
 *
 * @param {*} id 删除所有实体并清空管理器
 */
export const removeAll = () => {
  // 删除实体
  // 删除特效
  // 清空数据
  // entityList.forEach((item) => {
  //   removeById(item.id);
  // });
  // // entityList = [];
  entityList.forEach((item) => {
    if (item.type == 'satellite') {
      stopWx(item.optionParams.generalId, item.optionParams.equipmentId)
    }
  })
  entityList.forEach((item) => {
    removeById(item.id)
  })
  window.EVGISMAP && window.EVGISMAP('removeAllMapsEle')
  wxList = []
  entityList = []
  flowLineList = []
}
/**
 *
 * @param {*} show 显示或隐藏所有实体
 * @param {*} showEffects 显示或隐藏特效，默认true
 */
export const showEntityByAll = (show, showEffects) => {
  for (const item of entityList) {
    showEntityById(item.id, show, showEffects)
  }
}
/**
 *
 * @param {*} id 需要隐藏或显示的实体ID
 * @param {*} show 显示或隐藏实体
 * @param {*} showEffects 显示或隐藏特效，默认true
 */
export const showEntityById = (id, show, showEffects) => {
  // 根据ID隐藏或显示实体
  let index = entityList.findIndex((item) => {
    return item.id == id
  })

  if (index == -1) {
    console.log('没有找到对应实体，无法进行显隐控制')
    return
  }
  let obj = entityList[index]
  entityList[index].show = show
  window.EVGISMAP('updatePointByAttr', {
    id: obj.id + '_point',
    group: obj.group || 'DRAWLAYERGROUP',
    show: show,
  })
  if (showEffects !== false) {
    obj.effectsList.forEach((item) => {
      let effectShow

      switch (item.type) {
        case 'line':
          effectShow = false
          if (window.lineShow) {
            effectShow = show
          } else {
            effectShow = window.lineShow
          }
          // 显隐航迹
          window.EVGISMAP('updatePolylineByAttr', {
            id: item.id,
            group: obj.group || 'DRAWLAYERGROUP',
            show: effectShow,
          })

          break
        case 'radar':
          effectShow = false
          if (window.radarShow) {
            effectShow = show
          } else {
            effectShow = window.radarShow
          }
          //显隐雷达
          window.EVGISMAP('updateRadarFusionByAttr', {
            id: item.id,
            group: obj.group || 'DRAWLAYERGROUP',
            // show: show,
            show: effectShow,
          })
          break
        case 'satellite':
          effectShow = false
          if (window.lineShow) {
            effectShow = show
          } else {
            effectShow = window.lineShow
          }
          //显隐卫星轨道
          window.EVGISMAP('updatePolylineByAttr', {
            id: item.id,
            group: obj.group || 'DRAWLAYERGROUP',
            // show: show,
            show: effectShow,
          })
          break
        case 'cylinder':
          effectShow = false
          if (window.radarShow) {
            effectShow = show
          } else {
            effectShow = window.radarShow
          }
          //显隐圆锥
          window.EVGISMAP('updateCylinderByAttr', {
            id: item.id,
            group: obj.group || 'DRAWLAYERGROUP',
            // show: show,
            show: effectShow,
          })
          break
        case 'billBoard':
          //删除标牌
          break
        case 'flowLine':
          effectShow = false
          if (window.lineShow) {
            effectShow = show
          } else {
            effectShow = window.lineShow
          }
          // 显隐航迹
          window.EVGISMAP('updatePolylineByAttr', {
            id: item.id,
            group: obj.group || 'DRAWLAYERGROUP',
            show: effectShow,
          })


          break
        default:
          break
      }
      item.show = show
    })
  }
  flowLineList.forEach((item) => {
    if (item.localId == id || item.targetId == id) {
      window.EVGISMAP('updatePointByAttr', {
        id: item.id + '_point',
        group: item.group || 'DRAWLAYERGROUP',
        show: show,
      })
    }
  })
}
/**
 *
 * @param {*} id 需要隐藏或显示的实体ID
 * @param {*} type 显示或隐藏的特效类型
 * @param {*} show 显示或隐藏特性
 */
export const showEffById = (id, type, show) => {
  let types = []
  if('string' === typeof type){
	  types = [type]
  }else if(type instanceof Array){
	  types = [...type]
  }
  // 根据ID隐藏或显示实体
  let index = entityList.findIndex((item) => {
    return item.id == id
  })

  if (index == -1) {
    console.log('没有找到对应实体，无法进行显隐控制')
    return
  }
  let obj = entityList[index]
  // entityList[index].show = show
  // window.EVGISMAP("updatePointByAttr", {
  //   id: obj.id + "_point",
  //   group: obj.group || "DRAWLAYERGROUP",
  //   show: show,
  // });
  if (types.includes('label')) {
    // 显隐名称
    window.EVGISMAP('updatePointByAttr', {
      id: obj.id + '_point',
      group: obj.group || 'DRAWLAYERGROUP',
      name: show ? obj.optionParams.name : '',
    })
    return
  }
  obj.effectsList.forEach((item) => {
    if (types.includes(item.type)) {
      let effectShow = false
      switch (item.type) {
        case 'line':
          effectShow = false
          if (window.lineShow) {
            effectShow = show
          } else {
            effectShow = window.lineShow
          }
          // 显隐航迹
          window.EVGISMAP('updatePolylineByAttr', {
            id: item.id,
            group: obj.group || 'DRAWLAYERGROUP',
            // show: show,
            show: effectShow,
          })

          break
        case 'radar':
          effectShow = false
          if (window.radarShow) {
            effectShow = show
          } else {
            effectShow = window.radarShow
          }
          //显隐雷达
          window.EVGISMAP('updateRadarFusionByAttr', {
            id: item.id,
            group: obj.group || 'DRAWLAYERGROUP',
            // show: show,
            show: effectShow,
          })
          break
        case 'satellite':
          effectShow = false
          if (window.lineShow) {
            effectShow = show
          } else {
            effectShow = window.lineShow
          }
          //显隐卫星轨道
          window.EVGISMAP('updatePolylineByAttr', {
            id: item.id,
            group: obj.group || 'DRAWLAYERGROUP',
            // show: show,
            show: effectShow,
          })
          break
        case 'cylinder':
          effectShow = false
          if (window.radarShow) {
            effectShow = show
          } else {
            effectShow = window.radarShow
          }
          //显隐圆锥
          window.EVGISMAP('updateCylinderByAttr', {
            id: item.id,
            group: obj.group || 'DRAWLAYERGROUP',
            // show: show,
            show: effectShow,
          })
          break
        case 'billBoard':
          //删除标牌
          break
        default:
          break
      }
      item.show = show
    }
  })
  flowLineList.forEach((item) => {
    if (item.localId == id || item.targetId == id) {
      window.EVGISMAP('updatePointByAttr', {
        id: item.id + '_point',
        group: item.group || 'DRAWLAYERGROUP',
        show: show,
      })
    }
  })
}
export const showFlowLine = (show) => {
  flowLineList.forEach((item) => {
    console.log('item',item);

      window.EVGISMAP('updatePolylineByAttr', {
        id: item.id,
        group: item.group || 'DRAWLAYERGROUP',
        show: show,
      })
  })
}
/**
 * @param {*} type 显示或隐藏的特效类型
 * @param {*} show 显示或隐藏所有指定特效
 *
 */
export const showEffByAll = (type, show) => {
  for (const item of entityList) {
    showEffById(item.id, type, show)
  }
}

/**
 *
 */
export const flyTo = (options) => {
  Viewer.camera.flyTo({
    ...options,
    orientation: {
      heading: Cesium.Math.toRadians(options.heading || 0),
      pitch: options.pitch
        ? Cesium.Math.toRadians(options.pitch)
        : camera.pitch,
      roll: options.roll ? Cesium.Math.toRadians(options.roll) : camera.roll,
    },
  })
}

//测试用 和项目内任何代码无关 清空即刷新界面
export const lineline = (list) => {
  window.EVGISMAP('drawPolylineByAttr', {
    id: Math.random * 100000000 + '_taskLine',
    position: list,
    group: 'EVMAPGROUP',
    outlineColor: 'blue',
    outlineWidth: '5',
    zIndex: 4,
  })
}
