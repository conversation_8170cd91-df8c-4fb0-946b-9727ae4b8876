/*
 * @Author: nameZ
 * @data: Do not edit
 * @Description: 
 * @LastEditors: Do not edit
 * @LastEditTime: 2024-03-15 16:36:23
 * @FilePath: \qbmbtsweb\src\utils\transformation.js
 */
//根据敌我属性返回颜色
function enemyColor(enemy) {
  var color = JSON.parse(localStorage.getItem("color"));
  switch (Number(enemy)) {
    case 1 :
        //我方
      return color[1];
      break;
    case 2:
        //敌方
      return color[2];
      break;
    case 3:
        //友方
      return color[3];
      break;
    case 4:
        //不明
      return color[4];
      break;
  }
}

export { enemyColor };
