// import * as satellite from "satellite.js";
const Cesium = window.Cesium,
  satellite = window.satellite;
let viewer = window.Viewer;

let satelliteMap = new Map();
export function getSatllite({ tleLine1, tleLine2, name }, row, group) {
  viewer = window.Viewer;
  let satrec = satellite.twoline2satrec(tleLine1, tleLine2);
  let minsPerInterval = (2 * Math.PI) / satrec.no; // mins for 1 revolution around earth
  let positionProperty = null;
  let positionPonit = null;
  let postions = null;
  [postions, positionPonit, positionProperty] = getPositionSample(satrec, minsPerInterval);
  const { startTime, endTime } = getStratEndTime(minsPerInterval);

  return { positionProperty, startTime, endTime };
}
export function initSatllite({ tleLine1, tleLine2, name }, row, group) {
  viewer = window.Viewer;

  let satrec = satellite.twoline2satrec(tleLine1, tleLine2);
  console.log(satrec, "satrec");
  let minsPerInterval = (2 * Math.PI) / satrec.no; // mins for 1 revolution around earth
  // console.log(minsPerInterval, "minsPer");

  let positionProperty = null;
  let positionPonit = null;
  let postions = null;
  // catch
  [postions, positionPonit, positionProperty] = getPositionSample(satrec, minsPerInterval);

  // if (satelliteMap.has(name)) {
  //   positionProperty = satelliteMap.get(name);
  // } else {
  //   // 获取positionProperty
  //   [positionProperty, positionPonit] = getPositionSample(satrec, minsPerInterval);
  //   satelliteMap.set(name, positionProperty);
  // }

  // 获取startTime && endTime
  const { startTime, endTime } = getStratEndTime(minsPerInterval);
  console.log("positionPonit", positionPonit[0]);
  let random = Math.random();
  window.EVGISMAP("drawPointByAttr", {
    id: "name" + random + "_point",
    img: "./wx.png",
    group: group || "DRAWLAYERGROUP",
    position: positionPonit[0], //[120, 30,10000000],option
    name: row.name || name || "卫星",
    color: "#ffff00",
    outlineColor: "green",
    outlineWidth: "1",
    textColor: "red",
    textOutlineColor: "black",
    textScale: 1,
    scale: 1,
    direction: 90,
  });
  let pos = [positionPonit[0][0], positionPonit[0][1], positionPonit[0][2]];
  window.EVGISMAP("drawCylinderByAttr", {
    id: "name" + random + "_cyLine",
    group: group || "DRAWLAYERGROUP",
    position: pos, //几何体中心坐标位置
    length: pos[2] * 2,
    // length: row.cone_height,
    bottomRadius: +row.bottom_radius,
    topRadius: +row.top_radius,
    color: "rgba(255,0,0,0.5)",
  });
  let time = new Date(endTime).getTime() - new Date(startTime).getTime();
  //  =  Math.random() + ''
  let i = 1;

  let inteval = setInterval(() => {
    if (i >= positionPonit.length) {
      i = 0;
    }
    let entity = window.EVGISMAP("getGroupEntityById", {
      group: group || "DRAWLAYERGROUP",
      id: "name" + random + "_point",
    });
    if (!entity) {
      clearInterval(inteval);
      return;
    }
    window.EVGISMAP("updatePointByAttr", {
      position: positionPonit[i],
      id: "name" + random + "_point",
      group: group || "DRAWLAYERGROUP",
    });
    let posi = [positionPonit[i][0], positionPonit[i][1], positionPonit[i][2] / 2];

    window.EVGISMAP("updateCylinderByAttr", {
      position: posi,
      id: "name" + random + "_cyLine",
      group: group || "DRAWLAYERGROUP",
    });
    i++;
  }, 1000);
  // console.log(positionPonit);
  window.EVGISMAP("drawPolylineByAttr", {
    id: "name" + random + "_line",
    // group: "DRAWLAYERGROUP",
    position: positionPonit, //[120, 30,10000000],option
    group: group || "DRAWLAYERGROUP",
    outlineColor: "red",
    outlineWidth: "1",
  });

  // const entity = viewer.entities.add({
  //   id: name,
  //   name: name || "卫星",
  //   availability: new Cesium.TimeIntervalCollection([
  //     new Cesium.TimeInterval({
  //       start: startTime,
  //       stop: endTime,
  //     }),
  //   ]),
  //   position: positionProperty,
  //   orientation: new Cesium.VelocityOrientationProperty(positionProperty),
  //   // 卫星模型
  //   model: {
  //     uri: "/gltf/dsp卫星.gltf", // 模型uri
  //     minimumPixelSize: 32,
  //     //   maximumScale: 20000,
  //   },
  //   path: {
  //     resolution: 60,
  //     material: new Cesium.PolylineGlowMaterialProperty({
  //       glowPower: 0.5,
  //       color: Cesium.Color.RED,
  //     }),
  //     width: 1,
  //     // leadTime: calcTByTle2(tleLine2),
  //     // trailTime: calcTByTle2(tleLine2),
  //   },
  // });
  // entity.position.setInterpolationOptions({
  //   interpolationDegree: 5,
  //   interpolationAlgorithm: Cesium.LagrangePolynomialApproximation,
  // });
}
/**
 * 计算 startTime && endTime
 * minsPerInterval: 一圈分钟数
 */
function getStratEndTime(minsPerInterval) {
  const startTimeStamp = Date.now();
  // 结束时间为一圈后的时间
  const endTimeStamp = startTimeStamp + minsPerInterval * 60 * 1000;
  let startTime = new Cesium.JulianDate.fromDate(new Date(startTimeStamp));
  startTime = Cesium.JulianDate.addHours(startTime, 8, new Cesium.JulianDate());
  let endTime = new Cesium.JulianDate.fromDate(new Date(endTimeStamp));
  endTime = Cesium.JulianDate.addHours(endTime, 8, new Cesium.JulianDate());
  return {
    startTime,
    endTime,
  };
}
/**
 * 计算SampledPositionProperty
 * satrec: satellite.twoline2satrec返回值
 * minsPerInterval：一圈分钟数
 */
function getPositionSample(satrec, minsPerInterval) {
  const positionProperty = new Cesium.SampledPositionProperty();
  const postions = [];
  const positionPonit = [];
  const now = Date.now();
  let timeOut = parseInt(minsPerInterval / 64) + 1;
  for (let i = 0; i <= minsPerInterval + timeOut; i += timeOut) {
    // 从现在起，获取一圈内每分钟的位置;生成一个数组，用作插值
    const curTimeDate = new Date(now + i * 60 * 1000);
    let positionAndVelocity = satellite.propagate(satrec, curTimeDate); // 此方法拿到的是惯性系坐标
    // let gmst = satellite.gstime(new Date(curTimeDate));
    // 惯性
    // console.log(positionAndVelocity);
    const positionEci = positionAndVelocity.position;
    // 惯性转成地固
    // const positionEcf = satellite.eciToEcf(positionEci, gmst);
    // julian日期
    const curJulianDate = new Cesium.JulianDate.fromDate(curTimeDate);
    // 北京时
    const d = new Cesium.JulianDate.addHours(curJulianDate, 8, new Cesium.JulianDate());
    positionProperty.addSample(
      d,
      // 这里使用惯性或者地固都行，但是地固系在端点处偏差较大，因此使用惯性系
      new Cesium.Cartesian3(positionEci.x * 1000, positionEci.y * 1000, positionEci.z * 1000)
    );

    postions.push(new Cesium.Cartesian3(positionEci.x * 1000, positionEci.y * 1000, positionEci.z * 1000));
    // positionPonit.push([(positionEci.x * 1000) % 360, (positionEci.y * 1000) % 180, positionEci.z * 1000]);
    positionPonit.push([(positionEci.x * 1000) % 180, (positionEci.y * 1000) % 90, Math.abs(positionEci.z * 1000)]);
  }
  // return positionProperty;
  return [postions, positionPonit, positionProperty];
}

// 根据六要素，生成卫星轨道
function calcT(a) {
  return 2 * Math.PI * Math.sqrt((a * a * a) / 3.986e14);
}

// 根据tle，生成卫星轨道
function calcTByTle2(line2) {
  if (line2.length !== 69) {
    return 0;
  }
  let nums = line2.substring(53, 63);
  return 86400 / nums;
}
export function dmzNetworkTest(flag) {
  viewer = window.EVGISMAP?.EVMAP;
  if (!flag) {
    // viewer.entities.removeAll();
    viewer.entities.values.forEach((item) => {
      if (item.name.startsWith("地面站")) {
        setTimeout(() => {
          viewer.entities.remove(item);
        }, 0);
      }
    });
    return;
  }
  let posarr = [
    [105, 30, 0],
    [120, 30, 0],
    [115, 40, 0],
  ];
  for (let index = 0; index < posarr.length; index++) {
    if (index != 0) {
      let pos = index == 1 ? [...posarr[index], ...posarr[index - 1]] : [...posarr[index - 1], ...posarr[index]];
      viewer.entities.add({
        id: "地面站通信" + index,
        name: "地面站通信" + index,
        polyline: {
          positions: Cesium.Cartesian3.fromDegreesArrayHeights(pos),
          width: 2,
          material: new Cesium.EV_DynamicPolyline(
            Cesium.Color.GREEN,
            1000,
            Cesium.EV_DynamicPolyline.Direction.ClockWise
          ),
        },
      });
    }

    const entity = viewer.entities.add({
      id: "地面站" + index,
      name: "地面站" + index,
      data: {
        name: "地面站" + index,
        area: "中国" + index,
      },
      position: Cesium.Cartesian3.fromDegrees(...posarr[index]),
      billboard: {
        image: "/images/bg/地面站.png",
        width: 30,
        height: 30,
      },
    });
  }
}
export function satlliteTest(flag) {
  // viewer = window.EVGISMAP?.EVMAP;
  viewer = window.Viewer;
  if (!flag) {
    if (viewer.scene) {
      // viewer.entities.removeAll();
      viewer.entities.values.forEach((item) => {
        if (item.name.startsWith("卫星")) {
          setTimeout(() => {
            viewer.entities.remove(item);
          }, 0);
        }
      });
      viewer.clock.shouldAnimate = false;
      return;
    }
    viewer.scene.globe.depthTestAgainstTerrain = true;
    viewer.scene.enableDepthPlane = true;
    // viewer.scene.requestRenderMode = true;
    // viewer.scene.maximumRenderTimeChange = 1 || Infinity;
    viewer.clock.shouldAnimate = true;

    const { startTime, endTime } = getStratEndTime(1440 * 2);
    viewer.clock.startTime = startTime.clone();
    viewer.clock.endTime = endTime.clone();
    viewer.clock.currentTime = startTime.clone();
    // Sample TLE-tle数据示例
  }
  initSatllite({
    tleLine1: "1 25544U 98067A   19156.50900463  .00003075  00000-0  59442-4 0  9992",
    tleLine2: "2 25544  51.6433  59.2583 0008217  16.4489 347.6017 15.51174618173442",
    name: "卫星1",
  });
  initSatllite({
    tleLine1: "1 43246U 18029B   18194.72004803 -.00000064  00000-0  10000-3 0  9991",
    tleLine2: "2 43246  55.0642  39.6410 0004409 332.7876  76.6516  1.86232943  1964",
    name: "BEIDOU-3 M8",
  });
  initSatllite({
    tleLine1: "1 48274U 21035A 23091.00000000 .00021606 00000-0 22868-3 0 9995",
    tleLine2: "2 48274 41.4722 266.5980 0004849 263.0422 66.3063 15.63901732109761",
    name: "卫星-1",
  });
  initSatllite({
    tleLine1: "1 37737U 11032A 14147.49978063 -.00000062 00000-0 00000+0 0 8196",
    tleLine2: "2 37737 000.3101 081.0082 0026384 186.7849 324.1048 01.00269475 10567",
    name: "卫星-2",
  });
  initSatllite({
    tleLine1: "1 38730U 12040A 14148.20302271 .00000095 00000-0 10000-3 0 5313",
    tleLine2: "2 38730 000.6535 269.9905 0002053 244.1819 181.3008 01.00272014 6836",
    name: "卫星-3",
  });
}
