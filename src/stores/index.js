/*
 * @Author: ZJ
 * @data: Do not edit
 * @@Description::
 * @LastEditors: Do not edit
 * @LastEditTime: 2023-09-11 13:30:32
 * @FilePath: \XJCRB0368_WEB\src\stores\index.js
 */
import { defineStore } from "pinia";
import { ElMessage } from "element-plus";

export const useUserStore = defineStore("user", {
  state: () => ({
    userName: "user",
    userPwd: "user",
    notufyInfo: {},//最新消息数据
    // 消息中文件解析数据
    emailRequirementHandle: {}
  }),
  getters: {},
  actions: {
    changeUserName(data) {
      this.userName = data;
      this.userPwd = data;
    },
    changeNotufyInfo(data) {
      const {type,data:info} = JSON.parse(data)
      if (type != 10) return
      if(info.state === 'SUCCESS'){
        ElMessage.success(`${info.target}接入成功`)
        delete this.notufyInfo[info.sourceNode]
      }else if(info.state === 'FAILED'){
        ElMessage.error(`${info.target}接入失败`)
        delete this.notufyInfo[info.sourceNode]
      }else if(info.state === 'CANCEL'){
        ElMessage.error(`${info.target}取消接入`)
        delete this.notufyInfo[info.sourceNode]
      }else{
        this.notufyInfo[info.sourceNode] = info
      }
    },
    changeEmailRequirementHandle(data) {
      this.emailRequirementHandle = data;
    }
  },
});

export const useSockeMessStore = defineStore("socketMess", {
  state: () => ({
    socketMessage: [],
    tsSocket:[],
    tstimeSocket:[],
    zyssSocket:[],
    statedef:false,
    moreFlag:false
  }),
  getters: {},
  actions: {
    changeSocketMessageInfo(data) {
      const {type,data:info} = JSON.parse(data)
      // if (type != 10) return
      // if(info.state === 'SUCCESS'){
      //   ElMessage.success(`${info.target}接入成功`)
      //   delete this.notufyInfo[info.sourceNode]
      // }else if(info.state === 'FAILED'){
      //   ElMessage.error(`${info.target}接入失败`)
      //   delete this.notufyInfo[info.sourceNode]
      // }else if(info.state === 'CANCEL'){
      //   ElMessage.error(`${info.target}取消接入`)
      //   delete this.notufyInfo[info.sourceNode]
      // }else{
      //   this.notufyInfo[info.sourceNode] = info
      // }
      this.socketMessage = info
    },
  },
});
