/*
 * @Author: drz
 * @data: 2023-09-12
 * @@Description::
 * @LastEditors: 路由相关的状态管理器
 * @LastEditTime: 2023-09-12
 * @FilePath: \XJCRB0368_WEB\src\stores\users.js
 */
import { defineStore } from "pinia";

export const routerStore = defineStore("routers", {
  state: () => ({
    oneRouter: "", // 一级路由
  }),
  // 属性
  getters: {
    /**
     * @description 获取一级路由
     * @param {*} state
     */
    getOneRouter: (state) => {
      return state.oneRouter;
    },
  },
  // 方法
  actions: {
    /**
     * @description 设置一级路由
     * @param {String} data
     */
    setOneRouter(data) {
      this.oneRouter = data;
      window.sessionStorage.setItem("oneRoute", data);
    },
  },
});
