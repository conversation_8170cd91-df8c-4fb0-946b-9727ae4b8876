.el-form-item__label {
  color: #1eb2f4;
}

/* table */

@import "@/assets/style/theme/style.less";

.custom-table {
  // flex: 1;
  width: 100%;
  height: calc(100% - 40px);
  // height: 680px;
}

.ArrayClass {
  margin: 5px;
  padding: 0px 5px;
  display: inline-block;
  border: 1px solid #1054a4;
}

.custom_header_sc {
  width: 24px;
  height: 24px;
  background-image: @custom_header_delete;
  background-size: cover;
}

.custom_header_bianji {
  background-image: @custom_header_Edit;
  background-size: cover;
}

.custom_header_ck {
  background-image: @custom_header_look;
  background-size: cover;
}

.el-link :hover {
  color: #1eb2f480
}