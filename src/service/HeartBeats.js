/*
 * @Author: your name
 * @Date: 2022-02-18 11:23:51
 * @LastEditTime: 2022-04-01 17:44:59
 * @LastEditors: PXL
 * @Description: 创建ws心跳的类
 * @FilePath: \qyzhts-front\src\service\heartBeats.js
 */
export default class HeartBeatsClass {
  constructor(interval, ws) {
    this.timeout = interval
    this.ws = ws
    this.timeoutObj = null
    this.serverTimeoutObj = null
  }
  start() {
    var self = this
    this.timeoutObj && clearTimeout(this.timeoutObj)
    this.serverTimeoutObj && clearTimeout(this.serverTimeoutObj)
    this.timeoutObj = setTimeout(function () {
      //这里发送一个心跳，后端收到后，返回一个心跳消息，
      self.ws.send('HeartBeats')
      // 超时没有收到回执表明后端大概率已经断开了连接
      self.serverTimeoutObj = setTimeout(function () {
        self.ws.close()
        // createWebSocket();
      }, self.timeout)
    }, self.timeout)
  }
}
