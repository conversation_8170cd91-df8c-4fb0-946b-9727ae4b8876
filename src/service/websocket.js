/*
 * @Author: your name
 * @Date: 2022-02-18 11:06:04
 * @LastEditTime: 2022-07-19 09:21:31
 * @LastEditors: hy
 * @Description: 定义创建WebSocke的类
 * @FilePath: \qyzhts-front\src\service\websocket.js
 */
// 废弃-----2024-8-5
import { ElMessage } from 'element-plus'
import HeartBeats from './HeartBeats'
// import ReconnectingWebsocket from '../../public/js/websocket/reconnecting-websocket.min'
// import * as ReconnectingWebsocket from "../../public/js/websocket/reconnecting-websocket.min";

export class WebSocketClass {
  constructor(wsurl) {
    this.url = wsurl
    this.ws = null
    this.HeartBeats = null
    this.myInterceptor = new wsInterceptor()
  }
  // -创建连接
  initWS() {
    try {
      this.ws = new WebSocket(this.url)
      // this.ws = new ReconnectingWebsocket(this.url)
      const self = this
      // 创建成功
      this.ws.onopen = function (params) {
        //连接心跳
        console.log('连接开启');
        // self.heartBeats = new HeartBeats (2000,self.ws)
        // self.heartBeats.start()
      }
      // -接受数据
      this.ws.onmessage = function (event) {

        self.myInterceptor.run(event.data)
      }
      //-连接关闭的回调方法
      this.ws.onclose = function (e) {
        console.log('连接关闭');
        // ElMessage({
        //   message: `${self.url}的WS关闭了`,
        //   type: 'warning',
        // })
      }
      //onerror
      this.ws.onerror = function (e) {
        console.log('连接异常关闭');
      }


    } catch (error) {
      ElMessage.error(`${self.url}的WS连接失败`)

    }
  }
  // - 消息处理函数
  dealMsg(callback, fName) {
    if (Object.prototype.toString.call(callback).slice(8, -1) !== 'Function') {
      throw new Error(`处理WebSocket的函数需要接收一个Function作为参数，而不是${Object.prototype.toString.call(callback).slice(8, -1)}`)
    }
    this.myInterceptor.use(callback, fName)

  }

  //-关闭连接
  closeWS() {
    this.ws.close()
  }
  //-发送消息
  sendMsg(message) {
    this.ws.send(message)
  }
  //重连方法
  reconnect() {
    this.j++
    if (this.j > 1) {
      return
    } else {
      if (this.socket.readyState == 1 || this.socket.readyState == 0) {
        return false
      } else {
        console.log('开始重连');
        this.time = setTimeout(() => {
          this.initWS(this.wsurl)
        }, 4000)
      }
    }
  }
  //心跳检测
  // heartCheck = {
  //   timeout:3000,
  //   timeoutObj:null,
  //   serverTimeoutObj:null,
  //   start:function(){
  //     console.log('start');
  //     var self = this
  //     this.timeoutObj && clearTimeout(this.timeoutObj)
  //     this.serverTimeoutObj && clearTimeout(this.serverTimeoutObj)
  //     this.timeoutObj = setTimeout(function(){
  //       this.ws.send('123123')
  //       self.serverTimeoutObj = setTimeout(function(){
  //         this.ws.close()
  //       },self.timeout)
  //     },this.timeout)
  //   }
  // }
}

class wsInterceptor {
  constructor() {
    this.handlers = {}
  }
  isJsonStr(jsonStr) {
    let str
    try {
      str = JSON.parse(jsonStr)
    } catch (e) {
      return jsonStr
    }
    return str
  }
  use(fun, fName) {
    this.handlers[fName] = fun
  }
  run(res) {
    let data = this.isJsonStr(res)
    switch (data.fucName) {
      case 'analogRecording': {
        this.handlers[data.fucName] && this.handlers[data.fucName](data)
        break
      }
      case 'atmosphere': {
        this.handlers[data.fucName] && this.handlers[data.fucName](data)
        break
      }
      default: {
        this.handlers['info'](data)
        break
      }
    }
  }
}
