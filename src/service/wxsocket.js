/*
 * @Author: JRX <EMAIL>
 * @Date: 2024-07-08 10:26:46
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-08 14:42:47
 * @FilePath: \wrxtzhglrj\src\service\wxsocket.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// --------废弃-2024-8-5
import { ElMessage } from "element-plus";
import * as mapTool from "@/utils/mapTool.js";
import {useSockeMessStore} from '@/stores/index'
var wx;
export function init() {
  let sockeMessStore = useSockeMessStore()
  wx = new WebSocket(AdminServerWS + "/ws/simulation");
  wx.onopen = (msg) => {
    console.log("WebSocket", msg);
  };
  wx.onmessage = (msg) => {
    //接受卫星数据，然后传坐标点处理
    let data = JSON.parse(msg.data);
    let message = JSON.parse(data.message);
    if (data.type == 2) {
      let option = mapTool.getById(message.equipmentId)?.optionParams;
      option.longitude = message.longitude;
      option.latitude = message.latitude;
      option.altitude = message.altitude;
      mapTool.updateMap(option);
    }
    if([3].includes(data.type)){
      sockeMessStore.changeSocketMessageInfo(msg.data)
    }
  };
}
export function onmessage() {
  // wx.onopen()
}
// generaId   equipmentId
export function send(data) {
  wx.send(JSON.stringify(data));
}
export function close() {
  wx.close();
}
