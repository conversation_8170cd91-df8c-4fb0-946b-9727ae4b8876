/*
 * @Author: ZJ
 * @data: 2023-09-11 09:35:41
 * @@Description:: 请求
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-24 18:06:49
 * @FilePath: \XJCRB0368_WEB\src\service\request.js
 */
import axios from "axios";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";

const router = useRouter();
export class requestDemo {
  constructor(config) {
    this.axiosInstance = axios.create(config);
    //请求拦截
    this.axiosInstance.interceptors.request.use(
      (config) => {
        const token = sessionStorage.getItem("userToken");
        if (token) {
          config.headers.Authorization = token;
        }
        if (config.getFile) {
          config.responseType = "blob";
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    // 响应拦截
    this.axiosInstance.interceptors.response.use(
      (res) => {
        // console.log("res", res);
        if (res.data.code === 200) {
          if (res.data.message === "令牌过期") {
            return ElMessage.error("登陆过期，请重新登陆");
          }
          return res;
        } else {
          console.log("响应失败:", res.data.message);
          return res;
        }
      },
      (error) => {
        if (error.response?.data == "登录过期，请重新登录") {
          sessionStorage.removeItem("userToken");
          // setTimeout(() => {
          //   console.log("router", router);
          //   router.push({
          //     path: "/login",
          //     replace: true,
          //   });
          // }, 5000);
          ElMessage.error("登录过期，请重新登录");
          setTimeout(() => {
            window.location.href = window.location.origin + "/#/login";
          }, 3000);
        }
        console.log("error", error);
        return Promise.reject(error);
      }
    );
  }

  request(config) {
    return new Promise((resolve, reject) => {
      this.axiosInstance
        .request(config)
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }
  // - 文件上传专用
  // fileRequest(url, data) {
  //   return new Promise((resolve, reject) => {
  //     axios
  //       .post(url, data, {
  //         headers: {
  //           'Content-Type': `multipart/form-data; boundary=${new Date().getTime()}`,
  //         },
  //       })
  //       .then((res) => {
  //         resolve(res)
  //       })
  //       .catch((err) => {
  //         reject(err)
  //       })
  //   })
  // }
  get(config) {
    if (config.params) {
      Object.keys(config.params).forEach((item) => {
        if (!config.params[item]) {
          delete config.params[item];
        }
      });
    }
    return this.request({ method: "get", ...config });
  }
  post(config) {
    if (config.data) {
      Object.keys(config.data).forEach((item) => {
        if (!config.data[item] && config.data[item] != 0) {
          delete config.data[item];
        }
      });
    }
    return this.request({ method: "post", ...config });
  }
  delete(config) {
    return this.request({ method: "delete", ...config });
  }
  put(config) {
    return this.request({ method: "put", ...config });
  }
}
