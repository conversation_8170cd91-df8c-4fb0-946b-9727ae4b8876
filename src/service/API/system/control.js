import { requestDemo } from "@/service/request";
const request = new requestDemo({
    baseURL: AdminServerApi,
});
//整机参数设置
export const setMachineParam = (params) => {
    return request.post({
        url: "/external/terminal/setMachineParam",
        data: params,
    });
};
//抗干扰控制帧
export const antiJamming = (params) => {
    return request.post({
        url: "/external/terminal/antiJamming",
        data: params,
    });
};
//查询终端对应的体制
export const getTerminalMode = (params) => {
    return request.get({
        url: `/external/terminal/mode`,
        params: params,
    });
};
//数据传输控制
export const setDataControlParam = (params) => {
    return request.post({
        url: "/external/terminal/transfer",
        data: params,
    });
};
//获取当前节点接入开关
export const getTerminalNode = () => {
    return request.get({
        url: "/external/terminal/node/select",
    });
};
//节点接入算法开关
export const setTerminalNode = (params) => {
    return request.post({
        url: "/external/terminal/node/select",
        data: params,
    });
};
//无人机模拟导弹
export const uavSimulateDD = (val) => {
    return request.get({
        url: "/uavSimulateDDt",
        params: val ? undefined : { change: val },
    });
};
// 获取数据跟随状态
export const getFollowStatus = () => {
	return request.get({
		url: "/external/config/follow/status",
	});
};
// 切换数据跟随状态
export const toggleFollowStatus = () => {
	return request.post({
		url: "/external/config/follow/toggle",
	});
};

// 获取当前数据源模式
export const getDatasourceMode = () => {
	return request.get({
		url: "/external/config/datasource/mode",
	});
};

// 切换数据源模式
export const toggleDatasourceMode = () => {
	return request.post({
		url: "/external/config/datasource/toggle",
	});
};
// 根据终端编号查询终端体制状态信息
export const getModeStatus = (terminalSeq) => {
	return request.get({
		url: `/external/terminal/getModeStatus?terminalSeq=${terminalSeq}`,
	});
};
