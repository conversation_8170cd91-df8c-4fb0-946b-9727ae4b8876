import { requestDemo } from "@/service/request";
const request = new requestDemo({
  baseURL: AdminServerApi,
});

//播放或拖拽
// /wrpt/situation/dragOrPlay
export const dragOrPlay = (params) => {
  return request.post({
    url: `/situation/dragOrPlay`,
    data: params,
  });
};

// 需求执行情况个数统计
export const countRequirementExeCondition = (params) => {
  return request.get({
    url: `/count/countRequirementExeCondition`,
    params: params,
  });
};
// 任务执行情况个数统计
export const countTaskExeCondition = (params) => {
  return request.get({
    url: `/count/countTaskExeCondition`,
    params: params,
  });
};
// 目标-业务类型个数统计
export const countMbBusinessType = (params) => {
  return request.get({
    url: `/count/countMbBusinessType`,
    params: params,
  });
};



// 目标-业务类型个数统计
export const countDataAreaSchedule = (params) => {
  return request.get({
    url: `/count/countDataAreaSchedule`,
    params: params,
  });
};

//根据需求ID查询需求下所有目标以及设备数据
export const getSituationData = (params) => {
  return request.get({
    url: `/situation/getData`,
    params: params,
  });
};


//根据资源ID获取对应的其他数据--实时态势用 equipmentId requirementId
export const getDataByEquipment = (params) => {
  return request.get({
    url: `/occupancy/getDataByEquipment`,
    params: params,
  });
};

//根据目标ID获取对应的其他数据--实时态势用 targetRelationId
export const getDataByTarget = (params) => {
  return request.get({
    url: `/task/getDataByTarget`,
    params: params,
  });
};

// /wrpt/situation/simulaTargetState
//根据目标类型伪造数据
export const simulaTargetState = (params) => {
  return request.get({
    url: `/situation/simulaTargetState`,
    params: params,
  });
};
//实时态势推演统计目标及业务类型
export const getTargetBz = (params) => {
  return request.get({
    url: `/stats/real-time/targetBz`,
    params: params,
  });
};
//实时态势推演统计资源域类型
export const getRealTimeAreaType = (params) => {
  return request.get({
    url: `/stats/real-time/areaType`,
    params: params,
  });
};
//获取终端当前接入状态
export const getTerminalStatus = (params) => {
  return request.get({
    url: `external/terminal/getTerminalStatus?terId=${params}`,
  });
};

