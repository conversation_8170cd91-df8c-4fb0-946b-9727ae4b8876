/*
 * @Author: wlj <EMAIL>
 * @Date: 2024-07-09 11:06:41
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-10 09:43:44
 * @FilePath: \wrxtzhglrj\src\service\API\system\trackManager.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { requestDemo } from "@/service/request";
const request = new requestDemo({
  baseURL: AdminServerApi,
});
//获取航迹信息列表
export const getPresetList = (params) => {
  const {
	  page = 3,
	  size = 9999,
	  ...data
  } = params || {}
  return request.get({
    // url: `/preset?page=${params.page}&size=${params.size}`,
    url: `/preset?page=${page}&size=${size}`,
    params: data,
  });
};

//编辑航迹信息//新增航迹信息
export const auPresetList = (params) => {
  if (params.id) {
    return request.put({
      url: `/preset`,
      data: params,
    });
  } else {
    return request.post({
      url: `/preset`,
      data: params,
    });
  }
};
//删除航迹信息
export const delPresetById = (params) => {
  return request.delete({
    url: `/preset/${params.id}`,
  });
};
//根据航迹信息ID获取航迹点列表  presetId
export const getTrackList = (params) => {
  return request.get({
    url: `/preset/track`,
    params: params,
  });
};
//根据航迹信息ID获取航迹结束时间  presetId
export const getPresetTime = (params) => {
  return request.get({
    // url: `/preset?page=${params.page}&size=${params.size}`,
    url: `/preset/getTrackEndTime?presetId=${params.presetId }`,
    // params: params,
  });
};
//根据航迹信息ID新增航迹点列表
export const setTrackList = (params) => {
  return request.post({
    url: `/preset/track`,
    data: params,
  });
};
//根据航迹信息ID删除航迹点
export const delTrackById = (params) => {
  return request.delete({
    url: `/preset/track/${params.id}`,
  });
};


//更新任务关联航迹
export const updateTaskTrack = (params) => {
  return request.post({
    url: `/preset/updateTaskTrack`,
    params: params,
  });
};
