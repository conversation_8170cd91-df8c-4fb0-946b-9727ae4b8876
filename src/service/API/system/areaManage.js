import { requestDemo } from "@/service/request";
const request = new requestDemo({
  baseURL: AdminServerApi,
});

//所有字典名查询
export const getDictListType = (params) => {
  return request.get({
    url: `/dict/listType`,
    params: params,
  });
};
//字典值
export const getDictType = (params) => {
  return request.get({
    url: `/dict/getByType`,
    params: params,
  });
};

//获取资源域数据
export const getAreaList = (params) => {
  return request.get({
    url: "/dataAreas",
    params: params,
  });
};
//获取id获取资源域数据
export const getAreaInfo = (params) => {
  return request.get({
    url: "/dataAreas/" + params.id,
    // params: params,
  });
};
//获取id分类查询基础数据分类树{areaId}
export const getAreaGeneralTree = (params) => {
  return request.get({
    url: "/general/tree",
    params: params,
  });
};
// 资源域关联类型选择接口
export const getGeneralType = (params) => {
  return request.get({
    url: "/general/getType",
    params: params,
  });
};
// 根据ID和分类ID查询基础数据详情
export const getAreaGeneralDataDetail = (params) => {
  return request.get({
    url: "/general/dataDetail",
    params: params,
  });
};

// 根据类型查询基础字段(动态查询)
export const getAreaGeneralField = (params) => {
  return request.get({
    url: "/general/field",
    params: params,
  });
};
// 根据类型查询基础数据(动态查询)
export const getAreaGeneralData = (params) => {
  return request.get({
    url: "/general/data",
    params: params,
  });
};

// 新增或修改资源域
export const addEditArea = (params) => {
  if (params.id) {
    return request.put({
      url: `/dataAreas`,
      data: params,
    });
  } else {
    return request.post({
      url: "/dataAreas",
      data: params,
    });
  }
};
// 删除资源域
export const removeArea = (params) => {
  return request.delete({
    url: `/dataAreas/${params.id}`,
    params: params,
  });
};

// 资源设备类型数据统计信息
export const getStatsLeaf = (params) => {
  return request.get({
    url: `/stats/leaf`,
    params: params,
  });
};
// 类型数据统计信息
export const getStatsType = (params) => {
  return request.get({
    url: `/stats/type`,
    params: params,
  });
};
//占用状态统计
export const getOccupancyByArea = (params) => {
  return request.get({
    url: `/occupancy/getOccupancyByArea`,
    params: params,
  });
};
//统计分支信息
export const getAllLeaf = (params) => {
  return request.get({
    url: `stats/all/leaf`,
    params: params,
  });
};
// 新增或修改目标
export const addEditTarget = (params) => {
  if (params.dataId) {
    return request.put({
      url: `/general/updateData`,
      data: params,
    });
  } else {
    return request.post({
      url: "/general/saveData",
      data: params,
    });
  }
};
// 删除目标
export const removeTarget = (params) => {
  return request.delete({
    url: `/general/deleteData`,
    params: params,
  });
};

//资源域数量统计接口
export const getStatsArea = (params) => {
  return request.get({
    url: `/stats/area`,
    params: params,
  });
};

//资源域类型计接口
export const getStatsAreaType = (params) => {
  return request.get({
    url: `/stats/areaType`,
    params: params,
  });
};

//文件上传接口
export const uploadFile = (params) => {
  return request.post({
    url: "/file/upload",
    data: params,
  });
};

//文件查询接口
export const getFile = (params) => {
  return request.get({
    url: `/file/find/${params}`,
  });
};

//根据资源分类/或资源域分页查询设备数据
export const pageEquipmentByType = (params) => {
  return request.get({
    // url: `/general/pageEquipmentByType?page=${params.page}&size=${params.size}`,
    url: `/general/pageEquipmentByType`,
    params: params,
  });
};

//获取卫星轨道
export const calculateOrbit = (params) => {
  return request.get({
    url: `/satellite/calculateOrbit`,
    params: params,
  });
};

//开始卫星位置推演（控制websocket开始）
export const startWX = (params) => {
  return request.get({
    url: `/satellite/startNumericalForecast`,
    params: params,
  });
};

//结束卫星位置推演（控制websocket结束）
export const stopWX = (params) => {
  return request.get({
    url: `/satellite/stopNumericalForecast`,
    params: params,
  });
};

//获取根据时间和id获取卫星当前位置
export const getSatelliteCoordinate = (params) => {
  return request.post({
    url: `/satellite/getSatelliteCoordinate`,
    data: params,
  });
};

//获取小分类
export const queryLittleType = (params) => {
  return request.get({
    url: `/general/queryLittleType`,
    params: params,
  });
};

//资源设备统计数据分页查询 generaId
export const pageStatsEquipment = (params) => {
  return request.get({
    url: `/general/pageStatsEquipment`,
    params: params,
  });
};

//根据设备ID查询日志 equipmentId
export const logByEquipmentId = (params) => {
  return request.get({
    url: `/schedule/log/pageByEquipmentId`,
    params: params,
  });
};