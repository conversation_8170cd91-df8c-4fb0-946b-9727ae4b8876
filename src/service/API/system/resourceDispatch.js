/*
 * @Author: JRX <EMAIL>
 * @Date: 2024-06-17 14:45:29
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-23 17:49:30
 * @FilePath: \wrxtzhglrj\src\service\API\system\resourceDispatch.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { requestDemo } from "@/service/request";
const request = new requestDemo({
  baseURL: AdminServerApi,
});
//分页获取占用设备列表 taskId page size
export const getEquipmentByTask = (params) => {
  return request.get({
    url: "/occupancy/getEquipmentByTask",
    params: params,
  });
};

//提交资源调度结果
export const saveResource = (params) => {
  return request.post({
    url: "/schedule/confirmSchedule",
    data: params,
  });
};
//调用算法计算
export const scheduleAlgo = (params) => {
  return request.post({
    url: "/schedule/algo",
    data: params,
  });
};

//根据ID删除需求任务关联资源
export const delOccupancyById = (params) => {
  return request.delete({
    url: `/occupancy/${params.id}`,
  });
};

//查询所有资源域下面资源设备类型数据统计信息
export const statsAllLeaf = (params) => {
  return request.get({
    url: `/stats/all/leaf`,
    params: params,
  });
};

//域类型统计
export const statsAreaType = (params) => {
  return request.get({
    url: `/stats/areaType`,
    params: params,
  });
};

//空闲率统计
export const statsAreaIdle = (params) => {
  return request.get({
    url: `/stats/areaIdle`,
    params: params,
  });
};

//单个资源的覆盖率计算
export const calculateCover = (params) => {
  return request.post({
    url: `/schedule/calculateCover`,
    data: params,
  });
};

//根据id资源查询资源关联的任务信息
export const queryTaskByEquipment = (params) => {
  return request.get({
    url: `/task/queryTaskByEquipment`,
    params: params,
  });
};

//获取需求统计信息

export const getRequirementStat = (params) => {
  return request.get({
    url: `/stats/requirementStat`,
    params: params,
  });
};

//下发一级调度结果（不跨域）
export const sendResult = (params) => {
  return request.post({
    url: `/schedule/sendResult`,
    data: { ...params, clientId: params.clientId || window.clientId },
  });
};

//二次协商调度
export const renegotiate = (params) => {
  return request.post({
    url: `/schedule/renegotiate`,
    data: { ...params, clientId: window.clientId },
  });
};
