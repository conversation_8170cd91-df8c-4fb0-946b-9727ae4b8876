/*
 * @Author: JRX <EMAIL>
 * @Date: 2024-06-04 10:16:32
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-04 16:30:36
 * @FilePath: \wrxtzhglrj\src\service\API\system\login.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { requestDemo } from "@/service/request";
const request = new requestDemo({
  baseURL: AdminServerApi,
});
//登录请求
export const userLogin = (params) => {
  return request.post({
    url: "/login",
    data: params,
  });
};

//获取用户列表
export const getUserList = (params) => {
  return request.get({
    url: "/user",
    params: params,
  });
};
//所有字典名查询
export const getDictListType = (params) => {
  return request.get({
    url: `/dict/listType`,
    params: params,
  });
};
//字典值
export const getDictType = (params) => {
  return request.get({
    url: `/dict/getByType`,
    params: params,
  });
};
//所有字典字段
export const getAllDict = (params) => {
  return request.get({
    url: `/dict/allType`,
    params: params,
  });
};
//更新用户密码
// {
// 	"newPassword": "",
// 	"oldPassword": "",
// 	"userId": ""
// }
export const changePassword = (params) => {
  return request.post({
    url: "/user/changePassword",
    data: params,
  });
};

//获取用户信息
export const getUserInfo = (params) => {
  return request.get({
    url: `/user/${params.id}`,
    params: params,
  });
};
//更新用户信息（除密码）
export const putUserInfo = (params) => {
  return request.put({
    url: "/user",
    data: params,
  });
};

//新增用户信息
export const addUserInfo = (params) => {
  return request.post({
    url: "/user",
    data: params,
  });
};

//修改账号状态
export const updateUserStatus = (params) => {
  return request.put({
    url: `/user/changeStatus/${params.id}`,
    // data: params,
  });
};

//删除用户
export const delUser = (params) => {
  return request.delete({
    url: `/user/${params.id}`,
  });
};


//查询配置
export const getUserConfig = (params) => {
  return request.get({
    url: `/config`,
  });
};

//保存配置
export const saveUserConfig = (params) => {
  return request.post({
    url: `/config`,
    data: params
  });
};
