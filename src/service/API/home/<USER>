/*
 * @Author: nameZ
 * @data: Do not edit
 * @Description:
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-30 11:53:24
 * @FilePath: \qbmbtsweb\src\service\API\home\home.js
 */

import { requestDemo } from "@/service/request";
const request = new requestDemo({
  baseURL: AdminServerApi,
});
//分页获取需求列表
export const getRequirementList = (params) => {
  return request.get({
    url: "/requirement/list",
    params: params,
  });
};

//根据ID获取需求信息
export const getRequirementById = (params) => {
  return request.get({
    url: `/requirement/${params.id}`,
  });
};

//根据ID删除需求信息
export const delRequirementById = (params) => {
  return request.delete({
    url: `/requirement/${params.id}`,
  });
};

//新增/更新需求信息
export const subRequirement = (params) => {
  if (params.id) {
    return request.put({
      url: `/requirement`,
      data: params,
    });
  } else {
    return request.post({
      url: `/requirement`,
      data: params,
    });
  }
};

//新增或编辑任务（需求分解）
export const auTaskInfo = (params) => {
  if (params.id) {
    return request.put({
      url: `/task`,
      data: params,
    });
  } else {
    return request.post({
      url: `/task`,
      data: params,
    });
  }
};

//提交需求接口
export const submitTask = (params) => {
  return request.post({
    url: "/task/submitTask",
    params: params,
  });
};

//根据ID删除删除任务信息
export const delTaskById = (params) => {
  return request.delete({
    url: `/task/${params.id}`,
  });
};

//根据需求ID获取任务信息及相关联的目标信息
export const getTaskById = (params) => {
  return request.get({
    url: `/requirement/getTaskById`,
    params: params,
  });
};

//文件上传接口
export const uploadFile = (params) => {
  // file/importTrackFile
  return request.post({
    url: "/file/importTrackFile",
    data: params,
  });
};

//根据文件ID获取任务轨迹文件结果
export const getTrackFile = (params) => {
  return request.get({
    url: `/file/loadTrackFile`,
    params: params,
  });
};

//根据任务ID查询所有目标及航迹
export const getTaskTarget = (params) => {
  return request.get({
    url: `/task/target/listByTask`,
    params: params,
  });
};

//更新或者新增任务关联目标
export const auTaskTarget = (params) => {
  if (params.id) {
    return request.put({
      url: `task/target/updateTarget`,
      data: params,
    });
  } else {
    return request.post({
      url: `/task/target/storeTarget`,
      data: params,
    });
  }
};

//删除关联目标
export const removeTarget = (params) => {
  return request.delete({
    url: `task/target/removeTargetAndTrack`,
    params: params,
  });
};
// /wrpt/task/target/removeTrack

// 查询需求模板列表
export const getTemplateList = (params) => {
  return request.get({
    url: `/requirement/template`,
    params: params,
  });
};
//新增或更新需求模板
export const auTemplateInfo = (params) => {
  if (params.id) {
    return request.put({
      url: `/requirement/template`,
      data: params,
    });
  } else {
    return request.post({
      url: `/requirement/template`,
      data: params,
    });
  }
};
//根据ID获取模板信息
export const getTempInfo = (params) => {
  return request.get({
    url: `/requirement/template/${params.id}`,
  });
};

//根据ID删除模板
export const delTempById = (params) => {
  return request.delete({
    url: `/requirement/template/${params.id}`,
  });
};

//分页查询任务
export const getTaskList = (params) => {
  return request.post({
    url: `/task/list?page=${params.page}&size=${params.size}`,
    data: params,
  });
};

//根据任务ID查询任务
export const getTaskByTaskId = (params) => {
  return request.get({
    url: `/task/${params.id}`,
  });
};

//根据任务ID删除任务
export const delTaskByTaskId = (params) => {
  return request.delete({
    url: `/task/${params.id}`,
  });
};

//根据任务ID删除任务
export const upTaskInfo = (params) => {
  if (params.id) {
    return request.put({
      url: `/task`,
      data: params,
    });
  } else {
    return request.post({
      url: `/task`,
      data: params,
    });
  }
};

//查询周期任务的待调度列表，包含待调度和调度中的任务
export const getScheduleTask = (params) => {
  return request.get({
    url: `/task/queryScheduleTask`,
    params: params,
  });
};

// 查询当前周期任务定时轮询时间
export const queryPollingTime = (params) => {
  return request.get({
    url: `/task/queryPollingTime`,
    params: params,
  });
};

// 模板下载
export const getFileTemplate = (params) => {
  return request.get({
    getFile: true,
    url: `/requirement/getFileTemplate`,
  });
};

//获取域和目标数据
export const getAreaAndTargetInfo = (params) => {
  return request.get({
    url: `/dataAreas/getAreaAndTargetInfo`,
    params: params,
  });
};

//需求文件上传接口
export const uploadRequirementFile = (params) => {
  return request.post({
    url: "/uploadFile",
    data: params,
  });
};
// 解析上传的需求Excel
export const parseExcel = (params) => {
  return request.get({
    url: "/requirement/parseExcel",
    params: params,
  });
};
// 解析上传的需求new
export const receiveExternalRequirement = (data) => {
  return request.post({
    url: "requirement/receiveExternalRequirement",
    data,
  });
};

// 获取邮件消息文件列表
export const getRequirementFile = (params) => {
  return request.get({
    url: "/requirement/getRequirementFile",
    params: params,
  });
};
// 获取邮件消息文件列表new
export const getExternalRequirement = (params) => {
  return request.get({
    url: "/requirement/getExternalRequirement",
    params: params,
  });
};

// 忽略指定邮件
export const lgnoreExcel = (params) => {
  return request.get({
    url: "/requirement/deleteRequirementFile",
    params: params,
  });
};
//根据id获取设备详情
export const getDetailById = (params) => {
  return request.get({
    url: "/general/dataDetail",
    params: params,
  });
};

export const terminalConfirm= (params) => {
  return request.post({
    url: "/external/terminal/confirm",
    data: params,
  });
};
export const terminalCancel= (params) => {
  return request.post({
    url: "/external/terminal/cancel",
    data: params,
  });
};

//根据需求ID和任务ID数组获取任务列表
export const getTaskListByIds = (params) => {
  return request.post({
    url: "/task/listByIds",
    data: params,
  });
};

//根据需求ID和任务ID数组获取任务目标列表
export const getTaskTargetListByIds = (params) => {
  return request.post({
    url: "/task/target/listByTaskIds",
    data: params,
  });
};

