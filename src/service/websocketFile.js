/*
 * @Author: JRX <EMAIL>
 * @Date: 2024-07-22 13:19:14
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-23 17:48:50
 * @FilePath: \wrxtzhglrj\src\service\websocketFile.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// App.vue
var socket = null,
  timeout = 10 * 1000, // 45秒一次心跳
  timeoutObj = null, // 心跳心跳倒计时
  serverTimeoutObj = null, // 心跳倒计时
  timeoutnum = null, // 断开 重连倒计时
  lockReconnect = false, // 防止
  websocket = null;

 
export const initWebSocket = () => {
  window.clientId = parseInt(Math.random() * 1000000000) + new Date().getTime();
  // WebSocket与普通的请求所用协议有所不同，ws等同于http，wss等同于https
  websocket = new WebSocket(AdminServerWS + "/" + window.clientId);
  websocket.onopen = websocketonopen;
  websocket.onerror = websocketonerror;
  websocket.onmessage = setOnmessageMessage;
  websocket.onclose = websocketclose;
};

function start() {
  
  //清除延时器
  timeoutObj && clearTimeout(timeoutObj);
  serverTimeoutObj && clearTimeout(serverTimeoutObj);
  timeoutObj = setTimeout(() => {
    if (websocket && websocket.readyState == 1) {
      websocket.send("heartBath"); //发送消息，服务端返回信息，即表示连接良好，可以在socket的onmessage事件重置心跳机制函数
    } else {
      reconnect();
    }
    //定义一个延时器等待服务器响应，若超时，则关闭连接，重新请求server建立socket连接
    // serverTimeoutObj = setTimeout(() => {
    //   websocket.close();
    // }, timeout);
  }, timeout);
}

function reset() {
  // 重置心跳
  // 清除时间
  clearTimeout(timeoutObj);
  clearTimeout(serverTimeoutObj);
  // 重启心跳
  start();
}
function reconnect() {
  if (lockReconnect) return;
  lockReconnect = true;
  //没连接上会一直重连，设置延迟避免请求过多
  timeoutnum && clearTimeout(timeoutnum);
  timeoutnum = setTimeout(() => {
    initWebSocket();
    lockReconnect = false;
  }, 5000);
}

function setOnmessageMessage(event) {
  reset();
  // 自定义全局监听事件
  window.dispatchEvent(
    new CustomEvent("onmessageWS", {
      detail: {
        data: JSON.parse(event.data),
      },
    })
  );
 
  // //发现消息进入    开始处理前端触发逻辑
  // if (event.data === 'success' || event.data === 'heartBath') return
}

function websocketonopen() {
  //开启心跳
  start();
}

function websocketonerror(e) {
  console.log("WebSocket连接发生错误" + e);
}
function websocketclose(e) {
  websocket.close();
  clearTimeout(timeoutObj);
  clearTimeout(serverTimeoutObj);
  console.log("WebSocket连接关闭");
}
function websocketsend(messsage) {
  websocket.send(messsage);
}
function closeWebSocket() {
  // 关闭websocket
  websocket.close();
}
//--------------------注册事件参考-----------------------------------------
// window.addEventListener("onmessageWS", getSocketData);

// function getSocketData(res) {
//   if (res.detail.data === "success" || res.detail.data === "heartBath") return;
//   // ...业务处理
// }
