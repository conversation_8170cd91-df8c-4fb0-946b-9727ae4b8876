// 默认的主题颜色(白低黑字)
// @baseColor: var(--baseColor, rgba(25,121,255));
// @pageBgColor: var(--pageBgColor, rgba(255,255,255));
// @scrollBgColor: var(--scrollBgColor, rgba(0, 0, 0));
// @resultBgColor: var(--resultBgColor, rgba(255,192,203));
// @resultTextColor: var(--resultTextColor, rgba(0,0,0, 0.9));

@themeColor: var(--themeColor, rgba(14, 173, 253)); // 主题色
@bgColor: var(--bgColor, rgb(255, 255, 255)); // 背景色(蓝色)
@fontColor: var(--fontColor, rgba(217, 223, 232, 0.85)); // 文字颜色(白色)
@buttonColor: var(--buttonColor, rgba(33, 76, 119)); // 按钮色(蓝色)
@borderColor: var(--borderColor, rgba(255, 255, 255)); // 边框色
@popupTitleColor: var(--popupTitleColor, rgba(83, 163, 229)); // 边框色
@overlayBg: var(--overlayBg, rgba(2, 25, 66)); // overlayPoper

//默认主题treePopup图片地址
@tree01: var(--tree01, url('/images/bgBorder/tree-bg_01.png'));
@tree02: var(--tree02, url('/images/bgBorder/tree-bg_02.png'));
@tree03: var(--tree03, url('/images/bgBorder/tree-bg_03.png'));
//默认主题customWindow图片地址
@qb_03: var(--qb_03, url('/images/bgBorder/qb_03.png'));
@qb_04: var(--qb_04, url('/images/bgBorder/qb_04.png'));
@qb_05: var(--qb_05, url('/images/bgBorder/qb_05.png'));
@qb_06: var(--qb_06, url('/images/bgBorder/qb_06.png'));

@DK_06: var(--DK_06, url('/images/bgBorder/模型库DK_06.png'));
@DK_08: var(--DK_08, url('/images/bgBorder/模型库DK_08.png'));
@DK_09: var(--DK_09, url('/images/bgBorder/模型库DK_09.png'));
@DK_10: var(--DK_10, url('/images/bgBorder/模型库DK_10.png'));
@DK_11: var(--DK_11, url('/images/bgBorder/模型库DK_11.png'));
@DK_12: var(--DK_12, url('/images/bgBorder/模型库DK_12.png'));

@line_06: var(--line_06, url('/images/bgBorder/line_06.png'));
@line_07: var(--line_07, url('/images/bgBorder/line_07.png'));
@line_03: var(--line_03, url('/images/bgBorder/line_03.png'));

//默认主题customPopup图片地址
@small_03: var(--small_03, url('/images/bgBorder/forms-small_03.png'));
@small_04: var(--small_04, url('/images/bgBorder/forms-small_04.png'));
@small_05: var(--small_05, url('/images/bgBorder/forms-small_05.png'));
@small_06: var(--small_06, url('/images/bgBorder/forms-small_06.png'));
@small_08: var(--small_08, url('/images/bgBorder/forms-small_08.png'));
@small_09: var(--small_09, url('/images/bgBorder/forms-small_09.png'));
@small_10: var(--small_10, url('/images/bgBorder/forms-small_10.png'));
@small_11: var(--small_11, url('/images/bgBorder/forms-small_11.png'));
@small_12: var(--small_12, url('/images/bgBorder/forms-small_12.png'));
@small_13: var(--small_13, url('/images/bgBorder/forms-small_13.png'));
@small_14: var(--small_14, url('/images/bgBorder/forms-small_14.png'));

//默认主题headeList图片地址
@top_bg: var(--top_bg, url('/images/header/模型库-top-bg.png'));
@bg_mr: var(--bg_mr, url('/images/header/一级菜单按钮-bg-默认.png'));
@bg: var(--bg, url('/images/header/一级菜单按钮-bg.png'));
@app_bg: var(--app_bg, url('/images/new/icon/背景.png'));

@current_time: var(--current_time, url('/images/header/current-time.png'));
@data_reduction: var(--data_reduction, url('/images/header/data-reduction.png'));
@play_back: var(--play_back, url('/images/header/play-back.png'));
@system_settings: var(--system_settings, url('/images/header/system-settings.png'));
@popupTitleBg: var(--popupTitleBg, url('/images/bgBorder/装饰一级bt_03.png'));

@custom_header_close: var(--custom_header_close, url("/images/icon/close.png"));
@custom_header_delete: var(--custom_header_delete, url("/images/icon/sc.png"));
@custom_header_Edit: var(--custom_header_Edit, url("/images/icon/bianji.png"));
@custom_header_look: var(--custom_header_look, url("/images/icon/ck.png"));
@custom_header_wgz: var(--custom_header_wgz, url("/images/icon/wgz.png"));
@custom_header_zdgz: var(--custom_header_zdgz, url("/images/icon/zdgz.png"));

// 导出变量 （如果在 src/assets/style/theme/model文件中配置了，就直接导出里面的字体使用）
:export {
  name: "less";
  // baseColor: @baseColor;
  // pageBgColor: @pageBgColor;
  // scrollBgColor: @scrollBgColor;
  // resultBgColor: @resultBgColor;
  // resultTextColor: @resultTextColor;
  themeColor: @themeColor;
  bgColor: @bgColor;

  fontColor: @fontColor;
  buttonColor: @buttonColor;

  tree01: @tree01;
  tree02: @tree02;
  tree03: @tree03;
  qb_03: @qb_03;
  qb_04: @qb_04;
  qb_05: @qb_05;
  qb_06: @qb_06;
  DK_06: @DK_06;
  DK_08: @DK_08;
  DK_09: @DK_09;
  DK_10: @DK_10;
  DK_11: @DK_11;
  DK_12: @DK_12;
  line_06: @line_06;
  line_07: @line_07;
  line_03: @line_03;
  small_03: @small_03;
  small_04: @small_04;
  small_05: @small_05;
  small_06: @small_06;
  small_08: @small_08;
  small_09: @small_09;
  small_10: @small_10;
  small_11: @small_11;
  small_12: @small_12;
  small_13: @small_13;
  small_14: @small_14;
  top_bg: @top_bg;
  bg_mr: @bg_mr;
  bg: @bg;
  app_bg: @app_bg;
  current_time: @current_time;
  data_reduction: @data_reduction;
  play_back: @play_back;
  system_settings: @system_settings;
  popupTitleBg: @popupTitleBg;
  borderColor: @borderColor;
  custom_header_close: @custom_header_close;
  custom_header_delete: @custom_header_delete;
  custom_header_Edit: @custom_header_Edit;
  custom_header_look: @custom_header_look;
  custom_header_wgz: @custom_header_wgz;
  custom_header_zdgz: @custom_header_zdgz;

  overlayBg: @overlayBg;
}