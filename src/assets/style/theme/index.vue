<template>
  <!-- <div class="index"> -->
    <!-- <el-button class="btn" @click="themeDefault" :disabled="currentTheme === 'default'">默认</el-button>
    <el-button class="btn" @click="themeDark" :disabled="currentTheme === 'dark'">暗黑</el-button> -->
  <!-- </div> -->
</template>

<script>
import { setTheme } from "./theme"; // 引入切换主题方法
export default {
  data() {
    return {
      currentTheme:'default'
    };
  },
  methods: {
    // 默认主题方案（白底黑字）
    themeDefault() {
      document.documentElement.removeAttribute("theme-mode"); // 重置为浅色模式
      this.themeChange = true;
      this.currentTheme = 'default'
      setTheme("default"); // 初始化未默认主题
    },

    // 暗黑主题（黑底白字）
    themeDark() {
      document.documentElement.setAttribute("theme-mode", "dark"); // 重置为深色模式
      this.themeChange = false;
      this.currentTheme = 'dark'
      setTheme("dark");
    },
  },
  mounted: function () {
    this.themeDefault(); // 进入页面默认渲染默认主题方案
  },
};
</script>

<style lang="less" scoped>
@import "./style.less"; // 引入主题样式文件

.index {
    width: 110px;
    height: 100px;
    top: 9px;
    z-index: 9999;
    position: relative;
  .btn {
    width: 50px;
    height: 30px;
    background-color: rgba(@themeColor, 1);
    color: rgba(@fontColor,1);
    border: none;
    border-radius: 0;
    cursor: pointer;
    &:nth-child(2){
      margin-left: 5px;
    }
  }
}
</style>
