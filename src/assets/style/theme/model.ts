/*
 * @Author: JRX <EMAIL>
 * @Date: 2024-03-26 16:55:34
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-05-27 16:03:29
 * @FilePath: \wrxtzhglrj\src\assets\style\theme\model.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 一套默认主题以及一套暗黑主题
const bgBorderBaseUrl: string = '/images/bgBorder/'
const bgHeaderBaseUrl: string = '/images/Header/'
const bgIconBaseUrl: string = '/images/icon/'

const bgBorder1BaseUrl: string = '/images/bgBorder1/'
const bgHeader1BaseUrl: string = '/images/Header1/'
const bgIcon1BaseUrl: string = '/images/icon1/'

type ThemeColorInterface = {
    themeColor: string
    bgColor: string
    fontColor: string
    buttonColor: string
    borderColor: string
    overlayBg: string
}
/**
 *  @themeColor 主题色
    @bgColor 背景色
    @fontColor 字体色
    @buttonColor 按钮色
    @borderColor 边框色
    @overlayBg overlayPopper
 */
const getThemeColor = function ([themeColor, bgColor, fontColor, buttonColor, borderColor, overlayBg]: string[]): ThemeColorInterface {
    return { themeColor, bgColor, fontColor, buttonColor, borderColor, overlayBg }
}
/**
 *  @bgBorderBaseUrl Border
    @bgHeaderBaseUrl Header
    @bgIconBaseUrl Icon
 */
const getThemeImage = function ([bgBorderBaseUrl, bgHeaderBaseUrl, bgIconBaseUrl]: string[]): object {
    return {
        tree01: `url('${bgBorderBaseUrl}tree-bg_01.png')`,//树弹窗图片地址1
        tree02: `url('${bgBorderBaseUrl}tree-bg_02.png')`,//树弹窗图片地址2
        tree03: `url('${bgBorderBaseUrl}tree-bg_03.png')`,//树弹窗图片地址3
        //默认主题customWindow图片地址
        qb_03: `url('${bgBorderBaseUrl}qb_03.png')`,
        qb_04: `url('${bgBorderBaseUrl}qb_04.png')`,
        qb_05: `url('${bgBorderBaseUrl}qb_05.png')`,
        qb_06: `url('${bgBorderBaseUrl}qb_06.png')`,
        DK_06: `url('${bgBorderBaseUrl}模型库DK_06.png')`,
        DK_08: `url('${bgBorderBaseUrl}模型库DK_08.png')`,
        DK_09: `url('${bgBorderBaseUrl}模型库DK_09.png')`,
        DK_10: `url('${bgBorderBaseUrl}模型库DK_10.png')`,
        DK_11: `url('${bgBorderBaseUrl}模型库DK_11.png')`,
        DK_12: `url('${bgBorderBaseUrl}模型库DK_12.png')`,
        line_06: `url('${bgBorderBaseUrl}line_06.png')`,
        line_07: `url('${bgBorderBaseUrl}line_07.png')`,
        line_03: `url('${bgBorderBaseUrl}line_03.png')`,
        //默认主题customPopup图片地址
        small_03: `url('${bgBorderBaseUrl}forms-small_03.png')`,
        small_04: `url('${bgBorderBaseUrl}forms-small_04.png')`,
        small_05: `url('${bgBorderBaseUrl}forms-small_05.png')`,
        small_06: `url('${bgBorderBaseUrl}forms-small_06.png')`,
        small_08: `url('${bgBorderBaseUrl}forms-small_08.png')`,
        small_09: `url('${bgBorderBaseUrl}forms-small_09.png')`,
        small_10: `url('${bgBorderBaseUrl}forms-small_10.png')`,
        small_11: `url('${bgBorderBaseUrl}forms-small_11.png')`,
        small_12: `url('${bgBorderBaseUrl}forms-small_12.png')`,
        small_13: `url('${bgBorderBaseUrl}forms-small_13.png')`,
        small_14: `url('${bgBorderBaseUrl}forms-small_14.png')`,
        popupTitleBg: `url('${bgIconBaseUrl}需求管理_18.png')`,
        // app_bg:`url('${bgBorderBaseUrl}模型库-bg.jpg')`,
        app_bg: `url(${bgIconBaseUrl}背景.png)`,
        //默认主题headeList图片地址
        top_bg: `url('${bgHeaderBaseUrl}模型库-top-bg.png')`,
        bg_mr: `url('${bgHeaderBaseUrl}一级菜单按钮-bg-默认.png')`,
        bg: `url('${bgHeaderBaseUrl}一级菜单按钮-bg.png')`,
        current_time: `url('${bgHeaderBaseUrl}current-time.png')`,
        data_reduction: `url('${bgHeaderBaseUrl}data-reduction.png')`,
        play_back: `url('${bgHeaderBaseUrl}play-back.png')`,
        system_settings: `url('${bgHeaderBaseUrl}system-settings.png')`,

        custom_header_close: `url('${bgIconBaseUrl}close.png')`,
        custom_header_delete: `url('${bgIconBaseUrl}sc.png')`,
        custom_header_Edit: `url('${bgIconBaseUrl}bianji.png')`,
        custom_header_look: `url('${bgIconBaseUrl}ck.png')`,
        custom_header_wgz: `url('${bgIconBaseUrl}wgz.png')`,
        custom_header_zdgz: `url('${bgIconBaseUrl}zdgz.png')`,


    }
}
export const themes = {
    default: {
        ...getThemeColor([`55,160,234`, `0,0,0`, `0,0,0`, `0,0,0`, `0,0,0`, `55,160,234`]),
        ...getThemeImage([bgBorderBaseUrl, bgHeaderBaseUrl, bgIconBaseUrl]),
    },
    dark: {
        ...getThemeColor([`24,180,183`, `0,128,0`, `255,255,255`, `0,0,0`, `6,53,56`, `4,58,70`]),
        ...getThemeImage([bgBorder1BaseUrl, bgHeader1BaseUrl, bgIcon1BaseUrl])
    },

}
