<!--
 * @Author: name
 * @data: Do not edit
 * @@Description: 登录界面
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-25 17:11:31
 * @FilePath: \XJCRB0368_WEB\src\pages\login\Login.vue
-->
<template>
  <div class="login">
    <p class="loginText"><img width="100%" src="../../../public/images/login/无人登录/跨域_03.png" /></p>
    <div class="loginContent">
      <div class="loginTitle">欢迎登录！</div>
      <el-form ref="formLoginRef" :rules="rules" :model="formData" label-width="0">
        <el-form-item label="" required prop="username">
          <label class="loginLabel">
            <img src="../../../public/images/login/无人登录/登录2-2_11.png" />
            <el-input v-model="formData.username" placeholder="用户名" @keyup.enter="login"></el-input>
          </label>
        </el-form-item>
        <el-form-item label="" required prop="password">
          <label class="loginLabel">
            <img src="../../../public/images/login/无人登录/登录2-2_15.png" />
            <el-input v-model="formData.password" type="password" show-password clearable placeholder="密码"
              @keyup.enter="login"></el-input>
          </label>
        </el-form-item>
      </el-form>
      <el-button @click="login" class="loginButton" type="primary">登&nbsp;&nbsp;录</el-button>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import * as dataApi from "@/service/API/system/userManage.js";
import { dicHandle } from "@/utils/utils.js";

const router = useRouter();
const formData = reactive({
  password: "",
  username: "",
});
// let checked = ref(false);
// // 判断是否有本地账号
// if (window.localStorage.getItem("checked")) {
//   checked.value = Boolean(window.localStorage.getItem("checked"));
//   formData.userName = window.localStorage.getItem("username");
//   formData.userPwd = window.localStorage.getItem("password");
// }

// 验证规则
const rules = reactive({
  username: [{ required: true, message: "用户名不能为空" }],
  password: [{ required: true, message: "密码不能为空" }],
});

let formLoginRef = ref(null);
// 登录验证
const login = () => {
  formLoginRef.value.validate(async (valid) => {
    if (valid) {
      dataApi.userLogin({ password: formData.password, username: formData.username }).then((res) => {
        // console.log(res);
        if (res.headers.authorization) {
          ElMessage.success("登录成功");
          sessionStorage.setItem("userToken", res.headers.authorization);
          window.dictTypeList = {};
          dataApi.getAllDict().then((res) => {
            window.dictTypeList = {};
            if (res.data.code == 200) {
              let data = res.data.data;
              data.forEach((item) => {
                if (dictTypeList[item.dictType]) {
                  dictTypeList[item.dictType][item.dictValue] = item.dictName;
                } else {
                  dictTypeList[item.dictType] = {};
                  dictTypeList[item.dictType][item.dictValue] = item.dictName;
                }
                // dictTypeList[key] = [];
                // dicHandle(key);
              });
            }
          });
          // wxscoket.init();
          // getUserInfo()
          router.push("/home/<USER>"); // 跳转路由
          // router.push("/home/<USER>"); // 跳转路由
        } else {
          ElMessage.error("用户名或密码错误");
        }

      });
      return;
    }
  });
};
function getUserInfo() { }
</script>
<style lang="less" scoped>
.login {
  height: 100%;
  width: 100%;
  background-image: url("../../../public/images/login/无人登录/登录2-2-bg.png");
  background-position: center;
  background-size: cover;
  position: relative;
}

.loginText {
  position: absolute;
  width: 520px;
  top: 23%;
  left: calc(50% - 240px);
  font-size: 32px;
  color: #69c5ff;
  font-weight: bolder;
  margin-bottom: 20px;
  text-align: center;
}

.loginContent {
  .loginTitle {
    color: #00c1ea;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 40px
  }

  position: absolute;
  background: url("../../../public/images/login/无人登录/登录2-2_07.png") center no-repeat no-repeat;
  background-size: contain;
  width: 360px;
  padding: 20px;
  height: 250px;
  top: 50%;
  left: calc(50% - 180px);
  transform: translateY(-55%);
}

.loginLabel {
  position: relative;
  width: 100%;
  border: 0;
  border-bottom: 1px solid #4488bb78;

  :deep(.el-input__wrapper) {
    padding-left: 50px;
    box-shadow: none !important
  }
}

.loginLabel img {
  position: absolute;
  left: 13px;
  height: 24px;
  top: 6px;
}

.loginButton {
  position: relative;
  left: calc(50% - 180px);
  top: 30px;
  text-align: center;
  width: 360px;
  border-radius: 5px;
  
  height: 36px;
  font-size: 22px;
  font-weight: bold;
  // color: #0091e8 !important;
  background-color: #048dcd !important;
  border: 0;

  > :deep(span) {
    color: #dae1eb;
    font-size: 17px !important;
  }

  /* background-image: url("../../../public/images/login/anniu_03.png");
    background-size: 100% 100%; */
}

.loginButton>.login {
  :deep(.el-input__wrapper) {
    width: 240px;
  }

  :deep(.el-input__inner) {
    width: 240px;
    color: #fff;
    font-size: 16px;
  }

  :deep(input:-webkit-autofill) {
    -webkit-text-fill-color: #ffffff !important;
    background-color: transparent !important;
    background-image: none;
    caret-color: #ffffff;
    transition: background-color 500000s ease-in-out 0s;
  }

  :deep(.el-form-item) {
    border-bottom: 1px solid #5185be;
  }

  :deep(.el-form-item__error) {
    left: 40px;
  }
}
</style>
