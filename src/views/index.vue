<template>
  <!-- 头部菜单 -->
  <heade-list></heade-list>
  <!-- <router-view v-if="show"></router-view> -->
  <router-view></router-view>
  <!-- v-if="cesiumBoxFlag" -->
  <!-- <div style="width: 100vw; height: calc(100vh - 50px)" v-if="false">
    <cesium-box boxSize="true"></cesium-box>
  </div> -->
</template>
<script setup>
import { onMounted, ref, watchEffect, onBeforeMount } from "vue";

import HeadeList from "@/views/HeadeList.vue";
import CesiumBox from "@/components/evmap/CesiumBox.vue";
import { useRoute } from "vue-router";
// import { getAllDictData } from "@/service/API/home/<USER>";
import { WebSocketClass } from "@/service/websocket.js";
// import { getSjlxList } from "@/service/API/huifang/huifang";
// import { getSystemList } from "@/service/API/system/qbDistribution.js";

const route = useRoute();
let cesiumBoxFlag = ref(false); // 控制地图显示
let show = ref(false);
let sjlxList = ref([]);

let AllDict = new Map();
window.AllDict = AllDict;
onBeforeMount(async () => {
  //获取所有字典
  // await aa();
});
const aa = async () => {
  let res = await getAllDictData();
  // debugger;
  if (res.data.code == 200) {
    let data = res.data.data;
    AllDict = new Map();
    for (let i in data) {
      let Object = {};
      let newObject = {};
      data[i].forEach((item) => {
        Object[item.zdvaluebm] = item.zdvalue;
        newObject[item.zdvalue] = item.zdvaluebm;
      });
      AllDict.set(i, Object);
      AllDict.set(i + "_new", newObject);
    }
    let Objects = {};
    let newObjects = {};

    sjlxList.value.forEach((item) => {
      Objects[item.sjlx] = item.msgName;
      newObjects[item.msgName] = item.sjlx;
    });
    AllDict.set("sjlx", Objects);
    AllDict.set("sjlx_new", newObjects);

    console.log("全局字典", AllDict);
    window.AllDict = AllDict;
    show.value = true; //等待字典获取完成在加载组件
  }
  //获取所有字典
  // getAllDictData().then((res) => {
  //   if (res.data.code == 200) {
  //     let data = res.data.data;
  //     let AllDict = new Map();
  //     for (let i in data) {
  //       let Object = {};
  //       let newObject = {};
  //       data[i].forEach((item) => {
  //         Object[item.zdvaluebm] = item.zdvalue;
  //         newObject[item.zdvalue] = item.zdvaluebm;
  //       });
  //       AllDict.set(i, Object);
  //       AllDict.set(i + "_new", newObject);
  //     }
  //     let Objects = {};
  //     let newObjects = {};

  //     sjlxList.value.forEach((item) => {
  //       Objects[item.sjlx] = item.msgName;
  //       newObjects[item.msgName] = item.sjlx;
  //     });
  //     AllDict.set("sjlx", Objects);
  //     AllDict.set("sjlx_new", newObjects);

  //     console.log("全局字典", AllDict);
  //     window.AllDict = AllDict;
  //     show.value = true; //等待字典获取完成在加载组件
  //   }
  // });
};
onMounted(() => {
  //获取数据类型列表
  // getSjlxList().then((res) => {
  //   if (res.data.code == 200) {
  //     sjlxList.value = res.data.data;
  //   } else {
  //   }
  // });
  //获取目标颜色
  // getSystemList({
  //   pageNo: 1,
  //   pageSize: 100,
  // }).then((res) => {
  //   if (res.data.code == 200) {
  //     res.data.data.records.forEach((item) => {
  //       if (item.paramKey == "qb_tstpcolor") {
  //         if (item.paramValue) {
  //           localStorage.setItem("color", item.paramValue);
  //         }
  //       }
  //     });
  //   }
  // });
  //获取所有字典
  // getAllDictData().then((res) => {
  //   if (res.data.code == 200) {
  //     let data = res.data.data;
  //     let AllDict = new Map();
  //     for (let i in data) {
  //       let Object = {};
  //       let newObject = {};
  //       data[i].forEach((item) => {
  //         Object[item.zdvaluebm] = item.zdvalue;
  //         newObject[item.zdvalue] = item.zdvaluebm;
  //       });
  //       AllDict.set(i, Object);
  //       AllDict.set(i + "_new", newObject);
  //     }
  //     let Objects = {};
  //     let newObjects = {};
  //     sjlxList.value.forEach((item) => {
  //       Objects[item.sjlx] = item.msgName;
  //       newObjects[item.msgName] = item.sjlx;
  //     });
  //     AllDict.set("sjlx", Objects);
  //     AllDict.set("sjlx_new", newObjects);
  //     console.log("全局字典", AllDict);
  //     window.AllDict = AllDict;
  //     show.value = true; //等待字典获取完成在加载组件
  //   }
  // });
  // - 创建全局的WebSocket
  // if (window.myWS) {
  // } else {
  //   window.myWS = new WebSocketClass(`${AdminServerWS}/qbxxksh/ws`);
  //   window.myWS.initWS();
  // }
  // window.myWS.dealMsg((data) => {}, "info"); //全局使用WebSocket
});
// 监听一级路由
watchEffect(() => {
  // cesiumBoxFlag.value = route.path == "/home/<USER>" || route.path == "/home/<USER>" || route.path == "/home/<USER>"
  cesiumBoxFlag.value = ["/home/<USER>", "/home/<USER>", "/home/<USER>", "/home/<USER>", "/home/<USER>"].includes(
    route.path
  );
});
</script>
<style scoped>
.background-1 {
  /* background: url("../assets/imgs/XJ-bg1.png"); */
  /* 背景图不平铺 */
  background-repeat: no-repeat;
  /* 让背景图基于容器大小伸缩 */
  background-size: 100% 100%;
  height: 100%;
  position: relative;
}
</style>
