<template>
    <el-dialog v-model="state.show" :title="'编辑-' + state.form.targetName" width="50%" center
        :close-on-click-modal="false">
        <el-form ref="formRef" :model="state.form" label-width="120px">
            <el-form-item label="目标名称：">
                {{ state.form.targetName }}
            </el-form-item>
            <el-form-item label="航迹：">
                <el-select
					v-model="state.form.trackPresetId"
					style="width: 70%"
					placeholder="请选择"
					clearable
				   filterable
                    @change="trackChange"
				>
                    <el-option v-for="item in state.trackManagerList" :key="item.id" :label="item.name"
                        :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="航迹时间：">
                <el-date-picker type="datetime" style="width: 35%" v-model="state.form.trackStartTime"
                    placeholder="请选择时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                    @change="calcEndTime(state.form)" />
                <span style="margin: 0 10px;">{{ " - " }}</span>
                <el-date-picker type="datetime" disabled style="width: 35%" v-model="state.form.trackEndTime"
                    placeholder="" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item label="">
                <el-table :data="state.form.tasks" class="custom-table" style="height: 40vh;">
                    <el-table-column type="index" width="50" align="center" />
                    <el-table-column label="测控任务类型" align="center">
                        <template #default="{ row }">
                            <el-select v-model="row.taskType" placeholder="" clearable multiple style="width: 90%">
                                <el-option v-for="(value, key) in dictTypeList['businessType']" :key="key"
                                    :label="value" :value="Number(key)"></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="开始时间" align="center">
                        <template #default="{ row }">
                            <el-date-picker type="datetime" style="width: 90%" v-model="row.startTime"
                                placeholder="请选择时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
                        </template>
                    </el-table-column>
                    <el-table-column label="结束时间" align="center">
                        <template #default="{ row }">
                            <el-date-picker type="datetime" style="width: 90%" v-model="row.endTime" placeholder="请选择时间"
                                format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
                        </template>
                    </el-table-column>
                    <el-table-column label="重复" width="150" align="center">
                        <template #default="{ row }">
                            <el-select v-model="row.repeatType" placeholder="" clearable style="width: 90%">
                                <el-option v-for="(value, key) in dictTypeList['repetitionType']" :key="key"
                                    :label="value" :value="Number(key)"></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center">
                        <template #header="scope">
                            <el-button @click="addBusinessRow()">新增</el-button>
                        </template>
                        <template #default="scope">
                            <el-button @click="delBusinessRow(scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="close">关闭</el-button>
                <el-button @click="editTarget">确定</el-button>
            </span>
        </template>
    </el-dialog>

</template>

<script lang='ts' setup>
import { defineComponent, nextTick, onMounted, reactive, ref, toRefs, watch } from 'vue'
import * as trackApi from "@/service/API/system/trackManager.js";
import { ElMessage } from 'element-plus';
const timeSpan = window.timeSpan;
const dictValue = window.dictValue;
const dictTypeList = window.dictTypeList;
const props = defineProps({
    form: {
        tasks: []
    },
    requirementForm: {},
    show: false
})
const emits = defineEmits(["changeEmits", "close"])
const state = reactive({
    form: {
        tasks: []
    },//信息表单
    trackManagerList: [], //航迹列表管理
    requirementForm: {},//需求信息
    show: false,//展示
})
// watch(() => props.form, (val) => {
//     state.form = JSON.parse(JSON.stringify(val))
// })
// watch(() => props.requirementForm, (val) => {
//     state.requirementForm = JSON.parse(JSON.stringify(val))
// })
watch(() => props.show, (val) => {
    state.show = val
    state.form = JSON.parse(JSON.stringify(props.form))
    state.requirementForm = JSON.parse(JSON.stringify(props.requirementForm))
})

onMounted(() => {
    getTrackManagerList();
    state.form = props.form
    state.requirementForm = props.requirementForm

})
//获取航迹管理列表
function getTrackManagerList() {
    trackApi.getPresetList().then((res) => {
        if (res.data.code == 200) {
            state.trackManagerList = res.data.data.records;
        } else {
            ElMessage.error(res.data.message);
        }
    });
}
//航迹切换时，如果有开始时间，则计算结束时间
function trackChange() {
    if (state.form.trackStartTime) {
        calcEndTime(state.form)
    }
}
//计算航迹结束时间
function calcEndTime(target) {
    if (!target.trackPresetId) {
        ElMessage.error("航迹未选择")
        return
    }
    if (!state.requirementForm.startTime) {
        ElMessage.error("需求开始时间未选择")
        return
    }
    let track = state.trackManagerList.find(item => {
        return item.id == target.trackPresetId
    })
    let endTimeSpan;
    trackApi.getPresetTime({ presetId: target.trackPresetId }).then((res) => {
        if (res.data.code == 200) {
            endTimeSpan = res.data.data*1000;
            console.log('endTimeSpan',endTimeSpan);
                state.form.trackEndTime = moment(new Date(target.trackStartTime).getTime() + endTimeSpan).format("YYYY-MM-DD HH:mm:ss")
        } else {
            ElMessage.error(res.data.message);
        }
    });

    // state.form.trackEndTime = moment(new Date(moment(state.requirementForm.startTime).format("YYYY-MM-DD") + " " + target.trackStartTime).getTime() + endTimeSpan).format("HH:mm:ss")

    // console.log("track", track);
}

//删除一行业务数据
function delBusinessRow(businessIndex) {
    state.form.tasks.splice(businessIndex, 1);
}
//新增一行业务数据
function addBusinessRow() {
    state.form.tasks.push({
        taskType: [],
        startTime: state.form.trackStartTime || null,
        endTime: state.form.trackEndTime || null,
        repeatType: 1,
    });
}
function editTarget() {
    let { tasks, trackStartTime, trackEndTime } = state.form
    if (trackStartTime == 0) {
        ElMessage.error("请选择目标开始时间")
        return
    }
    if (tasks.length == 0) {
        ElMessage.error("请至少添加一条测控业务数据")
        return
    }
    trackStartTime = trackStartTime;
    trackEndTime = trackEndTime;
    const flag = tasks.some((ele) => {
        if (moment(ele.startTime).isSameOrAfter(trackStartTime) &&
            moment(trackEndTime).isSameOrAfter(ele.endTime)
            // 交给后端判断了，所以这里去掉了，只做航迹时间判断
            // &&
            // moment(startTime).isSameOrAfter(state.requirementForm.startTime) &&
            // moment(state.requirementForm.endTime).isSameOrAfter(endTime)
        ) {
            return false
        }
        return true
    })
    if (flag) {
        ElMessage.error('任务开始或结束时间超出航迹时间')
        return
    }
    emits("changeEmits", state.form)
}
function close() {
    emits("close")
}
</script>

<style lang='less' scoped>
:deep(.el-form-item) {
    margin-bottom: 18px
}
</style>
