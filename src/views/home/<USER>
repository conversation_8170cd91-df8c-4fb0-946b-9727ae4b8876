<!--
 * @@Description: 需求创建
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-17 16:05:29
 * @FilePath: \qbmbtsweb\src\views\home\HomePage.vue
-->

<template>
  <CesiumBox></CesiumBox>

  <!-- 左侧框体列表 w-->
  <div class="requireList rightList">
    <customPopup left="1%" top="8%" width="25%" height="90%">
      <template #header>
        <span class="list_icon"></span>
        <span class="title-font">需求详情</span>
        <div style="position: absolute; right: 2%">
          <el-button plain @click="submitTask">提交需求</el-button>

          <el-button plain @click="cancel">返回上一级</el-button>
        </div>
      </template>
      <template #content>
        <div class="requirementInfo">
          <div style="height: 90%" class="require">
            <el-row>
              <el-col :span="12">
                <el-form-item label="需求名称：">
                  <span>{{ state.requirementInfo.requirementName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="需求类型：">
                  <!-- <span v-else>{{ infoText("XQLX", options, demandInfo.XQLX) }}</span> -->
                  <span>{{ dictValue("requirementType", state.requirementInfo.requirementType) }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="重要程度：">
                  <span>{{ dictValue("importance", state.requirementInfo.importance) }}</span>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="提交状态：">
                  <span>{{ dictValue("submitStatus", state.requirementInfo.submitStatus) }}</span>
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="需求状态：">
                  <span>{{ dictValue("requirementStatus", state.requirementInfo.status) }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开始时间：">
                  <span>{{ timeSpan(state.requirementInfo.startTime) }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="需求描述：">
                  <span v-html="state.requirementInfo.requirementComment"></span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="demandHeader">
          <!-- <div class="demandHeader"> -->
          <div class="header-title">
            <span></span>
            <span class="title-font">子任务列表</span>
            <div style="position: absolute; right: 2%">
              <!-- <el-button plain>自动分解(todo)</el-button> -->
              <el-button plain @click="Chose">新增任务</el-button>
            </div>
          </div>
        </div>
        <div class="taskList">
          <customTable
            ref="taskListRef"
            :tableData="state.taskList"
            :tableColumn="taskTableColumn"
            paginationShow="false"
            @nodeClickEmit="handleTaskTableClick"
            @handleEditEmit="handleTaskEdit"
            @handleDeleteEmit="taskDeleteClick"
          ></customTable>
        </div>

        <!-- 已选目标列表 -->
        <!-- v-if="active > 0" -->
        <div class="demandHeader">
          <!-- <div class="demandHeader"> -->
          <div class="header-title">
            <span></span>
            <span class="title-font">已选目标列表</span>
            <div style="position: absolute; right: 2%">
              <el-button plain @click="addTargetClick">添加目标</el-button>
            </div>
          </div>
        </div>
        <div class="targetList">
          <!-- @nodeClickEmit="handleTableClick" -->
          <customTable
            :tableData="state.targetList"
            :tableColumn="targetTableColumn"
            paginationShow="false"
            @nodeClickEmit="targetRowClick"
            @handleDeleteEmit="targetDeleteClick"
          ></customTable>
        </div>
      </template>
    </customPopup>
  </div>
  <!-- 右侧目标列表 -->
  <customPopup right="1%" top="8%" width="25%" height="90%" v-if="state.targetListShow">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">目标列表</span>
      <div style="position: absolute; right: 2%">
        <el-button plain @click="closeEditTarget">关闭</el-button>
      </div>
    </template>
    <template #content>
      <el-tabs type="border-card" @tab-change="tabChange" v-model="areaState.activeName" style="height: 100%">
        <el-tab-pane :label="area.areaName" v-for="(area, index) in areaState.areaList" :key="index" :name="index">
          <template v-for="(value, key) in areaState.treeObj" :key="key">
            <div style="display: flex; align-items: center; justify-content: flex-start">
              <div class="small_list_icon"></div>
              <span class="user_subTitle">{{ value.label }}</span>
            </div>
            <div
              v-for="(classify, classifyIndex) in value.children"
              :key="'classify' + classifyIndex"
              style="margin-bottom: 25px"
            >
              <el-divider content-position="left">{{ classify.tableComment }}</el-divider>
              <span
                v-for="(target, targetIndex) in classify.targetInfo"
                :key="targetIndex"
                class="nameIcon"
                @click="addTarget(target, classify)"
              >
                <div class="dj_icon" v-if="value.dataType == 1"></div>
                <div class="tj_icon" v-else-if="value.dataType == 2"></div>
                <div class="kj_icon" v-else-if="value.dataType == 3"></div>
                <div class="kj_icon" v-else-if="value.dataType == 4"></div>
                <div class="dj_icon" v-else-if="value.dataType == 6"></div>
                {{ target.name }}
              </span>
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </template>
  </customPopup>
  <customPopup right="1%" top="8%" width="25%" height="90%" v-if="state.targetTrackShow">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">航迹设置</span>
      <div style="position: absolute; right: 2%">
        <el-button plain @click="state.targetTrackShow = false">关闭</el-button>
      </div>
      <div style="position: absolute; right: calc(2% + 80px)" v-if="!state.currentTarget.id">
        <el-button plain @click="addTargetClick">返回</el-button>
      </div>
    </template>
    <template #content>
      <el-form ref="trackFormRef" :model="state.currentTarget" :rules="trackRules" label-width="80px">
        <el-form-item label="目标名称" prop="">
          {{ state.currentTarget.name }}
        </el-form-item>
        <el-form-item label="航迹颜色" prop="trackColor">
          <el-color-picker v-model="state.currentTarget.trackColor" />
        </el-form-item>
        <el-form-item label="航迹宽度" prop="trackWidth">
          <el-input-number
            v-model="state.currentTarget.trackWidth"
            :max="10"
            :step="1"
            step-strictly
            :controls="false"
          />
        </el-form-item>
        <el-form-item label="航行速度" prop="trackSpeed">
          <el-input-number
            v-model="state.currentTarget.trackSpeed"
            @change="trackSpeedChange"
            :min="0"
            :step="0.01"
            step-strictly
            :controls="false"
          />
          &nbsp;&nbsp;&nbsp; KM/h
        </el-form-item>
        <el-form-item label="航迹绘制" prop="">
          <el-button type="primary" @click="trackDraw">手动绘制</el-button>
          <el-button type="primary" @click="fileDraw">航迹文件</el-button>
          <el-button type="primary" @click="trackClick">航迹选择</el-button>
        </el-form-item>
        <el-form-item label="航迹列表" prop="targetTrack"></el-form-item>
        <div style="height: 40vh; width: 100%">
          <el-table :data="state.currentTarget.targetTrack" class="custom-table" height="100%">
            <el-table-column prop="" label=" " width="40" align="center">
              <template v-slot="{ row, $index }">
                <span>{{ $index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="longitude" label="经度" width="100" align="center">
              <template v-slot="{ row }">
                <el-input-number
                  v-model="row.longitude"
                  disabled
                  :controls="false"
                  style="width: 100%"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column prop="latitude" label="纬度" width="100" align="center">
              <template v-slot="{ row }">
                <el-input-number
                  v-model="row.latitude"
                  disabled
                  :controls="false"
                  style="width: 100%"
                ></el-input-number>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="altitude" label="高度" width="100" align="center">
              <template v-slot="{ row }">
                <el-input-number v-model="row.altitude" :controls="false" style="width: 100%"></el-input-number>
              </template>
            </el-table-column> -->

            <el-table-column prop="timeVal" label="时间" align="center">
              <template v-slot="{ row, $index }">
                <el-date-picker
                  style="width: 100%"
                  v-model="row.timeVal"
                  @change="trackTimeChange(row, $index)"
                  type="datetime"
                  placeholder="选择航迹时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  :clearable="false"
                  :disabled-date="
                    (date) => {
                      return disabledDate(date, row, $index);
                    }
                  "
                  value-format="x"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="btn_center">
          <el-button type="primary" @click="saveTarget" style="width: 120px">
            确&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;认
          </el-button>
        </div>
      </el-form>
    </template>
  </customPopup>
  <div v-show="state.trackShow">
    <customPopup left="37.5%" top="18%" width="25%" height="200px">
      <template #header>
        <span class="list_icon"></span>
        <span class="title-font">选择航迹</span>
      </template>
      <template #content>
        <el-select v-model="state.chooseTrack" placeholder="" clearable>
          <el-option v-for="item in state.trackList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <div style="text-align: center; width: 100%">
          <el-button @click="chooseTrack">确认</el-button>
        </div>
      </template>
    </customPopup>
  </div>
  <!-- 任务编辑/新增框 isAddPlan-->
  <div v-show="isAddPlan">
    <!-- <customPopup right="20%" top="25%" width="18%" height="30%"> -->
    <customPopup left="37.5%" top="8%" width="25%" height="40%">
      <template #header>
        <span class="list_icon"></span>
        <span class="title-font">{{ state.addPlanForm.id ? "需求分解-编辑任务" : "需求分解-新增任务" }}</span>
      </template>
      <template #content>
        <div id="demand_Box">
          <el-form ref="addForm" :model="state.addPlanForm" :rules="addRules" label-width="100px">
            <el-form-item label="任务名称：" prop="taskName">
              <el-input v-model="state.addPlanForm.taskName"></el-input>
            </el-form-item>
            <el-form-item label="开始时间：" prop="startTime">
              <el-date-picker
                v-model="state.addPlanForm.startTime"
                type="datetime"
                placeholder="请选择开始时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="结束时间：" prop="endTime">
              <el-date-picker
                v-model="state.addPlanForm.endTime"
                type="datetime"
                :placeholder="state.addPlanForm.endTime ? state.addPlanForm.endTime : '请选择结束时间'"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="任务描述" prop="taskComment">
              <el-input type="textarea" rows="4" v-model="state.addPlanForm.taskComment"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div style="display: flex; justify-content: center; margin-top: 5%">
          <el-button style="background-color: rgb(18, 132, 208)" @click="saveAddPlan" :loading="state.loading">
            保存
          </el-button>
          <el-button
            style="background-color: rgb(18, 132, 208); margin-left: 10px"
            @click="canle"
            :loading="state.loading"
          >
            取消
          </el-button>
        </div>
      </template>
    </customPopup>
  </div>

  <div>
    <el-dialog title="选择航迹文件" width="500px" v-model="state.fileTrackShow" @close="state.fileTrackShow = false">
      <div style="width: 100%; text-align: center">
        <span>航迹文件列表：</span>
        <span>
          <el-select v-model="state.trackFileId" placeholder="" clearable style="width: 220px">
            <el-option
              v-for="item in state.trackFileList"
              :key="item.id"
              :label="item.fileName"
              :value="item.id"
            ></el-option>
          </el-select>
        </span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            @click="
              state.fileTrackShow = false;
              state.trackFileId = null;
            "
          >
            关 闭
          </el-button>
          <el-button type="primary" @click="fileTrack">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import CesiumBox from "@/components/evmap/CesiumBox.vue";
import customPopup from "@/components/customPopup.vue";
import customPopup1 from "@/components/customPopup1.vue";
import customTable from "@/components/customTable.vue";
import { reactive, ref, onMounted, onBeforeMount, onBeforeUnmount, onUnmounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import * as dataApi from "@/service/API/home/<USER>";
import * as areaApi from "@/service/API/system/areaManage.js";
import * as trackApi from "@/service/API/system/trackManager.js";
// import * as mapTool from "@/utils/mapTool.js";

import { ElMessage } from "element-plus";
const dictValue = window.dictValue;
const taskTableColumn = [
  { label: "任务名称", prop: "taskName", type: "String", width: "" },
  { label: "开始时间", prop: "startTime", type: "Time", width: "" },
  { label: "结束时间", prop: "endTime", type: "Time", width: "" },
  {
    prop: "",
    label: "操作",
    type: "operation",
    width: "",
    operationTypeName: { handleEdit: true, handleDelete: true },
  },
];
const targetTableColumn = [
  { label: "目标名称", prop: "name", type: "String", width: "" },
  { label: "目标类型", prop: "dataTypeValue", type: "String", width: "" },
  { label: "配置状态", prop: "setType", type: "String", width: "" },
  {
    prop: "",
    label: "操作",
    type: "operation",
    width: "",
    operationTypeName: { handleDelete: true },
  },
];
const timeSpan = window.timeSpan;
const state = reactive({
  requirementId: null, //需求ID
  taskList: [], //任务列表
  task: {}, //当前任务信息
  requirementInfo: {}, //需求信息
  targetList: [], //已选择目标列表
  targetListShow: false, //目标选择界面
  targetTrackShow: false, //航迹编辑界面
  addPlanForm: {},
  loading: false,
  currentTarget: {},
  newTargetInfo: {},
  trackFileList: [], //需求对应的轨迹文件列表
  fileTrackShow: false, //航迹文件弹窗控制
  trackFileId: null, //选中的航迹文件
  chooseTrack: null, //选中的航迹
  trackShow: false, //展示航迹选择窗体
  trackList: [], //航迹列表
});
const areaState = reactive({
  areaList: [], //域列表
  areaId: null, //当前操作的域
  activeName: null,
});

const isShow = ref(false);
const isAddPlan = ref(false);
const addPlanTitle = ref("");
let planTitle = ref();
const route = useRoute();
let addForm = ref(null);
const trackFormRef = ref(null);
const taskListRef = ref(null);
// 新增表单验证规则
const addRules = reactive({
  taskName: [{ required: true, message: "任务名称不能为空!", trigger: "blur" }],
  startTime: [{ required: true, message: "开始时间不能为空!", trigger: "blur" }],
  endTime: [
    { required: true, message: "结束时间不能为空!", trigger: "blur" },
    // { pattern: /^[a-z]+$/, message: "字典编码必须是小写字母!", trigger: "blur" },
  ],
});
const trackRules = reactive({
  trackColor: [{ required: true, message: "航迹颜色不能为空", trigger: "change" }],
  trackWidth: [{ required: true, type: "number", message: "航迹宽度不能为空", trigger: "blur" }],
  trackSpeed: [
    { required: true, type: "number", message: "速度不能为空!", trigger: "blur" },
    // { min: 0.01, message: "速度不能小于0.01", trigger: "blur" },
  ],
});

const defaultProps = {
  children: "children",
  label: "label",
};
const active = ref(0);
onBeforeMount(() => {
  // if (route.query.planTitle) {
  planTitle.value = route.query.planTitle;
  // if (planTitle.value == "需求分解") {
  state.requirementId = route.query.requirementId;
  if (!state.requirementId) {
    ElMessage.error("需求ID不存在,请从需求管理界面进入");
    return;
  }
  //获取需求信息
  getRequirementById();
  // 获取域
  getAreaList();
  getTrackList();
});
watch(
  () => state.targetTrackShow,
  (val) => {
    if (!val) {
      clearNewRoute();
    }
  }
);
// 获取枚举值对应的文字(目前value用的是label，但接后端后会用id或者value)
const infoText = (filed, list, val) => {
  var text = list.find((item) => {
    return item.label == val;
  })?.label;
  return text || "";
};

// 任务列表表格操作
function handleTaskTableClick(row) {
  state.task = row;
  taskListRef.value.setCurrentRow(row);
  window.EVGISMAP("removeAllEntityByGroup", { group: "DRAWLAYERGROUP" });
  dataApi.getTaskTarget({ taskId: row.id }).then((res) => {
    if (res.data.code == 200) {
      state.targetList = res.data.data;
      state.targetList.forEach((item) => {
        drawHjLine(item);
        if (item.targetTrack && item.targetTrack.length > 0) {
          item.setType = "已配置";
        } else {
          item.setType = "未配置";
        }
      });
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
//绘制任务下的目标航迹
function drawHjLine(target) {
  let list = target.targetTrack;
  window.EVGISMAP("removeAllEntityByGroup", { group: "DRAWLAYERGROUP" });
  if (!list || list.length == 0) {
    return;
  }
  setTimeout(() => {
    let posList = [];
    list.forEach((item) => {
      // console.log(item);
      posList.push([item.longitude, item.latitude]);
    });

    if (posList.length > 1) {
      window.EVGISMAP("drawPolylineByAttr", {
        id: target.id + "_line",
        group: "DRAWLAYERGROUP",
        position: posList,
        // name: target.name,
        outlineColor: target.trackColor || "red",
        outlineWidth: target.trackWidth || "2",
      });
    }
    window.EVGISMAP("drawPointByAttr", {
      id: target.id + "_point",
      img: window.dataTypeImg[target.dataType - 1],
      group: "DRAWLAYERGROUP",
      position: [list[0].longitude, list[0].latitude, list[0].altitude],
      name: target.name,
      color: "#59E870",
      outlineColor: "green",
      outlineWidth: target.trackWidth,
      textColor: target.trackColor,
      textOutlineColor: "black",
      textScale: 1,
      scale: 1,
      direction: 0,
    });
  }, 500);
}
//删除任务
function taskDeleteClick(row) {
  dataApi.delTaskById(row).then((res) => {
    if (res.data.code == 200) {
      ElMessage.success("任务删除成功");
      getTaskById();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
// 编辑任务
const handleTaskEdit = (row) => {
  isAddPlan.value = true;
  state.addPlanForm = { ...row };
};

// 弹出需求分解模态框
const Chose = () => {
  isAddPlan.value = true;
  state.targetTrackShow = false;
  state.targetListShow = false;
  state.addPlanForm = {};
};

const router = useRouter();
const cancel = () => {
  isShow.value = false;
  router.push({
    path: "/home/<USER>",
    replace: true,
  });
};

// 需求分解模态框保存
const saveAddPlan = () => {
  addForm.value.validate((valid) => {
    if (valid) {
      saveTask("info");
    }
  });
};
function saveTask(type) {
  state.loading = true;
  // 任务基础信息保存（无目标）
  if (type == "info") {
    state.addPlanForm.targets = [];
  }
  state.addPlanForm.requirementId = state.requirementId;
  dataApi.auTaskInfo(state.addPlanForm).then((res) => {
    if (res.data.code == 200) {
      isAddPlan.value = false;
      state.loading = false;
      if (state.addPlanForm.id) {
        ElMessage.success("编辑任务成功");
        getTaskById(state.addPlanForm);
      } else {
        ElMessage.success("新增任务成功");
        getTaskById(res.data.data);
      }
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

const canle = () => {
  isShow.value = false;
  isAddPlan.value = false;
  active.value = 0;
};
let id = 1000;
const append = (data) => {
  const newChild = { id: id++, label: "testtest", children: [] };
  if (!data.children) {
    data.children = [];
  }
  data.children.push(newChild);
  dataSource.value = [...dataSource.value];
};

const remove = (node, data) => {
  const parent = node.parent;
  const children = parent.data.children || parent.data;
  const index = children.findIndex((d) => d.id === data.id);
  children.splice(index, 1);
  dataSource.value = [...dataSource.value];
};

// 上传文件列表（上传多个文件）
let fileList = ref([]);
// 上转状态改变
const handleFileChange = (file, files) => {
  fileList.value = files;
};
// 删除之前的钩子
const handleFileRemove = (file, files) => {
  fileList.value = files;
  // console.log("文件", fileList.value)
};

//获取需求信息
function getRequirementById() {
  dataApi.getRequirementById({ id: state.requirementId }).then((res) => {
    if (res.data.code == 200) {
      state.requirementInfo = res.data.data;
      state.trackFileList = [state.requirementInfo.file];
      getTaskById();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
// 获取子任务列表
function getTaskById(row) {
  dataApi.getTaskById({ requirementId: state.requirementId }).then((res) => {
    if (res.data.code == 200) {
      state.taskList = res.data.data;
      if (state.taskList.length > 0) {
        handleTaskTableClick(row || state.taskList[0]);
      } else {
        state.targetList = [];
      }
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
// 获取域
function getAreaList() {
  areaApi.getAreaList({ page: 1, size: 999, areaType: 2 }).then((res) => {
    if (res.data.code == 200) {
      areaState.areaList = res.data.data.records;
      if (areaState.areaList.length > 0) {
        areaState.areaId = areaState.areaList[0].id;
        areaState.activeName = 0;
        getAreaGeneralTree(areaState.areaList[0].id);
      }
      // areaState.areaList.forEach((item, index) => {
      //   getAreaGeneralTree(item);
      // });
    }
  });
}
// 获取航迹列表
function getTrackList() {
  trackApi.getPresetList().then((res) => {
    if (res.data.code == 200) {
      state.trackList = res.data.data.records;
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
// 获取id分类查询基础数据分类树{areaId}
function getAreaGeneralTree(areaId) {
  areaState.areaId = areaId;
  areaState.treeObj = {};
  areaApi.getAreaGeneralTree({ areaId: areaId }).then((res) => {
    if (res.data.code) {
      let data = res.data.data;
      let obj = {};
      data.forEach((item, index) => {
        item.targetInfo = [];
        // 对空天地海分类 有则添加，没有则构建结构
        if (obj[item.dataType]) {
          obj[item.dataType].children.push(item);
        } else {
          obj[item.dataType] = {
            label: item.dataTypeValue,
            dataType: item.dataType,
            children: [item],
          };
        }
        getTargetList(item.dataType, item);
      });
      areaState.treeObj = obj;
    }
  });
}
//获取目标列表
function getTargetList(dataType, data) {
  getAreaGeneralData(dataType, data);
}
//tab页切换查询
function tabChange(index) {
  getAreaGeneralTree(areaState.areaList[index].id);
}
//获取每个分类下具体的资源数据
function getAreaGeneralData(dataType, data) {
  if (!data.id) {
    return;
  }
  areaApi
    .getAreaGeneralData({
      dataTypeId: data.id,
      areaId: areaState.areaId,
      page: 1,
      size: 999,
    })
    .then((res) => {
      if (res.data.code == 200) {
        for (let item of areaState.treeObj[dataType].children) {
          if (item.id == data.id) {
            item.targetInfo = res.data.data.records;
            break;
          }
        }
      } else {
        ElMessage.error(res.data.message);
      }
    });
}

function clearNewRoute() {
  window?.EVGISMAP("removeGroupEntityById", { group: "DRAWLAYERGROUP", id: "new_point" });
  window?.EVGISMAP("removeGroupEntityById", { group: "DRAWLAYERGROUP", id: "new_polyLine" });
}
//删除目标
function targetDeleteClick(row) {
  dataApi.removeTarget({ targetId: row.targetId, taskId: state.task.id }).then((res) => {
    if (res.data.code == 200) {
      ElMessage.success("目标删除成功");
      handleTaskTableClick(state.task);
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
//航迹选择按钮点击
function trackClick() {
  trackFormRef.value.validate((valid) => {
    if (valid) {
      state.trackShow = true;
    }
  });
}
//确认选择航迹
function chooseTrack() {
  clearNewRoute();
  setTimeout(() => {
    let row = state.trackList.find((item) => {
      return item.id == state.chooseTrack;
    });
    let tracks = row.tracks;
    state.chooseTrack = null;
    state.trackShow = false;
    state.currentTarget.targetTrack = [];
    tracks.forEach((item) => {
      state.currentTarget.targetTrack.push({
        longitude: Number(item.longitude.toFixed(6)),
        latitude: Number(item.latitude.toFixed(6)),
        // altitude: Number(pos[2].toFixed(6)) || 0,
        altitude: item.altitude,
        time: undefined,
        timeSpan: undefined,
        timeVal: undefined,
        direct: 0,
        speed: state.currentTarget.trackSpeed,
      });
    });
    calcTime();
    drawPonit(state.currentTarget);
  }, 200);
}
//目标信息
function targetRowClick(row) {
  state.targetListShow = false;
  state.targetTrackShow = true;
  state.currentTarget = { ...row };
  state.currentTarget.targetTrack?.forEach((item) => {
    for (let i = 0; i < state.currentTarget.targetTrack.length; i++) {
      let item = state.currentTarget.targetTrack[i];
      let itemLast = state.currentTarget.targetTrack[i - 1];
      item.timeSpan = item.time - (itemLast?.time || 0);
      item.timeVal = item.timeSpan + new Date(itemLast?.timeVal || state.task.startTime).getTime();
    }
  });
}
onBeforeUnmount(() => {
  document.onmousemove = null;
  document.oncontextmenu = null;
});
function closeEditTarget() {
  state.targetListShow = false;
  // window.EVGISMAP("removeGroupEntityById", {
  //   id: "new_point",
  //   group: "DRAWLAYERGROUP",
  // });
  // window.EVGISMAP("removeGroupEntityById", {
  //   id: "new_polyLine",
  //   group: "DRAWLAYERGROUP",
  // });
}
//绘制航迹
function trackDraw() {
  let handle = () => {
    setTimeout(() => {
      window.EVGISMAP("drawPolyline", {
        id: "new_polyLine",
        group: "DRAWLAYERGROUP",
        outlineColor: state.currentTarget.trackColor || "green",
        outlineWidth: state.currentTarget.trackWidth || "1",
        callBack: (params) => {
          state.currentTarget.targetTrack = [];
          params.position.forEach((pos) => {
            state.currentTarget.targetTrack.push({
              longitude: Number(pos[0].toFixed(6)),
              latitude: Number(pos[1].toFixed(6)),
              // altitude: Number(pos[2].toFixed(6)) || 0,
              altitude: 0,
              time: undefined,
              timeSpan: undefined,
              timeVal: undefined,
              direct: 0,
              speed: state.currentTarget.trackSpeed,
            });
          });
          calcTime();
          drawPonit(state.currentTarget);
          // trackSave(params);
        },
      });
    }, 200);
  };

  trackFormRef.value.validate((valid) => {
    if (valid) {
      clearNewRoute();
      handle();
    }
  });
}
//计算距离
function calcDistance(lng1, lat1, lng2, lat2) {
  let rad1 = (lat1 * Math.PI) / 180.0;
  let rad2 = (lat2 * Math.PI) / 180.0;
  let a = rad1 - rad2;
  let b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180;
  let r = 6378137;
  let distance =
    r *
    2 *
    Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));
  return (distance / 1000).toFixed(2);
}
function drawPonit(target) {
  let list = target.targetTrack;
  window.EVGISMAP("drawPointByAttr", {
    id: "new_point",
    img: window.dataTypeImg[target.dataType - 1],
    group: "DRAWLAYERGROUP",
    position: [list[0].longitude, list[0].latitude, list[0].altitude],
    name: target.name,
    color: "#59E870",
    outlineColor: "green",
    outlineWidth: target.trackWidth,
    textColor: target.trackColor,
    textOutlineColor: "black",
    textScale: 1,
    scale: 1,
    direction: 0,
  });
}
function trackSpeedChange() {
  if (state.currentTarget.trackSpeed && state.currentTarget.targetTrack.length > 0) {
    calcTime();
  }
}
//根据速度计算航迹时间
function calcTime() {
  for (let i = 0; i < state.currentTarget.targetTrack.length; i++) {
    let item = state.currentTarget.targetTrack[i];
    let itemLast = state.currentTarget.targetTrack[i - 1];
    if (i == 0) {
      item.timeVal = new Date(state.task.startTime).getTime();
      item.timeSpan = 0;
      item.distance = 0;
    } else {
      let distance = calcDistance(item.longitude, item.latitude, itemLast.longitude, itemLast.latitude);
      item.timeSpan = parseInt((distance / state.currentTarget.trackSpeed) * 60 * 60 * 1000);
      item.timeVal = item.timeSpan + new Date(itemLast.timeVal).getTime();
      // item.distance = distance;
    }
    item.speed = state.currentTarget.trackSpeed;
  }
  console.log("targetTrack", state.currentTarget.targetTrack);
}
//时间选择范围框定
function disabledDate(date, row, $index) {
  let min = state.currentTarget.targetTrack[$index - 1]?.timeVal || state.task.startTime;
  let max = state.currentTarget.targetTrack[$index - 1]?.timeVal || state.task.endTime;
  if (date <= min || date >= max) {
    return true;
  } else {
    return false;
  }
}
//修改航迹时间时，同步修改后续航迹的时间
function trackTimeChange(row, index) {
  let itemLa = state.currentTarget.targetTrack[index - 1];
  var a = itemLa?.timeSpan;
  console.log(a);
  state.currentTarget.targetTrack[index].timeSpan = parseInt(
    new Date(row.timeVal).getTime() - (itemLa?.timeSpan || new Date(state.task.startTime).getTime())
  );
  let distance = calcDistance(row.longitude, row.latitude, itemLa.longitude, itemLa.latitude);
  state.currentTarget.targetTrack[index].speed =
    distance / state.currentTarget.targetTrack[index].timeSpan / 1000 / 60 / 60;
  for (let i = index + 1; i < state.currentTarget.targetTrack.length; i++) {
    let item = state.currentTarget.targetTrack[i];
    let itemLast = state.currentTarget.targetTrack[i - 1];
    item.timeVal = item.timeSpan + new Date(itemLast.timeVal).getTime();
  }
}
function saveTarget() {
  trackFormRef.value.validate((valid) => {
    if (valid) {
      save();
    }
  });
  const save = () => {
    if (state.currentTarget.targetTrack.length == 0) {
      ElMessage.error("请手动绘制或导入航迹");
      return;
    }
    state.currentTarget.targetTrack.forEach((item) => {
      item.relationId = state.currentTarget.id;
      item.time = parseInt((item.timeVal - new Date(state.task.startTime).getTime()) / 1000);
    });
    state.currentTarget.latitude = state.currentTarget.targetTrack[0].latitude;
    state.currentTarget.longitude = state.currentTarget.targetTrack[0].longitude;
    state.currentTarget.altitude = state.currentTarget.targetTrack[0].altitude;

    dataApi.auTaskTarget(state.currentTarget).then((res) => {
      if (res.data.code == 200) {
        if (state.currentTarget.id) {
          ElMessage.success("添加资源目标成功");
        } else {
          ElMessage.success("修改资源目标成功");
        }
        state.targetTrackShow = false;
        handleTaskTableClick(state.task);
      } else {
        ElMessage.error(res.data.message);
      }
    });
  };
}
// 获取需求对应的轨迹文件数据
// function getTackFile(id){

//   /wrpt/file/loadTrackFile
//   fileId
// }
//使用航迹文件获取坐标
function fileDraw() {
  trackFormRef.value.validate((valid) => {
    if (valid) {
      state.fileTrackShow = true;
    }
  });
}
//用航迹文件的数据填充
function fileTrack() {
  if (!state.trackFileId) {
    ElMessage.error("请选择一个航迹文件");
    return;
  }
  dataApi.getTrackFile({ fileId: state.trackFileId }).then((res) => {
    if (res.data.code == 200) {
      console.log(res.data.data);
      let pos = [];
      state.currentTarget.targetTrack = [];
      res.data.data.forEach((item) => {
        state.currentTarget.targetTrack.push({
          longitude: item.longitude,
          latitude: item.latitude,
          altitude: item.altitude,
          time: undefined,
          timeSpan: undefined,
          timeVal: undefined,
          direct: 0,
          speed: state.currentTarget.trackSpeed,
        });
        pos.push([item.longitude, item.latitude, item.altitude]);
      });
      calcTime();
      clearNewRoute();
      setTimeout(() => {
        window.EVGISMAP("drawPolylineByAttr", {
          id: "new_polyLine",
          group: "DRAWLAYERGROUP",
          position: pos,
          // name: target.name,
          outlineColor: state.currentTarget.trackColor || "red",
          outlineWidth: state.currentTarget.trackWidth || "2",
        });
        console.log(state.currentTarget);
        drawPonit(state.currentTarget);
      }, 200);

      state.fileTrackShow = false;
      state.trackFileId = null;
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
function addTargetClick() {
  // if (state.targetList == 0) {
  //   return;
  // }
  state.targetListShow = true;
  state.targetTrackShow = false;
}
//双击添加目标
function addTarget(target, classify) {
  if (
    state.targetList.find((item) => {
      return item.targetId == target.id;
    })
  ) {
    ElMessage.warning("该目标已经存在");
    return;
  } else {
    state.newTargetInfo = target;
    state.targetListShow = false;
    state.targetTrackShow = true;
    state.currentTarget = {
      dataType: classify.dataType,
      generalId: classify.id,
      targetId: target.id,
      name: target.name,
      taskId: state.task.id,
      targetTrack: [],
      altitude: undefined,
      latitude: undefined,
      longitude: undefined,
      trackColor: "#D01010",
      trackWidth: 1,
    };
  }
}
function saveAddTarget(target, classify, position) {
  console.log("target", target, position);
  // return;
  let obj = {
    altitude: position[2],
    basicInfo: "",
    dataType: classify.dataType,
    generalId: classify.id,
    // id: "",
    latitude: position[1] || 0,
    longitude: position[0] || 0,
    modelId: "",
    targetId: target.id,
    name: target.name,
    targetTrack: [],
    taskId: state.task.id,
    trackColor: "",
    trackWidth: 0,
  };
  dataApi.auTaskTarget(obj).then((res) => {
    if (res.data.code == 200) {
      ElMessage.success("添加资源目标成功");
      handleTaskTableClick(state.task);
    } else {
      ElMessage.error(res.data.message);
    }
  });
}

function submitTask() {
  console.log("requirementId", state.requirementId);
  // return;
  dataApi.submitTask({ requirementId: state.requirementId }).then((res) => {
    if (res.data.code == 200) {
      ElMessage.success("需求任务提交成功");
      getRequirementById();
      // ElMessage.success("需求任务提交成功,跳转中。。。");
      // window.taskPageParame = {
      //   requirementId: state.requirementId,
      // };
      // router.replace({
      //   path: "/home/<USER>",
      // });
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
// ************************end***************
</script>

<style lang="less" scoped>
.title-font {
  font-size: 18px;
  font-weight: bolder;
  color: #c6cdd6;
}

.el-textarea__inner {
  min-height: 200px;
  height: 200px;
}

.rightList .custmoers .content1 {
  overflow-y: auto;
}

.stepBar .custom-popup-mian .content1 {
  height: 100%;
}

.drawHj {
  position: absolute;
  bottom: 18px;
  right: 474px;
  width: 92px;
  height: 30px;
  color: #fff;
  line-height: 30px;
  text-align: center;
  border-radius: 5px;
  background: #409eff;
  cursor: pointer;
}
</style>

<style lang="less" scoped>
@import "../../assets/style/theme/style.less";

.demandHeader {
  .header-title {
    display: flex;
    padding: 10px 0;
    align-items: center;

    & > span:first-of-type {
      margin-right: 10px;
      background-image: @popupTitleBg;
      background-size: 100% 100%;
      width: 19px;
      height: 13px;
    }
  }
}

.taskList {
  // height: auto;
  height: 35%;
  // margin-bottom: 10px;
  overflow-y: auto;
}
.requirementInfo {
  height: 25%;
  overflow-y: auto;
}
.targetList {
  // height: auto;
  height: 35%;
  overflow-y: auto;
}

/* 步骤条样式 */
:deep(.el-steps) {
  .el-step__icon {
    width: 50px;
    height: 50px;
    top: -14px;
    border: 5px solid;
  }

  .el-step__main {
    position: relative;
    top: -14px;
  }

  .el-step__line {
    background-color: #e3e9f4;
  }
}

:deep(.el-steps .el-step:first-of-type) {
  .el-step__icon {
    left: 30%;
    border-color: #40eb4e;
  }

  .el-step__main {
    left: 29%;
  }
}

:deep(.el-steps .el-step:nth-of-type(2)) {
  .el-step__icon {
    border: 8px solid #37a0ea;
  }

  .el-step__main {
    position: relative;
    left: -1%;
  }
}

:deep(.el-steps .el-step:last-of-type) {
  .el-step__icon {
    right: 290%;
  }

  .el-step__main {
    right: 296%;
  }
}

.require :deep(.el-form-item) {
  margin-bottom: 5px;
}

.require :deep(.el-select__wrapper.is-disabled) {
  background-color: #9e959566;

  .el-select__selected-item {
    color: #a8abb2;
  }
}

.require :deep(.el-input.is-disabled) {
  .el-input__wrapper {
    background-color: #9e959566;
  }
}

.demandImgSpan {
  z-index: 1999;
  position: absolute;
  top: 78px;
  right: 30%;
  height: 28px;
  width: 28px;
  cursor: pointer;
  border: 1px solid #37a0ea;
  background-color: gainsboro;
  border-radius: 4px;
}

.img-box {
  display: inline-block;
  width: 72px;
  height: 72px;
  border: 3px solid gainsboro;
  background-color: gainsboro;
  margin-top: 10px;
  cursor: pointer;

  img {
    height: 72px;
  }
}

/* 动画效果 */
:deep(.el-progress__text) {
  display: none;
}

/* 时间选择器 */
.el-picker__popper {
  border: 1px solid #37a0ea !important;
}
.nameIcon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-right: 15px;
}
.btn_center {
  height: 120px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
