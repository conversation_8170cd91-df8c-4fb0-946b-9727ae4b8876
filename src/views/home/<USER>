<!--
 * @Author: JRX <EMAIL>
 * @Date: 2024-06-18 15:11:31
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-31 10:05:51
 * @FilePath: \wrxtzhglrj\src\views\home\tempManage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog v-model="isShow" title="模板管理" :width="state.showTargetSet ? '90%' : '30%'" center :close-on-press-escape="false"
    :close-on-click-modal="false">
    <div class="dialog_body">
      <el-row :gutter="20" style="height: 60vh">
        <el-col :span="state.showTargetSet ? 8 : 24">
          <el-form ref="tempRef" :model="state.currentTemp" :rules="rules" label-width="95px">
            <el-form-item label="需求模板：">
              <el-select @change="getTempInfo" v-model="state.currentId" style="width: calc(100% - 90px)">
                <el-option v-for="item in state.tempList" :key="item.id" :label="item.templateName"
                  :value="item.id"></el-option>
              </el-select>
              <el-button type="primary" @click="addNewForm" style="width: 80px; margin-left: 10px">新增模板</el-button>
            </el-form-item>
            <el-form-item label="模板名称：" prop="templateName">
              <el-input v-model="state.currentTemp.templateName"></el-input>
            </el-form-item>
            <el-form-item label="需求类型：" prop="requirementType">
              <el-select v-model="state.currentTemp.requirementType">
                <el-option v-for="(value, key) in dictTypeList['requirementType']" :key="key" :label="value"
                  :value="Number(key)" />
              </el-select>
            </el-form-item>
            <el-form-item label="重要程度：" prop="importance">
              <el-select v-model.number="state.currentTemp.importance">
                <el-option v-for="(value, key) in dictTypeList['importance']" :key="key" :label="value"
                  :value="Number(key)" />
              </el-select>
            </el-form-item>

            <el-form-item label="开始时间：" prop="startTime">
              <el-date-picker style="width: 100%" v-model="state.currentTemp.startTime" type="datetime"
                placeholder="请选择时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item label="结束时间：" prop="endTime">
              <el-date-picker style="width: 100%" v-model="state.currentTemp.endTime" type="datetime"
                placeholder="请选择时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item label="任务目标：" prop="targetInfos">
              <el-button @click="state.showTargetSet = !state.showTargetSet">任务目标配置</el-button>
            </el-form-item>
            <el-form-item label="需求描述：" prop="requirementComment">
              <el-input type="textarea" rows="4" v-model="state.currentTemp.requirementComment"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="16" v-show="state.showTargetSet"
          style="padding: 0 20px; border-left: 1px solid #255788; height: 100%; box-sizing: border-box">
          <div style="width: 100%; height: 100%">
            <div style="height: 30px; width: 100%; border-bottom: 1px; padding-bottom: 10px; margin-bottom: 10px">
              <el-button @click="addTargetRow">增加</el-button>
            </div>
            <div style="height: calc(100% - 50px); overflow-y: auto; width: 100%; padding-right: 5px">
              <!-- <transition name="el-zoom-in-top">
        <div v-show="show" class="transition-box">.el-zoom-in-top</div>
      </transition> -->
              <el-card v-for="(fromTarget, targetIndex) in state.formTargetList" :key="targetIndex"
                style="margin-bottom: 30px">
                <template #header>
                  <el-row :gutter="0">
                    <el-col :span="6">
                      目标：
                      <el-cascader :props="cascaderProps" :show-all-levels="false"
                        :options="state.echoOption[targetIndex]" :key="state.cascaderKey + targetIndex"
                        style="width: 70%" :ref="(el) => {
                          setItemRef(el, targetIndex);
                        }
                          " v-model="fromTarget.targetId" @change="(val) => {
                            targetIdChange(val, targetIndex);
                          }
                            " @visible-change="(flag) => {
                              flag && (state.echoOption[targetIndex] = []);
                            }
                              " />
                    </el-col>
                    <el-col :span="6">
                      航迹：
                      <el-select v-model="fromTarget.trackPresetId" style="width: 70%" placeholder="请选择" clearable>
                        <el-option v-for="item in state.trackManagerList" :key="item.id" :label="item.name"
                          :value="item.id"></el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="12">
                      航迹时间：
                      <el-date-picker type="datetime" style="width: 70%" v-model="fromTarget.trackStartTime" placeholder="请选择时间"
                        format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
                      <!-- -
                      <el-time-picker
                        style="width: 35%"
                        v-model="fromTarget.trackEndTime"
                        placeholder="请选择时间"
                        format="HH:mm:ss"
                        value-format="HH:mm:ss"
                      /> -->
                      <el-button @click="delTargetRow(targetIndex)" style="margin-left: 4px">删除</el-button>
                    </el-col>
                  </el-row>
                </template>
                <div>
                  <el-table :data="fromTarget.tasks" class="custom-table">
                    <el-table-column type="index" width="50" align="center" />
                    <el-table-column label="业务类型" align="center">
                      <template #default="{ row }">
                        <el-select v-model="row.taskType" placeholder="" multiple clearable style="width: 90%">
                          <el-option v-for="(value, key) in dictTypeList['businessType']" :key="key" :label="value"
                            :value="Number(key)"></el-option>
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column label="开始时间" align="center">
                      <template #default="{ row }">
                        <el-date-picker type="datetime" style="width: 90%" v-model="row.startTime" placeholder="请选择时间" format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss" />
                      </template>
                    </el-table-column>
                    <el-table-column label="结束时间" align="center">
                      <template #default="{ row }">
                        <el-date-picker type="datetime" style="width: 90%" v-model="row.endTime" placeholder="请选择时间" format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss" />
                      </template>
                    </el-table-column>
                    <el-table-column label="重复" width="150" align="center">
                      <template #default="{ row }">
                        <el-select v-model="row.repeatType" placeholder="" clearable style="width: 90%">
                          <el-option v-for="(value, key) in dictTypeList['repetitionType']" :key="key" :label="value"
                            :value="Number(key)"></el-option>
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center">
                      <template #header="scope">
                        <el-button @click="addBusinessRow(targetIndex)">新增</el-button>
                      </template>
                      <template #default="scope">
                        <el-button @click="delBusinessRow(targetIndex, scope.$index)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button style="width: 80px" type="primary" @click="saveTemplate">保存</el-button>
        <el-button style="width: 80px" type="primary" @click="delTemp" v-show="state.currentId">删除模板</el-button>
        <el-button style="width: 80px" type="primary" @click="getFileTemplate">模板下载</el-button>
        <el-button style="width: 80px" @click="cancelAdd('edit')">取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { defineComponent } from "vue";
  import * as dataApi from "@/service/API/home/<USER>";
  import * as areaApi from "@/service/API/system/areaManage.js";
  import * as trackApi from "@/service/API/system/trackManager.js";
  import { reactive, onMounted, ref, watch } from "vue";
  import { ElMessage, ElMessageBox } from "element-plus";
  import customPopup from "@/components/customPopup.vue";

  let tempRef = ref(null);
  let uploadFileRef = ref(null);
  const isShow = ref(true);

  const dictValue = window.dictValue;
  const dictTypeList = window.dictTypeList;
  const validateTarger = (rule, value, callback) => {
    if (!state.formTargetList || state.formTargetList.length == 0) {
      callback(new Error("请至少添加一个目标"));
      return;
    }
    for (const item of state.formTargetList) {
      if (!item.targetId) {
        callback(new Error("有目标行未选择目标"));
        return;
      }
      if (!item.trackPresetId) {
        callback(new Error("有目标行未选择航迹"));
        return;
      }
      if (!item.trackPresetId) {
        callback(new Error("有目标行未选择航迹"));
        return;
      }
      for (const task of item.tasks) {
        if (task.taskType.length == 0) {
          callback(new Error("有任务行未选择业务类型"));
          return;
        }
        if (!task.startTime) {
          callback(new Error("有任务行未选择开始时间"));
          return;
        }
        if (!task.endTime) {
          callback(new Error("有任务行未选择结束时间"));
          return;
        }
        if (!task.repeatType && task.repeatType != 0) {
          callback(new Error("有任务行未选择重复类型"));
          return;
        }
      }
    }
    callback();
  };
  const rules = {
    templateName: [{ required: true, message: "请输入模板名称" }],
    requirementName: [{ required: true, message: "请输入需求名称" }],
    requirementType: [{ required: true, type: "number", message: "请选择需求类型", trigger: "change" }],
    scheduleType: [{ required: true, type: "number", message: "请选择调度类型", trigger: "change" }],
    importance: [{ required: true, type: "number", message: "请选择重要程度", trigger: "change" }],
    status: [{ required: true, type: "number", message: "请选择需求状态", trigger: "change" }],
    source: [{ required: true, type: "number", message: "请选择需求来源", trigger: "change" }],
    startTime: [{ required: true, type: "date", message: "请输入开始时间", trigger: "change" }],
    endTime: [{ required: true, type: "date", message: "请输入结束时间", trigger: "change" }],
    targetInfos: [{ required: true, validator: validateTarger, trigger: "change" }],
    // fileId: [{ required: true, message: "请上传轨迹文件", trigger: "change" }],
    // presetSource: [{ required: true, type: "array", message: "请选择预设资源", trigger: "change" }],
  };
  const cascaderProps = {
    multiple: false,
    emitPath: false,
    lazy: true,
    lazyLoad: lazyLoad,
    value: "value",
    label: "label",
    children: "children",
    leaf: "leaf",
  };
  const state = reactive({
    tempList: [], //模板列表
    currentTemp: {}, //当前模板信息 没有ID为新增 有ID为编辑
    currentId: null, //当前选中的模板ID
    fileList: [], //弹窗上传的文件列表
    showTargetSet: false, //目标配置部分显示
    formTargetList: [],
    trackManagerList: [],
    echoOption: [],
    cascaderKey: 1,
  });

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    requirementForm: {
      type: Object,
      default: {},
    },
  });
  watch(
    () => props.show,
    (val) => {
      // isShow.value = val;
      tempRef.value.clearValidate();
      if (val) {
        state.showTargetSet = false;
        getTempInfo(state.currentId);
      }
    }
  );

  onMounted(() => {
    getTemplateList();
    getTrackManagerList();
  });
  //获取航迹管理列表
  function getTrackManagerList() {
    trackApi.getPresetList().then((res) => {
      if (res.data.code == 200) {
        state.trackManagerList = res.data.data.records;
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }
  /**
   * @description 获取需求模板列表
   */
  function getTemplateList(id) {
    dataApi.getTemplateList({ page: 1, pageSize: 999 }).then((res) => {
      if (res.data.code == 200) {
        state.tempList = res.data.data.records;
        getTempInfo(id || state.tempList[0]?.id);
        emits("asyncTempData", state.tempList);
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }
  /**
   * @description 根据ID获取需求具体信息
   * @param {*} id 需求ID
   */
  function getTempInfo(id) {
    console.log("getTempInfo", id);
    if (!id) {
      state.currentTemp = {};
      return;
    }
    state.currentId = id;
    state.echoOption = [];
    dataApi.getTempInfo({ id: id }).then((res) => {
      if (res.data.code == 200) {
        state.currentTemp = res.data.data;
        state.formTargetList = state.currentTemp.targetInfos || [];
        ecohFun();
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }
  //用于级联的回显
  function ecohFun() {
    state.echoOption = [];
    state.formTargetList.forEach((item, index) => {
      dataApi.getAreaAndTargetInfo({ generalId: item.generalId, targetId: item.targetId }).then((res2) => {
        if (res2.data.code == 200) {
          let obj = [];
          res2.data.data.forEach((item) => {
            let children = [];
            item.dataGenerals?.forEach((item2) => {
              let children2 = [];
              if (item2.targetInfo) {
                children2.push({
                  leaf: true,
                  value: item2.targetInfo.id,
                  label: item2.targetInfo.name,
                });
              }
              children.push({
                leaf: false,
                value: item2.id,
                label: item.dataTypeValue,
                children: children2 || [],
              });
            });
            obj.push({
              leaf: false,
              value: item.id,
              label: item.areaName,
              children: children,
            });
          });
          state.echoOption[index] = obj;
          state.cascaderKey = state.cascaderKey + 1;
        } else {
          ElMessage.error(res2.data.message);
        }
      });
    });
  }
  //动态加载级联选择器
  var areaId = null;
  var areaGeneralTree = [];
  function lazyLoad(node, resolve) {
    const { level, value } = node;
    console.log(node);
    if (level == 0) {
      areaApi.getAreaList({ page: 1, size: 999, areaType: 2 }).then((res) => {
        if (res.data.code == 200) {
          let areaList = [];
          res.data.data.records.forEach((item) => {
            areaList.push({
              value: item.id,
              label: item.areaName,
              leaf: false,
            });
          });
          resolve(areaList);
        } else {
          ElMessage.error(res.data.message);
        }
      });
    } else if (level == 1) {
      areaId = value;
      areaApi.getAreaGeneralTree({ areaId: value }).then((res) => {
        if (res.data.code) {
          areaGeneralTree = res.data.data;
          let obj = {};
          res.data.data.forEach((item) => {
            obj[item.dataType] = item.dataTypeValue;
          });
          let data = [];
          for (const key in obj) {
            data.push({
              value: key,
              label: obj[key],
              leaf: false,
            });
          }
          resolve(data);
        } else {
          ElMessage.error(res.data.message);
        }
      });
    } else if (level == 2) {
      let data = areaGeneralTree.filter((item) => {
        return item.dataType == value;
      });
      console.log(data);
      let reData = [];
      data.forEach((item) => {
        reData.push({
          value: item.id,
          label: item.tableComment,
          leaf: false,
        });
      });
      resolve(reData);
    } else if (level == 3) {
      areaApi.getAreaGeneralData({ dataTypeId: value, areaId: areaId }).then((res) => {
        if (res.data.code) {
          let data = [];
          res.data.data.records.forEach((item) => {
            data.push({
              value: item.id,
              label: item.name,
              leaf: true,
            });
          });
          resolve(data);
        } else {
          ElMessage.error(res.data.message);
        }
      });
    }
  }
  /**
   * @description 新增模板
   */
  function addNewForm() {
    state.currentTemp = {};
    state.formTargetList = [];
    state.currentId = null;
  }
  /**
   * @description 删除提示
   */
  function delTemp() {
    ElMessageBox.confirm("是否确认删除该模板?", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      delTempById(state.currentId);
    });
  }
  /**
   * @description 根据ID删除需求具体信息
   * @param {*} id 需求ID
   */
  function delTempById(id) {
    if (!id) {
      return;
    }
    dataApi.delTempById({ id: id }).then((res) => {
      if (res.data.code == 200) {
        ElMessage.success("删除模板成功");
        getTemplateList();
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }
  /**
   * @description 验证后保存模板表单
   */
  function saveTemplate() {
    state.currentTemp.targetInfos = state.formTargetList;
    tempRef.value.validate((valid) => {
      if (valid) {
        dataApi.auTemplateInfo(state.currentTemp).then((res) => {
          if (res.data.code == 200) {
            ElMessage.success("保存模板成功");
            let curId = state.currentTemp.id || res.data.data
            getTemplateList(curId);
          }
        });
      } else {
        ElMessage.warning("请完善参数");
      }
    });
  }
  //获取文件模板
  function getFileTemplate() {
    dataApi.getFileTemplate().then((res) => {
      let data = res.data;
      // , { type: "application/vnd.ms-excel;charset=utf-8" }
      let url = window.URL.createObjectURL(new Blob([data]));
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", "模板.xlsx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }
  //关闭窗口
  function close() {
    emits("close");
    if (!state.currentTemp.id) {
      state.currentTemp = {};
    }
  }
  //情况上传列表
  function clearFile() {
    state.currentTemp.fileId = null;
    state.currentTemp.fileName = null;
    state.currentTemp.file = {};
  }

  //为新增/编辑需求 增加一行目标
  function addTargetRow() {
    state.formTargetList.push({
      targetId: undefined,
      trackPresetId: undefined,
      trackStartTime: props.requirementForm.startTime || null,
      // trackEndTime: props.requirementForm.endTime || null,
      tasks: [],
    });
  }
  //为新增/编辑需求 删除一行目标
  function delTargetRow(index) {
    state.formTargetList.splice(index, 1);
    ecohFun();
  }
  //删除一行业务数据
  function delBusinessRow(targetIndex, businessIndex) {
    state.formTargetList[targetIndex].tasks.splice(businessIndex, 1);

  }
  //新增一行业务数据
  function addBusinessRow(targetIndex) {
    state.formTargetList[targetIndex].tasks.push({
      taskType: [],
      startTime: null,
      endTime: null,
      repeatType: 1,
    });
  }
  // 文件上传
  function uploadFile(file, uploadFiles) {
    // console.log("uploadFile", file, uploadFiles)
    var upload = () => {
      let formData = new FormData();
      let bzPath = "";
      formData.append("file", file.raw);
      formData.append("bzPath", bzPath);
      dataApi.uploadFile(formData).then((res) => {
        if (res.data.code == 200) {
          ElMessage.success("上传成功");
          // console.log(res.data.data);
          state.currentTemp.fileId = res.data.data;
          state.currentTemp.fileName = file.name;
          state.currentTemp.file = file;
          uploadFileRef.value.clearFiles();
        } else {
          ElMessage.error(res.data.message);
        }
      });
    };
    if (uploadFiles.length == 0) {
      return;
    }
    if (state.currentTemp.fileId) {
      ElMessageBox.confirm("继续上传将覆盖前一文件，是否继续?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        upload();
      });
    } else {
      upload();
    }
  }
  let itemRefs = {};
  function setItemRef(el, index) {
    itemRefs[index] = el;
  }
  //设置分类ID
  function targetIdChange(val, index) {
    let row = itemRefs[index].getCheckedNodes(true)[0];
    let generalId = row.parent.data.value;
    let targetName = row.text + '';
    state.formTargetList[index].generalId = generalId;
    state.formTargetList[index].targetName = targetName

  }
  function cancelAdd() {
    emits("close");
    state.currentTemp = {};
    state.formTargetList = [];
  }
  defineExpose({ saveTemplate, state });
  const emits = defineEmits(["close", "asyncTempData"]);
</script>
<style scoped lang="less">
  @import "../../assets/style/theme/style.less";

  .title-font {
    font-size: 18px;
    font-weight: bolder;
    color: #c6cdd6;
  }
</style>
