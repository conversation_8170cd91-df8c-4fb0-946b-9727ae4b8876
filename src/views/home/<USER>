<!--
 * @Author: JRX <EMAIL>
 * @Date: 2024-03-26 16:55:34
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-23 16:26:18
 * @FilePath: \wrxtzhglrj\src\views\home\HomePage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @@Description: 需求管理
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-06-18 15:30:48
 * @FilePath: \qbmbtsweb\src\views\home\HomePage.vue
-->
<template>
  <div style="width: 100%; height: 100%; position: relative; background-color: #9a949475">
    <!-- 左 -->
    <!-- 中 -->
    <!-- <div style="position: absolute; width: 60%; height: 100%; left: calc(16% + 11px);top:10px;"> -->
    <div style="position: absolute; width: 82%; height: 100%; left: 0.5%; top: 10px">
      <!-- 中-上 -->
      <div style="width: 100%; height: 59%">
        <customPopup width="100%" height="59%">
          <template #header>
            <span class="list_icon"></span>
            <span class="title-font">需求列表</span>
            <div class="planButton">
              <img src="/public/images/icon/需求管理_03.png" alt="" />
              <span @click="newPlan">新建需求</span>
              <img src="/public/images/icon/需求管理_06.png" alt="" />
              <span @click="tempState.show = true">模板管理</span>
            </div>
          </template>
          <template #content>
            <div></div>
            <div>
              <el-input v-model="state.keyword" placeholder="请输入关键字" style="width: 93%" />
              <el-button
                style="
                  width: 6%;
                  padding-left: 40px;
                  left: 1%;
                  position: relative;
                  background-color: #1cb2ff;
                  color: #fff;
                "
                @click="getRequirementList()"
              >
                <img src="/public/images/icon/需求管理_15.png" style="position: absolute; left: 2px; top: -1px" />
                搜索
              </el-button>
            </div>
            <div style="position: relative; top: 1%; height: calc(100% - 40px)">
              <customTable
                :tableData="state.tableData"
                :tableColumn="state.tableColumn"
                :total="pageInfo.total"
                :currentPage="pageInfo.page"
                :pageSize="pageInfo.size"
                :defaultCurrentRow="0"
                ref="tableRef"
                @currentPageEmit="currentPageEmit"
                @pageSizeEmit="pageSizeEmit"
                @nodeClickEmit="handleTableClick"
                @handleEditEmit="handleEditClick"
                @handleDeleteEmit="handleDeleteClick"
                @handleDemandEmit="handleDemand"
              ></customTable>
            </div>
          </template>
        </customPopup>
      </div>
      <div style="width: 100%; height: 1%; background-color: transparent"></div>
      <!-- 中-下 -->
      <div style="width: 100%; height: 32%; top: 2%">
        <customPopup width="100%" height="32%" headType="2">
          <template #header>
            <span class="list_icon"></span>
            <span class="title-font">需求详情</span>
          </template>
          <template #content>
            <div class="requir">
              <el-form :model="state.formRequirement" label-width="120px" class="requirement_form">
                <!-- <div style="margin-bottom: 1%"> -->
                <el-row>
                  <el-col :span="6">
                    <el-form-item label="需求名称：">
                      <span>{{ state.formRequirement.requirementName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="需求类型：">
                      <span>{{ dictValue("requirementType", state.formRequirement.requirementType) }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="重要程度：">
                      <span>{{ dictValue("importance", state.formRequirement.importance) }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="需求来源：">
                      <span>{{ dictValue("source", state.formRequirement.source) }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="需求状态：">
                      <span>{{ dictValue("requirementStatus", state.formRequirement.status) }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="开始时间：">
                      <span>{{ timeSpan(state.formRequirement.startTime) }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="结束时间：">
                      <span>{{ timeSpan(state.formRequirement.endTime) }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <div class="textDescribe">
              <span v-html="state.formRequirement.requirementComment"></span>
            </div>
          </template>
        </customPopup>
      </div>
    </div>
    <!-- 右 -->
    <!-- <div style="position: absolute; width: 22%; height: 100%; right: 15px;top:10px"> -->
    <div style="position: absolute; width: 16.5%; height: 100%; right: 0.5%; top: 10px">
      <customPopup width="100%" height="92%">
        <template #header>
          <span class="list_icon"></span>
          <span class="title-font">{{ state.awaitSubmit ? "请确认自动分解任务" : "任务-目标" }}</span>
        </template>
        <template #content>
          <div
            v-if="state.taskList.length == 0"
            style="height: calc(100% - 40px); display: flex; justify-content: center; align-items: center"
          >
            暂无任务及目标
          </div>
          <div v-if="state.taskList.length > 0" style="height: calc(100% - 40px); overflow: auto">
            <div v-for="(task, index) in state.taskList" :key="task.id">
              <div style="margin-bottom: 5%">
                <img style="position: relative; top: 2px" src="/public/images/icon/需求管理_18.png" alt="" />
                <span style="font-size: 18px; font-weight: bold; padding-left: 10px; color: #309eff">
                  {{ task.taskName }}
                </span>
              </div>
              <template v-if="task.targetList">
                <div class="equip" v-for="target in task.targetList" :key="target.id">{{ target.name }}</div>
              </template>
            </div>
          </div>
          <div
            style="
              height: 40px;
              width: 100%;
              border-top: 1px solid;
              justify-content: center;
              align-items: flex-end;
              display: flex;
            "
          >
            <el-button @click="handleDemand(state.formRequirement)" v-if="!state.awaitSubmit">任务分解</el-button>
            <el-button @click="saveAndCall()" v-if="state.awaitSubmit && state.formRequirement.requirementType == 1">
              保存并调度
            </el-button>
            <el-button
              @click="saveRequirementTask()"
              v-if="state.awaitSubmit && state.formRequirement.requirementType == 2"
            >
              保存
            </el-button>
          </div>
        </template>
      </customPopup>
    </div>
  </div>

  <div>
    <!-- 新建，编辑任务 -->
    <el-dialog v-model="isShow" :title="title" width="25%" center :close-on-click-modal="false">
      <div class="dialog_body">
        <el-form ref="formRef" :model="state.auForm" :rules="rules" label-width="95px">
          <el-form-item label="需求模板：" v-if="title == '新建需求'">
            <el-select @change="tempChange" v-model="tempState.currentTemp">
              <el-option
                :value="item.id"
                :label="item.templateName"
                v-for="item in tempState.tempList"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="文件录入：">
            <el-button>选择文件(TODO)</el-button>
          </el-form-item> -->
          <el-form-item label="需求名称：" prop="requirementName">
            <el-input v-model="state.auForm.requirementName"></el-input>
          </el-form-item>
          <el-form-item label="需求类型：" prop="requirementType">
            <el-select v-model="state.auForm.requirementType">
              <el-option
                v-for="(value, key) in dictTypeList['requirementType']"
                :key="key"
                :label="value"
                :value="Number(key)"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="需求来源：" prop="source">
            <el-select v-model="state.auForm.source">
              <el-option
                v-for="(value, key) in dictTypeList['source']"
                :key="key"
                :label="value"
                :value="Number(key)"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="调度类型：" prop="scheduleType">
            <el-select v-model.number="state.auForm.scheduleType">
              <el-option
                v-for="(value, key) in dictTypeList['scheduleType']"
                :key="key"
                :label="value"
                :value="Number(key)"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item label="重要程度：" prop="importance">
            <el-select v-model.number="state.auForm.importance">
              <el-option
                v-for="(value, key) in dictTypeList['importance']"
                :key="key"
                :label="value"
                :value="Number(key)"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="开始时间：" prop="startTime">
            <el-date-picker
              style="width: 100%"
              v-model="state.auForm.startTime"
              type="datetime"
              placeholder="请选择时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="结束时间：" prop="endTime">
            <el-date-picker
              style="width: 100%"
              v-model="state.auForm.endTime"
              type="datetime"
              placeholder="请选择时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="任务目标：" prop="presetSource" v-if="!state.auForm.id">
            <el-cascader :props="cascaderProps" :show-all-levels="false" v-model="state.auForm.presetSource" />
          </el-form-item>
          <el-form-item label="轨迹导入：" prop="fileId">
            <!-- <el-button>导入</el-button> -->
            <el-upload
              action="#"
              :auto-upload="false"
              style="width: 100%"
              ref="uploadFileRef"
              :limit="1"
              v-model:file-list="state.fileList"
              :on-change="uploadFile"
              :show-file-list="false"
            >
              <template #trigger>
                <el-button type="primary">轨迹文件上传</el-button>
              </template>
              <template #tip>
                <div class="el-upload__tip userColor" style="position: relative">
                  轨迹文件：{{
                    state.auForm.fileName || state.auForm.file?.fileName || state.auForm.fileId || "未上传文件"
                  }}
                  <el-link
                    :icon="Edit"
                    @click.stop="clearFile"
                    :underline="false"
                    style="position: absolute; top: 10px; right: 0"
                  >
                    <el-icon><Close /></el-icon>
                  </el-link>
                </div>
              </template>
            </el-upload>
          </el-form-item>
          <el-form-item label="需求描述：" prop="requirementComment">
            <el-input type="textarea" rows="4" v-model="state.auForm.requirementComment"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button style="width: 80px" @click="saveAndDisassemble" v-if="!state.auForm.id">保存并分解</el-button>
          <el-button style="width: 80px" type="primary" @click="saveAdd">保 存</el-button>

          <el-button style="width: 80px" @click="cancelAdd('edit')">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <div class="tempBoxClass" v-show="tempState.show">
    <tempManage :show="tempState.show" @asyncTempData="asyncTempData" @close="tempState.show = false" />
  </div>
</template>
<script setup>
import { reactive, onMounted, onUnmounted, ref, nextTick, watch } from "vue";
import customWindow from "@/components/customWindow.vue";
import customPopup from "@/components/customPopup.vue";
import dataTable from "./components/dataTable.vue";
import newDataTable from "./components/newDataTable.vue";
import customTable from "@/components/customTable.vue";
import { useRouter } from "vue-router";
import { routerStore } from "@/stores/routerStore";
import * as dataApi from "@/service/API/home/<USER>";
import * as areaApi from "@/service/API/system/areaManage.js";
import tempManage from "./tempManage.vue";

import { ElMessage, ElMessageBox } from "element-plus";

const routerstore = routerStore(); // 路由的状态管理器
const router = useRouter();
const isShow = ref(false);
const timeSpan = window.timeSpan;
const dictValue = window.dictValue;
const dictTypeList = window.dictTypeList;
const tempState = reactive({
  show: false,
  tempList: [],
  currentTemp: null,
});
const cascaderProps = {
  multiple: true,
  emitPath: false,
  lazy: true,
  lazyLoad: lazyLoad,
  value: "value",
  label: "label",
  children: "children",
  leaf: "leaf",
};
let tableRef = ref(null);
// form表单
let formRef = ref(null);
let uploadFileRef = ref(null);

// 新增/修改
const title = ref("");
const rules = {
  requirementName: [{ required: true, message: "请输入需求名称" }],
  requirementType: [{ required: true, type: "number", message: "请选择需求类型", trigger: "change" }],
  scheduleType: [{ required: true, type: "number", message: "请选择调度类型", trigger: "change" }],
  importance: [{ required: true, type: "number", message: "请选择重要程度", trigger: "change" }],
  status: [{ required: true, type: "number", message: "请选择需求状态", trigger: "change" }],
  source: [{ required: true, type: "number", message: "请选择需求来源", trigger: "change" }],
  startTime: [{ required: true, type: "date", message: "请输入开始时间", trigger: "change" }],
  endTime: [{ required: true, type: "date", message: "请输入结束时间", trigger: "change" }],
  // fileId: [{ required: true, message: "请上传轨迹文件", trigger: "change" }],
  // presetSource: [{ required: true, type: "array", message: "请选择预设资源", trigger: "change" }],
};
const state = reactive({
  tableData: [], //需求表格数据
  keyword: "", //查询关键字
  auForm: {
    presetSource: [],
  }, //新增和修改需求用表单
  taskList: [], //任务列表
  tableColumn: [
    { label: "需求名称", prop: "requirementName", type: "String", width: "" },
    { label: "需求类型", prop: "requirementType", type: "Sysdic", width: "" },
    { label: "重要程度", prop: "importance", type: "Sysdic", width: "" },
    {
      label: "需求状态",
      prop: "status",
      type: "Sysdic",
      width: "",
      dicName: "requirementStatus",
    },
    { label: "需求来源", prop: "source", type: "Sysdic", width: "" },
    { label: "开始时间", prop: "startTime", type: "Time", width: "" },
    { label: "结束时间", prop: "endTime", type: "Time", width: "" },
    // { label: "描述", prop: "requirementComment", type: "String", width: "" },
    {
      prop: "",
      label: "操作",
      type: "operation",
      width: "150",
      operationTypeName: { demand: true, handleEdit: true, handleDelete: true },
    },
  ],
  fileList: [], //弹窗上传的文件列表

  formRequirement: {}, //需求信息
  awaitSubmit: false, //等待确认
});
const areaState = reactive({
  areaList: [],
});
const pageInfo = reactive({
  page: 1,
  size: 10,
  total: 0,
});
/******************** function start*************************/ 3;
onMounted(() => {
  getRequirementList();
});
function getRequirementList(row) {
  dataApi
    .getRequirementList({
      keyword: state.keyword,
      pageNum: pageInfo.page,
      pageSize: pageInfo.size,
    })
    .then((res) => {
      if (res.data.code == 200) {
        state.tableData = res.data.data.records;
        pageInfo.total = res.data.data.total;
        if (state.tableData.length > 0) {
          handleTableClick(row || state.tableData[0]);
        }
      } else {
        ElMessage.error(res.data.message);
      }
    });
}
function currentPageEmit(val) {
  pageInfo.page = val;
  getRequirementList();
}
function pageSizeEmit(val) {
  pageInfo.size = val;
  getRequirementList();
  // getAreaGeneralData(currentTreeNode);
}
//clearFile
function clearFile() {
  state.auForm.fileId = null;
  state.auForm.fileName = null;
  state.auForm.file = {};
  // state.fileList = [];
}
// 文件上传
function uploadFile(file, uploadFiles) {
  // console.log("uploadFile", uploadFile, UploadFiles);
  // return;
  // state.fileList = UploadFiles;

  var upload = () => {
    let formData = new FormData();
    let bzPath = "";
    formData.append("file", file.raw);
    formData.append("bzPath", bzPath);
    dataApi.uploadFile(formData).then((res) => {
      if (res.data.code == 200) {
        ElMessage.success("上传成功");
        // console.log(res.data.data);
        state.auForm.fileId = res.data.data;
        state.auForm.fileName = file.name;
        uploadFileRef.value.clearFiles();
      } else {
        ElMessage.error(res.data.message);
      }
    });
  };
  if (uploadFiles.length == 0) {
    return;
  }
  if (state.auForm.fileId) {
    ElMessageBox.confirm("继续上传将覆盖前一文件，是否继续?", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      upload();
    });
  } else {
    upload();
  }
}
/**
 * @description 新建需求
 */
// 新建需求
const newPlan = () => {
  title.value = "新建需求";
  isShow.value = true;
  state.auForm = {
    presetSource: [],
  };
};
//动态加载级联选择器

var areaId = null;
var areaGeneralTree = [];
function lazyLoad(node, resolve) {
  const { level, value } = node;
  console.log(node);
  if (level == 0) {
    areaApi.getAreaList({ page: 1, size: 999, areaType: 2 }).then((res) => {
      if (res.data.code == 200) {
        let areaList = [];
        res.data.data.records.forEach((item) => {
          areaList.push({
            value: item.id,
            label: item.areaName,
            leaf: false,
          });
        });
        resolve(areaList);
      } else {
        ElMessage.error(res.data.message);
      }
    });
  } else if (level == 1) {
    areaId = value;
    areaApi.getAreaGeneralTree({ areaId: value }).then((res) => {
      if (res.data.code) {
        areaGeneralTree = res.data.data;
        let obj = {};
        res.data.data.forEach((item) => {
          obj[item.dataType] = item.dataTypeValue;
        });
        let data = [];
        for (const key in obj) {
          data.push({
            value: key,
            label: obj[key],
            leaf: false,
          });
        }
        resolve(data);
      } else {
        ElMessage.error(res.data.message);
      }
    });
  } else if (level == 2) {
    let data = areaGeneralTree.filter((item) => {
      return item.dataType == value;
    });
    console.log(data);
    let reData = [];
    data.forEach((item) => {
      reData.push({
        value: item.id,
        label: item.tableComment,
        leaf: false,
      });
    });
    resolve(reData);
  } else if (level == 3) {
    areaApi.getAreaGeneralData({ dataTypeId: value, areaId: areaId }).then((res) => {
      if (res.data.code) {
        let data = [];
        res.data.data.records.forEach((item) => {
          data.push({
            value: item.id,
            label: item.name,
            leaf: true,
          });
        });
        resolve(data);
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }
  // setTimeout(() => {
  //   const nodes = Array.from({ length: level + 1 }).map((item) => ({
  //     value: ++id,
  //     label: `Option - ${id}`,
  //     leaf: level >= 2,
  //   }));
  //   // Invoke `resolve` callback to return the child nodes data and indicate the loading is finished.
  //   resolve(nodes);
  // }, 1000);
}
//切换模板时预填充需求描述 模板没做，暂时没改
const tempChange = (val) => {
  state.auForm = {};
  let temp = tempState.tempList.find((item) => {
    return item.id == val;
  });
  console.log(temp);
  state.auForm.requirementName = temp.templateName;
  state.auForm.requirementType = temp.templateType;
  state.auForm.fileId = temp.fileId;
  state.auForm.file = temp.file;
  state.auForm.requirementComment = temp.templateComment;
};
// 表格中某一行数据的需求分解
const handleDemand = (row) => {
  console.log("需求分解", row);
  let demandInfo = JSON.stringify(row);
  router.push({
    path: "/home/<USER>",
    query: {
      planTitle: "需求分解",
      // demandInfo,
      requirementId: row.id,
    },
    replace: true,
  });
};

/**
 * @description 保存新增/修改
 */
function formSubmit(fun) {
  dataApi.subRequirement(state.auForm).then((res) => {
    if (res.data.code == 200) {
      state.formRequirement = res.data.data;
      if (fun) {
        setTimeout(() => {
          fun();
        }, 1000);
      }
      getRequirementList(state.formRequirement);

      isShow.value = false;
      formRef = {};
      formRef.value.resetFields();
      formRef.value.clearValidate();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
//保存并分解
function saveAndDisassemble() {
  // 自动分解任务（ToDO）
  formRef.value.validate((valid) => {
    if (valid) {
      let fun = () => {
        state.taskList = [
          {
            id: "xxxxxxx1",
            taskName: "飞行任务1",
            targetList: [{ id: "xxxx1", name: "目标xxx" }],
          },
          {
            id: "xxxxxxx2",
            taskName: "飞行任务2",
            targetList: [{ id: "xxxx2", name: "目标xxx2" }],
          },
          {
            id: "xxxxxxx3",
            taskName: "飞行任务3",
            targetList: [{ id: "xxxx3", name: "目标xxx3" }],
          },
          {
            id: "xxxxxxx4",
            taskName: "飞行任务4",
            targetList: [{ id: "xxxx4", name: "目标xxx4" }],
          },
        ];
        state.awaitSubmit = true;
      };
      formSubmit(fun);
    }
  });
  return;
}
// 保存按钮
const saveAdd = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      formSubmit();
    }
  });
};
//确认任务列表并跳转到调度
function saveAndCall() {
  let fun = () => {
    router.push({
      path: "/home/<USER>",
      replace: true,
      query: {
        requirementId: state.formRequirement.id,
      },
    });
  };
  fun();
  // saveTaskList(fun)
}
//确认任务列表并跳转到任务管理页面
function saveRequirementTask() {
  let fun = () => {
    router.push({
      path: "/home/<USER>",
      replace: true,
      query: {
        requirementId: state.formRequirement.id,
      },
    });
  };
  // saveTaskList(fun)
  fun();
}
/**
 * @description 取消新增/编辑
 */
const cancelAdd = () => {
  isShow.value = false;
  formRef.value.resetFields();
  formRef.value.clearValidate();
};
/**
 * @description 表格点击事件
 */
const handleTableClick = (row) => {
  state.awaitSubmit = false;
  tableRef.value.setCurrentRow(row);
  dataApi.getRequirementById({ id: row.id }).then((res) => {
    if (res.data.code == 200) {
      state.formRequirement = res.data.data;
      getTaskById();
    } else {
      ElMessage.error(res.data.message);
    }
  });
};
// 获取子任务列表
function getTaskById() {
  dataApi.getTaskById({ requirementId: state.formRequirement.id }).then((res) => {
    if (res.data.code == 200) {
      state.taskList = res.data.data;
      state.taskList.forEach((task, index) => {
        getTaskTargets(task, index);
      });
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
function getTaskTargets(row, index) {
  dataApi.getTaskTarget({ taskId: row.id }).then((res) => {
    if (res.data.code == 200) {
      state.taskList[index].targetList = res.data.data;
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
/**
 * @description 需求分解
 */
const handleResolveClick = (row) => {
  router.push({
    path: "/home/<USER>",
    replace: true,
  });
};
/**
 * @description 删除表格数据
 */
const handleDeleteClick = (val) => {
  dataApi.delRequirementById({ id: val.id }).then((res) => {
    if (res.data.code == 200) {
      ElMessage.success(res.data.message);
      getRequirementList();
    } else {
      ElMessage.error(res.data.message);
    }
  });
};
/**
 * @description 编辑表格数据
 */
let changeID = "";
const handleEditClick = (row) => {
  title.value = "编辑需求";
  dataApi.getRequirementById({ id: row.id }).then((res) => {
    if (res.data.code == 200) {
      state.auForm = res.data.data;
      isShow.value = true;
    } else {
      ElMessage.error(res.data.message);
    }
  });
};
function asyncTempData(data) {
  tempState.tempList = data;
}
/**
 * @description 组件销毁
 */
onUnmounted(() => {});
</script>
<style scoped lang="less">
@import "../../assets/style/theme/style.less";

.title-font {
  font-size: 18px;
  font-weight: bolder;
  color: #c6cdd6;
}

.span1 {
  color: rgba(@themeColor, 1);
  cursor: pointer;
}

.span2 {
  color: white;
  cursor: pointer;
}

.highlight-row {
  background-color: aqua;
}

.transport {
  .el-tree {
    font-size: large !important;
    margin-top: 3% !important;

    .el-tree-node {
      margin-top: 3% !important;
    }
  }

  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #81d4f9;
  }
}

.requir {
  height: 30%;
}

.textDescribe {
  top: 10px;
  position: relative;
  height: 65%;
  // border: 1px solid rgb(223, 235, 235);
  background-color: #031527a6;
  // color: #4f738c;
  line-height: 30px;
  padding: 5px 10px;
  box-sizing: border-box;
  overflow-y: auto;
}

.planButton {
  position: absolute;
  top: 2%;
  right: 5px;
  display: inline-flex;
  justify-content: center;
  align-items: center;

  > span {
    margin-right: 10px;
    cursor: pointer;
  }
}

.equip {
  position: relative;
  margin-top: 5%;
  padding-left: 10px;
  margin-bottom: 5%;
  cursor: pointer;
}

.equip:hover {
  color: #309eff;
}

:deep(.el-tree .el-tree-node) {
  color: #3a627d;
  font-size: 16px;
}

:deep(.el-tree .is-current) {
  font-size: 19px;
  font-weight: 600;
  color: #39b8ff;

  .el-tree-node__content {
    background-color: transparent !important;
  }
}
.requirement_form {
  :deep(.el-form-item) {
    margin-bottom: 0px !important;
  }
}
.tempBoxClass {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 1999;
  background: rgba(0, 0, 0, 0.5);
}
</style>
