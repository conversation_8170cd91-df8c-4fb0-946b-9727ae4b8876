<!--
 * @@Description: 任务列表
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-30 15:54:43
 * @FilePath: \src\views\home\taskPage.vue
-->
<template>
  <div style="
      width: 100%;
      height: 100%;
      position: relative;
      background-color: #9a949475;
    ">
    <!-- 左 -->
    <!-- 中 -->
    <!-- <div style="position: absolute; width: 60%; height: 100%; left: calc(16% + 11px);top:10px;"> -->
    <div style="
        position: absolute;
        width: 62%;
        height: 100%;
        left: 0.5%;
        top: 10px;
      ">
      <!-- 中-上 -->
      <div style="width: 100%; height: 59%">
        <customPopup width="100%" height="59%">
          <template #header>
            <span class="list_icon"></span>
            <span class="title-font">任务列表</span>
            <el-button style="position: absolute;right: 20px;" @click=" router.push({ path: '/home/<USER>'})">返回需求列表</el-button>
          </template>
          <template #content>
            <div style="display:flex">
              <span class="el-form-item__label">需求：</span>
              <!--   remote
                reserve-keyword
                remote-show-suffix -->
              <!-- <el-select-v2 v-model="state.requirementId" filterable clearable :options="state.requirementList" multiple collapse-tags
                @change="changeRequirementId" @clear="clearRequirementId" placeholder="输入需求名称关键词"
                style="width: 20%; margin-right: 20px" :disabled="state.submitTaskShow" /> -->

              <el-select v-model="state.requirementId" multiple  collapse-tags placeholder="输入需求名称关键词" clearable  style="width: 20%; margin-right: 20px"
              @change="changeRequirementId" @clear="clearRequirementId" :disabled="state.submitTaskShow">
                <el-option v-for="item in state.requirementList" :key="item.id" :label="item.requirementName"
                  :value="item.id"></el-option>
              </el-select>
              <!-- <el-select
                v-model="state.requirementId"
                filterable
                remote
                reserve-keyword
                remote-show-suffix
                placeholder="输入需求名称关键词"
                :remote-method="remoteMethod"
                clearable
                @focus
                :loading="state.loading"
                style="width: 25%"
              >
                <el-option
                  v-for="item in state.requirementList"
                  :key="item.id"
                  :label="item.requirementName"
                  :value="item.id"
                />
              </el-select> -->
              <span class="el-form-item__label">查询开始时间：</span>
              <el-date-picker v-model="state.startTime" type="datetime" placeholder="查询开始时间"
                style="width: 20%; margin-right: 20px" value-format="YYYY-MM-DD HH:mm:ss" />
              <span class="el-form-item__label">查询结束时间：</span>
              <el-date-picker v-model="state.endTime" type="datetime" placeholder="查询结束时间"
                style="width: 20%; margin-right: 20px" value-format="YYYY-MM-DD HH:mm:ss" />
              <!-- 任务状态：
              <el-select v-model="state.taskStatus" style="width: 15%">
                <el-option
                  v-for="(value, key) in dictTypeList['taskStatus']"
                  :key="key"
                  :label="value"
                  :value="Number(key)"
                />
              </el-select> -->
              <el-button style="
                  width: 6%;
                  padding-left: 40px;
                  left: 1%;
                  position: relative;
                  background-color: #1cb2ff;
                  color: #fff;
                  float: right;
                  margin-right: 13px;
                " @click="getTaskList">
                <img src="/public/images/icon/需求管理_15.png" style="position: absolute; left: 2px; top: -1px" />
                查询
              </el-button>
            </div>
            <div style="position: relative; top: 2%; height: calc(100% - 40px)">
              <customTable ref="tableRef" :tableData="state.tableData" :tableColumn="state.tableColumn"
                :total="pageInfo.total" :currentPage="pageInfo.page" :pageSize="pageInfo.size" :defaultCurrentRow="0"
                @currentPageEmit="currentPageEmit" @pageSizeEmit="pageSizeEmit" @nodeClickEmit="handleTableClick"
                @handleEditEmit="handleEditClick" @handleDeleteEmit="handleDeleteClick" @handleSelectionChangeEmit="handleSelectionChange"></customTable>
            </div>

            <div style="text-align: right;">
              <el-button @click="handlegoZydd" style="transform: translate(-7px,-20px)">资源调度</el-button>
            </div>
          </template>
        </customPopup>
      </div>
      <div style="width: 100%; height: 1%; background-color: transparent"></div>
      <!-- 中-下 -->
      <div style="width: 100%; height: 32%; top: 2%">
        <customPopup width="100%" height="32%" headType="2">
          <template #header>
            <span class="list_icon"></span>
            <span class="title-font">任务详情</span>
          </template>
          <template #content>
            <div class="requir">
              <el-form :model="state.formTask" label-width="120px" class="requirement_form">
                <!-- <div style="margin-bottom: 1%"> -->
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="任务名称：">
                      <span>{{ state.formTask.taskName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="目标名称：">
                      <span>{{ state.formTask?.target?.name }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="来源需求：">
                      <span>{{
                        requirementValue(state.formTask.requirementId)
                        }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="业务类型：">
                      <span>{{
                        dictValue('businessType', state.formTask.taskType)
                        }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="任务周期：">
                      <span>{{
                        dictValue('repetitionType', state.formTask.repeatType)
                        }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="任务状态：">
                      <span>{{
                        dictValue('taskStatus', state.formTask.status)
                        }}</span>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="6">
                    <el-form-item label="调度状态：">
                      <span>{{ dictValue("scheduleStatus", state.formTask.scheduleStatus) }}</span>
                    </el-form-item>
                  </el-col> -->
                  <el-col :span="12">
                    <el-form-item label="任务开始时间：">
                      <span>{{ timeSpan(state.formTask.startTime) }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="航迹开始时间：">
                      <span>{{
                        timeSpan(state.formTask?.target?.startTime)
                        }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="任务结束时间：">
                      <span>{{ timeSpan(state.formTask.endTime) }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="航迹结束时间：">
                      <span>{{
                        timeSpan(state.formTask?.target?.endTime)
                        }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <!-- <div class="textDescribe">
              <span v-html="state.formTask.taskComment"></span>
            </div> -->
          </template>
        </customPopup>
      </div>
    </div>
    <!-- 右 -->
    <!-- <div style="position: absolute; width: 22%; height: 100%; right: 15px;top:10px"> -->
    <div style="
        position: absolute;
        width: 36.5%;
        height: 100%;
        right: 0.5%;
        top: 10px;
      ">
      <customPopup1 width="100%" height="92%">
        <template #content>
          <CesiumBox />
        </template>
      </customPopup1>
    </div>
  </div>

  <div>
    <!-- 新建，编辑任务 -->
    <el-dialog v-model="isShow" :title="title" width="25%" center :close-on-click-modal="false">
      <div class="dialog_body">
        <el-form ref="formRef" :model="state.auForm" :rules="rules" label-width="140px">
          <el-form-item label="任务名称：" prop="taskName">
            <el-input v-model="state.auForm.taskName"></el-input>
          </el-form-item>
          <el-form-item label="业务类型:" prop="taskType">
            <!-- <el-input v-model="state.auForm.taskType"></el-input> -->
            <el-select v-model="state.auForm.taskType" placeholder="" clearable multiple style="width: 100%">
                            <el-option v-for="(value, key) in dictTypeList['businessType']" :key="key" :label="value"
                              :value="Number(key)"></el-option>
                          </el-select>
          </el-form-item>
          <el-form-item label="重要程度:" prop="importance">
            <!-- <el-input v-model="state.auForm.importance"></el-input> -->
            <el-select v-model.number="state.auForm.importance">
                  <el-option v-for="(value, key) in dictTypeList['importance']" :key="key" :label="value"
                    :value="Number(key)" />
                </el-select>
          </el-form-item>
          <el-form-item label="任务状态：" prop="status">
            <el-select v-model="state.auForm.status" disabled>
              <el-option v-for="(value, key) in dictTypeList['taskStatus']" :key="key" :label="value"
                :value="Number(key)" />
            </el-select>
          </el-form-item>
          <el-form-item label="任务开始时间：" prop="startTime">
            <el-date-picker style="width: 100%" v-model="state.auForm.startTime" type="datetime" placeholder="请选择时间"
              format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" @change="timeChange" />
          </el-form-item>
          <el-form-item label="任务结束时间：" prop="endTime">
            <el-date-picker style="width: 100%" v-model="state.auForm.endTime" type="datetime" placeholder="请选择时间"
              format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
          </el-form-item>

          <el-form-item label="任务描述：" prop="taskComment">
            <el-input type="textarea" rows="4" v-model="state.auForm.taskComment"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button style="width: 80px" @click="cancelAdd('edit')">取 消</el-button>
          <el-button style="width: 80px" type="primary" @click="saveAdd">保 存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- <el-dialog v-model="state.submitTaskShow" title="任务确认" width="60%" center :close-on-click-modal="false">
      <customTable
        :tableData="state.submitTaskList"
        :paginationShow="false"
        :tableColumn="state.submitTableColumn"
      ></customTable>
      <template #footer>
        <span class="dialog-footer">
          <el-button style="width: 80px" type="primary">保存任务(TODO)</el-button>
          <el-button style="width: 80px">保存任务并调度(TODO)</el-button>
        </span>
      </template>
    </el-dialog> -->
  </div>
</template>
<script setup>
  import { reactive, onMounted, onUnmounted, ref, nextTick, watch } from 'vue'
  import customPopup from '@/components/customPopup.vue'
  import customPopup1 from '@/components/customPopup1.vue'
  import customTable from '@/components/customTable.vue'
  import CesiumBox from '@/components/evmap/CesiumBox.vue'
  import * as dataApi from '@/service/API/home/<USER>'
  import * as areaApi from '@/service/API/system/areaManage.js'
  import { useRouter, useRoute } from 'vue-router'

  import { ElMessage, ElMessageBox } from 'element-plus'

  const isShow = ref(false)
  const tableRef = ref()
  const timeSpan = window.timeSpan
  const dictValue = window.dictValue
  const dictTypeList = window.dictTypeList
  // form表单
  let formRef = ref(null)
  const route = useRoute()
const router = useRouter()
  // 新增/修改
  const title = ref('')

  const checkTime = (rule, value, callback) => {
    if (!state.auForm.startTime || !state.auForm.endTime) {
      callback()
    } else {
      if (state.auForm.startTime >= state.auForm.endTime) {
        callback(new Error('结束时间必须大于开始时间'))
      } else {
        callback()
      }
    }
  }
  let tableSelectList = ref({})
  const handleSelectionChange = (data)=>{
    tableSelectList.value[pageInfo.page] = data
  }
  const handlegoZydd = (data)=>{
    const query = Object.values(tableSelectList.value).map(it=>{
      return Array.from(new Set( it.map(item=>item.requirementId.split('-')[0])))
    }).flat()

    // 收集任务ID
    const taskIds = Object.values(tableSelectList.value).map(it=>{
      return Array.from(new Set( it.map(item=>item.id)))
    }).flat()

    router.push({path:'/home/<USER>',query:{xq:query.join(), taskIds:taskIds.join()}})
  }

  const rules = {
    taskName: [{ required: true, message: '请输入需求名称' }],
    // requirementType: [{ required: true, type: "number", message: "请选择需求类型", trigger: "change" }],
    // importance: [{ required: true, type: "number", message: "请选择重要程度", trigger: "change" }],
    // status: [{ required: true, type: "number", message: "请选择需求状态", trigger: "change" }],
    // submitStatus: [{ required: true, type: "number", message: "请选择提交状态", trigger: "change" }],
    startTime: [
      {
        required: true,
        type: 'date',
        message: '请输入开始时间',
        trigger: 'change',
      },
      // { validator: checkTime, trigger: 'change' }
    ],
    endTime: [
      {
        required: true,
        type: 'date',
        message: '请输入结束时间',
        trigger: 'change',
      },
      { validator: checkTime, trigger: 'change' },
    ],
    // fileId: [{ required: true, message: "请上传轨迹文件", trigger: "change" }],
    // presetSource: [{ required: true, type: "array", message: "请选择预设资源", trigger: "change" }],
  }
  const state = reactive({
    submitTaskShow: false, //从需求分解页面跳转过来时需要打开需求确认框
    tableData: [], //任务表格数据
    keyword: '', //查询关键字
    auForm: {
      presetSource: [],
    }, //新增和修改需求用表单
    taskList: [], //任务列表
    submitTaskList: [], //需要确认的需求列表
    submitTableColumn: [
      { label: '任务名称', prop: 'taskName', type: 'String', width: '' },
      {
        label: '任务状态',
        prop: 'status',
        type: 'Sysdic',
        dicName: 'taskStatus',
        width: '',
      },

      { label: '任务开始时间', prop: 'startTime', type: 'Time', width: '' },
      { label: '任务结束时间', prop: 'endTime', type: 'Time', width: '' },
      {
        prop: '',
        label: '操作',
        type: 'operation',
        width: '150',
        operationTypeName: { handleEdit: true, handleDelete: true },
      },
    ], //需要确认的字段列表

    tableColumn: [
      /**      "id": "1803625719541325826",
          "requirementId": "1",
          "taskName": "需求A-任务B",
          "status": 0,
          "startTime": "2024-06-19 00:00:00",
          "endTime": "2024-06-21 00:00:00",
          "taskComment": "111",
          "targets": null */
    { label: "多选", prop: "requirementName", type: "selection", width: "50" },

      { label: '任务名称', prop: 'taskName', type: 'String', minWidth: '200' },
      {
        label: '任务状态',
        prop: 'status',
        type: 'Sysdic',
        dicName: 'taskStatus',
        minWidth: '100',
      },
      {
        label: '业务类型',
        prop: 'taskType',
        type: 'Sysdic',
        dicName: 'businessType',
        minWidth: '60',
      },
      {
        label: '重要程度',
        prop: 'importance',
        type: 'Sysdic',
        dicName: 'importance',
        minWidth: '60',
      },
      {
        label: '任务周期',
        prop: 'repeatType',
        type: 'Sysdic',
        dicName: 'repetitionType',
        minWidth: '100',
      },
      { label: '任务开始时间', prop: 'startTime', type: 'Time', minWidth: '160' },
      { label: '任务结束时间', prop: 'endTime', type: 'Time', minWidth: '160' },

      {
        prop: '',
        label: '操作',
        type: 'operation',
        width: '120',
        operationTypeName: { handleEdit: true, handleDelete: true },
      },
    ],
    requirementList: [], //需求列表
    requirementId: null, //需求ID
    formTask: {},
    scheduleTaskList: [], //周期任务列表
    scheduleTableColumn: [
      { label: '待执行任务', prop: 'taskName', type: 'String', width: '' },
      {
        label: '任务状态',
        prop: 'status',
        type: 'Sysdic',
        dicName: 'taskStatus',
        width: '',
      },
    ], //周期任务列名
    handleTime: '1H',
    nextHandleTime: '30S',
    loading: false, //等待状态
    startTime: undefined, //开始时间
    endTime: undefined, //结束时间
    pollingTime: 0, // 任务执行间隔时间
  })
  const areaState = reactive({
    areaList: [],
  })
  const pageInfo = reactive({
    page: 1,
    size: 10,
    total: 0,
  })
  /******************** function start*************************/
  onMounted(() => {
    remoteMethod()
    // if (window.taskPageParame?.requirementId) {
    //   state.requirementId = window.taskPageParame.requirementId;
    //   window.taskPageParame = {};
    // }
    // getTaskList();

    // queryPollingTime();
    // getScheduleTask();
    if(route.query.requirementIds){
      state.requirementId = route.query.requirementIds.split(',')
    }

  })
  /**
   * @description 获取周期任务列表
   */
  function getScheduleTask() {
    dataApi.getScheduleTask().then((res) => {
      if (res.data.code == 200) {
        state.scheduleTaskList = res.data.data
      } else {
        ElMessage.error(res.data.message)
      }
    })
  }
  //需求列表
  function remoteMethod(query) {
    // if (query) {
    // state.loading = true;
    dataApi.getRequirementList({ pageNum: 1, pageSize: 999 }).then((res) => {
      // state.loading = false;
      if (res.data.code == 200) {
        state.requirementList = res.data.data.records
        state.requirementList.forEach((item) => {
          item.value = item.id
          item.label = item.requirementName
        })
        setTimeout(() => {
          if (window.taskPageParame?.requirementId) {
            state.requirementId =[ window.taskPageParame.requirementId]
            window.taskPageParame = {}
            changeRequirementId()
          }
          getTaskList()
        }, 200)
      } else {
        ElMessage.error(res.data.message)
      }
    })
    // } else {
    //   options.value = [];
    // }
  }
  /**
   * @description 查询当前周期任务定时轮询时间
   */
  function queryPollingTime() {
    dataApi.queryPollingTime().then((res) => {
      if (res.data.code == 200) {
        let data = res.data.data
        state.pollingTime = data + 'S'
      } else {
        ElMessage.error(res.data.message)
      }
    })
  }
  /**
   * @description 获取需要确认和调度的任务列表
   */
  function getSubTaskList(requirementId) {
    dataApi
      .getTaskList({
        page: 1,
        size: 999,
        requirementIds: requirementId ? [requirementId] : [],
      })
      .then((res) => {
        if (res.data.code == 200) {
          state.submitTaskList = res.data.data.records
        } else {
          ElMessage.error(res.data.message)
        }
      })
  }
  // 清空
  function clearRequirementId() {
    state.startTime = undefined
    state.endTime = undefined
  }
  // 改变需求，设置默认时间
  function changeRequirementId() {
    // const data = state.requirementList.find(
    //   (item) => state.requirementId.includes(item.value)
    // )
    // if (!data) return
    // state.startTime = data.startTime
    // state.endTime = data.endTime
  }
  /**
   * @description 获取任务列表
   */
  function getTaskList() {
    dataApi
      .getTaskList({
        page: pageInfo.page,
        size: pageInfo.size,
        // requirementIds: [state.requirementId],
        requirementIds: state.requirementId ? state.requirementId : [],
        startTime: state.startTime ? state.startTime : undefined,
        endTime: state.endTime ? state.endTime : undefined,
      })
      .then((res) => {
        if (res.data.code == 200) {
          state.tableData = res.data.data.records
          pageInfo.total = res.data.data.total
          if (state.tableData.length > 0) {
            handleTableClick(state.tableData[0])
            setTimeout(() => {
              tableRef.value.setCurrentRow(state.tableData[0])
            }, 200)
          }
        } else {
          ElMessage.error(res.data.message)
        }
      })
  }
  function currentPageEmit(val) {
    pageInfo.page = val
    getTaskList()
  }
  function pageSizeEmit(val) {
    pageInfo.size = val
    getTaskList()
    // getAreaGeneralData(currentTreeNode);
  }
  //调用校验
  function timeChange() {
    formRef.value.validateField('endTime')
  }
  /**
   * @description 保存新增/修改
   */
  function formSubmit() {
    dataApi.upTaskInfo(state.auForm).then((res) => {
      if (res.data.code == 200) {
        getTaskList()
        isShow.value = false
        formRef.value.resetFields()
        formRef.value.clearValidate()
      } else {
        ElMessage.error(res.data.message)
      }
    })
  }
  // 保存按钮
  const saveAdd = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        formSubmit()
      }
    })
  }
  /**
   * @description 取消新增/编辑
   */
  const cancelAdd = () => {
    isShow.value = false
    formRef.value.resetFields()
    formRef.value.clearValidate()
  }
  /**
   * @description 表格点击事件
   */
  const handleTableClick = (row) => {
    mapTool.removeAll()
    window.EVGISMAP('removeGroupEntityById', {
      id: state.formTask.id + '_taskLine',
      group: 'DRAWLAYERGROUP',
    })
    dataApi.getTaskByTaskId({ id: row.id }).then((res) => {
      if (res.data.code == 200) {
        state.formTask = res.data.data
      } else {
        ElMessage.error(res.data.message)
      }
    })
    getTaskTargets(row)
  }
  //获取任务目标
  function getTaskTargets(row) {
    dataApi.getTaskTarget({ taskId: row.id, isInterpolated: "false" }).then((res) => {
      if (res.data.code == 200) {
        let target = res.data.data
        if (target.id && target.taskCoverPoint) {
          mapTool.drawMap({ ...target, group: 'DRAWLAYERGROUP', outlineWidth: 2 })
          let pos = []
          // if(!target.taskCoverPoint){
          //   return
          // }
          target.taskCoverPoint.forEach((item, index) => {

            pos[index] = [item[1], item[0]]

          })

          // window.EVGISMAP('flyTo', {
          //   center: [pos[0][0], pos[0][1], target.viewHeight + 600000 || 1000000],
          // })
          const height = FlyToHeightConfig[state.requirementId] || 1000000


          let pos1 = pos.filter((v) => {
            if (
              typeof v[0] === 'number' &&
              typeof v[1] === 'number' &&
              !(Number.isNaN(v[0]) && Number.isNaN(v[1]))
            ) {
              return [v[0] + 0.01, v[1] + 0.01, 0]
            }
          })

          Viewer.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(
              pos1[0][0],
              pos1[0][1],
              target.viewHeight || height
            ),
          })
          window.EVGISMAP('drawPolylineByAttr', {
            id: state.formTask.id + '_taskLine',
            // group: "DRAWLAYERGROUP",
            // position: pos, //[120, 30,10000000],option
            position: pos1, //[120, 30,10000000],option
            group: 'DRAWLAYERGROUP',
            outlineColor: 'yellow',
            outlineWidth: 5,
            zIndex: 1,
          })
        }
      } else {
        ElMessage.error(res.data.message)
      }
    })
  }
  function requirementValue(id) {
    let requirement = state.requirementList.find((item) => {
      return item.id == id
    })
    return requirement ? requirement.requirementName : '查询不到对应需求'
  }
  /**
   * @description 删除表格数据
   */
  const handleDeleteClick = (val) => {
    dataApi.delTaskByTaskId({ id: val.id }).then((res) => {
      if (res.data.code == 200) {
        getTaskList()
        ElMessage.success(res.data.message)
      } else {
        ElMessage.error(res.data.message)
      }
    })
  }
  /**
   * @description 编辑表格数据
   */
  let changeID = ''
  const handleEditClick = (row) => {
    title.value = '编辑任务'
    dataApi.getTaskByTaskId({ id: row.id }).then((res) => {
      if (res.data.code == 200) {
        state.auForm = res.data.data
        isShow.value = true
      } else {
        ElMessage.error(res.data.message)
      }
    })
  }
  /**
   * @description 组件销毁
   */
  onUnmounted(() => {
    mapTool.removeAll()
  })
</script>
<style scoped lang="less">
  @import '../../assets/style/theme/style.less';

  .title-font {
    font-size: 18px;
    font-weight: bolder;
    color: #c6cdd6;
  }

  .span1 {
    color: rgba(@themeColor, 1);
    cursor: pointer;
  }

  .span2 {
    color: white;
    cursor: pointer;
  }

  .highlight-row {
    background-color: aqua;
  }

  .transport {
    .el-tree {
      font-size: large !important;
      margin-top: 3% !important;

      .el-tree-node {
        margin-top: 3% !important;
      }
    }

    .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
      background-color: #81d4f9;
    }
  }

  .requir {
    height: 100%;
    padding: 0 10%;
  }

  .textDescribe {
    top: calc(30% + 25px);
    position: relative;
    height: 30%;
    // border: 1px solid rgb(223, 235, 235);
    background-color: #031527a6;
    // color: #4f738c;
    line-height: 30px;
    padding: 5px 10px;
    box-sizing: border-box;
    overflow-y: auto;
  }

  .planButton {
    position: absolute;
    top: 2%;
    right: 5px;
    display: inline-flex;
    justify-content: center;
    align-items: center;

    >span {
      margin-right: 10px;
      cursor: pointer;
    }
  }

  .equip {
    position: relative;
    margin-top: 5%;
    padding-left: 10px;
    margin-bottom: 5%;
    cursor: pointer;
  }

  .equip:hover {
    color: #309eff;
  }

  :deep(.el-tree .el-tree-node) {
    color: #3a627d;
    font-size: 16px;
  }

  :deep(.el-tree .is-current) {
    font-size: 19px;
    font-weight: 600;
    color: #39b8ff;

    .el-tree-node__content {
      background-color: transparent !important;
    }
  }

  .requirement_form {
    :deep(.el-form-item) {
      margin-bottom: 10px !important;
    }
  }

  .tempBoxClass {
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 1999;
    background: rgba(0, 0, 0, 0.5);
  }
</style>
