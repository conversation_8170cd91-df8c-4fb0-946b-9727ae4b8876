<!--
 * @Author: JRX <EMAIL>
 * @Date: 2024-03-26 16:55:34
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-31 10:08:41
 * @FilePath: \wrxtzhglrj\src\views\home\HomePage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @@Description: 需求管理
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-06-18 15:30:48
 * @FilePath: \qbmbtsweb\src\views\home\HomePage.vue
-->
<template>
  <!-- <div style="width: 100vw; height: 100vh; position: fixed"><CesiumBox></CesiumBox></div> -->
  <div
    style="
      width: 100%;
      height: 100%;
      position: relative;
      background-color: #9a949475;
    "
  >
    <!-- 左 -->
    <!-- 中 -->
    <!-- <div style="position: absolute; width: 60%; height: 100%; left: calc(16% + 11px);top:10px;"> -->
    <div
      style="
        position: absolute;
        width: 99%;
        height: 100%;
        left: 0.5%;
        top: 10px;
      "
    >
      <!-- 中-上 -->
      <div style="width: 100%; height: 59%">
        <customPopup width="100%" height="59%">
          <template #header>
            <span class="list_icon"></span>
            <span class="title-font">需求列表</span>
            <div class="planButton">
              <!-- <img src="/public/images/icon/需求管理_06.png" alt="" />
              <span @click="getFileTemplate">文件模板</span> -->
              <img src="/public/images/icon/需求管理_03.png" alt="" />
              <span @click="requirementfileUpload">需求文件导入</span>
              <!-- <input type="file" id="requirementfile" style="display: none !important;"
                @change="uploadRequirementFile"></input> -->
              <img src="/public/images/icon/需求管理_03.png" alt="" />
              <span @click="newPlan">新建需求</span>
              <img src="/public/images/icon/需求管理_06.png" alt="" />
              <span @click="showTemplat">模板管理</span>
            </div>
          </template>
          <template #content>
            <div></div>
            <div>
              <el-input
                v-model="state.keyword"
                placeholder="请输入关键字"
                style="width: 84%"
              />
              <el-button
                style="
                  width: 6%;
                  padding-left: 40px;
                  left: 1%;
                  position: relative;
                  background-color: #1cb2ff;
                  color: #fff;
                "
                @click="getRequirementList()"
              >
                <img
                  src="/public/images/icon/需求管理_15.png"
                  style="position: absolute; left: 2px; top: -1px"
                />
                搜索
              </el-button>
            </div>
            <div style="position: relative; top: 1%; height: calc(100% - 40px)">
              <customTable
                :tableData="state.tableData"
                :tableColumn="state.tableColumn"
                :total="pageInfo.total"
                :currentPage="pageInfo.page"
                :pageSize="pageInfo.size"
                :defaultCurrentRow="0"
                ref="tableRef"
                @currentPageEmit="currentPageEmit"
                @pageSizeEmit="pageSizeEmit"
                @nodeClickEmit="handleTableClick"
                @handleEditEmit="handleEditClick"
                @handleDeleteEmit="handleDeleteClick"
                @handleDemandEmit="handleDemand"
                @handleDetailEmit="handleDetail"
                @handleSubEmit="handleSubEmit"
                @handleSelectionChangeEmit="handleSelectionChange"
              ></customTable>
            </div>
            <el-button
              style="
                width: 6%;
                background-color: #1cb2ff;
                color: #fff;
                position: absolute;
                right: 0;
                transform: translate(-20px, -20px);
              "
              @click="handleXQFJ"
            >
              <img
                src="/public/images/icon/需求分解.png"
                style="position: absolute; left: 4px; top: 7px"
              />
              需求分解
            </el-button>
          </template>
        </customPopup>
      </div>
      <div style="width: 100%; height: 1%; background-color: transparent"></div>
      <!-- 中-下 -->
      <div style="width: 100%; height: 32%; top: 2%">
        <customPopup width="100%" height="32%" headType="2">
          <template #header>
            <span class="list_icon"></span>
            <span class="title-font">需求详情</span>
          </template>
          <template #content>
            <div class="requir">
              <el-form
                :model="state.formRequirement"
                label-width="120px"
                class="requirement_form"
              >
                <!-- <div style="margin-bottom: 1%"> -->
                <el-row>
                  <el-col :span="6">
                    <el-form-item label="需求名称：">
                      <span>{{ state.formRequirement.requirementName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="需求类型：">
                      <span>{{
                        dictValue(
                          'requirementType',
                          state.formRequirement.requirementType
                        )
                      }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="重要程度：">
                      <span>{{
                        dictValue(
                          'importance',
                          state.formRequirement.importance
                        )
                      }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="需求来源：">
                      <span>{{
                        dictValue('source', state.formRequirement.source)
                      }}</span>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="6">
                    <el-form-item label="需求状态：">
                      <span>{{ dictValue("requirementStatus", state.formRequirement.status) }}</span>
                    </el-form-item>
                  </el-col> -->
                  <el-col :span="6">
                    <el-form-item label="开始时间：">
                      <span>{{
                        timeSpan(state.formRequirement.startTime)
                      }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="结束时间：">
                      <span>{{ timeSpan(state.formRequirement.endTime) }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <div class="textDescribe">
              <span v-html="state.formRequirement.requirementComment"></span>
            </div>
          </template>
        </customPopup>
      </div>
    </div>
  </div>
  <div
    style="
      width: 50vw;
      height: 60vh;
      position: fixed;
      top: 10%;
      left: 25%;
      z-index: 1989;
    "
    v-if="state.showTaskPage"
  >
    <customPopup width="100%" height="100%" left="0" top="0">
      <template #header>
        <span class="list_icon"></span>
        <span class="title-font">需求任务</span>
        <div style="position: absolute; right: 2%">
          <el-button plain @click="state.showTaskPage = false">关闭</el-button>
        </div>
      </template>
      <template #content>
        <div style="height: 55vh">
          <!-- <div v-for="(item, index) in state.taskList" :key="index" style="margin-top: 25px">
            <div class="taskTitle">
              <img src="/public/images/icon/需求管理_09.png" alt="" />
              {{ item.taskName }}({{ item.startTime }} - {{ item.endTime }})
            </div>
            <div class="taskTarget" v-for="target in item.targetList" :key="target.id">
              <img src="/public/images/icon/需求管理_18.png" alt="" />
              {{ target.name }}
            </div>
            </div> -->
          <customTable
            :tableData="state.taskList"
            :paginationShow="false"
            :tableColumn="tableColumn"
          ></customTable>
        </div>
      </template>
    </customPopup>
  </div>
  <!-- 新建，编辑需求 -->
  <div>
    <!-- :width="state.showTargetSet ? '90%' : '30%'" -->
    <el-dialog
      v-model="isShow"
      :title="title"
      width="30%"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="dialog_body">
        <el-row :gutter="20" style="height: 60vh">
          <!-- :span="state.showTargetSet ? 8 : 24" -->
          <el-col :span="24">
            <el-form
              ref="formRef"
              :model="state.auForm"
              :rules="rules"
              label-width="95px"
            >
              <el-form-item label="需求模板：" v-if="title == '新建需求'">
                <el-select
                  @change="tempChange"
                  @clear="tempClear"
                  v-model="tempState.currentTemp"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    :value="item.id"
                    :label="item.templateName"
                    v-for="item in tempState.tempList"
                    :key="item.id"
                  ></el-option>
                </el-select>
                <!-- <el-button @click="getFileTemplate" style="width: 120px; margin-left: 10px">文件模板下载</el-button> -->
              </el-form-item>

              <el-form-item label="需求名称：" prop="requirementName">
                <el-input v-model="state.auForm.requirementName"></el-input>
              </el-form-item>
              <el-form-item label="需求类型：" prop="requirementType">
                <el-select v-model="state.auForm.requirementType">
                  <el-option
                    v-for="(value, key) in dictTypeList['requirementType']"
                    :key="key"
                    :label="value"
                    :value="Number(key)"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="重要程度：" prop="importance">
                <el-select v-model.number="state.auForm.importance">
                  <el-option
                    v-for="(value, key) in dictTypeList['importance']"
                    :key="key"
                    :label="value"
                    :value="Number(key)"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="开始时间：" prop="startTime">
                <el-date-picker
                  style="width: 100%"
                  v-model="state.auForm.startTime"
                  type="datetime"
                  placeholder="请选择时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
              <el-form-item label="结束时间：" prop="endTime">
                <el-date-picker
                  style="width: 100%"
                  v-model="state.auForm.endTime"
                  type="datetime"
                  placeholder="请选择时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
              <el-form-item label="测控目标：" prop="targetInfos">
                <!-- <el-button @click="state.showTargetSet = !state.showTargetSet">测控配置</el-button> -->
                <el-button @click="openTargetDialog">新增测控目标</el-button>
                <el-dialog
                  v-model="state.newTargetShow"
                  title="测控目标"
                  width="30%"
                  center
                  :close-on-click-modal="true"
                >
                  <span class="el-form-item__label">测控目标：</span>
                  <el-cascader
                    :props="cascaderProps"
                    :show-all-levels="false"
                    style="width: 70%"
                    ref="targetNewRef"
                    v-model="newTargetForm.targetId"
                    :options="state.cascaderOption"
                    @visible-change="
                      (flag) => {
                        flag && (state.cascaderOption = [])
                      }
                    "
                    @change="targetIdNewChange"
                  />
                  <!--  -->
                  <template #footer>
                    <span class="dialog-footer">
                      <el-button @click="state.newTargetShow = false"
                        >取消</el-button
                      >
                      <el-button @click="addNewTarget">添加</el-button>
                    </span>
                  </template>
                </el-dialog>
                <div class="targetItem_box">
                  <div
                    class="targetItem"
                    :underline="false"
                    v-for="(fromTarget, targetIndex) in state.formTargetList"
                    :key="targetIndex"
                  >
                    <div
                      class="targetItem_text"
                      @click="showTargetEdit(fromTarget, targetIndex)"
                    >
                      {{ fromTarget.targetName }}
                    </div>
                    <el-link
                      class="targetItem_icon"
                      :underline="false"
                      @click="removeTarget(targetIndex)"
                    >
                      <el-icon>
                        <Close />
                      </el-icon>
                    </el-link>
                  </div>
                  <targetEdit
                    :show="targetEditForm.show"
                    :requirementForm="state.auForm"
                    :form="targetEditForm.targetForm"
                    @changeEmits="editTarget"
                    @close="targetEditForm.show = false"
                  >
                  </targetEdit>
                </div>
              </el-form-item>
              <el-form-item label="需求描述：" prop="requirementComment">
                <el-input
                  type="textarea"
                  rows="4"
                  v-model="state.auForm.requirementComment"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
          <!-- state.showTargetSet -->
          <el-col
            :span="16"
            v-show="false"
            style="
              padding: 0 20px;
              border-left: 1px solid #255788;
              height: 100%;
              box-sizing: border-box;
            "
          >
            <div style="width: 100%; height: 100%">
              <div
                style="
                  height: 30px;
                  width: 100%;
                  border-bottom: 1px;
                  padding-bottom: 10px;
                  margin-bottom: 10px;
                "
              >
                <el-button @click="addTargetRow">增加</el-button>
              </div>
              <div
                style="
                  height: calc(100% - 50px);
                  overflow-y: auto;
                  width: 100%;
                  padding-right: 5px;
                "
              >
                <!-- <transition name="el-zoom-in-top">
        <div v-show="show" class="transition-box">.el-zoom-in-top</div>
      </transition> -->
                <el-card
                  v-for="(fromTarget, targetIndex) in state.formTargetList"
                  :key="targetIndex"
                  style="margin-bottom: 30px"
                >
                  <template #header>
                    <el-row :gutter="0">
                      <el-col :span="6">
                        目标：
                        <el-cascader
                          :props="cascaderProps"
                          :show-all-levels="false"
                          style="width: 70%"
                          :options="state.echoOption[targetIndex]"
                          :key="state.cascaderKey + targetIndex"
                          :ref="
                            (el) => {
                              setItemRef(el, targetIndex)
                            }
                          "
                          v-model="fromTarget.targetId"
                          @change="
                            (val) => {
                              targetIdChange(val, targetIndex)
                            }
                          "
                          @visible-change="
                            (flag) => {
                              flag && (state.echoOption[targetIndex] = [])
                            }
                          "
                        />
                      </el-col>
                      <el-col :span="6">
                        航迹：
                        <el-select
                          v-model="fromTarget.trackPresetId"
                          style="width: 70%"
                          placeholder="请选择"
                          clearable
                        >
                          <el-option
                            v-for="item in state.trackManagerList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                          ></el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="12">
                        航迹时间：
                        <el-time-picker
                          style="width: 35%"
                          v-model="fromTarget.trackStartTime"
                          placeholder="请选择开始时间"
                          format="HH:mm:ss"
                          value-format="HH:mm:ss"
                          @change="calcEndTime(fromTarget, targetIndex)"
                        />
                        -
                        <el-time-picker
                          disabled
                          style="width: 35%"
                          v-model="fromTarget.trackEndTime"
                          placeholder=""
                          format="HH:mm:ss"
                          value-format="HH:mm:ss"
                        />
                        <el-button
                          @click="delTargetRow(targetIndex)"
                          style="margin-left: 4px"
                          >删除</el-button
                        >
                      </el-col>
                    </el-row>
                  </template>
                  <div>
                    <el-table :data="fromTarget.tasks" class="custom-table">
                      <el-table-column type="index" width="50" align="center" />
                      <el-table-column label="业务类型" align="center">
                        <template #default="{ row }">
                          <el-select
                            v-model="row.taskType"
                            placeholder=""
                            clearable
                            multiple
                            style="width: 90%"
                          >
                            <el-option
                              v-for="(value, key) in dictTypeList[
                                'businessType'
                              ]"
                              :key="key"
                              :label="value"
                              :value="Number(key)"
                            ></el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column label="开始时间" align="center">
                        <template #default="{ row }">
                          <el-time-picker
                            style="width: 90%"
                            v-model="row.startTime"
                            placeholder="请选择时间"
                            format="HH:mm:ss"
                            value-format="HH:mm:ss"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column label="结束时间" align="center">
                        <template #default="{ row }">
                          <el-time-picker
                            style="width: 90%"
                            v-model="row.endTime"
                            placeholder="请选择时间"
                            format=" HH:mm:ss"
                            value-format="HH:mm:ss"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column label="重复" width="150" align="center">
                        <template #default="{ row }">
                          <el-select
                            v-model="row.repeatType"
                            placeholder=""
                            clearable
                            style="width: 90%"
                          >
                            <el-option
                              v-for="(value, key) in dictTypeList[
                                'repetitionType'
                              ]"
                              :key="key"
                              :label="value"
                              :value="Number(key)"
                            ></el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="100" align="center">
                        <template #header="scope">
                          <el-button @click="addBusinessRow(targetIndex)"
                            >新增</el-button
                          >
                        </template>
                        <template #default="scope">
                          <el-button
                            @click="delBusinessRow(targetIndex, scope.$index)"
                            >删除</el-button
                          >
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-card>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button style="width: 80px" @click="cancelAdd('edit')"
            >取 消</el-button
          >
          <el-button style="width: 80px" type="primary" @click="saveTemplate"
            >保存为模板</el-button
          >
          <el-button style="width: 80px" type="primary" @click="saveAdd"
            >保存并分解</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
  <div class="tempBoxClass" v-show="tempState.show">
    <!-- :show="tempState.show" -->
    <tempManage
      ref="tempRef"
      :requirementForm="state.auForm"
      @asyncTempData="asyncTempData"
      @close="tempState.show = false"
      :show="tempState.show"
    />
  </div>
</template>
<script setup>
import { reactive, onMounted, onUnmounted, ref, nextTick, watch } from 'vue'
import customWindow from '@/components/customWindow.vue'
import customPopup from '@/components/customPopup.vue'
// import dataTable from "./components/dataTable.vue";
// import newDataTable from "./components/newDataTable.vue";
import customTable from '@/components/customTable.vue'
import { useRouter } from 'vue-router'
import CesiumBox from '@/components/evmap/CesiumBox.vue'
import { routerStore } from '@/stores/routerStore'
import * as dataApi from '@/service/API/home/<USER>'
import * as areaApi from '@/service/API/system/areaManage.js'
import * as trackApi from '@/service/API/system/trackManager.js'
import targetEdit from './components/targetEdit.vue'

import tempManage from './tempManage.vue'

import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { useUserStore } from '@/stores/index'
const userStore = useUserStore()

// 监听消息框中文件解析，成功后打开新增需求框，并填充数据
watch(
  () => userStore.emailRequirementHandle,
  (data) => {
    console.log(data)
    getRequirementList()
  }
)
const routerstore = routerStore() // 路由的状态管理器
const router = useRouter()
const isShow = ref(false)
const timeSpan = window.timeSpan
const dictValue = window.dictValue
const dictTypeList = window.dictTypeList
const tempRef = ref(null)
const tempState = reactive({
  show: false,
  tempList: [],
  currentTemp: null,
})
const cascaderProps = {
  multiple: false,
  emitPath: false,
  lazy: true,
  lazyLoad: lazyLoad,
  value: 'value',
  label: 'label',
  children: 'children',
  leaf: 'leaf',
  disabled: 'disabled',
}
let tableRef = ref(null)
// form表单
let formRef = ref(null)
let uploadFileRef = ref(null)

// 新增/修改
const title = ref('')
const validateTarger = (rule, value, callback) => {
  if (state.formTargetList.length == 0) {
    callback(new Error('请至少添加一个目标'))
    return
  }
  for (const item of state.formTargetList) {
    if (!item.targetId) {
      callback(new Error('有目标行未选择目标'))
      return
    }
    if (!item.trackPresetId) {
      callback(new Error('有目标行未选择航迹'))
      return
    }
  }
  callback()
}
const rules = {
  requirementName: [{ required: true, message: '请输入需求名称' }],
  requirementType: [
    {
      required: true,
      type: 'number',
      message: '请选择需求类型',
      trigger: 'change',
    },
  ],
  scheduleType: [
    {
      required: true,
      type: 'number',
      message: '请选择调度类型',
      trigger: 'change',
    },
  ],
  importance: [
    {
      required: true,
      type: 'number',
      message: '请选择重要程度',
      trigger: 'change',
    },
  ],
  status: [
    {
      required: true,
      type: 'number',
      message: '请选择需求状态',
      trigger: 'change',
    },
  ],
  source: [
    {
      required: true,
      type: 'number',
      message: '请选择需求来源',
      trigger: 'change',
    },
  ],
  startTime: [
    {
      required: true,
      type: 'date',
      message: '请输入开始时间',
      trigger: 'change',
    },
  ],
  endTime: [
    {
      required: true,
      type: 'date',
      message: '请输入结束时间',
      trigger: 'change',
    },
  ],
  targetInfos: [{ required: true, validator: validateTarger, trigger: 'blur' }],
  // fileId: [{ required: true, message: "请上传轨迹文件", trigger: "change" }],
  // presetSource: [{ required: true, type: "array", message: "请选择预设资源", trigger: "change" }],
}
const state = reactive({
  tableData: [], //需求表格数据
  keyword: '', //查询关键字
  auForm: {
    targetInfos: [],
    presetSource: [],
  }, //新增和修改需求用表单
  taskList: [], //任务列表
  tableColumn: [
    { label: '多选', prop: 'requirementName', type: 'selection', width: '50' },
    { label: '需求名称', prop: 'requirementName', type: 'String', width: '' },
    { label: '需求类型', prop: 'requirementType', type: 'Sysdic', width: '' },
    { label: '重要程度', prop: 'importance', type: 'Sysdic', width: '' },
    // {
    //   label: "需求状态",
    //   prop: "status",
    //   type: "Sysdic",
    //   width: "",
    //   dicName: "requirementStatus",
    // },
    { label: '需求来源', prop: 'source', type: 'Sysdic', width: '' },
    { label: '开始时间', prop: 'startTime', type: 'Time', width: '' },
    { label: '结束时间', prop: 'endTime', type: 'Time', width: '' },
    // { label: "描述", prop: "requirementComment", type: "String", width: "" },
    {
      prop: '',
      label: '操作',
      type: 'operation',
      width: '200',
      // handleEdit: true,
      operationTypeName: {
        demand: false,
        handleDelete: true,
        handleDetail: true,
        handleEdit: false,
        handleSub: true,
      },
    },
  ],
  fileList: [], //弹窗上传的文件列表

  formRequirement: {}, //需求信息
  awaitSubmit: false, //等待确认
  showTargetSet: false, //目标配置部分显示
  formTargetList: [], //新建需求中的目标列表
  trackManagerList: [], //航迹列表管理
  showTaskPage: false, //展示
  echoOption: [],
  cascaderOption: [],
  cascaderKey: 1,
  newTargetShow: false,
  targetConfigShow: false,
})
const newTargetForm = reactive({
  show: false,
  targetId: undefined,
  targetName: undefined,
  generalId: undefined,
})
const tableColumn = [
  { label: '任务名称', prop: 'taskName', type: 'String', width: '' },
  {
    label: '任务状态',
    prop: 'status',
    type: 'Sysdic',
    dicName: 'taskStatus',
    width: '',
  },
  { label: '开始时间', prop: 'startTime', type: 'Time', width: '' },
  { label: '结束时间', prop: 'endTime', type: 'Time', width: '' },
]
const areaState = reactive({
  areaList: [],
})
const pageInfo = reactive({
  page: 1,
  size: 10,
  total: 0,
})
const targetEditForm = reactive({
  targetForm: {
    tasks: [],
  },
  show: false,
  targetIndex: undefined,
})
let tableSelectList = ref({})
const handleSelectionChange = (data) => {
  tableSelectList.value[pageInfo.page] = data
}
const handleXQFJ = () => {
  const query = Object.values(tableSelectList.value)
    .map((it) => {
      return Array.from(new Set(it.map((item) => item.id.split('-')[0])))
    })
    .flat()

  // 显示带进度条的loading页面
  showProgressLoading(() => {
    router.push({
      path: '/home/<USER>',
      query: { requirementIds: query.join() },
    })
  })
}

// 显示带进度条的loading页面
const showProgressLoading = (callback) => {
  // 创建loading实例
  const loading = ElLoading.service({
    lock: true,
    text: '需求分解中...',
    background: 'rgba(0, 0, 0, 0.8)',
    customClass: 'custom-loading-with-progress',
  })

  // 创建进度条元素
  const progressContainer = document.createElement('div')
  progressContainer.innerHTML = `
    <div style="
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 9999;
      text-align: center;
      color: white;
    ">
      <div style="margin-bottom: 20px; font-size: 16px;">需求分解中...</div>
      <div style="
        width: 300px;
        height: 6px;
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 10px;
      ">
        <div id="progress-bar" style="
          width: 0%;
          height: 100%;
          background: linear-gradient(90deg, #409EFF 0%, #67C23A 100%);
          border-radius: 3px;
          transition: width 0.1s ease;
        "></div>
      </div>
      <div id="progress-text" style="font-size: 14px; color: #ccc;">0%</div>
    </div>
  `
  document.body.appendChild(progressContainer)

  // 进度条动画
  let progress = 0
  const progressBar = document.getElementById('progress-bar')
  const progressText = document.getElementById('progress-text')

  const interval = setInterval(() => {
    progress += 2 // 每50ms增加2%，总共1秒完成
    if (progress >= 100) {
      progress = 100
      clearInterval(interval)

      // 进度完成后，延迟一点时间再关闭loading
      setTimeout(() => {
        loading.close()
        document.body.removeChild(progressContainer)
        callback() // 执行回调函数（路由跳转）
      }, 100)
    }

    progressBar.style.width = progress + '%'
    progressText.textContent = progress + '%'
  }, 50) // 每50ms更新一次，1秒内完成
}
/******************** function start*************************/ 3
onMounted(() => {
  getRequirementList()
  getTrackManagerList()
})
//获取需求列表
function getRequirementList(row) {
  dataApi
    .getRequirementList({
      keyword: state.keyword,
      pageNum: pageInfo.page,
      pageSize: pageInfo.size,
    })
    .then((res) => {
      if (res.data.code == 200) {
        state.tableData = res.data.data.records
        pageInfo.total = res.data.data.total
        if (state.tableData.length > 0) {
          handleTableClick(row || state.tableData[0])
        }
      } else {
        ElMessage.error(res.data.message)
      }
    })
}
//获取航迹管理列表
function getTrackManagerList() {
  trackApi.getPresetList().then((res) => {
    if (res.data.code == 200) {
      state.trackManagerList = res.data.data.records
    } else {
      ElMessage.error(res.data.message)
    }
  })
}

//翻页
function currentPageEmit(val) {
  pageInfo.page = val
  getRequirementList()
}
function pageSizeEmit(val) {
  pageInfo.size = val
  getRequirementList()
  // getAreaGeneralData(currentTreeNode);
}
//clearFile
function clearFile() {
  state.auForm.fileId = null
  state.auForm.fileName = null
  state.auForm.file = {}
  // state.fileList = [];
}
// 文件上传
function uploadFile(file, uploadFiles) {
  // console.log("uploadFile", uploadFile, UploadFiles);
  // return;
  // state.fileList = UploadFiles;

  var upload = () => {
    let formData = new FormData()
    let bzPath = ''
    formData.append('file', file.raw)
    formData.append('bzPath', bzPath)
    dataApi.uploadFile(formData).then((res) => {
      if (res.data.code == 200) {
        ElMessage.success('上传成功')
        // console.log(res.data.data);
        state.auForm.fileId = res.data.data
        state.auForm.fileName = file.name
        uploadFileRef.value.clearFiles()
      } else {
        ElMessage.error(res.data.message)
      }
    })
  }
  if (uploadFiles.length == 0) {
    return
  }
  if (state.auForm.fileId) {
    ElMessageBox.confirm('继续上传将覆盖前一文件，是否继续?', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      upload()
    })
  } else {
    upload()
  }
}
// 上传文件
function requirementfileUpload() {
  let input = document.createElement('input')
  input.type = 'file'
  input.id = 'requirementfile'
  input.style.display = 'none'
  input.addEventListener('change', uploadRequirementFile)
  input.click()
}
function uploadRequirementFile(event) {
  let filelist = event.target.files
  if (!filelist.length) {
    ElMessage.warning('请选择需求文件')
    return
  }
  let file = new FormData()
  for (let i = 0; i < filelist.length; i++) {
    // const element = array[i];
    file.append('file', filelist[i])
  }
  event.target.remove()
  // document.getElementById("requirementfile").remove()
  dataApi.uploadRequirementFile(file).then((res) => {
    if (res.data.code == 200) {
      ElMessage.success('上传成功')
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
/**
 * @description 新建需求
 */
// 新建需求
const newPlan = () => {
  title.value = '新建需求'
  isShow.value = true
  state.auForm = {
    source: 1,
    targetInfos: [],
    presetSource: [],
  }
  tempState.currentTemp = null
  state.showTargetSet = false
  state.formTargetList = []
  setTimeout(() => {
    formRef.value.clearValidate()
  }, 400)
}

//动态加载级联选择器
var areaId = null
var areaGeneralTree = []
function lazyLoad(node, resolve) {
  const { level, value } = node
  console.log(node)
  if (level == 0) {
    areaApi.getAreaList({ page: 1, size: 999, areaType: 2 }).then((res) => {
      if (res.data.code == 200) {
        let areaList = []
        res.data.data.records.forEach((item) => {
          areaList.push({
            value: item.id,
            label: item.areaName,
            leaf: false,
          })
        })
        resolve(areaList)
      } else {
        ElMessage.error(res.data.message)
      }
    })
  } else if (level == 1) {
    areaId = value
    areaApi.getAreaGeneralTree({ areaId: value }).then((res) => {
      if (res.data.code) {
        areaGeneralTree = res.data.data
        let obj = {}
        res.data.data.forEach((item) => {
          obj[item.dataType] = item.dataTypeValue
        })
        let data = []
        for (const key in obj) {
          data.push({
            value: key,
            label: obj[key],
            leaf: false,
          })
        }
        resolve(data)
      } else {
        ElMessage.error(res.data.message)
      }
    })
  } else if (level == 2) {
    let data = areaGeneralTree.filter((item) => {
      return item.dataType == value
    })
    console.log(data)
    let reData = []
    data.forEach((item) => {
      reData.push({
        value: item.id,
        label: item.tableComment,
        leaf: false,
      })
    })
    resolve(reData)
  } else if (level == 3) {
    areaApi
      .getAreaGeneralData({ dataTypeId: value, areaId: areaId,page:1,size:9999 })
      .then((res) => {
        if (res.data.code) {
          let data = []
          res.data.data.records.forEach((item) => {
            let disabled = state.formTargetList.find((item2) => {
              return item2.targetId == item.id
            })
            data.push({
              value: item.id,
              label: item.name,
              leaf: true,
              disabled: !!disabled,
            })
          })
          console.log('resolve', data)
          resolve(data)
        } else {
          ElMessage.error(res.data.message)
        }
      })
  }
  // setTimeout(() => {
  //   const nodes = Array.from({ length: level + 1 }).map((item) => ({
  //     value: ++id,
  //     label: `Option - ${id}`,
  //     leaf: level >= 2,
  //   }));
  //   // Invoke `resolve` callback to return the child nodes data and indicate the loading is finished.
  //   resolve(nodes);
  // }, 1000);
}
//切换模板时预填充需求描述
function tempChange(val) {
  state.auForm = {
    source: 3,
  }
  if (!val) {
    return
  }
  dataApi.getTempInfo({ id: val }).then((res) => {
    if (res.data.code == 200) {
      state.auForm = res.data.data
      state.auForm.source = 3
      state.auForm.id = undefined
      state.formTargetList = res.data.data.targetInfos || []
      if (state.formTargetList.length > 0) {
        state.showTargetSet = true
      }
      //用于回显
      ecohFun()
      state.formTargetList.forEach((item, index) => {
        trackChange(item, index)
      })
    } else {
      ElMessage.error(res.data.message)
    }
  })
}

//航迹切换时，如果有开始时间，则计算结束时间
function trackChange(row, index) {
  if (row.trackStartTime) {
    calcEndTime2(row, index)
  }
}
//计算航迹结束时间 和calcEndTime 保持一样
function calcEndTime2(target, index) {
  if (!target.trackPresetId) {
    // ElMessage.error("航迹未选择")
    return
  }
  if (!state.auForm.startTime) {
    // ElMessage.error("需求开始时间未选择")
    return
  }
  let track = state.trackManagerList.find((item) => {
    return item.id == target.trackPresetId
  })
  if (!track) {
    return
  }
  let endTimeSpan = track.tracks[track.tracks.length - 1].time * 1000
  // state.formTargetList[index].trackEndTime = moment(new Date(moment(target.startTime).format("YYYY-MM-DD") + " " + target.trackStartTime).getTime() + endTimeSpan).format("HH:mm:ss")
  state.formTargetList[index].trackEndTime = moment(
    new Date(target.trackStartTime).getTime() + endTimeSpan
  ).format('YYYY-MM-DD HH:mm:ss')
  // console.log("track", track);
}

// 清空数据
function tempClear() {
  state.auForm = {}
  state.formTargetList = []
  formRef.value.clearValidate()
}
//用于级联的回显
function ecohFun() {
  state.echoOption = []
  state.formTargetList.forEach((item, index) => {
    dataApi
      .getAreaAndTargetInfo({
        generalId: item.generalId,
        targetId: item.targetId,
      })
      .then((res2) => {
        if (res2.data.code == 200) {
          let obj = []
          res2.data.data.forEach((item) => {
            let children = []
            item.dataGenerals?.forEach((item2) => {
              let children2 = []
              if (item2.targetInfo) {
                children2.push({
                  leaf: true,
                  value: item2.targetInfo.id,
                  label: item2.targetInfo.name,
                })
              }
              children.push({
                leaf: false,
                value: item2.id,
                label: item2.dataTypeValue,
                children: children2 || [],
              })
            })
            obj.push({
              leaf: false,
              value: item.id,
              label: item.areaName,
              children: children,
            })
          })
          state.echoOption[index] = obj
          state.cascaderKey = state.cascaderKey + 1
          console.log('echoOption', state.echoOption)
        } else {
          ElMessage.error(res2.data.message)
        }
      })
  })
}
//查看任务列表
function handleDetail(row) {
  //方式一：展示小弹窗
  // getTaskById(row);
  // state.showTaskPage = true;
  //方式二：跳转到任务
  window.taskPageParame = { requirementId: row.id }
  router.push({
    path: '/home/<USER>',
    // replace: true,
  })
}
// 表格中某一行数据的需求分解
function handleDemand(row) {
  let demandInfo = JSON.stringify(row)
  router.push({
    path: '/home/<USER>',
    query: {
      planTitle: '需求分解',
      // demandInfo,
      requirementId: row.id,
    },
    replace: true,
  })
}
//计算航迹结束时间
function calcEndTime(target, index) {
  if (!target.trackPresetId) {
    ElMessage.error('航迹未选择')
    return
  }
  if (!state.auForm.startTime) {
    ElMessage.error('需求开始时间未选择')
    return
  }
  let track = state.trackManagerList.find((item) => {
    return item.id == target.trackPresetId
  })
  let endTimeSpan = track.tracks[track.tracks.length - 1].time * 1000
  state.formTargetList[index].trackEndTime = moment(
    new Date(
      moment(state.auForm.startTime).format('YYYY-MM-DD') +
        ' ' +
        target.trackStartTime
    ).getTime() + endTimeSpan
  ).format('HH:mm:ss')
  // console.log("track", track);
}
/**
 * @description 保存新增/修改
 */
function formSubmit(fun) {
  // state.formTargetList.forEach((item, index) => {
  //   calcEndTime(item, index)
  // })
  state.auForm.targetInfos = JSON.parse(JSON.stringify(state.formTargetList))
  state.auForm.targetInfos.forEach((item) => {
    item.trackEndTime = undefined
    item.trackStartTime = item.trackStartTime
    item.tasks.forEach((task) => {
      task.startTime = task.startTime
      task.endTime = task.endTime
    })
  })
  state.auForm.isDeCompose = 1
  dataApi.subRequirement(state.auForm).then((res) => {
    if (res.data.code == 200) {
      state.formRequirement = res.data.data
      if (fun) {
        setTimeout(() => {
          fun()
        }, 1000)
      }
      window.taskPageParame = { requirementId: state.formRequirement.id }
      router.push({
        path: '/home/<USER>',
        replace: true,
      })
      // getRequirementList(state.formRequirement);
      // isShow.value = false;
      // formRef.value.resetFields();
      // formRef.value.clearValidate();
      // state.auForm = {};
    } else {
      ElMessage.error(res.data.message)
    }
  })
}

// 保存按钮
const saveAdd = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      formSubmit()
    }
  })
}
//保存为模板
function saveTemplate() {
  formRef.value.validate((valid) => {
    if (valid) {
      ElMessageBox.prompt('请输入模板名称', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '关闭',
      }).then(({ value }) => {
        if (!value) {
          ElMessage.error('请输入模板名称')
        } else {
          let template = JSON.parse(JSON.stringify(state.auForm))
          template.targetInfos = JSON.parse(
            JSON.stringify(state.formTargetList)
          )
          template.templateName = value
          tempRef.value.state.currentTemp = template
          tempRef.value.state.formTargetList = state.formTargetList
          tempRef.value.state.currentId = null
          tempRef.value.saveTemplate()
        }
      })
    }
  })
}
//确认任务列表并跳转到调度
function saveAndCall() {
  let fun = () => {
    router.push({
      path: '/home/<USER>',
      replace: true,
      query: {
        requirementId: state.formRequirement.id,
      },
    })
  }
  fun()
  // saveTaskList(fun)
}
//确认任务列表并跳转到任务管理页面
function saveRequirementTask() {
  let fun = () => {
    router.push({
      path: '/home/<USER>',
      replace: true,
      query: {
        requirementId: state.formRequirement.id,
      },
    })
  }
  // saveTaskList(fun)
  fun()
}
/**
 * @description 取消新增/编辑
 */
const cancelAdd = () => {
  isShow.value = false
  formRef.value.resetFields()
  formRef.value.clearValidate()
}
/**
 * @description 表格点击事件
 */
const handleTableClick = (row) => {
  state.awaitSubmit = false
  tableRef.value.setCurrentRow(row)
  dataApi.getRequirementById({ id: row.id }).then((res) => {
    if (res.data.code == 200) {
      state.formRequirement = res.data.data
      // getTaskById();
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
// 获取子任务列表
function getTaskById(row) {
  dataApi.getTaskById({ requirementId: row.id }).then((res) => {
    if (res.data.code == 200) {
      state.taskList = res.data.data
      state.taskList.forEach((task, index) => {
        getTaskTargets(task, index)
      })
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
function getTaskTargets(row, index) {
  dataApi.getTaskTarget({ taskId: row.id }).then((res) => {
    if (res.data.code == 200) {
      state.taskList[index].targetList = res.data.data
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
/**
 * @description 需求分解
 */
const handleResolveClick = (row) => {
  router.push({
    path: '/home/<USER>',
    replace: true,
  })
}
/**
 * @description 删除表格数据
 */
const handleDeleteClick = (val) => {
  dataApi.delRequirementById({ id: val.id }).then((res) => {
    if (res.data.code == 200) {
      ElMessage.success(res.data.message)
      getRequirementList()
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
/**
 * @description 编辑表格数据
 */
let changeID = ''
const handleEditClick = (row) => {
  title.value = '编辑需求'
  dataApi.getRequirementById({ id: row.id }).then((res) => {
    if (res.data.code == 200) {
      state.auForm = res.data.data
      state.formTargetList = res.data.data.targetInfos
      isShow.value = true
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
//设为实时态势的需求
function handleSubEmit(row) {
  localStorage.setItem('panoramaId', row.id)
  ElMessage.success('设置成功')
}
function showTemplat() {
  tempState.show = true
}
//同步模板
function asyncTempData(data) {
  tempState.tempList = data
}
//为新增/编辑需求 增加一行目标
function addTargetRow() {
  state.formTargetList.push({
    targetId: undefined,
    trackPresetId: undefined,
    startTime: null,
    endTime: null,
    tasks: [],
  })
}
//为新增/编辑需求 删除一行目标
function delTargetRow(index) {
  state.formTargetList.splice(index, 1)
}
//sett
let itemRefs = {}
function setItemRef(el, index) {
  itemRefs[index] = el
}
//目标切换
function targetIdChange(val, index) {
  let row = itemRefs[index].getCheckedNodes(true)[0]
  let generalId = row.parent.data.value
  let targetName = row.text + ''
  state.formTargetList[index].generalId = generalId
  state.formTargetList[index].targetName = targetName
}
let targetNewRef = ref()
//新增目标
function targetIdNewChange() {
  let row = targetNewRef.value.getCheckedNodes(true)[0]
  let generalId = row.parent.data.value
  let targetName = row.text + ''
  newTargetForm.generalId = generalId
  newTargetForm.targetName = targetName
}
function openTargetDialog() {
  const { startTime, endTime } = state.auForm
  if (!startTime || !endTime) {
    ElMessage.error('请先选择开始和结束时间')
    return
  }
  state.newTargetShow = true
}
//将新增的目标信息填充到目标列表中
function addNewTarget() {
  if (!newTargetForm.targetId) {
    ElMessage.error('请选择目标')
    return
  }
  state.formTargetList.push({
    targetId: newTargetForm.targetId,
    generalId: newTargetForm.generalId,
    targetName: newTargetForm.targetName,
    trackPresetId: undefined,
    startTime: null,
    endTime: null,
    tasks: [],
  })
  state.newTargetShow = false
  newTargetForm.targetId = undefined
  newTargetForm.generalId = undefined
  newTargetForm.targetName = undefined
  //sdw start
  showTargetEdit(
    state.formTargetList[state.formTargetList.length - 1],
    state.formTargetList.length - 1
  )
  //sdw end
}
//删除指定目标
function removeTarget(index) {
  ElMessageBox.confirm('确认移除该目标及其下属数据吗?', '警告', {
    confirmButtonText: '是',
    cancelButtonText: '否',
    type: 'warning',
  }).then(() => {
    state.formTargetList.splice(index, 1)
  })
}

//编辑目标
function showTargetEdit(target, index) {
  targetEditForm.targetForm = target
  targetEditForm.targetIndex = index
  targetEditForm.show = true
}
function editTarget(form) {
  state.formTargetList[targetEditForm.targetIndex] = form
  formRef.value.validate()
  targetEditForm.show = false
}
//获取文件模板
function getFileTemplate() {
  dataApi.getFileTemplate().then((res) => {
    let data = res.data
    // , { type: "application/vnd.ms-excel;charset=utf-8" }
    let url = window.URL.createObjectURL(new Blob([data]))
    let link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', '模板.xlsx')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  })
}
//删除一行业务数据
function delBusinessRow(targetIndex, businessIndex) {
  state.formTargetList[targetIndex].tasks.splice(businessIndex, 1)
}
//新增一行业务数据
function addBusinessRow(targetIndex) {
  state.formTargetList[targetIndex].tasks.push({
    taskType: [],
    startTime: null,
    endTime: null,
    repeatType: 1,
  })
}
/**
 * @description 组件销毁
 */
onUnmounted(() => {})
</script>
<style scoped lang="less">
@import '../../assets/style/theme/style.less';

.title-font {
  font-size: 18px;
  font-weight: bolder;
  color: #c6cdd6;
}

.span1 {
  color: rgba(@themeColor, 1);
  cursor: pointer;
}

.span2 {
  color: white;
  cursor: pointer;
}

.highlight-row {
  background-color: aqua;
}

.transport {
  .el-tree {
    font-size: large !important;
    margin-top: 3% !important;

    .el-tree-node {
      margin-top: 3% !important;
    }
  }

  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: #81d4f9;
  }
}

.requir {
  height: 30%;
}

.textDescribe {
  top: 10px;
  position: relative;
  height: 65%;
  // border: 1px solid rgb(223, 235, 235);
  background-color: #031527a6;
  // color: #4f738c;
  line-height: 30px;
  padding: 5px 10px;
  box-sizing: border-box;
  overflow-y: auto;
}

.planButton {
  position: absolute;
  top: 2%;
  right: 5px;
  display: inline-flex;
  justify-content: center;
  align-items: center;

  > span {
    margin-right: 10px;
    cursor: pointer;
  }
}

.equip {
  position: relative;
  margin-top: 5%;
  padding-left: 10px;
  margin-bottom: 5%;
  cursor: pointer;
}

.equip:hover {
  color: #309eff;
}

:deep(.el-tree .el-tree-node) {
  color: #3a627d;
  font-size: 16px;
}

:deep(.el-tree .is-current) {
  font-size: 19px;
  font-weight: 600;
  color: #39b8ff;

  .el-tree-node__content {
    background-color: transparent !important;
  }
}

.requirement_form {
  :deep(.el-form-item) {
    margin-bottom: 0px !important;
  }
}

.tempBoxClass {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 1999;
  background: rgba(0, 0, 0, 0.5);
}

.taskTitle {
  font-size: 18px;
  font-weight: bolder;
  color: #39b8ff;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 28px;
  margin-bottom: 8px;

  img {
    margin-right: 8px;
  }
}

.taskTarget {
  margin: 5px 0;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  img {
    margin-right: 8px;
  }
}

.targetItem_box {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  width: 100%;
  height: auto;
}

.targetItem {
  border: 1px solid;
  border-radius: 5px;
  margin-right: 10px;
  padding: 0 8px;
  line-height: 25px;
  box-sizing: border-box;
  border: #3a627d;
  cursor: pointer;

  &:hover {
    .targetItem_icon {
      opacity: 1;
    }
  }
}

.targetItem_text {
  display: inline-block;
  user-select: none;

  &:hover {
    color: #309eff;
  }
}

.targetItem_icon {
  user-select: none;
  opacity: 0;

  &:hover {
    color: #3a627d;
  }
}
</style>
