<!--
 * @Author: JRX <EMAIL>
 * @Date: 2024-07-25 17:03:38
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-25 17:06:52
 * @FilePath: \wrxtzhglrj\src\views\home\EarthPage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="earthPage">
    <CesiumBox />
  </div>
</template>
<script setup>
import CesiumBox from "@/components/evmap/CesiumBox.vue";
</script>
<style scoped lang="less">
.earthPage {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
}
</style>
