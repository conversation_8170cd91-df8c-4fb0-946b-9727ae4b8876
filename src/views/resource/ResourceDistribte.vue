<!--
 * @@Description: 资源统计
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-03 09:14:16
 * @FilePath: \qbmbtsweb\src\views\home\HomePage.vue
-->
<template>
  <customPopup left="10px" top="80px" width="99%" height="90%">
    <template #header>
      <span class="resource_icon"></span>
      <span class="title-font">资源使用统计</span>
    </template>
    <template #content>
      <div>
        <div class="total">
          <el-tooltip :content="state.areaPage <= 1 ? '已经是第一页了' : '上一页'" placement="bottom" effect="light">
            <el-button class="leftBtn" circle @click="pageChange('left')">
              <el-icon>
                <ArrowLeftBold />
              </el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip :content="state.areaPage >= state.areaPageMax ? '已经是最后一页了' : '下一页'" placement="bottom"
            effect="light">
            <el-button class="rightBtn" circle @click="pageChange('right')">
              <el-icon>
                <ArrowRightBold />
              </el-icon>
            </el-button>
          </el-tooltip>
          <div class="box" v-for="(item, index) in state.areaList" :key="index">
            <span class="title">{{ item.areaName }}</span>
            <span class="num" style="color: #11c1f9">{{ item.occupancy }} %</span>
          </div>
        </div>
      </div>
      <div style="display: flex;flex-wrap: wrap; margin-top: 5px; height: calc(100% - 120px)">
        <div class="top_box">
          <el-row :gutter="10" style="margin-bottom: 8px;">
            <el-col :span="16">
              <el-row>
                <span class="list_icon"></span>
                <span class="title-font" style="line-height: 30px;">资源列表</span>
              </el-row>
            </el-col>
            <el-col :span="8">
              <span class="el-form-item__label">类别：</span>
              <el-select v-model="search.dataType" style="width: calc(100% - 54px);" @change="getPageEquipmentByType">
                <el-option :value="littleType.id" :label="littleType.tableComment"
                  v-for="littleType in state.littleTypeList" :key="littleType.id"></el-option>
              </el-select>
            </el-col>
            <!-- <el-col :span="8">
              <span class="el-form-item__label">所属域</span>
              <el-select v-model="search.area" style="width: 60%;" @change="getPageEquipmentByType" clearable>
                <el-option :value="area.id" :key="area.id" v-for="area of state.allAreaList"
                  :label="area.areaName"></el-option>
              </el-select>
            </el-col> -->
          </el-row>
          <div style="height: calc(100% - 42px);">
            <customTable :tableData="state.resourceList" ref="resourceListRef" :tableColumn="tableColumn"
              :total="resourcePage.total" :currentPage="resourcePage.page" :pageSize="resourcePage.size"
              @nodeClickEmit="rowClick" @currentPageEmit="(val) => {
                resourcePage.page = val;
                getPageEquipmentByType();
              }
                " @pageSizeEmit="(val) => {
                  resourcePage.size = val;
                  getPageEquipmentByType();
                }
                  ">
            </customTable>
          </div>
        </div>
        <div class="top_box">
          <el-row>
            <span class="list_icon"></span>
            <span class="title-font" style="line-height: 30px;">资源消息</span>
          </el-row>
          <div class="message_info_box">
            <div class="message_info" v-for="(infoText, index) in state.infoTextList" :key="index">
              <div class="infoIcon"></div>
              <div class="infoText">【任务】{{ infoText.taskName }}【消息】：{{ infoText.record }}
              </div>
            </div>
          </div>
        </div>
        <div class="bottom_box">
          <el-row>
            <span class="resource_icon"></span>
            <span class="title-font" style="line-height: 30px;">资源统计</span>
          </el-row>
          <div class="chartList">
            <div v-for="(area, index ) in state.areaList" :key="'chart' + index" class="chartDiv"
              @click="selectDomain(area)">
              <!-- <div class="areaChart" :id="'areaChart' + area.id"></div> -->
              <div class="areaTitle">
                {{ area.areaName }}
              </div>
              <div class="areaBody">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <span class="el-form-item__label">资源空闲率：</span>
                  </el-col>
                  <el-col :span="12" style="line-height: 32px;">{{ area.occupancy }} %</el-col>
                  <el-col :span="12">
                    <span class="el-form-item__label">平均响应延迟：</span>
                  </el-col>
                  <el-col :span="12" style="line-height: 32px;">230 毫秒</el-col>
                  <el-col :span="12">
                    <span class="el-form-item__label">调度成功率：</span>
                  </el-col>
                  <el-col :span="12" style="line-height: 32px;">{{ parseInt(Math.random() * 10000) / 100 }} %</el-col>
                </el-row>

              </div>
            </div>
          </div>
        </div>
        <div class="bottom_box">
          <el-row>
            <span class="timeLine_icon"></span>
            <span class="title-font" style="line-height: 30px;">资源甘特图</span>
          </el-row>
          <div class="my-timeline" id="my-timeline"></div>
        </div>
      </div>
    </template>
  </customPopup>
</template>

<script setup>
import customTable from "@/components/customTable.vue";
import { reactive, onMounted, onUnmounted, ref, nextTick, watch } from "vue";
import { useRouter } from "vue-router";
import customWindow from "../../components/customWindow.vue";
import customPopup from "../../components/customPopup.vue";
import * as dataApi from "@/service/API/system/resourceDispatch.js";
import * as areaApi from "@/service/API/system/areaManage.js";
import { ElMessage, ElLoading } from "element-plus";


/**start */
let fontColor = window.$fontColor;
let StatsAreaTypeChart = ref(null);
let statsAreaIdleChart = ref(null);
let resourceListRef = ref(null);
let TimeLineChart = ref(null);
const state = reactive({
  statsAllLeaf: {
    count: 0,
  }, //所有统计信息
  resourceList: [], //列表
  page: 1, //表格当前页
  size: 10, //表格大小
  total: 0, //表格合计数据
  tabActive: 1, //默认选中地基平台
  tableColumn: [],
  noData: true, //是否有任务开始结束时间
  leisureData: [],
  areaList: [],
  areaPage: 1,
  allAreaList: [],//所有资源域列表
  areaPageMax: 0,
  littleTypeList: [],
  infoTextList: []
});
const search = reactive({
  dataType: undefined,
  area: undefined
})
const resourcePage = reactive({
  total: 0,
  page: 1,
  size: 10
})
const tableColumn = [
  { label: "设备名称", prop: "name", type: "String", minWidth: "150" },
  { label: "所属域", prop: "areaName", type: "String", minWidth: "100" },
  // { label: "业务类型", prop: "businessType", type: "Sysdic", dicName: "businessType", minWidth: "100" },
  { label: "总调用次数", prop: "totalCount", type: "String", minWidth: "100" },
  { label: "待执行任务数", prop: "unExecuteCount", type: "String", minWidth: "100" },
]
/**end */


let Chart = ref(null);
let StatsLeafChart = ref(null);

let myChart = null;
let myStatsLeafChart = null;
let myChart2 = null;
onMounted(() => {
  // getStatsAllLeaf();
  // getStatsAreaType();
  // getStatsAreaIdle();
  // getResourceList(1);
  getQueryLittleType()
  getAreaList()
  initTimeLine()

});
//获取小分类
function getQueryLittleType() {
  areaApi.queryLittleType({ type: 1 }).then(res => {
    if (res.data.code == 200) {
      state.littleTypeList = res.data.data
      search.dataType = state.littleTypeList[0]?.id
      getPageEquipmentByType()
    }
  })
}
// 获取资源域列表
function getAreaList() {
  areaApi.getAreaList({ page: 1, size: 999, areaType: 1 }).then(res => {
    if (res.data.code == 200) {

      state.allAreaList = res.data.data.records;
      state.areaPageMax = parseInt(state.allAreaList.length / 6) + 1
      state.allAreaList.forEach((item, index) => {
        getOccupancyByArea(item.id, index)
        // drawChart.A(item, index)
      })
      state.areaList = state.allAreaList.filter((item, index) => {
        if (index > state.areaPage - 1 * 6 && index < state.areaPage * 6) {
          return item
        }
      });
    } else {
      ElMessage.error(res.data.message)
    }
  })
}


//获取资源列表
function getPageEquipmentByType() {
  areaApi.pageStatsEquipment({
    page: resourcePage.page,
    size: resourcePage.size,
    generaId: search.dataType,
  }).then(res => {
    if (res.data.code == 200) {
      state.resourceList = res.data.data.records
      resourcePage.total = res.data.data.total
    }
  })
}
//获取空闲率
function getOccupancyByArea(id, index) {
  state.allAreaList[index].occupancy = parseInt(Math.random() * 10000) / 100
  //获取空闲率 -- 无接口
}
function searchFun() {
  //获取资源列表
  getPageEquipmentByType()
  //绘制时间轴

}
const drawChart = {
  A: function (item, index) {
    areaApi.getStatsType({ areaId: item.id }).then((res) => {
      if (res.data.code == 200) {
        let data = res.data.data;
        let option = {
          color: chartColors,
          title: {
            text: item.areaName,
            x: "left",
            y: "5%",
            textStyle: {
              color: fontColor,
              fontSize: 16,
              // fontWeight: "bolder",
            },
          },
          tooltip: {
            trigger: "item",
          },
          legend: {
            // orient: "vertical",
            // x: "70%",
            // y: "60%",
            left: "right",
            top: "bottom",
            data: [],
            textStyle: {
              color: fontColor,
            },
          },
          calculable: true,
          series: [
            {
              name: "类别",
              type: "pie",
              radius: "65%",
              center: ["45%", "50%"],
              data: [],
              label: {
                position: "inside",
                formatter: "{b}:{d}%",
                // formatter: (data) => {
                //   if (data.value == 0) {
                //     return "";
                //   } else {
                //     return data.name + " " + data.value;
                //   }
                // },
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
            },
          ],
        };
        for (let key in data) {
          let val = data[key];
          option.legend.data.push(key);
          option.series[0].data.push({
            value: val,
            name: key,
          });
        }
        let chartDom = document.getElementById("areaChart" + item.id);
        let cardChartx = echarts.init(chartDom);

        cardChartx.setOption(option, true);
        cardChartx.resize();
      } else {
        ElMessage.error(res.data.messagea);
      }
    });
  }
}
const router = useRouter();
// 点击卡片跳转区域
const selectDomain = (area) => {
  router.replace({
    path: "/home/<USER>",
    query: {
      id: area.id,
      areaName: area.areaName,
      pageType: 1,
    },
  });
};

// 切换页码
function pageChange(type) {
  if (state.areaPage <= 1 && type == "left") {
    return;
  } else if (state.areaPage >= state.areaPageMax && type == "right") {
    return;
  }
  type == "left" && (state.areaPage = state.areaPage - 1);
  type == "right" && (state.areaPage = state.areaPage + 1);
  state.areaList = state.allAreaList.filter((item, index) => {
    if (index > state.areaPage - 1 * 6 && index < state.areaPage * 6) {
      return item
    }
  });
}

var visGroups; //时间轴分组
var visItems; //时间轴项目
var timeline; //时间轴管理器
/**
 * @description 绘制时间轴
 */
function initTimeLine() {
  var container = document.getElementById("my-timeline");

  timeline = new vis.Timeline(container);
  visGroups = new vis.DataSet([]);
  visItems = new vis.DataSet([]);
  // let start = moment(timelineStart).format("YYYY-MM-DD HH:mm:ss");
  // let min = moment(timelineStart).subtract(1, "h").format("YYYY-MM-DD HH:mm:ss")
  // let end = moment(timelineEnd).format("YYYY-MM-DD HH:mm:ss");
  let options = {
    autoResize: true,
    height: "90%", //高度
    width: "100%", //宽度
    min: "2000-01-01 00:00:00", //设置最小时间范围
    max: "2099-12-31 23:59:59", //设置最大时间范围
    // start: "2024-08-06 15:03:18", //设置开始时间
    // end: "2024-08-07 15:03:18", //设置结束时间
    stack: true, // ture则不重叠
    limitSize: true,
    verticalScroll: true,
    // cluster: true, //数据量过大时使用。
    locale: "zh-cn",
    xss: {
      disabled: true,
    },
    groupOrder: function (a, b) {
      return a.value - b.value;
    },
    editable: false,
    showCurrentTime: false,
    moment: function (date) {
      // return moment(date).format("YYYY-MM-DD HH:mm:ss");
      return moment(date);
    },
    locale: moment.locale("zh-cn")

  };
  timeline.setOptions(options);
  timeline.setGroups(visGroups);
  timeline.setItems(visItems);
}
//行点击事件
function rowClick(row) {
  drawTimeLine(row)
  getLog(row)
}
//获取日志
function getLog(row) {
  areaApi.logByEquipmentId({ equipmentId: row.id }).then(res => {
    if (res.data.code == 200) {
      state.infoTextList = res.data.data.records
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
//添加时间轴上的任务块
function drawTimeLine(row) {
  visGroups.clear()
  visItems.clear()
  visGroups.add({ id: row.id, content: '资源-' + row.name, value: row.id });
  timeline.setGroups(visGroups);
  dataApi.queryTaskByEquipment({ generalId: row.generalId, equipmentId: row.id }).then(res => {
    if (res.data.code == 200) {
      let min = 0
      let max = 0
      res.data.data.forEach(item => {
        visItems.add({
          id: item.id,
          group: row.id,
          content: item.taskName,
          start: item.startTime,
          end: item.endTime,
        })
        if (item.startTime < min || min == 0) {
          min = item.startTime
        }
        if (item.endTime > max || max == 0) {
          max = item.endTime
        }
      })
      timeline.setOptions({
        min: timeSpan(new Date(min).getTime() - 86400 / 2 * 1000),
        start: timeSpan(new Date(min).getTime() - 86400 / 2 * 1000),
        max: timeSpan(new Date(max).getTime() + 86400 / 2 * 1000),
        end: timeSpan(new Date(max).getTime() + 86400 / 2 * 1000),
      })
    }
  })

}
</script>

<style lang="less" scoped>
.title-font {
  font-size: 18px;
  font-weight: bolder;
  color: #c6cdd6;
}

/* .el-menu--horizontal.el-menu{} */
.menu_distribte {
  display: flex;
  flex-direction: column;
  width: 40%;
  height: 750px;

  >div {
    flex: 1;
    height: 100%;
    margin-bottom: 10px;
  }

  >div:nth-child(2),
  >div:nth-child(3) {
    border: 1px solid #003a66;
  }

  .el-menu--horizontal.el-menu {
    border-bottom: solid 0px var(--el-menu-border-color);
  }
}

.total {
  display: flex;
  width: 100%;
  overflow-y: auto;
  height: 110px;
  position: relative;
  padding: 0 10px;
  box-sizing: border-box;
  // >div:nth-child(1) {
  //   flex: 1;
  //   margin-right: 10px;
  // }

  // >div:nth-child(2) {
  //   flex: 3;
  //   display: grid;
  //   grid-template-columns: repeat(3, 1fr);
  //   grid-gap: 10px;
  // }

  .box {
    text-align: center;
    line-height: 50px;
    border: 1px solid #003a66;
    border-radius: 4px;
    width: 14.5%;
    margin: 0 1%;

    span {
      display: block;
    }

    span:nth-child(2) {
      font-size: 24px;
      font-family: fantasy;
    }

    // span:nth-child(1) {
    //   color: #444;
    // }
  }

  .box:hover {
    border: 1px solid #218bdc;

    // span:nth-child(1) {
    //   font-size: 16px;
    // }
    span:nth-child(2) {
      font-size: 28px;
    }
  }
}

.top_box {
  height: 50%;
  width: 50%;
  box-sizing: border-box;
  padding: 1%;
  border: 1px solid #003a66;
}

.bottom_box {
  height: 50%;
  width: 50%;
  box-sizing: border-box;
  padding: 1%;
  border: 1px solid #003a66;

  .chartList {
    width: 100%;
    height: calc(100% - 25px);
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;
    margin-top: 8px;

    .chartDiv {
      height: 48%;
      width: 30%;
      cursor: pointer;
      border: 1px solid #003a66;
      border-radius: 5px;


      .areaChart {
        height: 100%;
      }

      .areaTitle {
        height: 30px;
        width: 100%;
        line-height: 30px;
        border-bottom: 1px solid #003a66;
        padding: 0 8px;
        box-sizing: border-box;
      }

      .areaBody {
        height: calc(100% - 30px);
        width: 100%;
        padding: 0 8px;
        box-sizing: border-box;

      }

    }
    .chartDiv:hover{
      background: #003a6650;
    }
  }
}

.leftBtn {
  background: transparent !important;
  position: absolute;
  top: 30%;
  opacity: 0.3;
  left: 0px;

  &:hover {
    opacity: 1;
  }
}

.rightBtn {
  background: transparent !important;
  position: absolute;
  top: 30%;
  opacity: 0.3;
  right: 0;

  &:hover {
    opacity: 1;
  }
}

.message_info_box {
  display: flex;
  height: calc(100% - 50px);
  // flex-wrap: wrap;
  flex-direction: column;
  padding: 10px;
  box-sizing: border-box;
  overflow-y: auto;

}



.message_info {
  line-height: 20px;
  width: 100%;
  margin-bottom: 5px;
  display: flex;

  .infoIcon {
    height: 25px;
    width: 25px;
    background: url(/public/images/icon/xx.png) 0 0 /100% 100% no-repeat;
  }

  .infoText {
    display: inline-block;
    min-height: 25px;
    width: calc(100% - 25px);
    line-height: 25px;
    overflow: visible;
    word-wrap: break-word;
  }
}

.my-timeline {
  width: 98%;
  height: calc(100%);
  margin: 1%;
}
</style>
