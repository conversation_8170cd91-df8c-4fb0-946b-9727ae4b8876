<!--
 * @Author: JRX <EMAIL>
 * @Date: 2024-03-26 16:55:34
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-30 18:06:13
 * @FilePath: \wrxtzhglrj\src\views\resource\zydd.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- 条件筛选框 -->
  <customPopup1 left="10%" top="10%" width="80%" :height="state.foldHeight" v-if="state.searchBoxShow">
    <template #content>
      <div class="steps_box">
        <el-steps :active="state.stepsActive" align-center finish-status="success">
          <el-step title="任务筛选" description="" />
          <el-step title="一级调度" description="" />
          <el-step title="二级调度" description="" />
          <el-step title="冲突消解" description="" />
          <el-step title="下发最终调度结果" description="" />
          <!-- <el-step title="结果确认" description="" /> -->
        </el-steps>
      </div>
      <el-carousel ref="carouselRef" indicator-position="none" :autoplay="false" arrow="never" height="62vh">
        <el-carousel-item name="任务筛选">
          <el-form label-width="120px">
            <el-form-item label="按需求筛选：" prop="currentRequirement">
              <el-select v-model="state.currentRequirement" multiple placeholder="请选择需求" clearable
                @change="getTaskList">
                <el-option v-for="item in state.requirementList" :key="item.id" :label="item.requirementName"
                  :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-row :gutter="0">
              <el-col :span="11">
                <el-form-item label="按时间筛选：" prop="sex">
                  <span class="el-form-item__label">开始时间：</span>
                  <el-date-picker style="width: calc(100% - 130px)" v-model="state.startTime" type="date"
                    placeholder="请选择时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="getTaskList" />
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="结束时间：" prop="sex">
                  <el-date-picker style="width: 100%" v-model="state.endTime" type="date" placeholder="请选择时间"
                    format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" @change="getTaskList" />
                </el-form-item>
              </el-col>
              <el-col :span="2" style="text-align: right">
                <el-button @click="autoSelect">调度配置</el-button>
              </el-col>
            </el-row>

            <div style="width: 100%; height: 45vh">
              <el-table ref="taskTableRef" :data="state.taskList" style="width: 100%" row-key="id" class="custom-table"
                highlight-current-row @selection-change="selectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <!-- <template v-for="e of tableOptions1" :key="e.label"> -->
                <el-table-column prop="taskName" label="任务名称" align="center" />
                <el-table-column prop="startTime" label="开始时间" align="center">
                  <template v-slot="{ row }">
                    {{ timeSpan(row.startTime) }}
                  </template>
                </el-table-column>
                <el-table-column prop="endTime" label="结束时间" align="center">
                  <template v-slot="{ row }">
                    {{ timeSpan(row.endTime) }}
                  </template>
                </el-table-column>
                <!-- </template> -->
              </el-table>
            </div>

            <div
              style="width: 100%; text-align: center; margin-top: 15px;display: flex;justify-content: space-between;align-items: center;">
              <div></div>
              <div>
                <!-- <el-button @click="autoSelect">自动调度</el-button> -->
                <el-button @click="saveAuto">资源调度</el-button>
              </div>
              <div>
                <el-button @click="nextStep" v-if="state.chooseResource.length > 0">下一步</el-button>
              </div>
            </div>
          </el-form>
        </el-carousel-item>
        <el-carousel-item name="一级调度">
          <div style="height: 54vh; width: 100%; margin-bottom: 5px">

            <el-table ref="tableL" :border="Border" :data="state.chooseResource" style="width: 100%"
              class="custom-table" :span-method="spanMethod" @selection-change="NewselectionChange">
              <!--@select="targetChecked" @select-all="selectAll"-->
              <!--  @select="targetChecked" @select-all="selectAll"-->
              <!-- <el-table-column type="selection" width="55" align="center" /> -->
              <el-table-column width="30" type="index" align="center"></el-table-column>
              <el-table-column label="任务名" prop="task.taskName" align="center">
                <!-- <template v-slot="{ row }">
                  {{ taskName(row.taskId) }}
                </template> -->
              </el-table-column>
              <!-- <el-table-column label="资源名称" prop="name" align="center" /> -->
              <el-table-column label="资源域" prop="areaName" align="center">
                <template v-slot="{ row }">
                  <span>{{ areaName(row.areaId) }}</span>
                </template>

              </el-table-column>
              <el-table-column label="设备名称" prop="name" align="center" />
              <el-table-column label="设备占用开始时间" prop="startTime" align="center" />
              <el-table-column label="设备占用结束时间" prop="endTime" align="center" />

              <!-- <el-table-column label="覆盖时间" prop="" align="center">
                <template v-slot="{ row }">
                  <span>{{ row.entryTime }} -- {{ row.exitTime }}</span>
                </template>
              </el-table-column> -->
              <!-- <el-table-column label="覆盖率" prop="averageCoverage" align="center" width="80">
                <template v-slot="{ row }">
                  {{ row.averageCoverage ? parseInt(row.averageCoverage * 10000) / 100 + "%" : 0 }}
                </template>
              </el-table-column> -->
              <!-- <el-table-column label="覆盖率" prop="coverRate" align="center" width="80">
                <template v-slot="{ row }">
                  {{ row.coverRate ? parseInt(row.coverRate * 10000) / 100 + "%" : 0 }}
                </template>
              </el-table-column> -->
              <el-table-column label="综合覆盖率" prop="totalCover" align="center" width="100">
                <template v-slot="{ row }">
                  {{ row.totalCover ? parseInt(row.totalCover * 10000) / 100 + "%" : 0 }}
                </template>
              </el-table-column>
              <!-- <el-table-column label="操作" width="100" align="center">
                <template v-slot="{ row }">
                  <el-tooltip effect="dark" content="移除资源" placement="top-start">
                    <el-link :underline="false" @click.stop="resourceRemove(row)" style="margin-left: 10px">
                      <el-icon :size="20">
                        <Remove style="width: 20px; height: 20px" />
                      </el-icon>
                    </el-link>
                  </el-tooltip>
                </template>
              </el-table-column> -->
            </el-table>
          </div>

          <el-row :gutter="0" style="text-align: center; width: 100%; margin-top: 20px" justify="between">
            <el-button @click="returnBtn">上一步</el-button>
            <div>
              <el-button @click="deliverResults" :loading="state.consultLoading">下发一级调度结果</el-button>
            </div>
            <div>
              <el-button @click="nextStep" v-if="state.returnInfoList.length > 0">
                下一步
              </el-button>
            </div>
          </el-row>
        </el-carousel-item>
        <el-carousel-item name="各域反馈">
          <div style="display: flex;justify-content: space-around;align-items: center">
            <el-progress style="width: calc(100% - 55px);" :percentage="state.progressBar1" :text-inside="true"
              :stroke-width="5" :color="customColor">
              <template v-slot>
                <div class="progressDiv"></div>
              </template>
            </el-progress>
            <span style="margin-left: 5px;width: 50px;flex-shrink: 0;">{{ state.progressBar1 }}%</span>
          </div>

          <div style="width: 100%; height: 25px; line-height: 25px; color: #1989fa;display: flex">
            <div class="infoIcon"></div>
            <div class="infoText">
              【消息】：{{ state.progressText }}</div>
            <div class="infoOpenText" v-show="state.foldHeight == '21%'" @click="state.foldHeight = '80%'">展 开</div>
            <div class="infoOpenText" v-show="state.foldHeight == '80%'" @click="state.foldHeight = '21%'">收 起</div>

          </div>

          <el-row :gutter="0" style="width: 100%; height: 4vh">
            <el-col :span="4" class="countClass" style="border-right: 0">
              <span class="countTitle">合计调度数：</span>
              <span style="color: #1989fa;">{{ state.progressBar1Count[0] }}</span>
            </el-col>
            <el-col :span="4" class="countClass" style="border-right: 0">
              <span class="countTitle">调度成功：</span>
              <span style="color: #03a2a7;">{{ state.progressBar1Count[1] }}</span>
            </el-col>
            <el-col :span="4" class="countClass">
              <span class="countTitle">调度失败：</span>
              <span style="color: red;">{{ state.progressBar1Count[2] }}</span>
            </el-col>
          </el-row>

          <div style="height: 45vh; width: 100%; margin-top: 10px">
            <el-table :data="returnInfoTableList" style="width: 100%" class="custom-table">

              <el-table-column type="expand">
                <template #default="{ row }">
                  <div m="4" style="padding:5px;background-color: #99999942;" v-if="row.equipments.length > 0">
                    <el-row :gutter="30">

                      <el-col :span="2"></el-col>
                      <el-col :span="2" style="color: #2f86ff;font-weight: bold;">设备名称:</el-col>
                      <el-col :span="6">{{ row.equipments[0].equipmentName }}</el-col>
                      <el-col :span="2" style="color: #2f86ff;font-weight: bold;">设备类型:</el-col>
                      <el-col :span="6">{{ row.equipments[0].generalName }}</el-col>

                      <!-- <el-col :span="2">设备总覆盖率:</el-col>
                      <el-col :span="6">{{ row.equipments[0].coverRate ? parseInt(row.equipments[0].coverRate * 10000) /
                        100 + "%" : 0 }}</el-col> -->
                    </el-row>
                    <!-- <el-table
                      style="border-right: 1px solid #2b3b47;border-top: 1px solid #2b3b47;border-left: 1px solid #2b3b47;"
                      :data="props.row.equipments" :border="Border">
                      <el-table-column label="设备名称" prop="equipmentName" align="center" />
                      <el-table-column label="设备类型" prop="generalName" align="center" />
                      <el-table-column label="覆盖率" prop="coverRate" align="center">
                        <template v-slot="{ row }">
                          <span>{{ row.coverRate ? parseInt(row.coverRate * 10000) / 100 + "%" : 0 }}</span>
                        </template>
      </el-table-column>
    </el-table> -->
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="任务名" prop="taskName" align="center" min-width="150"></el-table-column>
              <!-- <el-table-column label="设备名称" prop="equipmentName" align="center" min-width="150"></el-table-column> -->
              <el-table-column label="资源域" prop="areaName" align="center" min-width="150"></el-table-column>
              <el-table-column label="设备开始使用时间" align="center" min-width="100">
                <template v-slot="{ row }">{{ timeSpan(row.entryTime) }}</template>
              </el-table-column>
              <el-table-column label="设备结束使用时间" align="center" min-width="100">
                <template v-slot="{ row }">{{ timeSpan(row.exitTime) }}</template>
              </el-table-column>
              <!-- <el-table-column label="使用时间" align="center" min-width="200">
                <template v-slot="{ row }">{{ timeSpan(row.startTime) }} - {{ timeSpan(row.endTime) }}</template>
              </el-table-column> -->
              <el-table-column label="调度结果" prop="result" align="center" width="150">
                <template v-slot="{ row }">
                  <span v-if="row.result" style="color: #03a2a7">成功</span>
                  <span v-else style="color: red">失败</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-row :gutter="0" style="text-align: center; width: 100%; margin-top: 20px" justify="between">
            <el-button @click="returnBtn" style="margin-right: 8px"
              :disabled="state.progressBar1 != 100">上一步</el-button>
            <div>
              <!-- !state.putInStorage && -->

              <el-button @click="submitResult" style="margin-right: 8px"
                v-if="state.putInStorage && state.progressBar1 == 100">
                形成最终调度结果
              </el-button>
              <el-button @click="consultWithArea" style="margin-right: 8px"
                v-if="state.progressBar1 == 100 && !state.returnInfoList.every((_) => !!_.result)">进行冲突消解</el-button>
            </div>
            <div>
              <el-button @click="nextStep" v-if="state.nodeResult.length > 0 || state.nodeResultChoose.length > 0"
                style="margin-right: 8px" :disabled="state.progressBar1 != 100">
                下一步
              </el-button>
            </div>
          </el-row>
        </el-carousel-item>
        <el-carousel-item name="二次协商">
          <div style="display: flex;justify-content: space-around;align-items: center">
            <el-progress style="width: calc(100% - 55px);" :percentage="state.progressBar2" :text-inside="true"
              :stroke-width="5" :color="customColor">
              <template v-slot>
                <div class="progressDiv"></div>
              </template>
            </el-progress>
            <span style="margin-left: 5px;width: 50px;flex-shrink: 0;">{{ state.progressBar2 }}%</span>
          </div>
          <div style="width: 100%; height: 25px; line-height: 25px; color: #1989fa;display: flex">
            <div class="infoIcon"></div>
            <div class="infoText">
              【消息】：{{ state.progressText }}</div>
            <div class="infoOpenText" v-show="state.foldHeight == '21%'" @click="state.foldHeight = '80%'">展 开</div>
            <div class="infoOpenText" v-show="state.foldHeight == '80%'" @click="state.foldHeight = '21%'">收 起</div>
          </div>
          <el-row :gutter="0" style="width: 100%; height: 4vh">
            <el-col :span="4" class="countClass" style="border-right: 0">
              <span class="countTitle">合计调度数：</span>
              <span style="color: #1989fa;">{{ state.progressBar2Count[0] }}</span>
            </el-col>
            <el-col :span="4" class="countClass" style="border-right: 0">
              <span class="countTitle">调度成功：</span>
              <span style="color: #03a2a7;">{{ state.progressBar2Count[1] }}</span>
            </el-col>
            <el-col :span="4" class="countClass">
              <span class="countTitle">调度失败：</span>
              <span style="color: red;">{{ state.progressBar2Count[2] }}</span>
            </el-col>
          </el-row>

          <div style="height: 44vh; width: 100%; margin-top: 10px">
            <el-table :data="state.nodeResult.filter((_) => !!_.result)" style="width: 100%" ref="nodeResultPassRef"
              class="custom-table" @selection-change="secondSelectionChange" :row-class-name="setClassName">
              <el-table-column type="selection" width="55" align="center" :selectable="(row) => {
                return !!row.result
              }" />

              <el-table-column type="expand">
                <template #default="{ row }">
                  <div m="4" style="padding:5px;background-color: #99999942;">
                    <el-row :gutter="30" v-if="row.equipments.length != 0">
                      <el-col :span="2"></el-col>
                      <el-col :span="2" style="color: #2f86ff;font-weight: bold;">设备名称:</el-col>
                      <el-col :span="6">{{ row.equipments[0].equipmentName }}</el-col>
                      <el-col :span="2" style="color: #2f86ff;font-weight: bold;">设备类型:</el-col>
                      <el-col :span="6">{{ row.equipments[0].generalName }}</el-col>
                      <!-- <el-col :span="2">设备总覆盖率:</el-col>
                      <el-col :span="6">{{ row.equipments[0].coverRate ? parseInt(row.equipments[0].coverRate * 10000) /
                        100 + "%" : 0 }}</el-col> -->
                    </el-row>

                    <!-- <el-table v-if="props.row.equipments.length != 0"
                      style="border-right: 1px solid #2b3b47;border-top: 1px solid #2b3b47;border-left: 1px solid #2b3b47;"
                      :data="props.row.equipments" :border="Border">
                      <el-table-column label="设备名称" prop="equipmentName" align="center" />
                      <el-table-column label="设备类型" prop="generalName" align="center" />
                      <el-table-column label="覆盖率" prop="coverRate" align="center">
                        <template v-slot="{ row }">
                          <span>{{ row.coverRate ? parseInt(row.coverRate * 10000) / 100 + "%" : 0 }}</span>
                        </template>
      </el-table-column>
    </el-table> -->
                  </div>
                </template>
              </el-table-column>


              <el-table-column label="任务名" prop="taskName" align="center" min-width="150"></el-table-column>
              <!-- <el-table-column label="设备名称" prop="equipmentName" align="center" min-width="150"></el-table-column> -->
              <el-table-column label="资源域" prop="areaName" align="center" min-width="150"></el-table-column>
              <el-table-column label="调度结果" prop="result" align="center" width="150">
                <template v-slot="{ row }">
                  <span v-if="row.result" style="color: #03a2a7">调度成功</span>
                  <span v-else style="color: red">调度失败</span>
                </template>
              </el-table-column>
              <el-table-column label="时间" align="center" min-width="200">
                <template v-slot="{ row }">{{ timeSpan(row.entryTime) }} - {{ timeSpan(row.exitTime) }}</template>
              </el-table-column>
            </el-table>
          </div>
          <el-row :gutter="0" style="text-align: center; width: 100%; margin-top: 20px" justify="between">
            <el-button @click="returnBtn" style="margin-right: 8px"
              :disabled="state.progressBar2 != 100">上一步</el-button>
            <div></div>
            <div>
              <el-button @click="nextStep" style="margin-right: 8px"
                v-if="state.nodeResultChoose.length > 0 && state.progressBar2 == 100">
                下一步
              </el-button>
            </div>
          </el-row>
        </el-carousel-item>
        <el-carousel-item name="下发最终调度结果">
          <div style="height: 54vh; width: 100%; margin-bottom: 5px">
            <el-table :data="state.nodeResultChoose" style="width: 100%" class="custom-table" highlight-current-row>

              <el-table-column type="expand">
                <template #default="{ row }">
                  <div m="4" style="padding:5px;background-color: #99999942">
                    <el-row :gutter="30" v-if="row.equipments.length != 0">
                      <el-col :span="2"></el-col>
                      <el-col :span="2" style="color: #2f86ff;font-weight: bold;">设备名称:</el-col>
                      <el-col :span="6">{{ row.equipments[0].equipmentName }}</el-col>
                      <el-col :span="2" style="color: #2f86ff;font-weight: bold;">设备类型:</el-col>
                      <el-col :span="6">{{ row.equipments[0].generalName }}</el-col>
                      <!-- <el-col :span="2">设备总覆盖率:</el-col>
                      <el-col :span="6">{{ row.equipments[0].coverRate ? parseInt(row.equipments[0].coverRate * 10000) /
                        100 + "%" : 0 }}</el-col> -->
                    </el-row>
                    <!-- <el-table v-if="props.row.equipments.length != 0"
                      style="border-right: 1px solid #2b3b47;border-top: 1px solid #2b3b47;border-left: 1px solid #2b3b47;"
                      :data="props.row.equipments" :border="Border">
                      <el-table-column label="设备名称" prop="equipmentName" align="center" />
                      <el-table-column label="设备类型" prop="generalName" align="center" />
                      <el-table-column label="覆盖率" prop="coverRate" align="center">
                        <template v-slot="{ row }">
                          <span>{{ row.coverRate ? parseInt(row.coverRate * 10000) / 100 + "%" : 0 }}</span>
                        </template>
      </el-table-column>
    </el-table> -->
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="任务名" prop="taskName" align="center" min-width="150"></el-table-column>
              <!-- <el-table-column label="设备名称" prop="equipmentName" align="center" min-width="150"></el-table-column> -->
              <el-table-column label="资源域" prop="areaName" align="center" min-width="150"></el-table-column>

              <el-table-column label="资源占用时间" align="center" min-width="200">
                <template v-slot="{ row }">{{ timeSpan(row.entryTime) }} - {{ timeSpan(row.exitTime) }}</template>
              </el-table-column>
            </el-table>
          </div>
          <el-row :gutter="0" style="text-align: center; width: 100%; margin-top: 20px" justify="between">
            <el-button @click="returnBtn" style="margin-right: 8px">上一步</el-button>
            <div>
              <el-button @click="redoSteps" style="margin-right: 8px">重新调度</el-button>
              <el-button @click="saveConsult(state.nodeResultChoose)" style="margin-right: 8px">保存</el-button>
            </div>
            <div></div>
          </el-row>
        </el-carousel-item>

        <!-- <el-carousel-item name="结果确认">
          <div>
            <el-button @click="returnBtn" style="margin-right: 8px">上一步</el-button>
          </div>
        </el-carousel-item> -->
      </el-carousel>
      <div class="res-time" v-if="state.stepsActive == 1 && state.ddTime">
        <span>调度耗时:{{ state.ddTime }}秒</span>
      </div>
    </template>
  </customPopup1>
  <!-- <customPopup1 left="25%" top="70%" width="50%" height="30%" v-if="state.searchBoxShow">
    <template #content></template>
  </customPopup1> -->

  <CesiumBox></CesiumBox>
  <div class="showOrHidden-box" style="right:24%">
	<div>
		<label>名称显隐：</label>
		<el-switch v-model="state.showLabel" inline-prompt active-text="显示" inactive-text="隐藏" />
	</div>
	<div>
		<label>范围显隐：</label>
		<el-switch v-model="state.showArea" inline-prompt active-text="显示" inactive-text="隐藏" />
	</div>
  </div>

  <customPopup right="35%" top="30%" width="30%" height="35%" v-if="state.chooseShow">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">选择资源</span>
    </template>
    <template #content>
      <el-table :data="state.chooseResourceHand" class="custom-table" style="height: calc(100% - 45px) !important">
        <el-table-column width="30" type="index" align="center"></el-table-column>
        <el-table-column label="资源名称" prop="name" align="center" show-overflow-tooltip />
        <el-table-column label="任务名" align="center" show-overflow-tooltip>
          <template v-slot="{ row }">
            {{ taskName(row.taskId) }}
          </template>
        </el-table-column>
        <el-table-column label="覆盖率" prop="averageCoverage" align="center" width="80">
          <template v-slot="{ row }">
            {{ row.averageCoverage ? parseInt(row.averageCoverage * 10000) / 100 + "%" : 0 }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template v-slot="{ row }">
            <!-- <el-tooltip effect="dark" content="查看定位" placement="top-start">
              <el-link :underline="false" @click.stop="flyToResource(row)">
                <el-icon :size="20"><Position style="width: 20px; height: 20px" /></el-icon>
              </el-link>
            </el-tooltip> -->
            <el-tooltip effect="dark" content="移除资源" placement="top-start">
              <el-link :underline="false" @click.stop="resourceRemove(row)" style="margin-left: 10px">
                <el-icon :size="20">
                  <Remove style="width: 20px; height: 20px" />
                </el-icon>
              </el-link>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: right; margin-top: 15px">
        <el-button @click="state.chooseShow = false">关闭</el-button>
        <!-- <el-button @click="saveResource">保存配置</el-button> -->
        <el-button @click="submitResource">保存配置</el-button>
      </div>
    </template>
  </customPopup>
  <!-- v-if="rightShow" -->
  <!-- 手动调度 -->
  <customPopup right="10px" top="80px" width="23%" height="90%" v-if="state.resourceBoxShow">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">资源列表</span>
      <div style="position: absolute; right: 2%">
        <el-button @click="goBack">返回</el-button>
      </div>
    </template>
    <template #content>
      <div style="display: flex; justify-content: flex-start; width: 100%; align-items: center;margin-bottom: 10px;">
        <div style="width: 60px">任&nbsp;&nbsp;&nbsp;&nbsp;务：</div>
        <el-select style="width: calc(100% - 60px)" v-model="state.taskInfo" value-key="id" placeholder="请选择任务"
          clearable @change="taskInfoChange">
          <el-option v-for="item in state.taskList" :key="item.id" :label="item.taskName" :value="item"></el-option>
        </el-select>
      </div>
      <el-table ref="tableHand" :border="Border" :data="state.chooseResourceHand"
        style="width: 100%; height: calc(100% - 100px) !important;" class="custom-table"
        @selection-change="(list) => { state.chooseHand = list }">
	    <el-table-column label="-" width="25" align="center">
			<template #header>
				<el-icon @click="onShowAllEntity" class="show_hide_icon">
					<View v-if="state.isShowAll !== false"/>
					<Hide v-else/>
				</el-icon>
			</template>
		  <template v-slot="{ row }">
			  <el-icon @click="onShowEntity(row)" class="show_hide_icon">
				  <View v-if="row.isShow !== false"/>
				  <Hide v-else/>
			  </el-icon>
		  </template>
	    </el-table-column>

        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="任务名" prop="task.taskName" align="center" /> -->
        <el-table-column label="资源域" prop="areaName" align="center">
          <template v-slot="{ row }">
            <span>{{ areaName(row.areaId) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="设备名称" prop="name" align="center" />
        <el-table-column label="覆盖率" prop="coverRate" align="center" width="80">
          <template v-slot="{ row }">
            {{ row.coverRate ? parseInt(row.coverRate * 10000) / 100 + "%" : 0 }}
          </template>
        </el-table-column>
        <!-- @selection-change="NewselectionChange" -->
      </el-table>
      <div style="height: 75px;line-height: 75px;text-align: center">
        <el-button @click="submitResource">确认选择</el-button>
      </div>
      <!-- <div style="display: flex; justify-content: flex-start; width: 100%; align-items: center; margin-top: 10px">
        <div style="width: 60px">{{ "资源域：" }}</div>
        <el-select v-model="state.currentArea" class="m-2" placeholder="请选择资源域" style="width: calc(100% - 60px)"
          size="large" @change="getAreaGeneralTree(state.currentArea)">
          <el-option v-for="item in state.areaList" :key="item.id" :label="item.areaName" :value="item.id" />
        </el-select>
      </div> -->

      <div style="height: 50%">
        <div style="height: 90%">
          <div style="position: relative; top: 6%">
            <template v-for="(item, key) in state.areaGeneralTree" :key="key">
              <div style="margin: 5px 0 6px 0">
                <img style="position: relative; top: 2px" src="/images/icon/需求管理_18.png" alt="" />
                <span style="font-size: 18px; font-weight: bold; padding-left: 10px; color: #309eff">
                  {{ item.dataTypeValue }}
                </span>
              </div>
              <div>
                <div style="display: flex" class="itemPage">
                  <div v-if="item.total == 0" style="width: 100%; text-align: center; height: 60px; line-height: 50px">
                    暂无数据
                  </div>
                  <!-- :style="item.type == 2 ? 'margin-right: 9%' : ''" -->
                  <el-tooltip v-if="item.total != 0" :content="item.page <= 1 ? '已经是第一页了' : '上一页'" placement="bottom"
                    effect="light">
                    <el-button class="leftBtn" circle @click="pageChange('left', key)">
                      <el-icon>
                        <ArrowLeftBold />
                      </el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip v-if="item.total != 0" :content="item.page >= item.maxPage ? '已经是最后一页了' : '下一页'"
                    placement="bottom" effect="light">
                    <el-button class="rightBtn" circle @click="pageChange('right', key)">
                      <el-icon>
                        <ArrowRightBold />
                      </el-icon>
                    </el-button>
                  </el-tooltip>
                  <div v-for="resource in item.dataList" :key="resource.id"
                    :class="{ chooseItem: resource.id == state.lastResource.id }"
                    @click="flyToResource(resource, item.dataTypeValue, item.dataType)">
                    <!-- @click="resourceClick(resource, item.dataType)"
                    @dblclick="resourceDblClick(resource, item.dataTypeValue)" -->
                    <!-- :class="{ chooseItem: chooseItem(resource.id) }" -->
                    <!-- <div style="text-align: center">{{ state.resourceObj[id].name }}</div> -->
                    <span class="nameIcon">
                      <!-- @click="addTarget(target, classify)" -->
                      <div class="dj_icon itemIcon" v-if="key == 1"></div>
                      <div class="kj_icon itemIcon" v-else-if="key == 2"></div>
                      <div class="tj_icon itemIcon" v-else-if="key == 3"></div>
                      <div class="kj_icon itemIcon" v-else-if="key == 4"></div>
                      <span style="width: 50px; text-align: center">{{ resource.name }}</span>
                      <div class="resourceOperation">
                        <el-tooltip effect="dark" content="显示资源" placement="top-start" :key="state.forceUpdate"
                          v-if="!showEntity(resource.id)">
                          <el-link :underline="false">
                            <el-icon :size="20" @click.stop="showResource(resource, item.dataType, true)">
                              <View style="width: 20px; height: 20px" />
                            </el-icon>
                          </el-link>
                        </el-tooltip>
                        <el-tooltip effect="dark" content="隐藏资源" placement="top-start" :key="state.forceUpdate + '_'"
                          v-else>
                          <el-link :underline="false">
                            <el-icon :size="20" @click.stop="showResource(resource, item.dataType, false)">
                              <Hide style="width: 20px; height: 20px" />
                            </el-icon>
                          </el-link>
                        </el-tooltip>

                        <el-tooltip effect="dark" content="选择资源" placement="top-start">
                          <el-link :underline="false" @click.stop="resourceChoose(resource)">
                            <el-icon :size="20">
                              <Finished style="width: 20px; height: 20px" />
                            </el-icon>
                          </el-link>
                        </el-tooltip>
                      </div>
                    </span>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div style="height: calc(40% - 50px); margin-top: 8%" v-show="state.resourceInfo.id">
        <div style="margin: 5px 0 6px 0">
          <img style="position: relative; top: 2px" src="/images/icon/需求管理_18.png" alt="" />
          <span style="font-size: 18px; font-weight: bold; padding-left: 10px; color: #309eff">资源信息</span>
        </div>
        <div style="height: calc(100% - 5px)" class="demandDetail">
          <el-form :inline="false" :model="state.resourceInfo" label-position="right" label-width="100px">
            <el-form-item label="资源名称">
              <span>{{ state.resourceInfo.name }}</span>
            </el-form-item>
            <el-form-item label="资源类型">
              <span>{{ state.resourceInfo.dataTypeValue }}</span>
            </el-form-item>
            <el-form-item label="位置" v-if="state.resourceInfo.longitude">
              <span>
                {{ state.resourceInfo.longitude }},{{ state.resourceInfo.latitude }},
                {{ state.resourceInfo.height || state.resourceInfo.altitude }}
              </span>
            </el-form-item>
            <el-form-item label="天线类型">
              {{ dictValue("antennaType", state.resourceInfo.antennaType) }}
            </el-form-item>
            <el-form-item label="侦察半径" v-if="state.resourceInfo.radius">
              <span>{{ state.resourceInfo.radius }}m</span>
            </el-form-item>
            <el-form-item label="底点半径" v-if="state.resourceInfo.bottomRadius">
              <span>{{ state.resourceInfo.bottomRadius }}m</span>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </template>
  </customPopup>

  <!-- 算法选择 -->
  <div>
    <el-dialog v-model="isAuto" title="调度算法配置" width="30%" center>
      <p class="autoSubTitle">自动调度配置：</p>
      <div class="auto-Box">
        <div class="autoBase-box auto-box" :class="{ chooseAlgorihtm: state.algorihtm.name == el.name }"
          v-for="(el, index) in autoBaseList" :key="index" @click="
            state.algorihtm = el;
          state.sortValueList = [];
          getSortClass();
          ">
          {{ el.name }}
        </div>
        <!-- 暂时隐藏其他算法 -->
        <!-- <div class="autoTri-box auto-box" :class="{ chooseAlgorihtm: state.algorihtm.name == el.name }"
          @click="state.algorihtm = el" v-for="(el, index) in tripartiteList" :key="index">
          {{ el.name }}
        </div> -->
      </div>
      <!-- 暂时隐藏优先级 -->
      <!-- <div class="auto-Box" v-show="state.algorihtm.name == '优先级排序算法'">
        <p class="autoSubTitle">优先级</p>
        <div class="autoTri-box auto-box" :class="{ chooseAlgorihtm: state.sortValueList.includes(key) }"
          @click="sortByClass(key)" v-for="(label, key) in state.sortList" :key="key">
          {{ label }}
        </div>
      </div> -->
      <p class="autoSubTitle">手动调度：</p>
      <div class="auto-Box">
        <div class="autoBase-box auto-box" :class="{ chooseAlgorihtm: state.algorihtm.name == el.name }"
          v-for="(el, index) in handleBaseList" :key="index" @click="
            state.algorihtm = el;
          state.sortValueList = [];
          getSortClass();
          ">
          {{ el.name }}
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button style="width: 80px" type="primary" @click="submitAuto">确 认</el-button>
          <el-button style="width: 80px" @click="cancelAuto">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <el-dialog v-model="state.timeShow" title="占用时间选择" width="30%" center>
    <div style="width: 100%; text-align: center">
      <el-date-picker v-model="state.timeScope" type="datetimerange" range-separator="至" start-placeholder="开始占用时间"
        end-placeholder="结束占用时间" format="YYYY-MM-DD hh:mm:ss" value-format="YYYY-MM-DD hh:mm:ss" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button style="width: 80px" type="primary" @click="saveTimeScope">保 存</el-button>
        <el-button style="width: 80px" @click="() => {
          state.timeShow = false;
          state.timeScope = [];
        }
          ">
          取 消
        </el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 已选择框 -->
  <el-dialog v-model="state.resourceDetailShow" title="已选择资源信息" width="550px" center>
    <div style="width: 100%; text-align: center">
      <el-form :inline="false" :model="state.resourceDetail" label-position="right" label-width="120px">
        <el-form-item label="资源名称">
          <span>{{ state.resourceDetail.equipmentDetail.name }}</span>
        </el-form-item>
        <el-form-item label="资源类型">
          {{ state.resourceDetail.type }}
        </el-form-item>
        <el-form-item label="所属域">
          <span>{{ areaName(state.resourceDetail.areaId) }}</span>
        </el-form-item>
        <el-form-item label="开始占用时间">
          <span>{{ state.resourceDetail.startTime }}</span>
        </el-form-item>
        <el-form-item label="结束占用时间">
          <span>{{ state.resourceDetail.endTime }}</span>
        </el-form-item>
        <el-form-item label="设备总覆盖率">
          {{ state.resourceDetail.coverRate * 100 + "%" }}
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button style="width: 80px" type="primary" @click="state.resourceDetailShow = false">关 闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
  import CesiumBox from "@/components/evmap/CesiumBox.vue";
  import customPopup from "@/components/customPopup.vue";
  import { reactive, ref, onUnmounted, onMounted, watch, nextTick } from "vue";
  import * as areaApi from "@/service/API/system/areaManage.js";
  import * as requirementApi from "@/service/API/home/<USER>";
  import * as dataApi from "@/service/API/system/resourceDispatch.js";
  import { initSatllite, satlliteTest, getSatllite } from "@/utils/mySatellite.js";
  import { ElMessage, ElLoading } from "element-plus";
  // import { drawMap } from "@/utils/utils.js";
  import { useRoute, useRouter } from "vue-router";
  import customPopup1 from "@/components/customPopup1.vue";
  import { time } from "echarts";
  import { isArray } from "element-plus/es/utils/types.mjs";
  // import * as mapTool from "@/utils/mapTool.js";
  const dictTypeList = window.dictTypeList;
  //字典转换
  const dictValue = window.dictValue;
  //时间格式
  const timeSpan = window.timeSpan;
  const taskTableRef = ref(null);
  const state = reactive({
    requirementList: [], //需求列表
    currentRequirement: [], //当前需求ids
    startTime: null, //筛选条件-开始时间
    endTime: null, ////筛选条件-结束时间
    searchStatus: null, //筛选条件-状态
    selectRequirementRow: {}, //当前需求info
    pendingTaskIds: [], // 从taskPage传过来的任务ID，用于筛选任务列表
    taskList: [], //任务列表
    taskInfo: {
      id: undefined,
    }, //当前任务，
    taskIdList: [], //选中的任务ID
    targetList: [], //目标列表，
    targetInfo: [], //当前目标列表
    areaList: [], // 资源域列表
    currentArea: null, //当前选中的资源域
    areaGeneralTree: {}, //资源域分类列表
    resourceObj: {}, //具体资源 形式为 {资源ID：资源信息}
    usedResourceList: [], //已选择资源
    resourceInfo: {}, //资源信息
    chooseResource: [], //选中的资源
    chooseResourceHand: [], //可选中的资源(手动)
    chooseHand: [],//手动选择的资源
    timeShow: false, //占用时间的弹窗
    timeScope: [], //占用的时间范围
    algorihtm: {}, //当前选中的算法
    resourceDetailShow: false, //已选择资源显示
    resourceDetail: {}, //已选择资源查看行信息
    lastResource: {}, //lastResource是最后被高亮的资源，存储起来是为了恢复上个被查看资源的颜色
    forceUpdate: 0, //用作更新图标视图
    chooseShow: false, //是否显示已选择的资源小框
    sortList: [], //算法排序条件
    sortValueList: [], //算法排序值
    searchForm: {},
    searchBoxShow: true, //条件筛选框
    resourceBoxShow: false, //资源框显隐
    stepsActive: 0, //步骤条值
    returnInfo: true, //展示返回信息
    returnInfoList: [], //回调的信息
    reAgainInfoList: [], //再次确认的信息
    consultLoading: false, //等待回调
    nodeResult: [], //域节点协商结果
    nodeResultChoose: [], //选中的节点协商结果
    putInStorage: false, //是否在一级返回结果下显示入库按钮
    progressBar1: 0, //进度条1
    progressBar2: 0, //进度条2
    progressBar1Count: [0, 0, 0],
    progressBar2Count: [0, 0, 0],
    foldHeight: "80%",
    firstLevelList: [],// sdw 以及调度中的选择列表
    noSelected: [],//调度中未被选中的列表
    ddTime: 0, // 调度耗时时间
    checkNode: [],//二次调度的勾选结果，用于回显
    showArea: true, // 显隐范围
    showLabel: true, //名称显隐
  });
  // 回调的表格信息
  const returnInfoTableList = ref([])
  const nodeResultPassRef = ref()//二级调度的结果表格
  const Border = ref(false);//sdw 表格边框
  const tableL = ref();//sdw 表格的refs
  const isJump = ref(false); //sdw 判断是否是从第二步直接到第四步
  const customColor = [
    { color: "#f56c6c", percentage: 20 },
    { color: "#e6a23c", percentage: 40 },
    { color: "#1989fa", percentage: 60 },
    { color: "#5cb87a", percentage: 80 },
    { color: "#03a2a7", percentage: 100 },
  ];
  //sdw 增加result为0的行的class
  const setClassName = ((row, index) => {
    return row.row.result == '0' ? "noexpands" : "";
  });
  const carouselRef = ref(null);
  const nodeResultRef = ref(null); //域节点协商结果表格ref
  const route = useRoute();
  const router = useRouter(); //sdw 增加路由跳转
  onUnmounted(() => {
    // window.EVGISMAP("removeAllEntityByGroup", { group: "EVMAPGROUP" });
    // window.EVGISMAP("removeAllEntityByGroup", { group: "DRAWLAYERGROUP" });
    mapTool.removeAll();
    // window.EVGISMAP("removeAllEntityByGroup", { group: "EVMAPGROUP" });
    // Viewer.entities.removeAll();
    window.removeEventListener("onmessageWS", getSocketData);
  });

  onMounted(() => {
    //获取所有需求
    //在切换需求后请求需求下所有的任务
    //点击任务切换目标列表
    //查询已选择资源
    //手动调度查询资源域
    //资源域查询组装资源分类后对资源进行查询
    //
    window.addEventListener("onmessageWS", getSocketData);
    let requirementId = route.query?.requirementId;
    let xqParam = route.query?.xq;
    let taskIdsParam = route.query?.taskIds;

    // 保存传入的需求ID，在获取需求列表后再设置选中状态
    let pendingRequirementIds = [];
    if (requirementId) {
      pendingRequirementIds = [requirementId];
    } else if (xqParam) {
      // 处理从taskPage传过来的xq参数
      pendingRequirementIds = xqParam.split(',').filter(id => id.trim());
    }

    // 保存传入的任务ID，用于后续筛选任务列表
    if (taskIdsParam) {
      state.pendingTaskIds = taskIdsParam.split(',').filter(id => id.trim());
    }

    getRequirementList(pendingRequirementIds);
    getAreaList();
    // 默认配置第一个算法
    state.algorihtm = autoBaseList.value[0] || {}
    return;
  });
  // 显隐雷达
  watch(() => state.showArea, (val) => {
	  entityList.forEach((ele) => {
		  mapTool.showEffById(ele.id, ['cylinder', 'radar'], val)
	  })
  })
  // 显隐名称
  watch(() => state.showLabel, (val) => {
	  mapTool.showEffByAll('label', val)
  })
  //获取所有需求
  function getRequirementList(pendingRequirementIds) {
    requirementApi
      .getRequirementList({
        pageNum: 1,
        pageSize: 999,
      })
      .then((res) => {
        if (res.data.code == 200) {
          state.requirementList = res.data.data.records;
          console.log('requirementList',state.requirementList)

          if (state.requirementList.length > 0) {
            // 验证并设置传入的需求ID
            if (pendingRequirementIds && pendingRequirementIds.length > 0) {
              // 过滤出存在于需求列表中的ID
              const validRequirementIds = pendingRequirementIds.filter(id =>
                state.requirementList.some(item => item.id === id)
              );

              if (validRequirementIds.length > 0) {
                state.currentRequirement = validRequirementIds;
                getTaskList();
              }
            }
            // getTaskList(state.requirementList[0]);
            // state.currentRequirement = state.requirementList[0].id;
            // state.selectRequirementRow = state.requirementList[0];
          } else {
            state.taskList = [];
            state.targetList = [];
            state.usedResourceList = [];
          }
        } else {
          ElMessage.error(res.data.message);
        }
      });
  }
  //获取需求子任务列表
  function getTaskList() {
    if (state.currentRequirement.length == 0 && !state.startTime && !state.endTime) {
      state.taskList = []
      selectionChange([]);
      return
    }
    let serachForm = {
      page: 1,
      size: 999,
      requirementIds: state.currentRequirement,
      startTime: state.startTime,
      endTime: state.endTime,
    };
    // mapTool.deleteByGroup("EVMAPGROUP");
    clearTarget()
    //getTaskList
    requirementApi.getTaskList({ ...serachForm }).then((res) => {
      if (res.data.code == 200) {
        // state.taskList = res.data.data;
        let allTasks = res.data.data.records;

        // 如果有传入的任务ID，则根据任务ID筛选任务列表
        if (state.pendingTaskIds && state.pendingTaskIds.length > 0) {
          state.taskList = allTasks.filter(task =>
            state.pendingTaskIds.includes(task.id)
          );
        } else {
          state.taskList = allTasks;
        }

        //清除上个任务绘制的目标、资源及航迹
        // mapTool.deleteByGroup("EVMAPGROUP");
        state.chooseShow = false;
        selectionChange([]);

        // if (state.taskList.length == 0) {
        //   state.targetList = [];
        //   state.usedResourceList = [];
        // } else {
        //   getTaskTargets(state.taskList[0]);
        //   getUsedResourceList(state.taskList[0]);
        // }
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }
  //调度算法配置确认
  function submitAuto() {
    isAuto.value = false;
  }
  function clearTarget() {
    mapTool.deleteByGroup("EVMAPGROUP");
    window.EVGISMAP("removeGroupEntityById", {
      id: state.targetList.id + "_taskLine",
      group: "EVMAPGROUP",
    })

  }
  //获取任务下目标
  function getTaskTargets(row) {
    state.taskInfo = row;
    //删除目标图层
    // mapTool.deleteByGroup("EVMAPGROUP");
    clearTarget()
    requirementApi.getTaskTarget({ taskId: row.id, isInterpolated: "false" }).then((res) => {
      if (res.data.code == 200) {
        state.targetList = res.data.data;
        drawAllTarget();
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }
  //获取任务下已使用资源
  function getUsedResourceList(row) {
    // ElMessage("查询已使用资源");
    dataApi.getEquipmentByTask({ taskId: row.id, page: 1, size: 999 }).then((res) => {
      if (res.data.code == 200) {
        state.usedResourceList = [];
        // let old = [...state.chooseResource];
        // state.chooseResource = [];
        if (res.data.data.records.length > 0) {
          state.usedResourceList = res.data.data.records;
          state.currentArea = state.usedResourceList[0].areaId;
          state.usedResourceList.forEach((item) => {
            item.equipmentDetail.averageCoverage = item.coverRate;
            // state.chooseResource.push(item.equipmentDetail);
          });
          // drawAllResources();
        } else {
          state.currentArea = state.currentArea || state.areaList[0]?.id;
        }
        getAreaGeneralTree(state.currentArea);
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }

  //获取资源域列表
  function getAreaList() {
    areaApi.getAreaList({ page: 1, size: 999, areaType: 1 }).then((res) => {
      if (res.data.code == 200) {
        state.areaList = res.data.data.records;
        if (state.areaList.length > 0) {
          // getAreaGeneralTree(state.areaList[0].id);
        } else {
          state.areaGeneralTree = {};
        }
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }

  /**
   *@description 分类获取域下资源数据
   */
  function getAreaGeneralTree(areaId) {
    state.currentArea = areaId;
    state.areaGeneralTree = {
      1: { dataTypeValue: "地基平台", dataType: 1, dataList: [], page: 1, size: 5, total: 0, maxPage: 1 },
      2: { dataTypeValue: "空基平台", dataType: 2, dataList: [], page: 1, size: 5, total: 0, maxPage: 1 },
      3: { dataTypeValue: "天基平台", dataType: 3, dataList: [], page: 1, size: 5, total: 0, maxPage: 1 },
    };
    mapTool.deleteByGroup("DRAWLAYERGROUP");
    for (const key in state.areaGeneralTree) {
      let item = state.areaGeneralTree[key];
      getAreaGeneralData(item.dataType, item.page, item.size);
    }
  }
  //分页获取域下面的资源设备
  function getAreaGeneralData(dataType, page, size) {
    areaApi
      .pageEquipmentByType({ page: page, size: size, type: dataType, areaId: state.currentArea, category: 1 })
      .then((res) => {
        if (res.data.code == 200) {
          let { records, total } = res.data.data;
          let pageSize = state.areaGeneralTree[dataType].size;
          state.areaGeneralTree[dataType].dataList = res.data.data.records;
          state.areaGeneralTree[dataType].total = res.data.data.total;
          if (total % pageSize) {
            state.areaGeneralTree[dataType].maxPage = parseInt(total / pageSize) + 1;
          } else {
            state.areaGeneralTree[dataType].maxPage = parseInt(total / pageSize);
          }
          setTimeout(() => {
            let list = [...state.areaGeneralTree[dataType].dataList];
            for (let item of state.usedResourceList) {
              if (dataType == item.dataType) {
                if (state.areaGeneralTree[dataType].dataList.length == 0) {
                  list.push(item.equipmentDetail);
                } else {
                  let x = state.areaGeneralTree[dataType].dataList.find((it) => {
                    return item.equipmentDetail.id == it.id && item.dataType == dataType;
                  });
                  if (!x) {
                    list.push(item.equipmentDetail);
                  }
                }
              }
            }
            // state.usedResourceList.forEach((item) => {
            // });
            drawAllResources(list, dataType);
          }, 1000);
        } else {
          ElMessage.error(res.data.message);
        }
      });
  }
  // 切换页码
  function pageChange(type, key) {
    if (state.areaGeneralTree[key].page <= 1 && type == "left") {
      return;
    } else if (state.areaGeneralTree[key].page >= state.areaGeneralTree[key].maxPage && type == "right") {
      return;
    }
    type == "left" && (state.areaGeneralTree[key].page = state.areaGeneralTree[key].page - 1);
    type == "right" && (state.areaGeneralTree[key].page = state.areaGeneralTree[key].page + 1);

    mapTool.deleteByGroup("DRAWLAYERGROUP");
    for (const key in state.areaGeneralTree) {
      let item = state.areaGeneralTree[key];
      getAreaGeneralData(key, state.areaGeneralTree[key].page, state.areaGeneralTree[key].size);
      // getAreaGeneralData(item.dataType, item.page, item.size);
    }
  }
  //绘制所有选中目标
  function drawAllTarget() {
    //绘制目标及其航迹
    console.log('绘制目标及其航迹', state.targetList);

    mapTool.drawMap({ ...state.targetList, group: "EVMAPGROUP" });
    let pos = []
    state.targetList.taskCoverPoint.forEach((item, index) => {
      pos[index] = [item[1], item[0]]
    })
    //  解决航迹线和任务航迹线重叠显示不正确问题
    let pos1 = pos.filter((v) => {
      if (
        typeof v[0] === 'number' &&
        typeof v[1] === 'number' &&
        !(Number.isNaN(v[0]) && Number.isNaN(v[1]))
      ) {
        return [v[0] + 0.01, v[1] + 0.01, 0]
      }
    })
    window.EVGISMAP("drawPolylineByAttr", {
      id: state.targetList.id + "_taskLine",
      position: pos1,
      group: "EVMAPGROUP",
      outlineColor: "yellow",
      outlineWidth: 5,
      zIndex: 1,
    });

  }
  //绘制当前域下所有数据
  function drawAllResources(dataList, dataType) {
    dataList.forEach((item) => {
      mapTool.drawMap({ ...item, dataType: dataType, equipmentId: item.id, wxAutoPos: true });
    });
    setTimeout(() => {
      state.forceUpdate = 1;
    }, 1000);
  }
  //切换需求
  function requirementValChange() {
    getTaskList();
  }
  var manualFlag = false;
  //点击手动调度
  function manualHandle() {
    // manualFlag = true;
    state.searchBoxShow = false;
    state.resourceBoxShow = true;

    const row = state.taskList[0] || {}
    state.taskInfo = row;
    row.id && taskInfoChange(row)

    state.chooseHand = [];
    state.chooseResourceHand = [];
    state.currentArea = null;
  }
  //切换任务
  function taskInfoChange({ id }) {
    state.taskIdList = [id];
    // state.taskInfo = state.taskList.find((item) => {
    //   return item.id == id;
    // });
    state.chooseShow = false;
    state.chooseResourceHand = [];
    getTaskTargets(state.taskInfo);

    // getUsedResourceList(state.taskInfo);
    scheduleAlgo(4)
  }
  // 显示全部实体
  function onShowAllEntity(){
    state.isShowAll = state.isShowAll === false
	state.chooseResourceHand.forEach((e) => {
		e.isShow = state.isShowAll
		mapTool.showEntityById(e.id, state.isShowAll)
	})
  }
  // 显示单个实体
  function onShowEntity(row){
	  row.isShow = row.isShow === false
	  mapTool.showEntityById(row.id, row.isShow)
  }
  var resourceInfoTemp;
  var dataTypeValueTemp;
  function saveTimeScope() {
    if (state.timeScope[0] && state.timeScope[1]) {
      resourceInfoTemp.startTime = state.timeScope[0];
      resourceInfoTemp.endTime = state.timeScope[1];

      state.timeShow = false;
      state.timeScope = [];
    } else {
      return;
    }
  }
  //勾选中的进入确认页面表格
  function secondSelectionChange(list) {
    if (state.stepsActive != 3) {
      return
    }
    state.nodeResultChoose = list;
    state.checkNode = list
    submitResult();
  }
  function submitResult() {
    let trueList = state.returnInfoList.filter(item => {
      return !!item.result
    })
    //sdw
    if (state.stepsActive == 2) {
      isJump.value = true;
      state.stepsActive = 4
      carouselRef.value.setActiveItem(state.stepsActive);
      state.nodeResultChoose = [...trueList];
      return
    }
    state.nodeResultChoose = [...state.nodeResultChoose, ...trueList]
  }
  //任务列表复选框选中值
  function selectionChange(list) {
    state.taskIdList = [];
    state.taskIdList = list.map((item) => {
      return item.id;
    });
    // state.chooseShow = false;
  }
  // sdw 一级调度中的表格新增选择框
  function NewselectionChange(list) {
    // state.chooseResource = list
    // console.log(list)
    // state.firstLevelList = [];
    state.firstLevelList = list;
    // console.log(state.firstLevelList,'firstLevelList')
  }
  //sdw 单选复选框
  function targetChecked(selection, row) {

    let selected = selection.length && selection.indexOf(row) !== -1;
    if (selected) { //选中 添加进数组
      if (state.firstLevelList.length == 0) {
        state.firstLevelList.push(row);
      } else {
        let index = state.firstLevelList.findIndex(item => item.taskId == row.taskId);
        if (index != -1) {
          tableL.value.toggleRowSelection(state.firstLevelList[index], false);
          state.firstLevelList.splice(index, 1);
          state.firstLevelList.push(row);
        } else {
          state.firstLevelList.push(row);
        }
      }
    } else { //取消选中 取消数组里面的值
      let index = state.firstLevelList.findIndex(item => item.taskId == row.taskId);
      state.firstLevelList.splice(index, 1)
    }
    console.log(state.firstLevelList, 'firstLevelList')
  }
  //sdw 全选
  function selectAll(selection) {
    console.log(selection)
    state.firstLevelList = []
    selection.forEach((item, index) => { //把重复taskId的给筛选出来
      if (state.firstLevelList.length == 0) {
        state.firstLevelList.push(item);
      } else {
        let indexT = state.firstLevelList.findIndex(cur => cur.taskId == item.taskId);
        if (indexT != -1) {

        } else {
          state.firstLevelList.push(item)
        }
      }
    })
    // console.log(state.firstLevelList,'firstLevelList')

    state.chooseResource.forEach(item => { //通过筛选出来的值和全部值比较进行取消勾选
      let sign = state.firstLevelList.indexOf(item)
      if (sign == -1) {
        tableL.value.toggleRowSelection(item, false);
      }
    })
  }

  function taskName(taskId) {
    return state.taskList.find((item) => {
      return item.id == taskId;
    })?.taskName;
  }
  //提交手动资源--时间窗  把这个的名字改成submitResource 把下面那个submitResource加个2就行
  function submitResource() {
    state.resourceBoxShow = false;
    state.searchBoxShow = true;
    state.chooseShow = false;
    state.stepsActive = 1;
    //调用的覆盖率算法 较之前调用的自动调度算法多了一个手动选择的列表传入
    //后端需要给schedule/algo 这个给接口加一个manual 参数 Array类型
    let manual = []
    // 如果你只想要ID的话就把这句放出来，把下面的赋值那句注释掉
    // manual = state.chooseHand.map(item => {
    //   return item.id
    // })

    manual = state.chooseHand
    manual.forEach(item => {
      delete item.entryTime
      delete item.exitTime
    })

    state.noSelected = []
    state.chooseResourceHand.forEach(task => {
      // 将没有勾选的资源域作为备选，即noSelect数据
      let hasRow = state.chooseHand.find(resource => {
        return task.id == resource.id
      })
      !hasRow && state.noSelected.push(task)
    })
    //调用自动算法
    scheduleAlgo(1, manual)
  }
  //提交手动资源 废弃
  function submitResource2() {
    state.resourceBoxShow = false;
    state.searchBoxShow = true;
    state.chooseShow = false;
    state.stepsActive = 1;
    dataApi.calculateCover({ equipments: state.chooseHand }).then(res => {
      if (res.data.code == 200) {
        state.chooseHand[0].totalCover = Math.min(1, (res.data.data))

        // 处理手动调配问题  drz todo
        state.chooseHand.forEach((item, index) => {
          item.taskLength = state.chooseHand.length
          item.taskIndex = index
        })
        state.chooseResource = [...state.chooseHand]
        state.returnInfoList = []
        state.noSelected = []
        state.chooseResourceHand.forEach(task => {
          // 将没有勾选的资源域作为备选，即noSelect数据
          let hasRow = state.chooseResource.find(resource => {
            return task.id == resource.id
          })
          !hasRow && state.noSelected.push(task)
        })
        state.areaGeneralTree = {};
        clearTarget()
        mapTool.deleteByGroup("DRAWLAYERGROUP");
        setTimeout(() => {
          taskTableRef.value.setCurrentRow(state.taskInfo);
          taskTableRef.value.toggleRowSelection(state.taskInfo, true);
          carouselRef.value.setActiveItem(1);
          state.chooseResource = JSON.parse(JSON.stringify(state.chooseResource))
          setTimeout(() => {
            tableL.value.doLayout()
          }, 200);
        }, 1000);
      } else {
        ElMessage.error(res.data.message)
      }
    })
    return
    // let totalCover = 0
    // state.chooseHand.forEach((item, index) => {
    //   item.taskIndex = index
    //   item.taskLength = state.chooseHand.length
    //   totalCover += item.coverRate || 0
    // })
    // 需计算综合覆盖率
    // 计算方法
    // let chooseHand = state.chooseHand.sort((a, b) => {
    //   return b.entryTime - a.entryTime
    // })
    // let sTime = chooseHand[0].entryTime,
    //   eTime = chooseHand[0].exitTime,
    //   sumTime = 0

    // chooseHand.forEach(task => {
    //   if (moment(task.entryTime).isAfter(eTime)) {
    //     sumTime = sumTime + moment.duration(moment(sTime).diff(eTime))
    //     sTime = task.entryTime;
    //     eTime = task.exitTime;
    //   } else {
    //     eTime = moment(eTime).isAfter(task.exitTime) ? eTime : task.exitTime;
    //   }
    // })
    // sumTime = sumTime + moment.duration(moment(sTime).diff(eTime))
    // let allTime = moment.duration(moment(state.taskInfo.startTime).diff(state.taskInfo.endTime))._milliseconds

    // state.chooseHand[0].totalCover = Math.min(1, (sumTime / allTime))

    // state.chooseResource = [...state.chooseHand]
    // state.returnInfoList = []
    // state.noSelected = []
    // state.chooseResourceHand.forEach(task => {
    //   // 将没有勾选的资源域作为备选，即noSelect数据
    //   let hasRow = state.chooseResource.find(resource => {
    //     return task.id == resource.id
    //   })
    //   !hasRow && state.noSelected.push(task)
    // })


    // // state.chooseResource = []
    // // state.chooseResourceHand.forEach(item => {
    // //   let obj = {}
    // //   let area = state.areaList.find(item2 => { return item2.id == item.areaId })
    // //   obj = {...area}
    // //   obj.areaName = area.areaName
    // //   let task = state.taskList.find(item2 => { return item2.id == item.taskId })
    // //   obj.task = task
    // //   obj.taskId = item.taskId
    // //   obj.taskName = item.taskName
    // //   equipments.push({})

    // // }
    // // )
    // // state.chooseResource = JSON.parse(JSON.stringify(state.chooseResourceHand));
    // // console.log("state.chooseResourceHand", state.chooseResourceHand);
    // state.areaGeneralTree = {};
    // // mapTool.deleteByGroup("EVMAPGROUP");
    // clearTarget()
    // mapTool.deleteByGroup("DRAWLAYERGROUP");
    // setTimeout(() => {
    //   taskTableRef.value.setCurrentRow(state.taskInfo);
    //   taskTableRef.value.toggleRowSelection(state.taskInfo, true);
    //   carouselRef.value.setActiveItem(1);
    //   state.chooseResource = JSON.parse(JSON.stringify(state.chooseResource))
    //   setTimeout(() => {
    //     tableL.value.doLayout()
    //   }, 200);
    // }, 1000);
  }
  //提交资源配置
  function saveResource(saveList) {

    let list = [];
    console.log("saveList", saveList);
    // return
    for (const item of saveList) {
      let index = list.findIndex((item2) => {
        return item.taskId == item2.taskId;
      });

      //sdw
      // list.push({
      //   taskId: item.taskId,
      //   equipments: item.equipments
      // });
      if (index > -1) {
        list[index].equipments.push({
          // areaId: item.areaId,
          // generalId: item.generalId,
          // equipmentId: item.equipmentId || item.id,
          // coverRate: item.averageCoverage || 0,
          // startTime: "",
          // endTime: "",
          // isActive: 1,
          "areaId": item.areaId,
          "coverRate": item.averageCoverage || 0,
          "equipmentId": item.equipmentId || item.id,
          "generalId": item.generalId,
          "name": item.equipmentName,
          "startTime": item.entryTime,
          "endTime": item.exitTime,
          "type": null,
        });
      } else {
        list.push({
          taskId: item.taskId,
          equipments: [
            {
              "areaId": item.areaId,
              "coverRate": item.averageCoverage || 0,
              "equipmentId": item.equipmentId || item.id,
              "generalId": item.generalId,
              "name": item.equipmentName,
              "startTime": item.entryTime,
              "endTime": item.exitTime,
              "type": null,
              // areaId: item.areaId,
              // generalId: item.generalId,
              // equipmentId: item.equipmentId || item.id,
              // coverRate: item.averageCoverage || 0,
              // startTime: "",
              // endTime: "",
              // isActive: 1,
            },
          ],
        });
      }
    }
  	const loading = ElLoading.service({
	  lock: true,
	  text: "Loading",
	  background: "rgba(0, 0, 0, 0.7)",
 	});
    dataApi.saveResource(list).then((res) => {
      if (res.data.code == 200) {
        // mapTool.deleteByGroup("EVMAPGROUP");
        // mapTool.deleteByGroup("DRAWLAYERGROUP");
        ElMessage.success("提交资源成功");
        // getTaskTargets(state.taskInfo);

        // getUsedResourceList(state.taskInfo);

        //sdw 保存之后跳转到资源调度计划页面
        window.currentRequirement = state.currentRequirement;
        router.push({
          path: '/home/<USER>',
          replace: false,
        });
      } else {
        ElMessage.error(res.data.message);
      }
    }).finally(() => {
		loading.close()
	})
  }
  function scheduleAlgo(algo, manual) {

    const startTime = new Date()
    const loading = ElLoading.service({
      lock: true,
      text: "Loading",
      background: "rgba(0, 0, 0, 0.7)",
    });
    dataApi
      .scheduleAlgo({
        areaId: state.currentArea,
        // taskId: state.taskInfo.id,
        // requirementId: state.currentRequirement,
        requirementId: state.taskInfo.requirementId,
        algo: algo,
        priorityList: state.sortValueList,
        taskIds: manualFlag ? [state.taskInfo.id] : state.taskIdList,
        equipments: (manual && manual.length > 0) ? manual : undefined
      })
      .then((res) => {
        loading.close();
        if (res.data.code == 200) {
          if (state.searchBoxShow) {
            // 自动
            carouselRef.value.setActiveItem(1);
            state.stepsActive = 1;
            // clearMap();
            // mapTool.deleteByGroup("EVMAPGROUP");
            mapTool.deleteByGroup("DRAWLAYERGROUP");
            state.chooseResource = [];
            // if (manual && manual.length > 0) {
            //   state.noSelected = []
            // }
            for (const key in res.data.data) {
              //  key是任务ID
              let task = res.data.data[key];
              let taskLength = task.filter(ta => { return ta.marked == 1 })?.length || 0
              let showIndex = 0
              let totalCover = 0
              //任务中只有一条有综合覆盖率，将其找出来并赋值给任务中所有数据用，避免显示有问题
              task.forEach(item => {
                // item.marked == 1 &&
                if (item.totalCover) {
                  totalCover = item.totalCover
                }
              })
              task.forEach((item, index) => {
                if (item.marked == 1) {
                  state.chooseResource.push({
                    ...item,
                    taskId: key,
                    taskLength: taskLength,
                    taskIndex: showIndex,
                    // startTime:item.equipments[0].task.startTime, //sdw
                    // endTime:item.equipments[0].task.endTime //sdw
                    startTime: item.entryTime, //sdw
                    endTime: item.exitTime,  //sdw
                    totalCover: totalCover
                  });
                  showIndex = showIndex + 1
                } else {
                  // if (manual && manual.length > 1) {
                  state.noSelected.push({
                    ...item,
                    taskId: key,
                    startTime: item.entryTime, //sdw
                    endTime: item.exitTime  //sdw
                  });
                  // }
                }
              });
            }
            state.ddTime = Math.ceil((new Date() - startTime) / 1000) || 1
          } else {

            state.ddTime = 0
            //手动 chooseResourceHand
            state.stepsActive = 1;
            // state.chooseShow = true;
            // mapTool.deleteByGroup("EVMAPGROUP");
            mapTool.deleteByGroup("DRAWLAYERGROUP");
            state.chooseResourceHand = [];
            for (const key in res.data.data) {
              //  key是任务ID
              let task = res.data.data[key];
              task.forEach((item, index) => {
                mapTool.drawMap({ ...item, group: "DRAWLAYERGROUP" })
                state.chooseResourceHand.push({
                  ...item,
                  taskId: key,
                  taskLength: task.length,
                  taskIndex: index,
                  startTime: item.entryTime, //sdw
                  endTime: item.exitTime  //sdw
                });
              });
            }
          }
        } else {
          ElMessage.error(res.data.message);
        }
      });
  }
  /**
   * @description 根据ID获取资源域名称
   */
  function areaName(id) {
    let area = state.areaList.find((item) => {
      return item.id == id;
    });
    return area.areaName || "";
  }
  function handleDetail(row) {
    state.resourceDetail = row;
    state.resourceDetailShow = true;
  }

  //在地图上显示或隐藏资源
  function showResource(resource, dataType, show) {
    mapTool.showEntityById(
      resource.id, // group: "DRAWLAYERGROUP",
      show
    );

    // 用于更新dom
    state.forceUpdate = Math.random() + "";


  }
  //视角定位到资源的位置并将其高亮（黄色）
  function flyToResource(resource, dataTypeValue) {
    state.resourceInfo = resource;
    state.resourceInfo.dataTypeValue = dataTypeValue;
    goBackColor();
    state.lastResource = resource;
    //此处需要将选中的资源的侦察范围变成黄色
    let entity = mapTool.getById(resource.id);
    if (!entity) {
      return;
    }
    let optionParams = entity.optionParams;
    optionParams.color = "yellow";
    optionParams.textColor = "yellow";
    optionParams.cyLineColor = "#afaf0888";
    mapTool.updateMap({
      ...optionParams,
    });
    window.EVGISMAP("flyTo", {
      center: [resource.longitude, resource.latitude, 7000000],
    });
  }
  //恢复颜色
  function goBackColor() {
    if (!state.lastResource?.id) {
      return;
    }
    // let has = hasResource(state.lastResource.id);
    // let color = has ? "#5D67E8" : "#59E870";
    let color = "#fff";
    let entity = mapTool.getById(state.lastResource.id);
    if (!entity) {
      return;
    }
    let optionParams = entity.optionParams;
    optionParams.color = color;
    optionParams.textColor = color;
    optionParams.cyLineColor = null;
    mapTool.updateMap({
      ...optionParams,
    });
    // mapTool.updateMap({
    //   id: state.lastResource.id,
    //   color: color,
    //   textColor: color,
    // });
    // window.EVGISMAP("updatePointByAttr", {
    //   id: state.lastResource.id + "_point",
    //   group: "DRAWLAYERGROUP",
    //   color: color,
    //   outlineColor: color,
    //   textColor: color,
    // });
  }
  //手动选择资源，将其加入到小框的数据列表里
  function resourceChoose(resource) {
    if (!state.taskInfo.id) {
      ElMessage.warning("未选择任务");
      return;
    }
    state.chooseShow = true;
    if (hasResource(resource.id)) {
      ElMessage.warning("该资源已经在列表中了");
      return;
    }
    const loading = ElLoading.service({
      lock: true,
      text: "Loading",
      background: "rgba(0, 0, 0, 0.7)",
    });
    dataApi
      .calculateCover({ equipmentId: resource.id, generalId: resource.generalId, taskId: state.taskInfo.id })
      .then((res) => {
        loading.close();
        if (res.data.code == 200) {
          let obj = { ...resource };
          obj.averageCoverage = parseInt(res.data.data * 10000) / 10000;
          state.chooseResourceHand.push({ ...obj, taskId: state.taskInfo.id });
          // mapTool.updateMap({
          //   id: resource.id,
          //   textColor: "#59E870",
          //   color: "#59E870",
          // });
          // window.EVGISMAP("updatePointByAttr", {
          //   id: resource.id + "_point",
          //   outlineWidth: "1",
          //   group: "DRAWLAYERGROUP",
          //   // outlineColor: "#5D67E8",
          //   // textColor: "#5D67E8",
          //   // color: "#5D67E8",
          //   outlineColor: "#59E870",
          //   textColor: "#59E870",
          //   color: "#59E870",
          // });
        } else {
          ElMessage.error(res.data.message);
        }
      });
  }
  //合并相同的任务
  function spanMethod({
    row,
    column,
    rowIndex,
    columnIndex,
  }) {
    if (columnIndex == 1 || columnIndex == 6) {
      // if (columnIndex == 2) { // sdw 新增了一列选择框
      if (row.taskIndex == 0) {
        // debugger
        return [row.taskLength, 1]
      } else {
        return [0, 0]
      }
    }

    //  if(row.taskId)
  }
  //将资源从小框中移除
  function resourceRemove(resource) {
    state.chooseResourceHand = state.chooseResourceHand.filter((item) => {
      return item.id != resource.id;
    });
  }
  function showEntity(id) {
    let entity = window.EVGISMAP("getGroupEntityById", {
      group: "DRAWLAYERGROUP",
      id: id + "_point",
    });
    // $forceUpdate();

    return !!entity?.show;
  }
  //判断是否在被选中的列表里
  function hasResource(id) {
    let re = state.chooseResourceHand.find((item) => {
      return item.id == (id.id || id) && state.taskInfo.id == item.taskId;
    });
    return !!re;
  }
  /**
   * @description 选择排序类型
   */
  function sortByClass(key) {
    let i = state.sortValueList.indexOf(key);
    if (i > -1) {
      state.sortValueList.splice(i, 1);
    } else {
      state.sortValueList.push(key);
    }
  }
  /**
   * @description 获取排序参数
   */
  function getSortClass() {
    state.sortList = window.dictTypeList["stationType"];
  }
  let isAuto = ref(false);
  let autoBaseList = ref([
    {
      name: "开始时间优先",
      callBack: () => {
        scheduleAlgo(1);
      },
    },
    {
      name: "连续测控优先",
      callBack: () => {
        scheduleAlgo(5);
      },
    },
  ]);
  let handleBaseList = ref([
    {
      name: "手动调度",
      callBack: () => {
        manualHandle();
      },
    },
  ]);
  let tripartiteList = ref([
    {
      name: "最早截至时间优先算法(EDF)",
    },
    {
      name: "先来先服务算法(FCFS)",
    },
  ]);

  // 自动推荐，弹出算法选择弹窗
  const autoSelect = () => {
    isAuto.value = true;
    state._algorihtmCopy = state.algorihtm
  };
  // 资源调度
  const saveAuto = () => {
    state.algorihtm?.name == "手动调度" ? (manualFlag = true) : (manualFlag = false)
    if (!state.algorihtm.name) {
      // ElMessage.error("请选择算法");
      // return;
      state.algorihtm = {
        name: "覆盖率排序算法",
        callBack: () => {
          scheduleAlgo(1);
        },
      };
    }
    // 暂时隐藏优先级
    // if (state.sortValueList.length == 0 && state.algorihtm.name == "优先级排序算法") {
    //   ElMessage.warning("请至少选择一种优先方式");
    //   return;
    // }
    isAuto.value = false;

    // manualFlag = false;
    state.algorihtm.callBack();

    state.nodeResult = []
    state.chooseResource = []
    state.returnInfoList = []
    state.nodeResultChoose = []
    state.noSelected = []
  };
  // 取消算法配置
  const cancelAuto = () => {
    isAuto.value = false;
    state.algorihtm = state._algorihtmCopy;
  };
  function drawCenterArea() {
    // 112.8918, 9.5501
    mapTool.drawMap({ id: "KuaYuGuanKongZhongXin", name: "跨域管控中心", dataType: 1, position: window.controlCenterPos, noShowRadar: true });
    state.areaList.forEach((area, index) => {
      // 135-73
      //52 -3
      let position = [104 + Math.random() * 10 * (index % 2 ? -1 : 1), 27.5 + Math.random() * 10 * (index % 2 ? -1 : 1)]
      mapTool.drawMap({
        id: area.id,
        name: area.areaName,
        dataType: 1,
        // position: [104 + Math.random() * 10 * (index % 2 ? -1 : 1), 27.5 + Math.random() * 10 * (index % 2 ? -1 : 1)],
        position: area.areaPosition ? area.areaPosition.split(",") : position,
        noShowRadar: true
      });
      setTimeout(() => {
        mapTool.drawflowLine( "KuaYuGuanKongZhongXin",area.id, "#00ff0a");
      }, 0);
    });
  }
  //下发一级调度结果
  function deliverResults() {
    // 调用接口，将state.chooseResource发去确认
    if (state.chooseResource.length == 0) {
      ElMessage.error("请至少调度一个资源");
      return;
    }

    // // sdw 判断是否有勾选的
    // if (state.firstLevelList.length == 0) {
    //   ElMessage.error("请至少调度一个资源");
    //   return;
    // }
    // // sdw 把未勾选的存起来
    // state.noSelected = state.chooseResource.filter(item => state.firstLevelList.findIndex(cur => cur.taskId + cur.areaName == item.taskId + item.areaName) == -1);

    state.stepsActive = 2;
    carouselRef.value.setActiveItem(2);
    // state.putInStorage = true;
    state.putInStorage = false;
    state.progressBar1 = 0;
    state.progressBar1Count = [0, 0, 0];
    // state.chooseShow = false;
    state.consultLoading = true;
    state.returnInfoList = [];
    drawCenterArea();
    state.foldHeight = "21%";
    state.returnInfoList = [];

    localStorage.setItem('zydd_equipments',JSON.stringify({
      equipments: [...state.chooseResource],
      type: 1,
      clientId: window.clientId
    }))

    state.consultLoading = false;
    //这里不调接口了，将这个数据发送到单独的用户勾选界面 20250410 wlj
    // dataApi.sendResult({ equipments: [...state.chooseResource], type: 1 }).then((res) => {
    //   state.consultLoading = false;
    //   if (res.data.code == 200) {
    //   } else {
    //     ElMessage.error(res.data.message);
    //     state.foldHeight = "80%";
    //   }
    // });
    //进度进入第二步
  }

  // 二次调度 --废弃
  function secondDeliverResults() {
    state.stepsActive = 4;
    carouselRef.value.setActiveItem(4);
    state.consultLoading = true;
    state.reAgainInfoList = [];
    //这里不调接口了，将这个数据发送到单独的用户勾选界面 20250410 wlj
    state.consultLoading = false;
    localStorage.setItem('zydd_equipments',JSON.stringify({
      areas: [...state.chooseResource],
      type: 2,
      clientId: window.clientId
    }))
    // dataApi.sendResult({ areas: state.chooseResource, type: 2 }).then((res) => {
    //   state.consultLoading = false;
    //   if (res.data.code == 200) {
    //   } else {
    //     ElMessage.error(res.data.message);
    //     state.foldHeight = "80%";
    //   }
    // });
  }

  //和各域进行协商（广播）
  function consultWithArea() {
    let loseAreas = [], successList = [];

    state.returnInfoList.forEach((item) => {
      // let row = state.chooseResource.find((ite) => {
      //   return ite.taskId == item.taskId;
      // });
      if (!item.result) {
        loseAreas.push(item);
      } else {
        successList.push(item)
      }
    });

    // let otherAreas = [];
    // let allFailedObj = {}
    // //sdw 把失败的任务的其余区域取出来
    // loseAreas.forEach(item => {
    //   let allFailed = true
    //   state.noSelected.forEach(cur => {
    //     if (cur.taskId == item.taskId) {
    //       otherAreas.push(cur)
    //       allFailed = false
    //     }
    //   })
    //   successAreas.forEach(suc => {
    //     if (suc.taskId == item.taskId) {
    //       allFailed = false
    //     }
    //   })
    //   allFailed && (allFailedObj[item.taskId] = item)
    // })
    // let allFailedList = []

    // for (const key in allFailedObj) {
    //   let item = allFailedObj[key]
    //   allFailedList.push(item)
    // }
    // console.log(otherAreas)
    ////---------------------
    // 获取所有任务ID
    //判断任务ID是否存在于失败列表
    //  存在--判断是否存在调度列表中和任务相关的数据未被选择的情况，作为备选数据
    //  ------存在 判断备选数据中包含某个失败数据的时间段并截取为同一时间段
    // let taskIds = state.taskList.map(item => { return item.id })
    // taskIds = Array.from(new Set(taskIds))
    let failedList = []
    state.taskIdList.forEach(taskId => {
      let currFailedList = []
      // 失败的数据
      let loseArr = loseAreas.filter(area => {
        return area.taskId == taskId
      })
      let alternative = state.noSelected.filter(noUse => {
        return noUse.taskId == taskId
      })
      // 截取出每个段的有效时间
      for (let lose of loseArr) {
        for (let noUse of alternative) {
          if (moment(noUse.entryTime).isBetween(lose.entryTime, lose.exitTime, undefined, "[]") || moment(noUse.exitTime).isBetween(lose.entryTime, lose.exitTime, undefined, "[]")) {
            let pushNoUse = JSON.parse(JSON.stringify(noUse))
            moment(noUse.entryTime).isBetween(lose.entryTime, lose.exitTime, undefined, "[]") ? (pushNoUse.entryTime = noUse.entryTime) : (pushNoUse.entryTime = lose.entryTime)
            moment(noUse.exitTime).isBetween(lose.entryTime, lose.exitTime, undefined, "[]") ? (pushNoUse.exitTime = noUse.exitTime) : (pushNoUse.exitTime = lose.exitTime)
            // pushNoUse.noUseTime1 = noUse.entryTime
            // pushNoUse.noUseTime2 = noUse.exitTime
            // pushNoUse.loseTime1 = lose.entryTime
            // pushNoUse.loseTime2 = lose.exitTime
            currFailedList.push(pushNoUse)
          }
        }
      }
      //对有效时间进行排序
      currFailedList.sort((a, b) => {
        return new Date(a.entryTime).getTime() - new Date(b.entryTime).getTime()
      })

      //对有效时间进行覆盖去重 ，即如果某一段有效时间在另一段时间内的话，则算作无效时间
      for (let index in currFailedList) {
        let failed = currFailedList[index]
        for (let indexCopy in currFailedList) {
          let failedCopy = currFailedList[indexCopy]
          if (index != indexCopy && failed.taskId == failedCopy.taskId
            && !failed.isRepet
            && moment(failedCopy.exitTime).isBetween(failed.entryTime, failed.exitTime, undefined, "[]")
            && moment(failedCopy.entryTime).isBetween(failed.entryTime, failed.exitTime, undefined, "[]")) {
            failedCopy.isRepet = true
          }
        }
      }
      //去掉无效时间
      currFailedList = currFailedList.filter(item => {
        return !item.isRepet
      })
      //若有效时间中有部分重复，即后一段时间在前一段时间中占有一部分，就将后一条数据的开始时间修改为前一段的结束时间
      for (let index in currFailedList) {
        let failed = currFailedList[index]
        let failedLast = currFailedList[index - 1]
        if (index != 0 && moment(failed.entryTime).isBetween(failedLast.entryTime, failedLast.exitTime, undefined, "[]")) {
          currFailedList[index].entryTime = failedLast.exitTime
        }
      }
      failedList = [...failedList, ...currFailedList]
      // // 1015前
      // if (loseArr.length) {
      //   // 备选数据
      //   const alternative = state.noSelected.filter(noUse => {
      //     return noUse.taskId == taskId
      //   })
      //   if (alternative.length) {
      //     // 备选数据中能够覆盖某一条失败的数据
      //     // const dataArr = alternative.filter((ele) => {
      //     //   const flag = loseArr.some(({entryTime, exitTime}) => {
      //     //     return moment(ele.entryTime).isSameOrBefore(entryTime) && moment(ele.exitTime).isSameOrAfter(exitTime)
      //     //   })
      //     //   return flag
      //     // })
      //     // failedList.push.apply(failedList, dataArr)
      //     failedList.push.apply(failedList, alternative)
      //   }
      //   //  else {
      //   //   let has = successList.find(suc => { return suc.taskId == taskId })
      //   //   !has && (obj.allFailed = true)
      //   // }
      //   // failedList.push(obj)
      // }
      // // 1015前


    })
    if (failedList.length == 0) {
      ElMessage.error("无可用资源，请重新调度")
      return
    }

    state.stepsActive = 3;
    carouselRef.value.setActiveItem(3);
    state.progressBar2 = 0;
    state.progressBar2Count = [0, 0, 0];
    state.foldHeight = "21%";
    state.nodeResult = [];
    state.checkNode = []
    //---------------------
    // dataApi.renegotiate({ areas: areas }).then((res) => { //sdw
    // dataApi.renegotiate({ areas: otherAreas }).then((res) => {
    dataApi.renegotiate({ areas: failedList }).then((res) => {
      // dataApi.renegotiate({ areas: state.noSelected }).then((res) => {
      if (!res.data.code == 200) {
        ElMessage.error(res.data.message);
        state.foldHeight = "80%";
      }
    });
  }
  //入库

  //数据入库，调度完成
  function saveConsult(list) {
    let saveList = list.filter((item) => {
      return item.result == true;
    });
    if (saveList.length == 0) {
      ElMessage.error("没有可调度的资源");
      return;
    }
    saveResource(saveList);
    // state.stepsActive = 4;
    // carouselRef.value.setActiveItem(4);
  }
  //重新调度
  function redoSteps() {
    state.stepsActive = 0;
    carouselRef.value.setActiveItem(0);
  }
  //返回上一步
  function returnBtn() {
    if (isJump.value) {
      state.stepsActive = 2;
      carouselRef.value.setActiveItem(state.stepsActive);
      isJump.value = false;
      return
    }
    state.stepsActive = state.stepsActive - 1;
    carouselRef.value.setActiveItem(state.stepsActive);
    if (state.stepsActive == 3) {
      setTimeout(() => {
        state.checkNode.forEach(row => {
          nodeResultPassRef.value.toggleRowSelection(row, true)
        })
      }, 100);
    }
  }
  //下一步
  function nextStep() {
    let setp = state.stepsActive
    // 各域反馈
    if (state.stepsActive == 2) {
      if (state.nodeResult.length == 0 && state.nodeResultChoose.length) {
        setp += 1
        isJump.value = true
      }
    }
    state.stepsActive = setp + 1;
    carouselRef.value.setActiveItem(state.stepsActive);
    if (state.stepsActive == 3) {
      setTimeout(() => {
        state.checkNode.forEach(row => {
          nodeResultPassRef.value.toggleRowSelection(row, true)
        })
      }, 100);
    }
  }
  const firstMsg = ref(true)
  let timer
  function setReturnInfoTableList() {
	  if(timer) return
	  timer = setTimeout(() => {
		  timer = null
		  returnInfoTableList.value = [...state.returnInfoList]
	  }, 400)
  }
  // 获取webscoket的数据
  function getSocketData(evt) {
    let data = evt.detail.data;

    if (data.type !== 3) {
      return;
    }
    //设置进度条
    if (state.stepsActive == 2) {
	  state.progressBar1 = data.data.progress;
      state.progressBar1Count[0] = state.progressBar1Count[0] + 1;
      if (data.data.result.result) {
        state.progressBar1Count[1] = state.progressBar1Count[1] + 1;
      } else {
        state.progressBar1Count[2] = state.progressBar1Count[2] + 1;
      }
      // if (state.progressBar1 == 100) {
      //   setTimeout(() => {
      //     // state.foldHeight = "80%";
      //     // mapTool.removeAllFlowLine();
      //   }, 2000);
      // }
    } else if (state.stepsActive == 3) {
      state.progressBar2 = data.data.progress;
      state.progressBar2Count[0] = state.progressBar2Count[0] + 1;
      if (data.data.result.result) {
        state.progressBar2Count[1] = state.progressBar2Count[1] + 1;
      } else {
        state.progressBar2Count[2] = state.progressBar2Count[2] + 1;
      }
      if (state.progressBar2 == 100) {
        setTimeout(() => {
          state.foldHeight = "80%";
          // mapTool.removeAllFlowLine();
        }, 2000);
      }
    }
    state.progressText = data.data.message;

    // if (data.data.message.includes("调度失败")) {
    //   ElMessage.warning(data.data.message);
    // } else {
    //   ElMessage.success(data.data.message);
    // }
    // mapTool.removeFlowLine("KuaYuGuanKongZhongXin");
    state.areaList.forEach((area) => {
      if (data.data.result.areaId == area.id) {
        mapTool.drawflowLine(data.data.result.areaId, "KuaYuGuanKongZhongXin", "#00ff0a");
      } else {
        mapTool.drawflowLine(area.id,  "KuaYuGuanKongZhongXin","#00ff0a");

      }
    });

    if (!data.data.result || data.data.result.result === undefined) {
      return;
    }
    //存入数据
    if (state.stepsActive == 2) {
      if(firstMsg.value){
	  	firstMsg.value = false
		setTimeout(() => {
		  // 首次收到websocket数据时，先移除之前绘制的线
		  mapTool.removeAllFlowLine()
		  // // 然后重新绘制反向的线（交换两个ID的顺序）
		  // window.flowLineList(v=>{
			// mapTool.drawflowLine( v.id,"KuaYuGuanKongZhongXin", "#00ff0a");
		  // })
		}, 100);
      }
	  state.returnInfoList.push({
		  ...data.data.result,
		  equipments: [{
			  generalId: data.data.result.generalId,
			  areaId: data.data.result.areaId,
			  equipmentId: data.data.result.equipmentId,
			  generalName: data.data.result.generalName,
			  equipmentName: data.data.result.equipmentName,
			  coverRate: data.data.result.coverRate,
			  startTime: timeSpan(data.data.result.entryTime),
			  endTime: timeSpan(data.data.result.exitTime)
		  }]
	  });
	  setReturnInfoTableList()
      if (data.data.result.result) {
        state.putInStorage = true;
      }
    } else if (state.stepsActive == 3) {
      state.nodeResult.push({
        ...data.data.result,
        equipments: data.data.result.result ? [{
          generalId: data.data.result.generalId,
          areaId: data.data.result.areaId,
          equipmentId: data.data.result.equipmentId,
          generalName: data.data.result.generalName,
          equipmentName: data.data.result.equipmentName,
          coverRate: data.data.result.coverRate,
          startTime: timeSpan(data.data.result.entryTime),
          endTime: timeSpan(data.data.result.exitTime)
        }] : []
      });
    }

    // else if (state.stepsActive == 4) {
    //   state.reAgainInfoList.push({
    //     ...data.data.result,
    //   });
    // }
  }
  //返回按钮
  function goBack() {
    state.searchBoxShow = true;
    state.chooseShow = false;
    state.resourceBoxShow = false;
    state.taskIdList = []
    // manualFlag = false;
    // mapTool.deleteByGroup("EVMAPGROUP");
    clearTarget()
    mapTool.deleteByGroup("DRAWLAYERGROUP");
    state.areaGeneralTree = {};
    // setTimeout(() => {
    //   taskTableRef.value.setCurrentRow(state.taskInfo);
    //   taskTableRef.value.toggleRowSelection(state.taskInfo, true);
    // }, 500);
  }
  /*****************************end**********/
</script>

<style lang="less" scoped>
  .title-font {
    font-size: 18px;
    font-weight: bolder;
    color: #c6cdd6;
  }

  .res-time {
    position: absolute;
    right: 10px;
    bottom: 10px;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }

  .gltx-box {
    background-color: #57b4db;
    width: 120px;
    margin: 0 10px 10px 0px;
    text-align: center;
  }

  .detail {
    .el-form-item {
      margin-bottom: 5px;
    }
  }

  .demandDetail {
    height: 40%;
    padding: 10px;
    border: 1px solid #2c659fc0;
    box-sizing: border-box;
    background: #031527a6;
    border-radius: 4px;
  }

  // .demandDetail :deep(.el-form-item__label) {
  //   color: #285472;
  //   font-weight: bolder;
  //   font-size: 15px;
  // }

  // .demandDetail :deep(.el-form-item__content) {
  //   color: #285472;
  // }

  .demandDetail :deep(.el-form) {
    height: 100%;

    .el-form-item {
      margin-bottom: 8px;
    }
  }

  .autoSubTitle {
    font-size: 18px;
    font-weight: bold;
    color: #36b5ff;
    width: 400px;
    margin: 0 auto;
    padding-bottom: 10px;
  }

  .auto-Box {
    width: 400px;
    display: flex;
    flex-wrap: wrap;
    margin: 0 auto;
  }

  .autoBase-box {
    margin-left: 15px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  .auto-box {
    border: 1px solid #acecff;
    box-sizing: border-box;
    width: 160px;
    height: 40px;
    line-height: 40px;
    margin: 0 20px 10px 0px;
    text-align: center;
  }

  .autoTri-box {
    width: 250px;
  }

  .img-box {
    display: inline-block;
    width: 48px;
    height: 48px;
    border: 3px solid gainsboro;
    background-color: gainsboro;
    margin-top: 10px;
    cursor: pointer;
  }

  .img-box-zhanyong {
    display: inline-block;
    width: 48px;
    height: 48px;
    border: 3px solid rgb(160, 159, 159);
    background-color: rgb(160, 159, 159);
    margin-top: 10px;
  }

  .img-box-hover {
    display: inline-block;
    width: 48px;
    height: 48px;
    border: 3px solid #17afff;
    background-color: gainsboro;
    margin-top: 10px;
  }

  .img-zhanyong-hover {
    display: inline-block;
    width: 48px;
    height: 48px;
    border: 3px solid #17afff;
    background-color: rgb(160, 159, 159);
    margin-top: 10px;
  }

  .el-select--large .el-select__wrapper {
    min-height: 30px;
  }

  .taskTable {
    height: calc(33% - 70px);
  }

  .custom-table {
    height: 100% !important;
  }

  .nameIcon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-right: 15px;
    position: relative;

    &:hover {
      .resourceOperation {
        display: flex;
        animation: mymove 0.05s linear;
      }
    }

    .itemIcon {
      width: 45px !important;
      height: 45px !important;
    }
  }

  .chooseItem {
    .dj_icon {
      border: 2px solid #197adb !important;
    }

    .tj_icon {
      border: 2px solid #197adb !important;
    }

    .kj_icon {
      border: 2px solid #197adb !important;
    }
  }

  .chooseAlgorihtm {
    border: 1px solid #197adb !important;
  }

  .custom_header_wz {
    background-image: url("/images/icon/白_39.png");
    background-size: cover;
  }

  .resourceOperation {
    position: absolute;
    top: 1px;
    left: 4px;
    width: 43px;
    height: 43px;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: space-around;
    align-items: center;
    // flex-direction: column;
  }

  @keyframes mymove {
    0% {
      opacity: 0;
    }

    50% {
      opacity: 0.5;
    }

    100% {
      opacity: 1;
    }
  }

  .itemPage {
    position: relative;
    padding: 0 20px 0 20px;
  }

  .leftBtn {
    background: transparent !important;
    position: absolute;
    top: 15%;
    opacity: 0.5;
    left: -15px;

    &:hover {
      opacity: 1;
    }
  }

  .rightBtn {
    background: transparent !important;
    position: absolute;
    top: 15%;
    opacity: 0.5;
    right: -7px;

    &:hover {
      opacity: 1;
    }
  }

  .custom_header_ck {
    background-image: url("/images/icon/xq.png");
    background-size: cover;
  }

  .steps_box {
    width: 100%;
    text-align: center;
    margin: 30px 0;
  }

  :deep(.el-carousel__item) {
    box-shadow: inset 0 0 10px 0px #031836;
    padding: 10px;
    box-sizing: border-box;
  }

  .countClass {
    // border: 1px solid #197adb;
    line-height: calc(4vh - 2px);
    padding: 0 8px;
    color: #03a2a7;

    .countTitle {
      font-size: 14px;
      color: inherit;
    }
  }

  // .countClass {
  //   border: 1px solid #197adb;
  //   line-height: calc(4vh - 2px);
  //   padding: 0 8px;
  //   color: #03a2a7;

  //   .countTitle {
  //     font-size: 16px;
  //     color: inherit;
  //     // color: #03a2a7;
  //   }
  // }

  .progressDiv {
    width: 5px;
    height: 15px;
    position: absolute;
    background: #03a2a7;
    top: -15px;
    left: 0;
    border: 1px solid #3de6d7;
    border-radius: 5px;
  }

  .infoIcon {
    display: inline-block;
    height: 25px;
    width: 25px;
    background: url(/images/icon/xx.png) 0 0 /100% 100% no-repeat;
  }

  .infoText {
    display: inline-block;
    height: 25px;
    width: calc(100% - 80px);
    line-height: 25px;

  }

  .infoOpenText {
    display: inline-block;
    height: 25px;
    width: 40px;
    line-height: 25px;
    cursor: pointer;
  }

  .infoOpenText:hover {
    color: #03a2a7;
  }

  ::v-deep .noexpands .el-table__expand-column .cell {
    display: none;
  }

  .el-row.is-justify-between {
    justify-content: space-between;
  }
  .showOrHidden-box {
	  position: absolute;
	  top: 90px;
	  // z-index: 999;
  }
  .show_hide_icon{
	  cursor:pointer;
	  &:hover{
		  :deep(path){
			  color: #0c87e2;
		  }
	  }
  }
</style>
