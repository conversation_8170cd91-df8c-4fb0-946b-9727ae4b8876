<!--
 * @@Description: 资源调度计划
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-06-26 16:03:38
 * @FilePath: \qbmbtsweb\src\views\home\HomePage.vue
-->
<template>
  <customPopup left="10px" top="80px" width="99%" height="90%">
    <template #header>
      <span class="resource_icon"></span>
      <span class="title-font">资源调度结果</span>
    </template>
    <template #content>
      <div style="height: 100%; width: 98%;margin: 0 1%;">
        <div class="container">
          <el-form label-width="120px" class="requirement_form">
            <el-row>
              <el-col :span="6">
                <el-form-item label="需求名称：">
                  <!-- <el-select-v2 v-model="state.search.requirementId" filterable clearable
                    :options="state.requirementList" placeholder="输入需求名称关键词" /> -->
                  <el-select v-model="state.search.requirementId" placeholder="输入需求名称关键词">
                    <el-option v-for="item in state.requirementList" :key="item.value" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="开始时间：">
                  <el-date-picker v-model="state.search.startTime" type="date" placeholder="开始日期"
                    style="width: 90%; margin-right: 20px" value-format="YYYY-MM-DD" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="结束时间：">
                  <el-date-picker v-model="state.search.endTime" type="date" placeholder="开始日期"
                    style="width: 90%; margin-right: 20px" value-format="YYYY-MM-DD" />
                </el-form-item>
              </el-col>
              <el-col :span="6" style="text-align: center;">
                <el-button @click="searchFun" icon="Search" style="float: right;">搜索</el-button>

              </el-col>
            </el-row>
          </el-form>
        </div>
        <div style="width: 100%; height: calc(40% - 5px); display: flex">
          <div class="panoramaleftBox leftBox">
            <div class="Title userColor">任务列表</div>
            <div class="content" style="height: calc(100% - 40px)">
              <customTable :tableData="state.taskList" ref="taskRef" :tableColumn="taskListColumn"
                :total="taskPage.total" :currentPage="taskPage.page" :pageSize="taskPage.size" selectionShow="true"
                @handleSelectionChangeEmit="handleSelectionChangeEmit" @nodeClickEmit="getResourceList"
                @currentPageEmit="(val) => {
                  taskPage.page = val;
                  getTaskList();
                }
                  " @pageSizeEmit="(val) => {
                    taskPage.size = val;
                    getTaskList();
                  }
                    ">
              </customTable>
            </div>
          </div>
          <div class="rightBox">
            <div class="Title userColor">任务关联资源</div>
            <div class="content" style="height: calc(100% - 40px)">
              <div style="height: 100%">
                <customTable :tableData="state.resourceList" :tableColumn="resourceColumn" paginationShow="false">
                </customTable>
              </div>
            </div>
          </div>
        </div>
        <div style="width: 100%; height: calc(70% - 140px);margin-top: 20px;">
          <div class="taskTimeLine">
            <div id="my-timeline" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </div>
    </template>
  </customPopup>
</template>

<script setup>
import customTable from "@/components/customTable.vue";
import customPopup from "@/components/customPopup.vue";
import { reactive, onMounted, onUnmounted, ref, nextTick, watch } from "vue";
import customWindow from "../../components/customWindow.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import * as dataApi from "@/service/API/system/resourceDispatch.js";
import * as homeApi from "@/service/API/home/<USER>";
import * as areaApi from "@/service/API/system/areaManage.js";


let dictValue = window.dictValue;
var visGroups; //时间轴分组
var visItems; //时间轴项目
var timeline; //时间轴管理器
const state = reactive({
  requirementList: [], //需求列表
  taskList: [], //任务列表
  search: {
    requirementId: null,
    startTime: null,
    endTime: null
  },
  areaList: []//域列表
});
let taskRef = ref(null);
//任务列表分页数据
const taskPage = reactive({
  page: 1,
  size: 10,
  total: 0,
});
const taskListColumn = [
  { label: "任务名称", prop: "taskName", type: "String", width: "" },
  { label: "任务状态", prop: "status", type: "Sysdic", dicName: "taskStatus", width: "" },
  { label: "开始时间", prop: "startTime", type: "Time", width: "" },
  { label: "结束时间", prop: "endTime", type: "Time", width: "" },
];
const resourceColumn = [
  { label: "资源名称", prop: "name", type: "String", width: "" },
  { label: "经度", prop: "longitude", type: "String", width: "" },
  { label: "纬度", prop: "latitude", type: "String", width: "" },
  { label: "类型", prop: "type", type: "String", width: "" },
  { label: "开始时间", prop: "startTime", type: "Time", width: "" },
  { label: "结束时间", prop: "endTime", type: "Time", width: "" },
]
var curSelectList = []
onMounted(() => {
  setTimeout(() => {
    initTimeLine()
    //sdw 从资源调度保存后跳转过来回显示
    if (window.currentRequirement) {
      state.search.requirementId = window.currentRequirement[0];
      setTimeout(() => {
        searchFun()
      }, 10)
    }
  }, 0);
  getRequirementList();
  getTaskList();
  getAreaList()
});
//获取域列表
function getAreaList() {
  areaApi.getAreaList({ page: 1, size: 999, }).then((res) => {
    if (res.data.code == 200) {
      state.areaList = res.data.data.records;
    }
  });
}
/**
 * @description 分页获取需求列表
 */
function getRequirementList() {
  homeApi
    .getRequirementList({
      pageNum: 1,
      pageSize: 999,
    })
    .then((res) => {
      if (res.data.code == 200) {
        state.requirementList = res.data.data.records;
        state.requirementList.forEach(item => {
          item.value = item.id;
          item.label = item.requirementName;
        })
      } else {
        ElMessage.error(res.data.message);
      }
    });
}
function searchFun() {
  taskPage.page = 1
  getTaskList()
}
/**
 * @description 任务列表
 */
function getTaskList() {
  homeApi
    .getTaskList({
      requirementId: state.search.requirementId,
      page: taskPage.page,
      size: taskPage.size,
      // requirementIds: [state.requirementId],
      requirementIds: state.search.requirementId ? [state.search.requirementId] : [],
      startTime: state.search.startTime ? state.search.startTime + " 00:00:00" : undefined,
      endTime: state.search.endTime ? state.search.endTime + " 23:59:59" : undefined,
    })
    .then((res) => {
      if (res.data.code == 200) {
        state.taskList = res.data.data.records;
        taskPage.total = res.data.data.total;
        if (state.taskList.length > 0) {
          setTimeout(() => {
            taskRef.value.setCurrentRow(state.taskList[0]);
          }, 1000);
          getResourceList(state.taskList[0])
        } else {
          state.resourceList = []
        }


      } else {
        ElMessage.error(res.data.message);
      }
    });
}
//获取资源列表
function getResourceList(row) {
  // visGroups.clear()
  // visItems.clear()
  // const start = window.timeSpan(new Date(row.startTime).getTime() - 86400 / 2)
  // const end = window.timeSpan(new Date(row.endTime).getTime() + 86400 / 2)
  // timeline.setOptions({
  //   min: start,
  //   start,
  //   max: end,
  //   end
  // })

  dataApi.getEquipmentByTask({ taskId: row.id, page: 1, size: 999 }).then(res => {
    if (res.data.code == 200) {
      state.resourceList = res.data.data.records
      state.resourceList.forEach(item => {
        delete item.equipmentDetail.id
        let areaName = state.areaList.find(area => { return area.id == item.areaId })?.areaName

        for (const key in item.equipmentDetail) {
          item[key] = item.equipmentDetail[key]
        }
        // if(!visGroups.get(item.areaId) ){
        //   visGroups.add({ id: item.areaId, content: item.areaName, value: item.areaId })
        // }
        if (visItems.getIds(item.id).length == 0) {
          visItems.add({
            id: item.id,
            group: row.id,
            content: item.equipmentDetail.name,
            start: item.startTime,
            end: item.endTime,
            taskId: row.id
          })
        }
        // console.log("visItems", visItems);
        // console.log("visItems", visItems.getIds(item.id));
      })
      timeline.setItems(visItems);
      console.log("visGroups", visGroups);
      // timeline.setGroups(visGroups);
    }
  })
}
let pieChart = ref(null);

/**
 * @description 绘制时间轴
 */
function initTimeLine() {
  var container = document.getElementById("my-timeline");

  timeline = new vis.Timeline(container);
  visGroups = new vis.DataSet([]);
  visItems = new vis.DataSet([]);
  // let start = moment(timelineStart).format("YYYY-MM-DD HH:mm:ss");
  // let min = moment(timelineStart).subtract(1, "h").format("YYYY-MM-DD HH:mm:ss")
  // let end = moment(timelineEnd).format("YYYY-MM-DD HH:mm:ss");
  let options = {
    autoResize: true,
    height: "100%", //高度
    width: "100%", //宽度
    min: "2000-01-01 00:00:00", //设置最小时间范围
    max: "2099-12-31 23:59:59", //设置最大时间范围
    // start: "2024-08-06 15:03:18", //设置开始时间
    // end: "2024-08-07 15:03:18", //设置结束时间
    stack: false, // ture则不重叠
    limitSize: true,
    verticalScroll: true,
    // cluster: true, //数据量过大时使用。
    locale: "zh-cn",
    xss: {
      disabled: true,
    },
    groupOrder: function (a, b) {
      return a.value - b.value;
    },
    editable: false,
    showCurrentTime: false,
    moment: function (date) {
      // return moment(date).format("YYYY-MM-DD HH:mm:ss");
      return moment(date);
    },
    locale: moment.locale("zh-cn")

  };
  timeline.setOptions(options);
  timeline.setGroups(visGroups);
  timeline.setItems(visItems);
}
function handleSelectionChangeEmit(nodeList) {
  console.log("nodeList", nodeList);
  visGroups.clear()
  // visItems.clear()
  let addList = []
  let delList = []
  // 找到新勾选的数据
  let maxDate
  let minDate
  nodeList.forEach(item => {
    const start = window.timeSpan(new Date(item.startTime).getTime() - 86400 / 2)
    const end = window.timeSpan(new Date(item.endTime).getTime() + 86400 / 2)
    if (!minDate || start > minDate) {
      minDate = start
    }
    if (!maxDate || maxDate < end) {
      maxDate = end
    }
    let find = curSelectList.find(v => v.id == item.id)
    if (!find) {
      addList.push(item)
    }

    visGroups.add({
      id: item.id,
      content: item.taskName,
      value: item.id
    })
  })
  //找到取消勾选的数据
  curSelectList.forEach(item => {
    let find = nodeList.find(v => v.id == item.id)
    if (!find) {
      delList.push(item.id)
    }
  })

  let newList = []
  // 将taskId不在删除列表里的继续保存
  let a = visItems.get()
  // visItems.forEach(item => {
  //   if (!delList.includes(item.taskId)) {
  //     newList.push(item)
  //   }
  // })
  a.forEach(item => {
    if (!delList.includes(item.taskId)) {
      newList.push(item)
    }
  })
  console.log("newList", newList);
  // 添加回去
  newList.forEach(item => {
    visItems.add(item)
  })
  addList.forEach(item => {
    getResourceList(item)
  })
  curSelectList = nodeList
  // console.log("list");
  // nodeList

}
//end*******************************************
/**
 * @description 表格点击事件
 */


</script>

<style scoped lang="less">
.container {
  width: 100%;
  height: 50px;
  border: 1px solid #003a66;
  border-radius: 4px;
  padding: 8px;
  box-sizing: border-box;
}

.leftBox,
.rightBox {
  height: 100%;
  width: 49.5%;
  margin-left: 15px;
  margin-top: 10px;
  border: 1px solid #003a66;
  border-radius: 4px;
  padding: 8px;
  box-sizing: border-box;

  .Title {
    height: 30px;
    width: 100%;
    font-size: 18px;
    font-weight: bolder;
  }
}

.leftBox {
  margin-left: 0;

  .requir {
    .el-form-item {
      margin-bottom: 5px;
    }

    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      cursor: default;

      .el-input__inner {
        cursor: default !important;
      }
    }
  }
}

.requirement_form {
  :deep(.el-form-item) {
    margin-bottom: 0px !important;
  }
}

.taskTimeLine {
  height: 100%;
  width: 100%;
  border: 1px solid #003a66;
  border-radius: 4px;
  padding: 8px;
  box-sizing: border-box;

  :deep(.vis-group) {
    >.vis-item.vis-selected {
      background: #2f557b !important;
    }

    .vis-item {
      background: rgb(29, 94, 142);
    }
  }

  // >div:first-of-type {
  //   width: 100px;
  //   height: 40px;
  //   display: flex;
  //   flex-wrap: wrap;
  //   position: relative;
  //   left: -10px;

  //   >span {
  //     width: 100%;
  //     text-align: center;
  //   }
  // }

  // >div:nth-of-type(2) {
  //   width: calc(100% - 90px);
  //   height: 40px;
  // }
}
</style>
