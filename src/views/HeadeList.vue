<template>
  <div class="header_list_top">
    <!-- logo -->
    <div class="img-logo" @click="earthPage">
      <!-- <img style="width: 600px" src="../../public/images/bg/logo.png" /> -->
      <div>跨域管控中心</div>
    </div>

    <!-- 菜单栏 -->
    <div class="menuList">
      <div v-show="subTitle" class="title">
		  <img src="/images/icon/title_arrow.png" alt="">
		  <span :data-content="subTitle">{{ subTitle }}</span>
		  <img src="/images/icon/title_arrow.png" alt="" style="transform: rotate(180deg);">
	  </div>

      <el-menu
        :default-active="activeIndex"
        mode="horizontal"
        @select="routerTo"
        text-color="#f7feff"
        class="menu"
        ref="elMenuRef"
      >
        <template v-for="item of defaultMenuList" :key="item.name">
          <el-sub-menu :index="item.path" v-if="item.children" :item="item">
            <!-- 相当于一个item的功能 -->
            <template #title>
              <!-- <span class="img" :class="item.imgUrlName"></span> -->
              <img :src="item.imgUrlName" alt="" />
              <span style="font-size: 16px">{{ item.title }}</span>
            </template>
            <!-- 循环 -->
            <el-menu-item
              v-for="it in item.children"
              :key="it.name"
              :index="it.path"
              @click="menuClick(it)"
              style="font-size: 16px"
            >
              <!-- 判断是否有children  -->
              <!-- 如果有children 就自身条用自身 :it 是父子传值 如果不懂可以看vue.js 父子组件传值 -->
              <sub-menu
                v-if="it.children"
                :index="it.path"
                :it="it"
                :popper-append-to-body="false"
                popper-class="custom-submenu"
              ></sub-menu>
              <!-- 如果没有 childern   menu-item是自定义的组件-->
              <menu-item v-else :key="it.path" :it="it" :index="it.path">
                <span style="font-size: 16px">{{ it.title }}</span>
              </menu-item>
            </el-menu-item>
          </el-sub-menu>
          <el-menu-item :index="item.path" style="font-size: 16px" v-else="">
            <template #title>
              <!-- <span class="img" :class="item.imgUrlName"></span> -->
              <img :src="item.imgUrlName" alt="" />
              <span style="font-size: 16px">{{ item.title }}</span>
            </template>
          </el-menu-item>
        </template>
      </el-menu>
    </div>
    <!-- 时间 -->
    <changeBox />
    <el-dialog
      v-model="moreFLag"
      title="需求信息详情"
      width="30%"
      center
      @close="moreFLag = false"
    >
      <div class="requir">
        <el-form :model="moreRow" label-width="120px" class="requirement_form">
          <el-row>
            <el-col :span="12">
              <el-form-item label="需求名称：">
                <span>{{ moreRow.requirementName }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="需求来源：">
                <span>{{ moreRow.dataSource }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="来源IP：">
                <span>{{ moreRow.host }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="来源端口：">
                <span>{{ moreRow.port }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="来源时间：">
                <span>{{ moreRow.requestTime }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="需求描述：">
                <span>{{ moreRow.requirementComment }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-dialog>
    <div class="user-info">
      <div class="userName">
        <el-badge
          :value="emailCount"
          style="height: 32px; margin-right: 20px"
          :hidden="state.badgeHidden"
        >
          <img src="/images/header/Email.png" alt="" @click="newBadgeMsg" />
        </el-badge>
        <img src="/images/header/用户.png" alt="" />
        {{ currentUser }}
        <!-- <el-dropdown>
          <span class="el-dropdown-link">
            {{ currentUser }}
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="userConfig">用户中心</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown> -->
      </div>

      <div class="userExit" @click="exitLogin">
        <img src="/images/header/退出.png" alt="" />
      </div>
      <!-- <div class="time">{{ currentTime }}</div> -->
    </div>
    <div
      style="
        width: 70vw;
        height: 80vh;
        position: fixed;
        top: 10%;
        left: 15%;
        z-index: 1989;
      "
      v-show="state.showEmail"
    >
      <customPopup width="100%" height="100%" left="0" top="0">
        <template #header>
          <span class="EMail_icon"></span>
          <span class="title-font">需求接收消息</span>
          <div style="position: absolute; right: 2%">
            <el-button plain @click="state.showEmail = false">关闭</el-button>
          </div>
        </template>
        <template #content>
          <div style="height: 90%; width: 100%">
            <el-table
              :data="state.EmailList"
              ref="emalTableRef"
              class="custom-table"
              style="height: 100%"
            >
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              />
              <el-table-column
                label="需求名称"
                align="center"
                prop="requirementName"
              ></el-table-column>
              <el-table-column
                label="需求来源"
                align="center"
                prop="dataSource"
              ></el-table-column>
              <el-table-column
                label="来源IP"
                align="center"
                prop="host"
              ></el-table-column>
              <el-table-column
                label="来源端口"
                align="center"
                prop="port"
              ></el-table-column>
              <el-table-column
                label="来源时间"
                align="center"
                prop="requestTime"
              ></el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope">
                  <a style="cursor: pointer" @click="delBusinessRow(scope.row)"
                    >更多</a
                  >
                </template>
              </el-table-column>

              <!-- <el-table-column label="需求来源" align="center" prop="receiveTime">
                <template #default="{ row }">
                  {{ timeSpan(row.receiveTime) }}
                </template>
    </el-table-column> -->
            </el-table>
            <div style="margin: 5px; text-align: right">
              <el-button @click="requirementHandle()">接收</el-button>
              <el-button @click="requirementDel()">忽略</el-button>
            </div>
          </div>
        </template>
      </customPopup>
    </div>
  </div>
  <systemConfig
    v-if="state.showSystemSet"
    @close="state.showSystemSet = false"
  ></systemConfig>
  <!-- 消息提示 -->
  <div v-for="(item, i) in notufyInfoList" :key="i">
    <customPopup
      :right="'0.5%'"
      :top="60 + i * 200 + i * 10 + 'px'"
      width="16%"
      height="200px"
      :zIndex="1000"
    >
      <template #header>
        <span class="list_icon"></span>
        <span class="title-font">消息提示</span>
      </template>
      <template #content>
        <div style="height: auto">
          <!-- <div style="font-size: 20px;text-align: center;font-weight: bold;">是否需要切换节点？</div>
          <div style="font-size: 20px;text-align: center;font-weight: bold;">
            <span style="color: red;font-weight: bold;font-size: 20px;">{{ userStore.notufyInfo.data.timeLeft }}</span>后自动切换</div> -->
          <div class="info-content-box">
            <span
              ><span style="font-size: 19px">{{ item.target }}</span
              >需要{{ accHandEnObj[item._output.accHandEn]
              }}{{ item.station }}</span
            >
            <span>是否执行？</span>
          </div>
          <div style="margin-top: 10px; text-align: center; font-weight: bold">
            <!-- 目标节点:{{userStore.notufyInfo.nodeID}} -->
            <el-button @click="qhHandle(item, true)"
              >确定(<span
                style="color: red; font-weight: bold; font-size: 20px"
                >{{ item.timeLeft }}</span
              >s)</el-button
            >
            <el-button @click="qhHandle(item, false)">取消</el-button>
          </div>
        </div>
      </template>
    </customPopup>
  </div>
</template>
<script setup>
// vue带过来的
import { onMounted, reactive, ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import changeBox from '../assets/style/theme/index.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as homeApi from '@/service/API/home/<USER>'
import customPopup from '@/components/customPopup.vue'
import systemConfig from './system/systemConfig/systemConfig.vue'
// import {styles} from '@/assets/style/theme/style.less'
// debugger
// const color = styles['current_time']
import { routerStore } from '@/stores/routerStore'
import { useUserStore } from '@/stores/index'
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const timeSpan = window.timeSpan
const routerstore = routerStore() // 路由的状态管理器
let activeIndex = ref(location.hash.slice(1))
// activeIndex.value = location.hash.slice(1);
// 自建变量
let oneRouter = ref('/home/<USER>')
let currentUser = ref() //当前用户
let currentTime = ref() //当前时间
let emalTableRef = ref()
let emailCount = ref(0)
let elMenuRef = ref(null)
const state = reactive({
  showEmail: false, //邮件弹窗
  EmailList: [], //邮件列表
  badgeHidden: true, //展示消息
  showSystemSet: false,
})
const subTitle = ref('')

const notufyInfoList = computed(() => {
  return Object.values(userStore.notufyInfo).filter((item) => {
    item._output = JSON.parse(item.output || '{}')
    return item.state === 'pending' && item.timeLeft > 0
  })
})
const qhHandle = async (item, flag) => {
  if (flag) {
    homeApi.terminalConfirm({ terminalId: item.sourceNode })
  } else {
    homeApi.terminalCancel({ terminalId: item.sourceNode })
  }
}
const accHandEnObj = {
  1: '系统级抗干扰',
  2: '切换',
  3: '接入',
}
// 菜单
let defaultMenuList = [
  // {
  //   title: "首页",
  //   name: "home",
  //   path: "/home",
  //   imgUrlName: "/public/images/header/首页.png",
  // },
  {
    title: '需求分解',
    name: 'requirementManage',
    path: '/home/<USER>',
    imgUrlName: '/images/header/需求.png',
    children: [
      {
        title: '需求管理',
        name: 'xqgl',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
      {
        title: '任务管理',
        name: 'rwgl',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
    ],
  },
  {
    title: '资源调度',
    name: 'zydd',
    path: '/home/<USER>',
    imgUrlName: '/images/header/资源.png',
    children: [
      {
        title: '执行资源调度',
        name: 'zydd',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },

      {
        title: '资源调度结果',
        name: 'zyddjh',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
      {
        title: '测控态势推演',
        name: 'rwzs',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
      {
        title: '资源状态统计',
        name: 'zyfpqk',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
    ],
  },
  {
    title: '态势呈现',
    name: 'tsty',
    path: '/home/<USER>',
    imgUrlName: '/images/header/态势.png',
    children: [
      // {
      //   title: "态势推演",
      //   name: "rwzs",
      //   path: "/home/<USER>",
      //   imgUrlName: "current_time",
      // },
      // {
      //   title: "态势呈现",
      //   name: "panorama",
      //   path: "/home/<USER>",
      //   imgUrlName: "current_time",
      // },
      {
        title: '测控全景态势',
        name: 'panorama1',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
      {
        title: '测控任务态势',
        name: 'panorama2',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
      {
        title: '测控资源态势',
        name: 'panorama3',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
      {
        title: '测控目标态势',
        name: 'panorama4',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
      {
        title: '生成态势时间测试',
        name: 'panorama5',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
    ],
  },
  {
    title: '基础数据管理',
    name: 'baseDataPage',
    path: '/home/<USER>',
    imgUrlName: '/images/header/基础数据.png',
    children: [
      // {
      //   title: "资源数据管理",
      //   name: "baseDataPage",
      //   path: "/home/<USER>",
      //   imgUrlName: "current_time",
      // },
      {
        title: '资源数据管理',
        name: 'resourceDomain',
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
      // {
      //   title: '无人平台管理',
      //   name: 'wrptgl',
      //   path: '/home/<USER>',
      //   imgUrlName: 'current_time',
      // },
      {
        title: '目标数据管理',
        name: 'targetManage',
        // path: "/home/<USER>",
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
      {
        title: '航迹数据管理',
        name: 'trackManager',
        // path: "/home/<USER>",
        path: '/home/<USER>',
        imgUrlName: 'current_time',
      },
    ],
  },
  {
    title: '系统设置',
    name: 'stsz',
    path: '/system/stsz',
    imgUrlName: '/images/header/设置.png',
    children: [
      {
        title: '用户管理',
        name: 'userManage',
        path: '/system/userManage',
        imgUrlName: 'current_time',
      },
      {
        title: '系统设置',
        name: 'systemConfig',
        path: '/system/systemConfig/systemConfig',
        imgUrlName: 'current_time',
      },
    ],
  },
  // { title: "接收数据管理", name: "JSSJGL", path: "/home/<USER>", imgUrlName: "current_time"},
  // { title: "实时态势", name: "SSTS", path: "/home/<USER>", imgUrlName: "data_reduction" },
  // { title: "回放态势", name: "HFTS", path: "/home/<USER>", imgUrlName: "play_back" },
  // { title: "系统管理", name: "XTGL", path: "/home/<USER>", imgUrlName: "system_settings" },
]
const moreFLag = ref(false)
const moreRow = ref({})
const delBusinessRow = (row) => {
  console.log('row', row)
  moreRow.value = row
  moreFLag.value = true
}
//跳转到空球
function earthPage() {
  // routerTo("/home/<USER>");
  routerTo('/home/<USER>')
}
// 监听路由变化，自动更新activeIndex以保持菜单高亮同步
watch(
  () => route.path,
  (newPath) => {
    console.log('路由变化，更新菜单高亮:', newPath)

    // 根据路由路径找到对应的菜单项路径
    let menuPath = findMenuPathByRoute(newPath)
    if (menuPath) {
      activeIndex.value = menuPath
      console.log('设置菜单高亮:', menuPath)
    }

    // 更新态势页面标题
    updatePanoramaTitle(newPath)
  },
  { immediate: true }
)

// 根据路由路径查找对应的菜单路径
function findMenuPathByRoute(routePath) {
  // 遍历菜单配置
  for (let item of defaultMenuList) {
    // 直接匹配父菜单
    if (item.path === routePath) {
      return item.path
    }
    // 检查子菜单
    if (item.children) {
      for (let child of item.children) {
        if (child.path === routePath) {
          return item.path // 返回父菜单路径用于高亮
        }
      }
    }
  }

  return null
}

// 更新态势页面标题
function updatePanoramaTitle(routePath) {
  // 定义态势页面路径和对应标题的映射
  const panoramaTitleMap = {
    '/home/<USER>': '测控全景态势',
    '/home/<USER>': '测控任务态势',
    '/home/<USER>': '测控资源态势',
    '/home/<USER>': '测控目标态势'
  }

  // 如果是态势页面，设置对应标题，否则清空标题
  if (panoramaTitleMap[routePath]) {
    subTitle.value = panoramaTitleMap[routePath]
  } else {
    subTitle.value = ''
  }
}
// 自建方法
/**
 * @description 菜单选择
 * @param {*} path  路由
 */
// 路由跳转
const routerTo = async (path) => {
  console.log('path', path)
  //path == '/home'  || path == '/home/<USER>'
  console.log(path)
  if (!path) {
    return
  }
  //  else if (path == "/home/<USER>") {
  //   ElMessageBox.alert("该页面尚未开发", "提示", {
  //     confirmButtonText: "确认",
  //   });
  //   return;
  // }
  if (path == '/system/systemConfig/systemConfig') {
    state.showSystemSet = true
    return
  }
  oneRouter.value = path
  activeIndex.value = path

  // 更新全家的一级菜单
  routerstore.setOneRouter(path)

  // if (path == "/home/<USER>") {
  //   window.pageType == 1;
  // } else if (path == "/home/<USER>") {
  //   window.pageType == 2;
  // }
  window.currentRequirement = null //sdw 跳转的时候把此值空防止进入页面自动填入
  router.push({
    path: path,
    replace: false,
  })
}
const menuClick = (item) => {
  // 使用统一的标题更新函数
  updatePanoramaTitle(item.path)
}
function userConfig() {
  // dataApi.getUserInfo({id:})
  routerTo('/login')
}
//时间组装
const formattedDate = (data) => {
  var date = data
  var year = date.getFullYear()
  var month = (date.getMonth() + 1).toString().padStart(2, '0')
  var day = date.getDate().toString().padStart(2, '0')
  var hours = date.getHours().toString().padStart(2, '0')
  var minutes = date.getMinutes().toString().padStart(2, '0')
  var seconds = date.getSeconds().toString().padStart(2, '0')
  return (
    year + '/' + month + '/' + day + ' ' + hours + ':' + minutes + ':' + seconds
  )
}
/**
 * 页面加载完成后执行方法
 */
onMounted(() => {
  console.log('router', router.currentRoute.value)
  // 使用统一的标题更新函数
  updatePanoramaTitle(router.currentRoute.value.path)

  setInterval(() => {
    currentTime.value = formattedDate(new Date())
    currentUser.value = 'admin'
  }, 1000)
  window.addEventListener('onmessageWS', getSocketData)
})
// 获取webscoket的数据
// function getSocketData(evt) {
//   let data = evt.detail.data;

//   if (data.type == 3) {
//     state.badgeHidden = false
//     // return;
//   }
// }
//接收websocket消息
function getSocketData(evt) {
  let data = evt.detail.data
  if (data.type !== 4) {
    return
  }
  homeApi.getExternalRequirement().then((res) => {
    if (res.data.code == 200) {
      state.EmailList = [...res.data.data]
      state.EmailList.sort((a, b) => {
        return (
          new Date(b.requestTime).getTime() - new Date(a.requestTime).getTime()
        )
      })
      emailCount.value = state.EmailList.length
      state.badgeHidden = emailCount.value == 0
    } else {
      ElMessage.error(res.data.message)
    }
  })
  state.badgeHidden = false
}
// 退出登录
const exitLogin = () => {
  ElMessageBox.alert('确认退出登录？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      sessionStorage.removeItem('userToken')
      routerTo('/login')
      ElMessage.success('退出成功')
    })
    .catch(() => {})
}
//展示新消息
function newBadgeMsg() {
  //   homeApi.getRequirementFile().then(res => {
  //     if (res.data.code == 200) {
  //       state.EmailList = []
  //       state.EmailList = [{
  //   name:'远遥港测试任务',resource:'无人艇用户中心',ip:'*************',port:'8890',time:'2025-03-06 19:42:56'
  // }]
  //       state.badgeHidden = true
  //       state.showEmail = true
  //       for (const key in res.data.data) {
  //         let item = res.data.data[key]
  //         state.EmailList.push(item)
  //       }
  //       state.EmailList = state.EmailList.sort(a, b => {
  //         return a.receiveTime - b.receiveTime
  //       })

  //     } else {
  //       ElMessage.error(res.data.message)
  //     }
  //   })

  state.showEmail = true
  homeApi.getExternalRequirement().then((res) => {
    if (res.data.code == 200) {
      state.EmailList = [...res.data.data]
      state.EmailList.sort((a, b) => {
        return (
          new Date(b.requestTime).getTime() - new Date(a.requestTime).getTime()
        )
      })
      emailCount.value = state.EmailList.length
      state.badgeHidden = emailCount.value == 0
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
//需求分解
function requirementHandle() {
  // homeApi.parseExcel({ id: row.id }).then(res => {
  //   if (res.data.code == 200) {
  //     ElMessage.success("文件解析成功！")
  //     userStore.changeEmailRequirementHandle({})
  //     newBadgeMsg()
  //   } else {
  //     ElMessage.error(res.data.message)
  //   }
  // })

  homeApi
    .receiveExternalRequirement({
      keys: emalTableRef.value.getSelectionRows().map((item) => item.id),
      type: 1,
    })
    .then((res) => {
      if (res.data.code == 200) {
        ElMessage.success('需求接收成功！')
        userStore.changeEmailRequirementHandle({})
        newBadgeMsg()
      } else {
        ElMessage.error(res.data.message)
      }
    })
}
//邮件忽略
function requirementDel(row) {
  // homeApi.lgnoreExcel({ id: row.id }).then(res => {
  //   if (res.data.code == 200) {
  //     ElMessage.success("已忽略")
  //     newBadgeMsg()
  //   } else {
  //     ElMessage.error(res.data.message)
  //   }
  // })
  homeApi
    .receiveExternalRequirement({
      keys: emalTableRef.value.getSelectionRows().map((item) => item.id),
      type: 2,
    })
    .then((res) => {
      if (res.data.code == 200) {
        ElMessage.success('已忽略')
        newBadgeMsg()
      } else {
        ElMessage.error(res.data.message)
      }
    })
}
</script>
<style scoped lang="less">
@import '../assets/style/theme/style.less';

.header_list_top {
  display: flex;
  // background-image: url("../public/images/header/headerBg.png");
  background-image: url('/public/images/new/icon/无人-top.png');
  background-size: cover;
  background-position: center center;
  background-color: #001331;
  z-index: 2099 !important;
  position: relative;
  height: 60px;
}

.img-logo {
  width: 20%;
  height: 60px;
  box-sizing: border-box;
  padding-left: 60px; //sdw
  letter-spacing: 20px; //sdw
  color: white;
  line-height: 60px;
  font-size: 23px;
  cursor: pointer;

  > div {
    font-size: 23px;
  }
}

.active .menu-span {
  color: rgb(14, 191, 231);
  font-weight: bold;
  font-size: 16px;
  z-index: 9;
}

.menu-span {
  color: #ffffff;
}

.menu-item {
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 110px;
  height: 100%;
  font-size: 16px;
  text-align: center;
  color: #333333;
  cursor: pointer;
}

.user-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #0ebfe7;
  height: 100%;
  font-size: 16px;

  .time {
    color: #ffffff;
    padding-right: 10px;
  }

  .userName {
    color: #ffffff;
    padding-right: 20px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    cursor: pointer;
  }

  .userExit {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding-right: 20px;
    cursor: pointer;
  }
}

.menuList {
  width: 60%;
  height: 47px;
  position: relative;

  .img {
    width: 25px;
    height: 25px;
    padding: 5px;
    background-size: 100% 100%;
    box-sizing: border-box;
  }

  /* .menu {
      // height: 94%;
    } */
}

.el-menu--horizontal.el-menu {
  border-bottom: none !important;
  z-index: 9;
}

.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: none !important;
}

.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: none !important;
}

.el-menu--popup {
  background-color: white;
  z-index: 9999 !important;
}

/* .el-menu-item {
    // background-image: url('../../public/images/header/一级菜单按钮-bg-默认.png');
    // background-image: @bg_mr;

    // background-size: cover;
    // background-position: center center;
  }

  // .el-menu--horizontal{
  //   background-color: #0597ff;

  // } */
.el-menu .el-menu--popup .el-menu--popup-bottom-start {
  // background-color: #0597ff;
  background-color: #092a4a;
}

.el-menu-item.is-active.is-opened {
  background-color: #fff;
  /* // background-image: url('../../public/images/header/一级菜单按钮-bg.png');
    // background-image: @bg;

    // background-size: cover;
    // background-position: center center;
    // background-color:#0000001a !important
    // color: rgba(@themeColor, 1) !important; */
}

.el-menu--horizontal > .el-menu-item.is-active {
  background-color: #092a4a !important;
  // color: #329efe;
}

.el-menu--horizontal > .el-sub-menu.is-active {
  background-color: #092a4a !important;
  // color: #329efe;
}

.el-menu--horizontal > .el-menu-item.is-active {
  // color: #329efe !important;
}

/* // .el-menu--horizontal>.el-menu-item.is-active,
// .el-menu-item.is-active {
//   color: #fff !important;
//   color: #329efe !important;
// }

// .el-menu-item:hover {
//   background-color: #0000001a !important
// } */

:deep(.el-menu--horizontal > .el-menu--popup-container) {
  background-color: #022141c0 !important;
}

.current_time {
  background-image: @current_time;
}

.data_reduction {
  background-image: @data_reduction;
}

.play_back {
  background-image: @play_back;
}

.system_settings {
  background-image: @system_settings;
}

.el-menu-item.is-active {
  color: #fff !important;
}

.msg-list {
  position: fixed;
  top: 8%;
}

:deep(.el-badge__content) {
  align-items: flex-start !important;
  height: 15px !important;
  padding: 0 3px !important;
  line-height: 1;
}

.info-content-box {
  text-align: center;
  font-weight: bold;
  height: 90px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  > span {
    font-size: 18px;
  }
}

.requirement_form {
  :deep(.el-form-item) {
    margin-bottom: 0px !important;
  }
}

.title {
	position: absolute;
	z-index: 1;
	display:flex;
	align-content:center;
	text-align: center;
	// background-color: ;
	min-width: 180px;
	left: calc(50% - 70px);
	top: 80px;
	//pointer-events:none;
	//background-color: rgba(2, 33, 65, 0.75);
	//border: 2px solid rgba(44, 101, 159, 0.75);
	//border-radius: 2px;
	span{
		margin:0 10px;
		font-size: 16px;
		text-shadow: 4px 2px 2px #001420;
		//-webkit-text-stroke: 4px rgb(47, 98, 129);
		-webkit-text-stroke: 4px #1c5577;
		position:relative;
		font-weight: 100;
		&::after{
			content: attr(data-content);
			color: rgb(240, 243, 245);
			position: absolute;
			-webkit-text-stroke-width: 0;
			left: 0;
		}
	}
	img{
		height: 20px;
	}
}

.custom-submenu {
  z-index: 9999 !important;
}
</style>
