<!-- 航迹管理 -->
<template>
  <div style="width: 100%; height: 100%">
    <CesiumBox></CesiumBox>
  </div>
  <customPopup width="20%" height="70%" top="70px" left="20px">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">航迹列表</span>
      <div style="position: absolute; right: 2%">
        <el-button plain @click="addPreset">新增航迹</el-button>
      </div>
    </template>
    <template #content>
		<div style="display:flex;margin-bottom:8px">
			<el-input
				placeholder="请输入关键字"
				v-model="state.keyword"
				clearable
				@clear="getPresetList"
				@keydown.enter="getPresetList"
			></el-input>
			<el-button
				style="margin-left:15px"
				@click="getPresetList"
			>
				搜索
			</el-button>
		</div>
		<div style="height: calc(100% - 70px)">
			<el-table
				ref="trackTableRef"
				row-key="id"
				:data="state.presetList"
				class="custom-table"
				highlight-current-row
				@row-click="handleDetail"
				style="height: 100%"
			  >
				<el-table-column align="center" width="60">
				  <template #header>
					<div>
					  <el-tooltip content="全显示" placement="bottom" effect="light">
						<el-link
						  :underline="false"
						  icon="View"
						  @click="showAll"
						></el-link>
					  </el-tooltip>
					  /
					  <el-tooltip content="全隐藏" placement="bottom" effect="light">
						<el-link
						  :underline="false"
						  icon="Hide"
						  @click="unShowAll"
						></el-link>
					  </el-tooltip>
					</div>
				  </template>
				  <template #default="{ row, $index }">
<!--					  :true-label="1"-->
<!--					  :false-label="0"-->
					<el-checkbox
					  v-model="state.showTrack[row.id]"
					  @click.stop
					  @change="trackShowChange(row, state.showTrack[row.id])"
					/>
				  </template>
				</el-table-column>
				<el-table-column
				  label="航迹名称"
				  align="center"
				  prop="name"
				  min-width="100"
				></el-table-column>
				<el-table-column
				  label="航迹类型"
				  align="center"
				  prop="type"
				  min-width="90"
				>
				  <template #default="scope">
					<span>{{ dictValue('trackType', scope.row.type) }}</span>
					<!-- <span>{{ scope.row.altitude }}</span> -->
				  </template>
				</el-table-column>
				<el-table-column label="操作" width="100" align="center">
			  <template v-slot="scope">
				<el-tooltip
				  popper-class="login-tooltip"
				  class="item"
				  effect="dark"
				  content="详情查看"
				  placement="bottom"
				>
				  <!-- <img :src="imagesIcon.detailIcon" style="width: 22px; height: 22px; cursor: pointer" @click="handleDetail(scope.row)" type="info" /> -->
				  <span
					class="custom_header_ck"
					style="
					  display: inline-block;
					  width: 22px;
					  height: 22px;
					  cursor: pointer;
					"
					@click.stop="handleDetail(scope.row)"
				  ></span>
				</el-tooltip>
				<!-- <el-tooltip popper-class="login-tooltip" class="item" effect="dark" content="位置" placement="bottom">
				  <span class="custom_header_wz"
					style="display: inline-block; width: 22px; height: 22px; cursor: pointer; margin-left: 10px"></span>
				</el-tooltip> -->
				<el-popconfirm
				  confirm-button-text="确认"
				  cancel-button-text="取消"
				  width="220px"
				  :icon="InfoFilled"
				  icon-color="red"
				  title="是否确定删除当前数据?"
				  @confirm="handleDelete(scope.row)"
				>
				  <template #reference>
					<span @click.stop>
					  <el-tooltip
						popper-class="login-tooltip"
						class="item"
						effect="dark"
						content="删除"
						placement="bottom"
					  >
						<!-- <img :src="imagesIcon.deleteIcon" style="width: 25px; height: 25px; margin-left: 10px; cursor: pointer; transform: translateY(2px)" type="info" /> -->
						<span
						  class="custom_header_sc"
						  style="
							display: inline-block;
							width: 25px;
							height: 25px;
							margin-left: 10px;
							cursor: pointer;
							transform: translateY(2px);
						  "
						></span>
					  </el-tooltip>
					</span>
				  </template>
				</el-popconfirm>
			  </template>
			</el-table-column>
		  	</el-table>
		</div>
		<div style="padding-top:8px" class="pageBox">
			<el-pagination
				v-model:currentPage="state.currentPage"
				v-model:page-size="state.pageSize"
				:page-sizes="[15,30,40,60]"
			   	layout="total, prev, pager, next"
				:pager-count="5"
				small
				:total="state.total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>
    </template>
  </customPopup>
  <customPopup
    width="20%"
    height="70%"
    top="70px"
    left="22%"
    v-if="state.presetFormShow"
  >
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">航迹详细信息</span>
      <div style="position: absolute; right: 2%">
        <el-button @click="saveForm">保存</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button @click="closeEdit">关闭</el-button>
      </div>
    </template>
    <template #content>
      <div style="height: 100%; width: 100%; overflow-y: auto">
        <el-form ref="form" :model="state.presetForm" label-width="80px">
          <el-form-item label="名称" prop="name">
            <el-input
              placeholder="请填写名称"
              :maxLength="20"
              style="width: 90%"
              v-model="state.presetForm.name"
            ></el-input>
          </el-form-item>
          <el-form-item label="航迹类型" prop="type">
            <el-select
              placeholder="请选择航迹类型"
              style="width: 90%"
              v-model="state.presetForm.type"
            >
              <el-option
                v-for="(value, key) in state.dictTypeList['trackType']"
                :key="key"
                :label="value"
                :value="Number(key)"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="视点高度" prop="description">
            <el-input-number
              :min="0"
              v-model="state.presetForm.viewHeight"
              :controls="false"
              :step="1"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input
              placeholder="请填写名称"
              :rows="2"
              type="textarea"
              v-model="state.presetForm.description"
              style="width: 90%"
            ></el-input>
          </el-form-item>
          <el-form-item label="速度" prop="description">
            <el-input-number
              :min="0"
              v-model="state.presetForm.speed"
              :step="1"
            ></el-input-number>
            &nbsp;&nbsp;km/h
          </el-form-item>
          <el-form-item label="航迹点">
            <el-button @click="trackDraw">手动绘制</el-button>（右键结束绘制）
            <!-- <el-button>上传文件</el-button> -->
          </el-form-item>
          <el-row style="width: 100%">
            <el-table
              :data="state.trackList"
              class="custom-table"
              style="height: 200px"
            >
              <el-table-column type="index" width="50" align="center" />
              <el-table-column label="经度" min-width="100" align="center">
                <template #default="scope">
                  <span>{{ scope.row.longitude }}</span>
                  <!-- <span>{{ scope.row.altitude }}</span> -->
                </template>
              </el-table-column>
              <el-table-column label="纬度" min-width="100" align="center">
                <template #default="scope">
                  <span>{{ scope.row.latitude }}</span>
                  <!-- <span>{{ scope.row.altitude }}</span> -->
                </template>
              </el-table-column>
              <el-table-column label="高度" min-width="100" align="center">
                <template #default="scope">
                  <span>{{ scope.row.altitude }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-row>
        </el-form>
      </div>
    </template>
  </customPopup>


  <el-dialog title="提示" width="400px" draggable v-model="state.areaDialogShow"
    :close-on-click-modal="false">
    <span style="color: #ffffff;">
      是否同步该航迹到关联任务？
    </span>
    <template #footer>
      <div class="dialog-footer">
        <el-button size="small" @click="areaDialogClose">取 消</el-button>
        <el-button
          size="small"
          type="primary"
          @click="areaDialogSubmit(areaFormRef)"
          :loading="state.loading"
        >
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { onMounted, reactive, ref, onUnmounted, nextTick } from 'vue'
import CesiumBox from '@/components/evmap/CesiumBox.vue'
import * as trackApi from '@/service/API/system/trackManager.js'
import customPopup from '@/components/customPopup.vue'
import { ElMessage } from 'element-plus'
import {InfoFilled} from '@element-plus/icons-vue';

const dictValue = window.dictValue
let trackTableRef = ref()
const state = reactive({
  presetList: [],
  keyword: '',
  currentPage: 1,
  pageSize: 15,
  total: 0,
  trackList: [],
  presetForm: {},
  presetFormCopy: {}, //用于重置
  presetFormShow: false,
  showTrack: {},
  areaDialogShow: false,
  title:'',// 新增还是编辑
  isAll:true
})

onMounted(() => {
  console.log('adasdqweasasdas')
  state.dictTypeList = window.dictTypeList
  getPresetList()
})

// 获取航迹信息列表
function getPresetList(color) {
  state.presetList.forEach((row) => {
	mapTool.removeById(row.id)
  })
  state.showTrack = {}

  trackApi.getPresetList({ page: state.currentPage, size: state.pageSize, keyword: state.keyword }).then((res) => {
    if (res.data.code == 200) {
		const list = res.data.data.records || []
	    state.total = res.data.data.total || 0
		list.forEach((item) => {
		  if (item.tracks && item.tracks.length) {
		    if (highlightId === item.id) {
			  drawLine(item.tracks, item.id, item.name, 'yellow')
		    } else {
			   drawLine(item.tracks, item.id, item.name)
		    }
		    state.showTrack[item.id] = state.isAll// 修改
		  }
	    })
		state.presetList = list
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
function handleSizeChange() {
	getPresetList()
}
function handleCurrentChange() {
	getPresetList()
}
//详情查看点击事件
function handleDetail(row) {

  clearNewRoute()
  trackApi.getTrackList({ presetId: row.id }).then((res) => {
    if (res.data.code == 200) {
      state.trackList = res.data.data.tracks
      state.presetForm = JSON.parse(JSON.stringify(row))
      state.presetFormCopy = JSON.parse(JSON.stringify(row))
      state.presetFormShow = true
      state.title = 'edit'
      handPosition(state.presetForm)
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
let highlightId = null,
  highlightTrack = [],
  highlightName = ''
//查看定位 flyto第一个点
function handPosition(row) {
  trackApi.getTrackList({ presetId: row.id }).then((res) => {
    if (res.data.code == 200) {
      state.trackList = res.data.data.tracks
      // state.presetFormShow = false;
      if (res.data.data.tracks.length > 0) {
        flyto(res.data.data.tracks[0], row.viewHeight)
        if (row.id == highlightId) {
          return
        }
        drawLine(state.trackList, row.id, row.name, 'yellow')
        if (highlightId) {
          drawLine(highlightTrack, highlightId, highlightName, 'red')
        }
        highlightId = row.id
        highlightTrack = JSON.parse(JSON.stringify(state.trackList))
        highlightName = row.name
      } else {
        ElMessage.warning('航迹未绘制')
      }
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
function flyto(row, viewHeight) {
  Viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      row.longitude,
      row.latitude,
      viewHeight || 2999990
    ),
  })
  // window.EVGISMAP("flyTo", {
  //   center: [row.longitude, row.latitude, row.viewHeight || 2999990],
  // });
}
//删除操作
function handleDelete(row) {
  trackApi.delPresetById({ id: row.id }).then((res) => {
    if (res.data.code == 200) {
      mapTool.removeById(row.id)
      if (state.presetForm.id == row.id) {
        state.presetFormShow = false
      }
      if (highlightId == row.id) {
        highlightId = null
        highlightTrack = []
        highlightName = ''
      }
      ElMessage.success('删除成功')
      getPresetList()
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
// 新建预设数据
function addPreset() {
  state.presetForm = {}
  state.presetFormCopy = {}
  state.trackList = []
  state.presetFormShow = true
  state.title='add'
}
//重置
function reset() {
  state.presetForm = state.presetFormCopy
  clearNewRoute()
  if (state.presetForm.id) {
    handleDetail(state.presetForm)
  }
}
// 关闭
function closeEdit() {
  clearNewRoute()
  state.presetFormShow = false
}
//根据速度计算航迹时间
function calcTime() {
  for (let i = 0; i < state.trackList.length; i++) {
    let item = state.trackList[i]
    let itemLast = state.trackList[i - 1]
    if (i == 0) {
      state.trackList[i].time = 0
      // item.distance = 0;
    } else {
      let distance = calcDistance(
        item.longitude,
        item.latitude,
        itemLast.longitude,
        itemLast.latitude
      )
      state.trackList[i].time =
        parseInt((distance / state.presetForm.speed) * 60 * 60) +
        (itemLast?.time || 0)
      // item.timeVal = item.timeSpan + new Date(itemLast.timeVal).getTime();
      // item.distance = distance;
    }
    item.speed = state.presetForm.speed
  }
}
//计算距离
function calcDistance(lng1, lat1, lng2, lat2) {
  let rad1 = (lat1 * Math.PI) / 180.0
  let rad2 = (lat2 * Math.PI) / 180.0
  let a = rad1 - rad2
  let b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180
  let r = 6378137
  let distance =
    r *
    2 *
    Math.asin(
      Math.sqrt(
        Math.pow(Math.sin(a / 2), 2) +
          Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)
      )
    )
  return Number((distance / 1000).toFixed(2))
}
//保存数据
function saveForm() {
  if (!state.presetForm.name) {
    ElMessage.error('请输入预设航迹名称')
    return
  }
  if (!state.presetForm.type && state.presetForm.type !== 0) {
    ElMessage.error('请选择航迹类型')
    return
  }
  if (!state.presetForm.speed) {
    ElMessage.error('请填写速度')
    return
  }
  if (state.trackList.length == 0) {
    ElMessage.error('请绘制航迹')
    return
  }
  if(state.title != 'add'){
    state.areaDialogShow = true
  }else{
    // 更新关联任务的航迹提示 关闭弹出框
    auPresetListFucn()
    // trackApi.auPresetList({ ...state.presetForm }).then((res) => {
    //   if (res.data.code == 200) {
    //     state.presetForm.id = res.data.data?.id || state.presetForm.id;
    //     highlightTrack = JSON.parse(JSON.stringify(state.trackList))
    //     auATrack();
    //   } else {
    //     ElMessage.error(res.data.message);
    //   }
    // });
  }
}

// 更新关联任务的航迹提示
function areaDialogSubmit() {
  auPresetListFucn()
  trackApi.updateTaskTrack({ presetId: state.presetForm.id }).then((res) => {
    if (res.data.code == 200) {
      state.areaDialogShow = false
      ElMessage.success("更新关联任务成功！");
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
function auPresetListFucn() {
  trackApi.auPresetList({ ...state.presetForm }).then((res) => {
    if (res.data.code == 200) {
      state.presetForm.id = res.data.data?.id || state.presetForm.id
      highlightTrack = JSON.parse(JSON.stringify(state.trackList))
      auATrack()
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
// 更新关联任务的航迹提示 关闭弹出框
function areaDialogClose() {
  state.areaDialogShow = false
  auPresetListFucn()
}

function auATrack() {
  calcTime()
  trackApi
    .setTrackList({
      presetId: state.presetForm.id,
      tracks: [...state.trackList],
    })
    .then((res) => {
      if (res.data.code == 200) {
        ElMessage.success('保存成功')
        // state.trackList = res.data.data;
        mapTool.removeById('new_polyLine')
        // setTimeout(() => {
        // trackTableRef.value.setCurrentRow({ id: state.presetForm.id })
        state.presetFormShow = false
        // }, 2000);
        getPresetList()
      } else {
        ElMessage.error(res.data.message)
      }
    })
}
//绘制航迹
function trackDraw() {
  let handle = () => {
    setTimeout(() => {
      window.EVGISMAP('drawPolyline', {
        id: 'new_polyLine',
        group: 'DRAWLAYERGROUP',
        outlineColor: 'yellow',
        outlineWidth: '2',
        callBack: (params) => {
          state.trackList = []
          params.position.forEach((pos) => {
            let track = {
              presetId: state.presetForm.presetId,
              longitude: Number(pos[0].toFixed(6)),
              latitude: Number(pos[1].toFixed(6)),
              altitude: 0,
              time: undefined,
              direct: 0,
              speed: undefined,
            }
            state.trackList.push(track)
          })
          clearNewRoute()
          setTimeout(() => {
            drawLine(state.trackList, undefined, '当前绘制')
          }, 0)
          // trackSave(params);
        },
      })
    }, 200)
  }
  // let entity = mapTool.getById("new_polyLine")
  clearNewRoute()
  // if (entity) {
  // }
  handle()
}
//清除航迹
function clearNewRoute() {
  mapTool.removeById('new_polyLine')
  window?.EVGISMAP('removeGroupEntityById', {
    group: 'DRAWLAYERGROUP',
    id: 'new_polyLine',
  })
}
//绘制航迹
function drawLine(trackList, id, name, outlineColor) {
  mapTool.drawMap({
    id: id || 'new_polyLine',
    name: name,
    outlineColor: outlineColor ? outlineColor : 'red',
    outlineWidth: '2',
    targetTrack: trackList,
    position: [
      trackList[0].longitude,
      trackList[0].latitude,
      trackList[0].altitude,
    ],
  })
}
var nextTrackId
// 显隐航迹
function trackShowChange(row, val) {
	nextTick(() => {
  		mapTool.showEntityById(row.id || 'new_polyLine', val)
	})
}
//显示所有
function unShowAll() {
  state.presetList.forEach((item) => {
    state.showTrack[item.id] = false
    state.isAll = false
    trackShowChange(item, false)
  })
}
function showAll() {
  state.presetList.forEach((item) => {
    state.showTrack[item.id] = true
    state.isAll = true
    trackShowChange(item, true)
  })
}
</script>

<style scoped lang="less">
.custom_header_ck {
  background-image: url('/images/icon/xq.png');
  background-size: cover;
}

.custom_header_wz {
  background-image: url('/images/icon/白_39.png');
  background-size: cover;
}
.pageBox .el-pagination{
	--el-pagination-button-color:#a8a8a8;
}
</style>
