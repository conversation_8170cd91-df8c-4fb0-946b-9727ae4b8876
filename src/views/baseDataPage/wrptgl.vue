<template>
  <customPopup left="10px" top="80px" width="15%" height="90%">
    <template #header>
      <span class="title-font">
        无人设备列表
        <el-button type="primary" icon="CirclePlus" style="margin-right: 10px">新增</el-button>
      </span>
    </template>
    <template #content>
      <div style="display: flex; margin-bottom: 5px">
        <el-input v-model="leftNodeTreeInput"></el-input>
        <el-button type="primary" icon="Search">搜索</el-button>
      </div>
      <!-- show-checkbox  -->
      <div class="leftNodeTree">
        <!-- 只有数据的树（无法操作） -->
        <el-tree
          :data="dataSource"
          node-key="id"
          default-expand-all
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
          :current-node-key="11"
          :highlight-current="true"
        >
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <span>{{ node.label }}</span>
            </span>
          </template>
        </el-tree>
      </div>
    </template>
  </customPopup>
  <div class="contentDIV">
    <div class="particularsDIV">
      <customPopup width="100%" height="100%">
        <template #header>
          <span class="title-font">平台详情</span>
        </template>
        <template #content>
          <el-form :inline="true" :model="{}" label-position="right" label-width="130px">
            <template v-for="item in Object.keys(currentNode)" :key="item">
              <el-form-item :label="item + ':'">
                <span>{{ currentNode[item] }}</span>
              </el-form-item>
            </template>
          </el-form>
        </template>
      </customPopup>
    </div>
    <!-- <div class="rwglDIV">
      <customPopup width="100%" height="51%">
        <template #header>
          <span class="title-font">关联需求任务</span>
        </template>
        <template #content>
          <el-tabs v-model="activeName" style="height: 100%">
            <el-tab-pane label="当前" name="first">
              <el-table
                :data="tableData"
                style="width: 100%"
                class="custom-table"
                :cell-style="cellStyle"
              >
                <template v-for="e of tableOptions" :key="e.label">
                  <el-table-column :prop="e.prop" :label="e.label" />
                </template>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="历史" name="second">
              <el-table
                :data="tableData1"
                style="width: 100%"
                class="custom-table"
                :cell-style="cellStyle"
              >
                <template v-for="e of tableOptions" :key="e.label">
                  <el-table-column :prop="e.prop" :label="e.label" />
                </template>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </template>
      </customPopup>
    </div> -->
  </div>
  <!-- <customPopup1 right="10px" top="80px" width="20%" height="90%">
        <template #header>
      <span class="title-font">详情</span>
      <span class="closePopBtn" @click="handleCloseClick('list')">
        <el-icon>
          <Close />
        </el-icon>
      </span>
    </template>
    <template #content>
      <el-tabs v-model="activeName" style="height: 100%;">
        <el-tab-pane label="平台详情" name="first">
          <el-form :inline="true" :model="{}" label-position="right" label-width="150px">
            <template v-for="item in Object.keys(currentNode)" :key="item">
              <el-form-item :label="item+':'">
                <span>{{ currentNode[item] }}</span>
              </el-form-item>
            </template>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="关联调度任务" name="second">
          <el-table :data="tableData" style="width: 100%" class="custom-table" :cell-style="cellStyle">
            <template v-for="e of tableOptions" :key="e.label">
              <el-table-column :prop="e.prop" :label="e.label" />
            </template>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </template>
  </customPopup1> -->
  <customPopup1 left="17%" top="80px" width="59%" height="90%">
    <template #content>
      <div style="height: 29%; display: flex; margin-top: 20px">
        <el-button style="position: absolute; top: 10px; right: 28px" @click="goBack">返回</el-button>
        <div style="flex: 1">
          <div ref="Chart" style="height: 100%; width: 100%"></div>
        </div>
        <div style="flex: 1">
          <div ref="Chart1" style="height: 100%; width: 100%"></div>
        </div>
        <div style="flex: 1">
          <div ref="Chart2" style="height: 100%; width: 100%"></div>
        </div>
      </div>
      <div class="title-font headerDiv" style="height: 50px">
        <span></span>
        <div class="header-title">
          {{ ClickTree }}
        </div>
        <el-button type="primary" icon="CirclePlus" style="margin-right: 10px">新增</el-button>
      </div>
      <div class="headerLine">
        <div class="headerLine-left"></div>
        <div class="headerLine-md"></div>
        <div class="headerLine-right"></div>
      </div>
      <div style="display: flex; margin-bottom: 5px">
        <el-input v-model="leftNodeTreeInput"></el-input>
        <el-button type="primary" icon="Search">搜索</el-button>
      </div>
      <div style="height: 55%; margin-top: 10px; border: 1px solid #3691d291; padding: 5px">
        <customTable
          :tableData="stateData.tableData"
          :tableColumn="stateData.tableColumn"
          :total="total"
          :currentPage="currentPage"
          :pageSize="pageSize"
          @nodeClickEmit="nodeClickEmit"
          @handlePositionEmit="handlePosition"
        ></customTable>
      </div>
    </template>
  </customPopup1>
  <!-- <customPopup left="17%" top="80px" width="59%" height="90%">
    <div style="height: 29%; display: flex; margin-top: 20px">
      <el-button
        style="position: absolute; top: 10px; right: 28px"
        @click="goBack"
        >返回</el-button
      >
      <div style="flex: 1">
        <div ref="Chart" style="height: 100%; width: 100%"></div>
      </div>
      <div style="flex: 1">
        <div ref="Chart1" style="height: 100%; width: 100%"></div>
      </div>
      <div style="flex: 1">
        <div ref="Chart2" style="height: 100%; width: 100%"></div>
      </div>
    </div>
    <template #header>
      <span class="title-font">
        {{ ClickTree }}
        <el-button type="primary" icon="CirclePlus" style="margin-right: 10px"
          >新增</el-button
        >
      </span>
    </template>
    <template #content>
      <div style="display: flex; margin-bottom: 5px">
        <el-input v-model="leftNodeTreeInput"></el-input>
        <el-button type="primary" icon="Search">搜索</el-button>
      </div>
      <div
        style="
          height: 90%;
          margin-top: 10px;
          border: 1px solid #3691d291;
          padding: 5px;
        "
      >
        <customTable
          :tableData="stateData.tableData"
          :tableColumn="stateData.tableColumn"
          :total="total"
          :currentPage="currentPage"
          :pageSize="pageSize"
          @nodeClickEmit="nodeClickEmit"
          @handlePositionEmit="handlePosition"
        ></customTable>
      </div>
    </template>
  </customPopup> -->

  <!-- <customPopup v-if="isShow" left="23%" top="80px" width="55%" height="90%"> -->
  <customPopup v-if="isShow" left="17%" top="80px" width="61%" height="90%">
    <template #header>
      <div style="position: absolute; right: 2%">
        <el-button @click="isShow = false">关闭</el-button>
      </div>
    </template>
    <template #content>
      <CesiumBox></CesiumBox>
    </template>
  </customPopup>
</template>

<script setup>
import CesiumBox from "@/components/evmap/CesiumBox.vue";
import customPopup from "@/components/customPopup.vue";
import customPopup1 from "@/components/customPopup1.vue";
import { reactive, ref, onMounted, onUnmounted, nextTick } from "vue";
import customTable from "@/components/customTable.vue";
import { useRouter } from "vue-router";
let ClickTree = ref("无人机");
const stateData = ref({
  tableColumn: [
    { label: "无人平台名称", prop: "ptmc", type: "String", width: "150" },
    { label: "平台编号", prop: "ptbh", type: "String", width: "150" },
    { label: "平台类型", prop: "ptlx", type: "String", width: "150" },
    { label: "通信方式", prop: "txfs", type: "String", width: "" },
    { label: "创建时间", prop: "cjsj", type: "String", width: "" },
    {
      prop: "",
      label: "操作",
      type: "operation",
      width: "130",
      operationTypeName: {
        position: false,
        handleDetail: true,
        demand: false,
        handleEdit: true,
        handleDelete: true,
      },
    },
  ],
  tableData: [
    {
      id: 200,
      ptlx: "无人机",
      ptmc: "ZHC-009",
      txfs: "ADSB",
      cjsj: "2024/4/10",
      ptbh: "2252-3226",
      // qspl: "22MHz",
      // zzpl: "55MHz",
      // zslmd: "52dB",
      // jhfs: "单极化",
      // txzy: "30dbm",
      sbjd: "30", // 设备经度
      sbwd: "100", // 设备纬度
      sbgd: "50000", // 设备高度
      // smbj: "100000",// 扫描半径
      dbbj: "10000", // 底部半径
      dbbj1: "0", // 顶部半径
    },
    {
      id: 201,
      ptlx: "无人机",
      ptmc: "ZHC-010",
      txfs: "ADSB",
      cjsj: "2024/4/10",
      ptbh: "2252-3227",
      // qspl: "22MHz",
      // zzpl: "55MHz",
      // zslmd: "52dB",
      // jhfs: "单极化",
      // txzy: "30dbm",
      sbjd: "30", // 设备经度
      sbwd: "100", // 设备纬度
      sbgd: "50000", // 设备高度
      // smbj: "100000",// 扫描半径
      dbbj: "10000", // 底部半径
      dbbj1: "0", // 顶部半径
    },
    {
      id: 202,
      ptlx: "无人机",
      ptmc: "ZHC-011",
      txfs: "ADSB",
      cjsj: "2024/4/10",
      ptbh: "2252-3228",
      // qspl: "22MHz",
      // zzpl: "55MHz",
      // zslmd: "52dB",
      // jhfs: "单极化",
      // txzy: "30dbm",
      sbjd: "30", // 设备经度
      sbwd: "100", // 设备纬度
      sbgd: "50000", // 设备高度
      // smbj: "100000",// 扫描半径
      dbbj: "10000", // 底部半径
      dbbj1: "0", // 顶部半径
    },
    {
      id: 203,
      ptlx: "无人船",
      ptmc: "ZHC-012",
      txfs: "ADSB",
      cjsj: "2024/4/10",
      ptbh: "2252-3229",
      // qspl: "22MHz",
      // zzpl: "55MHz",
      // zslmd: "52dB",
      // jhfs: "单极化",
      // txzy: "30dbm",
      sbjd: "30", // 设备经度
      sbwd: "100", // 设备纬度
      sbgd: "50000", // 设备高度
      smbj: "100000", // 扫描半径
    },
    {
      id: 204,
      ptlx: "无人船",
      ptmc: "ZHC-013",
      txfs: "ADSB",
      cjsj: "2024/4/10",
      ptbh: "2252-3230",
      // qspl: "22MHz",
      // zzpl: "55MHz",
      // zslmd: "52dB",
      // jhfs: "单极化",
      // txzy: "30dbm",
      sbjd: "30", // 设备经度
      sbwd: "100", // 设备纬度
      sbgd: "50000", // 设备高度
      smbj: "100000", // 扫描半径
    },
    {
      id: 205,
      ptlx: "无人船",
      ptmc: "ZHC-014",
      txfs: "ADSB",
      cjsj: "2024/4/10",
      ptbh: "2252-3231",
      // qspl: "22MHz",
      // zzpl: "55MHz",
      // zslmd: "52dB",
      // jhfs: "单极化",
      // txzy: "30dbm",
      sbjd: "30", // 设备经度
      sbwd: "100", // 设备纬度
      sbgd: "50000", // 设备高度
      smbj: "100000", // 扫描半径
    },
    {
      id: 206,
      ptlx: "无人车",
      ptmc: "ZHC-015",
      txfs: "ADSB",
      cjsj: "2024/4/10",
      ptbh: "2252-3232",
      // qspl: "22MHz",
      // zzpl: "55MHz",
      // zslmd: "52dB",
      // jhfs: "单极化",
      // txzy: "30dbm",
      sbjd: "30", // 设备经度
      sbwd: "100", // 设备纬度
      sbgd: "50000", // 设备高度
      // smbj: "100000",// 扫描半径
      dbbj: "10000", // 底部半径
      dbbj1: "0", // 顶部半径
    },
    {
      id: 207,
      ptlx: "无人车",
      ptmc: "ZHC-016",
      txfs: "ADSB",
      cjsj: "2024/4/10",
      ptbh: "2252-3233",
      // qspl: "22MHz",
      // zzpl: "55MHz",
      // zslmd: "52dB",
      // jhfs: "单极化",
      // txzy: "30dbm",
      sbjd: "30", // 设备经度
      sbwd: "100", // 设备纬度
      sbgd: "50000", // 设备高度
      // smbj: "100000",// 扫描半径
      dbbj: "10000", // 底部半径
      dbbj1: "0", // 顶部半径
    },
    {
      id: 208,
      ptlx: "无人车",
      ptmc: "ZHC-017",
      txfs: "ADSB",
      cjsj: "2024/4/10",
      ptbh: "2252-3234",
      // qspl: "22MHz",
      // zzpl: "55MHz",
      // zslmd: "52dB",
      // jhfs: "单极化",
      // txzy: "30dbm",
      sbjd: "30", // 设备经度
      sbwd: "100", // 设备纬度
      sbgd: "50000", // 设备高度
      // smbj: "100000",// 扫描半径
      dbbj: "10000", // 底部半径
      dbbj1: "0", // 顶部半径
    },
    {
      id: 209,
      ptlx: "弹",
      ptmc: "ZHC-018",
      txfs: "ADSB",
      cjsj: "2024/4/10",
      ptbh: "2252-3235",
      // qspl: "22MHz",
      // zzpl: "55MHz",
      // zslmd: "52dB",
      // jhfs: "单极化",
      // txzy: "30dbm",
      sbjd: "30", // 设备经度
      sbwd: "100", // 设备纬度
      sbgd: "50000", // 设备高度
      smbj: "100000", // 扫描半径
    },
  ],
});
let id = 1000;
let currentNode = ref({});
let currentId = ref("");
let currentOp = ref("");
let formList = ref({});
let lxghList = ref([]);
let tableCheckList = ref([]);
let activeName = ref("first");
let multipleTableRef = ref();

let isShow = ref(false);
let sbjdPlainLineVisb = ref(false);
let sbjdEditVisb = ref(false);
let sbjdListVisb = ref(true);
const yuList = reactive([
  {
    value: 0,
    label: "区域1",
  },
  {
    value: 1,
    label: "区域2",
  },
]);
let Chart = ref(null);
let Chart1 = ref(null);
let Chart2 = ref(null);

let myChart = null;
let myChart1 = null;
let myChart2 = null;
// 无人平台类型饼图
let Option = {
  title: {
    text: "无人平台类型饼状图",
    x: "center",
  },
  legend: {
    orient: "vertical",
    x: "250px",
    y: "100px",
    data: ["无人机", "无人车", "无人艇", "弹"],
  },
  calculable: true,
  series: [
    {
      name: "无人平台类型",
      type: "pie",
      radius: "65%",
      center: ["45%", "60%"],
      data: [
        { value: 335, name: "无人机" },
        { value: 310, name: "无人车" },
        { value: 234, name: "无人艇" },
        { value: 135, name: "弹" },
      ],
      label: {
        show: true,
        position: "inside",
      },
    },
  ],
};
//  测控节点类型统计
let Option1 = {
  title: {
    text: "测控节点类型统计",
    x: "center",
  },
  tooltip: {
    trigger: "item",
    formatter: "{a} <br/>{b} : {c} ({d}%)",
  },
  legend: {
    orient: "vertical",
    x: "260px",
    y: "100px",
    data: ["地基", "空基", "天基"],
  },
  calculable: true,
  series: [
    {
      name: "访问来源",
      center: ["45%", "60%"],
      type: "pie",
      radius: ["50%", "70%"],
      itemStyle: {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
        },
        emphasis: {
          label: {
            show: true,
            position: "center",
            textStyle: {
              fontSize: "30",
              fontWeight: "bold",
            },
          },
        },
      },
      data: [
        { value: 234, name: "天基" },
        { value: 310, name: "空基" },
        { value: 335, name: "地基" },
      ],
    },
  ],
};
//  设备测控能力范围
let Option2 = {
  title: {
    x: "center",
    text: "设备测控能力范围",
  },
  tooltip: {
    // trigger: 'axis',
    trigger: "item",
  },
  polar: [
    {
      indicator: [
        { text: "无人机", max: 6000 },
        { text: "无人车", max: 16000 },
        { text: "无人艇", max: 30000 },
        { text: "弹", max: 38000 },
        { text: "其他", max: 52000 },
      ],
      center: ["45%", 150],
      radius: 70,
    },
  ],
  calculable: true,
  series: [
    {
      name: "测控范围",
      type: "radar",
      itemStyle: { normal: { areaStyle: { type: "default" } } },
      data: [
        {
          value: [4300, 10000, 28000, 30000, 5000],
          name: "数量",
        },
      ],
    },
  ],
};
onMounted(() => {
  nextTick(() => {
    myChart1 = echarts.init(Chart1.value);
    myChart1.setOption(Option1, true);
  });
  nextTick(() => {
    myChart2 = echarts.init(Chart2.value);
    myChart2.setOption(Option2, true);
  });
  nextTick(() => {
    myChart = echarts.init(Chart.value);
    myChart.setOption(Option, true);
  });
  if (sessionStorage.getItem("jcsj")) {
    dataSource.value = JSON.parse(sessionStorage.getItem("jcsj"));
  }
  currentNode.value = dataSource.value[0].children[0].info;
});
onUnmounted(() => {
  window.EVGISMAP("removeAllEntityByGroup", { group: "EVMAPGROUP" });
  Viewer.entities.removeAll();
});
// 表格中某一行数据查看(位置)
const handlePosition = (row) => {
  isShow.value = true;
  setTimeout(() => {
    Viewer.entities.removeAll();
    window.EVGISMAP("removeAllEntityByGroup", { group: "EVMAPGROUP" });
    drawMap({
      type: row["ptlx"],
      position: [+row["sbwd"], +row["sbjd"], +row["sbgd"]],
      id: row["ptlx"] + "_" + row.id,
      name: row.ptmc,
      bottomRadius: Number(row["dbbj"] || 0),
      topRadius: Number(row["dbbj1"] || 0),
      radius: Number(row["smbj"] || 0),
    });
  }, 1000);
};
// 表格点击事件
const nodeClickEmit = (data) => {
  //  debugger
  // sbjdListVisb.value = true
  // currentId.value = data.id
  // currentNode.value = data.info
  // window.EVGISMAP('removeAllEntityByGroup', { group: 'EVMAPGROUP' })
  // Viewer.entities.removeAll()
  // if (data.lxgh) {
  //   sbjdPlainLineVisb.value = true
  //   lxghList.value = data.lxgh.map((item, i) => ({ id: 'lxgh_' + i, ...item }))
  // } else {
  //   sbjdPlainLineVisb.value = false
  // }
};
const dataSource = ref([
  {
    id: 1,
    label: "全部",
    type: "全部",
    isParent: true,

    info: {
      无人平台名称: "",
      平台编号: "",
      平台类型: "",
      通信方式: "",
      通信距离: "",
      最大载重量: "",
      尺寸: "",
      生产厂家: "",
      生产年份: "",
    },
    children: [
      {
        id: 11,
        label: "无人机",
        info: {
          无人平台名称: "xx无人机",
          平台编号: "552-235-hj210",
          平台类型: "无人机",
          通信方式: "ADSB",
          通信距离: "2654KM",
          最大载重量: "1120kg",
          尺寸: "4.5m*6.5m",
          生产厂家: "xxx",
          生产年份: "2024/04/10",
        },
      },
      {
        id: 12,
        label: "无人车",
        info: {
          无人平台名称: "xx无人车",
          平台编号: "552-235-hj210",
          平台类型: "无人车",
          通信方式: "ADSB",
          通信距离: "2654KM",
          最大载重量: "1120kg",
          尺寸: "4.5m*6.5m",
          生产厂家: "xxx",
          生产年份: "2024/04/10",
        },
      },
      {
        id: 13,
        label: "无人船",
        info: {
          无人平台名称: "xx无人船",
          平台编号: "552-235-hj210",
          平台类型: "无人船",
          通信方式: "ADSB",
          通信距离: "2654KM",
          最大载重量: "1120kg",
          尺寸: "4.5m*6.5m",
          生产厂家: "xxx",
          生产年份: "2024/04/10",
        },
      },
      {
        id: 14,
        label: "弹",
        info: {
          无人平台名称: "xx弹",
          平台编号: "552-235-hj210",
          平台类型: "弹",
          通信方式: "ADSB",
          通信距离: "2654KM",
          最大载重量: "1120kg",
          尺寸: "4.5m*6.5m",
          生产厂家: "xxx",
          生产年份: "2024/04/10",
        },
      },
    ],
  },
]);

let gjTreeData = ref([
  {
    id: 1,
    label: "路线轨迹1",
    lx: "[[100,30,0],[110,30,0],[120,30,0]]",
    cn: false,
  },
  {
    id: 2,
    label: "路线轨迹2",
    lx: "[[110,30,0],[110,40,0],[120,35,0]]",
    cn: false,
  },
  {
    id: 3,
    label: "路线轨迹3",
    lx: "[[100,35,0],[125,30,0],[115,40,0]]",
    cn: false,
  },
]);
const drawLineRow = async (row) => {
  const ps = await drawLine();
  var positions = [];
  ps.forEach((item) => {
    const { longitude, latitude, height } = Cesium.Ellipsoid.WGS84.cartesianToCartographic(item);
    positions.push([Cesium.Math.toDegrees(longitude).toFixed(6), Cesium.Math.toDegrees(latitude).toFixed(6), height]);
  });
  row.lx = JSON.stringify(positions);
  handleSelectionChange(tableCheckList.value);
};
const handleLxghNodeClick = (data) => {
  window.EVGISMAP("flyTo", {
    center: JSON.parse(data.lx)[0],
  });
};
const lxghAdd = () => {
  lxghList.value.push({
    id: "lxgh_" + lxghList.value.length,
    mc: "",
    lx: [],
    cn: false,
    isEdit: true,
  });
};
const saveGhlxData = () => {
  dataSource.value.forEach((item) => {
    item.children.forEach((it) => {
      if (it.id === currentId.value) {
        it.lxgh.length = 0;
        lxghList.value.forEach((its) => {
          it.lxgh.push({ mc: its.mc, lx: its.lx, cn: its.cn });
        });
      }
    });
  });
  sbjdPlainLineVisb.value = false;
};

const handleLxghtoMain = (row) => {
  lxghList.value.forEach((item) => {
    item.cn = false;
    if (row.id === item.id) {
      item.cn = true;
    }
  });
};
const handleSelectionChange = (data) => {
  window.EVGISMAP("removeAllEntityByGroup", { group: "EVMAPGROUP" });
  Viewer.entities.removeAll();
  tableCheckList.value = data;
  data.forEach((item) => {
    drawMap({
      type: "路线规划",
      position: JSON.parse(item.lx),
      id: item.id,
      name: item.mc,
    });
  });
};
// 轨迹显隐控制
let showGjList = [];
const showGj = (data) => {
  window.EVGISMAP("removeAllEntityByGroup", { group: "EVMAPGROUP" });
  Viewer.entities.removeAll();
  let count = 0;
  showGjList.forEach((el) => {
    if (el == data.id) {
      count++;
    }
  });
  if (count == 0) {
    drawMap({
      type: "路线规划",
      position: JSON.parse(data.lx),
      id: data.id,
      name: data.label,
    });
    showGjList.push(data.id);
    showGjList.forEach((el, index) => {
      if (el !== data.id) {
        showGjList.splice(index, 1);
      }
    });
  } else {
    showGjList.forEach((el, index) => {
      if (el == data.id) {
        showGjList.splice(index, 1);
      }
    });
  }
};
// 轨迹删除
const deleteGj = (data) => {
  gjTreeData.value.forEach((item, index) => {
    if (item.id == data.id) {
      gjTreeData.value.splice(index, 1);
    }
  });
};
const treeCheck = (nodes, checks) => {
  Viewer.entities.removeAll();
  window.EVGISMAP("removeAllEntityByGroup", { group: "EVMAPGROUP" });
  checks.checkedNodes.forEach((item) => {
    if (!item.type) {
      drawMap({
        type: item.info["设备类型"],
        position: [+item.info["设备经度"], +item.info["设备纬度"], +item.info["设备高度"]],
        id: item.info["设备类型"] + "_" + item.id,
        name: item.label,
        bottomRadius: Number(item.info["底部半径"] || 0),
        topRadius: Number(item.info["顶部半径"] || 0),
        radius: Number(item.info["扫描半径"] || 0),
      });
    }
  });
};
const handleNodeClick = (data) => {
  // debugger
  if (data.isParent) return;
  ClickTree.value = data.label;
  sbjdListVisb.value = true;
  currentId.value = data.id;
  currentNode.value = data.info;
  window.EVGISMAP("removeAllEntityByGroup", { group: "EVMAPGROUP" });
  Viewer.entities.removeAll();
  if (data.lxgh) {
    sbjdPlainLineVisb.value = true;
    lxghList.value = data.lxgh.map((item, i) => ({ id: "lxgh_" + i, ...item }));
  } else {
    sbjdPlainLineVisb.value = false;
  }
};
const handleAddClick = (data) => {
  currentOp.value = "add";
  sbjdEditVisb.value = true;
  formList.value = data.info;
};
const handleEditClick = (data) => {
  currentOp.value = "edit";
  sbjdEditVisb.value = true;
  currentId.value = data.id;
  currentNode.value = data.info;
  formList.value = { ...currentNode.value };
};
const handleLocationClick = (data) => {
  window.EVGISMAP("flyTo", {
    center: [+data.info["设备经度"], +data.info["设备纬度"], +data.info["设备高度"]],
  });
};
const handleRemoveClick = (data) => {
  dataSource.value.forEach((item) => {
    const i = item.children.findIndex((it) => item.id === data.id);
    item.children.splice(i, 1);
  });
};
const handleFormSubmit = () => {
  sbjdEditVisb.value = false;
  if (currentOp.value === "add") {
    currentNode.value = { ...formList.value };
    dataSource.value.forEach((item) => {
      if (item.type === formList.value["设备类型"]) {
        item.children.push({
          id: ++id,
          label: formList.value["设备名称"],
          info: { ...formList.value },
        });
      }
    });
  } else if (currentOp.value === "edit") {
    currentNode.value = { ...formList.value };
    dataSource.value.forEach((item) => {
      item.children.forEach((it) => {
        if (it.id === currentId.value) {
          it.info = currentNode.value;
        }
      });
    });
  } else if (currentOp.value === "addGj") {
    // console.log(formList.value)
    if (formList.value.label) {
      formList.value.id = formList.value.label + "22";
      gjTreeData.value.push(formList.value);
    }
  }
  sessionStorage.setItem("jcsj", JSON.stringify(dataSource.value));
};
const handleCloseClick = (item) => {
  switch (item) {
    case "edit":
      sbjdEditVisb.value = false;
      break;
    case "list":
      sbjdListVisb.value = false;
      break;
    case "plainLine":
      sbjdPlainLineVisb.value = false;
  }
};

let tableOptions = [
  {
    label: "任务名称",
    prop: "aaa",
  },
  {
    label: "任务类型",
    prop: "bbb",
  },
  {
    label: "任务状态",
    prop: "status",
  },
  {
    label: "执行时间",
    prop: "ccc",
  },
];
let tableData = reactive([
  {
    aaa: "任务名称1",
    bbb: "单目标连续测控",
    ccc: "2024/4/16",
    status: "执行中",
  },
  {
    aaa: "任务名称2",
    bbb: "多目标协调测控",
    ccc: "2023/4/25",
    status: "未开始",
  },
]);
let tableData1 = reactive([
  {
    aaa: "任务名称1",
    bbb: "单目标连续测控",
    ccc: "2023/12/3",
    status: "已完成",
  },
  {
    aaa: "任务名称2",
    bbb: "多目标协调测控",
    ccc: "2023/12/5",
    status: "已完成",
  },
]);

const drawLine = () => {
  return new Promise((resolve) => {
    var drawHandleTool = new Cesium.EV_DrawHandler(window.Viewer);
    if (drawHandleTool.stop) {
      drawHandleTool.stop = false;
    }
    drawHandleTool.draw(
      {
        material: Cesium.Color.RED,
        width: 1,
      },
      function (entity) {
        resolve(entity.positions);
      },
      Cesium.EV_DrawType.POLYLINE,
      true
    );
  });
};
const drawMap = (option) => {
  switch (option.type) {
    case "天基":
      window.EVGISMAP("drawPointByAttr", {
        id: option.id + "_point",
        img: "./wx.png",
        position: option.position, //[120, 30,10000000],option
        name: option.name,
        color: "#ffff00",
        outlineColor: "green",
        outlineWidth: "1",
        textColor: "red",
        textOutlineColor: "black",
        textScale: 1,
        scale: 1,
        direction: 90,
      });

      var draw = new Cesium.EV_DrawGeometry(window.Viewer);
      var cylinder3 = draw.drawCylinder({
        id: option.id + "_cyLine",
        position: Cesium.Cartesian3.fromDegrees(option.position[0], option.position[1]), //几何体中心坐标位置
        bottomRadius: +option.bottomRadius,
        topRadius: +option.topRadius,
        length: option.position[2],
        material: Cesium.Color.RED.withAlpha(0.3),
      });
      break;
    case "地基":
      window.EVGISMAP("drawPointByAttr", {
        id: option.id + "_point",
        img: "./dmz1.png",
        position: option.position,
        name: option.name,
        color: "#ffff00",
        outlineColor: "green",
        outlineWidth: "1",
        textColor: "red",
        textOutlineColor: "black",
        textScale: 1,
        scale: 1,
        direction: 360,
      });
      window.EVGISMAP("drawCircleByAttr", {
        id: option.id + "_circle",
        color: "rgba(255,0,0,0.3)",
        position: option.position,
        radius: option.radius,
      });
      break;
    case "空基":
      window.EVGISMAP("drawPointByAttr", {
        id: option.id + "_point",
        img: "./fj.png",
        position: option.position,
        name: option.name,
        color: "#ffff00",
        outlineColor: "green",
        outlineWidth: "1",
        textColor: "red",
        textOutlineColor: "black",
        textScale: 1,
        scale: 1,
        direction: 90,
      });
    case "路线规划":
      window.EVGISMAP("drawPointByAttr", {
        id: option.id + "_point",
        img: "./HQ17.png",
        position: option.position[0],
        name: option.name,
        color: "#ffff00",
        outlineColor: "green",
        outlineWidth: "1",
        textColor: "red",
        textOutlineColor: "black",
        textScale: 1,
        scale: 1,
        direction: 360,
      });
      window.EVGISMAP("drawPolylineByAttr", {
        id: option.id + "_line",
        position: option.position,
        outlineColor: "red",
        outlineWidth: 1,
      });
      break;
  }
};
let showGjBox = ref(false);
// 轨迹配置
const gjSet = () => {
  showGjBox.value = !showGjBox.value;
};
// 新增轨迹
const addGjClick = () => {
  currentOp.value = "addGj";
  sbjdEditVisb.value = true;
  formList.value = { label: "" };
};

const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
  if (row.status && columnIndex == 3) {
    if (row.status == "执行中") {
      return { color: "#03a9f4" };
    }
  }
};

const router = useRouter();
// 返回上一级
const goBack = () => {
  router.push({
    path: "/home/<USER>",
    replace: true,
  });
};
</script>

<style scoped lang="less">
@import "@/assets/style/theme/style.less";

.title-font {
  /* color: blue; */
  font-size: 18px;
  font-weight: bolder;
  color: #285472;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.gjSet {
  position: absolute;
  top: 80px;
  left: 21%;
  width: 150px;
  height: 40px;
  z-index: 5;
  background-color: #0080da;
  color: #fff;
}

.gjSetBox {
  position: absolute;
  top: 130px;
  left: 21%;
  z-index: 5;
  background-color: #fff;
  border-radius: 5px;
  width: 150px;
  border: 0px solid #80808047;
  padding-bottom: 10px;

  > p {
    background-color: #37a0ea;
    height: 40px;
    border-radius: 5px 5px 0px 0px;
    color: #fff;
    text-align: center;
    line-height: 46px;
    font-size: 16px;
    font-weight: 800;
  }

  > div {
    box-sizing: border-box;
    padding-left: 8px;
    color: #285472;
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
}

.custom-tree-node1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
}

.el-form-item__label {
  color: aqua !important;
}

.is-icon {
  margin-left: 2px;
}

.closePopBtn {
  right: 30px;
  position: absolute;
  cursor: pointer;
}

.el-form-item__label {
  color: aqua !important;
}

:deep(.custom-popup-mian .content1) {
  padding: 0 !important;
}

:deep(.el-tree-node__content > .el-tree-node__expand-icon) {
  padding: 0 !important;
  width: 0 !important;
}

.leftNodeTree {
  overflow-y: auto;
  height: 100%;
  width: 100%;
}

.leftNodeTree :deep(.el-tree-node__content) {
  color: rgb(48, 158, 255);
  font-size: 17px;
  font-weight: bolder;
}

.leftNodeTree :deep(.el-tree-node__children .el-tree-node__content) {
  color: #285472;
  font-size: 15px;
  font-weight: normal;
}

/* 气泡确认框样式  */
/* :deep(.el-popconfirm) {
    background-color: #fff;
    border: 1px solid #37a0ea;
  }

  :deep(.el-popper.is-light) {
    background-color: #fff !important;
  } */

:deep(.el-tabs__header) {
  margin: 0;

  .el-tabs__item {
    font-size: 17px;
    font-weight: bolder;
  }

  .el-tabs__nav-wrap::after {
    background-color: #03a9f46e;
    height: 1px;
  }

  .is-active {
    border-bottom: 5px solid #409eff;
  }
}

:deep(.el-tabs__content) {
  border: 1px solid #b0c6cf;
  border-top: none;
  padding-top: 20px;
  height: calc(100% - 66px);
}

:deep(.el-tabs__content .el-form-item) {
  display: flex;

  > .el-form-item__label {
    color: #285472;
    font-size: 15px;
    font-weight: bolder;
  }
}

// :deep(.elPager) {
//   background-color: #bec1c1;
// }

.contentDIV {
  right: 10px;
  top: 80px;
  width: 23%;
  height: 90%;
  position: absolute;
  z-index: 1988;

  .particularsDIV,
  .rwglDIV {
    height: 100%;
    width: 100%;

    :deep(.el-form) {
      overflow: auto;
      height: 792px;
    }
  }
}
.headerDiv {
  .header-title {
    display: flex;
    padding: 10px 0;
    align-items: center;
    width: 100%;
  }
  & > span {
    margin-right: 10px;
    background-image: @popupTitleBg;
    background-size: 100% 100%;
    width: 19px;
    height: 13px;
  }
}
.headerLine {
  display: flex;
  width: 100%;

  .headerLine-left {
    width: 32px;
    height: 1px;
    /* // background: url("../../public/images/bgBorder/line_06.png"); */
    background: @line_06;
  }

  .headerLine-md {
    flex: 1;
    height: 1px;
    /* // background: url("../../public/images/bgBorder/line_07.png"); */
    background: @line_07;
  }

  .headerLine-right {
    width: 8px;
    height: 8px;
    transform: translateY(-7px);
    /* // background: url("../../public/images/bgBorder/line_03.png"); */
    background: @line_03;
  }
}
</style>
