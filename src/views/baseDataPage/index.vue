<template>
  <!-- 左侧分类树 -->
  <customPopup left="0.5%" top="80px" width="16%" height="90%">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">设备节点列表</span>
      <!-- <span style="position: absolute; right: 30px; color: #309eff; font-weight: bold; font-size: 17px">资源域一</span> -->
    </template>
    <template #content>
      <div style="display: flex; margin-bottom: 5px">
        <el-input v-model="stateData.leftNodeTreeInput"></el-input>
        <el-button type="primary" icon="Search" @click="getAreaGeneralTree(stateData.areaId)" class="transparentBtn"
          style="margin-left: 15px">
          搜索
        </el-button>
      </div>
      <!-- show-checkbox  -->
      <div class="leftNodeTree">
        <!-- 只有数据的树（无法操作） -->
        <el-tree :data="stateData.treeData" ref="treeRef" node-key="id" default-expand-all :expand-on-click-node="false"
          :highlight-current="true" @node-click="handleNodeClick">
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <span :style="`color: ${fontColor}`">
                <!-- <el-icon ><Collection /></el-icon> -->
                <span v-if="!data.tableComment" class="treeParent"></span>
                {{ data.tableComment || data.label }}
              </span>
            </span>
          </template>
        </el-tree>
      </div>
    </template>
  </customPopup>

  <!-- 右侧详情界面 -->
  <customPopup right="0.5%" top="80px" width="21%" height="90%" headType="2" v-if="sbjdListVisb">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">详情</span>
      <!-- <span class="closePopBtn" @click="handleCloseClick('list')">
        <el-icon>
          <Close />
        </el-icon>
      </span> -->
    </template>
    <template #content>
      <div style="height: 100%; width: 100%; overflow: auto">
        <el-row :gutter="0" v-for="item in stateData.tableColumn" :key="item.prop"
          style="height: 40px; line-height: 40px" v-show="!stateData.unShowList.includes(item.prop)">
          <el-col :span="2"></el-col>
          <el-col v-if="item.prop == 'fileId'" :span="10"
            style="color: #37a0ea; font-size: 14px; line-height: 40px; text-align: center">
            {{ '文件名称' + ":" }}
          </el-col>
          <el-col v-else :span="10" style="color: #37a0ea; font-size: 14px; line-height: 40px; text-align: center">
            {{ item.label + ":" }}
          </el-col>
          <el-col :span="10" v-if="item.prop.includes('time')"
            :style="`color: ${fontColor};font-size: 14px;line-height: 40px;`">
            {{ timeFormat(currentNode[item.prop]) }}
          </el-col>
          <el-col :span="12" v-else-if="stateData.dictTypeList[item.prop]"
            :style="`color: ${fontColor};font-size: 14px;line-height: 40px;`">
            <!-- <span v-if="item.prop=='businessType'" v-for=" i in '1,2,3,4'.split(',')"> -->
            <span v-if="item.prop=='businessType'">
              <!-- {{ stateData.dictTypeList[item.prop][i] }} -->
              {{ dictValue("businessType",currentNode[item.prop]) }}
            </span>
            <span v-else-if="item.prop=='frequency'">
              {{ dictValue("frequency",currentNode[item.prop]) }}
            </span>
            <span v-else-if="item.prop=='workSystem'">
              {{ dictValue("workSystem",currentNode[item.prop]) }}
            </span>
            <span v-else>
              {{ stateData.dictTypeList[item.prop][currentNode[item.prop]] }}
            </span>
          </el-col>
          <el-col v-else-if="item.prop == 'fileId'" :style="`color: ${fontColor};font-size: 14px;line-height: 40px;`">
            {{ currentNode[item.fileName] }}
          </el-col>
          <el-col :span="12" v-else :style="`color: ${fontColor};font-size: 14px;line-height: 40px;`">
            {{ currentNode[item.prop] }}
          </el-col>
        </el-row>
      </div>
      <!-- <el-tabs v-model="activeName" style="height: 100%">
        <el-tab-pane label="设备节点详情" name="first">
          <el-form :inline="true" :model="{}" label-position="right" label-width="150px">
            <template v-for="item in stateData.tableColumn" :key="item.prop">
              <el-form-item :label="item.label + ':'">
                <span>{{ currentNode["prop"] }}</span>
              </el-form-item>
            </template>
  </el-form>
  </el-tab-pane>

  </el-tabs> -->
    </template>
  </customPopup>

  <!-- 中间表格 -->
  <customPopup left="17%" top="80px" width="61%" height="90%">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">{{ stateData.pageType == 1 ? '资源' : '目标' }}域详情--{{ stateData.areaName }} </span>

    </template>
    <template #content>
      <div style="height: 29%; display: flex; margin-top: 20px">
        <el-button style="position: absolute; top: 10px; right: 28px" @click="goBack">返回</el-button>
        <!-- <div style="flex: 1">
          <div ref="Chart" style="height: 100%; width: 100%"></div>
        </div> -->
        <!-- <div style="flex: 1">
          <div ref="Chart1" style="height: 100%; width: 100%"></div>
        </div> -->
        <div style="flex: 1">
          <div ref="Chart2" style="height: 100%; width: 100%"></div>
        </div>
        <div style="flex: 1">
          <div ref="Chart3" style="height: 100%; width: 100%"></div>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 5px">
        <el-input v-model="stateData.listInput"></el-input>
        <el-button type="primary" icon="Search" @click="getAreaGeneralData(currentTreeNode)" class="transparentBtn"
          style="margin-left: 15px">
          搜索
        </el-button>
        <el-button type="primary" icon="Position" @click="handlePosition" v-show="stateData.pageType == 1"
          class="transparentBtn">
          定位
        </el-button>
        <el-button type="primary" icon="DocumentAdd" @click="handleAdd" class="transparentBtn">新增</el-button>
      </div>
      <div style="height: 60%; margin-top: 10px; border: 0px solid #3691d291; padding: 5px">
        <customTable :tableData="stateData.dataInfoList" :tableColumn="stateData.tableColumn" :total="pageInfo.total"
          :currentPage="pageInfo.page" :pageSize="pageInfo.size" @nodeClickEmit="nodeClickEmit"
          @currentPageEmit="currentPageEmit" @pageSizeEmit="pageSizeEmit" @handleEditEmit="handleEditEmit"
          @handleDeleteEmit="handleDeleteEmit"></customTable>
      </div>
    </template>
  </customPopup>

  <!-- 图上资源 -->
  <customPopup v-if="isShow" left="0.5%" top="80px" width="99%" height="90%">
    <template #header>
      <div style="position: absolute; right: 2%; top: 3%; z-index: 9">
        <el-button @click="isShow = false; stateData.allPosition = false">关闭</el-button>
      </div>
    </template>
    <template #content>
      <CesiumBox></CesiumBox>
      <customPopup left="20px" top="25px" width="20%" height="30%">
        <template #header>
          <span class="title-font">{{ stateData.gisName }}</span>
        </template>
        <template #content>
          <div style="height: 100%; width: 100%; overflow: auto">
            <el-row :gutter="0"
              v-for="(item, index) in stateData.allPosition ? stateData.allPositionList : stateData.dataInfoList"
              :key="item.id"> <el-checkbox :checked="true" @change="(val) => {
                showEntity(val, item, index);
              }
                ">
                {{ item.name }}
              </el-checkbox>
            </el-row>
          </div>
        </template>
      </customPopup>
    </template>
  </customPopup>

  <el-dialog :title="stateData.targetForm.id ? '资源-编辑' : '资源-新增'" width="600px" v-model="stateData.targetDialogShow"
    :close-on-click-modal="false" @close="targetDialogClose">
    <div style="width: 100%; height: 50vh; overflow: auto">
      <el-form ref="targetFormRef" :model="stateData.targetForm" :rules="stateData.targetRules" label-width="190px">
        <template v-for="(item, index) in stateData.tableColumn">
          <el-form-item label="轨迹文件" prop="fileId" v-if="item.prop == 'fileId'">
            <el-upload action="#" :auto-upload="false" :limit="1" v-model:file-list="stateData.fileList"
              :show-file-list="false">
              <template #trigger>
                <el-button type="primary">选择文件</el-button>
                <!-- <el-button type="primary" @click.stop="uploadFile">上传</el-button> -->
              </template>
              <template #tip>
                <div class="el-upload__tip" :style="`color: ${fontColor}`">
                  {{ stateData.fileList[0]?.name || stateData.targetForm.fileName || stateData.targetForm.fileId }}
                </div>
              </template>
            </el-upload>
          </el-form-item>
          <el-form-item :label="item.label" :prop="item.prop" :key="item.prop" :rules="[
            {
              required: item.prop == 'id'?false:true,
              message: '请填写' + item.label,
              trigger: 'blur',
              type: item.prop == 'businessType' || item.prop == 'workSystem' || item.prop == 'frequency' ?'array':item.formType == 'string' ? 'string' : 'number',
            },
          ]" v-else-if="!stateData.unShowList.includes(item.prop)">
            <el-input style="width: 80%" v-model="stateData.targetForm[item.prop]" disabled placeholder="主键由系统生成"
              v-if="item.prop == 'id'">
            </el-input>
            <el-input :placeholder="`请填写${item.label}`" style="width: 80%" :maxLength="item.maxLength"
              v-model="stateData.targetForm[item.prop]" :disabled="item.prop == 'id'"
              v-else-if="item.formType == 'string'">
            </el-input>
            <!-- <el-select v-model="stateData.targetForm[item.prop]" :placeholder="`请选择${item.label}`" clearable
              v-else-if="item.formType == 'sysdic'" style="width: 80%">
              <el-option v-for="(item, key, index) in stateData.dictTypeList[item.prop] || item.SysdicName" :key="key"
                :label="item" :value="Number(key)"></el-option>
            </el-select> -->
            <!--  -->
            <el-select v-model="stateData.targetForm[item.prop]" :placeholder="`请选择${item.label}`" clearable
              v-else-if="item.formType == 'sysdic' && item.prop != 'businessType' && item.prop != 'workSystem' && item.prop != 'frequency'"
              style="width: 80%">
              <el-option v-for="(item, key, index) in stateData.dictTypeList[item.prop] || item.SysdicName" :key="key"
                :label="item" :value="Number(key)"></el-option>
            </el-select>
            <!-- 添加业务类型处理 -->
            <el-select v-model="stateData.targetForm[item.prop]" :placeholder="`请选择${item.label}`" clearable
              v-else-if="item.formType == 'sysdic' && item.prop=='businessType'" multiple style="width: 80%">
              <el-option v-for="(item, key, index) in stateData.dictTypeList[item.prop] || item.SysdicName" :key="key"
                :label="item" :value="Number(key)"></el-option>
            </el-select>

            <!-- 添加工作体制处理 -->
            <el-select v-model="stateData.targetForm[item.prop]" :placeholder="`请选择${item.label}`" clearable
              v-else-if="item.formType == 'sysdic' && item.prop=='workSystem'" multiple style="width: 80%">
              <el-option v-for="(item, key, index) in stateData.dictTypeList[item.prop] || item.SysdicName" :key="key"
                :label="item" :value="Number(key)"></el-option>
            </el-select>

            <!-- 添加频段处理 -->
            <el-select v-model="stateData.targetForm[item.prop]" :placeholder="`请选择${item.label}`" clearable
              v-else-if="item.formType == 'sysdic' && item.prop=='frequency'" multiple style="width: 80%">
              <el-option v-for="(item, key, index) in stateData.dictTypeList[item.prop] || item.SysdicName" :key="key"
                :label="item" :value="Number(key)"></el-option>
            </el-select>

            <el-input-number v-else-if="item.formType == 'number'" style="width: 80%" :placeholder="`请填写${item.label}`"
              v-model="stateData.targetForm[item.prop]" :controls="false" />
          </el-form-item>
        </template>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button size="small" @click="targetDialogClose">取 消</el-button>
        <el-button size="small" type="primary" @click="targetDialogSubmit(targetFormRef)" :loading="stateData.loading">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script setup>
  // import CesiumBox from "@/components/evmap/CesiumBox2D.vue";
  import CesiumBox from "@/components/evmap/CesiumBox.vue";
  import customPopup from "@/components/customPopup.vue";
  import customPopup1 from "@/components/customPopup1.vue";
  import { reactive, ref, onMounted, onUnmounted, nextTick, onBeforeUnmount, watch } from "vue";
  import customTable from "@/components/customTable.vue";
  import { useRouter } from "vue-router";
  import * as dataApi from "@/service/API/system/areaManage.js";
  import { ElMessage } from "element-plus";
  import { initSatllite, satlliteTest } from "@/utils/mySatellite.js";
  // import * as mapTool from "@/utils/mapTool.js";
  const dictValue = window.dictValue;

  const router = useRouter();
  let fontColor = window.$fontColor;
  const dataTypeList = ["地基", "空基", "天基", "无人机"];
  let targetFormRef = ref();
  const treeRef = ref(null);
  const pageInfo = reactive({
    page: 1,
    size: 10,
    total: 0,
  });
  const stateData = reactive({
    areaId: null, //域ID
    treeData: [], //左侧树数据
    currentDataTypeId: null, //当前选中的类型
    leftNodeTreeInput: "", //左侧查询关键字
    listInput: "", //查询关键字
    //id显示但不编辑
    unShowList: ["generalId", "", "operation", "updateTime", "createTime", "areaId"], //不展示的字段
    dictTypeList: {}, //字典
    fileList: [], //弹窗上传的文件列表
    loading: false, //等待状态
    dataInfoList: [], //表格数据（动态查询）
    tableColumn: [], //列表字段名（动态查询）
    targetForm: { id: null }, //资源表单
    targetDialogShow: false, //资源新增修改弹窗
    targetRules: {
      name: [{ required: true, message: "请输入名称", trigger: "blur" }],
    }, //资源表单校验规则
    gisName: "图上资源", //地图资源名称
    allPositionList: [], //全查域资源定位
    allPosition: false, //是否是全查域定位点进来的
    armyType: undefined,//
  });
  let id = 1000;
  let currentNode = ref({}); //左侧树当前选中节点数据
  let currentId = ref(""); //左侧树当前选中节点数据ID
  let formList = ref({});
  let isShow = ref(false);
  let sbjdListVisb = ref(true);
  let Chart = ref(null);
  let Chart1 = ref(null);
  let Chart2 = ref(null);
  let Chart3 = ref(null);

  watch(
    () => stateData.fileList,
    (val) => {
      uploadFile()
    }
  );
  //  设备测控能力范围
  let Option2 = {
    title: {
      x: "center",
      text: "设备测控能力范围",
      textStyle: {
        color: fontColor,
      },
    },
    tooltip: {
      // trigger: 'axis',
      trigger: "item",
    },
    polar: [
      {
        indicator: [
          { text: "无人机", max: 6000 },
          { text: "无人车", max: 16000 },
          { text: "无人艇", max: 30000 },
          { text: "弹", max: 38000 },
          { text: "其他", max: 52000 },
        ],
        center: ["45%", 150],
        radius: 70,
      },
    ],
    calculable: true,
    series: [
      {
        name: "测控范围",
        type: "radar",
        itemStyle: { normal: { areaStyle: { type: "default" } } },
        data: [
          {
            value: [4300, 10000, 28000, 30000, 5000],
            name: "数量",
          },
        ],
      },
    ],
  };

  onMounted(() => {
    stateData.areaId = router.currentRoute._value?.query?.id;
    stateData.areaName = router.currentRoute._value?.query?.areaName;
    stateData.pageType = router.currentRoute._value?.query?.pageType || 1;
    stateData.allPosition = router.currentRoute._value?.query?.allPosition || false;
    // stateData.armyType = router.currentRoute._value?.query?.armyType || null;
    if (stateData.allPosition) {
      allPositionFun();
      router.currentRoute._value.query.allPosition = false;
    }
    getAreaGeneralTree(stateData.areaId);

    topChart()
  });

  onBeforeUnmount(() => {
    // window.EVGISMAP && window.EVGISMAP("removeAllEntityByGroup", { group: "EVMAPGROUP" });
    mapTool.removeAll();
    // Viewer.entities.removeAll();
  });
  function topChart() {
    // getStatsType(); 
    getStatsLeaf();
    // getStatsType2();
    getStatsLeaf2();
  }
  // 时间格式转换
  function timeFormat(time) {
    if (time) {
      return echarts.format.formatTime("yyyy-MM-dd hh:mm:ss", time);
    } else {
      return "";
    }
  }
  function allPositionFun() {
    dataApi
      .pageEquipmentByType({
        areaId: stateData.areaId,
        category: 1,
        page: 1,
        size: 50,
      })
      .then((res) => {
        if (res.data.code == 200) {
          // ElMessage.error(res.data.message);
          stateData.allPositionList = res.data.data.records;
          stateData.gisName = stateData.areaName;
          isShow.value = true;
          setTimeout(() => {
            // window.EVGISMAP("removeAllEntityByGroup", { group: "EVMAPGROUP" });
            mapTool.removeAll();
            // Viewer.entities.removeAll();
            stateData.allPositionList.forEach((row, index) => {
              mapTool.drawMap({ ...row, equipmentId: row.equipmentId ? row.equipmentId : row.id });
            });
          }, 1000);
          // pageInfo.total = res.data.data.total;
        } else {
          ElMessage.error(res.data.message);
        }
      });
  }
  // 获取id分类查询基础数据分类树{areaId}
  function getAreaGeneralTree(areaId) {
    dataApi.getAreaGeneralTree({ areaId: areaId, keyword: stateData.leftNodeTreeInput || null }).then((res) => {
      if (res.data.code) {
        let data = res.data.data;
        let obj = {};
        data.forEach((item, index) => {
          !obj[item.dataType] &&
            (obj[item.dataType] = {
              label: item.dataTypeValue,
              dataType: item.dataType,
              id: "dataType" + item.dataType,
              children: [],
              isParent: true,
            });
          obj[item.dataType].children.push({
            ...item,
          });
          if (index == 0) {
            setTimeout(() => {
              treeRef.value.setCurrentKey(item.id);
            }, 500);
            handleNodeClick(item);
          }
        });
        stateData.treeData = [];

        for (const key in obj) {
          stateData.treeData.push(obj[key]);
        }
      }
    });
  }
  // 获取统计图1
  function getStatsType() {
    dataApi.getStatsType({ areaId: stateData.areaId, type: stateData.pageType }).then((res) => {
      if (res.data.code == 200) {
        let data = res.data.data;
        let option = {
          color: chartColors,
          title: {
            text: "类别比例",
            x: "center",
            textStyle: {
              color: fontColor,
            },
          },
          legend: {
            orient: "vertical",
            bottom: 0,
            right: 0,
            data: [],
            textStyle: {
              color: fontColor,
            },
          },
          tooltip: {
            trigger: "item",
          },
          calculable: true,
          series: [
            {
              name: "类别",
              type: "pie",
              radius: "65%",
              center: ["35%", "50%"],
              data: [],
              label: {
                position: "inside",
                // formatter: "{b}:{d}%",
                formatter: function (node) {
                  if (node.percent > 0) {
                    // return "{b}:{d}%"
                    return node.name + "：" + node.percent + '%';
                  } else {
                    return ''
                  }
                }
                // formatter: (data) => {
                //   if (data.value == 0) {
                //     return "";
                //   } else {
                //     return data.name + " " + data.value;
                //   }
                // },
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
            },
          ],
        };
        for (let key in data) {
          let val = data[key];
          option.legend.data.push(key);
          option.series[0].data.push({
            value: val,
            name: key,
          });
        }
        let myChart = echarts.init(Chart.value);
        myChart.setOption(option, true);
        myChart.resize();
      } else {
        ElMessage.error(res.data.messagea);
      }
    });
  }
  function getStatsType2() {
    dataApi.getStatsType({ areaId: stateData.areaId, type: stateData.pageType }).then((res) => {
      if (res.data.code == 200) {
        let data = res.data.data;
        let option = {
          color: chartColors,
          title: {
            text: "类别比例",
            x: "center",
            textStyle: {
              color: fontColor,
            },
          },
          tooltip: {
            trigger: "item",
          },
          xAxis: {
            type: "category",
            axisLine: window.axisLine,
            data: [],
            axisLabel: {
              color: fontColor,
              rotate: 45,
            },
          },
          yAxis: {
            type: "value",
            splitLine: window.splitLine,
            axisLine: window.axisLine,
            axisLabel: {
              color: fontColor,
            },
          },
          series: [
            {
              data: [],
              type: "bar",
              barMaxWidth: 30,
            },
          ],
        };
        for (let key in data) {
          let val = data[key];
          // option.legend.data.push(key);
          option.xAxis.data.push(key);
          option.series[0].data.push({
            value: val,
            name: key,
          });
        }
        let myChart = echarts.init(Chart1.value);
        myChart.setOption(option, true);
        myChart.resize();
      } else {
        ElMessage.error(res.data.messagea);
      }
    });
  }
  //获取统计图2
  function getStatsLeaf() {
    dataApi.getStatsLeaf({ areaId: stateData.areaId, type: stateData.pageType }).then((res) => {
      if (res.data.code == 200) {
        let data = res.data.data;
        let option = {
          color: chartColors,
          title: {
            text: "类型比例",
            x: "center",
            textStyle: {
              color: fontColor,
            },
          },
          legend: {
            orient: "vertical",
            // x: "60%",
            // y: "60%",
            bottom: 0,
            right: 0,
            data: [],
            textStyle: {
              color: fontColor,
            },
          },
          tooltip: {
            trigger: "item",
          },
          calculable: true,
          series: [
            {
              name: "类型",
              type: "pie",
              radius: "85%",
              center: ["35%", "50%"],
              data: [],
              // label: {
              //   show: true,
              //   position: "inside",
              //   formatter: (node) => {
              //     console.log(node);
              //     // if (node.value) {
              //     return node.name + "：" + node.value;
              //     // } else {
              //     //   return "";
              //     // }
              //   },
              //   //  "{a} <br/>{b} : {c} ({d}%)",
              // },
              label: {
                position: "inside",
                formatter: function (node) {
                  if (node.percent > 0) {
                    // return "{b}:{d}%"
                    return node.name + "：" + node.percent + '%';
                  } else {
                    return ''
                  }
                }
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
            },
          ],
        };
        for (let key in data) {
          let val = data[key];
          option.legend.data.push(key);
          option.series[0].data.push({
            value: val,
            name: key,
          });
        }
        let myChart = echarts.init(Chart2.value);
        myChart.setOption(option, true);
        myChart.resize();
      } else {
        ElMessage.error(res.data.messagea);
      }
    });
  }
  //获取统计图2
  function getStatsLeaf2() {
    dataApi.getStatsLeaf({ areaId: stateData.areaId, type: stateData.pageType }).then((res) => {
      if (res.data.code == 200) {
        let data = res.data.data;
        let option = {
          color: chartColors,
          title: {
            text: "类型数量",
            x: "center",
            textStyle: {
              color: fontColor,
            },
          },
          tooltip: {
            trigger: "item",
          },
          xAxis: {
            type: "category",
            axisLine: window.axisLine,
            data: [],
            axisLabel: {
              color: fontColor,
              rotate: 45,
            },
          },
          yAxis: {
            type: "value",
            splitLine: window.splitLine,
            axisLine: window.axisLine,
            axisLabel: {
              color: fontColor,
              barMaxWidth: 30,
            },
          },
          series: [
            {
              data: [],
              type: "bar",
              barWidth: 30,
            },
          ],
        };
        for (let key in data) {
          let val = data[key];
          // option.legend.data.push(key);
          option.xAxis.data.push(key);
          option.series[0].data.push({
            value: val,
            name: key,
          });
        }
        let myChart = echarts.init(Chart3.value);
        myChart.setOption(option, true);
        myChart.resize();
      } else {
        ElMessage.error(res.data.messagea);
      }
    });
  }

  // 表格编辑按钮事件
  function handleEditEmit(row) {
    stateData.targetForm = { ...row };
    // 处理2个多选问题
    stateData.targetForm.workSystem = stateData.targetForm.workSystem.split(',').map(map => Number(map))
    stateData.targetForm.businessType = stateData.targetForm.businessType.split(',').map(map => Number(map))
    stateData.targetForm.frequency = stateData.targetForm.frequency.split(',').map(map => Number(map))
    stateData.fileList = [];
    stateData.targetDialogShow = true;
  }
  // 表格删除按钮事件
  function handleDeleteEmit(row) {
    dataApi.removeTarget({ dataTypeId: currentTreeNode.id, dataId: row.id }).then((res) => {
      if (res.data.code == 200) {
        ElMessage.success("删除成功");
        topChart()
        handleNodeClick(currentTreeNode);
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }

  // 查看地图事件
  const handlePosition = () => {
    isShow.value = true;
    stateData.gisName = stateData.areaName + "-" + currentTreeNode.tableComment;
    setTimeout(() => {
      mapTool.removeAll();
      stateData.dataInfoList.forEach((row, index) => {
        mapTool.drawMap({ ...row, dataType: currentTreeNode.dataType, equipmentId: row.id });
      });
    }, 1000);
  };
  function drawSatellite(row, url) {
    // return;
    let txtUrl = AdminServerApi + "/file/" + url;
    fetch(txtUrl, { headers: { Authorization: sessionStorage.getItem("userToken") } }).then((res) =>
      res.text().then((text) => {
        const lines = text
          .split("\n")
          .map((t) => t && t.trim())
          .filter((it) => it);
        console.log("lines", lines);
        lines && initSatllite({ tleLine1: lines[1], tleLine2: lines[2], name: lines[0] }, row, "EVMAPGROUP");
      })
    );
  }
  // 新增事件
  function handleAdd() {
    if (currentTreeNode && currentTreeNode.id) {
      stateData.targetForm = {};
      stateData.fileList = [];
      stateData.tableColumn.forEach((column) => {
        stateData.targetForm[column.prop] = null;
      });
      stateData.targetDialogShow = true;
    } else {
      ElMessage.error("请在左边的树选择一个类型");
    }
  }
  //关闭新增编辑弹窗
  function targetDialogClose() {
    stateData.targetDialogShow = false;
  }
  function uploadFile() {
    console.log("uploadFile");
    if (stateData.fileList.length == 0) {
      return;
    }
    console.log(stateData.fileList);
    let formData = new FormData();
    formData.append("file", stateData.fileList[0].raw);
    let bzPath = ["djpt", "tjpt", "kjpt"][currentTreeNode.dataType - 1];
    formData.append("file", stateData.fileList[0].raw);
    formData.append("bzPath", bzPath);
    dataApi.uploadFile(formData).then((res) => {
      if (res.data.code == 200) {
        ElMessage.success("上传成功");
        // console.log(res.data.data);
        stateData.targetForm.fileId = res.data.data;
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }
  //资源目标提交按钮事件
  function targetDialogSubmit(formRef) {
    formRef.validate((valid, fields) => {
      if (valid) {
        // console.log("submit!");
        addEditTarger();
      } else {
        console.log("error submit!", fields);
      }
    });
  }
  //新增 目标
  function addEditTarger() {
    stateData.loading = true;
    delete stateData.targetForm.operation;
    if (!stateData.targetForm.id) {
      delete stateData.targetForm.id;
    }
    delete stateData.targetForm.updateTime;
    delete stateData.targetForm.createTime;
    delete stateData.targetForm.fileName;
    // 业务类型  工作体制 频段
    stateData.targetForm.workSystem = stateData.targetForm.workSystem.join()
    stateData.targetForm.businessType = stateData.targetForm.businessType.join()
    stateData.targetForm.frequency = stateData.targetForm.frequency.join()
    dataApi
      .addEditTarget({
        dataId: stateData.targetForm.id,
        dataTypeId: currentTreeNode.id,
        data: { ...stateData.targetForm, areaId: stateData.areaId, generalId: currentTreeNode.id },
      })
      .then((res) => {
        stateData.loading = false;
        if (res.data.code == 200) {
          if (stateData.targetForm.id) {
            ElMessage.success("修改成功");
          } else {
            ElMessage.success("新增成功");
          }
          topChart()
          stateData.targetDialogShow = false;
          handleNodeClick(currentTreeNode);
        } else {
          ElMessage.error(res.data.message);
        }
      });
  }
  //控制图上资源显隐
  const showEntity = (show, row, index) => {
    mapTool.showEntityById(row.id, show);
    return;
    // TODO 2D的显隐失效，3D有效
    // window.EVGISMAP("updatePointByAttr", {
    //   id: row["jdlb"] + "_" + row.id + "_point",
    //   position: row.position,
    //   show: show,
    // });
  };
  // 表格点击事件
  const nodeClickEmit = (data) => {
    sbjdListVisb.value = true;
    currentNode.value = { ...data };
  };

  var currentTreeNode;
  const handleNodeClick = (data) => {
    // debugger
    // console.log("data", data);
    if (data.isParent) return;
    currentTreeNode = data;

    currentNode.value = {};
    const typeSet = (val, prop) => {
      let reType = "string";
      switch (val) {
        case "timestamp":
          reType = "timestamp";
          break;
        case "int":
        case "decimal":
        case "tinyint":
          reType = "number";
          break;
        case "varchar":
          reType = "string";
          break;
        default:
          break;
      }
      window.dictTypeList[prop] && (reType = "sysdic");
      return reType;
    };
    dataApi.getAreaGeneralField({ dataTypeId: data.id }).then((res2) => {
      if (res2.data.code == 200) {
        let data = res2.data.data;
        stateData.tableColumn = [];
        data &&
          data.length &&
          data.forEach((fieldObj) => {
            if (!stateData.unShowList.includes(fieldObj.columnName)) {
              stateData.tableColumn.push({
                label: fieldObj.columnComment,
                prop: fieldObj.columnName,
                type: window.dictTypeList[fieldObj.columnName] ? "Sysdic" : "String",
                // width: (fieldObj.CHARACTER_MAXIMUM_LENGTH / 32) * 150 + "",
                width: "170",
                maxLength: fieldObj.characterMaximumLength,
                formType: typeSet(fieldObj.dataType, fieldObj.columnName),
                // SysdicName: window.dictTypeList[fieldObj.COLUMN_NAME],
              });
            }
            // console.log(window.dictTypeList[fieldObj.COLUMN_NAME]);
          });
        stateData.dictTypeList = window.dictTypeList;
        stateData.tableColumn.push({
          prop: "operation",
          label: "操作",
          type: "operation",
          width: "130",
          operationTypeName: {
            position: false,
            demand: false,
            handleEdit: true,
            handleDelete: true,
          },
        });
      }
    });
    getAreaGeneralData(data);
  };

  function getAreaGeneralData(data) {
    if (!data.id) {
      return;
    }
    dataApi
      .getAreaGeneralData({
        dataTypeId: data.id,
        areaId: stateData.areaId,
        keyword: stateData.listInput,
        page: pageInfo.page,
        size: pageInfo.size,
      })
      .then((res) => {
        if (res.data.code == 200) {
          // ElMessage.error(res.data.message);
          stateData.dataInfoList = res.data.data.records;
          pageInfo.total = res.data.data.total;
        } else {
          ElMessage.error(res.data.message);
        }
      });
  }
  function currentPageEmit(val) {
    pageInfo.page = val;
    getAreaGeneralData(currentTreeNode);
  }
  function pageSizeEmit(val) {
    pageInfo.size = val;
    getAreaGeneralData(currentTreeNode);
  }
  const drawMap = (option) => {
    console.log("option", option.id);
    switch (option.type) {
      case "天基":
        window.EVGISMAP("drawPointByAttr", {
          id: option.id + "_point",
          img: "./wx.png",
          // group: "EVMAPGROUP",
          position: option.position, //[120, 30,10000000],option
          name: option.name,
          color: "#ffff00",
          outlineColor: "green",
          outlineWidth: "1",
          textColor: "red",
          textOutlineColor: "black",
          textScale: 1,
          scale: 1,
          direction: 90,
          width: 15,
          height: 15,
        });

        // var draw = new Cesium.EV_DrawGeometry(window.Viewer);
        // start
        window.EVGISMAP("drawCylinderByAttr", {
          id: option.id + "_cyLine",
          group: "EVMAPGROUP",
          position: option.position, //几何体中心坐标位置
          length: option.position[2],
          bottomRadius: +option.bottomRadius,
          // topRadius: +option.topRadius,
          color: "rgba(255,0,0,0.5)",
        });
        //end

        // var cylinder3 = draw.drawCylinder({
        //   id: option.id + "_cyLine",
        //   position: Cesium.Cartesian3.fromDegrees(option.position[0], option.position[1]), //几何体中心坐标位置
        //   bottomRadius: +option.bottomRadius,
        //   topRadius: +option.topRadius,
        //   length: option.position[2],
        //   material: Cesium.Color.RED.withAlpha(0.3),
        // });
        break;
      case "地基":
        window.EVGISMAP("drawPointByAttr", {
          id: option.id + "_point",
          img: "./dmz1.png",
          position: option.position,
          name: option.name,
          color: "#ffff00",
          outlineColor: "green",
          outlineWidth: "1",
          textColor: "red",
          textOutlineColor: "black",
          textScale: 1,
          scale: 1,
          direction: 360,
          width: 15,
          height: 15,
        });
        window.EVGISMAP("drawCircleByAttr", {
          id: option.id + "_circle",
          color: "rgba(255,0,0,0.3)",
          position: option.position,
          radius: option.radius,
        });
        break;
      case "空基":
        window.EVGISMAP("drawPointByAttr", {
          id: option.id + "_point",
          img: "./fj.png",
          position: option.position,
          name: option.name,
          color: "#ffff00",
          outlineColor: "green",
          outlineWidth: "1",
          textColor: "red",
          textOutlineColor: "black",
          textScale: 1,
          scale: 1,
          direction: 90,
          width: 15,
          height: 15,
        });
        break;
      case "无人机":
        window.EVGISMAP("drawPointByAttr", {
          id: option.id + "_point",
          img: "./fj.png",
          position: option.position,
          name: option.name,
          color: "#ffff00",
          outlineColor: "green",
          outlineWidth: "1",
          textColor: "red",
          textOutlineColor: "black",
          textScale: 1,
          scale: 1,
          direction: 90,
          width: 15,
          height: 15,
        });

        // var draw = new Cesium.EV_DrawGeometry(window.Viewer);
        // start
        window.EVGISMAP("drawCylinderByAttr", {
          id: option.id + "_cyLine",
          group: "EVMAPGROUP",
          position: option.position, //几何体中心坐标位置
          length: option.position[2],
          bottomRadius: +option.bottomRadius,
          topRadius: +option.topRadius,
          color: "rgba(255,0,0,0.5)",
        });
        break;
    }
  };

  // 返回上一级
  const goBack = () => {
    if (stateData.pageType == 1) {
      router.push({
        path: "/home/<USER>/",
        replace: true,
        query: {
          // armyType: stateData.armyType
          currentPage: router.currentRoute._value?.query?.currentPage || 1
        }
      });
    } else if (stateData.pageType == 2) {
      router.push({
        path: "/home/<USER>",
        replace: true,
      });
    }
  };
</script>

<style scoped lang="less">
  .title-font {
    font-size: 18px;
    font-weight: bolder;
    color: #c6cdd6;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
  }

  .custom-tree-node1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
  }

  .el-form-item__label {
    color: aqua !important;
  }

  .is-icon {
    margin-left: 2px;
  }

  .closePopBtn {
    right: 30px;
    position: absolute;
    cursor: pointer;
  }

  .el-form-item__label {
    color: aqua !important;
  }

  :deep(.custom-popup-mian .content1) {
    padding: 0 !important;
  }

  :deep(.el-tree-node__content > .el-tree-node__expand-icon) {
    padding: 0 !important;
    width: 0 !important;
  }

  .leftNodeTree {
    overflow-y: auto;
    height: 100%;
    width: 100%;
  }

  .leftNodeTree :deep(.el-tree-node__content) {
    color: rgb(48, 158, 255);
    font-size: 17px;
    font-weight: bolder;
  }

  .leftNodeTree :deep(.el-tree-node__children .el-tree-node__content) {
    color: #285472;
    font-size: 15px;
    font-weight: normal;
  }

  :deep(.el-tabs__header) {
    margin: 0;

    .el-tabs__item {
      font-size: 17px;
      font-weight: bolder;
    }

    .el-tabs__nav-wrap::after {
      background-color: #03a9f46e;
      height: 1px;
    }

    .is-active {
      border-bottom: 5px solid #409eff;
    }
  }

  :deep(.el-tabs__content) {
    border: 1px solid #b0c6cf;
    border-top: none;
    padding-top: 20px;
    height: calc(100% - 66px);
  }

  :deep(.el-tabs__content .el-form-item) {
    display: flex;

    >.el-form-item__label {
      color: #285472;
      font-size: 15px;
      font-weight: bolder;
    }
  }

  :deep(.el-input__inner) {
    text-align: left;
  }

  .treeParent {
    width: 32px;
    height: 32px;
    background: url("/public/images/new/icon/任务推演_09.png");
  }

  .cesium_box {
    position: fixed;
    left: 3vw;
    height: 97vw;
    z-index: 999;
  }
</style>