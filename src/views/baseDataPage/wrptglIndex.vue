<template>
  <customPopup left="10px" top="80px" width="24%" height="90%">
    <template #header>
      <span class="title-font">资源域数据管理</span>
    </template>
    <template #content>
      <!-- show-checkbox  -->
      <div class="leftChartBox">
        <div>
          <div ref="Chart" style="height: 100%; width: 100%"></div>
        </div>
        <div>
          <div ref="Chart1" style="height: 100%; width: 100%"></div>
        </div>
        <div>
          <div ref="Chart2" style="height: 100%; width: 100%"></div>
        </div>
      </div>
    </template>
  </customPopup>

  <customPopup1 left="26%" top="80px" width="73%" height="90%">
    <template #content>
      <!-- <div style="height: 30%;display: flex;margin-top: 20px;">
        dddd
      </div>
      <div style="height: 63%;margin-top: 20px;border: 1px solid #3691d291;padding: 5px;">
        sssss
      </div> -->
      <div class="rightCardBox">
        <el-card
          class="box-card"
          v-for="item in cardList"
          :key="item.id"
          @click="selectDomain(item.id)"
        >
          <template #header>
            <div class="card-header">
              <span>{{ item.name }}</span>
            </div>
          </template>
          <div class="cardContent">
            <div>
              <span class="spanKey">编号：</span
              ><span>{{ item.serialNumber }}</span>
            </div>
            <div>
              <span class="spanKey">{{ item.uav }}：</span
              ><span>{{ item.uavNum }}</span>
            </div>
            <div>
              <span class="spanKey">{{ item.car }}：</span
              ><span>{{ item.carNum }}</span>
            </div>
            <div>
              <span class="spanKey">{{ item.ship }}：</span
              ><span>{{ item.shipNum }}</span>
            </div>
            <div>
              <span class="spanKey">雷达数量：</span
              ><span>{{ item.radarNum }}</span>
            </div>
            <div>
              <span class="spanKey">侦测方式：</span
              ><span>{{ item.detectionMode }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </template>
  </customPopup1>
  <!-- <customPopup v-if="isShow" left="17%" top="80px" width="61%" height="90%">
    <template #header>
      <div style="position: absolute; right: 2%;">
        <el-button @click="isShow = false">关闭</el-button>
      </div>
    </template>
    <template #content>
      <CesiumBox></CesiumBox>
    </template>
  </customPopup> -->
</template>

<script setup>
import customPopup from '@/components/customPopup.vue'
import customPopup1 from '@/components/customPopup1.vue'
import { reactive, ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'

let Chart = ref(null)
let Chart1 = ref(null)
let Chart2 = ref(null)

let myChart = null
let myChart1 = null
let myChart2 = null

// 无人平台类型饼图
let Option = {
  title: {
    text: '资源域占比图',
    x: '25%',
    y: '5%',
  },
  legend: {
    orient: 'vertical',
    x: '300px',
    y: '100px',
    data: ['资源域1', '资源域2', '资源域3', '资源域4'],
  },
  calculable: true,
  series: [
    {
      name: '资源域',
      type: 'pie',
      radius: '65%',
      center: ['45%', '60%'],
      data: [
        { value: 335, name: '资源域1' },
        { value: 310, name: '资源域2' },
        { value: 234, name: '资源域3' },
        { value: 135, name: '资源域4' },
      ],
      label: {
        show: true,
        position: 'inside',
      },
    },
  ],
}
//  测控节点类型统计
let Option1 = {
  title: {
    text: '测控节点类型统计',
    x: '25%',
    y: '5%',
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c} ({d}%)',
  },
  legend: {
    orient: 'vertical',
    x: '300px',
    y: '100px',
    data: ['地基', '空基', '天基'],
  },
  calculable: true,
  series: [
    {
      name: '访问来源',
      center: ['45%', '60%'],
      type: 'pie',
      radius: ['50%', '70%'],
      itemStyle: {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
        },
        emphasis: {
          label: {
            show: true,
            position: 'center',
            textStyle: {
              fontSize: '30',
              fontWeight: 'bold',
            },
          },
        },
      },
      data: [
        { value: 234, name: '天基' },
        { value: 310, name: '空基' },
        { value: 335, name: '地基' },
      ],
    },
  ],
}
//  设备测控能力范围
let Option2 = {
  title: {
    x: '25%',
    y: '5%',
    text: '无人平台类型',
  },
  tooltip: {
    // trigger: 'axis',
    trigger: 'item',
  },
  legend: {
    orient: 'vertical',
    x: '300px',
    y: '100px',
    data: ['无人机', '无人车', '无人艇', '弹'],
  },
  // polar: [
  //   {
  //     indicator: [
  //       { text: '无人机', max: 6000 },
  //       { text: '无人车', max: 16000 },
  //       { text: '无人艇', max: 30000 },
  //       { text: '弹', max: 38000 },
  //       { text: '其他', max: 52000 },
  //     ],
  //     center: ['45%', 150],
  //     radius: 70
  //   },

  // ],
  calculable: true,
  series: [
    {
      name: '无人平台类型',
      type: 'pie',
      radius: '65%',
      center: ['36.5%', '55%'],
      data: [
        { value: 430, name: '无人机' },
        { value: 310, name: '无人车' },
        { value: 234, name: '无人艇' },
        { value: 135, name: '弹' },
        // { value: 135, name: '其他' },
      ],
      // label: {
      //   show: true,
      //   position: 'inside'
      // }
      // itemStyle: { normal: { areaStyle: { type: 'default' } } },
      // data: [
      //   {
      //     value: [4300, 10000, 28000, 30000, 5000],
      //     name: '数量'
      //   },
      // ]
    },
  ],
}

let cardList = reactive([
  {
    id: 1,
    name: '资源域一',
    serialNumber: 'Z36', //编号
    detectionMode: '天基/地基/空基',
    uavNum: 2, //无人机数量
    uav: '无人机数量',
    carNum: 4, //无人车数量
    car: '无人车数量',
    shipNum: 8, //无人艇数量
    ship: '无人艇数量',
    radarNum: 3, //雷达数量
  },
  {
    id: 2,
    name: '资源域二',
    serialNumber: 'U-56', //编号
    detectionMode: '天基侦测',
    car: '无人车数量',
    uavNum: 3, //无人机数量
    ship: '无人艇数量',
    carNum: 7, //无人车数量
    uav: '无人机数量',
    shipNum: 4, //无人艇数量
    radarNum: 1, //雷达数量
  },
  {
    id: 3,
    name: '资源域三',
    serialNumber: 'U-23', //编号
    detectionMode: '地基侦测',
    carNum: 13, //无人车数量
    car: '无人车数量',
    shipNum: 6, //无人艇数量
    ship: '无人艇数量',
    uavNum: 12, //无人机数量
    uav: '无人机数量',
    radarNum: 5, //雷达数量
  },
  {
    id: 4,
    name: '资源域四',
    serialNumber: 'H-126', //编号
    detectionMode: '空基侦测',
    car: '无人车数量',
    carNum: 22, //无人车数量
    ship: '无人艇数量',
    shipNum: 12, //无人艇数量
    uav: '无人机数量',
    uavNum: 5, //无人机数量
    radarNum: 2, //雷达数量
  },
])

const router = useRouter()
// 点击卡片跳转区域
const selectDomain = (id) => {
  // console.log("选中域的id：", id)
  router.replace({
    path: '/home/<USER>',
  })
}
onMounted(() => {
  nextTick(() => {
    myChart1 = echarts.init(Chart1.value)
    myChart1.setOption(Option1, true)
  })
  nextTick(() => {
    myChart2 = echarts.init(Chart2.value)
    myChart2.setOption(Option2, true)
  })
  nextTick(() => {
    myChart = echarts.init(Chart.value)
    myChart.setOption(Option, true)
  })
})
</script>

<style scoped lang="less">
.title-font {
  /* color: blue; */
  font-size: 18px;
  font-weight: bolder;
  color: #285472;
}

.leftChartBox {
  height: 100%;

  > div {
    height: 33%;
  }
}

.rightCardBox {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: space-around;
  justify-content: center;

  .cardContent {
    display: flex;

    .spanKey {
      color: #3fb5ff;
    }

    > div {
      width: 200px;
    }
  }
}

:deep(.el-card) {
  width: 90%;
  height: 22%;
  border: 2px solid #0080da;
  cursor: pointer;

  .el-card__header {
    color: #285472;
    font-weight: bold;
    border-bottom: 1px solid #0080da;
  }
}
</style>
