<template>
  <customPopup left="10px" top="80px" width="21%" height="90%" headType="2">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">目标域数据管理</span>
    </template>
    <template #content>
      <!-- show-checkbox  -->
      <div class="leftChartBox">
        <div>
          <div class="sub_title">域目标比例</div>
          <div ref="Chart" class="leftChart"></div>
        </div>
        <div>
          <div class="sub_title">目标类别统计</div>

          <div ref="Chart1" class="leftChart"></div>
        </div>
        <div>
          <div class="sub_title">目标类型统计</div>
          <div ref="Chart2" class="leftChart"></div>
        </div>
      </div>
    </template>
  </customPopup>
  <!-- card_all_box -->
  <customPopup top="80px" left="22%" width="77.5%" height="calc(100vh - 95px)">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">目标域数据管理</span>
      <el-button style="position: absolute;right: 20px;top: 50%; transform: translatey(-50%);" type="primary"
        icon="CirclePlus" @click="state.areaDialogShow = true">
        新增域
      </el-button>
    </template>
    <template #content>
      <!-- 切换视图按钮 -->
      <!-- <el-icon style="position: absolute; right: 40px; top: 17px" size="20" @click="changeView">
        <el-tooltip content="切换视图" placement="bottom" effect="light">
          <Grid style="width: 20px; height: 20px" />
        </el-tooltip>
      </el-icon> -->
      <el-tooltip :content="state.currentPage <= 1 ? '已经是第一页了' : '上一页'" placement="bottom" effect="light">
        <el-button class="leftBtn" circle @click="pageChange('left')">
          <el-icon>
            <ArrowLeftBold />
          </el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip :content="state.currentPage >= state.maxPage ? '已经是最后一页了' : '下一页'" placement="bottom" effect="light">
        <el-button class="rightBtn" circle @click="pageChange('right')">
          <el-icon>
            <ArrowRightBold />
          </el-icon>
        </el-button>
      </el-tooltip>
      <div class="card_all_box">
        <div v-for="(card, index) in state.areaList" :key="card.id" class="card_box"
          :style="{ width: state.cardStyle.width, height: state.cardStyle.height }">
          <customPopup left="0" top="0" width="100%" height="100%" :hover="true" bgColor="#031527a0">
            <template #header>
              <span class="title-font" style="position: relative; width: 100%" @click="selectDomain(card)">
                <span style="margin-left: 10px; font-size: 18px">{{ card.areaName }}</span>
                <el-tooltip content="删除域" placement="top" effect="light">
                  <el-icon class="iconHover" style="position: absolute; right: 45px; top: calc(50% - 10px)"
                    @click.stop="removeArea(card)">
                    <Remove style="height: 2em; width: 2em" />
                  </el-icon>
                </el-tooltip>
                <el-tooltip content="编辑域" placement="top" effect="light">
                  <el-icon class="iconHover" style="position: absolute; right: 20px; top: calc(50% - 10px)"
                    @click.stop="editArea(card)">
                    <Edit style="height: 2em; width: 2em" />
                  </el-icon>
                </el-tooltip>
              </span>
            </template>
            <template #content>
              <div style="height: 100%" @click="selectDomain(card)" class="cardFlex">
                <div :id="'cardChartA' + card.id" style="height: 30%; width: 45%; margin: 0 2.5%"
                  :style="{ height: state.viewStatus == 'twoCard' ? '30%' : '100%' }"></div>
                <div :id="'cardChartB' + card.id" style="height: 30%; width: 45%; margin: 0 2.5%"
                  :style="{ height: state.viewStatus == 'twoCard' ? '30%' : '100%' }"></div>
                <div :id="'cardChartC' + card.id" style="height: 30%; width: 95%; margin: 0 2.5%"
                  v-if="state.viewStatus == 'twoCard'">
                  <div :style="`font-weight: bolder; font-size: 16px; color: ${fontColor};margin-bottom: 10px;`">
                    空闲状态
                  </div>
                  <div style="overflow-y: auto; height: calc(100% - 65px); width: 100%; padding: 10px 10px 0 0">
                    <div v-for="(value, key, index) in state.occupancyList[card.id]" :key="index"
                      style="margin-bottom: 20px">
                      <span :style="`color:${fontColor}`">{{ key }}</span>
                      <span style="color: #64a1dd; float: right">{{ value * 100 + "%" }}</span>
                      <br />
                      <el-progress :percentage="value * 100" :show-text="false" style="margin-top: 10px"
                        color="#2376A7c0" />
                    </div>
                    <div style="height: 10px; width: 100%"></div>
                  </div>
                </div>
                <!-- <div :id="'cardChartD' + card.id" style="height: 30%; width: 45%; margin: 0 2.5%" v-if="state.viewStatus == 'twoCard'"></div> -->
                <div style="height: 40%; width: 100%" v-if="state.viewStatus == 'twoCard'">
                  <customTable :paginationShow="false" v-if="state.dataInfoList[card.id]"
                    :tableData="state.dataInfoList[card.id]" :tableColumn="state.tableColumn"></customTable>
                </div>
              </div>
            </template>
          </customPopup>
        </div>
      </div>
    </template>
  </customPopup>
  <el-dialog :title="state.areaForm.id ? '目标域-编辑' : '目标域-新增'" width="600px" draggable v-model="state.areaDialogShow"
    :close-on-click-modal="false" @close="areaDialogClose">
    <el-form ref="areaFormRef" :model="state.areaForm" :rules="state.areaRules" label-width="100px">
      <el-form-item label="名称" prop="areaName">
        <el-input placeholder="请填写域的名称" :maxLength="20" v-model="state.areaForm.areaName"></el-input>
      </el-form-item>
      <el-form-item label="目标域类型" prop="areaType">
        <el-select placeholder="请选择目标域类型" v-model="state.areaForm.areaType" @change="state.areaForm.generalIds = []"
          disabled clearable>
          <el-option :label="item.name" :value="item.value" v-for="(item, index) in state.areaTypeList"
            :key="index"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="generalIds" v-show="state.areaForm.areaType">
        <el-select placeholder="请选择类型" v-model="state.areaForm.generalIds" multiple clearable>
          <el-option :label="item.tableComment" :value="item.id"
            v-for="(item, index) in state.areaForm.areaType == 1 ? state.resourcesTypeList : state.targetTypeList"
            :key="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="areaDescribe">
        <el-input :rows="2" type="textarea" placeholder="请填写对域的描述" v-model="state.areaForm.areaDescribe"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button size="small" @click="areaDialogClose">取 消</el-button>
        <el-button size="small" type="primary" @click="areaDialogSubmit(areaFormRef)" :loading="state.loading">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import customPopup from "@/components/customPopup.vue";
import customPopup1 from "@/components/customPopup1.vue";
import { reactive, ref, onMounted, onUnmounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import * as dataApi from "@/service/API/system/areaManage.js";
import { ElMessage, ElMessageBox } from "element-plus";
import customTable from "@/components/customTable.vue";

let Chart = ref(null);
let Chart1 = ref(null);
let Chart2 = ref(null);
let areaFormRef = ref();

let myChart = null;
let myChart1 = null;
let myChart2 = null;
let fontColor = window.$fontColor;
const areaTypeDic = {
  1: "资源",
  2: "目标",
};
var resourcesTypeDic = {};
const state = reactive({
  pageType: 2, //1: area域 2： target目标
  areaList: [], //域列表
  areaDialogShow: false, //新增、编辑域弹窗展示
  areaForm: {
    areaDescribe: "",
    areaName: "",
    areaType: 2,
    id: null,
    generalIds: [],
  }, //域表单
  areaRules: {
    areaName: [{ required: true, message: "请输入域名称", trigger: "blur" }],
    areaType: [{ required: true, message: "请选择域类型", trigger: "blur" }],
    generalIds: [
      {
        type: "array",
        required: true,
        message: "请至少选择一个类型",
        trigger: "change",
      },
    ],
  }, //域校验规则
  loading: false,
  areaTypeList: [
    { name: "资源", value: 1 },
    { name: "目标", value: 2 },
  ],

  resourcesTypeList: [],
  targetTypeList: [],
  dataInfoList: {},
  tableColumn: [
    { label: "节点名称", prop: "name", type: "String", width: "150" },
    { label: "类型", prop: "generalId", type: "Sysdic", width: "150", SysdicName: resourcesTypeDic },
    { label: "ERIP", prop: "eirp", type: "String", width: "150" },
    { label: "频率", prop: "start_frequency", type: "String", minWidth: "130" },
  ],
  currentPage: 1, //当前页码
  occupancyList: {}, //占用状态列表
  currentSize: 4, //当前页大小
  viewStatus: "gridCard", //卡片展示方式
  cardStyle: {
    height: "47%",
    width: "49%",
  },
});

//  设备测控能力范围
let Option2 = {
  color: chartColors,
  tooltip: {
    // trigger: 'axis',
    trigger: "item",
  },
  legend: {
    orient: "vertical",
    x: "70%",
    y: "20%",
    data: ["无人机", "无人车", "无人艇", "弹"],
    textStyle: {
      color: fontColor,
    },
  },
  // polar: [
  //   {
  //     indicator: [
  //       { text: '无人机', max: 6000 },
  //       { text: '无人车', max: 16000 },
  //       { text: '无人艇', max: 30000 },
  //       { text: '弹', max: 38000 },
  //       { text: '其他', max: 52000 },
  //     ],
  //     center: ['45%', 150],
  //     radius: 70
  //   },

  // ],
  calculable: true,
  series: [
    {
      name: "无人平台类型",
      type: "pie",
      radius: "75%",
      center: ["30%", "50%"],
      data: [
        { value: 430, name: "无人机" },
        { value: 310, name: "无人车" },
        { value: 234, name: "无人艇" },
        { value: 135, name: "弹" },
        // { value: 135, name: '其他' },
      ],
      label: {
        formatter: function (node) {
          if (node.percent > 0) {
            return node.name + "：" + node.percent + '%';
          } else {
            return ''
          }
        }
      },
      // label: {
      //   show: true,
      //   position: 'inside'
      // }
      // itemStyle: { normal: { areaStyle: { type: 'default' } } },
      // data: [
      //   {
      //     value: [4300, 10000, 28000, 30000, 5000],
      //     name: '数量'
      //   },
      // ]
    },
  ],
};

const router = useRouter();
// 点击卡片跳转区域
const selectDomain = (area) => {
  router.replace({
    path: "/home/<USER>",
    query: {
      id: area.id,
      areaName: area.areaName,
      pageType: 2,
    },
  });
};

onMounted(() => {
  window.pageType = 2;
  getAreaList();
  getGeneralIdList();
  leftChart()
  // nextTick(() => {
  //   myChart2 = echarts.init(Chart2.value);
  //   myChart2.setOption(Option2, true);
  // });
});
function leftChart(){
  getStatsArea();
  getStatsAreaType();
  getAllLeaf();
}
// 目标域数量统计接口
function getStatsArea() {
  dataApi.getStatsArea({ type: 2 }).then((res) => {
    if (res.data.code == 200) {
      let data = res.data.data;
      myChart = echarts.init(Chart.value);
      let option = {
        color: chartColors,
        legend: {
          type: 'scroll',
          orient: "vertical",
          x: "70%",
          y: "20%",
          data: [],
          textStyle: {
            color: fontColor,
          },
        },
        tooltip: {
          trigger: "item",
        },
        calculable: true,
        series: [
          {
            type: "pie",
            radius: "75%",
            center: ["30%", "50%"],
            data: [],
            label: {
              position: "inside",
              // formatter: "{b}:{d}%",
              formatter: function (node) {
                if (node.percent > 0) {
                  
                  return node.name + "：" + node.percent +  '%';                  
                } else {
                  return ''
                }
              }
              // formatter: (data) => {
              //   if (data.value == 0) {
              //     return "";
              //   } else {
              //     return data.name + " " + data.value;
              //   }
              // },
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      for (let key in data) {
        let val = data[key];
        option.legend.data.push(key);
        option.series[0].data.push({
          value: val,
          name: key,
        });
      }
      myChart.setOption(option, true);
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
// 目标域类型计接口
function getStatsAreaType() {
  dataApi.getStatsAreaType({ type: 2 }).then((res) => {
    if (res.data.code == 200) {
      let data = res.data.data;
      myChart1 = echarts.init(Chart1.value);
      let option = {
        color: chartColors,
        legend: {
          orient: "vertical",
          x: "70%",
          y: "20%",
          textStyle: {
            color: fontColor,
          },
        },
        tooltip: {
          trigger: "item",
        },
        series: [
          {
            type: "pie",
            radius: "75%",
            center: ["30%", "50%"],
            roseType: "area",
            itemStyle: {
              borderRadius: 1,
            },
            label: {
              position: "inside",
              // formatter: "{b}:{d}%",
              formatter: function (node) {
                if (node.percent > 0) {
                  return node.name + "：" + node.percent +  '%';                  
                } else {
                  return ''
                }
              }
              // formatter: (data) => {
              //   if (data.value == 0) {
              //     return "";
              //   } else {
              //     return data.name + " " + data.value;
              //   }
              // },
            },
            data: [],
          },
        ],
      };
      for (let key in data) {
        let val = data[key];
        // option.legend.data.push(key);
        option.series[0].data.push({
          value: val,
          name: key,
        });
      }
      myChart1.setOption(option, true);
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
function getAllLeaf() {
  dataApi.getAllLeaf({ type: 2 }).then((res) => {
    if (res.data.code == 200) {
      myChart2 = echarts.init(Chart2.value);
      let data = res.data.data;
      let option = {
        color: chartColors,
        legend: {
          type: 'scroll',
          orient: "vertical",
          x: "70%",
          y: "20%",
          textStyle: {
            color: fontColor,
          },
        },
        tooltip: {
          trigger: "item",
        },
        series: [
          {
            type: "pie",
            radius: "75%",
            center: ["30%", "50%"],
            roseType: "area",
            itemStyle: {
              borderRadius: 1,
            },
            label: {
              position: "inside",
              // formatter: "{b}:{d}%",
              formatter: function (node) {
                if (node.percent > 0) {
                  
                  return node.name + "：" + node.percent +  '%';                  
                } else {
                  return ''
                }
              }
              // formatter: (data) => {
              //   if (data.value == 0) {
              //     return "";
              //   } else {
              //     return data.name + " " + data.value;
              //   }
              // },
            },
            data: [],
          },
        ],
      };
      for (let key in data) {
        let val = data[key];
        // option.legend.data.push(key);
        option.series[0].data.push({
          value: val,
          name: key,
        });
      }
      myChart2.setOption(option, true);
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
// 切换页码
function pageChange(type) {
  if (state.currentPage <= 1 && type == "left") {
    return;
  } else if (state.currentPage >= state.maxPage && type == "right") {
    return;
  }
  type == "left" && (state.currentPage = state.currentPage - 1);
  type == "right" && (state.currentPage = state.currentPage + 1);
  getAreaList();
}
// 获取域列表
function getAreaList() {
  dataApi.getAreaList({ page: state.currentPage, size: state.currentSize, areaType: state.pageType }).then((res) => {
    if (res.data.code == 200) {
      state.areaList = res.data.data.records;
      state.maxPage = Math.ceil(res.data.data.total / 2);
      setTimeout(() => {
        state.occupancyList = {}; //占用状态列表
        state.dataInfoList = {}; //表格数据列表
        state.areaList.forEach((area, index) => {
          drawChart.A(area.id);
          drawChart.B(area.id);
          if (state.viewStatus == "twoCard") {
            drawChart.C(area.id);
            // drawChart.D(area.id);
            drawChart.Table(area);
          }
        });
      }, 1000);
    }
  });
}
const drawChart = {
  A: function (id) {
    //  测控节点类型统计
    dataApi.getStatsType({ areaId: id }).then((res) => {
      if (res.data.code == 200) {
        let data = res.data.data;
        let option = {
          color: chartColors,
          title: {
            text: "类别比例",
            x: "left",
            textStyle: {
              color: fontColor,
              fontSize: 16,
              // fontWeight: "bolder",
            },
          },
          tooltip: {
            trigger: "item",
          },
          legend: {
            // orient: "vertical",
            // x: "70%",
            // y: "60%",
            left: "right",
            top: "bottom",
            data: [],
            textStyle: {
              color: fontColor,
            },
          },
          calculable: true,
          series: [
            {
              name: "类别",
              type: "pie",
              radius: "65%",
              center: ["45%", "50%"],
              data: [],
              // label: {
              //   show: true,
              //   position: "inside",
              //   formatter: (node) => {
              //     console.log(node);
              //     // if (node.value) {
              //     return node.name + "：" + node.value;
              //     // } else {
              //     //   return "";
              //     // }
              //   },
              //   //  "{a} <br/>{b} : {c} ({d}%)",
              // },
              label: {
                position: "inside",
                // formatter: "{b}:{d}%",
                formatter: function (node) {
                if (node.percent > 0) {
                  return node.name + "：" + node.percent +  '%';                  
                } else {
                  return ''
                }
              }
                // formatter: (data) => {
                //   if (data.value == 0) {
                //     return "";
                //   } else {
                //     return data.name + " " + data.value;
                //   }
                // },
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
            },
          ],
        };
        for (let key in data) {
          let val = data[key];
          option.legend.data.push(key);
          option.series[0].data.push({
            value: val,
            name: key,
          });
        }
        let chartDom = document.getElementById("cardChartA" + id);
        let cardChartx = echarts.init(chartDom);

        cardChartx.setOption(option, true);
        cardChartx.resize();
      } else {
        ElMessage.error(res.data.messagea);
      }
    });
  },
  B: function (id) {
    // getStatsLeaf
    dataApi.getStatsType({ areaId: id }).then((res) => {
      if (res.data.code == 200) {
        let data = res.data.data;
        let option = {
          color: chartColors,
          title: {
            text: "类别数量",
            x: "left",
            textStyle: {
              color: fontColor,
              fontSize: 16,
            },
          },
          tooltip: {
            trigger: "item",
          },
          xAxis: {
            type: "category",
            data: [],
            axisLine: window.axisLine,
            axisLabel: {
              color: fontColor,
              rotate: 45,
            },
          },
          yAxis: {
            type: "value",
            splitLine: window.splitLine,
            axisLine: window.axisLine,
            axisLabel: {
              color: fontColor,
            },
          },
          series: [
            {
              data: [],
              type: "bar",
              barMaxWidth: 30,
            },
          ],
        };
        for (let key in data) {
          let val = data[key];
          option.xAxis.data.push(key);
          option.series[0].data.push(val);
        }
        // setTimeout(() => {
        let chartDom = document.getElementById("cardChartB" + id);
        let cardChartx = echarts.init(chartDom);
        cardChartx.setOption(option, true);
        cardChartx.resize();
        // }, 1000);
      } else {
        ElMessage.error(res.data.messagea);
      }
    });
  },
  C: function (id) {
    dataApi.getOccupancyByArea({ areaId: id }).then((res) => {
      if (res.data.code == 200) {
        state.occupancyList[id] = {};
        Object.keys(res.data.data).forEach((item, index) => {
          if (index < 2) {
            state.occupancyList[id][item] = res.data.data[item];
          }
        });
      } else {
        ElMessage.error(res.data.message);
      }
    });
  },
  D: function (id) {
    let chartDom = document.getElementById("cardChartD" + id);
    let cardChartx = echarts.init(chartDom);
    let option;
    option = {
      title: {
        text: "雷达图",
        textStyle: {
          color: fontColor,
          fontSize: 16,
        },
      },
      legend: {
        data: ["品牌", "内容", "可用性", "功能"],
      },
      radar: {
        indicator: [
          { text: "品牌", max: 100 },
          { text: "内容", max: 100 },
          { text: "可用性", max: 100 },
          { text: "功能", max: 100 },
        ],
        radius: 70,
        center: ["50%", "50%"],
      },
      series: [
        {
          type: "radar",
          tooltip: {
            trigger: "item",
          },

          areaStyle: {},
          data: [
            {
              value: [60, 73, 85, 40],
              name: "某软件",
            },
          ],
        },
      ],
    };

    cardChartx.setOption(option, true);
    cardChartx.resize();
  },
  Table: function (data) {
    if (!data.id) {
      return;
    }
    // dataApi
    //   .getAreaGeneralData({
    //     dataTypeId: "2",
    //     areaId: data.id,
    //     page: 1,
    //     size: 10,
    //   })
    dataApi
      .pageEquipmentByType({
        areaId: data.id,
        category: 2,
        page: 1,
        size: 10,
      })
      .then((res) => {
        if (res.data.code == 200) {
          // ElMessage.error(res.data.message);
          state.dataInfoList[data.id] = res.data.data.records;
          if (state.dataInfoList[data.id].length > 5) {
            state.dataInfoList[data.id].length = 5;
          }
          // pageInfo.total = res.data.data.total;
        } else {
          ElMessage.error(res.data.message);
        }
      });
  },
};
// 获取资源和目标类型对应的generalIdsList 1:资源 2：目标
function getGeneralIdList() {
  dataApi.getGeneralType({ areaType: 1 }).then((res) => {
    if (res.data.code == 200) {
      state.resourcesTypeList = res.data.data;
      resourcesTypeDic = {};
      state.resourcesTypeList.forEach((item) => {
        resourcesTypeDic[item.id] = item.tableComment;
      });
      state.tableColumn[1].SysdicName = resourcesTypeDic;
      console.log(resourcesTypeDic);
    } else {
      ElMessage.error(res.data.message);
    }
  });
  dataApi.getGeneralType({ areaType: 2 }).then((res) => {
    if (res.data.code == 200) {
      state.targetTypeList = res.data.data;
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
// 获取单个域的数据
function getAreaInfo(areaId, index) {
  dataApi.getAreaInfo({ id: areaId }).then((res) => {
    if (res.data.code == 200) {
      state.areaList[index].info = res.data.data;
      // state.areaList = res.data.data.records;
    }
  });
}
function areaDialogSubmit(formRef) {
  formRef.validate((valid, fields) => {
    if (valid) {
      // console.log("submit!");
      addEditArea();
    } else {
      console.log("error submit!", fields);
    }
  });
}
//关闭域弹窗
function areaDialogClose() {
  state.areaForm = {
    areaDescribe: "",
    areaName: "",
    areaType: 2,
    // id: null,
    generalIds: [],
  };
  state.areaDialogShow = false;
}
// 新增/编辑域
function addEditArea() {
  state.loading = true;
  dataApi.addEditArea(state.areaForm).then((res) => {
    if (res.data.code == 200) {
      getAreaList();
      leftChart()
      state.areaDialogShow = false;
    } else {
      ElMessage.error(res.data.message);
    }
    state.loading = false;
  });
}
function editArea(card) {
  console.log(card);
  state.areaForm = { ...card };
  state.areaDialogShow = true;
}
function removeArea(card) {
  ElMessageBox.confirm("确认删除该目标域及后续数据吗", "Warning", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "删除确认",
  }).then(() => {
    removeAreaById(card.id);
  });
}
function removeAreaById(id) {
  dataApi.removeArea({ id: id }).then((res) => {
    if (res.data.code == 200) {
      ElMessage.success("删除成功");
      getAreaList();
      leftChart()
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
//切换视图
function changeView() {
  if (state.viewStatus == "twoCard") {
    state.viewStatus = "gridCard";
    state.currentSize = 4;
    state.cardStyle.width = "49%";
    state.cardStyle.height = "47%";
  } else if (state.viewStatus == "gridCard") {
    state.viewStatus = "twoCard";
    state.currentSize = 2;
    state.cardStyle.width = "48%";
    state.cardStyle.height = "98%";
    // state.viewStatus = "gridCard";
    // state.currentSize = 20;
    // state.cardStyle.width = "18%";
    // state.cardStyle.height = "23%";
  }
  // else if (state.viewStatus == "manyCard") {
  //   state.viewStatus = "twoCard";
  //   state.currentSize = 2;
  //   state.cardStyle.width = "48%";
  //   state.cardStyle.height = "98%";
  // }

  getAreaList();
}
</script>

<style scoped lang="less">
.title-font {
  /* color: blue; */
  font-size: 18px;
  font-weight: bolder;
  color: #c6cdd6;
  line-height: 28px;
}

.leftChartBox {
  height: 100%;

  >div {
    height: 33%;
    margin-bottom: 10px;
  }
}

.leftChart {
  height: calc(100% - 65px);
  width: 100%;
  background: #031527a0;
}

.sub_title {
  margin-top: 15px;
  margin-bottom: 10px;

  // 二级标题#d9dfe8 14px加粗
  // color: #64a1dd;
  font-weight: bold;
  font-size: 15px;
  line-height: 25px;
  height: 25px;
  // font-weight: bolder;
}

.card_all_box2 {
  position: relative;
  height: calc(100% - 70px);
  margin-top: 10px;
  width: 74%;
  left: 25%;
  overflow-x: auto;
  box-sizing: border-box;

  .space {
    position: absolute;
    height: 1px;
    top: 50%;
    width: 5px;
  }
}

.card_all_box {
  height: calc(100% - 0px);
  width: 100%;
  overflow: auto;
  position: relative;
  //
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  justify-content: flex-start;

  .card_box {
    position: relative;
    margin: 0.5%;
  }

  //
  .space {
    position: absolute;
    height: 1px;
    top: 50%;
    width: 5px;
  }
}

.rightCardBox {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: space-around;
  justify-content: center;

  .cardContent {
    display: flex;

    .spanKey {
      color: #3fb5ff;
    }

    >div {
      width: 200px;
    }
  }
}

:deep(.el-card) {
  width: 90%;
  height: 22%;
  border: 2px solid #0080da;
  cursor: pointer;

  .el-card__header {
    color: #285472;
    font-weight: bold;
    border-bottom: 1px solid #0080da;
  }
}

.iconHover {
  cursor: pointer;

  &:hover {
    color: #37a0ea;
  }
}

.cardFlex {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;

  >div {
    box-sizing: border-box;
  }
}

:deep(.el-select__placeholder) {
  color: rgba(217, 223, 232, 0.85) !important;
}

.leftBtn {
  background: transparent !important;
  position: absolute;
  top: 50%;
  opacity: 0.3;
  left: -7px;
  z-index: 1;

  &:hover {
    opacity: 1;
  }
}

.rightBtn {
  background: transparent !important;
  position: absolute;
  top: 50%;
  opacity: 0.3;
  right: 7px;
  z-index: 1;

  &:hover {
    opacity: 1;
  }
}

:deep(.el-progress__text) {
  color: rgba(217, 223, 232, 0.85) !important;
}
</style>
