<template>
    <!-- 图上资源 -->
    <customPopup left="0.5%" top="80px" width="99%" height="90%">
        <template #header>
            <div style="position: absolute; right: 2%; top: 3%; z-index: 9">
                <el-button @click="close">关闭</el-button>
            </div>
        </template>
        <template #content>
            <CesiumBox></CesiumBox>
            <customPopup left="20px" top="25px" width="20%" height="30%">
                <template #header>
                    <span class="title-font">{{ stateData.gisName }}</span>
                </template>
                <template #content>
                    <div style="height: 100%; width: 100%; overflow: auto">
                        <el-row :gutter="0" v-for="(item, index) in stateData.allPositionList" :key="item.id">
                            <el-checkbox :checked="true" @change="(val) => {
                                showEntity(val, item, index);
                            }
                                ">
                                {{ item.name }}
                            </el-checkbox>
                        </el-row>
                    </div>
                </template>
            </customPopup>
        </template>
    </customPopup>
</template>

<script setup>
import { onMounted, onUnmounted, nextTick, onBeforeUnmount, reactive } from 'vue'
import * as dataApi from "@/service/API/system/areaManage.js";
// import CesiumBox from "@/components/evmap/CesiumBox2D.vue";
import CesiumBox from "@/components/evmap/CesiumBox.vue";
import customPopup from "@/components/customPopup.vue";


const props = defineProps({
    areaId: {
        type: [String, Number],
    },
    areaName: {
        type: [String],
    },
    pageType: {
        type: [Number],
    },
})
const emits = defineEmits(["close"]);

const stateData = reactive({
    areaId: undefined,
    areaName: undefined,
    pageType: undefined,
    gisName: undefined,
    allPositionList: []
})

onMounted(() => {
    stateData.areaId = props.areaId;
    stateData.areaName = props.areaName;
    stateData.pageType = props.pageType || 1;
    allPositionFun()
})
onBeforeUnmount(() => {
    mapTool.removeAll();
});
//获取域下所有资源
function allPositionFun() {
    dataApi
        .pageEquipmentByType({
            areaId: stateData.areaId,
            category: 1,
            page: 1,
            size: 999,
        })
        .then((res) => {
            if (res.data.code == 200) {
                stateData.allPositionList = res.data.data.records;
                stateData.gisName = stateData.areaName;
                setTimeout(() => {
                    mapTool.removeAll();
                    stateData.allPositionList.forEach((row, index) => {
                        mapTool.drawMap({ ...row, equipmentId: row.equipmentId ? row.equipmentId : row.id });
                    });
                }, 1000);
            } else {
                ElMessage.error(res.data.message);
            }
        });
}
//控制图上资源显隐
function showEntity(show, row, index) {
    mapTool.showEntityById(row.id, show);
};
function close() {
    emits("close")
}
</script>

<style lang='stylus' scoped></style>