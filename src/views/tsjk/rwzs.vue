<!-- 实时态势监控页面 -->
<template>
  <!-- <div class="topText" v-show="!state.dialogShow" :data-content="textTitle">
    {{ textTitle }}
  </div> -->
  <div class="showOrHidden-box" v-if="!state.dialogShow">
    <div>
      <label>航迹显隐：</label>
      <el-switch
        v-model="state.showTrack"
        inline-prompt
        active-text="显示"
        inactive-text="隐藏"
      />
    </div>
    <div>
      <label>连接线显隐：</label>
      <el-switch
        v-model="state.showFlowLine"
        inline-prompt
        active-text="显示"
        inactive-text="隐藏"
      />
    </div>
    <div>
      <label>范围显隐：</label>
      <el-switch
        v-model="state.showArea"
        inline-prompt
        active-text="显示"
        inactive-text="隐藏"
      />
    </div>
    <div>
      <label>名称显隐：</label>
      <el-switch
        v-model="state.showLabel"
        inline-prompt
        active-text="显示"
        inactive-text="隐藏"
      />
    </div>
  </div>
  <customPopup
    left="0.5%"
    top="80px"
    width="18%"
    height="90%"
    :z-index="1"
    v-if="!state.dialogShow"
  >
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">目标、资源列表</span>
    </template>
    <template #content>
      <div style="height: 100%; width: 100%; overflow: auto">
        <el-tree
          :data="state.treeData"
          node-key="id"
          default-expand-all
          ref="treeRef"
        >
          <!-- @node-click="treeRefNodeClick" -->

          <!-- :check-strictly="true" -->
          <template #default="{ node, data }">
            <el-checkbox
              v-model="data.isShow"
              v-if="data.isTask || data.isLeaf"
              @change="
                (val) => {
                  treeRefNodeClick(data, val)
                }
              "
              @click.stop
            >
              {{ '' }}
            </el-checkbox>
            <span class="custom-tree-node">
              <span>{{ node.label }}</span>
              <!-- <span>
                <span v-if="data.start" class="StartSpan">{{ data.start }}</span>
                <span class="endSpan" v-if="!data.children">
                  <el-tooltip effect="dark" content="显隐控制" placement="bottom">
                    <el-icon @click="">
                      <Document />
                    </el-icon>
                  </el-tooltip>
                </span>
              </span> -->
            </span>
          </template>
        </el-tree>
      </div>
    </template>
  </customPopup>
  <div class="taskOperation" v-show="!state.dialogShow">
    <span class="demandImgSpan" style="width: 60px; margin-right: 4px">
      <div
        style="
          height: 27px;
          width: 60px;
          background: #0080db;
          line-height: 28px;
          text-align: center;
          border-radius: 5px;
        "
      >
        {{ state.playSpeed }}{{ state.playSpeed > 1 ? '.0' : '' }}x
      </div>
    </span>

    <el-tooltip
      class="box-item"
      effect="light"
      content="减速"
      placement="bottom"
    >
      <span
        class="demandImgSpan"
        @click="
          () => {
            state.playSpeed >= 0.5 && (state.playSpeed = state.playSpeed / 2)
          }
        "
      >
        <img style="height: 28px" src="/images/icon/speedDown.png" alt="" />
      </span>
    </el-tooltip>
    <el-tooltip
      class="box-item"
      effect="light"
      content="启动"
      placement="bottom"
      v-if="state.playStatus != 'play'"
    >
      <span class="demandImgSpan" @click="startTask">
        <img style="height: 28px" src="/images/icon/play.png" alt="" />
      </span>
    </el-tooltip>
    <el-tooltip
      class="box-item"
      effect="light"
      content="暂停"
      placement="bottom"
      v-if="state.playStatus == 'play'"
    >
      <span class="demandImgSpan" @click="suspendTask">
        <img style="height: 28px" src="/images/icon/pause.png" alt="" />
      </span>
    </el-tooltip>
    <!-- <span class="demandImgSpan" @click="adjustRatio">
      {{ speedRatio }}
    </span> -->
    <el-tooltip
      class="box-item"
      effect="light"
      content="停止"
      placement="bottom"
    >
      <span class="demandImgSpan" @click="stopTask" style="margin-top: 1px">
        <img style="height: 28px" src="/images/icon/stop.png" alt="" />
      </span>
    </el-tooltip>

    <el-tooltip
      class="box-item"
      effect="light"
      content="加速"
      placement="bottom"
    >
      <span
        class="demandImgSpan"
        @click="
          () => {
            state.playSpeed < 128 && (state.playSpeed = state.playSpeed * 2)
          }
        "
      >
        <img style="height: 28px" src="/images/icon/speedUp.png" alt="" />
      </span>
    </el-tooltip>
  </div>
  <div v-show="!state.dialogShow">
    <customPopup
      left="19%"
      bottom="calc(100% - 90% - 80px)"
      width="80.5%"
      height="300px"
      :z-index="1"
    >
      <template #header>
        <span class="list_icon"></span>
        <span class="title-font">仿真推演</span>
      </template>
      <template #content>
        <div class="taskChart taskChart1">
          <div class="taskTimeLine">
            <div id="my-timeline" style="width: 100%; height: 210px"></div>
          </div>
          <div id="main" ref="Chart"></div>
        </div>
      </template>
    </customPopup>
  </div>

  <CesiumBox></CesiumBox>
  <!-- 弹窗 -->
  <div>
    <el-dialog
      v-model="state.dialogShow"
      title="选择需求"
      width="60%"
      center
      :close-on-click-modal="false"
      append-to-body
    >
      <div style="height: 90%" class="require">
        <el-form-item label="筛选条件：">
          <el-radio-group
            v-model="state.dialogRadio"
            @change="dialogRadioChange"
          >
            <el-radio :label="1" size="large">指定需求</el-radio>
            <el-radio :label="2" size="large">时间范围</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="需求名称：" v-show="state.dialogRadio == 1">
          <el-select
            v-model="state.currentRequirement"
            @change="serachTask"
            placeholder="请选择对应的需求"
            multiple
            size="large"
            class="selectHeight"
          >
            <el-option
              v-for="item in state.requirementList"
              :key="item.id"
              :label="item.requirementName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围：" v-show="state.dialogRadio == 2">
          <el-date-picker
            v-model="state.dateTimeRange"
            type="datetimerange"
            range-separator="至"
            @change="serachTask"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="任务列表：">
          <el-table
            :data="state.taskList"
            class="custom-table"
            @selection-change="selectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              label="任务名称"
              prop="taskName"
              align="center"
            ></el-table-column>
            <el-table-column
              label="开始时间"
              prop="startTime"
              align="center"
            ></el-table-column>
            <el-table-column
              label="结束时间"
              prop="endTime"
              align="center"
            ></el-table-column>
          </el-table>
        </el-form-item>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button style="width: 80px" type="primary" @click="saveAdd"
            >确认</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import CesiumBox from '@/components/evmap/CesiumBox.vue'
import customPopup from '@/components/customPopup.vue'
import { onMounted, reactive, watch, ref, onUnmounted, nextTick } from 'vue'
import customTable from '@/components/customTable.vue'

import * as homeApi from '@/service/API/home/<USER>'
import * as areaApi from '@/service/API/system/areaManage.js'
import * as situationApi from '@/service/API/system/situation.js'
import { ElMessage, ElLoading } from 'element-plus'
import * as resourceApi from '@/service/API/system/resourceDispatch.js'
// import * as mapTool from "@/utils/mapTool.js";

const state = reactive({
  requirementList: [], //需求列表
  currentRequirement: [], //当前选择的需求IDs
  currentRequirementInfo: {}, //当前选择的需求信息
  dialogShow: true, //需求选择弹窗
  treeData: [], //树状结构
  targetList: [], //目标数据
  resourceList: [], //资源数据
  mapList: [],
  currentTime: null, //当前态势时间
  playStatus: 'stop', //play:播放, pause:暂停,stop
  playSpeed: 1, //播放倍速
  dialogRadio: 1,
  dateTimeRange: [], //时间范围
  taskList: [], //筛选后的任务列表
  selectList: [], //被选中的任务
  getting: false,
  areaList: [],
  initEntity: [], //初始的位置，用于停止后回到起点
  showTrack: false, // 显隐航迹
  showFlowLine: true, // 显隐连接线
  showArea: true, // 显隐范围
  showLabel: true, //名称显隐
})
var visGroups //
//时间轴分组
var visItems //时间轴项目
var timeline //时间轴管理器
var TSinterval //态势推演轮询器

onMounted(() => {
  getRequirementList()
  getAreaList()

  // 初始化全局连接线显隐状态
  window.flowLineShow = state.showFlowLine
})
function getAreaList() {
  areaApi.getAreaList({ page: 1, size: 999 }).then((res) => {
    if (res.data.code == 200) {
      state.areaList = res.data.data.records
    }
  })
}
//切换清空条件和列表
function dialogRadioChange() {
  state.currentRequirement = []
  state.dateTimeRange = []
  state.taskList = []
  state.selectList = []
}
/**
 * @description
 */
function selectionChange(list) {
  state.selectList = list
}
/**
 * @description 获取需求列表
 */
function getRequirementList() {
  homeApi.getRequirementList({ pageNum: 1, pageSize: 999 }).then((res) => {
    if (res.data.code == 200) {
      state.requirementList = res.data.data.records
      state.currentRequirement = []
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
let clacNum = 0 //模拟双击
// 设置时间轴范围和缩放级别
function setTimelineRange() {
  if (!timelineStart || !timelineEnd) return

  // 计算时间跨度（毫秒）
  const duration = moment(timelineEnd).diff(moment(timelineStart))
  const durationHours = duration / (1000 * 60 * 60) // 转换为小时
  const durationDays = duration / (1000 * 60 * 60 * 24) // 转换为天

  let min, max, zoomMin, zoomMax

  if (durationHours <= 24) {
    // 24小时以内：显示小时级别
    min = moment(timelineStart).subtract(2, 'hours')
    max = moment(timelineEnd).add(2, 'hours')
    zoomMin = 1000 * 60 * 10 // 最小缩放：10分钟
    zoomMax = 1000 * 60 * 60 * 48 // 最大缩放：48小时
  } else if (durationDays <= 7) {
    // 7天以内：显示天级别
    min = moment(timelineStart).subtract(1, 'day')
    max = moment(timelineEnd).add(1, 'day')
    zoomMin = 1000 * 60 * 60 // 最小缩放：1小时
    zoomMax = 1000 * 60 * 60 * 24 * 14 // 最大缩放：14天
  } else if (durationDays <= 30) {
    // 30天以内：显示周级别
    min = moment(timelineStart).subtract(3, 'days')
    max = moment(timelineEnd).add(3, 'days')
    zoomMin = 1000 * 60 * 60 * 6 // 最小缩放：6小时
    zoomMax = 1000 * 60 * 60 * 24 * 60 // 最大缩放：60天
  } else {
    // 30天以上：显示月级别
    min = moment(timelineStart).subtract(1, 'week')
    max = moment(timelineEnd).add(1, 'week')
    zoomMin = 1000 * 60 * 60 * 24 // 最小缩放：1天
    zoomMax = 1000 * 60 * 60 * 24 * 365 // 最大缩放：1年
  }

  timeline.setOptions({
    start: timelineStart,
    end: timelineEnd,
    min: min.toDate(),
    max: max.toDate(),
    zoomMin: zoomMin,
    zoomMax: zoomMax
  })

  // 设置当前时间指示器
  timeline.setCustomTime(timelineStart)
}

function initTimeLine() {
  var container = document.getElementById('my-timeline')
  timeline = new vis.Timeline(container)
  timeline.on('click', function (item) {
    if (state.playStatus == 'stop') {
      return
    }
    clacNum = clacNum + 1
    setTimeout(() => {
      clacNum = 0
    }, 200)
    if (clacNum == 2) {
      timeline.setCustomTime(item.time)
      drag = true
      state.currentTime = moment(item.time).format('YYYY-MM-DD HH:mm:ss')
    }
  })

  visGroups = new vis.DataSet([])
  visItems = new vis.DataSet([])
  // let start = moment(timelineStart).format("YYYY-MM-DD HH:mm:ss");
  // let min = moment(timelineStart).subtract(1, "h").format("YYYY-MM-DD HH:mm:ss")
  // let end = moment(timelineEnd).format("YYYY-MM-DD HH:mm:ss");
  let options = {
    autoResize: true,
    height: '220px', //高度
    width: '97%', //宽度
    min: Date.now(), //设置最小时间范围
    max: Date.now(), //设置最大时间范围
    start: Date.now(), //设置开始时间
    end: Date.now(), //设置结束时间
    stack: true, // ture则不重叠
    limitSize: true,
    verticalScroll: true,
    // cluster: true, //数据量过大时使用。
    locale: 'zh-cn',
    xss: {
      disabled: true,
    },
    groupOrder: function (a, b) {
      return a.value - b.value
    },
    editable: false,
    showCurrentTime: true,
    moment: function (date) {
      // return moment(date).format("YYYY-MM-DD HH:mm:ss");
      return moment(date)
    },
    locale: moment.locale('zh-cn'),
    format: {
      //设置展示形式
      minorLabels: {
        millisecond: "SSS",
        second: "HH:mm:ss",
        minute: "HH:mm",
        hour: "HH:mm",
        weekday: "ddd D",
        day: "D",
        week: "w",
        month: "MMM",
        year: "YYYY",
      },
      majorLabels: {
        millisecond: "HH:mm:ss",
        second: "D MMMM",
        minute: "D MMMM",
        hour: "D MMMM",
        weekday: "MMMM YYYY",
        day: "MMMM YYYY",
        week: "MMMM YYYY",
        month: "YYYY",
        year: "",
      },
    },
  }
  timeline.setOptions(options)
  // window.timeline = timeline;
  // timeline.setCustomTime(moment(state.currentRequirementInfo.startTime));
  setTimeout(() => {
    // console.log(state.currentRequirementInfo.startTime);
    // timeline.setCurrentTime(state.currentRequirementInfo.startTime);
    timeline.addCustomTime(timelineStart)
    // timeline.moveTo(new Date(state.currentRequirementInfo.startTime));
  }, 1000)
}

function serachTask() {
  let obj = {
    page: 1,
    size: 999,
    requirementIds: state.currentRequirement,
    startTime: state.dateTimeRange[0] || null,
    endTime: state.dateTimeRange[1] || null,
  }
  state.selectList = []
  homeApi.getTaskList(obj).then((res) => {
    if (res.data.code == 200) {
      state.taskList = res.data.data.records
    }
  })
}
var timelineStart = 0,
  timelineEnd = 0,
  groups = [],
  items = []
/**
 * @description 确认选择需求
 */
function saveAdd() {
  // if (!state.currentRequirement) {
  //   ElMessage.error("请选择一个需求");
  //   return;
  // }
  if (state.selectList.length == 0) {
    ElMessage.error('请至少选择一个任务')
    return
  }

  // 显示loading
  const loading = ElLoading.service({
    lock: true,
    text: '正在加载任务数据，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)',
  })

  state.dialogShow = false
  timelineStart = 0
  timelineEnd = 0
  state.treeData = []
  state.targetList = []
  state.resourceList = []
  // state.selectList.forEach((item) => {
  //   if (item.startTime < timelineStart || timelineStart == 0) {
  //     timelineStart = item.startTime;
  //   }
  //   // if (item.endTime > timelineEnd || timelineEnd == 0) {
  //   //   timelineEnd = item.endTime;
  //   // }
  // });
  initTimeLine()
  groups = []
  items = []

  // const panoramaId = state.selectList[0].requirementId
  // const height = FlyToHeightConfig[panoramaId] || 1000000
  // const position = FlyToHeightConfig[panoramaId+'-position']
  // position && Viewer.camera.flyTo({ destination: Cesium.Cartesian3.fromDegrees(position[0], position[1], height) })

  // 使用批量接口获取任务详情
  const taskIds = state.selectList.map(item => item.id)
  const requirementIds = [...new Set(state.selectList.map(item => item.requirementId))]
  homeApi.getTaskListByIds({ requirementIds, taskIds }).then((res) => {
    if (res.data.code == 200) {
      const taskList = res.data.data
      taskList.forEach((taskItem, selectIndex) => {
        if (selectIndex == 0) {
          // 根据任务定位
          const panoramaId = state.selectList[0].requirementId
          const height = FlyToHeightConfig[panoramaId] || 4000 // 1000000
          const position = FlyToHeightConfig[panoramaId + '-position'] || [
            taskItem.target.longitude,
            taskItem.target.latitude,
          ]
          position &&
            Viewer.camera.flyTo({
              destination: Cesium.Cartesian3.fromDegrees(
                position[0],
                position[1],
                height
              ),
            })
        }
        //获取时间范围
        state.treeData.push({
          label: taskItem.taskName,
          id: taskItem.id,
          info: taskItem,
          isTask: true,
          isShow: true,
          // disabled: true,
          children: [
            {
              label: '目标',
              id: taskItem.id + 'target',
              children: [],
              // disabled: true,
            },
            {
              label: '资源',
              id: taskItem.id + 'resource',
              children: [],
              // disabled: true,
            },
          ],
        })

        if (taskItem.target.startTime < timelineStart || timelineStart == 0) {
          timelineStart = taskItem.target.startTime
        }

        if (taskItem.target.endTime > timelineEnd || timelineEnd == 0) {
          timelineEnd = taskItem.target.endTime
        }
        if (taskItem.target) {
          visItems.add({
            id: taskItem.id,
            group: taskItem.target.id,
            content: taskItem.taskName,
            start: taskItem.startTime,
            end: taskItem.endTime,
          })
        }

        // 获取资源信息
        // getUsedResourceList(taskItem)
      })

      // 批量获取任务目标信息
      homeApi.getTaskTargetListByIds(taskIds).then((targetRes) => {
        if (targetRes.data.code == 200) {
          const targetList = targetRes.data.data
          targetList.forEach((targetInfo, index) => {
            getUsedResourceList(targetInfo)
            let taskId = targetInfo.taskId+''
            targetInfo = targetInfo.target
            state.targetList = [...state.targetList, targetInfo]

            let taskIndex = state.treeData.findIndex((item) => {
              return item.id == taskId
            })
            if (taskIndex !== -1) {
              state.treeData[taskIndex].children[0].children.push({
                label: targetInfo.name,
                info: targetInfo,
                id: targetInfo.id,
                isLeaf: true,
                isShow: true,
              })
            }

            let info = { ...targetInfo }
            info.dataId = info.id
            info.id = info.targetId
            mapTool.drawMap({ ...info,lineShow:state.showTrack, color: 'red', startTime: timelineStart })

            if (!groups.includes(targetInfo.id)) {
              visGroups.add({
                id: targetInfo.id,
                content: targetInfo.name,
                value: targetInfo.id,
                className: 'vis_class_group_' + (index % 16),
              })
              groups.push(targetInfo.id)
            }
          })

          timeline.setGroups(visGroups)
          timeline.setItems(visItems)

          // 设置时间轴范围和缩放级别
          setTimelineRange()

          // 关闭loading
          loading.close()
        } else {
          ElMessage.error(targetRes.data.message)
          // 关闭loading
          loading.close()
        }
      }).catch(() => {
        // 关闭loading
        loading.close()
      })
    } else {
      ElMessage.error(res.data.message)
      // 关闭loading
      loading.close()
    }
  }).catch(() => {
    // 关闭loading
    loading.close()
  })
}
const lineTestData = {}

//获取任务下已使用资源
function getUsedResourceList(row) {
  // ElMessage("查询已使用资源");
        if (row) {
        let resourceList = []
        if (!row.equipments||row.equipments.length==0) return
       row.equipments.forEach((item) => {
          let areaName = state.areaList.find((area) => {
            return area.id == item.areaId
          })?.areaName
          item.id = item.equipmentDetail.id
          item.equipmentDetail.name =
            areaName + '--' + item.equipmentDetail.name
          // delete item.equipmentDetail.id;
          resourceList.push({ ...item, ...item.equipmentDetail })
        })
        // state.resourceList = JSON.parse(JSON.stringify(res.data.data.records));
        state.resourceList = [...state.resourceList, ...resourceList]
        let taskIndex = state.treeData.findIndex((item) => {
          return item.id == row.taskId
        })
        let index = state.treeData.findIndex((item) => {
          return row.taskId == item.id
        })
        resourceList.forEach((item) => {
		  if(!lineTestData[row.target.targetId]){
			  lineTestData[row.target.targetId] = [item.id]
		  }else{
			  lineTestData[row.target.targetId].push(item.id)
		  }
          state.treeData[taskIndex].children[1].children.push({
            label: item.equipmentDetail?.name,
            info: item,
            id: item.id,
            isLeaf: true,
            isShow: true,
            isResource: true,
          })
          // state.mapList.push({ ...item, name: item.equipmentDetail?.name })
          mapTool.drawMap({
            ...item,
            lineShow:state.showTrack,
            name: item.equipmentDetail?.name,
            startTime: timelineStart,
          })
          // setTimeout(() => {
          // treeRef.value.setChecked(item.id, true);
          // }, 0);
        })
      }
  return
  resourceApi
    .getEquipmentByTask({ taskId: row.id, page: 1, size: 999 })
    .then((res) => {
      if (res.data.code == 200) {
        let resourceList = []
        res.data.data.records.forEach((item) => {
          let areaName = state.areaList.find((area) => {
            return area.id == item.areaId
          })?.areaName
          item.id = item.equipmentDetail.id
          item.equipmentDetail.name =
            areaName + '--' + item.equipmentDetail.name
          // delete item.equipmentDetail.id;
          resourceList.push({ ...item, ...item.equipmentDetail })
        })
        // state.resourceList = JSON.parse(JSON.stringify(res.data.data.records));
        state.resourceList = [...state.resourceList, ...resourceList]
        let taskIndex = state.treeData.findIndex((item) => {
          return item.id == row.id
        })
        let index = state.treeData.findIndex((item) => {
          return row.id == item.id
        })
        resourceList.forEach((item) => {
          state.treeData[taskIndex].children[1].children.push({
            label: item.equipmentDetail?.name,
            info: item,
            id: item.id,
            isLeaf: true,
            isShow: true,
            isResource: true,
          })
          // state.mapList.push({ ...item, name: item.equipmentDetail?.name })
          mapTool.drawMap({
            ...item,
            lineShow:state.showTrack,
            name: item.equipmentDetail?.name,
            startTime: timelineStart,
          })
          // setTimeout(() => {
          // treeRef.value.setChecked(item.id, true);
          // }, 0);
        })
      } else {
        ElMessage.error(res.data.message)
      }
    })
}
// 显隐航迹
watch(
  () => state.showTrack,
  (val) => {
    window.lineShow = val
    mapTool.showEffByAll('line', val)
  }
)
// 显隐连接线
watch(
  () => state.showFlowLine,
  (val) => {
    // 设置全局连接线显隐状态
    window.flowLineShow = val
    mapTool.showFlowLine(val)
  }
)
// 显隐雷达
watch(
  () => state.showArea,
  (val) => {
    window.radarShow = val
    mapTool.showEffByAll('radar', val)
    mapTool.showEffByAll('cylinder', val)
  }
)
// 显隐名称
watch(
  () => state.showLabel,
  (val) => {
    window.nameShow = val
    mapTool.showEffByAll('label', val)
  }
)

function treeRefNodeClick(node, show) {
  if (node.isTask) {
    node.children.forEach(({ children }) => {
      children.forEach((ele) => {
        ele.isShow = node.isShow
        const id = ele.isResource === true ? ele.id : ele.info.targetId
        mapTool.showEntityById(id, show)
      })
    })
    return
  }
  const id = node.isResource === true ? node.id : node.info.targetId
  // let keys = treeRef.value.getCheckedKeys(true);
  mapTool.showEntityById(id, show)
  // syncMap(keys);
}
// function treeRefNodeClick(node) {
//   let keys = treeRef.value.getCheckedKeys(true);
//   syncMap(keys);
// }
/**
 * @description 同步树上勾选和地图上数据
 * @param  keys 左侧选择树的id集合
 */
function syncMap(keys) {
  // console.log("resourceList", state.resourceList);
  keys.forEach((id) => {
    if (!state.mapList.includes(id)) {
      let option =
        state.targetList.find((item) => {
          return item.id == id
        }) ||
        state.resourceList.find((item) => {
          return item.id == id
        })
      // console.log("option", option);
      mapTool.drawMap({ ...Option,lineShow:state.showTrack, startTime: timelineStart })
    }
  })
  state.mapList.forEach((id) => {
    if (!keys.includes(id)) {
      mapTool.removeById(id)
    }
  })
  state.mapList = [...keys]
}

let isShow = ref(false)
const SelectValue = ref('')
var textTitle = ref('')
const options = [
  {
    value: '1',
    label: '探测任务-1',
  },
  {
    value: '2',
    label: '探测任务-2',
  },
  {
    value: '3',
    label: '探测任务-3',
  },
  {
    value: '4',
    label: '探测任务-4',
  },
  {
    value: '5',
    label: '探测任务-5',
  },
]
const treeRef = ref()

let checked1 = ref('1')
import { useRouter } from 'vue-router'

import { dataType } from 'element-plus/es/components/table-v2/src/common.mjs'
const router = useRouter()

onUnmounted(() => {
  console.log('onUnmountedonUnmountedonUnmounted')
  mapTool.removeAll()
  TSinterval && clearInterval(TSinterval)
  TSinterval = undefined
  // window.intervalccc = undefined;
})
let dataList = []

let Chart = ref(null)
let drag = false // 是否是被拖拽过的时间轴
var posIndex = 0
var timePoint = 0
var uuid = null
var loading
var totalTime = undefined
//播放按钮
function startTask() {
  state.playStatus = 'play'

  if (!TSinterval) {
    state.currentTime = timelineStart
    uuid = parseInt(Math.random() * 100000000000)
    loading = ElLoading.service({
      lock: true,
      text: '推演初始化中，请稍候......',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    totalTime = undefined
    TSinterval = setInterval(() => {
      if (state.playStatus == 'play') {
        // state.currentTime = moment(state.currentTime).add("1", "s").format("YYYY-MM-DD HH:mm:ss");
        callServer()
      } else if (state.playStatus == 'stop') {
        timeline?.setCustomTime(timelineStart || new Date())
        state.currentTime = timelineStart || new Date()
        clearInterval(TSinterval)
        TSinterval = null
      } else if (state.playStatus == 'pause') {
        timeline?.setCustomTime(state.currentTime)
      }
    }, 1000)
  }
}
//暂停按钮
function suspendTask() {
  state.playStatus = 'pause'
}
//停止按钮
function stopTask() {
  state.playStatus = 'stop'
  clearInterval(TSinterval)
  TSinterval = null
  timePoint = 0
  timeline?.setCustomTime(timelineStart)
  state.targetList.forEach((item) => {
    let obj = { ...item }
    obj.id = item.targetId
    mapTool.drawMap({ ...obj,lineShow:state.showTrack })
  })
  mapTool.removeAllFlowLine()
}

function callServer() {
  if (state.getting) {
    return
  }
  // timePoint = timePoint + 1;
  // state.currentTime;
  let ids = state.selectList.map((item) => {
    return item.id
  })
  let timeX =
    parseInt(
      new Date(state.currentTime).getTime() / 1000 -
        new Date(timelineStart).getTime() / 1000
    ) + 1
  if (totalTime && timeX >= totalTime) {
    timeX = totalTime
  }
  let obj = {
    // timePoint: moment(state.currentTime).add("1", "s").format("YYYY-MM-DD HH:mm:ss"),
    timePoint: timeX,
    // requirementId: state.currentRequirementInfo.id,
    taskIds: ids,
    drag: drag || false,
    step: state.playSpeed,
    bzId: uuid,
  }
  drag = false
  state.getting = true
  mapTool.removeAllFlowLine()
  situationApi.dragOrPlay(obj).then((res) => {
    if (state.playStatus == 'stop') {
      return
    }
    if (res.data.code == 200) {
      let data = res.data.data
      state.currentTime = moment(state.currentTime)
        .add(1 * state.playSpeed, 's')
        .format('YYYY-MM-DD HH:mm:ss')
      state.getting = false
      timeline?.setCustomTime(state.currentTime)
      if (loading) {
        loading.close()
        loading = null
      }
      for (const key in data.satellite) {
        let item = data.satellite[key]
        let info = state.resourceList.find((item) => {
          return item.id == key
        })
        // 1816714679375142914
        // if(!info){
        //   info = {

        //   }
        // }
        mapTool.drawMap({
          ...info,
          lineShow:state.showTrack,
          position: [item.degLon, item.degLat, item.alt * 1000],
          time: state.currentTime,
        })
      }
      //更新实体位置
      data.target.forEach((item) => {
        for (const target of state.targetList) {
          if (target.targetId == item.mbId) {
            let option = { ...target, id: target.targetId }
            option.longitude = item.points[0].x
            option.latitude = item.points[0].y
            option.altitude = item.points[0].z
            option.direction = item.points[0].angle

            option.name = state.showLabel ? option.name : ''
            mapTool.drawMap({...option,lineShow:state.showTrack })
            break
          }
        }
      })
      //绘制流动线
      data.targetEquipmentRelation.forEach((item) => {
        // #808080b0
        // let showResourceIds = state.showResourceList.map(x => {
        //   return x.equipmentId
        // })
        // let showTargetIds = state.showTargetList.map(x => {
        //   return x.id
        // })
        // if (showTargetIds.includes(item.targetId) && showResourceIds.includes(item.equipmentId)) {
        // ff000090
        // 检查是否已存在相同的流动线，避免重复绘制
        const lineId1 = `${item.targetId}_${item.equipmentId}`
        const lineId2 = `${item.equipmentId}_${item.targetId}`

        if (window.flowLineList && window.flowLineList.length > 0) {
          const existingLine = window.flowLineList.find(line =>
            line.id === lineId1 || line.id === lineId2 ||
            (line.id.includes(item.targetId) && line.id.includes(item.equipmentId))
          )
          if (existingLine) {
            return // 已存在相同的流动线，跳过绘制
          }
        }

        // 只有在连接线显示状态为true时才绘制
        if (window.flowLineShow !== false && state.showFlowLine) {
			const lineLit = lineTestData[item.targetId] || []
			if(lineLit.includes(item.equipmentId)){
				// 绘制流动线
				mapTool.drawflowLine(item.targetId, item.equipmentId, '#100bef')
				mapTool.drawflowLine(item.equipmentId, item.targetId, '#100bef')
			}
          // // 绘制流动线
          // mapTool.drawflowLine(item.targetId, item.equipmentId, '#100bef')
          // mapTool.drawflowLine(item.equipmentId, item.targetId, '#100bef')
        }

        // 将新绘制的流动线ID添加到列表中，避免重复绘制
        if (!window.flowLineList) {
          window.flowLineList = []
        }
        window.flowLineList.push({ id: lineId1 })
        window.flowLineList.push({ id: lineId2 })
      })
      totalTime = data.totalTime
    } else {
      ElMessage.error(res.data.message)
      state.getting = false
      state.playStatus == 'stop'
      if (loading) {
        loading.close()
        loading = null
      }
    }
  })
}
function selectValueChange(val) {
  textTitle = options.find((item) => {
    return item.value == val
  })?.label
}
</script>

<style scoped lang="less">
:deep(.el-tree-node__children) {
  background: transparent !important;
}

.showOrHidden-box {
  position: absolute;
  right: 50px;
  top: 90px;
  z-index: 999;
}

.topText {
  position: absolute;
  top: 72px;
  left: 50%;
  // width: 100%;
  text-align: center;
  transform: translateX(-50%);
  font-size: 18px;
  z-index: 1;
  color: #fff;
  font-weight: bold;
  padding: 3px;
  background: #3b96f050;
  // -webkit-text-stroke: 1px black;
  // text-shadow: 3px 3px 20px #0c117b;
}

@realTimeTaskHeight: 300px;

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.el-form-item__label {
  color: aqua !important;
}

.is-icon {
  margin-left: 2px;
}

.custom-table {
  height: 200px !important;
}

:deep(.el-table .el-table__body .cell) {
  color: #285472;
  /* font-weight: bold; */
  font-size: 16px;
}

:deep(.el-form-item) {
  margin-right: 0px;
}

:deep(.el-form-item__label) {
  // color: #285472;
  font-weight: bolder;
  font-size: 16px;
}

:deep(.el-tree) {
  font-size: 16px;

  .custom-tree-node {
    font-size: 18px;
  }

  .StartSpan {
    margin-right: 10px;
    font-size: 14px;
  }

  .endSpan {
    margin-right: 30px;
    font-size: 14px;
    cursor: pointer;
    color: #007acc;
  }
}

.taskOperation {
  position: absolute;
  display: flex;
  height: 40px;
  width: 200px;
  z-index: 10;
  bottom: calc(@realTimeTaskHeight + 15px);
  left: 50%;

  .demandImgSpan {
    cursor: pointer;
    margin-right: 5px;
    text-align: center;
    line-height: 28px;
    width: 28px;
    height: 28px;
    font-size: 14px;
    font-weight: bold;
    color: #09b3ee;
    // border: 2px solid #205685;
    // background-color: #063462;
    border-radius: 4px;
  }
}

@media screen {
  .vis_class_group_for(15);
}

.vis_class_group_for(@n, @i: 0) when (@i<=@n) {
  :deep(.vis_class_group_@{i} > .vis-item) {
    background: rgb(20, 8 * @i+8, 8 * @i+18) !important;
  }

  .vis_class_group_for(@n, (@i+1));
}

.taskTimeLine {
  :deep(.vis-panel) {
    .vis-custom-time {
      background-color: red;
    }

    .vis-foreground .vis-group,
    .vis-labelset .vis-label,
    &.vis-center,
    &.vis-left {
      border-color: #0794ff !important;
    }
  }
}
</style>
