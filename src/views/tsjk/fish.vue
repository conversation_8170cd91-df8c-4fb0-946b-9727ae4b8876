<template>
    <div class="fishbone">
        <div class="content">
            <el-row type="flex" class="top-bone">
                <div class="item-bone" v-for="(item, index) in state.arrList" :key="index">
                    <ul class="item-bone-children" v-if="index % 2 == 0">
                        <!-- <div v-if="index % 2 == 0" class="textTop">{{ state.text }}</div> -->
                        <!-- <el-result icon="success" v-if="index % 2 == 0" class=""></el-result> -->
                        <!-- <el-result :icon="item.value ? 'success' : 'error'"
                        v-if="index % 2 == 0" class="textTop"></el-result> -->
                        <li class="children-item">
                            <!-- <div v-if="ele.title" class="title"> {{ ele.title }}</div> -->
                            <div class="text">{{ item.label }}
                                <el-icon v-if="item.value" size="16px" class="sucess_icon">
                                    <CircleCheckFilled />
                                </el-icon>
                                <el-icon v-else size="16px" class="close_icon">
                                    <CircleCloseFilled />
                                </el-icon>
                            </div>
                        </li>
                    </ul>
                    <!-- <ul class="item-bone-children item-bone-children-right" v-if="index % 2 == 0">
                        <li v-for="(ele, i) in item.right" class="children-item-right" :key="i">
                            <div v-if="ele.title" class="title"> {{ ele.title }}</div>
                            <div class="text" v-else>{{ ele.label }}</div>
                        </li>
                    </ul> -->
                </div>
            </el-row>
            <div class="center-line">
                <!-- <div class="textCenter">{{ state.text }}</div> -->
            </div>
            <el-row type="flex" class="bottom-bone">
                <div class="item-bone" v-for="(item, index) in state.arrList" :key="index">
                    <ul class="item-bone-children" v-if="index % 2 != 0">
                        <!-- <div v-if="index % 2 != 0" class="textBottom">{{ state.text }}</div> -->

                        <!-- <el-result :icon="item.value ? 'success' : 'error'"
                        v-if="index % 2 != 0" class="textBottom"></el-result> -->
                        <li class="children-item1">
                            <!-- <div v-if="ele.label" class="title">
                                {{ ele.label }}
                            </div> -->
                            <div class="text">{{ item.label }}
                                <el-icon v-if="item.value" size="16px" class="sucess_icon">
                                    <CircleCheckFilled />
                                </el-icon>
                                <el-icon v-else size="16px" class="close_icon">
                                    <CircleCloseFilled />
                                </el-icon>
                            </div>
                        </li>
                    </ul>
                    <!-- <ul class="item-bone-children item-bone-children-right" v-if="index % 2 != 0">
                        <li v-for="(ele, i) in item.right" :key="i" class="children-item2">
                            <div v-if="ele.title" class="title">
                                {{ ele.title }}
                            </div>
                            <div class="text" v-else>{{ ele.label }}</div>
                        </li>
                    </ul> -->
                </div>
            </el-row>
        </div>
    </div>
</template>
<script setup>
import { onMounted, onUnmounted, ref, reactive, watch } from 'vue'
const state = reactive({
    text: '字',
    strip_num: 8, //根数
    // wing_num: 1, //翅数
    arrList: [],
    statusList: [],//状态列表
})

const props = defineProps({
    statusObj: {
        type: Object,
        default: () => {
            return {}
        },
    },
})
watch(statusObj => {
    state.statusList = []
    for (let key in props.statusObj) {
        state.statusList.push({
            label: key,
            value: props.statusObj[key]
        })
    }
    state.wing_num = state.statusList.length
    calcStatus()
})
onMounted(() => {
    calcStatus()
})
function calcStatus() {
    let arr = [];
    // let gObj = {
    //     // label: '',
    //     left: [],
    //     right: [],
    // };
    let cObj = {
        label: '请输入文字',
    };
    let wing_num = parseInt(state.wing_num) ?? 1;
    // let strip_num = parseInt(state.strip_num) ?? 5;
    for (let index = 0; index < wing_num; index++) {
        arr.push({ ...state.statusList[index] });
        // gObj.right.push(cObj);
    }
    // for (let index = 0; index < strip_num; index++) {
    //     arr.push(gObj);
    // }
    state.arrList = arr;
}
</script>
<style lang="less" scoped>
@bnoe-color: #00d0ff69;

.text {
    border-radius: 5px;
}

.fishbone {
    // min-height: 650px;
    // height: 100%;
    height: 44vh;
    position: relative;
    // max-width: 650px;

    .textCenter {
        position: absolute;
        top: -12px;
        width: 25px;
        height: 25px;
        line-height: 25px;
        text-align: center;
        background: green;
        font-weight: bold;
        right: -29px;
    }

    .textBottom {
        position: absolute;
        bottom: -47px;
        width: 25px;
        height: 25px;
        line-height: 25px;
        text-align: center;
        // background: green;
        font-weight: bold;
        right: -35px;
        transform: skewx(45deg);

        :deep(.icon-success) {
            width: 15px;

            path {
                fill: rgb(5, 199, 5);
            }
        }

        :deep(.icon-error) {
            width: 15px;

            path {
                fill: rgb(226, 1, 1);
            }
        }
    }

    .textTop {
        position: absolute;
        top: -40px;
        width: 25px;
        height: 25px;
        line-height: 25px;
        text-align: center;
        // background: green;
        font-weight: bold;
        right: -27px;
        transform: skewx(-45deg);

        :deep(.icon-success) {
            width: 15px;

            path {
                fill: rgb(5, 199, 5);
            }
        }

        :deep(.icon-error) {
            width: 15px;

            path {
                fill: rgb(226, 1, 1);
            }
        }
    }

    .leftIcon {
        border: 6px solid transparent;
        border-right: 6px solid black;
    }

    .rightIcon {
        border: 6px solid transparent;
        border-left: 6px solid black;
    }

    .content {
        width: 100%;
        height: 44vh;
        position: relative;
        padding-top: 7vh;
        padding-left: 2vw;
        padding-right: 2vw;
        box-sizing: border-box;
        // height: 100%;
        /* position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%); */
    }

    .center-line {
        /* position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(50%); */
        position: relative;
        width: 100%;
        height: 5px;
        background-color: @bnoe-color;
        border-radius: 3px;

        // &:after {
        //     position: absolute;
        //     right: -6px;
        //     bottom: -5px;
        //     content: ' ';
        //     border: 5px solid transparent;
        //     border-left: 5px solid black !important;
        // }
    }

    .top-bone {
        padding-top: 40px;
        padding-left: 4vw;
    }

    .top-bone,
    .bottom-bone {
        /* position: absolute;
        height: 50%;
        width: 100%;
        padding-right: 100px;
        left: 0; */

        .item-bone {
            position: relative;
            display: flex;
            margin: 0 5px;
            //float: left;
            //min-width: 150px;
            // width: 150px;
            //margin: 0 10px;
        }

        .item-bone-children-right {
            border: none !important;
            margin: 0 !important;
            padding: 0;
        }

        .bottom-children-item-right {
            position: relative;

            &:not(:last-child) {
                border-bottom: 1px solid @bnoe-color;

                &:after {
                    position: absolute;
                    left: -5px;
                    bottom: -5px;
                    content: ' ';
                    border: 5px solid transparent;
                    border-right: 5px solid black !important;
                    transform: skewx(-45deg);
                }
            }
        }

        .children-item-right {
            position: relative;

            &:not(:last-child) {
                border-bottom: 1px solid @bnoe-color;

                &:after {
                    position: absolute;
                    left: -5px;
                    bottom: -5px;
                    content: ' ';
                    border: 5px solid transparent;
                    border-right: 5px solid black !important;
                    transform: skewx(-45deg);
                }
            }
        }

        .children-item {
            position: relative;

            &:not(:last-child) {
                border-bottom: 1px solid @bnoe-color;

                &:after {
                    position: absolute;
                    right: -5px;
                    bottom: -5px;
                    content: ' ';
                    border: 5px solid transparent;
                    border-left: 5px solid black;
                    transform: skewx(-45deg);
                }
            }

            .text {
                margin-bottom: 45px;
                border: 1px solid #245b84;
                background: #0b2032;
                box-shadow: 2px 3px 5px #000;
            }

        }

        .item-bone-children {
            position: relative;
            height: 100%;
            border-right: 2px solid @bnoe-color;
            transform: skewX(45deg);
            margin: 0 10px;
            list-style-type: none;
            padding: 0;
            padding-right: 5px;

            .text {
                text-align: right;
                padding-right: 20px;
                transform: skewx(-45deg);
                font-size: 13px;
                width: 100%;
                line-height: 30px;
                white-space: nowrap;
                color: #fff;
                display: flex;
                justify-content: space-around;
                align-items: center;
            }

            .title {
                text-align: center;
                transform: skewX(-45deg);
                font-size: 16px;
                font-weight: bolder;
                line-height: 35px;
                color: #002766;
            }
        }
    }

    .bottom-bone {
        bottom: 0;
        padding-left: 4vw;
        .item-bone-children {
            position: relative;
            transform: skewX(-45deg);

            .text {
                transform: skewX(45deg);
                display: flex;
                justify-content: space-around;
                align-items: center;
            }

            .title {
                transform: skewX(45deg);
            }

            .children-item1 {
                position: relative;

                &:not(:last-child) {
                    border-bottom: 1px solid @bnoe-color;

                    &:after {
                        position: absolute;
                        right: -5px;
                        bottom: -5px;
                        content: ' ';
                        border: 5px solid transparent;
                        border-left: 5px solid black !important;
                        transform: skewx(45deg);
                    }
                }

                .text {
                    margin-top: 45px;
                    border: 1px solid #245b84;
                    background: #0b2032;
                    box-shadow: 2px -3px 5px #000;
                }
            }

            .children-item2 {
                position: relative;

                &:not(:last-child) {
                    border-bottom: 1px solid @bnoe-color;

                    &:after {
                        position: absolute;
                        left: -7px;
                        bottom: -5px;
                        content: ' ';
                        border: 5px solid transparent;
                        border-right: 5px solid black !important;
                        transform: skewx(45deg);
                    }
                }
            }
        }
    }
}

.sucess_icon {
    :deep(path) {
        fill: rgb(5, 199, 5);
    }
}

.close_icon {
    :deep(path) {
        fill: rgb(226, 1, 1);
    }
}
</style>
