<template>
	<CesiumBox></CesiumBox>
	<!-- <el-radio-group v-model="state.pageRadio" class="pageRadio" @change="pageRadioChange">
		<el-radio-button label="1">全景态势</el-radio-button>
		<el-radio-button label="2">任务态势 </el-radio-button>
		<el-radio-button label="3">资源态势</el-radio-button>
		<el-radio-button label="4">目标态势</el-radio-button>
	</el-radio-group> -->
	<div class="showOrHidden-box" :style="{ right: state.pageRadio == 1 ? '500px' : '16%' }">
		<template v-if="state.pageRadio != 1">
			<!-- <div>
				<label>航迹显隐：</label>
				<el-switch
					v-model="state.showTrack"
					inline-prompt
					active-text="显示"
					inactive-text="隐藏"
				/>
			</div> -->
			<div>
				<label>范围显隐：</label>
				<el-switch v-model="state.showArea" inline-prompt active-text="显示" inactive-text="隐藏" />
			</div>
		</template>
		<div>
			<label>名称显隐：</label>
			<el-switch v-model="state.showLabel" inline-prompt active-text="显示" inactive-text="隐藏" />
		</div>
	</div>
	<!-- 全景态势 -->
	<template v-if="state.pageRadio == 1">
		<customPopup left="0.5%" top="7%" width="25%" height="92%" headType="2">
			<template #header>
				<span class="list_icon"></span>
				<span class="title-font">任务执行情况</span>
			</template>
			<template #content>
				<div class="scheduleClass">
					<div
						class="task_schedule"
						v-for="task in displayTaskList"
						:key="task.id"
						v-memo="[task.id, task.taskName, task.schedule]"
					>
						<div style="width: calc(100% - 20px);padding-right: 20px;overflow-wrap:break-word">
							{{ task.taskName }}
						</div>
						<el-progress :percentage="task.schedule || 0" :color="customColor" />
					</div>
				</div>
			</template>
		</customPopup>
	</template>
	<template v-if="state.pageRadio == 1">
		<customPopup right="0.5%" top="7%" width="25%" height="35%" headType="2">
			<template #header>
				<span class="list_icon"></span>
				<span class="title-font">目标接入情况</span>
			</template>
			<template #content>
				<div class="chartClass" ref="Chart2" v-once></div>
			</template>
		</customPopup>
		<customPopup right="0.5%" bottom="1%" width="25%" height="35%" headType="2">
			<template #header>
				<span class="list_icon"></span>
				<span class="title-font">资源接入情况</span>
			</template>
			<template #content>
				<div class="chartClass" ref="Chart4" v-once></div>
			</template>
		</customPopup>
	</template>
	<!-- 任务态势 -->
	<template v-if="state.pageRadio == 2">
		<customPopup left="0.5%" top="7%" width="15%" height="60%">
			<template #header>
				<span class="list_icon"></span>
				<span class="title-font">任务列表</span>
			</template>
			<template #content>
				<div style="height: 100%;width: 100%;">
					<el-table
						ref="taskTableRef"
						class="custom-table"
						:data="displayTaskList"
						highlight-current-row
						style="height: 100%"
						@row-click="taskTableClick"
						:row-key="getTaskRowKey"
						lazy
					>
						<el-table-column align="center" prop="taskName" label="任务名称" min-width="3">
							<template v-slot="scope">
								<span v-memo="[scope.row.taskName]">{{ scope.row.taskName }}</span>
							</template>
						</el-table-column>
						<el-table-column align="center" prop="status" label="任务类型" min-width="2">
							<template v-slot="scope">
								<span v-memo="[scope.row.taskType]">
									{{ scope.row.taskType[0] ? dictValue("businessType", scope.row.taskType[0]) : "" }}
								</span>
							</template>
						</el-table-column>
						<el-table-column align="center" prop="schedule" label="任务进度" min-width="2">
							<template v-slot="scope">
								<span v-memo="[scope.row.schedule]">{{ scope.row.schedule }} %</span>
							</template>
						</el-table-column>
					</el-table>
					<div v-if="state.taskList.length > 100" style="text-align: center; padding: 5px; font-size: 12px; color: #999;">
						显示前100条，共{{ state.taskList.length }}条数据
					</div>
				</div>
			</template>
		</customPopup>
	</template>
	<customPopup right="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 2">
		<template #header>
			<span class="list_icon"></span>
			<span class="title-font">任务详情</span>
		</template>
		<template #content>
			<div style="height: 100%;width: 100%;overflow-y: auto;">
				<el-form :model="state.taskForm" label-width="80px">
					<el-form-item label="任务名称" prop="taskName">{{ state.taskForm.taskName }}</el-form-item>
					<!-- <el-form-item label="所属需求" prop="endTime">{{ state.taskForm.requirementName }}</el-form-item> -->
					<!-- dictValue("taskStatus", state.taskForm.status) -->
					<el-form-item label="业务类型" prop="taskType">{{ state.taskForm.taskType &&
					state.taskForm.taskType.length > 0 ? dictValue("businessType", state.taskForm.taskType[0]) : ""
						}}</el-form-item>
					<el-form-item label="任务状态" prop="taskStatus">{{
							state.taskForm.taskStatus
						}}</el-form-item>
					<el-form-item label="任务进度" prop="schedule">{{ state.taskForm.schedule }}% </el-form-item>

					<el-form-item label="任务周期" prop="endTime">{{ dictValue("repetitionType", state.taskForm.repeatType)
						}}</el-form-item>
					<el-form-item label="开始时间" prop="startTime">{{ state.taskForm.startTime }}</el-form-item>
					<el-form-item label="结束时间" prop="endTime">{{ state.taskForm.endTime }}</el-form-item>
					<el-form-item label="任务描述" prop="taskComment">{{ state.taskForm.taskComment }}</el-form-item>
				</el-form>
			</div>
		</template>
	</customPopup>
	<customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 2">
		<template #header>
			<span class="timeLine_icon"></span>
			<span class="title-font">任务执行情况</span>
			<span class="title-font" style="position: absolute; right: 3%;">当前时间：{{ timeSpan(state.currentTime)
				}}</span>
		</template>
		<template #content>
			<div class="chartClass" id="taskTimeLine"></div>
		</template>
	</customPopup>
	<!-- 资源态势 -->
	<customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 3">
		<template #header>
			<span class="timeLine_icon"></span>
			<span class="title-font">资源使用情况</span>
			<span class="title-font" style="position: absolute; right: 3%;">当前时间：{{ timeSpan(state.currentTime)
				}}</span>

		</template>
		<template #content>
			<div class="chartClass" id="resourceTimeLine"></div>
		</template>
	</customPopup>
	<customPopup left="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 3">
		<template #header>
			<span class="list_icon"></span>
			<span class="title-font">资源列表</span>
		</template>
		<template #content>
			<div style="height: 100%;width: 100%;">
				<el-table ref="resourceTableRef" class="custom-table" :data="state.resourceList" style="height: 100%"
						  :row-class-name="chooseZY" highlight-current-row @row-click="taskTableClickZY">
					<el-table-column align="center" prop="name" label="设备名称" min-width="3"></el-table-column>
					<el-table-column align="center" prop="typeValue" label="设备类型" min-width="2">
					</el-table-column>
				</el-table>
			</div>
		</template>
	</customPopup>
	<customPopup right="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 3">
		<template #header>
			<span class="list_icon"></span>
			<span class="title-font">资源详情</span>
		</template>
		<template #content>
			<div style="height: 100%;width: 100%;overflow-y: auto;">
				<el-form :model="state.ZYForm" label-width="120px" class="targetForm_class">
					<el-form-item label="名称" prop="name">{{ state.ZYForm.name }}</el-form-item>
					<el-form-item label="节点代号" prop="code">{{ state.ZYForm.code }}</el-form-item>
					<el-form-item label="频段" prop="frequency">{{ state.ZYForm.frequency }}</el-form-item>
					<el-form-item label="多目标能力" prop="targetCount">{{ state.ZYForm.targetCount }}</el-form-item>
					<el-form-item label="业务类型" prop="businessType">{{ dictValue("businessType",
						state.ZYForm.businessType) }}</el-form-item>
					<el-form-item label="EIRP（dB）" prop="eirp">{{ state.ZYForm.eirp }}</el-form-item>
					<el-form-item label="G/T（dB）" prop="gt">{{ state.ZYForm.gt }}</el-form-item>
					<el-form-item label="天线类型" prop="antennaType">{{ dictValue("antennaType", state.ZYForm.antennaType)
						}}</el-form-item>
					<el-form-item label="工作体制" prop="workSystem">{{ dictValue("workSystem", state.ZYForm.workSystem)
						}}</el-form-item>
					<el-form-item label="经度（°）" prop="longitude">{{ state.ZYForm.longitude }}</el-form-item>
					<el-form-item label="纬度（°）" prop="latitude">{{ state.ZYForm.latitude }}</el-form-item>
					<el-form-item label="高度（米）" prop="altitude">{{ state.ZYForm.altitude }}</el-form-item>
					<el-form-item label="测控距离（米）" prop="radius">{{ state.ZYForm.radius }}</el-form-item>
				</el-form>

				<el-link style="text-align: left;float: right;margin-right: 10px" @click="clickGplot()"
						 v-if="state.ZYForm.id == '00000000'">详情</el-link>
				<el-link style="text-align: left;float: right;" @click="clickStationRow()"
						 v-else-if="state.ZYForm.id">更多...</el-link>
			</div>
		</template>
	</customPopup>

	<!-- 目标态势 -->
	<template v-if="state.pageRadio == 4">
		<customPopup left="0.5%" top="7%" width="15%" height="45%">
			<template #header>
				<span class="list_icon"></span>
				<span class="title-font">目标列表</span>
			</template>
			<template #content>
				<div style="height: 100%;width: 100%;">
					<el-table
						ref="targetTableRef"
						class="custom-table"
						:data="displayTargetList"
						style="height: 100%"
						highlight-current-row
						:row-class-name="chooseMB"
						@row-click="targetTableClick"
						:row-key="getTargetRowKey"
						lazy
					>
						<el-table-column align="center" prop="name" label="目标名称">
							<template v-slot="scope">
								<span v-memo="[scope.row.name]">{{ scope.row.name }}</span>
							</template>
						</el-table-column>
						<el-table-column align="center" prop="targetTypeValue" label="目标类型">
							<template v-slot="scope">
								<span v-memo="[scope.row.targetTypeValue]">{{ scope.row.targetTypeValue }}</span>
							</template>
						</el-table-column>
					</el-table>
					<div v-if="state.targetList.length > 100" style="text-align: center; padding: 5px; font-size: 12px; color: #999;">
						显示前100条，共{{ state.targetList.length }}条数据
					</div>
				</div>
			</template>
		</customPopup>
	</template>
	<customPopup left="0.5%" top="53%" width="15%" height="45%" v-if="state.pageRadio == 4">
		<template #header>
			<span class="list_icon"></span>
			<span class="title-font">目标详情</span>
		</template>
		<template #content>
			<el-form
				:model="state.chooseTargetForm"
				label-width="80px"
				style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;"
				class="targetForm_class"
				label-position="left"
				v-memo="[state.chooseTargetForm.id, state.chooseTargetForm.name, state.chooseTargetForm.targetTypeValue]"
			>
				<el-form-item label="目标名称:" v-memo="[state.chooseTargetForm.name]">
					{{ state.chooseTargetForm.name || '-' }}
				</el-form-item>
				<el-form-item label="目标类型:" v-memo="[state.chooseTargetForm.targetTypeValue]">
					{{ state.chooseTargetForm.targetTypeValue || '-' }}
				</el-form-item>
				<el-form-item label="工作体制:" prop="workSystem" v-memo="[state.chooseTargetForm.workSystem]">
					{{ dictValue("workSystem", state.chooseTargetForm.workSystem) || '-' }}
				</el-form-item>
				<el-form-item label="业务类型:" prop="businessType" v-memo="[state.chooseTargetForm.businessType]">
					{{ dictValue("businessType", state.chooseTargetForm.businessType) || '-' }}
				</el-form-item>
				<el-form-item label="EIRP" prop="eirp" v-memo="[state.chooseTargetForm.eirp]">
					{{ state.chooseTargetForm.eirp || '0' }}dB
				</el-form-item>
				<el-form-item label="天线类型:" prop="antennaType" v-memo="[state.chooseTargetForm.antennaType]">
					{{ dictValue("antennaType", state.chooseTargetForm.antennaType) || '-' }}
				</el-form-item>
				<el-form-item
					label="跟随无人机"
					prop="eirp"
					v-if="state.chooseTargetForm.targetTypeValue == '无人艇'"
					style="margin-top: 20px;"
					v-memo="[state.isFollow]"
				>
					<el-switch v-model="state.isFollow" @change="changeFollow"/>
				</el-form-item>
			</el-form>
			<el-link style="text-align: left;float: right;" @click="clickDetileRow()" v-once>目标状态</el-link>
		</template>
	</customPopup>
	<customPopup right="0.5%" top="53%" width="15%" height="45%" v-if="state.pageRadio == 4">
		<template #header>
			<span class="list_icon"></span>
			<span class="title-font">终端状态</span>
			<el-link style="position: absolute;right:20px;" @click="clickGplot()">拓扑图</el-link>
		</template>
		<template #content>
			<el-form :model="targetDataObj[state.chooseTargetForm?.id] || {}" label-width="144px"
					 style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;" class="targetForm_class"
					 label-position="left">
				<el-form-item label="终端ID :" label-width="80px">
                    <span>
                        {{ state.chooseTargetForm.nodeId || '-' }}
                    </span>
				</el-form-item>
				<el-form-item label="随遇接入状态 :" label-width="100px">
                    <span>
                        {{ terminalStatus || '-' }}
                    </span>
				</el-form-item>
				<el-form-item label="终端体制 :" label-width="80px">
                    <span>
                        {{ modeCtrlList[state.chooseTargetForm.terCurrentMode] || "暂无" }}
                    </span>
				</el-form-item>
				<el-form-item label="当前接入节点 :" label-width="100px">
                    <span>
                        {{ state.chooseTargetForm.curStationName || '未接入' }}
                    </span>
				</el-form-item>
				<el-form-item label="接收频率 :" label-width="80px">
                    <span>
                        {{ (targetDataObj[state.chooseTargetForm?.id]?.machineFrame?.rxFreq || '0') + (targetDataObj[state.chooseTargetForm?.id]?.machineFrame?.rxFreq ? 'MHz' : 'MHz') }}
                    </span>
				</el-form-item>
				<el-form-item label="发射频率 :" label-width="80px">
                    <span>
                        {{ (targetDataObj[state.chooseTargetForm?.id]?.machineFrame?.txFreq || '0') + (targetDataObj[state.chooseTargetForm?.id]?.machineFrame?.txFreq ? 'MHz' : 'MHz') }}
                    </span>
				</el-form-item>
				<el-form-item v-for="item in terminalList" :label="item.label + ' :'" :prop="item.key" :key="item.key"
							  v-show="terminalBriefList.includes(item.key)">

                    <span>{{ (targetDataObj[state.chooseTargetForm?.id]?.machineFrame?.[item.key] || '-') + (item.unit
						||
						'') }}</span>
					<!-- <span>
						<span v-for="(value, textKey ) in state.terminalDate[item.key]" :key="textKey">{{
							textKey }}：{{ value }}<br /></span>
					</span> -->
				</el-form-item>
			</el-form>
			<!-- v-show="state.targetForm?.id" -->
			<el-link style="text-align: left;float: right;" @click="clickTerminalRow()">更多...</el-link>
		</template>
	</customPopup>
	<customPopup left="33%" top="20%" width="35%" height="50%" v-if="state.showPic && state.pageRadio == 4">
		<template #header>
			<span class="list_icon"></span>
			<span class="title-font">目标素材</span>
			<div style="position: absolute; right: 2%">
				<el-button @click="state.showPic = false">关闭</el-button>
			</div>
		</template>
		<template #content>
			<div style="height: 100%;width: 100%;overflow-y: auto;">
				<div style="width: 100%;height: 48%;">
					<img style="height: 100%;width: 100%;" :src="hostLink + '/file/test.png'" />
				</div>
				<div style="width: 100%;;height: 48%;">
					<video style="height: 100%;width: 100%;" controls type="video/wmv"
						   :src="hostLink + '/file/video.wmv'"></video>
				</div>
			</div>
		</template>
	</customPopup>
	<customPopup left="16%" bottom="3%" width="68%" height="20%" v-if="state.pageRadio == 4">
		<template #header>
			<span class="list_icon"></span>
			<span class="title-font">实时日志</span>
			<div style="position: absolute; right: 2%; display: flex; align-items: center;">
				<span style="color: white; margin-right: 8px; font-size: 12px;">日志类型：</span>
				<el-select
					v-model="state.logTypeFilter"
					placeholder="全部"
					size="small"
					style="width: 120px;"
					clearable
					@change="handleLogTypeChange">
					<el-option label="站点状态" value="stationState"></el-option>
					<el-option label="终端状态" value="terminalState"></el-option>
					<el-option label="操控端交互日志" value="controlLog"></el-option>
					<el-option label="接入决策模块交互日志" value="selectionLog"></el-option>
				</el-select>
			</div>
		</template>
		<template #content>
			<div ref="logContainer" style="height: 124px;overflow: auto;" class="log-container">
				<div
					class="log-item"
					style="font-size:16px;line-height:25px;padding:2px 0;"
					v-for="(item, index) in filteredLogs"
					:key="`log_${index}_${item.time}`"
					v-memo="[item.time, item.type, item.message]"
				>
					<span class="log-time">{{ item.time }}</span>
					<span class="log-bracket">【</span>
					<span
						class="log-type"
						:class="{ 'log-error': item.type === 'ERROR', 'log-success': item.type !== 'ERROR' }"
					>
						{{ item.type }}
					</span>
					<span class="log-bracket">】：</span>
					<span class="log-message">{{ item.message }}</span>
				</div>
			</div>
		</template>
	</customPopup>
	<!-- <customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 4">
		<template #header>
			<span class="timeLine_icon"></span>
			<span class="title-font">目标接入情况</span>
			<span class="title-font" style="position: absolute; right: 3%;">当前时间：{{ timeSpan(state.currentTime)
				}}</span>

		</template>
		<template #content>
			<div class="chartClass" id="targerTimeLine"></div>
		</template>
	</customPopup> -->
	<!-- 目标详情弹窗 -->
	<customPopup left="25%" top="15%" width="50%" height="60%" v-if="state.pageRadio == 4 && state.detailShow">
		<template #header>
			<span class="timeLine_icon"></span>
			<span class="title-font">目标状态 -- {{ state.chooseTargetForm.name }}</span>
			<span class="title-font" style="position: absolute; right: 3%;cursor: pointer;"
				  @click="state.detailShow = false">X
            </span>
		</template>
		<template #content>
			<tsDetail :tsData="state.targetForm" v-if="state.pageRadio == 4 && state.detailShow"></tsDetail>
		</template>
	</customPopup>

	<!-- 终端更多弹窗 -->
	<customPopup left="calc(50% - 580px)" top="15%" width="1163px" height="70%"
				 v-if="state.pageRadio == 4 && state.terminalStateShow">
		<template #header>
			<span class="timeLine_icon"></span>
			<span class="title-font">终端状态-{{ targetDataObj[state.chooseTargetForm.id]?.tTime }}</span>
			<span class="title-font" style="position: absolute; right: 3%;cursor: pointer;"
				  @click="state.terminalStateShow = false">X
            </span>
		</template>
		<template #content>
			<!-- <terminalState :terminalDate="state.terminalDate" v-if="state.pageRadio == 4 && state.terminalStateShow"> -->
			<terminalState :terminalDate="targetDataObj[state.chooseTargetForm.id] || {}"
						   v-if="state.pageRadio == 4 && state.terminalStateShow" :logList="termLockLog[state.chooseTargetForm.nodeId]">
			</terminalState>
		</template>
	</customPopup>
	<!-- 拓扑图弹窗 -->
	<customPopup left="25%" top="15%" width="56%" height="60%" v-if="state.pageRadio == 4 && state.gplotShow">
		<template #header>
			<span class="timeLine_icon"></span>
			<span class="title-font">随遇接入节点</span>
			<span class="title-font" style="position: absolute; right: 3%;cursor: pointer;"
				  @click="state.gplotShow = false">X
            </span>
		</template>
		<template #content>
			<!-- {{}} -->
			<gplot :gplotData="state.gplotData" :terminalDate="targetDataObj[0] || {}"
				   v-if="state.pageRadio == 4 && state.gplotShow">
			</gplot>
		</template>
	</customPopup>
	<!-- 终端控制弹窗 -->
	<customPopup top="7%" width="15%" height="45%" right="0.5%" v-if="state.pageRadio == 4">
		<template #header>
			<span class="list_icon"></span>
			<span class="title-font">终端控制</span>
			<!-- <div style="position: absolute; right: 2%">
				<el-button @click="state.terminalControlShow = false">关闭</el-button>
			</div> -->
		</template>
		<template #content>
			<!-- <el-tabs v-model="state.activeName" class="tabsClass" @tab-change="tabChange"> -->
			<!-- <el-tab-pane label="整机参数设置" name="1"> -->

			<el-tabs v-model="state.contorlActive" class="tabsClass" @tab-change="tabChange">
				<el-tab-pane label="整机参数设置" name="1" style="padding-top: 15px;">
					<el-form :model="state.terminalControlForm.machineParam" label-width="124px"
							 style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;" label-position="left">
						<el-form-item label="目标终端序号">
							<el-select v-model="state.terminalControlForm.machineParam.targetNode"
									   placeholder="请选择目标终端序号" style="width: 100%;">
								<el-option
									v-for="item in [1, 2, 3, 4, 5]"
									:key="item"
									:label="item"
									:value="item">
								</el-option>
							</el-select>
						</el-form-item>
						<!-- <el-form-item label="站点选择">
							<el-select v-model="state.terminalControlForm.machineParam.nodeId" style="width: 70%;">
								<el-option value="1834120494834802689" label="航天站_S"></el-option>
								<el-option value="1834055782248001538" label="航天站_Ka"></el-option>
							</el-select>
						</el-form-item> -->
						<el-form-item label="体制选择">
							<el-select v-model="state.terminalControlForm.machineParam.modeCtrl" style="width: 100%;">
								<el-option :value="0" label="不切换体制"></el-option>
								<el-option :value="1" label="扩频测控模式二-S1"></el-option>
								<el-option :value="2" label="扩频测控模式二-S2"></el-option>
								<el-option :value="3" label="扩频测控模式二-S3"></el-option>
								<el-option :value="4" label="扩频测控模式二-S4"></el-option>
								<el-option :value="5" label="扩频测控模式二-Ka1"></el-option>
								<el-option :value="6" label="扩频测控模式二-Ka2"></el-option>
								<el-option :value="7" label="扩频测控模式二-Ka3"></el-option>
								<el-option :value="8" label="扩频测控模式二-Ka4"></el-option>
								<el-option :value="9" label="扩频四合一综合测控体制"></el-option>
								<el-option :value="10" label="常规导弹测控体制"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="接收频率(MHz)">
							<el-input-number v-model="state.terminalControlForm.machineParam.rxFreq"
											 style="width: 100%;" :controls="false">
							</el-input-number>
						</el-form-item>
						<el-form-item label="发射频率(MHz)">
							<el-input-number v-model="state.terminalControlForm.machineParam.txFreq"
											 style="width: 100%;" :controls="false"></el-input-number>
						</el-form-item>
						<el-form-item label="发射衰减(dB)">
							<el-input-number v-model="state.terminalControlForm.machineParam.txAtte"
											 style="width: 100%;" :controls="false"></el-input-number>
						</el-form-item>
						<el-form-item label="">
							<el-button @click="setMachineParam">发送</el-button>
						</el-form-item>
					</el-form>
				</el-tab-pane>


				<el-tab-pane label="抗干扰参数设置" name="2" style="padding-top: 15px;">
					<el-form :model="state.terminalControlForm.sendForm" label-width="124px"
							 style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;" label-position="left">
						<el-form-item label="目标终端序号">
							<el-select v-model="state.terminalControlForm.sendForm.targetNode"
									   placeholder="请选择目标终端序号" style="width: 100%;">
								<el-option
									v-for="item in [1, 2, 3, 4, 5]"
									:key="item"
									:label="item"
									:value="item">
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="决策方式">
							<el-select size="mini" v-model="state.terminalControlForm.sendForm.decisionMethod"
									   clearable>
								<el-option :value="0" label="无效"></el-option>
								<el-option :value="1" label="手动"></el-option>
								<el-option :value="2" label="自动"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="抗干扰手段选择">
							<el-select size="mini" clearable
									   v-model="state.terminalControlForm.sendForm.counterInterferenceMeasures">
								<el-option :value="0" label="关闭抗干扰"></el-option>
								<el-option :value="2" label="体制级抗干扰"></el-option>
								<el-option :value="4" label="信号级抗干扰"></el-option>
								<el-option :value="5" label="系统级抗干扰"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="链路切换方式">
							<el-select size="mini" clearable
									   v-model="state.terminalControlForm.sendForm.switchLink">
								<el-option :value="0" label="无效"></el-option>
								<el-option :value="1" label="系统级抗干扰"></el-option>
								<el-option :value="2" label="切换调度"></el-option>
								<el-option :value="3" label="接入调度"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="链路切换代号">
							<el-select size="mini" clearable
									   v-model="state.terminalControlForm.sendForm.switchLinkCode">
								<el-option :value="0" label="无效"></el-option>
								<el-option :value="1" label="链路1"></el-option>
								<el-option :value="2" label="链路2"></el-option>
								<el-option :value="3" label="链路3"></el-option>
								<el-option :value="4" label="链路4"></el-option>
								<el-option :value="5" label="链路5"></el-option>
								<el-option :value="6" label="链路6"></el-option>
								<el-option :value="7" label="链路7"></el-option>
								<el-option :value="8" label="链路8"></el-option>
								<el-option :value="9" label="链路9"></el-option>
							</el-select>
						</el-form-item>
						<!-- <el-form-item label="抗干扰手段决策结果">
							<span class="content_bg">{{
								spectrumText('kgrsdjcjg') ? kgrsdList[
									spectrumText('kgrsdjcjg')] : '--'
							}}</span>
						</el-form-item> -->
						<el-form-item label="">
							<el-button size="mini" style="height: 30px;width: 60px; padding: 0 5px;float: right;"
									   @click="sendAntiJamming">发送</el-button>
						</el-form-item>
					</el-form>
				</el-tab-pane>
				<el-tab-pane label="接入平台测试" name="5" style="padding-top: 15px;">

					<el-form label-width="150px" style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;"
							 label-position="left">
						<el-form-item label="无人机模拟导弹">
							<!-- <el-switch v-model="isEnableModel" @change="changeEnableModel" inline-prompt active-text="启用"
		inactive-text="禁用" /> -->
							<el-radio-group v-model="simulateDD" @change="uavSimulateDD">
								<el-radio :label="true">是</el-radio>
								<el-radio :label="false">否</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-form>
				</el-tab-pane>
				<el-tab-pane label="数据传输控制" name="3" style="padding-top: 15px;height: 100%;">
					<el-form :model="state.terminalControlForm.dataControl" label-width="124px"
							 style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;" label-position="left">
						<el-form-item label="终端选择">
							<el-select v-model="state.terminalControlForm.dataControl.targetNode" style="width: 100%;"
									   @change="targetNodeChange">
								<el-option value="1" label="终端1"></el-option>
								<el-option value="2" label="终端2"></el-option>
								<el-option value="3" label="终端3"></el-option>
								<el-option value="4" label="终端4"></el-option>
								<el-option value="5" label="终端5"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="终端体制">
							{{ state.terminalControlForm.dataControl.modeCtrl ?
							modeCtrlList[state.terminalControlForm.dataControl.modeCtrl] : "暂无" }}
						</el-form-item>
						<el-form-item label="下行发射">
							<el-select v-model="state.terminalControlForm.dataControl.transModState"
									   style="width: 100%;">
								<el-option :value="0xb3d2" label="遥测任务"></el-option>
								<el-option :value="0xc72f" label="数传任务"
										   v-show="!['1', '2', '3'].includes(state.terminalControlForm.dataControl.modeCtrl)"></el-option>
								<el-option :value="0x4c2d" label="遥测测试"
										   v-show="!['1', '2', '3'].includes(state.terminalControlForm.dataControl.modeCtrl)"></el-option>
								<el-option :value="0x3968" label="数传测试"
										   v-show="!['1', '2', '3'].includes(state.terminalControlForm.dataControl.modeCtrl)"></el-option>
								<el-option :value="0x235a" label="单音测试"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="">
							<el-button @click="setDataControlParam">发送</el-button>
						</el-form-item>
					</el-form>
				</el-tab-pane>
				<el-tab-pane label="调试参数" name="4" style="padding-top: 15px;height: 100%;">
					<el-form :model="state.terminalControlForm.dataControl" label-width="90px"
							 style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;" label-position="left">
						<el-form-item label="是否启用">
							<!-- <el-switch v-model="isEnableModel" @change="changeEnableModel" inline-prompt active-text="启用"
							inactive-text="禁用" /> -->
							<el-radio-group v-model="isEnableModel" @change="changeEnableModel">
								<el-radio :label="1">启用</el-radio>
								<el-radio :label="0">禁用</el-radio>
							</el-radio-group>
						</el-form-item>
						<el-form-item label="跟随数据源">
							<el-radio-group v-model="datasourceModel" @change="changeDatasourceModel">
								<el-radio label="real">真实</el-radio>
								<el-radio label="simulation">模拟</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-form>
				</el-tab-pane>

			</el-tabs>
		</template>
	</customPopup>
	<!-- 站点信息弹窗 -->
	<customPopup left="calc(50% - 575px)" top="15%" width="1150px" height="60%"
				 v-if="state.pageRadio == 3 && state.stationMoreShow">
		<template #header>
			<span class="timeLine_icon"></span>
			<span class="title-font">资源详情 -- {{ state.ZYForm.name }}</span>
			<span class="title-font" style="position: absolute; right: 3%;cursor: pointer;"
				  @click="state.stationMoreShow = false">X
            </span>
		</template>
		<template #content>
			<stationDataState :stationData="state.stationData" v-if="state.pageRadio == 3 && state.stationMoreShow">
			</stationDataState>
		</template>
	</customPopup>
</template>

<script setup>
import {onMounted,onUnmounted,ref,reactive,watch,nextTick,computed} from 'vue'
import CesiumBox from "@/components/evmap/CesiumBox.vue";
import customPopup from "@/components/customPopup.vue";
import * as situationApi from "@/service/API/system/situation.js";
import * as areaApi from "@/service/API/system/areaManage.js";
import * as dataApi from "@/service/API/home/<USER>";
import * as controlApi from "@/service/API/system/control.js";
import * as resourceApi from "@/service/API/system/resourceDispatch.js";
import * as formLabel from "./formLabel.js"
import * as formLabel2 from "./formLabel2.js"
import * as terminalLable from "./terminalLable.js"
import tsDetail from "./tsDetail.vue";
import terminalState from './terminalState.vue';
import stationDataState from './stationDataState.vue';
import {ElMessage} from 'element-plus';
import gplot from "./gplot.vue"
import {useSockeMessStore} from '@/stores/index'

const sockeMessStore = useSockeMessStore()

const dictValue = window.dictValue;
const timeSpan = window.timeSpan;
let hostLink = AdminServerApi
let simulateDD = ref(false)

const taskTableRef = ref(null)
const resourceTableRef = ref(null)
const targetTableRef = ref(null)
const logContainer = ref(null)

const customColor = [
	{color:"#f56c6c",percentage:20},
	{color:"#e6a23c",percentage:40},
	{color:"#1989fa",percentage:60},
	{color:"#5cb87a",percentage:80},
	{color:"#03a2a7",percentage:100},
];

function spectrumText(key){
	let t = state.terminalDate?.antiJammingFrame?.[key]
	if(t === 0){
		t = "0"
	}
	return t
}

let isEnableModel = ref(0)
const datasourceModel = ref('')
function tabChange(){
	if(state.contorlActive == 4){
		getEnableModel()
	}
}

// 获取是否启用接入模块
function getEnableModel(){
	controlApi.getTerminalNode().then((res) => {
		// 确保返回的值是数字类型（1或0），与radio-group的label匹配
		isEnableModel.value = Number(res.data.data)
	})
}

function changeEnableModel(){
	// 传递当前选择的值（1表示启用，0表示禁用）
	controlApi.setTerminalNode({nodeEnable:isEnableModel.value}).then((res) => {
		if(res.data.code == 200){
			ElMessage.success('修改成功')
		}else{
			ElMessage.error(res.data.message)
		}
		// 重新获取状态以确保UI与后端同步
		getEnableModel()
	})
}
// 切换数据源
const changeDatasourceModel = () => {
	controlApi.toggleDatasourceMode().then((res) => {
		if(res.data.code !== 200){
			ElMessage.error(res.data.message)
			return
		}
		const {mode = '', message} = res.data?.data ?? {}
		datasourceModel.value = mode
		message && ElMessage.success(message)
	})
}
const getDatasourceMode = () => {
	controlApi.getDatasourceMode().then((res) => {
		datasourceModel.value = res.data?.data?.mode ?? ''
	})
}
function sendAntiJamming(){
	// state.terminalControlForm.sendForm.targetNode = state.terminalControlForm.machineParam.nodeId
	controlApi.antiJamming(state.terminalControlForm.sendForm).then(res => {
		if(res.data.code == 200){
			ElMessage.success("发送成功")
		}else{
			ElMessage.error("发送失败")
		}
	})
}

const kgrsdList = ["关闭抗干扰",' ','体制级抗干扰',' ','信号级抗干扰','系统级抗干扰']
const modeCtrlList = {
	'0':"不切换体制",
	'1':"扩频测控模式二-S",
	'2':"扩频测控模式二-Ka",
	'3':"扩频四合一综合测控体制",
	'4':"常规导弹测控体制",
}
var formLabelList = formLabel2.failedList
var terminalList = terminalLable.failedList //终端字段（全）
var terminalBriefList = terminalLable.showBriefList //终端字段（简略显示列表）
var targetShowLabelList = formLabel2.targetShowList //目标详情展示列表

const state = reactive({
	pageRadio:"4",
	taskList:[],//任务列表
	taskForm:{},//任务详情
	// ZYList: [],// sdw 资源列表
	ZYForm:[],// sdw 资源详情
	chooseTask:undefined,
	chooseResource:undefined,
	chooseTarget:undefined,
	showTargetList:[],//当前要展示的目标列表
	showResourceList:[],//当前要展示的资源列表
	targetList:[],
	targetFiled:[],//目标的字段
	showList:[],
	targetForm:{},//目标详情
	showPic:false,//素材展示
	getting:false,//正在获取中
	playStatus:"pause",//播放状态
	resourceList:[{
		"id":"00000000",
		"name":"随遇接入节点",
		"typeValue":"地基随遇",
		"code":"00000000",
		"longitude":122.091505,
		"latitude":37.543916,
		"altitude":2000,
		"updateTime":1735887373000,
		"bottomRadius":100000,
		"targetCount":1,
		"eirp":100,
		"gt":1,
		"antennaType":1,
		"frequency":"0,1",
		"pitchAngle":0,
		"workSystem":"1",
		"areaId":"1838030102630240258",
		"createTime":1726107948000,
		"generalId":"7",
		"bearingAngle":0,
		"businessType":"1",

	}],//展示的资源列表
	currentTime:"2000-01-01 00:00:01",//当前时间
	showTrack:true, // 显隐航迹
	showArea:true, // 显隐范围
	showLabel:true, //名称显隐
	targetTypeList:["无人车","无人艇","无人机"],
	detailShow:false,//目标详情展示
	detailActiveName:"1",//目标详情展示页面的tab
	contorlActive:"1",//控制帧的tab
	terminalStateShow:false,//终端状态弹窗控制
	terminalDate:{},//终端数据
	gplotShow:false,//拓扑图显示控制
	gplotData:{},//拓扑图数据
	chooseTargetForm:{},//在列表中选中的目标
	terminalControlShow:false,//
	terminalControlForm:{
		// 定义 整机参数设置 初始值
		machineParam:{
			// targetNode: 1,
			// modeCtrl: 0,
			// rxFreq: 2070,
			// txFreq: 2260,
			// txAtte: 30
		},
		sendForm:{},
		dataControl:{
			// targetNode: undefined,
			// modeCtrl: '',
			// transModState: undefined
		}
	},
	stationMoreShow:false,//站点的更多显示控制
	stationData:[], //站点信息
	logTypeFilter:'', //实时日志类型过滤器
	isFollow: false

})
const changeFollow = async () => {
	controlApi.toggleFollowStatus().then((res) => {
		if(res.data.code !== 200){
			ElMessage.error(res.data.message)
			return
		}
		const {followStatus = false, message} = res.data?.data ?? {}
		state.isFollow = followStatus
		message && ElMessage.success(message)
	})
}
const getFollowStatus = () => {
	controlApi.getFollowStatus().then((res) => {
		state.isFollow = res.data?.data?.followStatus ?? false
	})
}
// 终端上行链路锁定状态锁定日志
const termLockLog = ref({})
const targetDataObj = ref({})
const Chart2 = ref(null);

const Chart4 = ref(null);

let fontColor = window.$fontColor;
let TSinterval
let timePoint = 0
let uuid = parseInt(Math.random() * 100000000000);
let panoramaId

// 优化的计算属性，减少不必要的重新计算（添加缓存和懒加载）
const displayTaskList = computed(() => {
	// 只在当前页面需要时才计算
	if (state.pageRadio !== "2" && state.pageRadio !== "1") return []
	return state.taskList.slice(0, 100)
})

const displayTargetList = computed(() => {
	// 只在目标态势页面需要时才计算
	if (state.pageRadio !== "4") return []
	return state.targetList.slice(0, 100)
})

const displayResourceList = computed(() => {
	// 只在资源态势页面需要时才计算
	if (state.pageRadio !== "3") return []
	return state.resourceList.slice(0, 100)
})

// 懒加载的图表渲染标志
const shouldRenderCharts = computed(() => {
	return state.pageRadio === "1"
})

// 懒加载的表格渲染标志
const shouldRenderTables = computed(() => {
	return ["2", "3", "4"].includes(state.pageRadio)
})

// 行键函数，提高表格渲染性能
const getTaskRowKey = (row) => {
	return `task_${row.id}`
}

const getTargetRowKey = (row) => {
	return `target_${row.id}`
}

const getResourceRowKey = (row) => {
	return `resource_${row.id}`
}

// 性能监控工具
const performanceMonitor = {
	updateCount: 0,
	lastMemoryCheck: 0,

	// 监控数据更新频率
	trackUpdate() {
		this.updateCount++
		if (this.updateCount % 50 === 0) {
			console.log(`[性能监控] 数据更新次数: ${this.updateCount}`)
		}
	},

	// 监控内存使用
	checkMemory() {
		const now = Date.now()
		if (now - this.lastMemoryCheck > 30000) { // 每30秒检查一次
			this.lastMemoryCheck = now
			if (performance.memory) {
				const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
				const total = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
				console.log(`[性能监控] 内存使用: ${used}MB / ${total}MB`)

				// 内存使用过高时的警告
				if (used > 200) {
					console.warn(`[性能警告] 内存使用过高: ${used}MB`)
				}
			}
		}
	},

	// 监控渲染性能
	measureRender(name, fn) {
		const start = performance.now()
		const result = fn()
		const end = performance.now()
		if (end - start > 16) { // 超过一帧的时间
			console.warn(`[性能警告] ${name} 渲染耗时: ${(end - start).toFixed(2)}ms`)
		}
		return result
	}
}

// 数据更新防抖机制
let dataUpdateTimer = null
let pendingUpdates = {
	targets: new Map(),
	resources: new Map(),
	terminals: new Map()
}

// 批量更新函数（优化版本，添加数据量限制）
const flushPendingUpdates = () => {
	// 批量更新目标数据
	if (pendingUpdates.targets.size > 0) {
		pendingUpdates.targets.forEach((data, id) => {
			const index = state.targetList.findIndex(item => item.id === id)
			if (index !== -1) {
				state.targetList.splice(index, 1, data)
			} else {
				state.targetList.push(data)
			}
		})

		// 限制目标列表大小，防止内存无限增长
		if (state.targetList.length > 1000) {
			state.targetList = state.targetList.slice(-800) // 保留最新的800个
		}

		pendingUpdates.targets.clear()
		targetTableRef.value?.doLayout()
	}

	// 批量更新资源数据
	if (pendingUpdates.resources.size > 0) {
		pendingUpdates.resources.forEach((data, id) => {
			const index = state.resourceList.findIndex(item => item.id === id)
			if (index !== -1) {
				state.resourceList.splice(index, 1, data)
			} else {
				state.resourceList.push(data)
			}
		})

		// 限制资源列表大小
		if (state.resourceList.length > 500) {
			state.resourceList = state.resourceList.slice(-400) // 保留最新的400个
		}

		pendingUpdates.resources.clear()
		resourceTableRef.value?.doLayout()
	}

	// 批量更新终端数据
	if (pendingUpdates.terminals.size > 0) {
		pendingUpdates.terminals.forEach((data, id) => {
			targetDataObj.value[id] = data
		})

		// 限制终端数据对象大小
		const terminalKeys = Object.keys(targetDataObj.value)
		if (terminalKeys.length > 200) {
			// 保留最新的150个终端数据
			const keysToKeep = terminalKeys.slice(-150)
			const newTargetDataObj = {}
			keysToKeep.forEach(key => {
				newTargetDataObj[key] = targetDataObj.value[key]
			})
			targetDataObj.value = newTargetDataObj
		}

		pendingUpdates.terminals.clear()
	}
}

// 防抖的数据更新（集成性能监控）
const debouncedDataUpdate = () => {
	if (dataUpdateTimer) {
		clearTimeout(dataUpdateTimer)
	}
	dataUpdateTimer = setTimeout(() => {
		performanceMonitor.measureRender('数据批量更新', () => {
			flushPendingUpdates()
			performanceMonitor.trackUpdate()
			performanceMonitor.checkMemory()
		})
		dataUpdateTimer = null
	}, 50) // 50ms防抖
}

onMounted(() => {
	panoramaId = localStorage.getItem("panoramaId")
	pageRadioChange()
	window.addEventListener("onmessageTSWS",getSocketData);
	getFollowStatus()
	getDatasourceMode()
})
onUnmounted(() => {
	// 移除事件监听器
	window.removeEventListener("onmessageTSWS",getSocketData);

	// 清理地图相关资源
	mapTool.removeAll()

	// 清理所有定时器
	if (TSinterval){
		clearInterval(TSinterval)
		TSinterval = null
	}
	if (dataUpdateTimer) {
		clearTimeout(dataUpdateTimer)
		dataUpdateTimer = null
	}
	if (scrollUpdateTimer) {
		clearTimeout(scrollUpdateTimer)
		scrollUpdateTimer = null
	}
	if (terminalStatusTimer) {
		clearTimeout(terminalStatusTimer)
		terminalStatusTimer = null
	}
	if (flowLineUpdateTimer) {
		clearTimeout(flowLineUpdateTimer)
		flowLineUpdateTimer = null
	}
	if (showTrackTimer) {
		clearTimeout(showTrackTimer)
		showTrackTimer = null
	}
	if (showAreaTimer) {
		clearTimeout(showAreaTimer)
		showAreaTimer = null
	}
	if (showLabelTimer) {
		clearTimeout(showLabelTimer)
		showLabelTimer = null
	}

	// 清理待处理的更新数据
	pendingUpdates.targets.clear()
	pendingUpdates.resources.clear()
	pendingUpdates.terminals.clear()

	// 清理时间轴相关资源
	if (timeline) {
		timeline.destroy()
		timeline = null
	}
	if (visGroups) {
		visGroups.clear()
		visGroups = null
	}
	if (visItems) {
		visItems.clear()
		visItems = null
	}
})
// 优化的 watch 监听器，添加防抖和条件检查
let showTrackTimer = null
let showAreaTimer = null
let showLabelTimer = null

// 显隐航迹（优化版本，添加防抖）
watch(() => state.showTrack, (val) => {
	if (showTrackTimer) clearTimeout(showTrackTimer)
	showTrackTimer = setTimeout(() => {
		mapTool.showEffByAll('line', val)
		showTrackTimer = null
	}, 100)
}, { flush: 'post' })

// 显隐雷达（优化版本，添加防抖和批量处理）
watch(() => state.showArea, (val) => {
	if (showAreaTimer) clearTimeout(showAreaTimer)
	showAreaTimer = setTimeout(() => {
		// 批量处理，减少单独的 DOM 操作
		const operations = []
		state.resourceList.forEach((ele) => {
			let type = ele.dataType == 2 || ele.dataType == 3 ? 'cylinder' : 'radar'
			let resourceId = ele.id || ele.equipmentId || ele.equipmentDetail?.id
			if (resourceId) {
				operations.push({ id: resourceId, type, show: val })
			}
		})

		// 批量执行操作
		operations.forEach(op => {
			mapTool.showEffById(op.id, op.type, op.show)
		})
		showAreaTimer = null
	}, 100)
}, { flush: 'post' })

// 显隐名称（优化版本，添加防抖）
watch(() => state.showLabel, (val) => {
	if (showLabelTimer) clearTimeout(showLabelTimer)
	showLabelTimer = setTimeout(() => {
		mapTool.showEffByAll('label', val)
		showLabelTimer = null
	}, 100)
}, { flush: 'post' })

// 优化后的滚动监听机制
let wasAtBottom = true
let scrollUpdateTimer = null

// 防抖的滚动更新函数
const debouncedScrollUpdate = () => {
	if (scrollUpdateTimer) {
		clearTimeout(scrollUpdateTimer)
	}
	scrollUpdateTimer = setTimeout(() => {
		if (logContainer.value && wasAtBottom) {
			logContainer.value.scrollTop = logContainer.value.scrollHeight
		}
		scrollUpdateTimer = null
	}, 16) // 约60fps的更新频率
}

// 简化的滚动监听
watch(
	() => sockeMessStore.tsSocket.length,
	() => {
		if (!logContainer.value) return

		const container = logContainer.value
		const { scrollTop, clientHeight, scrollHeight } = container

		// 检测是否在底部（容差10px）
			wasAtBottom = scrollTop + clientHeight >= scrollHeight - 10

		// 如果在底部，延迟滚动到新底部
				if(wasAtBottom){
			nextTick(debouncedScrollUpdate)
				}
	},
	{ flush: 'post' }
)

var currentPage,currentIds
const terminalStatus = ref(null)
let terminalStatusTimer
// 获取终端当前接入状态
const getTerminalStatus = (immediate) => {
	const id = state.chooseTargetForm.nodeId
	if(!id) return
	if(terminalStatusTimer) return
	terminalStatusTimer = setTimeout(async () => {
		terminalStatusTimer = null
		const {data} = await situationApi.getTerminalStatus(id)
		terminalStatus.value = data.data == 1 ? '已接入' : '未接入'
	}, immediate === true ? 0 : 300)
}
// 根据终端编号查询终端体制状态信息（优化版本，添加错误处理和数据量限制）
const getModeStatus = async () => {
	const {nodeId} = state.chooseTargetForm
	if(!nodeId) return

	try {
		const {data={data:{}}} = await controlApi.getModeStatus(nodeId)
		let time
		const isLoseLock = Object.values(data.data).some((ele) => {
			time = ele.startTime
			return ele.value == 0
		})
		const logs = termLockLog.value[nodeId]
		if(!logs) {
			termLockLog.value[nodeId] = [{time, isLoseLock}]
			return
		}
		const lastData = logs[logs.length - 1]
		if(lastData.isLoseLock !== isLoseLock){
			logs.push({time, isLoseLock})

			// 限制日志数量，防止内存无限增长
			if(logs.length > 150) {
				termLockLog.value[nodeId] = logs.slice(-100) // 保留最新的50条
			}
		}
	} catch (error) {
		console.error('获取终端体制状态失败:', error)
	}
}
//切换场景
function pageRadioChange(){
	// mapTool.removeAll()
	// state.playStatus = "pause"
	if(state.pageRadio != 1){
		state.showArea = true
	}
	state.showLabel = true;
	state.chooseTask = undefined
	state.chooseResource = undefined;
	state.chooseTarget = undefined;
	state.showResourceList = []
	state.showTargetList = []
	state.targetForm = {}
	state.detailShow = false
	mapTool.removeAllFlowLine()

	switch(state.pageRadio){
		case "1":

			// drawChart2()

			// drawChart4()
			setTimeout(() => {
				// mapTool.showEffByAll("cylinder", false)
				const height = FlyToHeightConfig[panoramaId] || 1000000
				const position = FlyToHeightConfig[panoramaId + '-position']
				position && Viewer.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(position[0],position[1],height)})
			},1000);
			mapTool.showEntityByAll(true,false)
			state.chooseTask = undefined
			state.chooseTarget = undefined
			state.chooseResource = undefined
			state.showTargetList = [...state.targetList]
			state.showResourceList = [...state.resourceList]
			break;
		case "2":
			setTimeout(() => {
				initTimeLine("taskTimeLine")
				drawTimeLine("taskTimeLine")
				mapTool.showEntityByAll(true,true)
			},0);
			break;
		case "3":
			setTimeout(() => {
				initTimeLine("resourceTimeLine")
				drawTimeLine("resourceTimeLine")
				mapTool.showEntityByAll(true,true)

			},0);
			break;
		case "4":
			setTimeout(() => {
				initTimeLine("targerTimeLine")
				drawTimeLine("targerTimeLine")

				mapTool.showEntityByAll(true,true)

			},0);
			break;
		default:
			break;
	}
}

//获取

//右上统计图 //目标执行情况
function drawChart2(){
	let chart = echarts.init(Chart2.value)
	let option = {
		color:chartColors,

		tooltip:{
			trigger:'axis',
			axisPointer:{
				type:'shadow'
			}
		},
		legend:{
			data:['遥控','遥测','测量','数传'],
			textStyle:{
				color:fontColor,
			},
		},
		toolbox:{
			show:true,
			orient:'vertical',
			left:'right',
			top:'center',
			// feature: {
			//     mark: {show: true},
			//     dataView: {show: true, readOnly: false},
			//     magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
			//     restore: {show: true},
			//     saveAsImage: {show: true}
			// }
		},
		xAxis:[
			{
				type:'category',
				axisTick:{show:false},
				data:['无人机','无人艇','无人车','弹'],
				axisLine:window.axisLine,
				axisLabel:{
					color:fontColor,
					rotate:45,
				},
			}
		],
		yAxis:[
			{
				type:'value',
				splitLine:window.splitLine,
				axisLine:window.axisLine,
				axisLabel:{
					color:fontColor,
				},
			}
		],
		series:[
			{
				name:'遥控',
				type:'bar',
				barGap:0,
				// label: "遥控",
				emphasis:{
					focus:'series'
				},
				data:[0,0,0,0]
			},
			{
				name:'遥测',
				type:'bar',
				// label: labelOption,
				emphasis:{
					focus:'series'
				},
				data:[0,0,0,0]


			},
			{
				name:'测量',
				type:'bar',
				// label: labelOption,
				emphasis:{
					focus:'series'
				},
				data:[0,0,0,0]


			},
			{
				name:'数传',
				type:'bar',
				// label: labelOption,
				emphasis:{
					focus:'series'
				},
				data:[0,0,0,0]


			}
		]
	};
	situationApi.getTargetBz({requirementId:panoramaId}).then(res => {
		if(res.data.code == 200){
			let data = res.data.data
			let targetType = ['无人机','无人艇','无人车',"弹"]
			data.forEach(item => {
				let businessIndex = item.taskType[0] - 1
				let targetTypeIndex = targetType.indexOf(item.target.dataTypeValue)
				if(targetTypeIndex > -1 && businessIndex > -1){
					option.series[businessIndex].data[targetTypeIndex] += 1
				}
			})
			chart.setOption(option,true)
		}else{
			ElMessage.error(res.data.message)
		}
	})

	// chart.setOption(option, true)


}

//右下统计图 //资源接入情况 第一版-废弃
function drawChart4(){
	let chart = echarts.init(Chart4.value)
	// 来源 1 陆 2 海 3 空 4 天
	let typeList = [
		"地基",
		"海基",
		"空基",
		"天基",]
	let option = {
		color:chartColors,

		tooltip:{
			trigger:'axis',
			axisPointer:{
				type:'shadow'
			}
		},
		legend:{
			data:[],
			type:'scroll',
			orient:"horizontal",
			textStyle:{
				color:fontColor,
			},
		},
		toolbox:{
			show:false,
			orient:'vertical',
			left:'right',
			top:'center',
			// feature: {
			//     mark: {show: true},
			//     dataView: {show: true, readOnly: false},
			//     magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
			//     restore: {show: true},
			//     saveAsImage: {show: true}
			// }
		},
		yAxis:[
			{
				type:'category',
				axisTick:{show:false},
				data:typeList,
				axisLine:window.axisLine,
				axisLabel:{
					color:fontColor,
					rotate:45,
				},
			}
		],
		xAxis:[
			{
				type:'value',
				splitLine:window.splitLine,
				axisLine:window.axisLine,
				axisLabel:{
					color:fontColor,
				},
			}
		],
		series:[]
	};

	// typeList.forEach(type => {
	//     let seriesItem = {
	//         name: type,
	//         type: 'bar',
	//         emphasis: {
	//             focus: 'series'
	//         },
	//         data: []
	//     }
	//     option.series.push({ ...seriesItem })
	// })
	situationApi.getRealTimeAreaType({requirementId:panoramaId}).then(res => {
		if(res.data.code == 200){
			let resData = res.data.data
			// 兵种
			for(const key in resData){
				let data = resData[key]
				// 域
				data.forEach(item => {
					option.legend.data.push(item.areaName)
					let seriesItem = {
						name:item.areaName,
						type:'bar',
						stack:'total',
						emphasis:{
							focus:'series'
						},
						data:[0,0,0,0]
					}
					let count = 0

					item.dataGenerals?.forEach(general => {
						count = count + general.count
					})
					seriesItem.data[key - 1] = count
					option.series.push({...seriesItem})
				})
			}
			chart.setOption(option,true)
		}else{
			ElMessage.error(res.data.message)
		}
	})


}

window.historyTrack = {}

function getSocketData(evt){
	//
	// 目标
	let detailData = evt.detail.data;
	if(detailData.type == 5){
		let _data = evt.detail.data.data;
		_data.forEach(item => {
			item.id = item.bindingTarget.id;
			/*20250823 使用目标id连线有问题
			item.id = item.mbId;
			item.direction = item.rollAngle;
			*/
			// item.name = item.name + item.id
			// item.longitude = 110 + clacNum*0.1;
			// item.latitude = 29;
			let _type = 4;
			if(item.targetType == 0){
				_type = 6;
			}else if(item.targetType == 1){
				_type = 5;
			}
			//region 用于 【接入平台测试】， 后面将其删除
			if(simulateDD.value && item.bindingTarget.targetTypeValue === '无人机'){
				item.bindingTarget.targetTypeValue = '导弹'
				item.bindingTarget.name = 'DF-26'
				_type = 7;
				const entity = mapTool.getById(item.bindingTarget.id)?.entity
				if(entity){
					entity.billboard.image = window.dataTypeImg[_type - 1]
				}
			}else if(item.bindingTarget.targetTypeValue === '无人机'){
				const entity = mapTool.getById(item.bindingTarget.id)?.entity
				if(entity){
					entity.billboard.image = window.dataTypeImg[_type - 1]
				}
			}
			//endregion

			// 只更新变化了的字段，避免页面闪动
			if(item.id == state.chooseTargetForm.id || item.bindingTarget.id == state.chooseTargetForm.id){
				updateChangedFields(state.targetForm, item)
			}

			// 使用批量更新机制，而不是立即更新
			const targetData = prepareTargetData(item, _type)
			pendingUpdates.targets.set(item.bindingTarget.id, targetData)
		})
		// 触发防抖更新
		debouncedDataUpdate()
		// let targetList = _data
		// console.log('targetList',targetList);
		/*// 只更新变化了的字段，避免页面闪动
		const newTargetForm = targetList.find(item => item.id == state.chooseTargetForm.id || item.bindingTarget.id == state.chooseTargetForm.id) || {}
		updateChangedFields(state.targetForm,newTargetForm)
		targetList.forEach(item => {
			let index = state.targetList.findIndex(item2 => item2.id == item.bindingTarget.id)
			if(index == -1){
				state.targetList.push({...item.bindingTarget,nodeId:item.nodeId})
			}else{
				state.targetList.splice(index,1,{...item.bindingTarget,nodeId:item.nodeId})
			}
		})*/
	}
	// 终端
	else if(detailData.type == 6){
		let nodeList = Object.keys(detailData.data)
		let nodeId = nodeList[0]
		const {terCurrentMode,terCurrentNode,createLinkDataFrame={}} = detailData.data[nodeId]
		if(detailData.data[nodeId].bindingTarget){
			let bindingTarget = detailData.data[nodeId].bindingTarget
			//region 用于 【接入平台测试】， 后面将其删除
			if(simulateDD.value && bindingTarget.targetTypeValue === '无人机'){
				bindingTarget.targetTypeValue = '导弹'
				bindingTarget.name = 'DF-26'
				bindingTarget.dataType = 7
				const entity = mapTool.getById(bindingTarget.id)?.entity
				if(entity){
					entity.billboard.image = window.dataTypeImg[bindingTarget.dataType - 1]
				}
			}else if(bindingTarget.targetTypeValue === '无人机'){
				const entity = mapTool.getById(bindingTarget.id)?.entity
				if(entity){
					entity.billboard.image = window.dataTypeImg[bindingTarget.dataType - 1]
				}
			}
			// endregion
			if(detailData.data[nodeId].curStationName){
				bindingTarget.curStationName = detailData.data[nodeId].curStationName
			}
			let index = state.targetList.findIndex(item2 => item2.id == bindingTarget.id)
			if(index === -1){
				// 使用批量更新机制
				const targetData = {
					id:bindingTarget.id,
					bindingTarget,
					nodeId,
					terCurrentMode,
					terCurrentNode,
					longitude:createLinkDataFrame.longitude,
					latitude:createLinkDataFrame.latitude,
					altitude:createLinkDataFrame.altitude,
				}
				const preparedData = prepareTargetData(targetData,bindingTarget.dataType || 9)
				pendingUpdates.targets.set(bindingTarget.id,preparedData)
			}
			if(state.chooseTargetForm.id === bindingTarget.id){
				state.chooseTargetForm = Object.assign(state.chooseTargetForm,{...bindingTarget,nodeId:nodeId,terCurrentMode,terCurrentNode})
				nextTick(() => {
					getModeStatus()
				})
				getTerminalStatus()
			}
		}
		state.gplotData =
			{
				nodeRelation:detailData.data[nodeId].nodeRelation,
				nodeId:nodeId
			}
		if(nodeId == 0){
			const terminalData = {
				...JSON.parse(JSON.stringify({
					...detailData.data[nodeId],
					nodeId:nodeId,
					terCurrentMode,
					terCurrentNode
				}))
			}
			pendingUpdates.terminals.set(0, terminalData)
			debouncedDataUpdate()
			return
		}
		// add 2025-4-10 start  待验证 wlj

		let bindingTarget = detailData.data[nodeId]?.bindingTarget
		if(!bindingTarget || !bindingTarget.id){
			return
		}
		// if(state.chooseTargetForm.id != bindingTarget?.id){
		//     return
		// }
		// add 2025-4-10 end

		// 处理终端数据
		const processTerminalData = (nodeData) => {
			let terminalDate = JSON.parse(JSON.stringify({
				...nodeData,
				nodeId:nodeId,
				terCurrentMode,
				terCurrentNode
			}))
			terminalDate.tTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
			return {
				...terminalDate,
				sysAntJResult:getSysAntJResult(targetDataObj.value[bindingTarget.id],terminalDate)
			}
		}

		let baseTxt = detailData.data[nodeId]?.spectrumFrame?.spectrumValues
		if(!baseTxt){
			const terminalData = processTerminalData(detailData.data[nodeId])
			pendingUpdates.terminals.set(bindingTarget.id, terminalData)
		}else{
			// 处理频谱数据
			let aData = window.atob(baseTxt)
			let arr = new Int8Array(aData.length)
			// let arr2 = new Int8Array(a.length)x`
			let arr2 = []
			for(let i = 0; i < aData.length; i++){
				arr[i] = aData.charCodeAt(i)
				arr2.push(arr[i])
			}
			detailData.data[nodeId].spectrumFrame.spectrumList = arr2

			const terminalData = processTerminalData(detailData.data[nodeId])
			pendingUpdates.terminals.set(bindingTarget.id, terminalData)
			}

		// 触发防抖更新
		debouncedDataUpdate()
	}
	else if(detailData.type == 7){
		// 站点信息处理
		let stationInfo = detailData.data[0]
		let stationInfoList = detailData.data
		if(!stationInfo){
			return
		}

		stationInfo = {...stationInfo,...stationInfo.stationInfo,pageStationType:'station'}
		stationInfoList.forEach(item => item.pageStationType = 'station')

		// 使用批量更新机制
		pendingUpdates.resources.set(stationInfo.id, stationInfo)

		// 立即更新地图（地图更新不能延迟）
			mapTool.drawMap({
				...stationInfo,
				dataType:stationInfo.dataType,
				name:state.showLabel ? stationInfo.name : '',
				customInfo:`<img class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />`
			})

		// 更新选中的资源表单
			if(state.ZYForm.id == stationInfo.id){
				state.ZYForm = stationInfo
				state.stationData = stationInfoList
			}

		debouncedDataUpdate()
	}
	else if(detailData.type == 8){
		let stationInfo = detailData.data
		if(!stationInfo){
			return
		}
		stationInfo = {...stationInfo,...stationInfo.stationInfo,pageStationType:'DD'}

		// 使用批量更新机制
		pendingUpdates.resources.set(stationInfo.id, stationInfo)

		// 立即更新地图
			mapTool.drawMap({
				...stationInfo,
				dataType:1,
				name:state.showLabel ? stationInfo.name : '',
				customInfo:`<img class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />`
			})

		// 更新选中的资源表单
			if(state.ZYForm.id == stationInfo.id){
				state.ZYForm = stationInfo
				state.stationData = [stationInfo]
			}

		debouncedDataUpdate()
	}
	else if (detailData.type == 9) {
		let stationInfo = detailData.data
		if (!stationInfo) {
			return
		}
		stationInfo = {
			...stationInfo,
			...stationInfo.stationInfo,
			pageStationType: 'WRJ',
		}

		// 使用批量更新机制
		pendingUpdates.resources.set(stationInfo.id, stationInfo)

		// 立即更新地图
		mapTool.drawMap({
			...stationInfo,
			name: state.showLabel ? stationInfo.name : '',
			customInfo: `<img class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />`
		})

		// 更新选中的资源表单
			if (state.ZYForm.id == stationInfo.id) {
				state.ZYForm = stationInfo
				state.stationData = [stationInfo]
			}

		debouncedDataUpdate()
	}
	// tsDetailData.value =

}
// 准备目标数据（用于批量更新）
const prepareTargetData = (target, dataType) => {
	let direction = target.sxj || target.headAngle || target.ph || 0
	if(target.ph != undefined){
		direction = 360 - direction
	}
	const now = Date.now()
	const {
		longitude,
		latitude,
		altitude = 0,
		terCurrentMode,
		terCurrentNode
	} = target

	// 获取现有航迹数据
	const existingTarget = state.targetList.find(item => item.id === target.bindingTarget.id)
	const existingTrack = existingTarget?.targetTrack ?? []

	// 过滤3分钟内的航迹并添加新点，同时限制航迹点数量
	const filteredTrack = existingTrack.filter(e => e.time + 3 * 60 * 1000 > now)
	const targetTrack = filteredTrack.concat([{longitude, latitude, altitude, time: now}])

	const params = {
		...target.bindingTarget,
		terCurrentMode,
		terCurrentNode,
		longitude,
		latitude,
		altitude,
		nodeId: target.nodeId,
		targetTrack,
		direction
	}

	// 更新地图实体（这部分需要立即执行）
	const entityObj = {
		...target,
		dataType,
		direction,
		name: state.showLabel ? params.name : '',
		targetTrack,
		color: 'red',
		customInfo: `<img class='popImg' src="${target.bindingTarget.imgUrl}" width="110" height="110" />`,
	}
	mapTool.drawMap(entityObj)

	return params
}

// 绘制终端（优化版本，添加防抖和条件检查）
let flowLineUpdateTimer = null
watch(state.targetList,(list) => {
	// 防抖处理，避免频繁更新
	if (flowLineUpdateTimer) {
		clearTimeout(flowLineUpdateTimer)
	}

	flowLineUpdateTimer = setTimeout(() => {
		// 只在目标态势页面更新流线
		if (state.pageRadio !== "4") return

	list.forEach((e) => {
		const {terCurrentNode} = targetDataObj.value[e.id] ?? {}
			if (!terCurrentNode) return

			const data = window.flowLineList?.find(item => item.targetId === e.id)
		if(data && data.localId !== terCurrentNode){
			mapTool.removeFlowLine(data.localId,data.targetId)
			mapTool.removeFlowLine(data.targetId,data.localId)
		}
		// const lineId = terCurrentNode + '_' + e.id + '_flowLine'
		// if (window.flowLineList.find(item => item.id === lineId)) return
		mapTool.drawflowLine(terCurrentNode,e.id,'#00ff00')
		mapTool.drawflowLine(e.id,terCurrentNode,'#00ff00')
	})
		flowLineUpdateTimer = null
	}, 100) // 100ms防抖
}, { deep: false }) // 不进行深度监听，提高性能

// 校验决策模块结果， accHandEn的值1 -> 0,或者0 -> 0,变化时保留1时的值
function getSysAntJResult(data,newData){
	if(!data || !data.sysAntJResult) return newData.sysAntJResult
	const oldFlag = data.sysAntJResult.accHandEn === 1 || data.sysAntJResult.accHandEn === 0
	const newFlag = newData.sysAntJResult?.accHandEn === 0
	data.sysAntJResult.accHandEn = 0
	return oldFlag && newFlag ? data.sysAntJResult : newData.sysAntJResult
}

var visGroups = new vis.DataSet([]); //时间轴分组
var visItems = new vis.DataSet([]);
; //时间轴项目
var timeline; //时间轴管理器
/**
 * @description 绘制时间轴
 */
function initTimeLine(domID){
	var container = document.getElementById(domID);
	timeline = new vis.Timeline(container);
	visGroups = new vis.DataSet([]);
	visItems = new vis.DataSet([]);
	// let start = moment(timelineStart).format("YYYY-MM-DD HH:mm:ss");
	// let min = moment(timelineStart).subtract(1, "h").format("YYYY-MM-DD HH:mm:ss")
	// let end = moment(timelineEnd).format("YYYY-MM-DD HH:mm:ss");
	let options = {
		autoResize:true,
		height:"90%", //高度
		width:"100%", //宽度
		min:"2000-01-01 00:00:00", //设置最小时间范围
		max:"2099-12-31 23:59:59", //设置最大时间范围
		// start: "2024-08-06 15:03:18", //设置开始时间
		// end: "2024-08-07 15:03:18", //设置结束时间
		stack:true, // ture则不重叠
		limitSize:true,
		verticalScroll:true,
		// cluster: true, //数据量过大时使用。
		locale:"zh-cn",
		xss:{
			disabled:true,
		},
		// groupOrder: function (a, b) {
		//     return a.value - b.value;
		// },
		editable:false,
		showCurrentTime:false,
		moment:function(date){
			// return moment(date).format("YYYY-MM-DD HH:mm:ss");
			return moment(date);
		},
		locale:moment.locale("zh-cn")

	};
	timeline.setOptions(options);
	timeline.setGroups(visGroups);
	timeline.setItems(visItems);
	timeline.addCustomTime("2000-01-01 00:00:01");
}

//绘制时间轴项目
function drawTimeLine(domID){
	// if (domID == "taskTimeLine") {
	// state.taskList.forEach(item => {
	//     visGroups.add({ id: "111" + item.id, content: '规划-' + item.taskName, value: "111" + item.id });
	//     visGroups.add({ id: "222" + item.id, content: '实际-' + item.taskName, value: "222" + item.id });
	//     visItems.add({
	//         id: item.id + "guihua",
	//         group: "111" + item.id,
	//         content: item.taskName,
	//         start: item.startTime,
	//         end: item.endTime,
	//     })
	//     visItems.add({
	//         id: item.id + "shiji",
	//         group: "222" + item.id,
	//         content: item.taskName,
	//         start: timeSpan(new Date(item.startTime).getTime() - Math.random() * 3000),
	//         end: timeSpan(new Date(item.endTime).getTime() - Math.random() * 2000),
	//     })
	// })
	visItems.clear()
	visGroups.clear()

	timeline.setItems(visItems);
	timeline.setGroups(visGroups);

	// }
}

/**
 * @description 任务表格点击事件
 */
const taskTableClick = (row) => {
	clearData()
	state.chooseTask = row.id
	const height = FlyToHeightConfig[panoramaId] || 1000000
	const position = FlyToHeightConfig[panoramaId + '-position']
	position && Viewer.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(position[0],position[1],height)})
	dataApi.getTaskByTaskId({id:row.id}).then((res) => {
		if(res.data.code == 200){
			state.taskForm = res.data.data;
			state.taskForm.schedule = row.schedule
			// state.playStatus = "play"
			getResourceByTask(row.id)
			visGroups.add({id:"222" + row.id,content:'',value:"222" + row.id});
			timeline.setGroups(visGroups);

			timeline.setOptions({
				min:timeSpan(new Date(state.taskForm.startTime).getTime() - 86400),
				start:timeSpan(new Date(state.taskForm.startTime).getTime() - 86400),
				max:timeSpan(new Date(state.taskForm.endTime).getTime() + 86400),
				end:timeSpan(new Date(state.taskForm.endTime).getTime() + 86400),
			})
			state.showTargetList = state.targetList.filter(item => {
				return item.targetId == state.taskForm.target.targetId
			})
			state.showTargetList.forEach(item => {
				mapTool.showEntityById(item.targetId,true)
			})
		}else{
			ElMessage.error(res.data.message);
		}
	});
};

//根据任务ID获取资源
function getResourceByTask(taskId){
	resourceApi.getEquipmentByTask({taskId:taskId,size:999,page:1}).then(res => {
		if(res.data.code == 200){
			state.showResourceList = res.data.data.records
			state.showResourceList.forEach(item => {
				mapTool.showEntityById(item.equipmentId,true)
			})
			state.playStatus = "play"
		}else{
			ElMessage.error(res.data.message)
		}
	})
}

//资源行点击事件
const taskTableClickZY = (row) => {
	// state.ZYForm = row
	// return

	// clearData()
	// state.chooseResource = row.equipmentId
	// // window.EVGISMAP("flyTo", {
	// //     center: [row.equipmentDetail.longitude, row.equipmentDetail.latitude],
	// // });
	// const height = FlyToHeightConfig[panoramaId] || 1000000
	// Viewer.camera.flyTo({ destination: Cesium.Cartesian3.fromDegrees(row.equipmentDetail.longitude, row.equipmentDetail.latitude, height) })
	// dataApi.getDetailById({ dataId: row.equipmentId, dataTypeId: row.generalId }).then(res => {
	//     if (res.data.code == 200) {
	//         state.ZYForm = res.data.data;
	//         getDataByResource(row)
	//     } else {
	//         ElMessage.error(res.data.message);
	//     }
	// })
	//

	state.ZYForm = row
	if(row.id === "00000000"){
		if(state.stationMoreShow === true){
			clickGplot()
		}
		return
	}
	if(state.gplotShow === true){
		clickStationRow()
	}
	window.EVGISMAP("flyTo",{
		center:[row.longitude,row.latitude],
	});
}

function chooseMB(scope){
	if(state.chooseTargetForm.id && scope.row.id == state.chooseTargetForm.id){
		return "chooseRow"
	}else{
		return ''
	}
}

function chooseZY(scope){
	if(state.ZYForm.id && scope.row.id == state.ZYForm.id){
		return "chooseRow"
	}else{
		return ''
	}
}

//获取最大最小时间
function getMaxMinTime(list){
	let max,min
	list.forEach(item => {
		if(item.startTime < min || !min){
			min = item.startTime
		}
		if(item.endTime > max || !max){
			max = item.endTime
		}
	})
	return {min,max}
}

var taskByEquipment

//获取资源对应的需要显示的目标
function getDataByResource(row){
	mapTool.showEntityById(row.id,true)
	situationApi.getDataByEquipment({equipmentId:row.id,requirementId:panoramaId}).then(res => {
		if(res.data.code == 200){
			let tasks = res.data.data.tasks || []
			let targets = res.data.data.targets || []
			taskByEquipment = res.data.data
			let max,min
			window.aaa = []
			tasks.forEach(item => {
				visGroups.add({id:item.id,content:'',value:item.id});
				aaa.push(item.id)
				item.occupancies.forEach(occ => {
					if(occ.startTime < min || !min){
						min = occ.startTime
					}
					if(occ.endTime > max || !max){
						max = occ.endTime
					}

				})
			})

			timeline.setGroups(visGroups);
			// timeline.setItems(visItems);

			timeline.setOptions({
				min:timeSpan(new Date(min).getTime() - 8640 * 10),
				start:timeSpan(new Date(min).getTime() - 8640 * 10),
				max:timeSpan(new Date(max).getTime() + 8640 * 10),
				end:timeSpan(new Date(max).getTime() + 8640 * 10),
			})
			let ids = targets.map(item => {
				return item.targetId
			})
			//控制显示
			state.showTargetList = state.targetList.filter(item => {
				return ids.includes(item.id)
			})
			targets.forEach(item => {
				mapTool.showEntityById(item.targetId,false)

			})
			state.showResourceList = state.resourceList.filter(item => {
				return row.id == item.id
			})
			mapTool.showEntityById(row.id,true)


			state.playStatus = "play"

		}else{
			ElMessage.error(res.data.message)
		}
	})
}

function getRowData(row){
	areaApi.getAreaGeneralDataDetail({dataTypeId:row.generalId,dataId:row.targetId}).then(res => {
		if(res.data.code == 200){
			// 只更新变化了的字段，避免页面闪动
			updateChangedFields(state.targetForm,res.data.data)
		}else{
			ElMessage.error(res.data.message)

		}
	})

}

// 只更新变化了的字段的函数
function updateChangedFields(target,source){
	for(const key in source){
		if(source.hasOwnProperty(key)){
			// 只有当值真正发生变化时才更新
			if(target[key] !== source[key]){
				target[key] = source[key]
			}
		}
	}
}

function targetTableClick(row){
	state.chooseTargetForm = row

	// 清空目标状态弹窗内容
	state.targetForm = {}

	// controlApi.getTerminalMode({ terminalSeq: row.nodeId }).then(res => {
	//     if (res.data.code == 200) {
	//         state.chooseTargetForm._modeCtrl = res.data.data
	//     } else {
	//         ElMessage.error(res.data.message)
	//     }
	// })
	window.EVGISMAP('flyTo', {
		center: [row.longitude, row.latitude, 10000],
	})
	getTerminalStatus(true)
}

//初始化数据
function clearData(){
	visGroups?.clear()
	visItems?.clear()
	state.chooseTask = undefined
	state.chooseTarget = undefined
	state.chooseResource = undefined
	mapTool.showEntityByAll(false)
	// 关闭标牌框
	window.EVGISMAP('closeDiv',{className:'.infoDiv'})
	// 删除聚焦框
	window.selectedId && window.EVGISMAP("removeGroupEntityById",{
		id:window.selectedId + 'makerGroupOnlyOne',
		group:'makerGroup'
	});

	state.showTargetList = []
	state.showResourceList = []
}

//目标更多点击事件
function clickDetileRow(){
	state.terminalStateShow = false
	state.detailShow = true
}

// 终端更多点击事件
function clickTerminalRow(){
	state.detailShow = false
	state.terminalStateShow = true
}

//拓扑图点击事件
function clickGplot(){
	state.gplotShow = true
	state.stationMoreShow = false

}

//站点更多点击事件
function clickStationRow(){
	state.stationMoreShow = true
	state.gplotShow = false


}

function terminalControl(){
	state.terminalControlShow = true
	state.terminalControlForm = {
		// 定义 整机参数设置 初始值
		machineParam:{
			targetNode:1,
			modeCtrl:0,
			rxFreq:2070,
			txFreq:2260,
			txAtte:30,
			// nodeId: undefined,
			nodeId:"1834120494834802689",
		},dataControl:{
			targetNode:undefined,
			modeCtrl:'',
			transModState:undefined
		}
	}
}

//整机控制帧发送
function setMachineParam(){
	controlApi.setMachineParam(state.terminalControlForm.machineParam).then(res => {
		if(res.data.code == 200){
			ElMessage.success("发送成功")
		}else{
			ElMessage.error("发送失败")
		}
	})
}

//查询当前选中的终端的体制-用于显示
function targetNodeChange(val){
	controlApi.getTerminalMode({terminalSeq:val}).then(res => {
		if(res.data.code == 200){
			state.terminalControlForm.dataControl.modeCtrl = res.data.data
		}else{
			ElMessage.error(res.data.message)
		}
	})
}

//数据传输控制
function setDataControlParam(){
	controlApi.setDataControlParam(state.terminalControlForm.dataControl).then(res => {
		if(res.data.code == 200){
			ElMessage.success("发送成功")
		}else{
			ElMessage.error("发送失败")
		}
	})
}

// 日志类型过滤处理函数
function handleLogTypeChange(value){
	console.log('日志类型过滤器变更:',value)
}

// 过滤后的日志数据计算属性（优化版本，添加分页和数量限制）
const filteredLogs = computed(() => {
	let logs = []

	if(!state.logTypeFilter){
		// 如果没有选择过滤类型，显示所有日志
		logs = sockeMessStore.tsSocket
	} else {
	// 根据选择的类型过滤日志
		logs = sockeMessStore.tsSocket.filter(log => {
		return log.source === state.logTypeFilter
	})
	}

	// 限制显示的日志数量，只显示最新的200条，提高渲染性能
	return logs.slice(-100)
})

//无人机模拟导弹
function uavSimulateDD(val){
	//region 用于 【接入平台测试】， 后面将其删除
	if(simulateDD.value && state.chooseTargetForm.targetTypeValue === '无人机'){
		state.chooseTargetForm = Object.assign(state.chooseTargetForm, {
			name: 'DF-26',
			targetTypeValue: '导弹'
		})
	}else if(state.chooseTargetForm.targetTypeValue === '导弹'){
		state.chooseTargetForm = Object.assign(state.chooseTargetForm, {
			name: 'SHC-Y50',
			targetTypeValue: '无人机'
		})

	}
	//endregion

	controlApi.uavSimulateDD(val).then(res => {
		if(res.data.code == 200){
			simulateDD.value == res.data.data
			ElMessage.success("切换成功")
		}else{
			ElMessage.error("切换失败")
		}
	})


}
</script>

<style lang='less' scoped>
.showOrHidden-box {
	position: absolute;
	right: 500px;
	top: 90px;
	// z-index: 999;
	/* 性能优化 */
	transform: translateZ(0);
	will-change: transform;
	/* 避免重排重绘 */
	contain: layout style paint;
}

.scheduleClass {
	height: 100%;
	width: 100%;
	overflow-y: auto;
	box-sizing: border-box;
	/* 性能优化 */
	transform: translateZ(0);
	-webkit-overflow-scrolling: touch;
	will-change: scroll-position;
	/* 避免重排重绘 */
	contain: layout style paint;

	.task_schedule {
		height: auto;
		min-height: 45px;
		width: 100%;
		padding: 0 10px;
		margin-bottom: 10px;
		box-sizing: border-box;
		/* 性能优化 */
		transform: translateZ(0);
		contain: layout style paint;
	}
}

.chartClass {
	width: 100%;
	height: 100%;
	background: #022141c0;
	/* 性能优化 */
	transform: translateZ(0);
	will-change: transform;
	/* 避免重排重绘 */
	contain: layout style paint;

	:deep(.vis-content > .vis-labelset .vis-inner) {
		font-weight: bolder;
		font-size: 16px;
		margin: 0 5px;
	}

	:deep(.vis-group) {
		> .vis-item.vis-selected {
			background: #2f557b !important;
		}

		.vis-item {
			background: rgb(29, 94, 142);
		}
	}
}

.pageRadio {
	position: absolute;
	top: 10%;
	left: 50%;
	transform: translate(-50%, 0);
	background: #022141c0;
	border: 1px solid #2e5175;
}

.targetForm_class {
	height: calc(100% - 30px);

	:deep(.el-form-item) {
		margin-bottom: 0;

		.el-form-item__label {
			padding-right: 8px;
		}
	}
}

.custom-table {
	:deep(.chooseRow) {
		background: rgba(55, 160, 234, 0.5) !important
	}
}

/* 日志容器优化样式 */
.log-container {
	/* 启用硬件加速 */
	transform: translateZ(0);
	will-change: scroll-position;
	/* 优化滚动性能 */
	-webkit-overflow-scrolling: touch;
	scroll-behavior: smooth;
}

.log-item {
	/* 避免重排重绘 */
	contain: layout style paint;
	/* 优化渲染性能 */
	transform: translateZ(0);
}

.log-time {
	color: #d9dfe8d9;
}

.log-bracket {
	color: #d9dfe8d9;
}

.log-type {
	font-weight: bold;
}

.log-error {
	color: #f00 !important;
}

.log-success {
	color: #008000 !important;
}

.log-message {
	color: #d9dfe8d9;
}

/* 表格性能优化 */
.custom-table {
	/* 启用硬件加速 */
	transform: translateZ(0);

	:deep(.el-table__body-wrapper) {
		/* 优化滚动性能 */
		-webkit-overflow-scrolling: touch;
		will-change: scroll-position;
	}

	:deep(.el-table__row) {
		/* 避免重排重绘 */
		contain: layout style paint;
	}
}
</style>
