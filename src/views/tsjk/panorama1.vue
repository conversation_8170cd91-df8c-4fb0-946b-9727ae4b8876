<template>
    <CesiumBox></CesiumBox>
    <!-- <el-radio-group v-model="state.pageRadio" class="pageRadio" @change="pageRadioChange">
        <el-radio-button label="1">全景态势</el-radio-button>
        <el-radio-button label="2">任务态势 </el-radio-button>
        <el-radio-button label="3">资源态势</el-radio-button>
        <el-radio-button label="4">目标态势</el-radio-button>
    </el-radio-group> -->
    <div class="showOrHidden-box" :style="{ right: state.pageRadio == 1 ? '500px' : '16%' }">
        <template v-if="state.pageRadio != 1">
            <!-- <div>
                <label>航迹显隐：</label>
                <el-switch
                    v-model="state.showTrack"
                    inline-prompt
                    active-text="显示"
                    inactive-text="隐藏"
                />
            </div> -->
        </template>
		<div>
			<label>范围显隐：</label>
			<el-switch v-model="state.showArea" inline-prompt active-text="显示" inactive-text="隐藏" />
		</div>
        <div>
            <label>名称显隐：</label>
            <el-switch v-model="state.showLabel" inline-prompt active-text="显示" inactive-text="隐藏" />
        </div>
    </div>
    <!-- 全景态势 -->

    <customPopup left="0.5%" top="7%" width="25%" height="92%" headType="2" v-if="state.pageRadio == 1">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">任务执行情况</span>
        </template>
        <template #content>
            <div class="scheduleClass">
                <div class="task_schedule" v-for="task in state.taskList" :key="task.id">
                    <div style="width: calc(100% - 20px);padding-right: 20px;overflow-wrap:break-word">{{ task.taskName
                        }}</div>
                    <el-progress :percentage="task.schedule || 0" :color="customColor" />
                </div>
            </div>
        </template>
    </customPopup>
    <customPopup right="0.5%" top="7%" width="25%" height="30%" headType="2" v-if="state.pageRadio == 1">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">目标接入情况</span>
        </template>
        <template #content>
            <div class="chartClass" ref="Chart2"></div>
        </template>
    </customPopup>
    <customPopup right="0.5%" top="38%" width="25%" height="30%" headType="2" v-if="state.pageRadio == 1">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">任务执行情况{{ getCurrentDate() }}</span>
        </template>
        <template #content>
            <div class="chartClass" ref="Chart3"></div>
        </template>
    </customPopup>
    <customPopup right="0.5%" bottom="1%" width="25%" height="30%" headType="2" v-if="state.pageRadio == 1">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">资源接入情况</span>
        </template>
        <template #content>
            <div class="chartClass2" ref="Chart4"></div>
        </template>
    </customPopup>
    <!-- 任务态势 -->
    <customPopup left="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 2">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">任务列表</span>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;">
                <el-table ref="taskTableRef" class="custom-table" :data="state.taskList" highlight-current-row
                    style="height: 100%" @row-click="taskTableClick">
                    <el-table-column align="center" prop="taskName" label="任务名称" min-width="3"></el-table-column>
                    <el-table-column align="center" prop="status" label="任务类型" min-width="2">
                        <template v-slot="scope">
                            <span>{{ scope.row.taskType[0] ? dictValue("businessType", scope.row.taskType[0]) : ""
                                }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="schedule" label="任务进度" min-width="2">
                        <template v-slot="scope">
                            <span>{{ scope.row.schedule }} %</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </template>
    </customPopup>
    <customPopup right="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 2">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">任务详情</span>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;overflow-y: auto;">
                <el-form :model="state.taskForm" label-width="80px">
                    <el-form-item label="任务名称" prop="taskName">{{ state.taskForm.taskName }}</el-form-item>
                    <!-- <el-form-item label="所属需求" prop="endTime">{{ state.taskForm.requirementName }}</el-form-item> -->
                    <!-- dictValue("taskStatus", state.taskForm.status) -->
                    <el-form-item label="业务类型" prop="taskType">{{ state.taskForm.taskType &&
                        state.taskForm.taskType.length > 0 ? dictValue("businessType", state.taskForm.taskType[0]) : ""
                        }}</el-form-item>
                    <el-form-item label="任务状态" prop="taskStatus">{{
                        state.taskForm.taskStatus
                    }}</el-form-item>
                    <el-form-item label="任务进度" prop="schedule">{{ state.taskForm.schedule }}% </el-form-item>

                    <el-form-item label="任务周期" prop="endTime">{{ dictValue("repetitionType", state.taskForm.repeatType)
                        }}</el-form-item>
                    <el-form-item label="开始时间" prop="startTime">{{ state.taskForm.startTime }}</el-form-item>
                    <el-form-item label="结束时间" prop="endTime">{{ state.taskForm.endTime }}</el-form-item>
                    <el-form-item label="任务描述" prop="taskComment">{{ state.taskForm.taskComment }}</el-form-item>
                </el-form>
            </div>
        </template>
    </customPopup>
    <customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 2">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">任务执行情况</span>
            <span class="title-font" style="position: absolute; right: 3%;">当前时间：{{ timeSpan(state.currentTime)
                }}</span>
        </template>
        <template #content>
            <div class="chartClass" id="taskTimeLine"></div>
        </template>
    </customPopup>
    <!-- 资源态势 -->
    <customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 3">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">资源使用情况</span>
            <span class="title-font" style="position: absolute; right: 3%;">当前时间：{{ timeSpan(state.currentTime)
                }}</span>

        </template>
        <template #content>
            <div class="chartClass" id="resourceTimeLine"></div>
        </template>
    </customPopup>
    <customPopup left="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 3">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">资源列表</span>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;">
                <el-table ref="resourceTableRef" class="custom-table" :data="state.resourceList" style="height: 100%"
                    :row-class-name="chooseZY" highlight-current-row @row-click="taskTableClickZY">
                    <el-table-column align="center" prop="name" label="设备名称" min-width="3"></el-table-column>
                    <el-table-column align="center" prop="typeValue" label="设备类型" min-width="2">
                    </el-table-column>
                </el-table>
            </div>
        </template>
    </customPopup>
    <customPopup right="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 3">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">资源详情</span>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;overflow-y: auto;">
                <el-form :model="state.ZYForm" label-width="120px" class="targetForm_class">
                    <el-form-item label="名称" prop="name">{{ state.ZYForm.name }}</el-form-item>
                    <el-form-item label="节点代号" prop="code">{{ state.ZYForm.code }}</el-form-item>
                    <el-form-item label="频段" prop="frequency">{{ state.ZYForm.frequency }}</el-form-item>
                    <el-form-item label="多目标能力" prop="targetCount">{{ state.ZYForm.targetCount }}</el-form-item>
                    <el-form-item label="业务类型" prop="businessType">{{ dictValue("businessType",
                        state.ZYForm.businessType) }}</el-form-item>
                    <el-form-item label="EIRP（dB）" prop="eirp">{{ state.ZYForm.eirp }}</el-form-item>
                    <el-form-item label="G/T（dB）" prop="gt">{{ state.ZYForm.gt }}</el-form-item>
                    <el-form-item label="天线类型" prop="antennaType">{{ dictValue("antennaType", state.ZYForm.antennaType)
                        }}</el-form-item>
                    <el-form-item label="工作体制" prop="workSystem">{{ dictValue("workSystem", state.ZYForm.workSystem)
                        }}</el-form-item>
                    <el-form-item label="经度（°）" prop="longitude">{{ state.ZYForm.longitude }}</el-form-item>
                    <el-form-item label="纬度（°）" prop="latitude">{{ state.ZYForm.latitude }}</el-form-item>
                    <el-form-item label="高度（米）" prop="altitude">{{ state.ZYForm.altitude }}</el-form-item>
                    <el-form-item label="测控距离（米）" prop="radius">{{ state.ZYForm.radius }}</el-form-item>
                </el-form>

                <el-link style="text-align: left;float: right;margin-right: 10px" @click="clickGplot()"
                    v-if="state.ZYForm.id == '00000000'">详情</el-link>
                <el-link style="text-align: left;float: right;" @click="clickStationRow()"
                    v-else-if="state.ZYForm.id">更多...</el-link>
            </div>
        </template>
    </customPopup>

    <!-- 目标态势 -->
    <customPopup left="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 4">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">目标列表</span>
            <div style="position: absolute; right: 2%">
                <el-button @click="terminalControl">终端控制</el-button>
            </div>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;">
                <el-table ref="targetTableRef" class="custom-table" :data="state.targetList" style="height: 100%"
                    highlight-current-row :row-class-name="chooseMB" @row-click="targetTableClick">
                    <el-table-column align="center" prop="name" label="目标名称"></el-table-column>
                    <el-table-column align="center" prop="targetTypeValue" label="目标类型">


                        <!-- <template v-slot="{ row }">
                            <span>{{ state.targetTypeList[row.targetTypeValue] }}</span>
                        </template> -->
                    </el-table-column>
                    <!-- <el-table-column align="center" prop="name" label=" " width="50">
                        <template v-slot="{ row }">
                            <span @click="clickDetileRow(row)">
                                <el-icon style="cursor: pointer;">
                                    <View />
                                </el-icon>
                            </span>
                        </template>
                    </el-table-column> -->

                </el-table>
            </div>
        </template>
    </customPopup>
    <customPopup right="0.5%" top="7%" width="15%" height="30%" v-if="state.pageRadio == 4">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">目标详情</span>
        </template>
        <template #content>
            <el-form :model="state.chooseTargetForm" label-width="80px"
                style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;" class="targetForm_class"
                v-show="state.chooseTargetForm?.id" label-position="right">
                <el-form-item label="目标名称">{{ state.chooseTargetForm.name }}</el-form-item>
                <el-form-item label="目标类型">{{ state.chooseTargetForm.targetTypeValue }}</el-form-item>
                <el-form-item label="工作体制" prop="workSystem">{{ dictValue("workSystem",
                    state.chooseTargetForm.workSystem)
                    }}</el-form-item>
                <el-form-item label="业务类型" prop="businessType">{{ dictValue("businessType",
                    state.chooseTargetForm.businessType) }}</el-form-item>
                <el-form-item label="EIRP" prop="eirp">{{ state.chooseTargetForm.eirp }}dB</el-form-item>
                <el-form-item label="天线类型" prop="antennaType">{{ dictValue("antennaType",
                    state.chooseTargetForm.antennaType)
                    }}</el-form-item>
                <!-- <el-form-item v-for="item in formLabelList" :label="item.label + ' :'" :prop="item.key" :key="item.key"
                    v-show="targetShowLabelList.includes(item.key)">
                    <span v-if="!item.multiple">{{ state.chooseTargetForm[item.key] }}</span>
                    <span v-else-if="item.multiple">
                        <span v-for="(textKey, value) in state.chooseTargetForm[item.key]" :key="textKey">{{
                            value }}：{{ textKey }}<br /></span>
                    </span>
                </el-form-item> -->

            </el-form>
            <!-- v-show="state.chooseTargetForm?.id" -->
            <el-link style="text-align: left;float: right;" @click="clickDetileRow()">更多...</el-link>

        </template>
    </customPopup>
    <customPopup right="0.5%" top="38%" width="15%" height="30%" v-if="state.pageRadio == 4">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">终端状态</span>
        </template>
        <template #content>
            <el-form :model="targetDataObj[state.chooseTargetForm?.id] || {}" label-width="144px"
                style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;" class="targetForm_class"
                v-show="state.chooseTargetForm?.id" label-position="right">
                <el-form-item label="终端ID :" label-width="80px">
                    <span>
                        {{ state.chooseTargetForm.nodeId }}
                    </span>
                </el-form-item>
                <el-form-item label="终端体制 :" label-width="80px">
                    <span>
                        {{ modeCtrlList[state.chooseTargetForm.terCurrentMode] || "暂无" }}
                    </span>
                </el-form-item>
                <el-form-item v-for="item in terminalList" :label="item.label + ' :'" :prop="item.key" :key="item.key"
                    v-show="terminalBriefList.includes(item.key)">

                    <span>{{ (targetDataObj[state.chooseTargetForm?.id]?.machineFrame?.[item.key] || '-') + (item.unit
                        ||
                        '') }}</span>
                    <!-- <span>
                        <span v-for="(value, textKey ) in state.terminalDate[item.key]" :key="textKey">{{
                            textKey }}：{{ value }}<br /></span>
                    </span> -->
                </el-form-item>
            </el-form>
            <!-- v-show="state.targetForm?.id" -->
            <el-link style="text-align: left;float: right;" @click="clickTerminalRow()">更多...</el-link>
        </template>
    </customPopup>
    <customPopup left="33%" top="20%" width="35%" height="50%" v-if="state.showPic && state.pageRadio == 4">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">目标素材</span>
            <div style="position: absolute; right: 2%">
                <el-button @click="state.showPic = false">关闭</el-button>
            </div>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;overflow-y: auto;">
                <div style="width: 100%;height: 48%;">
                    <img style="height: 100%;width: 100%;" :src="hostLink + '/file/test.png'" />
                </div>
                <div style="width: 100%;;height: 48%;">
                    <video style="height: 100%;width: 100%;" controls type="video/wmv"
                        :src="hostLink + '/file/video.wmv'"></video>
                </div>
            </div>
        </template>
    </customPopup>
    <customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 4">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">目标接入情况</span>
            <span class="title-font" style="position: absolute; right: 3%;">当前时间：{{ timeSpan(state.currentTime)
                }}</span>

        </template>
        <template #content>
            <div class="chartClass" id="targerTimeLine"></div>
        </template>
    </customPopup>
    <!-- 目标详情弹窗 -->
    <customPopup left="25%" top="15%" width="50%" height="60%" v-if="state.pageRadio == 4 && state.detailShow">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">目标详情</span>
            <span class="title-font" style="position: absolute; right: 3%;cursor: pointer;"
                @click="state.detailShow = false">X
            </span>
        </template>
        <template #content>
            <tsDetail :tsData="state.targetForm" v-if="state.pageRadio == 4 && state.detailShow"></tsDetail>
        </template>
    </customPopup>

    <!-- 终端更多弹窗 -->
    <customPopup left="calc(50% - 575px)" top="15%" width="1150px" height="60%"
        v-if="state.pageRadio == 4 && state.terminalStateShow">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">终端状态-{{ targetDataObj[state.chooseTargetForm.id]?.tTime }}</span>
            <span class="title-font" style="position: absolute; right: 3%;cursor: pointer;"
                @click="state.terminalStateShow = false">X
            </span>
        </template>
        <template #content>
            <!-- <terminalState :terminalDate="state.terminalDate" v-if="state.pageRadio == 4 && state.terminalStateShow"> -->
            <terminalState :terminalDate="targetDataObj[state.chooseTargetForm.id] || {}"
                v-if="state.pageRadio == 4 && state.terminalStateShow">
            </terminalState>
        </template>
    </customPopup>
    <!-- 拓扑图弹窗 -->
    <customPopup left="25%" top="15%" width="56%" height="60%" v-if="sockeMessStore.$state.moreFlag">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">随遇接入节点</span>
            <span class="title-font" style="position: absolute; right: 3%;cursor: pointer;"
                @click="sockeMessStore.$state.moreFlag = false">X
            </span>
        </template>
        <template #content>
            <gplot :gplotData="state.gplotData" :terminalDate="targetDataObj[0] || {}"
                v-if="sockeMessStore.$state.moreFlag">
            </gplot>
        </template>
    </customPopup>
    <!-- 终端控制弹窗 -->
    <customPopup left="37.5%" top="15%" width="25%" height="50%"
        v-if="state.pageRadio == 4 && state.terminalControlShow">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">终端控制</span>
            <div style="position: absolute; right: 2%">
                <el-button @click="state.terminalControlShow = false">关闭</el-button>
            </div>
        </template>
        <template #content>
            <!-- <el-tabs v-model="state.activeName" class="tabsClass" @tab-change="tabChange"> -->
            <!-- <el-tab-pane label="整机参数设置" name="1"> -->
            <el-tabs v-model="state.contorlActive" class="tabsClass">
                <el-tab-pane label="整机参数设置" name="1" style="padding-top: 15px;">
                    <el-form :model="state.terminalControlForm.machineParam" label-width="124px"
                        style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;" label-position="right">
                        <el-form-item label="目标终端序号">
                            <el-input-number v-model="state.terminalControlForm.machineParam.targetNode"
                                :controls="false" style="width: 70%;text-align: left"></el-input-number>
                        </el-form-item>
                        <el-form-item label="站点选择">
                            <el-select v-model="state.terminalControlForm.machineParam.nodeId" style="width: 70%;">
                                <el-option value="1834120494834802689" label="航天站_S"></el-option>
                                <el-option value="1834055782248001538" label="航天站_Ka"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="体制选择">
                            <el-select v-model="state.terminalControlForm.machineParam.modeCtrl" style="width: 70%;">
                                <el-option :value="0" label="不切换体制"></el-option>
                                <el-option :value="1" label="航天测控体制-S1"></el-option>
                                <el-option :value="2" label="航天测控体制-S2"></el-option>
                                <el-option :value="3" label="航天测控体制-S3"></el-option>
                                <el-option :value="4" label="航天测控体制-S4"></el-option>
                                <el-option :value="5" label="航天测控体制-Ka1"></el-option>
                                <el-option :value="6" label="航天测控体制-Ka2"></el-option>
                                <el-option :value="7" label="航天测控体制-Ka3"></el-option>
                                <el-option :value="8" label="航天测控体制-Ka4"></el-option>
                                <el-option :value="9" label="无人机测控体制"></el-option>
                                <el-option :value="10" label="DD测控体制"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="接收频率(MHz)">
                            <el-input-number v-model="state.terminalControlForm.machineParam.rxFreq" style="width: 70%;"
                                :controls="false">
                            </el-input-number>
                        </el-form-item>
                        <el-form-item label="发射频率(MHz)">
                            <el-input-number v-model="state.terminalControlForm.machineParam.txFreq" style="width: 70%;"
                                :controls="false"></el-input-number>
                        </el-form-item>
                        <el-form-item label="发射衰减(dB)">
                            <el-input-number v-model="state.terminalControlForm.machineParam.txAtte" style="width: 70%;"
                                :controls="false"></el-input-number>
                        </el-form-item>
                        <el-form-item label="">
                            <el-button @click="setMachineParam">发送</el-button>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="数据传输控制" name="2" style="padding-top: 15px;height: 100%;">
                    <el-form :model="state.terminalControlForm.dataControl" label-width="124px"
                        style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;" label-position="right">
                        <el-form-item label="终端选择">
                            <el-select v-model="state.terminalControlForm.dataControl.targetNode" style="width: 70%;"
                                @change="targetNodeChange">
                                <el-option value="1" label="终端1"></el-option>
                                <el-option value="2" label="终端2"></el-option>
                                <el-option value="3" label="终端3"></el-option>
                                <el-option value="4" label="终端4"></el-option>
                                <el-option value="5" label="终端5"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="终端体制">
                            {{ state.terminalControlForm.dataControl.modeCtrl ?
                                modeCtrlList[state.terminalControlForm.dataControl.modeCtrl] : "暂无" }}
                        </el-form-item>
                        <el-form-item label="下行发射">
                            <el-select v-model="state.terminalControlForm.dataControl.transModState"
                                style="width: 70%;">
                                <el-option :value="0xb3d2" label="遥测任务"></el-option>
                                <el-option :value="0xc72f" label="数传任务"
                                    v-show="!['1', '2', '3'].includes(state.terminalControlForm.dataControl.modeCtrl)"></el-option>
                                <el-option :value="0x4c2d" label="遥测测试"
                                    v-show="!['1', '2', '3'].includes(state.terminalControlForm.dataControl.modeCtrl)"></el-option>
                                <el-option :value="0x3968" label="数传测试"
                                    v-show="!['1', '2', '3'].includes(state.terminalControlForm.dataControl.modeCtrl)"></el-option>
                                <el-option :value="0x235a" label="单音测试"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="">
                            <el-button @click="setDataControlParam">发送</el-button>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
        </template>
    </customPopup>
    <!-- 站点信息弹窗 -->
    <customPopup left="calc(50% - 575px)" top="15%" width="1150px" height="60%"
        v-if="state.pageRadio == 3 && state.stationMoreShow">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">资源详情 -- {{ state.ZYForm.name }}</span>
            <span class="title-font" style="position: absolute; right: 3%;cursor: pointer;"
                @click="state.stationMoreShow = false">X
            </span>
        </template>
        <template #content>
            <stationDataState :stationData="state.stationData" v-if="state.pageRadio == 3 && state.stationMoreShow">
            </stationDataState>
        </template>
    </customPopup>

</template>

<script setup>
import { onMounted, onUnmounted, ref, reactive, watch } from 'vue'
import CesiumBox from "@/components/evmap/CesiumBox.vue";
import customPopup from "@/components/customPopup.vue";
import * as situationApi from "@/service/API/system/situation.js";
import * as areaApi from "@/service/API/system/areaManage.js";
import * as dataApi from "@/service/API/home/<USER>";
import * as controlApi from "@/service/API/system/control.js";
import * as resourceApi from "@/service/API/system/resourceDispatch.js";
import * as formLabel from "./formLabel.js"
import * as formLabel2 from "./formLabel2.js"
import * as terminalLable from "./terminalLable.js"
import tsDetail from "./tsDetail.vue";
import terminalState from './terminalState.vue';
import stationDataState from './stationDataState.vue';
import { ElMessage } from 'element-plus';
import gplot from "./gplot.vue"
import { useSockeMessStore } from '@/stores/index'
const sockeMessStore = useSockeMessStore()
const dictValue = window.dictValue;
const timeSpan = window.timeSpan;
let hostLink = AdminServerApi

const taskTableRef = ref(null)
const resourceTableRef = ref(null)
const targetTableRef = ref(null)
const tarInfo = ref({
    "id": "00000000",
    "name": "随遇接入节点",
    "typeValue": "地基随遇",
    "code": "00000000",
    "longitude": 122.091505,
    "latitude": 37.543916,
    "altitude": 0,
    "updateTime": 1735887373000,
    "bottomRadius": 100000,
    "targetCount": 1,
    "eirp": 100,
    "gt": 1,
    "antennaType": 1,
    "frequency": "0,1",
    "pitchAngle": 0,
    "workSystem": "1",
    "areaId": "1838030102630240258",
    "createTime": 1726107948000,
    "generalId": "7",
    "bearingAngle": 0,
    "businessType": "1",
    customInfo: '拓扑图'

})
const customColor = [
    { color: "#f56c6c", percentage: 20 },
    { color: "#e6a23c", percentage: 40 },
    { color: "#1989fa", percentage: 60 },
    { color: "#5cb87a", percentage: 80 },
    { color: "#03a2a7", percentage: 100 },
];
const modeCtrlList = {
    '0': "不切换体制",
    '1': "航天测控体制-S",
    '2': "航天测控体制-ka",
    '3': "无人机测控体制",
    '4': "DD测控体制",
}
var formLabelList = formLabel2.failedList
var terminalList = terminalLable.failedList //终端字段（全）
var terminalBriefList = terminalLable.showBriefList //终端字段（简略显示列表）
var targetShowLabelList = formLabel2.targetShowList //目标详情展示列表
watch(() => sockeMessStore.$state.moreFlag, (val) => {
    console.log('valvvv', val);
})
const state = reactive({
    pageRadio: "1",
    taskList: [],//任务列表
    taskForm: {},//任务详情
    // ZYList: [],// sdw 资源列表
    ZYForm: [],// sdw 资源详情
    chooseTask: undefined,
    chooseResource: undefined,
    chooseTarget: undefined,
    showTargetList: [],//当前要展示的目标列表
    showResourceList: [],//当前要展示的资源列表
    targetList: [],
    targetFiled: [],//目标的字段
    showList: [],
    targetForm: {

    },//目标详情
    showPic: false,//素材展示
    getting: false,//正在获取中
    playStatus: "pause",//播放状态
    resourceList: [{
        "id": "00000000",
        "name": "随遇接入节点",
        "typeValue": "地基随遇",
        "code": "00000000",
        "longitude": 122.091505,
        "latitude": 37.543916,
        "altitude": 2000,
        "updateTime": 1735887373000,
        "bottomRadius": 100000,
        "targetCount": 1,
        "eirp": 100,
        "gt": 1,
        "antennaType": 1,
        "frequency": "0,1",
        "pitchAngle": 0,
        "workSystem": "1",
        "areaId": "1838030102630240258",
        "createTime": 1726107948000,
        "generalId": "7",
        "bearingAngle": 0,
        "businessType": "1",

    }],//展示的资源列表
    currentTime: "2000-01-01 00:00:01",//当前时间
    showTrack: true, // 显隐航迹
    showArea: true, // 显隐范围
    showLabel: true, //名称显隐
    targetTypeList: ["无人车", "无人艇", "无人机"],
    detailShow: false,//目标详情展示
    detailActiveName: "1",//目标详情展示页面的tab
    contorlActive: "1",//控制帧的tab
    terminalStateShow: false,//终端状态弹窗控制
    terminalDate: {},//终端数据
    gplotShow: false,//拓扑图显示控制
    gplotData: {},//拓扑图数据
    chooseTargetForm: {},//在列表中选中的目标
    terminalControlShow: false,//
    terminalControlForm: {
        // 定义 整机参数设置 初始值
        machineParam: {
            // targetNode: 1,
            // modeCtrl: 0,
            // rxFreq: 2070,
            // txFreq: 2260,
            // txAtte: 30
        }, dataControl: {
            // targetNode: undefined,
            // modeCtrl: '',
            // transModState: undefined
        }
    },
    stationMoreShow: false,//站点的更多显示控制
    stationData: [], //站点信息


})
const targetDataObj = ref({})
const Chart2 = ref(null);
const Chart3 = ref(null);
const Chart4 = ref(null);

let fontColor = window.$fontColor;
let TSinterval
let timePoint = 0
let uuid = parseInt(Math.random() * 100000000000);
let panoramaId
onMounted(() => {
    panoramaId = localStorage.getItem("panoramaId")
    pageRadioChange()
    window.addEventListener("onmessageTSWS", getSocketData);
    setTimeout(() => {
        mapTool.drawMap({
            customInfo: tarInfo.value.customInfo,
            id: tarInfo.value.id,
            name: state.showLabel ? tarInfo.value.name : '',
            position: [tarInfo.value.longitude, tarInfo.value.latitude, tarInfo.value.altitude],
            dataType: 9,
        })

        // 初始化目标接入情况饼图
        updateTargetPieChart()

        // 初始化资源接入情况饼图
        updateResourcePieChart()
    }, 1000);
})
onUnmounted(() => {
    window.removeEventListener("onmessageTSWS", getSocketData);
    mapTool.removeAll()
    clearInterval(TSinterval)
})
// 显隐航迹
watch(() => state.showTrack, (val) => {
    mapTool.showEffByAll('line', val)
})
// 显隐雷达
watch(() => state.showArea, (val) => {
	state.resourceList.forEach((ele) => {
		let type = ele.dataType == 2 || ele.dataType == 3 ? 'cylinder' : 'radar'
		// 兼容不同的ID字段名称：id, equipmentId, equipmentDetail.id
		let resourceId = ele.id || ele.equipmentId || ele.equipmentDetail?.id
		if (resourceId) {
			mapTool.showEffById(resourceId, type, val)
		} else {
			console.warn('资源ID未找到:', ele)
		}
	})
    // state.resourceList.forEach((ele) => {
    //     let type = ele.dataType == 2 ? 'cylinder' : 'radar'
    //     mapTool.showEffById(ele.equipmentDetail.id, type, val)
    // })
    //   mapTool.showEffByAll('radar', val)
    //   mapTool.showEffByAll('cylinder', val)
})
// 显隐名称
watch(() => state.showLabel, (val) => {
    mapTool.showEffByAll('label', val)
})

var currentPage, currentIds

//切换场景
function pageRadioChange() {
    // mapTool.removeAll()
    // state.playStatus = "pause"
    if (state.pageRadio != 1) {
        state.showArea = true
    }
    state.showLabel = true;
    state.chooseTask = undefined
    state.chooseResource = undefined;
    state.chooseTarget = undefined;
    state.showResourceList = []
    state.showTargetList = []
    state.targetForm = {}
    state.detailShow = false
    mapTool.removeAllFlowLine()
    switch (state.pageRadio) {
        case "1":
            setTimeout(() => {
                drawChart3()
            }, 100);
            setTimeout(() => {
                // mapTool.showEffByAll("cylinder", false)
                const height = FlyToHeightConfig[panoramaId] || 1000000
                const position = FlyToHeightConfig[panoramaId + '-position']
                position && Viewer.camera.flyTo({ destination: Cesium.Cartesian3.fromDegrees(position[0], position[1], height) })
            }, 1000);
            mapTool.showEntityByAll(true, false)
            state.chooseTask = undefined
            state.chooseTarget = undefined
            state.chooseResource = undefined
            state.showTargetList = [...state.targetList]
            state.showResourceList = [...state.resourceList]
            break;
        case "2":
            setTimeout(() => {
                initTimeLine("taskTimeLine")
                drawTimeLine("taskTimeLine")
                mapTool.showEntityByAll(true, true)
            }, 0);
            break;
        case "3":
            setTimeout(() => {
                initTimeLine("resourceTimeLine")
                drawTimeLine("resourceTimeLine")
                mapTool.showEntityByAll(true, true)

            }, 0);
            break;
        case "4":
            setTimeout(() => {
                initTimeLine("targerTimeLine")
                drawTimeLine("targerTimeLine")

                mapTool.showEntityByAll(true, true)

            }, 0);
            break;
        default:
            break;
    }
}
//获取当前日期
function getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

//中间折线图 //任务执行情况
function drawChart3() {
    let chart = echarts.init(Chart3.value)

    // 生成24小时的数据
    const hours = [];
    const taskCounts = [];

    for (let i = 0; i < 24; i++) {
        hours.push(i + ':00');
        // 固定数据，可以根据需要修改
        taskCounts.push(Math.floor(Math.random() * 20) + 5);
    }

    let option = {
        color: ['#37A0EA'],
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#37A0EA',
            textStyle: {
                color: '#fff'
            }
        },
        grid: {
            left: '10%',
            right: '10%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: hours,
            axisLine: {
                lineStyle: {
                    color: fontColor
                }
            },
            axisLabel: {
                color: fontColor,
                fontSize: 10,
                interval: 2 // 每隔2个小时显示一个标签
            }
        },
        yAxis: {
            type: 'value',
            name: '任务数量',
            nameTextStyle: {
                color: fontColor
            },
            axisLine: {
                lineStyle: {
                    color: fontColor
                }
            },
            axisLabel: {
                color: fontColor,
                fontSize: 10
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        },
        series: [{
            name: '任务数量',
            type: 'line',
            data: taskCounts,
            smooth: true,
            lineStyle: {
                width: 2,
                color: '#37A0EA'
            },
            itemStyle: {
                color: '#37A0EA',
                borderColor: '#37A0EA',
                borderWidth: 2
            },
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0,
                        color: 'rgba(55, 160, 234, 0.3)'
                    }, {
                        offset: 1,
                        color: 'rgba(55, 160, 234, 0.1)'
                    }]
                }
            }
        }]
    };

    chart.setOption(option);
}

//右上统计图 //目标执行情况
function drawChart2() {
    let chart = echarts.init(Chart2.value)
    let option = {
        color: chartColors,

        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['遥控', '遥测', '测量', '数传'],
            textStyle: {
                color: fontColor,
            },
        },
        toolbox: {
            show: true,
            orient: 'vertical',
            left: 'right',
            top: 'center',
            // feature: {
            //     mark: {show: true},
            //     dataView: {show: true, readOnly: false},
            //     magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
            //     restore: {show: true},
            //     saveAsImage: {show: true}
            // }
        },
        xAxis: [
            {
                type: 'category',
                axisTick: { show: false },
                data: ['无人机', '无人艇', '无人车', '弹'],
                axisLine: window.axisLine,
                axisLabel: {
                    color: fontColor,
                    rotate: 45,
                },
            }
        ],
        yAxis: [
            {
                type: 'value',
                splitLine: window.splitLine,
                axisLine: window.axisLine,
                axisLabel: {
                    color: fontColor,
                },
            }
        ],
        series: [
            {
                name: '遥控',
                type: 'bar',
                barGap: 0,
                // label: "遥控",
                emphasis: {
                    focus: 'series'
                },
                data: [0, 0, 0, 0]
            },
            {
                name: '遥测',
                type: 'bar',
                // label: labelOption,
                emphasis: {
                    focus: 'series'
                },
                data: [0, 0, 0, 0]


            },
            {
                name: '测量',
                type: 'bar',
                // label: labelOption,
                emphasis: {
                    focus: 'series'
                },
                data: [0, 0, 0, 0]


            },
            {
                name: '数传',
                type: 'bar',
                // label: labelOption,
                emphasis: {
                    focus: 'series'
                },
                data: [0, 0, 0, 0]


            }
        ]
    };
    situationApi.getTargetBz({ requirementId: panoramaId }).then(res => {
        if (res.data.code == 200) {
            let data = res.data.data
            let targetType = ['无人机', '无人艇', '无人车', "弹"]
            data.forEach(item => {
                let businessIndex = item.taskType[0] - 1
                let targetTypeIndex = targetType.indexOf(item.target.dataTypeValue)
                if (targetTypeIndex > -1 && businessIndex > -1) {
                    option.series[businessIndex].data[targetTypeIndex] += 1
                }
            })
            chart.setOption(option, true)
        } else {
            ElMessage.error(res.data.message)
        }
    })

    // chart.setOption(option, true)


}

// 更新目标接入情况饼图
function updateTargetPieChart() {
    // 查找目标接入情况的图表容器
    let chartContainer = document.querySelector('#chart2') || document.querySelector('.chartClass')
    if (!chartContainer) {
        console.warn('目标接入情况图表容器未找到')
        return
    }

    let chart = echarts.init(chartContainer)

    // 统计目标类型
    let typeStats = {}
    uniqueTargetStats.forEach(target => {
        let type = target.type || '未知类型'
        if (typeStats[type]) {
            typeStats[type]++
        } else {
            typeStats[type] = 1
        }
    })

    // 转换为饼图数据格式
    let pieData = Object.keys(typeStats).map(type => ({
        name: type,
        value: typeStats[type]
    }))

    // 计算总数量
    let totalCount = uniqueTargetStats.size

    let option = {
        color: ['#00d7ff', '#0099ff', '#0066ff', '#0033ff', '#0000ff', '#ff6b6b', '#4ecdc4', '#45b7d1'],
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left',
            textStyle: {
                color: '#ffffff'
            }
        },
        series: [
            {
                name: '目标接入情况',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['60%', '50%'],
                avoidLabelOverlap: false,
                label: {
                    show: true,
                    position: 'outside',
                    formatter: '{b}: {c}',
                    color: '#ffffff',
                    fontSize: 12
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '14',
                        fontWeight: 'bold',
                        color: '#ffffff'
                    }
                },
                labelLine: {
                    show: true,
                    lineStyle: {
                        color: '#ffffff'
                    }
                },
                data: pieData
            },
            // 中间显示总数量
            {
                name: '总数量',
                type: 'pie',
                radius: ['0%', '35%'],
                center: ['60%', '50%'],
                silent: true,
                label: {
                    show: true,
                    position: 'center',
                    formatter: function () {
                        return '总数量\n' + totalCount
                    },
                    color: '#ffffff',
                    fontSize: 16,
                    fontWeight: 'bold'
                },
                data: [{
                    value: 1,
                    itemStyle: {
                        color: 'transparent'
                    }
                }]
            }
        ]
    }

    chart.setOption(option, true)

    console.log('目标接入情况饼图更新:', {
        uniqueTargets: uniqueTargetStats.size,
        typeStats,
        pieData
    })
}

// 更新资源接入情况柱状图
function updateResourcePieChart() {
    // 查找资源接入情况的图表容器
    let chartContainer = document.querySelector('#chart4') || document.querySelector('.chartClass2')
    if (!chartContainer) {
        console.warn('资源接入情况图表容器未找到')
        return
    }

    let chart = echarts.init(chartContainer)

    // 统计资源类型
    let typeStats = {}
    uniqueResourceStats.forEach(resource => {
        let type = resource.type || '未知类型'
        if (typeStats[type]) {
            typeStats[type]++
        } else {
            typeStats[type] = 1
        }
    })

    // 转换为柱状图数据格式
    let categories = Object.keys(typeStats)
    let values = Object.values(typeStats)

    // 计算总数量
    let totalCount = uniqueResourceStats.size

    let option = {
        color: ['#37A0EA'],
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#37A0EA',
            textStyle: {
                color: '#fff'
            },
            formatter: function(params) {
                return params[0].name + '<br/>' +
                       params[0].seriesName + ': ' + params[0].value
            }
        },
        grid: {
            left: '10%',
            right: '10%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: categories,
            axisLine: {
                lineStyle: {
                    color: fontColor
                }
            },
            axisLabel: {
                color: fontColor,
                fontSize: 10,
                rotate: 45 // 旋转标签以防重叠
            }
        },
        yAxis: {
            type: 'value',
            name: '数量',
            nameTextStyle: {
                color: fontColor
            },
            axisLine: {
                lineStyle: {
                    color: fontColor
                }
            },
            axisLabel: {
                color: fontColor,
                fontSize: 10
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        },
        series: [{
            name: '资源数量',
            type: 'bar',
            data: values,
            itemStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0,
                        color: '#37A0EA'
                    }, {
                        offset: 1,
                        color: '#1E5A8A'
                    }]
                },
                borderRadius: [4, 4, 0, 0]
            },
            emphasis: {
                itemStyle: {
                    color: '#4FB3F1'
                }
            }
        }]
    }

    chart.setOption(option, true)

    console.log('资源接入情况柱状图更新:', {
        uniqueResources: uniqueResourceStats.size,
        typeStats,
        categories,
        values
    })
}

//右下统计图 //资源接入情况 第一版-废弃
function drawChart4() {
    let chart = echarts.init(Chart4.value)
    // 来源 1 陆 2 海 3 空 4 天
    let typeList = [
        "地基",
        "海基",
        "空基",
        "天基",]
    let option = {
        color: chartColors,

        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: [],
            type: 'scroll',
            orient: "horizontal",
            textStyle: {
                color: fontColor,
            },
        },
        toolbox: {
            show: false,
            orient: 'vertical',
            left: 'right',
            top: 'center',
            // feature: {
            //     mark: {show: true},
            //     dataView: {show: true, readOnly: false},
            //     magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
            //     restore: {show: true},
            //     saveAsImage: {show: true}
            // }
        },
        yAxis: [
            {
                type: 'category',
                axisTick: { show: false },
                data: typeList,
                axisLine: window.axisLine,
                axisLabel: {
                    color: fontColor,
                    rotate: 45,
                },
            }
        ],
        xAxis: [
            {
                type: 'value',
                splitLine: window.splitLine,
                axisLine: window.axisLine,
                axisLabel: {
                    color: fontColor,
                },
            }
        ],
        series: []
    };

    // typeList.forEach(type => {
    //     let seriesItem = {
    //         name: type,
    //         type: 'bar',
    //         emphasis: {
    //             focus: 'series'
    //         },
    //         data: []
    //     }
    //     option.series.push({ ...seriesItem })
    // })
    situationApi.getRealTimeAreaType({ requirementId: panoramaId }).then(res => {
        if (res.data.code == 200) {
            let resData = res.data.data
            // 兵种
            for (const key in resData) {
                let data = resData[key]
                // 域
                data.forEach(item => {
                    option.legend.data.push(item.areaName)
                    let seriesItem = {
                        name: item.areaName,
                        type: 'bar',
                        stack: 'total',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [0, 0, 0, 0]
                    }
                    let count = 0

                    item.dataGenerals?.forEach(general => {
                        count = count + general.count
                    })
                    seriesItem.data[key - 1] = count
                    option.series.push({ ...seriesItem })
                })
            }
            chart.setOption(option, true)
        } else {
            ElMessage.error(res.data.message)
        }
    })


}
window.historyTrack = {}

// 存储唯一目标统计的全局变量
let uniqueTargetStats = new Map()

// 存储唯一资源统计的全局变量
let uniqueResourceStats = new Map()

function getSocketData(evt) {
    //
    // 目标
    let detailData = evt.detail.data;
    if (detailData.type == 5) {
        let _data = evt.detail.data.data;
        _data.forEach(item => {
            item.id = item.mbId;
            item.direction = item.rollAngle;
            // item.name = item.name + item.id
            // item.longitude = 110 + clacNum*0.1;
            // item.latitude = 29;
            let _type = 4;
            if (item.targetType == 0) {
                _type = 6;
            } else if (item.targetType == 1) {
                _type = 5;
            }
            mapTool.drawMap({ ...item, color: 'red', dataType: _type, name: state.showLabel ? item.name : '', customInfo: `<img class='popImg' src="${item.bindingTarget.imgUrl}" width="110" height="110" />`})

            // 统计唯一目标 - type 5
            if (item.bindingTarget && item.bindingTarget.id) {
                uniqueTargetStats.set(item.bindingTarget.id, {
                    id: item.bindingTarget.id,
                    name: item.bindingTarget.name,
                    type: item.bindingTarget.targetTypeValue || '未知类型',
                    source: 'type5'
                })
            }
        })
        let targetList = _data
        console.log('targetList', targetList);
        targetTableRef.value?.doLayout()
        state.targetForm = targetList.find(item => item.id == state.chooseTargetForm.id || item.bindingTarget.id == state.chooseTargetForm.id) || {}
        targetList.forEach(item => {
            let index = state.targetList.findIndex(item2 => item2.id == item.bindingTarget.id)
            if (index == -1) {
                state.targetList.push({ ...item.bindingTarget })
            } else {
                state.targetList.splice(index, 1, { ...item.bindingTarget })
            }
        })

        // 更新饼图
        updateTargetPieChart()
        return
    }
    // 终端
    if (detailData.type == 6) {
        let nodeList = Object.keys(detailData.data)
        let nodeId = nodeList[0]
        const { terCurrentMode, terCurrentNode } = detailData.data[nodeId]

        // 统计唯一目标 - type 6，且 key 不等于 0
        if (nodeId != 0 && detailData.data[nodeId].bindingTarget) {
            let bindingTarget = detailData.data[nodeId].bindingTarget
            if (bindingTarget.id) {
                uniqueTargetStats.set(bindingTarget.id, {
                    id: bindingTarget.id,
                    name: bindingTarget.name,
                    type: bindingTarget.targetTypeValue || '未知类型',
                    source: 'type6'
                })
            }
        }

        if (detailData.data[nodeId].bindingTarget) {
            let bindingTarget = detailData.data[nodeId].bindingTarget
            let index = state.targetList.findIndex(item => item.id == bindingTarget.id)
            const params = { ...bindingTarget, nodeId: nodeId, terCurrentMode, terCurrentNode }
            if (index == -1) {
                state.targetList.push(params)
            } else {
                state.targetList.splice(index, 1, params)
            }
            if (state.chooseTargetForm.id === bindingTarget.id) {
                Object.assign(state.chooseTargetForm, params)
            }
        }

        state.gplotData =
        {
            nodeRelation: detailData.data[nodeId].nodeRelation,
            nodeId: nodeId
        }
        if (nodeId == 0) {
            targetDataObj.value[0] = { ...JSON.parse(JSON.stringify({ ...detailData.data[nodeId], nodeId: nodeId, terCurrentMode, terCurrentNode })) }
            return
        }

        // 更新饼图
        updateTargetPieChart()
        // add 2025-4-10 start  待验证 wlj

        let bindingTarget = detailData.data[nodeId]?.bindingTarget
        if (!bindingTarget || !bindingTarget.id) {
            return
        }
        // if(state.chooseTargetForm.id != bindingTarget?.id){
        //     return
        // }
        // add 2025-4-10 end

        let baseTxt = detailData.data[nodeId]?.spectrumFrame?.spectrumValues
        if (!baseTxt) {
            let terminalDate = JSON.parse(JSON.stringify({ ...detailData.data[nodeId], nodeId: nodeId, terCurrentMode, terCurrentNode }))
            terminalDate.tTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
            targetDataObj.value[bindingTarget.id] = {
                ...terminalDate,
                sysAntJResult: getSysAntJResult(targetDataObj.value[bindingTarget.id], terminalDate)
            }
        } else {
            let aData = window.atob(baseTxt)
            let arr = new Int8Array(aData.length)
            // let arr2 = new Int8Array(a.length)x`
            let arr2 = []
            for (let i = 0; i < aData.length; i++) {
                arr[i] = aData.charCodeAt(i)
                arr2.push(arr[i])
            }
            detailData.data[nodeId].spectrumFrame.spectrumList = arr2
            // state.terminalDate = JSON.parse(JSON.stringify({ ...detailData.data[nodeId], nodeId: nodeId }))
            let terminalDate = JSON.parse(JSON.stringify({ ...detailData.data[nodeId], nodeId: nodeId, terCurrentMode, terCurrentNode }))
            terminalDate.tTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
            targetDataObj.value[bindingTarget.id] = {
                ...terminalDate,
                sysAntJResult: getSysAntJResult(targetDataObj.value[bindingTarget.id], terminalDate)
            }
        }
        return
    }
    if (detailData.type == 7) {
        // state.resourceList =
        let stationInfo = detailData.data[0]
        let stationInfoList = detailData.data
        if (!stationInfo) {
            return
        }
        // debugger;
        stationInfo = { ...stationInfo, ...stationInfo.stationInfo, pageStationType: 'station' }
        stationInfoList.forEach(item => item.pageStationType = 'station')

        // 统计唯一资源 - type 7
        if (stationInfo.id) {
            uniqueResourceStats.set(stationInfo.id, {
                id: stationInfo.id,
                name: stationInfo.name,
                type: '航天测控站',
                source: 'type7'
            })
        }

        let index = state.resourceList.findIndex(item => item.id == stationInfo.id)
        if (index == -1) {
            state.resourceList.push(stationInfo)
            // mapTool.drawMap({ ...stationInfo, dataType: 1 })
            mapTool.drawMap({ ...stationInfo, dataType: stationInfo.dataType, name: state.showLabel ? stationInfo.name : '', customInfo: `<img class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />` })
        } else {
            state.resourceList.splice(index, 1, stationInfo)
            if (state.ZYForm.id == stationInfo.id) {
                state.ZYForm = stationInfo
                state.stationData = stationInfoList
            }
        }
        resourceTableRef.value?.doLayout()

        // 更新资源饼图
        updateResourcePieChart()
    }
    if (detailData.type == 8) {
        let stationInfo = detailData.data
        // let stationInfoList = detailData.data
        if (!stationInfo) {
            return
        }
        stationInfo = { ...stationInfo, ...stationInfo.stationInfo, pageStationType: 'DD' }

        // 统计唯一资源 - type 8
        if (stationInfo.id) {
            uniqueResourceStats.set(stationInfo.id, {
                id: stationInfo.id,
                name: stationInfo.name,
                type: '导弹测控站',
                source: 'type8'
            })
        }

        let index = state.resourceList.findIndex(item => item.id == stationInfo.id)
        if (index == -1) {
            state.resourceList.push(stationInfo)
            mapTool.drawMap({ ...stationInfo, dataType: 1, name: state.showLabel ? stationInfo.name : '', customInfo: `<img class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />` })
        } else {
            state.resourceList.splice(index, 1, stationInfo)
            if (state.ZYForm.id == stationInfo.id) {
                state.ZYForm = stationInfo
                state.stationData = [stationInfo]
            }
        }
        resourceTableRef.value?.doLayout()

        // 更新资源饼图
        updateResourcePieChart()
    }
    if (detailData.type == 9) {
        let resourceInfo = detailData.data
        if (!resourceInfo) {
            return
        }

        // 统计唯一资源 - type 9
        if (resourceInfo.id) {
            uniqueResourceStats.set(resourceInfo.id, {
                id: resourceInfo.id,
                name: resourceInfo.name,
                type: '无人机测控站',
                source: 'type9'
            })
        }
		let stationInfo = {
			...resourceInfo,
			...resourceInfo.stationInfo,
			pageStationType: 'WRJ',
		}
		let index = state.resourceList.findIndex(
			(item) => item.id == stationInfo.id
		)
		if (index == -1) {
			state.resourceList.push(stationInfo)
			// mapTool.drawMap({ ...stationInfo, dataType: 1, name: state.showLabel ? stationInfo.name : '', customInfo: `<img class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />` })
			mapTool.drawMap({ ...stationInfo, name: state.showLabel ? stationInfo.name : '', customInfo: `<img class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />` })
		} else {
			state.resourceList.splice(index, 1, stationInfo)
			if (state.ZYForm.id == stationInfo.id) {
				state.ZYForm = stationInfo
				state.stationData = [stationInfo]
			}
		}
		resourceTableRef.value?.doLayout()
        // 更新资源饼图
        updateResourcePieChart()
    }
    if (detailData.type == 14) {
        // 处理任务数据，参考 panorama_old 的 dragOrPlay 回调
        let taskData = detailData.data || []

        // 更新当前时间显示（取第一个任务的当前时间，或使用系统时间）
        if (taskData.length > 0 && taskData[0].currentTime) {
            state.currentTime = taskData[0].currentTime
        }

        // 如果 taskList 为空，先初始化任务列表
        if (!state.taskList || state.taskList.length === 0) {
            state.taskList = taskData.map(item => ({
                id: item.taskId,
                taskName: item.taskName,
                schedule: item.progressPercentage || 0,
                progressPercentage: item.progressPercentage || 0,
                progress: item.progress || 0,
                taskStatus: item.progressPercentage == 0 ? '待执行' :
                    item.progressPercentage >= 100 ? '执行完成' : '执行中',
                statusName: item.statusName || (item.progressPercentage == 0 ? '待执行' :
                    item.progressPercentage >= 100 ? '执行完成' : '执行中'),
                startTime: item.startTime,
                endTime: item.endTime,
                currentTime: item.currentTime,
                elapsedSeconds: item.elapsedSeconds,
                remainingSeconds: item.remainingSeconds,
                totalSeconds: item.totalSeconds,
                requirementId: item.requirementId,
                taskType: item.taskType,
                taskTypeName: item.taskTypeName,
                importance: item.importance
            }))
        } else {
            // 更新现有任务进度
            taskData.forEach(item => {
                let existingTask = state.taskList.find(task => task.id == item.taskId)

                if (existingTask) {
                    // 更新现有任务
                    let progressPercentage = item.progressPercentage || 0

                    // 更新进度
                    existingTask.schedule = progressPercentage
                    existingTask.progressPercentage = progressPercentage
                    existingTask.progress = item.progress || 0

                    // 更新状态
                    if (progressPercentage == 0) {
                        existingTask.taskStatus = '待执行'
                        existingTask.statusName = '待执行'
                    } else if (progressPercentage >= 100) {
                        existingTask.taskStatus = '执行完成'
                        existingTask.statusName = '执行完成'
                    } else {
                        existingTask.taskStatus = '执行中'
                        existingTask.statusName = '执行中'
                    }

                    // 更新时间信息（startTime 和 endTime 不更新，保持初始值）
                    // existingTask.startTime = item.startTime  // 不更新开始时间
                    // existingTask.endTime = item.endTime      // 不更新结束时间
                    existingTask.currentTime = item.currentTime
                    existingTask.elapsedSeconds = item.elapsedSeconds
                    existingTask.remainingSeconds = item.remainingSeconds
                    existingTask.totalSeconds = item.totalSeconds

                    // 更新任务详情（如果当前选中的是这个任务）
                    if (state.taskForm.id == existingTask.id) {
                        state.taskForm = { ...existingTask }
                    }
                } else {
                    // 添加新任务
                    let newTask = {
                        id: item.taskId,
                        taskName: item.taskName,
                        schedule: item.progressPercentage || 0,
                        progressPercentage: item.progressPercentage || 0,
                        progress: item.progress || 0,
                        taskStatus: item.progressPercentage == 0 ? '待执行' :
                            item.progressPercentage >= 100 ? '执行完成' : '执行中',
                        statusName: item.statusName || (item.progressPercentage == 0 ? '待执行' :
                            item.progressPercentage >= 100 ? '执行完成' : '执行中'),
                        startTime: item.startTime,
                        endTime: item.endTime,
                        currentTime: item.currentTime,
                        elapsedSeconds: item.elapsedSeconds,
                        remainingSeconds: item.remainingSeconds,
                        totalSeconds: item.totalSeconds,
                        requirementId: item.requirementId,
                        taskType: item.taskType,
                        taskTypeName: item.taskTypeName,
                        importance: item.importance
                    }
                    state.taskList.push(newTask)
                }
            })
        }

        console.log('任务数据更新:', {
            taskData,
            updatedTaskList: state.taskList,
            isInitialized: !state.taskList || state.taskList.length === 0
        })

        // 如果有选中的任务，更新甘特图和当前时间
        if (state.pageRadio == 2 && state.chooseTask) {
            // 更新选中任务的当前时间
            const selectedTaskData = taskData.find(item => item.taskId == state.chooseTask)
            if (selectedTaskData && selectedTaskData.currentTime) {
                state.currentTime = selectedTaskData.currentTime
            }

            // 更新甘特图
            updateTaskTimeline()
        }
    }

}
// 绘制终端
watch(state.targetList, (list) => {
    list.forEach((e) => {
        const { longitude, latitude, altitude } = targetDataObj.value[e.id]?.createLinkDataFrame ?? {}
        const position = [longitude, latitude, altitude]
        mapTool.drawMap({
            id: e.id,
            name: state.showLabel ? e.name : '',
			color: 'red',
            position,
            // dataType: 9
            dataType: e.dataType || 9,
             customInfo: `<img class='popImg' src="${e.imgUrl}" width="110" height="110" />`
        })
        const data = window.flowLineList.find(item => item.targetId === e.id)
        if (data && data.localId !== e.terCurrentNode) {
            mapTool.removeFlowLine(data.localId, data.targetId)
            mapTool.removeFlowLine(data.targetId, data.localId)
        }
        const lineId = e.terCurrentNode + '_' + e.id + '_flowLine'
        if (window.flowLineList.find(item => item.id === lineId)) return
        mapTool.drawflowLine(e.terCurrentNode, e.id, '#00ff00')
        mapTool.drawflowLine(e.id, e.terCurrentNode, '#00ff00')
    })
})
// 校验决策模块结果， accHandEn的值1 -> 0,或者0 -> 0,变化时保留1时的值
function getSysAntJResult(data, newData) {
    if (!data || !data.sysAntJResult) return newData.sysAntJResult
    const oldFlag = data.sysAntJResult.accHandEn === 1 || data.sysAntJResult.accHandEn === 0
    const newFlag = newData.sysAntJResult?.accHandEn === 0
    data.sysAntJResult.accHandEn = 0
    return oldFlag && newFlag ? data.sysAntJResult : newData.sysAntJResult
}
var visGroups = new vis.DataSet([]); //时间轴分组
var visItems = new vis.DataSet([]);; //时间轴项目
var timeline; //时间轴管理器
/**
 * @description 绘制时间轴
 */
function initTimeLine(domID) {
    var container = document.getElementById(domID);
    timeline = new vis.Timeline(container);
    visGroups = new vis.DataSet([]);
    visItems = new vis.DataSet([]);
    // let start = moment(timelineStart).format("YYYY-MM-DD HH:mm:ss");
    // let min = moment(timelineStart).subtract(1, "h").format("YYYY-MM-DD HH:mm:ss")
    // let end = moment(timelineEnd).format("YYYY-MM-DD HH:mm:ss");
    let options = {
        autoResize: true,
        height: "90%", //高度
        width: "100%", //宽度
        min: "2000-01-01 00:00:00", //设置最小时间范围
        max: "2099-12-31 23:59:59", //设置最大时间范围
        // start: "2024-08-06 15:03:18", //设置开始时间
        // end: "2024-08-07 15:03:18", //设置结束时间
        stack: true, // ture则不重叠
        limitSize: true,
        verticalScroll: true,
        // cluster: true, //数据量过大时使用。
        locale: "zh-cn",
        xss: {
            disabled: true,
        },
        // groupOrder: function (a, b) {
        //     return a.value - b.value;
        // },
        editable: false,
        showCurrentTime: false,
        moment: function (date) {
            // return moment(date).format("YYYY-MM-DD HH:mm:ss");
            return moment(date);
        },
        locale: moment.locale("zh-cn")

    };
    timeline.setOptions(options);
    timeline.setGroups(visGroups);
    timeline.setItems(visItems);
    timeline.addCustomTime("2000-01-01 00:00:01");
}
//绘制时间轴项目
function drawTimeLine(domID) {
    // if (domID == "taskTimeLine") {
    // state.taskList.forEach(item => {
    //     visGroups.add({ id: "111" + item.id, content: '规划-' + item.taskName, value: "111" + item.id });
    //     visGroups.add({ id: "222" + item.id, content: '实际-' + item.taskName, value: "222" + item.id });
    //     visItems.add({
    //         id: item.id + "guihua",
    //         group: "111" + item.id,
    //         content: item.taskName,
    //         start: item.startTime,
    //         end: item.endTime,
    //     })
    //     visItems.add({
    //         id: item.id + "shiji",
    //         group: "222" + item.id,
    //         content: item.taskName,
    //         start: timeSpan(new Date(item.startTime).getTime() - Math.random() * 3000),
    //         end: timeSpan(new Date(item.endTime).getTime() - Math.random() * 2000),
    //     })
    // })
    visItems.clear()
    visGroups.clear()

    timeline.setItems(visItems);
    timeline.setGroups(visGroups);

    // }
}
/**
 * @description 任务表格点击事件
 */
const taskTableClick = (row) => {
    clearData()
    state.chooseTask = row.id
    const height = FlyToHeightConfig[panoramaId] || 1000000
    const position = FlyToHeightConfig[panoramaId + '-position']
    position && Viewer.camera.flyTo({ destination: Cesium.Cartesian3.fromDegrees(position[0], position[1], height) })
    dataApi.getTaskByTaskId({ id: row.id }).then((res) => {
        if (res.data.code == 200) {
            state.taskForm = res.data.data;
            state.taskForm.schedule = row.schedule
            // state.playStatus = "play"
            getResourceByTask(row.id)
            visGroups.add({ id: "222" + row.id, content: '', value: "222" + row.id });
            timeline.setGroups(visGroups);

            timeline.setOptions({
                min: timeSpan(new Date(state.taskForm.startTime).getTime() - 86400),
                start: timeSpan(new Date(state.taskForm.startTime).getTime() - 86400),
                max: timeSpan(new Date(state.taskForm.endTime).getTime() + 86400),
                end: timeSpan(new Date(state.taskForm.endTime).getTime() + 86400),
            })
            state.showTargetList = state.targetList.filter(item => {
                return item.targetId == state.taskForm.target.targetId
            })
            state.showTargetList.forEach(item => {
                mapTool.showEntityById(item.targetId, true)
            })
        } else {
            ElMessage.error(res.data.message);
        }
    });
};
//根据任务ID获取资源
function getResourceByTask(taskId) {
    resourceApi.getEquipmentByTask({ taskId: taskId, size: 999, page: 1 }).then(res => {
        if (res.data.code == 200) {
            state.showResourceList = res.data.data.records
            state.showResourceList.forEach(item => {
                mapTool.showEntityById(item.equipmentId, true)
            })
            state.playStatus = "play"
        } else {
            ElMessage.error(res.data.message)
        }
    })
}
//资源行点击事件
const taskTableClickZY = (row) => {
    // state.ZYForm = row
    // return

    // clearData()
    // state.chooseResource = row.equipmentId
    // // window.EVGISMAP("flyTo", {
    // //     center: [row.equipmentDetail.longitude, row.equipmentDetail.latitude],
    // // });
    // const height = FlyToHeightConfig[panoramaId] || 1000000
    // Viewer.camera.flyTo({ destination: Cesium.Cartesian3.fromDegrees(row.equipmentDetail.longitude, row.equipmentDetail.latitude, height) })
    // dataApi.getDetailById({ dataId: row.equipmentId, dataTypeId: row.generalId }).then(res => {
    //     if (res.data.code == 200) {
    //         state.ZYForm = res.data.data;
    //         getDataByResource(row)
    //     } else {
    //         ElMessage.error(res.data.message);
    //     }
    // })
    //

    state.ZYForm = row
    if (row.id === "00000000") {
        if (state.stationMoreShow === true) {
            clickGplot()
        }
        return
    }
    if (state.gplotShow === true) {
        clickStationRow()
    }
    window.EVGISMAP("flyTo", {
        center: [row.longitude, row.latitude, row.altitude || 1000000],
    });
}
function chooseMB(scope) {
    if (state.chooseTargetForm.id && scope.row.id == state.chooseTargetForm.id) {
        return "chooseRow"
    } else {
        return ''
    }
}
function chooseZY(scope) {
    if (state.ZYForm.id && scope.row.id == state.ZYForm.id) {
        return "chooseRow"
    } else {
        return ''
    }
}
//获取最大最小时间
function getMaxMinTime(list) {
    let max, min
    list.forEach(item => {
        if (item.startTime < min || !min) {
            min = item.startTime
        }
        if (item.endTime > max || !max) {
            max = item.endTime
        }
    })
    return { min, max }
}
var taskByEquipment
//获取资源对应的需要显示的目标
function getDataByResource(row) {
    mapTool.showEntityById(row.id, true)
    situationApi.getDataByEquipment({ equipmentId: row.id, requirementId: panoramaId }).then(res => {
        if (res.data.code == 200) {
            let tasks = res.data.data.tasks || []
            let targets = res.data.data.targets || []
            taskByEquipment = res.data.data
            let max, min
            window.aaa = []
            tasks.forEach(item => {
                visGroups.add({ id: item.id, content: '', value: item.id });
                aaa.push(item.id)
                item.occupancies.forEach(occ => {
                    if (occ.startTime < min || !min) {
                        min = occ.startTime
                    }
                    if (occ.endTime > max || !max) {
                        max = occ.endTime
                    }

                })
            })

            timeline.setGroups(visGroups);
            // timeline.setItems(visItems);

            timeline.setOptions({
                min: timeSpan(new Date(min).getTime() - 8640 * 10),
                start: timeSpan(new Date(min).getTime() - 8640 * 10),
                max: timeSpan(new Date(max).getTime() + 8640 * 10),
                end: timeSpan(new Date(max).getTime() + 8640 * 10),
            })
            let ids = targets.map(item => { return item.targetId })
            //控制显示
            state.showTargetList = state.targetList.filter(item => { return ids.includes(item.id) })
            targets.forEach(item => {
                mapTool.showEntityById(item.targetId, false)

            })
            state.showResourceList = state.resourceList.filter(item => { return row.id == item.id })
            mapTool.showEntityById(row.id, true)


            state.playStatus = "play"

        } else {
            ElMessage.error(res.data.message)
        }
    })
}
function getRowData(row) {
    areaApi.getAreaGeneralDataDetail({ dataTypeId: row.generalId, dataId: row.targetId }).then(res => {
        if (res.data.code == 200) {
            state.targetForm = { ...state.targetForm, ...res.data.data }
        } else {
            ElMessage.error(res.data.message)

        }
    })

}
function targetTableClick(row) {
    state.chooseTargetForm = row

    // 清除详情页面数据
    state.targetForm = {}
    state.detailShow = false
    state.terminalStateShow = false

    // controlApi.getTerminalMode({ terminalSeq: row.nodeId }).then(res => {
    //     if (res.data.code == 200) {
    //         state.chooseTargetForm._modeCtrl = res.data.data
    //     } else {
    //         ElMessage.error(res.data.message)
    //     }
    // })
}
//初始化数据
function clearData() {
    visGroups?.clear()
    visItems?.clear()
    state.chooseTask = undefined
    state.chooseTarget = undefined
    state.chooseResource = undefined
    mapTool.showEntityByAll(false)
    // 关闭标牌框
    window.EVGISMAP('closeDiv', { className: '.infoDiv' })
    // 删除聚焦框
    window.selectedId && window.EVGISMAP("removeGroupEntityById", {
        id: window.selectedId + 'makerGroupOnlyOne',
        group: 'makerGroup'
    });

    state.showTargetList = []
    state.showResourceList = []
}
//目标更多点击事件
function clickDetileRow() {
    state.terminalStateShow = false
    state.detailShow = true
}

// 终端更多点击事件
function clickTerminalRow() {
    state.detailShow = false
    state.terminalStateShow = true
}
//拓扑图点击事件
function clickGplot() {
    state.gplotShow = true
    state.stationMoreShow = false

}
//站点更多点击事件
function clickStationRow() {
    state.stationMoreShow = true
    state.gplotShow = false


}
function terminalControl() {
    state.terminalControlShow = true
    state.terminalControlForm = {
        // 定义 整机参数设置 初始值
        machineParam: {
            targetNode: 1,
            modeCtrl: 0,
            rxFreq: 2070,
            txFreq: 2260,
            txAtte: 30,
            // nodeId: undefined,
            nodeId: "1834120494834802689",
        }, dataControl: {
            targetNode: undefined,
            modeCtrl: '',
            transModState: undefined
        }
    }
}
//整机控制帧发送
function setMachineParam() {
    controlApi.setMachineParam(state.terminalControlForm.machineParam).then(res => {
        if (res.data.code == 200) {
            ElMessage.success("发送成功")
        } else {
            ElMessage.error("发送失败")
        }
    })
}
//查询当前选中的终端的体制-用于显示
function targetNodeChange(val) {
    controlApi.getTerminalMode({ terminalSeq: val }).then(res => {
        if (res.data.code == 200) {
            state.terminalControlForm.dataControl.modeCtrl = res.data.data
        } else {
            ElMessage.error(res.data.message)
        }
    })
}
//数据传输控制
function setDataControlParam() {
    controlApi.setDataControlParam(state.terminalControlForm.dataControl).then(res => {
        if (res.data.code == 200) {
            ElMessage.success("发送成功")
        } else {
            ElMessage.error("发送失败")
        }
    })
}
</script>

<style lang='less' scoped>
.showOrHidden-box {
    position: absolute;
    right: 500px;
    top: 90px;
    // z-index: 999;
}

.scheduleClass {
    height: 100%;
    width: 100%;
    overflow-y: auto;
    box-sizing: border-box;

    .task_schedule {
        height: auto;
        min-height: 45px;
        width: 100%;
        padding: 0 10px;
        margin-bottom: 10px;
        box-sizing: border-box;

    }
}

.chartClass {
    width: 100%;
    height: 100%;
    background: #022141c0;

    :deep(.vis-content > .vis-labelset .vis-inner) {
        font-weight: bolder;
        font-size: 16px;
        margin: 0 5px;
    }

    :deep(.vis-group) {
        >.vis-item.vis-selected {
            background: #2f557b !important;
        }

        .vis-item {
            background: rgb(29, 94, 142);
        }
    }
}

.chartClass2 {
    width: 100%;
    height: 100%;
    background: #022141c0;

    :deep(.vis-content > .vis-labelset .vis-inner) {
        font-weight: bolder;
        font-size: 16px;
        margin: 0 5px;
    }

    :deep(.vis-group) {
        >.vis-item.vis-selected {
            background: #2f557b !important;
        }

        .vis-item {
            background: rgb(29, 94, 142);
        }
    }
}

.pageRadio {
    position: absolute;
    top: 10%;
    left: 50%;
    transform: translate(-50%, 0);
    background: #022141c0;
    border: 1px solid #2e5175;
}

.targetForm_class {
    height: calc(100% - 30px);

    :deep(.el-form-item) {
        margin-bottom: 0;

        .el-form-item__label {
            padding-right: 8px;
        }
    }
}

.custom-table {
    :deep(.chooseRow) {
        background: rgba(55, 160, 234, 0.5) !important
    }
}
</style>
