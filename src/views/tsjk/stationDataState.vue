<template>
    <div>
        <el-tabs v-model="state.activeName" class="tabsClass" @tab-change="tabChange">
            <el-tab-pane
                :label="props.stationData.stationData[0]?.pageStationType == 'DD' ? '导弹地面站' : props.stationData.stationData[0]?.pageStationType == 'WRJ' ? '链路状态信息' : '目标' + (index + 1)"
                :name="index" class="tab-pane" v-for=" (stationInfo, index) in props.stationData.stationData"
                :key="index">
                <!-- 根据数据的不同，调用不同的状态帧字段（TODO） v-if="props.stationData.stationData?.droneFrame" -->
                <!-- ----------{{ stationInfo.stationInfo }} -->
                 <!-- || tsDataShow(textKey) -->
                <el-row v-if="(stationInfo.pageStationType == 'station') ">
                    <!-- <el-row>  -->
                    <!-- 宽度用class设置成20%了 -->
                    <div v-for="textKey in booleanList" :key="textKey" class="icon_box">
                        <div style="margin: 10px;" >
                            <span class="cell-text" style="margin-bottom: 10px;">
                                <div class="sucess_icon" v-if="stationInfo?.[textKey] == 1"
                                    style="height: 40px;width: 40px;">
                                </div>
                                <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>
                            </span>
                        </div>
                        <div> {{ labelText(textKey) }}</div>
                    </div>
                </el-row>
                <el-row v-if="['DD', 'WRJ'].includes(stationInfo.pageStationType)">
                    <!-- 宽度用class设置成20%了 -->
                    <div v-for="textKey in showKeyListFun(stationInfo.pageStationType)" :key="textKey" class="icon_box">
                        <div style="margin: 10px;">
                            <span class="cell-text" style="margin-bottom: 10px;" v-if="stationInfo.pageStationType == 'DD'">
                                <div class="sucess_icon" v-if="stationInfo?.[textKey] == 'aa'"
                                    style="height: 40px;width: 40px;">
                                </div>
                                <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>
                            </span>
                            <span class="cell-text" style="margin-bottom: 10px;" v-if="stationInfo.pageStationType == 'WRJ'">
                                <div class="sucess_icon" v-if="stationInfo?.[textKey] == '49'"
                                    style="height: 40px;width: 40px;">
                                </div>
                                <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>
                            </span>
                        </div>

                        <div> {{ labelText(textKey) }}</div>
                    </div>
                </el-row>


                <!-- <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                    <el-descriptions-item width="150px">
                        <template #label>
                            <div class="cell-item">
                                {{ '图片' }}
                            </div>
                        </template>
<div class="image_div">
    2025-04-10 add.
    <img src="/public/images/bgBorder1/模型库DK_08.png" alt="" style="max-height: 200px;">
    <img :src="props.stationData.stationData[0]?.imgUrl" alt="" style="max-height: 200px;">
</div>
</el-descriptions-item>
</el-descriptions> -->
                <el-descriptions style="margin-top:20px;" title="" :column="2" size="small" :border="true">
                    <el-descriptions-item v-for="(textKey) in KeyListFun(stationInfo)" :key="textKey + 'infoList'"
                        width="150px">
                        <template #label>
                            <div class="cell-item">
                                {{ labelText(textKey) }}
                            </div>
                        </template>
                        <span class="cell-text">{{ valueText(textKey) }}
                        </span>
                    </el-descriptions-item>
                </el-descriptions>
            </el-tab-pane>
            <el-tab-pane label="测控数据">
                <div ref="ckDataContainer" style="height: 450px;overflow: auto;padding: 10px;border: 1px solid #003a66;">
                    <p style="font-size:16px;line-height:25px" v-for="(item, index) in sockeMessStore.zyssSocket"
                        :key="index" @click="handleClick(item)" v-show="item.id == props.stationData.ZYForm.id">
                        {{ `${item.time} ` }}
                        【
                        <span :style="{ color: item.type == 'ERROR' ? '#f00' : '#008000' }">{{ `${item.type}`
                            }}</span>
                        】：
                        {{ `${item.message}` }}
						<span class="view-more" style="color:aquamarine;cursor: pointer;" @click="onViewMore">查看更多</span>
                    </p>
				</div>
            </el-tab-pane>
        </el-tabs>
		<div class="el-popover is-light el-popper" style="position:fixed;width:450px;" ref="moreInfoBoxRef" v-show="moreDetailsIsShow"
			 v-click-outside="onClickOutside">
			<div style="padding: 10px;height: 300px;overflow: auto;">
				{{ currentData }}
			</div>
		</div>
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, reactive, watch, nextTick, unref } from 'vue'
import * as stationDataLable from "./stationDataLable.js"
import { ElMessage, ClickOutside as vClickOutside } from 'element-plus';
import { useSockeMessStore } from '@/stores/index'
const sockeMessStore = useSockeMessStore()

var stationList = ref(stationDataLable.failedList)  //所有站点key/lable
var booleanList = ref(stationDataLable.booleanList)  // 红绿灯列表
var infoList = ref(stationDataLable.infoList) // 信息列表
var DDKeyList = ref(stationDataLable.DDKeyList) // 导弹列表
var DDKeyListBoolean = ref(stationDataLable.DDKeyListBoolean) // 导弹列表-锁
var WRJKeyList = ref(stationDataLable.WRJKeyList) // 无人机列表
var WRJKeyListBoolean = ref(stationDataLable.WRJKeyListBoolean) //无人机列表-锁
const grlxList = ["无干扰", '单音', '多音', '部分带', '脉冲', '扫频']
const props = defineProps({
    stationData: {
        type: Array,
        default: () => {
            return []
        },
    },
})
let currentData = ref({})
const ckDataContainer = ref(null)
const handleClick = (data) => {
    currentData.value = data.data
}
watch(() => props.stationData.stationData, () => {
    console.log('props.stationData.stationData', props.stationData);
    // upDateChart()
    setFiledList()
}
)
watch(() => sockeMessStore.zyssSocket, () => {
    console.log('sockeMessStore.zyssSocket', sockeMessStore.zyssSocket);
}
)
const state = reactive({
    activeName: 0,
    singleData: {},
    subPaneList: [{
        label: "平台机动状态",
        failed: "platformState"
    }],
})
onMounted(() => {
    setFiledList()
})
const moreInfoBoxRef = ref()
const moreDetailsIsShow = ref(false)
const onViewMore = (e) => {
	const height = 300
	let top = e.pageY - height / 3
	if(top + height > document.body.offsetHeight){
		top = document.body.offsetHeight - height - 20
	}

	Object.assign(moreInfoBoxRef.value.style, {
		top: top + 'px',
		left: (e.pageX + 50) + 'px'
	})
	moreDetailsIsShow.value = true
}
const onClickOutside = (e) => {
	if(e.target.className === 'view-more'){
		return
	}
	moreDetailsIsShow.value = false
}
function showKeyListFun(pageStationType) {
    if (pageStationType == 'DD') {
        return DDKeyListBoolean.value
    } else if (pageStationType == 'WRJ') {
        return WRJKeyListBoolean.value
    } else {
        return []
    }
}
function setFiledList() {
    if (props.stationData.stationData?.[0]?.typeValue == '浮空平台') {
        stationList.value = stationDataLable.failedList
        booleanList.value = stationDataLable.booleanList
        infoList.value = stationDataLable.infoList
    }

}
// 灯的显示列表
function booleanKeyListFun(station) {
    if (station.pageStationType == 'station') {
        return booleanList.value
    } else if (station.pageStationType == 'DD') {
        return DDKeyListBoolean.value
    } else if (station.pageStationType == 'WRJ') {
        return WRJKeyListBoolean.value
    } else {
        return []
    }
}

// function tsDataShow(key) {
//     // let row = formLabelList.find(item => item.key == key)
//     if (key == "wxclsd") {
//         return false
//     }
//     else {
//         return !state.subPaneList.find(item => item.failed == key)
//     }
// }
//字段的显示列表
function KeyListFun(station) {
    if (station.pageStationType == 'station') {
        return infoList.value
    } else if (station.pageStationType == 'DD') {
        return DDKeyList.value
    } else if (station.pageStationType == 'WRJ') {
        return WRJKeyList.value
    } else {
        return []
    }
}
// key转文字
function labelText(key) {
    let row = stationList.value.find(item => item.key == key)
    return row ? row.label : "未定义"
}
//显示值
function valueText(key) {
    let val = props.stationData.stationData?.[state.activeName]?.[key] || 0
    let row = stationList.value.find(item => item.key == key)
    if (row.factor) {
        val = val / row.factor
    }
    if (row.unit) {
        val = val + ' ' + row.unit
    }
    return val
}
function tabChange() {
}

// 记录滚动状态
let wasAtBottom = true
let previousScrollHeight = 0

// 监听测控数据变化前，记录当前状态
watch(
  () => sockeMessStore.zyssSocket.length,
  () => {
    if (ckDataContainer.value) {
      const container = ckDataContainer.value
      // 记录新数据添加前的状态
      const scrollTop = container.scrollTop
      const clientHeight = container.clientHeight
      const scrollHeight = container.scrollHeight

      // 更宽松的底部检测，容差增加到10px
      wasAtBottom = scrollTop + clientHeight >= scrollHeight - 10
      previousScrollHeight = scrollHeight

      // 调试信息
      console.log('测控数据滚动状态检测:', {
        scrollTop,
        clientHeight,
        scrollHeight,
        isAtBottom: wasAtBottom,
        diff: scrollHeight - (scrollTop + clientHeight)
      })
    }
  },
  { flush: 'pre' } // 在DOM更新前执行
)

// 监听测控数据变化后，智能滚动（类似VSCode终端效果）
watch(
  () => sockeMessStore.zyssSocket.length,
  () => {
    nextTick(() => {
      if (ckDataContainer.value) {
        const container = ckDataContainer.value

        if (wasAtBottom) {
          // 如果之前在底部，自动滚动到新的底部
          console.log('测控数据执行自动滚动到底部')

          const scrollToBottom = () => {
            const newScrollHeight = container.scrollHeight
            container.scrollTop = newScrollHeight
            console.log('测控数据滚动执行:', {
              scrollTop: container.scrollTop,
              scrollHeight: newScrollHeight,
              clientHeight: container.clientHeight
            })
          }

          // 立即滚动
          scrollToBottom()

          // 延迟滚动，确保DOM完全渲染
          setTimeout(scrollToBottom, 0)
          setTimeout(scrollToBottom, 50) // 增加延迟时间
        } else {
          console.log('测控数据滚动条不在底部，保持当前位置')
          // 如果之前不在底部，保持相对位置不变
          // 由于新内容添加到底部，滚动位置不需要调整，内容自然保持不变
        }
      }
    })
  },
  { flush: 'post' } // 在DOM更新后执行
)

// 额外的滚动确保机制
watch(
  () => sockeMessStore.zyssSocket,
  () => {
    if (ckDataContainer.value && wasAtBottom) {
      // 使用 requestAnimationFrame 确保在浏览器下一次重绘时滚动
      requestAnimationFrame(() => {
        if (ckDataContainer.value) {
          ckDataContainer.value.scrollTop = ckDataContainer.value.scrollHeight
          console.log('测控数据 requestAnimationFrame 滚动执行')
        }
      })
    }
  },
  { deep: true, flush: 'post' }
)

</script>

<style scoped lang="less">
.tab-pane {
    height: 48vh;
    overflow: auto;
    margin-top: 10px;

    :deep(.el-form-item) {
        margin-bottom: 0px;
    }

    :deep(div) {
        box-sizing: border-box;
    }
}

.sucess_icon {
    width: 40px;
    height: 40px;
    background: url(/public/images/icon/green_light.png) 0 0/100% 100% no-repeat;
    // :deep(path) {
    //     fill: rgb(5, 199, 5);
    // }
}

.close_icon {
    width: 20px;
    height: 20px;
    background: url(/public/images/icon/red_light.png) 0 0/100% 100% no-repeat;
    // :deep(svg) {
    //     font-size: 30px;
    // }

    // :deep(path) {
    //     fill: rgb(226, 1, 1);
    // }
}

.icon_box {
    height: 90px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    box-sizing: border-box;
    padding: 5px;
    flex-direction: column;
    width: 25% !important;
}

.subTitle {
    width: 100%;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 18px;
    font-weight: bold;
    position: relative;

    .sub_text {
        font-size: 12px;
        font-weight: 100;
        position: absolute;
        right: 20px;
    }
}

.spectrum_perception {
    height: calc(100% - 50px);
    width: 100%;
    padding: 0 20px 30px 20px;
    display: flex;
    align-items: flex-end;
    overflow-y: auto;
}

.spectrum_decision {
    height: calc(100% - 50px);
    width: 100%;
    padding: 0 20px 40px 20px;
    display: flex;
    align-items: flex-end;
    overflow-y: auto;
    flex-wrap: wrap;

    :deep(.el-select) {
        height: 25px;
        vertical-align: bottom;
        width: 100%;
    }

    :deep(.el-select__wrapper) {
        height: 100%;
        min-height: unset !important;
        padding: 0 !important;
    }
}

.lable_bg {
    background: rgba(0, 174, 255, 0.4);
    border: 2px solid #003a66;
    display: inline-block;
    width: 48%;
    line-height: 25px;

    text-align: center;

}

.content_bg {
    // background: rgba(0, 174, 255, 0);
    border: 2px solid #003a66;
    display: inline-block;
    width: 48%;
    line-height: 25px;

    padding: 0 5px;
    box-sizing: border-box;

}

.row_content {
    border: 3px solid #003a66;
    width: 100%;
}

.image_div {
    height: auto;
    width: 100%;
    text-align: center;
}
</style>
