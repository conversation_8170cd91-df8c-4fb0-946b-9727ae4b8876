export const failedList =

    [
        //无人机 1
        [
            { failed: "name", label: "目标名称" },
            { failed: "code", label: "目标代号" },
            { failed: "targetType", label: "目标类型" },

            // { failed: "domain", label: "所属域" },
            { failed: "longitude", label: "经度" },
            { failed: "latitude", label: "纬度" },
            { failed: "airspeed", label: "空速" },
            { failed: "groundSpeed", label: "地速" },
            { failed: "altitude", label: "海拔高度" },
            { failed: "pitchAngle", label: "俯仰角" },
            { failed: "rollAngle", label: "横滚角" },
            { failed: "headingAngle", label: "航向角" },

        ],
        // 无人车
        [
            { failed: "name", label: "目标名称" },
            { failed: "code", label: "目标代号" },
            { failed: "targetType", label: "目标类型" },

            // { failed: "domain", label: "所属域" },
            { failed: "pitchAngle", label: "俯仰角" },
            { failed: "rollAngle", label: "侧倾角" },
            { failed: "headingAngle", label: "航向角" },
            { failed: "itude", label: "经度" },
            { failed: "latitude", label: "纬度" },
            { failed: "trackingDeviation", label: "跟踪偏差" },
            { failed: "realTimeSpeed", label: "实时速度" },
            { failed: "parkingState", label: "驻车状态" },
            { failed: "gear", label: "档位" },
            { failed: "emergencyBrakeState", label: "紧急停车状态" },
            { failed: "drivingMode", label: "驾驶模式" },
            { failed: "vehicleSpeed", label: "车速" },
            { failed: "batteryVoltage", label: "系统电压" },
            { failed: "batterySoc", label: "电池SOC" },
            { failed: "batteryCurrent", label: "电池电流" },
            { failed: "mileage", label: "车辆行驶里程" },
            { failed: "batteryStatus", label: "电池状态信息" },
            { failed: "batteryAlarm", label: "电池告警信息" },
            { failed: "engineWaterTemp", label: "发动机水温" },
            { failed: "generatorControllerTemp", label: "发电机控制器温度" },
            { failed: "generatorTemp", label: "发电机温度" },
            { failed: "generatorDCVoltage", label: "发电机直流侧电压" },
            { failed: "generatorFault", label: "发电机故障" },
            { failed: "generatorStatusFault", label: "发电机状态故障" },
            { failed: "generatorCommunicationFault", label: "发电机通信故障" },
            { failed: "engineCommunicationFault", label: "发动机通信故障" },
            { failed: "apuCommunicationAnomaly", label: "APU通信异常" },
            { failed: "engineTempAnomaly", label: "发动机温度异常" },
            { failed: "engineOilPressureAnomaly", label: "发动机机油压异常" },

        ],
        //无人艇
        [
            { failed: "name", label: "目标名称" },
            { failed: "code", label: "目标代号" },
            { failed: "targetType", label: "目标类型" },

            // { failed: "domain", label: "所属域" },
            { failed: "latitude", label: "纬度" },
            { failed: "longitude", label: "经度" },
            { failed: "speed", label: "航速" },
            { failed: "heading", label: "航向" },
            { failed: "bowDirection", label: "艏向" },
            { failed: "turnRate", label: "转向速率" },
            { failed: "roll", label: "横滚" },
            { failed: "pitch", label: "俯仰" },
            { failed: "windSpeed", label: "风速" },
            { failed: "windDirection", label: "风向" },
            { failed: "positioningDeviceStatus", label: "定位设备状态" },
            { failed: "compassDeviceStatus", label: "罗经设备状态" },
            { failed: "windDirectionDeviceStatus", label: "风向设备状态" },
        ],
        // 弹
        [
            { failed: "name", label: "目标名称" },
            { failed: "code", label: "目标代号" },
            { failed: "targetType", label: "目标类型" },

            // { failed: "domain", label: "所属域" },
            { failed: "longitude", label: "经度" },
            { failed: "latitude", label: "纬度" },
            { failed: "altitude", label: "高度(km)" },
            { failed: "pitch", label: "俯仰角" },
            { failed: "yaw", label: "偏航角" },
            { failed: "roll", label: "滚转角" },
            { failed: "speed", label: "综合速度(km/s)" },

        ],

    ]