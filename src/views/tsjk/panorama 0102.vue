<template>
    <CesiumBox></CesiumBox>
    <el-radio-group v-model="state.pageRadio" class="pageRadio" @change="pageRadioChange">
        <el-radio-button label="1">全景态势</el-radio-button>
        <el-radio-button label="2">任务态势 </el-radio-button>
        <el-radio-button label="3">资源态势</el-radio-button>
        <el-radio-button label="4">目标态势</el-radio-button>
    </el-radio-group>
    <div class="showOrHidden-box" :style="{ right: state.pageRadio == 1 ? '500px' : '16%' }">
        <template v-if="state.pageRadio != 1">
            <!-- <div>
                <label>航迹显隐：</label>
                <el-switch 
                    v-model="state.showTrack" 
                    inline-prompt
                    active-text="显示"
                    inactive-text="隐藏"
                />
            </div> -->
            <div>
                <label>范围显隐：</label>
                <el-switch v-model="state.showArea" inline-prompt active-text="显示" inactive-text="隐藏" />
            </div>
        </template>
        <div>
            <label>名称显隐：</label>
            <el-switch v-model="state.showLabel" inline-prompt active-text="显示" inactive-text="隐藏" />
        </div>
    </div>
    <!-- 全景态势 -->

    <customPopup left="0.5%" top="7%" width="25%" height="92%" headType="2" v-if="state.pageRadio == 1">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">任务执行情况</span>
        </template>
        <template #content>
            <div class="scheduleClass">
                <div class="task_schedule" v-for="task in state.taskList" :key="task.id">
                    <div style="width: calc(100% - 20px);padding-right: 20px;overflow-wrap:break-word">{{ task.taskName
                        }}</div>
                    <el-progress :percentage="task.schedule || 0" :color="customColor" />
                </div>
            </div>
        </template>
    </customPopup>
    <customPopup right="0.5%" top="7%" width="25%" height="35%" headType="2" v-if="state.pageRadio == 1">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">目标接入情况</span>
        </template>
        <template #content>
            <div class="chartClass" ref="Chart2"></div>
        </template>
    </customPopup>
    <customPopup right="0.5%" bottom="1%" width="25%" height="35%" headType="2" v-if="state.pageRadio == 1">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">资源接入情况</span>
        </template>
        <template #content>
            <div class="chartClass" ref="Chart4"></div>
        </template>
    </customPopup>
    <!-- 任务态势 -->
    <customPopup left="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 2">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">任务列表</span>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;">
                <el-table ref="taskTableRef" class="custom-table" :data="state.taskList" highlight-current-row
                    style="height: 100%" @row-click="taskTableClick">
                    <el-table-column align="center" prop="taskName" label="任务名称" min-width="3"></el-table-column>
                    <el-table-column align="center" prop="status" label="任务类型" min-width="2">
                        <template v-slot="scope">
                            <span>{{ scope.row.taskType[0] ? dictValue("businessType", scope.row.taskType[0]) : ""
                                }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="schedule" label="任务进度" min-width="2">
                        <template v-slot="scope">
                            <span>{{ scope.row.schedule }} %</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </template>
    </customPopup>
    <customPopup right="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 2">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">任务详情</span>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;overflow-y: auto;">
                <el-form :model="state.taskForm" label-width="80px">
                    <el-form-item label="任务名称" prop="taskName">{{ state.taskForm.taskName }}</el-form-item>
                    <!-- <el-form-item label="所属需求" prop="endTime">{{ state.taskForm.requirementName }}</el-form-item> -->
                    <!-- dictValue("taskStatus", state.taskForm.status) -->
                    <el-form-item label="业务类型" prop="taskType">{{ state.taskForm.taskType &&
                        state.taskForm.taskType.length > 0 ? dictValue("businessType", state.taskForm.taskType[0]) : ""
                        }}</el-form-item>
                    <el-form-item label="任务状态" prop="taskStatus">{{
                        state.taskForm.taskStatus
                        }}</el-form-item>
                    <el-form-item label="任务进度" prop="schedule">{{ state.taskForm.schedule }}% </el-form-item>

                    <el-form-item label="任务周期" prop="endTime">{{ dictValue("repetitionType", state.taskForm.repeatType)
                        }}</el-form-item>
                    <el-form-item label="开始时间" prop="startTime">{{ state.taskForm.startTime }}</el-form-item>
                    <el-form-item label="结束时间" prop="endTime">{{ state.taskForm.endTime }}</el-form-item>
                    <el-form-item label="任务描述" prop="taskComment">{{ state.taskForm.taskComment }}</el-form-item>
                </el-form>
            </div>
        </template>
    </customPopup>
    <customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 2">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">任务执行情况</span>
            <span class="title-font" style="position: absolute; right: 3%;">当前时间：{{ timeSpan(state.currentTime)
                }}</span>
        </template>
        <template #content>
            <div class="chartClass" id="taskTimeLine"></div>
        </template>
    </customPopup>
    <!-- 资源态势 -->
    <customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 3">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">资源使用情况</span>
            <span class="title-font" style="position: absolute; right: 3%;">当前时间：{{ timeSpan(state.currentTime)
                }}</span>

        </template>
        <template #content>
            <div class="chartClass" id="resourceTimeLine"></div>
        </template>
    </customPopup>
    <customPopup left="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 3">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">资源列表</span>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;">
                <el-table ref="resourceTableRef" class="custom-table" :data="state.resourceList" style="height: 100%"
                    highlight-current-row @row-click="taskTableClickZY">
                    <el-table-column align="center" prop="equipmentDetail.name" label="设备名称"
                        min-width="3"></el-table-column>
                    <el-table-column align="center" prop="type" label="设备类型" min-width="2">
                    </el-table-column>
                </el-table>
            </div>
        </template>
    </customPopup>
    <customPopup right="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 3">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">资源详情</span>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;overflow-y: auto;">
                <el-form :model="state.ZYForm" label-width="120px" class="targetForm_class">
                    <el-form-item label="名称" prop="name">{{ state.ZYForm.name }}</el-form-item>
                    <el-form-item label="节点代号" prop="code">{{ state.ZYForm.code }}</el-form-item>
                    <el-form-item label="频段" prop="frequency">{{ state.ZYForm.frequency }}</el-form-item>
                    <el-form-item label="多目标能力" prop="targetCount">{{ state.ZYForm.targetCount }}</el-form-item>
                    <el-form-item label="业务类型" prop="businessType">{{ dictValue("businessType",
                        state.ZYForm.businessType) }}</el-form-item>
                    <el-form-item label="EIRP（dB）" prop="eirp">{{ state.ZYForm.eirp }}</el-form-item>
                    <el-form-item label="G/T（dB）" prop="gt">{{ state.ZYForm.gt }}</el-form-item>
                    <el-form-item label="天线类型" prop="antennaType">{{ dictValue("antennaType", state.ZYForm.antennaType)
                        }}</el-form-item>
                    <el-form-item label="工作体制" prop="workSystem">{{ dictValue("workSystem", state.ZYForm.workSystem)
                        }}</el-form-item>
                    <el-form-item label="经度（°）" prop="longitude">{{ state.ZYForm.longitude }}</el-form-item>
                    <el-form-item label="纬度（°）" prop="latitude">{{ state.ZYForm.latitude }}</el-form-item>
                    <el-form-item label="高度（米）" prop="altitude">{{ state.ZYForm.altitude }}</el-form-item>
                    <el-form-item label="测控距离（米）" prop="radius">{{ state.ZYForm.radius }}</el-form-item>
                </el-form>

                <el-link style="text-align: left;float: right;" @click="clickGplot()">拓扑图</el-link>
            </div>
        </template>
    </customPopup>

    <!-- 目标态势 -->
    <customPopup left="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 4">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">目标列表</span>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;">
                <el-table ref="targetTableRef" class="custom-table" :data="state.targetList" style="height: 100%"
                    highlight-current-row @row-click="targetTableClick">
                    <el-table-column align="center" prop="name" label="目标名称"></el-table-column>
                    <el-table-column align="center" prop="targetTypeValue" label="目标类型">
                        <!-- <template v-slot="{ row }">
                            <span>{{ state.targetTypeList[row.targetTypeValue] }}</span>
                        </template> -->
                    </el-table-column>
                    <!-- <el-table-column align="center" prop="name" label=" " width="50">
                        <template v-slot="{ row }">
                            <span @click="clickDetileRow(row)">
                                <el-icon style="cursor: pointer;">
                                    <View />
                                </el-icon>
                            </span>
                        </template>
                    </el-table-column> -->

                </el-table>
            </div>
        </template>
    </customPopup>
    <customPopup right="0.5%" top="7%" width="15%" height="30%" v-if="state.pageRadio == 4">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">目标详情</span>
        </template>
        <template #content>
            <el-form :model="state.targetForm" label-width="124px"
                style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;" class="targetForm_class"
                v-show="state.targetForm?.id" label-position="right">
                <el-form-item v-for="item in formLabelList" :label="item.label + ' :'" :prop="item.key" :key="item.key"
                    v-show="targetShowLabelList.includes(item.key)">
                    <span v-if="!item.multiple">{{ state.targetForm[item.key] }}</span>
                    <span v-else-if="item.multiple">
                        <span v-for="(textKey, value) in state.targetForm[item.key]" :key="textKey">{{
                            value }}：{{ textKey }}<br /></span>
                    </span>
                </el-form-item>

            </el-form>
            <el-link style="text-align: left;float: right;" v-show="state.targetForm?.id"
                @click="clickDetileRow(state.targetForm)">更多...</el-link>
        </template>
    </customPopup>
    <customPopup right="0.5%" top="38%" width="15%" height="30%" v-if="state.pageRadio == 4">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">终端状态</span>
        </template>
        <template #content>
            <el-form :model="state.terminalDate" label-width="124px"
                style="height: calc(100% - 30px);overflow: auto;padding-left: 0px;" class="targetForm_class"
                v-show="state.terminalDate?.id" label-position="right">
                <el-form-item v-for="item in terminalList" :label="item.label + ' :'" :prop="item.key" :key="item.key"
                    v-show="terminalBriefList.includes(item.key)">
                    <span v-if="!item.multiple">{{ state.terminalDate[item.key] }}</span>
                    <span v-else-if="item.multiple">
                        <span v-for="(textKey, value) in state.terminalDate[item.key]" :key="textKey">{{
                            value }}：{{ textKey }}<br /></span>
                    </span>
                </el-form-item>
            </el-form>
            <!-- v-show="state.targetForm?.id" -->
            <el-link style="text-align: left;float: right;" @click="clickTerminalRow()">更多...</el-link>
        </template>
    </customPopup>
    <customPopup left="33%" top="20%" width="35%" height="50%" v-if="state.showPic && state.pageRadio == 4">
        <template #header>
            <span class="list_icon"></span>
            <span class="title-font">目标素材</span>
            <div style="position: absolute; right: 2%">
                <el-button @click="state.showPic = false">关闭</el-button>
            </div>
        </template>
        <template #content>
            <div style="height: 100%;width: 100%;overflow-y: auto;">
                <div style="width: 100%;height: 48%;">
                    <img style="height: 100%;width: 100%;" :src="hostLink + '/file/test.png'" />
                </div>
                <div style="width: 100%;;height: 48%;">
                    <video style="height: 100%;width: 100%;" controls type="video/wmv"
                        :src="hostLink + '/file/video.wmv'"></video>
                </div>
            </div>
        </template>
    </customPopup>
    <customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 4">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">目标接入情况</span>
            <span class="title-font" style="position: absolute; right: 3%;">当前时间：{{ timeSpan(state.currentTime)
                }}</span>

        </template>
        <template #content>
            <div class="chartClass" id="targerTimeLine"></div>
        </template>
    </customPopup>
    <customPopup left="25%" top="15%" width="50%" height="60%" v-if="state.pageRadio == 4 && state.detailShow">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">目标详情</span>
            <span class="title-font" style="position: absolute; right: 3%;cursor: pointer;"
                @click="state.detailShow = false">X
            </span>
        </template>
        <template #content>
            <tsDetail :tsData="state.targetForm" v-if="state.pageRadio == 4 && state.detailShow"></tsDetail>
        </template>
    </customPopup>
    <customPopup left="calc(50% - 575px)" top="15%" width="1150px" height="60%"
        v-if="state.pageRadio == 4 && state.terminalStateShow">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">终端状态</span>
            <span class="title-font" style="position: absolute; right: 3%;cursor: pointer;"
                @click="state.terminalStateShow = false">X
            </span>
        </template>
        <template #content>
            <terminalState :terminalDate="state.terminalDate" v-if="state.pageRadio == 4 && state.terminalStateShow">
            </terminalState>
        </template>
    </customPopup>
    <customPopup left="25%" top="15%" width="50%" height="60%" v-if="state.pageRadio == 3 && state.gplotShow">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">拓扑图</span>
            <span class="title-font" style="position: absolute; right: 3%;cursor: pointer;"
                @click="state.gplotShow = false">X
            </span>
        </template>
        <template #content>
            <gplot :gplotData="state.gplotData" v-if="state.pageRadio == 3 && state.gplotShow">
            </gplot>
        </template>
    </customPopup>
</template>

<script setup>
import { onMounted, onUnmounted, ref, reactive, watch } from 'vue'
import CesiumBox from "@/components/evmap/CesiumBox.vue";
import customPopup from "@/components/customPopup.vue";
import * as situationApi from "@/service/API/system/situation.js";
import * as areaApi from "@/service/API/system/areaManage.js";
import * as dataApi from "@/service/API/home/<USER>";
import * as resourceApi from "@/service/API/system/resourceDispatch.js";
import * as formLabel from "./formLabel.js"
import * as formLabel2 from "./formLabel2.js"
import * as terminalLable from "./terminalLable.js"
import tsDetail from "./tsDetail.vue";
import terminalState from './terminalState.vue';
import { ElMessage } from 'element-plus';
import gplot from "./gplot.vue"

const dictValue = window.dictValue;
const timeSpan = window.timeSpan;
let hostLink = AdminServerApi

const taskTableRef = ref(null)
const resourceTableRef = ref(null)
const targetTableRef = ref(null)

const customColor = [
    { color: "#f56c6c", percentage: 20 },
    { color: "#e6a23c", percentage: 40 },
    { color: "#1989fa", percentage: 60 },
    { color: "#5cb87a", percentage: 80 },
    { color: "#03a2a7", percentage: 100 },
];
var formLabelList = formLabel2.failedList
var terminalList = terminalLable.failedList //终端字段（全）
var terminalBriefList = terminalLable.showBriefList //终端字段（简略显示列表）
var targetShowLabelList = formLabel2.showList //目标详情展示列表
const state = reactive({
    pageRadio: "1",
    taskList: [],//任务列表
    taskForm: {},//任务详情
    // ZYList: [],// sdw 资源列表
    ZYForm: [],// sdw 资源详情
    chooseTask: undefined,
    chooseResource: undefined,
    chooseTarget: undefined,
    showTargetList: [],//当前要展示的目标列表
    showResourceList: [],//当前要展示的资源列表
    targetList: [],
    targetFiled: [],//目标的字段
    showList: [],
    targetForm: {

    },//目标详情
    showPic: false,//素材展示
    getting: false,//正在获取中
    playStatus: "pause",//播放状态
    resourceList: [],//展示的资源列表
    currentTime: "2000-01-01 00:00:01",//当前时间
    showTrack: true, // 显隐航迹
    showArea: true, // 显隐范围
    showLabel: true, //名称显隐
    targetTypeList: ["无人车", "无人艇", "无人机"],
    detailShow: false,//目标详情展示
    detailActiveName: "1",//目标详情展示页面的tab
    terminalStateShow: false,//终端状态弹窗控制
    terminalDate: {},//终端数据
    gplotShow: false,//拓扑图显示控制
    gplotData: {},//拓扑图数据
    chooseTargetId: null,//在列表中选中的目标
})
const Chart2 = ref(null);

const Chart4 = ref(null);

let fontColor = window.$fontColor;
let TSinterval
let timePoint = 0
let uuid = parseInt(Math.random() * 100000000000);
let panoramaId
onMounted(() => {
    panoramaId = localStorage.getItem("panoramaId")
    pageRadioChange()
    // getTaskList()//获取任务进度列表
    // getZYList() // sdw

    window.addEventListener("onmessageTSWS", getSocketData);
    // if (panoramaId) {
    //     getSituationData(panoramaId)
    //     state.playStatus = "play"
    // } else {
    //     ElMessage.warning("请在需求界面指定演示需求")
    // }

    // TSinterval = setInterval(() => {
    //     // console.log("panoramaPage");
    //     if (state.playStatus == "play") {
    //         callServer()
    //     } else {
    //         // callServer()
    //         // mapTool.removeAll()
    //     }

    // }, 1000);

})
onUnmounted(() => {
    window.removeEventListener("onmessageTSWS", getSocketData);
    mapTool.removeAll()
    clearInterval(TSinterval)
})
// 显隐航迹
watch(() => state.showTrack, (val) => {
    mapTool.showEffByAll('line', val)
})
// 显隐雷达
watch(() => state.showArea, (val) => {
    state.showResourceList.forEach((ele) => {
        let type = ele.dataType == 2 ? 'cylinder' : 'radar'
        mapTool.showEffById(ele.equipmentDetail.id, type, val)
    })
    //   mapTool.showEffByAll('radar', val)
    //   mapTool.showEffByAll('cylinder', val)
})
// 显隐名称
watch(() => state.showLabel, (val) => {
    mapTool.showEffByAll('label', val)
})

var currentPage, currentIds
function callServer() {
    if (state.getting) {
        return;
    }
    let ids = state.taskList.map(item => { return item.id })
    let obj = {
        timePoint: timePoint * (apanoramaTimeConfig || 10),
        requirementId: panoramaId,
        equipmentId: undefined,
        targetId: undefined,
        // taskIds: [],
        drag: false,
        step: 1,
        bzId: uuid,
        // progressTaskIds: ids
        taskIds: ids,
        filterTaskId: undefined,
        isSimulate: true
    };
    // if (!state.chooseTask && !state.chooseResource && !state.chooseTarget) {
    //     obj.taskIds = ids
    // } else {
    //     state.chooseTask && (obj.taskIds = [state.chooseTask])
    // }
    // obj.taskIds = ids
    state.chooseResource && (obj.equipmentId = state.chooseResource)
    state.chooseTarget && (obj.targetId = state.chooseTarget)
    state.chooseTask && (obj.filterTaskId = state.chooseTask)
    state.getting = true;
    mapTool.removeAllFlowLine()
    situationApi.dragOrPlay(obj).then((res) => {
        if (res.data.code == 200) {
            let data = res.data.data;
            timePoint += 1
            state.getting = false;
            // timeline?.setCustomTime(state.currentTime);
            // if (loading) {
            //     loading.close();
            //     loading = null;
            // }
            for (const key in data.satellite) {
                let item = data.satellite[key][0]
                let info = state.showResourceList.find(item => { return item.id == key })
                info && mapTool.drawMap({ ...info, position: [item.degLon, item.degLon, item.alt * 1000], time: state.currentTime })
            }
            //更新实体位置
            state.targetList.forEach((target) => {
                let show = false
                const item = data.target.find((item) => target.targetId == item.mbId && item.points[0])
                if (item) {
                    let option = { ...target };
                    option.longitude = item.points[0].x;
                    option.latitude = item.points[0].y;
                    option.altitude = item.points[0].z;
                    option.direction = item.points[0].angle;
                    option.noShowRadar = state.pageRadio == 1 ? true : false
                    option.noShowCylinder = state.pageRadio == 1 ? true : false

                    option.name = state.showLabel ? option.name : ''
                    // option.position = [option.longitude, option.latitude, option.altitude];
                    mapTool.drawMap(option);
                    mapTool.showEntityById(option.targetId, true)
                    show = true
                    if (target.id == state.targetForm.id) {
                        // state.targetForm.longitude = parseInt(option.longitude * 1000) / 1000
                        // state.targetForm.latitude = parseInt(option.latitude * 1000) / 1000
                        // state.targetForm.altitude = parseInt(option.altitude * 1000) / 1000
                        state.targetForm.longitude = option.longitude
                        state.targetForm.latitude = option.latitude
                        state.targetForm.altitude = option.altitude

                    }
                }
                !show && mapTool.showEntityById(target.targetId, false)
            })
            let visEquipment = new vis.DataSet([]);
            let visTarget = new vis.DataSet([]);
            //更新任务进度
            data.task.forEach(item => {
                state.taskList.forEach(item2 => {
                    if (item2.id == item.id) {
                        // 更新有变动的进度和最终时间
                        let process = parseInt(item.process * 1000) / 1000
                        process == 0 && (item2.taskStatus = '待执行')
                        process == 100 && (item2.taskStatus = '执行完成')
                        if (process != item2.schedule) {
                            item2.schedule = parseInt(item.process * 1000) / 1000
                            item2.exitTime = process == 100 ? item.endTime : item.currentTime
                            item2.exitTime = process == 0 ? "" : item2.exitTime
                            process != 100 && (item2.taskStatus = '执行中')
                        } else {
                            (process !== 0 && process !== 100) && (item2.taskStatus = '执行中断')

                        }
                        if (state.taskForm.id == item2.id) {
                            state.taskForm = { ...item2 }
                        }
                    }
                })
                // 只绘制当前选中的进度条
                if (state.pageRadio == 2 && state.taskForm.id == item.id) {
                    if (item.id == state.chooseTask && item.process > 0) {
                        let visTask = new vis.DataSet([]);
                        // visTask.add({
                        //     id: state.taskForm.id + "guihua",
                        //     group: "111" + state.taskForm.id,
                        //     content: state.taskForm.taskName,
                        //     start: timeSpan(state.taskForm.startTime),
                        //     end: timeSpan(state.taskForm.endTime),
                        //     className: "vis_guihuaTask"
                        // })
                        item.periods.forEach((period, index) => {
                            visTask.add({
                                id: state.taskForm.id + "shiji" + index,
                                group: "222" + state.taskForm.id,
                                content: period.equipmentName,
                                start: period.startTime,
                                end: period.endTime,
                                className: "vis_shijiTask"

                            })
                        })
                        // let task = state.taskList.find(ta => { return ta.id == item.id })
                        // let visTask = visItems.get(state.chooseTask + "shiji")
                        // visTask.start = task.startTime
                        // visTask.end = task.exitTime;
                        visItems = visTask
                        timeline?.setItems(visItems);
                    }
                    // state.taskForm.schedule = parseInt(item.process * 1000) / 1000
                    // if (state.pageRadio == 2 && item.id == state.chooseTask && item.process > 0 && item.process <= 100) {
                    //     let visTask = visItems.get(state.chooseTask + "shiji")
                    //     visTask.start = item.startTime
                    //     visTask.end = item.process == 100 ? item.endTime : item.currentTime
                    //     timeline?.setItems(visItems);

                    // }
                }

                if (state.pageRadio == 3) {
                    // state.chooseResource



                    item.periods.forEach(period => {
                        if (period.equipmentId == state.chooseResource) {
                            visEquipment.add({
                                id: period.equipmentId + "" + item.id,
                                group: item.id,
                                content: item.taskName,
                                start: period.startTime,
                                end: period.endTime,
                                className: "vis_shijiTask"
                            })
                        }
                    })
                    // visItems = visEquipment
                }
                if (state.pageRadio == 4) {
                    // state.chooseTarget

                    item.periods.forEach(period => {
                        if (period.targetId == state.chooseTarget) {
                            visTarget.add({
                                id: period.targetId + "" + item.id,
                                group: state.chooseTarget,
                                content: item.taskName,
                                start: period.startTime,
                                end: period.endTime,
                                className: "vis_shijiTask"
                            })
                        }
                    })
                    // visItems = visTarget
                }
            })
            if (state.pageRadio == 3) {
                timeline?.setItems(visEquipment);

            } else if (state.pageRadio == 4) {
                timeline?.setItems(visTarget);

            }
            timeline?.setCustomTime(data.task[0]?.currentTime || "2000-01-01 00:00:01")

            // timeline?.setItems(visItems);
            state.currentTime = data.task[0]?.currentTime || "2000-01-01 00:00:01"
            //绘制流动线
            data.targetEquipmentRelation.forEach(item => {
                // #808080b0
                let showResourceIds = state.showResourceList.map(x => {
                    return x.equipmentId
                })
                let showTargetIds = state.showTargetList.map(x => {
                    return x.id
                })
                if (showTargetIds.includes(item.targetId) && showResourceIds.includes(item.equipmentId)) {
                    // #ff000090
                    mapTool.drawflowLine(item.targetId, item.equipmentId, "#100befd4")
                    mapTool.drawflowLine(item.equipmentId, item.targetId, "#100befd4")
                }
            })

            // 显示隐藏地面雷达
            let ids = state.showResourceList.map(item => {
                return item.equipmentId
            })

            // state.resourceList.forEach(item => {
            //     let noShowRadar = false
            //     if (state.pageRadio == 1) {
            //         noShowRadar = true
            //     } else {
            //         noShowRadar = !ids.includes(item.equipmentDetail.id)
            //     }
            //     mapTool.drawMap({ ...item.equipmentDetail, dataType: item.dataType, noShowRadar: noShowRadar })
            // })
            if (currentPage != state.pageRadio || JSON.stringify(currentIds) != JSON.stringify(ids)) {
                currentPage = state.pageRadio
                currentIds = ids
                state.resourceList.forEach(item => {
                    let noShowRadar = false
                    if (state.pageRadio == 1) {
                        noShowRadar = true
                    } else {
                        noShowRadar = !ids.includes(item.equipmentDetail.id)
                    }
                    mapTool.drawMap({ ...item.equipmentDetail, dataType: item.dataType, noShowRadar: noShowRadar, noShowCylinder: noShowRadar })
                    // 全景态势不显示圆锥
                })
                state.showResourceList.forEach(item => {
                    if (state.pageRadio != 1) {
                        setTimeout(() => {
                            mapTool.showEffById(item.equipmentDetail.id, "cylinder", true)
                        }, 100);
                    }
                })
            }

        } else {
            ElMessage.error(res.data.message);
            state.getting = false;
        }
    });
}
//切换场景
function pageRadioChange() {
    // mapTool.removeAll()
    // state.playStatus = "pause"
    if (state.pageRadio != 1) {
        state.showArea = true
    }
    state.showLabel = true;
    state.chooseTask = undefined
    state.chooseResource = undefined;
    state.chooseTarget = undefined;
    state.showResourceList = []
    state.showTargetList = []
    state.targetForm = {}
    state.detailShow = false
    mapTool.removeAllFlowLine()

    switch (state.pageRadio) {
        case "1":

            // drawChart2()

            // drawChart4()
            setTimeout(() => {
                // mapTool.showEffByAll("cylinder", false)
                const height = FlyToHeightConfig[panoramaId] || 1000000
                const position = FlyToHeightConfig[panoramaId + '-position']
                position && Viewer.camera.flyTo({ destination: Cesium.Cartesian3.fromDegrees(position[0], position[1], height) })
            }, 1000);
            mapTool.showEntityByAll(true, false)
            state.chooseTask = undefined
            state.chooseTarget = undefined
            state.chooseResource = undefined
            state.showTargetList = [...state.targetList]
            state.showResourceList = [...state.resourceList]
            break;
        case "2":
            setTimeout(() => {
                initTimeLine("taskTimeLine")
                drawTimeLine("taskTimeLine")
                // mapTool.showEffByAll("cylinder", true)
                // mapTool.showEntityByAll(false)
                mapTool.showEntityByAll(true, true)
                // getTaskData()
                // 默认第一个
                // if (state.taskList[0]) {
                //     taskTableClick(state.taskList[0])
                //     taskTableRef.value.setCurrentRow(state.taskList[0])
                // }


            }, 0);
            break;
        case "3":
            setTimeout(() => {
                initTimeLine("resourceTimeLine")
                drawTimeLine("resourceTimeLine")
                // mapTool.showEffByAll("cylinder", true)
                // mapTool.showEntityByAll(false)
                mapTool.showEntityByAll(true, true)
                // 默认第一个
                // if (state.taskList[0]) {
                //     taskTableClickZY(state.resourceList[0])
                //     resourceTableRef.value.setCurrentRow(state.resourceList[0])
                // }
            }, 0);
            break;
        case "4":
            setTimeout(() => {
                initTimeLine("targerTimeLine")
                drawTimeLine("targerTimeLine")
                // mapTool.showEffByAll("cylinder", true)
                mapTool.showEntityByAll(true, true)

                // mapTool.showEntityByAll(false)

                // 默认第一个
                // if (state.taskList[0]) {
                //     targetTableClick(state.targetList[0])
                //     targetTableRef.value.setCurrentRow(state.targetList[0])
                // }
            }, 0);
            break;
        default:
            break;
    }
}
//获取任务列表
function getTaskData() {

}
//获取

//右上统计图 //目标执行情况
function drawChart2() {
    let chart = echarts.init(Chart2.value)
    let option = {
        color: chartColors,

        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['遥控', '遥测', '测量', '数传'],
            textStyle: {
                color: fontColor,
            },
        },
        toolbox: {
            show: true,
            orient: 'vertical',
            left: 'right',
            top: 'center',
            // feature: {
            //     mark: {show: true},
            //     dataView: {show: true, readOnly: false},
            //     magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
            //     restore: {show: true},
            //     saveAsImage: {show: true}
            // }
        },
        xAxis: [
            {
                type: 'category',
                axisTick: { show: false },
                data: ['无人机', '无人艇', '无人车', '弹'],
                axisLine: window.axisLine,
                axisLabel: {
                    color: fontColor,
                    rotate: 45,
                },
            }
        ],
        yAxis: [
            {
                type: 'value',
                splitLine: window.splitLine,
                axisLine: window.axisLine,
                axisLabel: {
                    color: fontColor,
                },
            }
        ],
        series: [
            {
                name: '遥控',
                type: 'bar',
                barGap: 0,
                // label: "遥控",
                emphasis: {
                    focus: 'series'
                },
                data: [0, 0, 0, 0]
            },
            {
                name: '遥测',
                type: 'bar',
                // label: labelOption,
                emphasis: {
                    focus: 'series'
                },
                data: [0, 0, 0, 0]


            },
            {
                name: '测量',
                type: 'bar',
                // label: labelOption,
                emphasis: {
                    focus: 'series'
                },
                data: [0, 0, 0, 0]


            },
            {
                name: '数传',
                type: 'bar',
                // label: labelOption,
                emphasis: {
                    focus: 'series'
                },
                data: [0, 0, 0, 0]


            }
        ]
    };
    situationApi.getTargetBz({ requirementId: panoramaId }).then(res => {
        if (res.data.code == 200) {
            let data = res.data.data
            let targetType = ['无人机', '无人艇', '无人车', "弹"]
            data.forEach(item => {
                let businessIndex = item.taskType[0] - 1
                let targetTypeIndex = targetType.indexOf(item.target.dataTypeValue)
                if (targetTypeIndex > -1 && businessIndex > -1) {
                    option.series[businessIndex].data[targetTypeIndex] += 1
                }
            })
            chart.setOption(option, true)
        } else {
            ElMessage.error(res.data.message)
        }
    })

    // chart.setOption(option, true)


}
//右下统计图 //资源接入情况 第一版-废弃
function drawChart4_old() {
    let chart = echarts.init(Chart4.value)
    let typeList = [
        "无人艇",
        "弹",
        "大型无人机",
        "中型无人机",
        "小型无人机",
        "大型无人艇",
        "中型无人艇",
        "小型无人艇",
        "低轨卫星",
        "中轨卫星",
        "高轨卫星",
        "固定站",
        "移动站",
        "浮空平台",
        "无人机",
        "无人车",
    ]
    let option = {
        color: chartColors,

        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: typeList,
            type: 'scroll',
            orient: "horizontal",
            textStyle: {
                color: fontColor,
            },
        },
        toolbox: {
            show: false,
            orient: 'vertical',
            left: 'right',
            top: 'center',
            // feature: {
            //     mark: {show: true},
            //     dataView: {show: true, readOnly: false},
            //     magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
            //     restore: {show: true},
            //     saveAsImage: {show: true}
            // }
        },
        yAxis: [
            {
                type: 'category',
                axisTick: { show: false },
                data: [],
                axisLine: window.axisLine,
                axisLabel: {
                    color: fontColor,
                    rotate: 45,
                },
            }
        ],
        xAxis: [
            {
                type: 'value',
                splitLine: window.splitLine,
                axisLine: window.axisLine,
                axisLabel: {
                    color: fontColor,
                },
            }
        ],
        series: []
    };

    typeList.forEach(type => {
        let seriesItem = {
            name: type,
            type: 'bar',
            stack: 'total',
            emphasis: {
                focus: 'series'
            },
            data: []
        }
        option.series.push({ ...seriesItem })
    })
    situationApi.getRealTimeAreaType({ requirementId: panoramaId }).then(res => {
        if (res.data.code == 200) {
            let data = res.data.data
            data.forEach(item => {
                option.yAxis[0].data.push(item.areaName)
                item.dataGenerals?.forEach(general => {
                    let typeIndex = typeList.indexOf(general.tableComment)
                    if (typeIndex > -1) {
                        option.series[typeIndex].data.push(general.count)
                    } else {
                        option.series[typeIndex].data.push(0)
                    }
                })
            })
            chart.setOption(option, true)
        } else {
            ElMessage.error(res.data.message)
        }
    })


}
//右下统计图 //资源接入情况 第一版-废弃
function drawChart4() {
    let chart = echarts.init(Chart4.value)
    // 来源 1 陆 2 海 3 空 4 天
    let typeList = [
        "地基",
        "海基",
        "空基",
        "天基",]
    let option = {
        color: chartColors,

        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: [],
            type: 'scroll',
            orient: "horizontal",
            textStyle: {
                color: fontColor,
            },
        },
        toolbox: {
            show: false,
            orient: 'vertical',
            left: 'right',
            top: 'center',
            // feature: {
            //     mark: {show: true},
            //     dataView: {show: true, readOnly: false},
            //     magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
            //     restore: {show: true},
            //     saveAsImage: {show: true}
            // }
        },
        yAxis: [
            {
                type: 'category',
                axisTick: { show: false },
                data: typeList,
                axisLine: window.axisLine,
                axisLabel: {
                    color: fontColor,
                    rotate: 45,
                },
            }
        ],
        xAxis: [
            {
                type: 'value',
                splitLine: window.splitLine,
                axisLine: window.axisLine,
                axisLabel: {
                    color: fontColor,
                },
            }
        ],
        series: []
    };

    // typeList.forEach(type => {
    //     let seriesItem = {
    //         name: type,
    //         type: 'bar',
    //         emphasis: {
    //             focus: 'series'
    //         },
    //         data: []
    //     }
    //     option.series.push({ ...seriesItem })
    // })
    situationApi.getRealTimeAreaType({ requirementId: panoramaId }).then(res => {
        if (res.data.code == 200) {
            let resData = res.data.data
            // 兵种
            for (const key in resData) {
                let data = resData[key]
                // 域
                data.forEach(item => {
                    option.legend.data.push(item.areaName)
                    let seriesItem = {
                        name: item.areaName,
                        type: 'bar',
                        stack: 'total',
                        emphasis: {
                            focus: 'series'
                        },
                        data: [0, 0, 0, 0]
                    }
                    let count = 0

                    item.dataGenerals?.forEach(general => {
                        count = count + general.count
                    })
                    seriesItem.data[key - 1] = count
                    option.series.push({ ...seriesItem })
                })
            }
            chart.setOption(option, true)
        } else {
            ElMessage.error(res.data.message)
        }
    })


}
window.historyTrack = {}
// var clacNum = 0
function getSocketData(evt) {
    // clacNum++;
    // 目标
    let detailData = evt.detail.data;
    if (detailData.type == 5) {
        let _data = evt.detail.data.data;
        _data.forEach(item => {
            item.id = item.mbId;
            item.direction = item.rollAngle;
            // item.name = item.name + item.id
            // item.longitude = 110 + clacNum*0.1;
            // item.latitude = 29;
            let _type = 4;
            if (item.targetType == 0) {
                _type = 6;
            } else if (item.targetType == 1) {
                _type = 5;
            }
            mapTool.drawMap({ ...item, dataType: _type })
        })
        let targetList = _data

        state.targetForm = targetList.find(item => item.id == state.chooseTargetId) || {}
        return
    }
    // 终端
    if (detailData.type == 6) {
        let nodeList = Object.keys(detailData.data)
        let nodeId = nodeList[0]
        if (detailData.data[nodeId].bindingTarget) {
            let bindingTarget = detailData.data[nodeId].bindingTarget
            let index = state.targetList.findIndex(item => item.id == bindingTarget.id)
            if (index == -1) {
                state.targetList.push({ ...bindingTarget })
            } else {
                state.targetList.splice(index, 1, { ...bindingTarget })
            }
        }

        state.gplotData =
        {
            nodeRelation: detailData.data[nodeId].nodeRelation,
            nodeId: nodeId
        }
        if (nodeId == 0) {
            return
        }
        let baseTxt = detailData.data[nodeId]?.spectrumFrame?.spectrumValues
        if (!baseTxt) {
            state.terminalDate = JSON.parse(JSON.stringify({ ...detailData.data[nodeId], nodeId: nodeId }))
        } else {
            let aData = window.atob(baseTxt)
            let arr = new Int8Array(aData.length)
            // let arr2 = new Int8Array(a.length)x`
            let arr2 = []
            for (let i = 0; i < aData.length; i++) {
                arr[i] = aData.charCodeAt(i)
                arr2.push(arr[i])
            }
            detailData.data[nodeId].spectrumFrame.spectrumList = arr2
            // state.terminalDate = JSON.parse(JSON.stringify({ ...detailData.data[nodeId], nodeId: nodeId }))
            state.terminalDate = { ...detailData.data[nodeId], nodeId: nodeId }
        }
    }
    // tsDetailData.value = 

}
//获取实体
function getTarget(dataTypeId, dataId, dataType, pos) {
    areaApi.getAreaGeneralDataDetail({ dataTypeId, dataId }).then(res => {
        if (res.data.code == 200) {
            mapTool.drawMap({
                ...res.data.data,
                dataType: dataType,
                position: pos,
                targetTrack: historyTrack[dataId],
                equipmentId: dataType == 3 ? dataId : undefined
            })
        } else {
            ElMessage.error(res.data.message)
        }
    })
}

var visGroups = new vis.DataSet([]); //时间轴分组
var visItems = new vis.DataSet([]);; //时间轴项目
var timeline; //时间轴管理器
/**
 * @description 绘制时间轴
 */
function initTimeLine(domID) {
    var container = document.getElementById(domID);
    timeline = new vis.Timeline(container);
    visGroups = new vis.DataSet([]);
    visItems = new vis.DataSet([]);
    // let start = moment(timelineStart).format("YYYY-MM-DD HH:mm:ss");
    // let min = moment(timelineStart).subtract(1, "h").format("YYYY-MM-DD HH:mm:ss")
    // let end = moment(timelineEnd).format("YYYY-MM-DD HH:mm:ss");
    let options = {
        autoResize: true,
        height: "90%", //高度
        width: "100%", //宽度
        min: "2000-01-01 00:00:00", //设置最小时间范围
        max: "2099-12-31 23:59:59", //设置最大时间范围
        // start: "2024-08-06 15:03:18", //设置开始时间
        // end: "2024-08-07 15:03:18", //设置结束时间
        stack: true, // ture则不重叠
        limitSize: true,
        verticalScroll: true,
        // cluster: true, //数据量过大时使用。
        locale: "zh-cn",
        xss: {
            disabled: true,
        },
        // groupOrder: function (a, b) {
        //     return a.value - b.value;
        // },
        editable: false,
        showCurrentTime: false,
        moment: function (date) {
            // return moment(date).format("YYYY-MM-DD HH:mm:ss");
            return moment(date);
        },
        locale: moment.locale("zh-cn")

    };
    timeline.setOptions(options);
    timeline.setGroups(visGroups);
    timeline.setItems(visItems);
    timeline.addCustomTime("2000-01-01 00:00:01");
}
//绘制时间轴项目
function drawTimeLine(domID) {
    // if (domID == "taskTimeLine") {
    // state.taskList.forEach(item => {
    //     visGroups.add({ id: "111" + item.id, content: '规划-' + item.taskName, value: "111" + item.id });
    //     visGroups.add({ id: "222" + item.id, content: '实际-' + item.taskName, value: "222" + item.id });
    //     visItems.add({
    //         id: item.id + "guihua",
    //         group: "111" + item.id,
    //         content: item.taskName,
    //         start: item.startTime,
    //         end: item.endTime,
    //     })
    //     visItems.add({
    //         id: item.id + "shiji",
    //         group: "222" + item.id,
    //         content: item.taskName,
    //         start: timeSpan(new Date(item.startTime).getTime() - Math.random() * 3000),
    //         end: timeSpan(new Date(item.endTime).getTime() - Math.random() * 2000),
    //     })
    // })
    visItems.clear()
    visGroups.clear()

    timeline.setItems(visItems);
    timeline.setGroups(visGroups);

    // }
}

// 任务列表获取
function getTaskList() {
    dataApi
        .getTaskList({
            page: 1,
            size: 20,
            // requirementIds: [state.requirementId],
            requirementIds: [panoramaId]
        })
        .then((res) => {
            if (res.data.code == 200) {
                state.taskList = res.data.data.records;
                // state.targetList = []
                state.taskList.forEach(item => {
                    item.schedule = 0
                    // getTaskTarget(item.id)
                })
                if (state.taskList.length > 0) {
                    // setTimeout(() => {
                    //     taskTableClick(state.taskList[0]);
                    // }, 500);
                }

            } else {
                ElMessage.error(res.data.message);
            }
        });
}
//根据需求ID查询需求下所有目标以及设备数据
function getSituationData(id) {
    situationApi.getSituationData({ id: id }).then(res => {
        if (res.data.code == 200) {
            state.targetList = res.data.data?.targets || []
            state.targetList.forEach(item => {
                item.relationId = item.id
                item.id = item.targetId
                mapTool.drawMap({ ...item, color: "red", textColor: "#fff" })
            })
            state.resourceList = res.data.data?.equipments || []
            let colors = ["#b3d1f8", "#a4ff21", "aqua", "pink", "green", "cadetblue"]
            let area = state.resourceList.map(item => {
                return item.equipmentDetail.areaId
            })
            area = Array.from(new Set(area))
            state.resourceList.forEach(item => {
                item.relationId = item.id
                item.id = item.equipmentId
                let colorIndex = area.indexOf(item.equipmentDetail.areaId)
                // console.log("colors", colors[colorIndex % 6]);
                mapTool.drawMap({
                    ...item.equipmentDetail,
                    dataType: item.dataType,
                    color: colors[colorIndex % 6],
                    textColor: "#fff",
                    noShowRadar: true,
                    noShowCylinder: true,
                })
            })
            state.showResourceList = state.resourceList
            state.showTargetList = state.targetList
        } else {
            ElMessage.error(res.data.message)
        }
    })
}
// 获取目标列表--
function getTaskTarget(taskId) {
    dataApi.getTaskTarget({ taskId: taskId }).then(res => {
        if (res.data.code == 200) {
            state.targetList.push(res.data.data)
        } else {
            ElMessage.error(res.data.message)
        }
    })
}
/**
 * @description 任务表格点击事件
 */
const taskTableClick = (row) => {
    clearData()
    state.chooseTask = row.id
    const height = FlyToHeightConfig[panoramaId] || 1000000
    const position = FlyToHeightConfig[panoramaId + '-position']
    position && Viewer.camera.flyTo({ destination: Cesium.Cartesian3.fromDegrees(position[0], position[1], height) })
    dataApi.getTaskByTaskId({ id: row.id }).then((res) => {
        if (res.data.code == 200) {
            state.taskForm = res.data.data;
            state.taskForm.schedule = row.schedule
            // state.playStatus = "play"
            getResourceByTask(row.id)
            // getRequirementById(state.taskForm.requirementId)
            // visGroups.add({ id: "111" + row.id, content: '规划-' + row.taskName, value: "111" + row.id });
            // visGroups.add({ id: "222" + row.id, content: '实际-' + row.taskName, value: "222" + row.id });

            // visGroups.add({ id: "111" + row.id, content: '规划任务', value: "111" + row.id });
            // visGroups.add({ id: "222" + row.id, content: '实际任务', value: "222" + row.id });
            // visItems.add({
            //     id: row.id + "guihua",
            //     group: "111" + row.id,
            //     content: row.taskName,
            //     start: timeSpan(state.taskForm.startTime),
            //     end: timeSpan(state.taskForm.endTime),
            //     className: "vis_guihuaTask"
            // })
            // visItems.add({
            //     id: row.id + "shiji",
            //     group: "222" + row.id,
            //     content: row.taskName,
            //     start: "2000-01-01 00:00:01",
            //     end: "2000-01-01 00:00:01",
            //     className: "vis_shijiTask"

            // })
            visGroups.add({ id: "222" + row.id, content: '', value: "222" + row.id });
            // visItems.add({
            //     id: row.id + "shiji",
            //     group: "222" + row.id,
            //     content: row.taskName,
            //     start: "2000-01-01 00:00:01",
            //     end: "2000-01-01 00:00:01",
            //     className: "vis_shijiTask"
            // })
            timeline.setGroups(visGroups);
            // timeline.setItems(visItems);

            timeline.setOptions({
                min: timeSpan(new Date(state.taskForm.startTime).getTime() - 86400),
                start: timeSpan(new Date(state.taskForm.startTime).getTime() - 86400),
                max: timeSpan(new Date(state.taskForm.endTime).getTime() + 86400),
                end: timeSpan(new Date(state.taskForm.endTime).getTime() + 86400),
            })
            state.showTargetList = state.targetList.filter(item => {
                return item.targetId == state.taskForm.target.targetId
            })
            state.showTargetList.forEach(item => {
                mapTool.showEntityById(item.targetId, true)
            })
        } else {
            ElMessage.error(res.data.message);
        }
    });
};
//根据任务ID获取资源
function getResourceByTask(taskId) {
    resourceApi.getEquipmentByTask({ taskId: taskId, size: 999, page: 1 }).then(res => {
        if (res.data.code == 200) {
            state.showResourceList = res.data.data.records
            state.showResourceList.forEach(item => {
                mapTool.showEntityById(item.equipmentId, true)
            })
            state.playStatus = "play"
        } else {
            ElMessage.error(res.data.message)
        }
    })
}
//资源行点击事件
const taskTableClickZY = (row) => {
    clearData()
    state.chooseResource = row.equipmentId
    // window.EVGISMAP("flyTo", {
    //     center: [row.equipmentDetail.longitude, row.equipmentDetail.latitude],
    // });
    const height = FlyToHeightConfig[panoramaId] || 1000000
    Viewer.camera.flyTo({ destination: Cesium.Cartesian3.fromDegrees(row.equipmentDetail.longitude, row.equipmentDetail.latitude, height) })
    dataApi.getDetailById({ dataId: row.equipmentId, dataTypeId: row.generalId }).then(res => {
        if (res.data.code == 200) {
            state.ZYForm = res.data.data;
            getDataByResource(row)
        } else {
            ElMessage.error(res.data.message);
        }
    })
}
//从列表中获取资源设备的名字
function getEquipmentName(id) {
    let name = ""
    name = state.resourceList.find(item => {
        return item.id == id
    })?.equipmentDetail.name
    return name
}
//获取最大最小时间
function getMaxMinTime(list) {
    let max, min
    list.forEach(item => {
        if (item.startTime < min || !min) {
            min = item.startTime
        }
        if (item.endTime > max || !max) {
            max = item.endTime
        }
    })
    return { min, max }
}
var taskByEquipment
//获取资源对应的需要显示的目标
function getDataByResource(row) {
    mapTool.showEntityById(row.id, true)
    situationApi.getDataByEquipment({ equipmentId: row.id, requirementId: panoramaId }).then(res => {
        if (res.data.code == 200) {
            let tasks = res.data.data.tasks || []
            let targets = res.data.data.targets || []
            taskByEquipment = res.data.data
            let max, min
            window.aaa = []
            tasks.forEach(item => {
                visGroups.add({ id: item.id, content: '', value: item.id });
                aaa.push(item.id)
                item.occupancies.forEach(occ => {
                    if (occ.startTime < min || !min) {
                        min = occ.startTime
                    }
                    if (occ.endTime > max || !max) {
                        max = occ.endTime
                    }
                    // visItems.add({
                    //     id: occ.equipmentId + "" + item.id,
                    //     group: item.id,
                    //     // content: getEquipmentName(occ.equipmentId),
                    //     content: item.taskName,
                    //     start: occ.startTime,
                    //     end: occ.endTime,
                    // })
                })
            })

            timeline.setGroups(visGroups);
            // timeline.setItems(visItems);

            timeline.setOptions({
                min: timeSpan(new Date(min).getTime() - 8640 * 10),
                start: timeSpan(new Date(min).getTime() - 8640 * 10),
                max: timeSpan(new Date(max).getTime() + 8640 * 10),
                end: timeSpan(new Date(max).getTime() + 8640 * 10),
            })
            let ids = targets.map(item => { return item.targetId })
            //控制显示
            state.showTargetList = state.targetList.filter(item => { return ids.includes(item.id) })
            targets.forEach(item => {
                mapTool.showEntityById(item.targetId, false)

            })
            state.showResourceList = state.resourceList.filter(item => { return row.id == item.id })
            mapTool.showEntityById(row.id, true)


            state.playStatus = "play"

        } else {
            ElMessage.error(res.data.message)
        }
    })
}
/**
 * @description 目标表格获取数据
 */
function simulaTargetState(row) {

    let list = ["无人机", "无人车", "无人艇", "弹"]
    let type = list.indexOf(row.targetType)
    situationApi.simulaTargetState({ type: type + 1 }).then(res => {
        if (res.data.code == 200) {
            state.targetForm = { ...res.data.data, ...state.targetForm }
            state.targetForm.longitude = null
            state.targetForm.latitude = null

            state.targetFiled = formLabel.failedList[type]
            getRowData(row)
        } else {
            ElMessage.error(res.data.message)
        }
    })
}
function getRowData(row) {
    areaApi.getAreaGeneralDataDetail({ dataTypeId: row.generalId, dataId: row.targetId }).then(res => {
        if (res.data.code == 200) {
            state.targetForm = { ...state.targetForm, ...res.data.data }
        } else {
            ElMessage.error(res.data.message)

        }
    })

}
function targetTableClick(row) {
    state.chooseTargetId = row.id

    // 清除详情页面数据
    state.targetForm = {}
    state.detailShow = false
    state.terminalStateShow = false
}
/**
 * @description 目标表格点击事件
 */
function targetTableClick2(row) {
    state.targetForm = {
        ...row
    }
    clearData()
    state.chooseTarget = row.targetId

    // window.EVGISMAP("flyTo", {
    //     center: [state.targetForm.longitude, state.targetForm.latitude],
    // });
    const height = FlyToHeightConfig[panoramaId] || 1000000
    Viewer.camera.flyTo({ destination: Cesium.Cartesian3.fromDegrees(state.targetForm.longitude, state.targetForm.latitude, height) })

    situationApi.getDataByTarget({ targetRelationId: row.relationId }).then(res => {
        if (res.data.code == 200) {
            let { equipment, task } = res.data.data
            window.bbb = []
            visGroups.add({ id: row.id, content: '', value: row.id });
            bbb.push(row.id)
            let ids = equipment
            state.showResourceList = state.resourceList.filter(item => { return ids.includes(item.id) })
            //控制显示
            state.showResourceList.forEach(item => {
                // visItems.add({
                //     id: item.equipmentDetail.id,
                //     group: row.id,
                //     content: item.equipmentDetail.name,
                //     start: item.startTime,
                //     end: item.endTime,
                // })
                mapTool.showEntityById(item.id, true)
            })
            state.showTargetList = state.targetList.filter(item => { return item.id == row.id })
            mapTool.showEntityById(row.id, true)
            // task.forEach(item => {
            //     visItems.add({
            //         id: item.id,
            //         group: row.id,
            //         content: item.taskName,
            //         start: item.startTime,
            //         end: item.endTime,
            //     })
            // })
            let { min, max } = getMaxMinTime(task)
            timeline.setOptions({
                min: timeSpan(new Date(min).getTime() - 8640 * 10),
                start: timeSpan(new Date(min).getTime() - 8640 * 10),
                max: timeSpan(new Date(max).getTime() + 8640 * 10),
                end: timeSpan(new Date(max).getTime() + 8640 * 10),
            })
            state.playStatus = "play"
            simulaTargetState(row)
        } else {
            ElMessage.error(res.data.message)
        }
    })
}
//初始化数据
function clearData() {
    visGroups?.clear()
    visItems?.clear()
    state.chooseTask = undefined
    state.chooseTarget = undefined
    state.chooseResource = undefined
    mapTool.showEntityByAll(false)
    // 关闭标牌框
    window.EVGISMAP('closeDiv', { className: '.infoDiv' })
    // 删除聚焦框
    window.selectedId && window.EVGISMAP("removeGroupEntityById", {
        id: window.selectedId + 'makerGroupOnlyOne',
        group: 'makerGroup'
    });

    state.showTargetList = []
    state.showResourceList = []
}
//获取需求信息
function getRequirementById(requirementId) {
    dataApi.getRequirementById({ id: requirementId }).then(res => {
        if (res.data.code == 200) {
            state.taskForm.requirementName = res.data.data?.requirementName
        } else {
            ElMessage.error(res.data.message);

        }
    })
}
//目标更多点击事件
function clickDetileRow() {
    state.terminalStateShow = false
    state.detailShow = true
}
// 终端更多点击事件
function clickTerminalRow() {
    state.detailShow = false
    state.terminalStateShow = true
}
//拓扑图更多点击事件
function clickGplot() {
    state.gplotShow = true
}
</script>

<style lang='less' scoped>
.showOrHidden-box {
    position: absolute;
    right: 500px;
    top: 90px;
    z-index: 999;
}

.scheduleClass {
    height: 100%;
    width: 100%;
    overflow-y: auto;
    box-sizing: border-box;

    .task_schedule {
        height: auto;
        min-height: 45px;
        width: 100%;
        padding: 0 10px;
        margin-bottom: 10px;
        box-sizing: border-box;

    }
}

.chartClass {
    width: 100%;
    height: 100%;
    background: #022141c0;

    :deep(.vis-content > .vis-labelset .vis-inner) {
        font-weight: bolder;
        font-size: 16px;
        margin: 0 5px;
    }

    :deep(.vis-group) {
        >.vis-item.vis-selected {
            background: #2f557b !important;
        }

        .vis-item {
            background: rgb(29, 94, 142);
        }
    }
}

.pageRadio {
    position: absolute;
    top: 10%;
    left: 50%;
    transform: translate(-50%, 0);
    background: #022141c0;
    border: 1px solid #2e5175;
}

.targetForm_class {
    height: calc(100% - 30px);

    :deep(.el-form-item) {
        margin-bottom: 0;

        .el-form-item__label {
            padding-right: 8px;
        }
    }
}
</style>