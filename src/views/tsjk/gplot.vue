<template>
    <el-tabs v-model="state.activeName" class="tabsClass">
        <el-tab-pane 
            label="拓扑图"
            name="tpt"
        >
            <div id="mynetwork"></div>
            <div class="tip_div">
                <el-row align="middle">
                    <div style="height: 3px;width: 30px;background: #05c705;"></div> &nbsp;&nbsp;一跳直连
                </el-row>
                <el-row align="middle">
                    <div style="height: 3px;width: 30px;background: gray;"></div> &nbsp;&nbsp;不直连
                </el-row>
            </div>
        </el-tab-pane>
        <el-tab-pane 
            label="节点状态"
            name="zdzt"
            class="tab-pane"
        >
            <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                <el-descriptions-item v-for="(textKey) in nodeMachineFrame || []" :key="textKey" width="150px">
                    <template #label>
                        <div class="cell-item">
                            {{ labelText(textKey) }}
                        </div>
                    </template>
                    <span class="cell-text" v-if="isBoolean(textKey)">
                        <div class="sucess_icon" v-if="props.terminalDate?.machineFrame?.[textKey]"
                            style="height: 24px;width: 24px;"></div>
                        <div class="close_icon" v-else style="height: 24px;width: 24px;"></div>
                    </span>
                    <span class="cell-text" v-else>{{ props.terminalDate?.machineFrame?.[textKey] || 0 }}{{
                        unitText(textKey) }}</span>
                </el-descriptions-item>
            </el-descriptions>
        </el-tab-pane>
    </el-tabs>
    </template>

<script lang='ts' setup>
import { defineComponent, onMounted, watch, onBeforeUnmount, reactive } from 'vue'
import * as terminalLable from "./terminalLable.js"

const props = defineProps({
    gplotData: {
        type: Object,
        default: () => {
            return {}
        },
    },
    terminalDate: {
        type: Object,

        default: () => {
            return {}
        },
    },
})
const state = reactive({
    activeName: 'tpt',
})
var linkData = []
var network
window.network = network
var nodes = new vis.DataSet([])
var edges = new vis.DataSet([])
var defaultLink = []
var interval
const nodeData = [{ id: "node0", label: '节点0', color: "#89abb1", x: "-100", y: "-150", },
{ id: "node1", label: '节点1', x: "100", y: "-150" },
{ id: "node2", label: '节点2', x: "200", y: "0" },
{ id: "node3", label: '节点3', x: "100", y: "150" },
{ id: "node4", label: '节点4', x: "-100", y: "150" },
{ id: "node5", label: '节点5', x: "-200", y: "0" },]

const terminalList = terminalLable.failedList
const nodeMachineFrame = terminalLable.nodeMachineFrame// 节点状态帧
// key转文字
function labelText(key) {
    let row = terminalList.find(item => item.key == key)
    return row ? row.label : "未定义"
}
//单位文字
function unitText(key) {
    let row = terminalList.find(item => item.key == key)
    return row?.unit ? ' ' + row.unit : " "
}


function isBoolean(key) {
    let row = terminalList.find(item => item.key == key)
    return row.isBoolean || false
}

onMounted(() => {

    for (let i = 0; i < nodeData.length; i++) {
        let itemA = nodeData[i];
        for (let j = 0; j < nodeData.length; j++) {
            let itemB = nodeData[j];
            if (i == j) {
                continue;
            } else {
                linkData.push({ from: itemA.id, to: itemB.id, color: "gray" })
            }
        }
    }
    // defaultLink = JSON.parse(JSON.stringify(linkData))
    initGplot()
    interval = setInterval(() => {
        linkData.forEach(item => {
            item.callback_num = (item.callback_num || 0) + 1
            if (item.callback_num == 2) {
                item.color = "gray"
                item.callback_num = 0
            }
        })
        edges.clear()
        edges.update(linkData)
    }, 1500)
})
onBeforeUnmount(() => {
    clearInterval(interval)
    interval = undefined
})
watch(() => props.gplotData, () => {
    updateGplot()
}
)

function initGplot() {
    nodes = new vis.DataSet([...nodeData]);

    // create an array with edges
    edges = new vis.DataSet([...linkData]);
    // create a network
    var container = document.getElementById('mynetwork');

    // provide the data in the vis fromat
    var data = {
        nodes: nodes,
        edges: edges
    };

    var options = {
        locale: "cn",
        autoResize: true,
        nodes: {
            shape: "dot",
            size: 20,
            font: {
                size: 16,
                color: "#ffffff",
            },
            borderWidth: 2,
            color: {
                background: "#6e6efd",
                border: "#4220fb"
            },
            shadow: true,//显示阴影
        },
        edges: {
            smooth: {
                enabled: true,
                type: "diagonalCross",
                // "type": "continuous",
                roundness: 0.35
            },
            arrows: { to: true }
        },
        // 用于调试
        // configure: {
        //     enabled: true,
        //     filter: "nodes,edges",
        //     container: container,
        //     showButton: true,
        // },
        physics: {
            enabled: false,//禁用物理模拟,因为网格布局是静态的
        }
    };
    // initialize your network!
    network = new vis.Network(container, data, options);
    window.network = network
    window.edges = edges

}
function updateGplot() {

    if (!network) {
        return
    }
    let nodeId = "node" + props.gplotData.nodeId
    let nodeRelation = props.gplotData.nodeRelation
    edges.clear()
    linkData = linkData.filter(item => item.to != nodeId)
    for (let nodeKey in nodeRelation) {
        if (nodeKey == nodeId) {
            continue;
        }
        switch (nodeRelation[nodeKey]) {
            case 0:

                break;
            case 1:
                linkData.push({ from: nodeKey, to: nodeId, color: "#05c705", callback_num: 0 })
                // edges.add({ from: nodeKey, to: nodeId })
                break;
            case 99:
                linkData.push({ from: nodeKey, to: nodeId, color: "gray", callback_num: 0 })
                break;

        }
    }

    edges.update(linkData)
    // edges = new vis.DataSet([...linkData]);
    // console.log(linkData);
    // var container = document.getElementById('mynetwork');

    // provide the data in the vis fromat
    // var data = {
    //     nodes: nodes,
    //     edges: edges
    // };

    // var options = {
    //     locale: "cn"
    // };
    // network = new vis.Network(container, data, options);
    // network.setData({
    //     nodes: nodes,
    //     edges: linkData
    // })
}

</script>

<style lang='less' scoped>
#mynetwork {
    width: 100%;
    height: 100%;

    // border: 1px solid lightgray;
}

.tip_div {
    position: absolute;
    right: 20px;
    top: 70px;
}
.tab-pane {
    margin-top: 10px;

    :deep(.el-form-item) {
        margin-bottom: 0px;
    }

    :deep(div) {
        box-sizing: border-box;
    }
}

.sucess_icon {
    width: 40px;
    height: 40px;
    background: url(/public/images/icon/green_light.png) 0 0/100% 100% no-repeat;
    // :deep(path) {
    //     fill: rgb(5, 199, 5);
    // }
}

.close_icon {
    width: 20px;
    height: 20px;
    background: url(/public/images/icon/red_light.png) 0 0/100% 100% no-repeat;
    // :deep(svg) {
    //     font-size: 30px;
    // }

    // :deep(path) {
    //     fill: rgb(226, 1, 1);
    // }
}
.el-tabs{
    height: 100%;
    :deep(.el-tabs__content){
        height: calc(100% - 40px);
    }
    .el-tab-pane{
        height: 100%;
    }
}


</style>