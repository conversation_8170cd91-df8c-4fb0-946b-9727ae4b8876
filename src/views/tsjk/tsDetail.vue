<template>
    <div>
        <el-tabs v-model="state.detailActiveName" class="tabsClass">

            <el-tab-pane label="状态信息" name="1" class="tab-pane">
                <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                    <!-- <el-descriptions-item width="150px">
                        <template #label>
                            <div class="cell-item">
                                {{ '图片' }}
                            </div>
                        </template>
<div class="image_div">
    <img src="http://**************:9080/cloudPng//%E6%B0%94%E8%B1%A1%E6%B0%B4%E6%96%87//%E6%B0%94%E8%B1%A1%E4%BA%91%E5%9B%BE//2020%E5%B9%B407%E6%9C%8806%E6%97%A5//160000//%E4%BA%A7%E5%93%81%E6%96%87%E4%BB%B6//Z_SATE_C_BAWX_20200706165054_P_FY2G_HPF_850_OTG_20200706_1600.png"
        alt="" style="max-height: 200px;">
    <img :src="props.tsData?.imgUrl" alt="" style="max-height: 200px;">
</div>
</el-descriptions-item> -->
                </el-descriptions>
                <el-descriptions class="margin-top" title="" :column="3" size="small" :border="true">
                    <!-- 优先显示经度纬度字段 -->
                    <template v-for="(value, textKey) in props.tsData">
                        <el-descriptions-item :key="'priority-' + textKey" width="150px"
                            v-if="tsDataShow(textKey) && (labelText(textKey) === '经度' || labelText(textKey) === '纬度')">
                            <template #label>
                                <div class="cell-item">
                                    {{ labelText(textKey) }}
                                </div>
                            </template>
                            <span class="cell-text">{{ value }}{{ unitText(textKey) }}</span>
                        </el-descriptions-item>
                    </template>

                    <!-- 普通字段显示，排除经度纬度和链路连接状态 -->
                    <template v-for="(value, textKey) in props.tsData">
                        <el-descriptions-item :key="'normal-' + textKey" width="150px"
                            v-if="tsDataShow(textKey) && labelText(textKey) !== '链路连接状态' && labelText(textKey) !== '经度' && labelText(textKey) !== '纬度'">
                            <template #label>
                                <div class="cell-item">
                                    {{ labelText(textKey) }}
                                </div>
                            </template>
                            <span class="cell-text">{{ value }}{{ unitText(textKey) }}</span>
                        </el-descriptions-item>
                    </template>

                    <!-- 最后显示链路连接状态字段，拆分为子项 -->
                    <template v-for="(value, textKey) in props.tsData">
                        <template
                            v-if="tsDataShow(textKey) && labelText(textKey) === '链路连接状态' && value && typeof value === 'object'">
                            <el-descriptions-item v-for="(status, linkName) in value" :key="'link-' + linkName"
                                width="150px">
                                <template #label>
                                    <div class="cell-item">
                                        {{ linkName }}
                                    </div>
                                </template>
                                <div class="link-status-cell">
                                    <div v-if="status" class="sucess_icon"></div>
                                    <div v-else class="close_icon"></div>
                                </div>
                            </el-descriptions-item>
                        </template>
                    </template>
                </el-descriptions>
            </el-tab-pane>
            <el-tab-pane label="告警信息" name="2" class="tab-pane" v-if="props.tsData.targetType == 0">
                <fish :statusObj="state.statusObj"></fish>
                <!-- <div class="noData" v-else>暂无数据</div> -->
            </el-tab-pane>
            <!-- <el-tab-pane label="平台状态" name="3" class="tab-pane" v-if="props.tsData.targetType == 0">
                <div class="cardGroup">
                    <el-card class="card_item" v-for="(group, index) of groupList" :key="index">
                        <template #header>{{ group.groupName }}</template>
                        <div>

                            <body>
                                <el-form :model="props.tsData" label-width="100px">
                                    <el-row :gutter="5">
                                        <el-col :span="12" v-for="(textKey) in group.failedList" :key="textKey">
                                            <el-form-item :label="labelText(textKey)" prop="textKey">{{
                                                props.tsData[textKey]
                                                }}</el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                                <el-descriptions class="margin-top" title="" :column="3" size="small" :border="true">
                                    <el-descriptions-item v-for="(textKey) in group.failedList || {}" :key="textKey">
                                        <template #label>
                                            <div class="cell-item">
                                                {{ labelText(textKey) }}
                                            </div>
                                        </template>
                                        <span class="cell-text">{{ props.tsData[textKey] }}</span>
                                    </el-descriptions-item>
                                </el-descriptions>
                            </body>
                        </div>
                    </el-card>
                </div>
            </el-tab-pane> -->
        </el-tabs>
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, reactive, watch } from 'vue'
// import customPopup from "@/components/customPopup.vue";
import { ElMessage } from 'element-plus';
// import * as formLabel from "./formLabel.js"
import * as formLabel2 from "./formLabel2.js"
import fish from './fish.vue';
var formLabelList = formLabel2.failedList
var showTsList = formLabel2.showTsList
var showWRTList = formLabel2.showWRTList
var showWRJList = formLabel2.showWRJList

const props = defineProps({
    tsData: {
        type: Object,

        default: () => {
            return {}
        },
    },
})
const state = reactive({
    detailActiveName: "1",//目标详情展示页面的tab
    statusObj: {},
    subDetailName: "底盘各部分状态",
    subPaneList: [{
        label: "平台机动状态",
        failed: "platformState"
    }],
})
const groupList = [
    {
        groupName: "目标基础信息",
        failedList: ['seq', "name", "mbId", "targetType"]
    },
    {
        groupName: "目标位置姿态",
        failedList: ['pitchAngle', "rollAngle", "headAngle", "longitude", "latitude", "speed", "tracking", "trackState"]
    },
    {
        groupName: "目标系统参数",
        failedList: ['rpm', "chassis", "oil", "csbDistance", "singleDistance"]
    },
    {
        groupName: "目标电池参数",
        failedList: ['voltage', "charge", "electric", "generatorCurrent"]
    },
]

onMounted(() => {

})
watch(() => props.tsData, (data) => {
    console.log('props.tsData', props.tsData.chassisMultiState);
    state.statusObj = { ...props.tsData.chassisMultiState }

}, { deep: true })
function tsDataShow(key) {
    // let row = formLabelList.find(item => item.key == key)
    let hasKey
    if (props.tsData.targetType == 1) {
        hasKey = showWRTList.includes(key)
    } else if (props.tsData.targetType == 2) {
        hasKey = showWRJList.includes(key)
    } else {
        hasKey = showTsList.includes(key)
    }
    if (!hasKey) {
        return false
    }
    if (key == "chassisMultiState" || key == 'mbId' || key == 'name' || key == 'targetType' || key == 'seq') {
        return false
    }
    else {
        return !state.subPaneList.find(item => item.failed == key)
    }
}
function labelText(key) {
    let row = formLabelList.find(item => item.key == key)
    return row ? row.label : "未定义"
}
//单位文字
function unitText(key) {
    let row = formLabelList.find(item => item.key == key)
    return row?.unit ? ' ' + row.unit : " "
}
</script>

<style lang='less' scoped>
.cell-item {
    color: aliceblue;
}

.cell-text {
    color: aliceblue;
}

.tab-pane {
    height: 48vh;
    overflow: auto;
    margin-top: 10px;
}

.sub_tabs {
    :deep(.el-tabs__item) {
        border: 0px !important;
        // text-align: left !important;
        justify-content: flex-start;
    }
}

.cardGroup {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    overflow-y: auto;
    padding: 10px 0;
    box-sizing: border-box;
    flex-wrap: wrap;
    height: 100%;

    .card_item {
        width: 48%;
        height: 45%;
        // padding: 30px;
        margin: 1%;
        box-sizing: border-box;
        background: #0222417a;

        :deep(.el-card__header) {
            background: #022141c0;
            padding: 10px 20px;
        }

        :deep(.el-form-item) {
            margin-bottom: 0;
        }

    }

}

.noData {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
}

.image_div {
    height: auto;
    width: 100%;
    text-align: center;
}

/* 链路连接状态样式 - 表格内圆点展示 */
.link-status-cell {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 100%;
}

.sucess_icon {
    width: 12px;
    height: 12px;
    background-color: #67C23A;
    border-radius: 50%;
    display: inline-block;
}

.close_icon {
    width: 12px;
    height: 12px;
    background-color: #F56C6C;
    border-radius: 50%;
    display: inline-block;
}
</style>