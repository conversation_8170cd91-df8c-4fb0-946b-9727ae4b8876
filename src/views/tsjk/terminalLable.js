
export const failedList = [
    {
        "key": "noting",
        "label": ""
    },
    // 整机状态帧
    {
        "key": "syFPGATemp",
        "label": "随遇板FPGA温度值",
        "unit": "℃"
    },
    {
        "key": "syFPGAVolt",
        "label": "随遇板FPGA电压值",
        "unit": "V"
    },
    {
        "key": "sybphpp",
        "label": "随遇板捕获频偏",
        "unit": "Hz"
    },
    {
        "key": "sybxzb",
        "label": "随遇板信噪比",
        "unit": ""
    },
    {
        "key": "sybwzs",
        "label": "随遇板误帧数/S",
        "unit": ""
    },
    {
        "key": "ckFPGATemp",
        "label": "测控板FPGA温度值",
        "unit": "℃"
    },
    {
        "key": "ckFPGAVolt",
        "label": "测控板FPGA电压值",
        "unit": "V"
    },
    {
        "key": "tddElectricCurrent",
        "label": "TDD信道状态-电流",
        "unit": "A"
    },
    {
        "key": "tddTemperature",
        "label": "TDD信道状态-温度",
        "unit": "℃"
    }, {
        "key": "tddAttenuation",
        "label": "TDD信道状态-衰减值",
        "unit": "dB"
    }, {
        "key": "tddRevPllLock",
        "label": "TDD信道状态-接收PLL锁定状态",
        isBoolean: true,

    },
    {
        "key": "tddLaunchPllLock",
        "label": "TDD信道状态-发射PLL锁定状态",
        isBoolean: true,
    },
    {
        "key": "fddElectricCurrent",
        "label": "FDD信道状态-电流",
        "unit": "A"
    },
    {
        "key": "fddTemperature",
        "label": "FDD信道状态-温度",
        "unit": "℃"
    }, {
        "key": "fddAttenuation",
        "label": "FDD信道状态-衰减值",
        "unit": "dB"

    }, {
        "key": "fddRevPllLock",
        "label": "FDD信道状态-接收PLL锁定状态",
        isBoolean: true,

    },
    {
        "key": "fddLaunchPllLock",
        "label": "FDD信道状态-发射PLL锁定状态",
        isBoolean: true,

    },
    {
        "key": "randomVersion",
        "label": "随遇板版本号",
    }, {
        "key": "controlVersion",
        "label": "测控板版本号",
    },
    {
        "key": "rsFreq",
        "label": "随遇板收发频率",
        unit: "MHz"
    }, {
        "key": "rxFreq",
        "label": "测控板接收频率",
        unit: "MHz"

    }, {
        "key": "txFreq",
        "label": "测控板发射频率",
        unit: "MHz",
    },

    // 无人机测控体制状态帧
    {
        "key": "ycCWaveStat",
        "label": "载波锁定",
        isBoolean: true,
    },
    {
        "key": "ycPNStat",
        "label": "码锁定",
        isBoolean: true,
    },
    {
        "key": "ycFrameStat",
        "label": "帧同步",
        isBoolean: true,
    },
    {
        "key": "clCWaveStat",
        "label": "载波(测量)",
        isBoolean: true,
    },
    {
        "key": "clPNStat",
        "label": "码(测量)",
        isBoolean: true,
    },
    {
        "key": "clFrameStat",
        "label": "帧同步(测量)",
        isBoolean: true,
    },
    // 航天测控体制状态帧
    {
        "key": "KP2WaveLockCl",
        "label": "载波（测量）",
        isBoolean: true,
    },
    {
        "key": "KP2CodeLockCl",
        "label": "伪码（测量）",
        isBoolean: true,
    },
    {
        "key": "KP2BitLockCl",
        "label": "位（测量）",
        isBoolean: true,
    },
    {
        "key": "KP2FrameLockCl",
        "label": "帧同步（测量）",
        isBoolean: true,
    },
    {
        "key": "KP2WaveLockYk",
        "label": "载波（遥控）",
        isBoolean: true,
    },
    {
        "key": "KP2CodeLockYk",
        "label": "伪码（遥控）",
        isBoolean: true,
    },
    {
        "key": "KP2BitLockYk",
        "label": "位（遥控）",
        isBoolean: true,
    },
    {
        "key": "KP2FrameLockYk",
        "label": "帧同步（遥控）",
        isBoolean: true,
    },

    // 抗干扰状态帧
    {
        "key": "seq",
        "label": "终端编号",
    },

    {
        "key": "grxh",
        "label": "干扰信号有无",
        isBoolean: true,
    },
    {
        "key": "grqd",
        "label": "干扰强度",
        unit: "dB"
    },
    {
        "key": "grlx",
        "label": "干扰类型",
    },
    {
        "key": "yyxhyw",
        "label": "有用信号有无",
        isBoolean: true,
    },
    {
        "key": "yyxhqd",
        "label": "有用信号强度",
        unit: "dB",
    },
    {
        "key": "kgrsdjcjg",
        "label": "抗干扰手段决策结果",
    },
    {
        "key": "kgrskqzt",
        "label": "系统级抗干扰开启状态",
        isBoolean: true,

    },
    {
        "key": "grpd",
        "label": "干扰频点",
        unit: "KHz"
    },
    {
        "key": "grdk",
        "label": "干扰带宽",
        unit: "KHz"
    },
    {
        "key": "xhpd",
        "label": "信号频点",
        unit: "KHz"
    },
    {
        "key": "xhdk",
        "label": "信号带宽",
        unit: "KHz"
    },
    {
        "key": "cglldh",
        "label": "重构链路代号",
    },
    {
        "key": "cktz",
        "label": "测控体制",
    },
    {
        "key": "xhpl",
        "label": "信号频率",
        unit: "KHz"
    },
    {
        "key": "xhdk",
        "label": "信号带宽",
        unit: "KHz"
    },
    // 建链申请数据帧
    {
        "key": "seq",
        "label": "终端编号",
    },
    {
        "key": "longitude",
        "label": "经度",
    },
    {
        "key": "latitude",
        "label": "纬度",
    },
    {
        "key": "altitude",
        "label": "高度",
        unit: "m"
    },
    {
        "key": "apply",
        "label": "建链申请",
    },
    {
        "key": "applyState",
        "label": "建链情况",
        isBoolean: true
    },
    //频谱数据帧
    {
        "key": "seq",
        "label": "终端编号",

    },
    {
        "key": "typeValue",
        "label": "频段",

    }, {
        "key": "startFrequency",
        "label": "频率起始值",

    }, {
        "key": "endFrequency",
        "label": "频率截止值",

    }, {
        "key": "interval",
        "label": "频率间隔值",
    },

    // 决策模块结果
    {
        "key": "accHandEn",
        "label": "链路切换方式",
    },
    {
        "key": "tarID",
        "label": "终端ID",
    },
    {
        "key": "nodeID",
        "label": "接入节点ID",
    },
    {
        "key": "sysAntj",
        "label": "接入节点ID",
    },
    {
        "key": "lastNode",
        "label": "需退网节点",
    },
    {
        "key": "slctEn",
        "label": "系统级决策结果使能",
    },

    //DD测控状态帧
    {
        "key": "DDPhaseLockYk",
        "label": "锁相环锁定",
        isBoolean: true
    },
    {
        "key": "DDFreqLockYk",
        "label": "锁频环锁定",
        isBoolean: true
    },
    {
        "key": "DDCodeLockYk",
        "label": "码环锁定",
        isBoolean: true
    },
    {
        "key": "DDBitLockYk",
        "label": "位同步锁定",
        isBoolean: true
    },
    {
        "key": "DDFrameLockYk",
        "label": "帧同步锁定",
        isBoolean: true
    },
    {
        "key": "dplFreqYk",
        "label": "多普勒频率",
        unit: "Hz"
    },
    {
        "key": "snrDbYk",
        "label": "信噪比",
        unit: "dB"

    },
    {
        "key": "transModState",
        "label": "下行发射状态",
    }

]

// 终端状态-简略  限制使用整机状态帧
export const showBriefList = [
    // "syFPGATemp", "syFPGAVolt", "ckFPGATemp", "ckFPGAVolt"
]

// 整机状态帧
export const wholeMachineFrame = [
    "syFPGATemp", "ckFPGATemp",
    "syFPGAVolt", "ckFPGAVolt",
    "rsFreq", "txFreq",
    "sybphpp", "rxFreq",
    "sybxzb", "controlVersion",
    "sybwzs", "randomVersion",
    "tddTemperature", "fddTemperature",
    "tddElectricCurrent", "fddElectricCurrent",
    "tddAttenuation", "fddAttenuation",
    "tddLaunchPllLock", "fddLaunchPllLock",
    "tddRevPllLock", "fddRevPllLock",
]
// 节点状态帧
export const nodeMachineFrame = [
    "syFPGATemp",
    "syFPGAVolt",
    "sybphpp",
    "sybxzb",
    "sybwzs",
    "randomVersion",
    "rsFreq",
    "tddElectricCurrent",
    "tddAttenuation",
    "tddLaunchPllLock",
    "tddRevPllLock", "tddTemperature",
]
// 无人机测控体制状态帧 "clCWaveStat", "clPNStat", "clFrameStat"
export const UAVsystemFrame = [
    "ycCWaveStat", "ycPNStat", "ycFrameStat",]
// 航天测控体制状态帧  "KP2BitLockYk", "KP2FrameLockYk",
export const spaceFlightSystemFrame = [
    "KP2WaveLockYk", "KP2CodeLockYk", "KP2BitLockYk", "KP2WaveLockCl", "KP2CodeLockCl",  "KP2BitLockCl"]
//导弹体制帧-灯
export const missileFrame = [
    "DDPhaseLockYk",
    "DDFreqLockYk",
    "DDCodeLockYk",
    // "DDBitLockYk",
    // "DDFrameLockYk"
]
//导弹体制帧-keyValue
export const missileFrameKey = [
    "dplFreqYk",
    "snrDbYk",
    "transModState",
]
// 抗干扰状态帧
export const antiInterFrame =
    ["seq", "grxh", "grqd", "grlx", "yyxhyw", "yyxhqd", "kgrsdjcjg", "kgrskqzt", "grpd", "grdk", "xhpd", "xhdk", "cglldh"]
// 建链申请数据帧
export const createLinkFrame = [
    // "seq",
    "longitude",
    "apply", "latitude",
    "applyState", "altitude",]
// 频谱数据帧 "startFrequency", "endFrequency", "interval"
export const spectrumFrame = ["seq", "typeValue"]
// 决策模块结果
export const decisionModuleResult = ["accHandEn", "tarID", "nodeID", "sysAntj", "lastNode", "slctEn"]













