

export const failedList = [

    {
        "key": "seq",
        "label": "目标序号"
    },
    {
        "key": "mbId",
        "label": "目标ID"
    },
    {
        "key": "name",
        "label": "目标名称"
    },
    {
        "key": "targetType",
        "label": "目标类型"
    },
    {
        "key": "targetTypeValue",
        "label": "目标类型"
    },
    {
        "key": "pitchAngle",
        "label": "俯仰角",
        "unit": "°"
    },
    {
        "key": "rollAngle",
        "label": "侧倾角",
        "unit": "°"
    },
    {
        "key": "headAngle",
        "label": "航向角",
        "unit": "°"
    },
    {
        "key": "longitude",
        "label": "经度",
        "unit": "°"
    },
    {
        "key": "latitude",
        "label": "纬度",
        "unit": "°"
    },
    {
        "key": "tracking",
        "label": "跟踪偏差",
        "unit": "m"
    },
    {
        "key": "speed",
        "label": "实时速度",
        "unit": "m/s"
    },
    {
        "key": "trackState",
        "label": "目标跟踪状态"
    },
    {
        "key": "singleDistance",
        "label": "单次里程",
        "unit": "m"
    },
    {
        "key": "csbDistance",
        "label": "超声波检测距离",
        "unit": "m"
    },
    {
        "key": "platformState",
        "label": "平台机动状态",
        multiple: true
    },
    {
        "key": "carSpeed",
        "label": "车速",
        "unit": "km/h"
    },
    {
        "key": "voltage",
        "label": "系统电压",
        "unit": "V"
    },
    {
        "key": "charge",
        "label": "电池Soc",
        "unit": "%"
    },
    {
        "key": "electric",
        "label": "电池电流",
        "unit": "A"
    },
    {
        "key": "generatorCurrent",
        "label": "发电机电流",
        "unit": "A"
    },
    {
        "key": "rpm",
        "label": "发动机转速",
        "unit": "rpm"
    },
    {
        "key": "chassis",
        "label": "底盘状态"
    },
    {
        "key": "oil",
        "label": "油量",
        "unit": "L"
    },

    //态势
    {
        "key": "longitude",
        "label": "经度",
        "unit": "°"
    },
    {
        "key": "latitude",
        "label": "纬度",
        "unit": "°"
    },
    {
        "key": "hy",
        "label": "横摇",
        "unit": "°"
    },
    {
        "key": "zy",
        "label": "纵摇",
        "unit": "°"
    },
    {
        "key": "sxj",
        "label": "艏相角",
        "unit": "°"
    },
    {
        "key": "sdfx",
        "label": "速度方向",
        "unit": "°"
    },
    {
        "key": "sddx",
        "label": "速度大小",
        "unit": "m/s"
    },
    {
        "key": "sj",
        "label": "时间"
    },
    {
        "key": "dj",
        "label": "舵角",
        "unit": "°"
    },
    {
        "key": "kzq",
        "label": "控制权"
    },
    {
        "key": "kzms",
        "label": "控制模式"
    },
    {
        "key": "gdmx",
        "label": "惯导模式"
    },
    // 相对高度
    {
        "key": "xdgd",
        "label": "相对高度",
        "unit": "m"
    },
    // GPS高度
    {
        "key": "gpsgd",
        "label": "GPS高度",
        "unit": "m"
    },
    // 空速
    {
        "key": "ks",
        "label": "空速",
        "unit": "m/s"
    },
    // 地速
    {
        "key": "ds",
        "label": "地速",
        "unit": "m/s"
    },
    // 俯仰
    {
        "key": "fy",
        "label": "俯仰",
        "unit": "°"
    },
    // 滚转
    {
        "key": "gz",
        "label": "滚转",
        "unit": "°"
    },
    // 偏航
    {
        "key": "ph",
        "label": "偏航",
        "unit": "°"
    },
    {
        "key": "shipLineStatus",
        "label": "链路连接状态"
    },
    {
        "key": "yccssy",
        "label": "遥测传输时延",
        "unit": "ms"
    },
    // {
    //     "key": "ycjssj",
    //     "label": "遥测接收时间"
    // }
]


export const showTsList = [
    "seq",
    "mbId",
    "name",
    "targetType",
    "targetTypeValue",
    "pitchAngle",
    "rollAngle",
    "headAngle",
    "longitude",
    "latitude",
    "tracking",
    "speed",
    "trackState",
    "singleDistance",
    "csbDistance",
    "platformState",
    "carSpeed",
    "voltage",
    "charge",
    "electric",
    "generatorCurrent",
    "rpm",
    "chassis",
    "oil"
]
export const showWRTList = [
    "longitude",
    "latitude",
    "hy",
    "zy",
    "sxj",
    "sdfx",
    "sddx",
    "sj",
    "dj",
    "kzq",
    "kzms",
    "gdmx",
    "xdgd",
    "gpsgd",
    "ks",
    "ds",
    "fy",
    "gz",
    "ph",
    "shipLineStatus"
    // "yccssy"
]
export const showWRJList = [
    "xdgd",
    "gpsgd",
    "ks",
    "ds",
    "longitude",
    "latitude",
    "fy",
    "gz",
    "ph",
    "shipLineStatus"
]
export const showList = [
    "name",
    "targetTypeValue",
    "pitchAngle",
    "rollAngle",
    "headAngle",
    "longitude",
    "latitude",
    "speed",
    "trackState",
    "carSpeed",
    "voltage",
    "rpm",
    "oil",
]
export const targetShowList = [
    "name",
    "targetTypeValue",
]