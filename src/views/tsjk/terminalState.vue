<template>
    <div> <el-tabs v-model="state.activeName" class="tabsClass" @tab-change="tabChange">
            <el-tab-pane label="整机状态" name="1" class="tab-pane">
                <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                    <el-descriptions-item v-for="(textKey) in wholeMachineFrame || []" :key="textKey" width="150px">
                        <template #label>
                            <div class="cell-item">
                                {{ labelText(textKey) }}
                            </div>
                        </template>
                        <span class="cell-text" v-if="isBoolean(textKey)">
                            <div class="sucess_icon" v-if="props.terminalDate?.machineFrame?.[textKey]"
                                style="height: 24px;width: 24px;"></div>
                            <div class="close_icon" v-else style="height: 24px;width: 24px;"></div>
                        </span>
                        <span class="cell-text" v-else>{{ props.terminalDate?.machineFrame?.[textKey] || 0 }}{{
                            unitText(textKey) }}</span>
                    </el-descriptions-item>
                </el-descriptions>
            </el-tab-pane>
            <!-- <el-tab-pane label="测控体制状态帧" name="2" class="tab-pane">
                <div class="light_box">
                    <div :span="6" v-for="textKey in UAVsystemFrame" :key="textKey" class="icon_box">
                        <el-row style="width: 100%;">
                            <el-col :span="5"></el-col>
                            <el-col :span="5" style="line-height: 40px;"> {{ labelText(textKey) }}
                            </el-col>
                            <el-col :span="4"></el-col>
                            <el-col :span="10">
                                <span class="cell-text" v-if="isBoolean(textKey)">
                                    <div class="sucess_icon" v-if="props.terminalDate?.droneFrame?.[textKey]"
                                        style="height: 40px;width: 40px;"></div>
                                    <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>
                                </span>
                            </el-col>
                        </el-row>
                    </div>
                </div>

                <div class="light_box" v-if="props.terminalDate?.spaceFrame">
                    <div v-for="textKey in spaceFlightSystemFrame" :key="textKey" class="icon_box">
                        <el-row style="width: 100%;">
                            <el-col :span="5"></el-col>
                            <el-col :span="5" style="line-height: 40px;"> {{ labelText(textKey) }}
                            </el-col>
                            <el-col :span="4"></el-col>
                            <el-col :span="10">
                                <span class="cell-text" v-if="isBoolean(textKey)">
                                    <div class="sucess_icon" v-if="props.terminalDate?.spaceFrame?.[textKey]"
                                        style="height: 40px;width: 40px;"></div>
                                    <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>

                                </span>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                <div v-if="props.terminalDate?.missileFrame" style="height: 50px;margin-top: 20px;">
                    <el-descriptions class="margin-top" title="" :column="3" size="small" :border="true">
                        <el-descriptions-item v-for="(textKey) in missileFrameKey || []" :key="textKey" width="150px">
                            <template #label>
                                <div class="cell-item">
                                    {{ labelText(textKey) }}
                                </div>
                            </template>
                            <span class="cell-text">{{ props.terminalDate?.missileFrame?.[textKey] || 0 }}{{
                                unitText(textKey) }}</span>
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
                <div class="light_box" v-if="props.terminalDate?.missileFrame" style="height: calc(100% - 70px);">
                    <div v-for="textKey in missileFrame" :key="textKey" class="icon_box" style="height: 30%;">
                        <el-row style="width: 100%;">
                            <el-col :span="5"></el-col>
                            <el-col :span="5" style="line-height: 40px;"> {{ labelText(textKey) }}
                            </el-col>
                            <el-col :span="4"></el-col>
                            <el-col :span="10">
                                <span class="cell-text" v-if="isBoolean(textKey)">
                                    <div class="sucess_icon" v-if="props.terminalDate?.missileFrame?.[textKey]"
                                        style="height: 40px;width: 40px;"></div>
                                    <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>

                                </span>
                            </el-col>
                        </el-row>
                    </div>
                </div>

            </el-tab-pane> -->
            <el-tab-pane label="链路状态" name="3" class="tab-pane">
                <el-row style="height: 99%;">
                    <el-col :span="12" style="border: 1px solid #1d436d;height: 100%;">

                        <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                            <el-descriptions-item width="150px">
                                <template #label>
                                    <div class="cell-item">当前链路体制</div>
                                </template>
                                <span class="cell-text"> {{ modeCtrlList[props.terminalDate.terCurrentMode] || "暂无"
                                }}</span>
                            </el-descriptions-item>
                        </el-descriptions>

                        <!-- 根据数据的不同，调用不同的状态帧字段（TODO） v-if="props.terminalDate?.droneFrame" -->
                        <div style="    height: 50px;
    background: #1d436d69;
    line-height: 50px;
    font-size: 15px;margin-bottom: 30px;">上行链路锁定状态 </div>

                        <!-- 航空数据帧 -->
                        <!--  -->
                        <div class="light_box" v-if="props.terminalDate?.spaceFrame">
                            <div :span="6" v-for="textKey in spaceFlightSystemFrame" :key="textKey" class="icon_box">
                                <div>
                                    <span> {{ labelText(textKey) }}</span>
                                    <span class="cell-text" v-if="isBoolean(textKey)">
                                        <div class="sucess_icon" v-if="props.terminalDate?.spaceFrame?.[textKey]"
                                            style="height: 40px;width: 40px;"></div>
                                        <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>
                                    </span>

                                </div>
                            </div>
                            <!-- <div v-for="textKey in spaceFlightSystemFrame" :key="textKey" class="icon_box">
                                <el-row style="width: 100%;">
                                    <el-col :span="1"></el-col>
                                    <el-col :span="12" style="line-height: 40px;"> {{ labelText(textKey) }}
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="10">
                                        <span class="cell-text" v-if="isBoolean(textKey)">
                                            <div class="sucess_icon" v-if="props.terminalDate?.spaceFrame?.[textKey]"
                                                style="height: 40px;width: 40px;"></div>
                                            <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>

                                        </span>
                                    </el-col>
                                </el-row>
                            </div> -->
                        </div>
                        <!-- 导弹测控数据帧-灯 -->
                        <div class="light_box" v-else-if="props.terminalDate?.missileFrame">
                            <div :span="6" v-for="textKey in missileFrame" :key="textKey" class="icon_box">
                                <div>
                                    <span> {{ labelText(textKey) }}</span>
                                    <span class="cell-text" v-if="isBoolean(textKey)">
                                        <div class="sucess_icon" v-if="props.terminalDate?.missileFrame?.[textKey]"
                                            style="height: 40px;width: 40px;"></div>
                                        <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>
                                    </span>

                                </div>
                            </div>
                            <!-- <div v-for="textKey in missileFrame" :key="textKey" class="icon_box" style="height: 30%;">
                                <el-row style="width: 100%;">
                                    <el-col :span="5"></el-col>
                                    <el-col :span="5" style="line-height: 40px;"> {{ labelText(textKey) }}
                                    </el-col>
                                    <el-col :span="4"></el-col>
                                    <el-col :span="10">
                                        <span class="cell-text" v-if="isBoolean(textKey)">
                                            <div class="sucess_icon" v-if="props.terminalDate?.missileFrame?.[textKey]"
                                                style="height: 40px;width: 40px;"></div>
                                            <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>
                                        </span>
                                    </el-col>
                                </el-row>
                            </div> -->
                        </div>
                        <div class="light_box" v-else>
                            <div :span="6" v-for="textKey in UAVsystemFrame" :key="textKey" class="icon_box">
                                <div>
                                    <span> {{ labelText(textKey) }}</span>
                                    <span class="cell-text" v-if="isBoolean(textKey)">
                                        <div class="sucess_icon" v-if="props.terminalDate?.droneFrame?.[textKey]"
                                            style="height: 40px;width: 40px;"></div>
                                        <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>
                                    </span>

                                </div>
                            </div>
                        </div>
                        <!-- 导弹测控数据帧-文字   v-if="props.terminalDate?.missileFrame" -->
                        <!-- <div style="height: 80px;margin-top: 20px;">
                                <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                                    <el-descriptions-item v-for="(textKey) in missileFrameKey || []" :key="textKey"
                                        width="150px">
                                        <template #label>
                                            <div class="cell-item">
                                                {{ labelText(textKey) }}
                                            </div>
                                        </template>
                                        <span class="cell-text">{{ props.terminalDate?.missileFrame?.[textKey] || 0 }}{{
                                            unitText(textKey) }}</span>
                                    </el-descriptions-item>
                                </el-descriptions>
                            </div> -->

                        <div style="    height: 50px;
    background: #1d436d69;
    line-height: 50px;
    font-size: 15px;">实时链路参数 </div>
                        <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                            <el-descriptions-item width="150px">
                                <template #label>
                                    <div class="cell-item">发射频率 </div>
                                </template>
                                <span class="cell-text"> {{ spectrumText_bl('txFreq') ? spectrumText_bl('txFreq') +
                                    'MHz' :
                                    "-"
                                }}</span>
                            </el-descriptions-item>
                            <el-descriptions-item width="150px">
                                <template #label>
                                    <div class="cell-item">接收频率 </div>
                                </template>
                                <span class="cell-text"> {{ spectrumText_bl('rxFreq') ? spectrumText_bl('rxFreq') +
                                    'MHz'
                                    : "-"
                                }}</span>
                            </el-descriptions-item>
                            <el-descriptions-item width="150px">
                                <template #label>
                                    <div class="cell-item">信号功率 </div>
                                </template>
                                <span class="cell-text"> {{ spectrumText_bl('grxh') ? '有' : "无" }}</span>
                            </el-descriptions-item>
                            <el-descriptions-item width="150px">
                                <template #label>
                                    <div class="cell-item">抗干扰手段 </div>
                                </template>
                                <span class="cell-text"> {{ spectrumText_gl('kgrsdjcjg') ?
                                    kgrsdList[spectrumText_gl('kgrsdjcjg')] :
                                    "无" }}</span>
                            </el-descriptions-item>
                            <el-descriptions-item width="150px">
                                <template #label>
                                    <div class="cell-item">当前接入节点 </div>
                                </template>
                                <span class="cell-text"> {{ spectrumText_jr('curStationName') ?
                                    spectrumText_jr('curStationName') :
                                    "未接入" }}</span>
                            </el-descriptions-item>
                            <template>
                                <!-- v-if="props.terminalDate?.missileFrame" -->
                                <el-descriptions-item v-for="(textKey) in missileFrameKey || []" :key="textKey"
                                    width="150px" v-if="props.terminalDate?.missileFrame">
                                    <template #label>
                                        <div class="cell-item">
                                            {{ labelText(textKey) }}
                                        </div>
                                    </template>
                                    <span class="cell-text">{{ props.terminalDate?.missileFrame?.[textKey] || 0 }}{{
                                        unitText(textKey) }}</span>
                                </el-descriptions-item>
                            </template>

                        </el-descriptions>
<!--						{{props.logList}}-->
                        <!-- <el-row style="width: 100%;height: 20%;">
                                <el-col :span="12">
                                    <span class="lable_bg">实时链路参数</span>
                                    <span class="content_bg">
                                        {{ spectrumText_bl('grxh') ? '有' : "无" }}
                                    </span>
                                </el-col>
                                <el-col :span="12">
                                    <span class="lable_bg">测控板发射(接收频率)</span>
                                    <span class="content_bg">
                                        {{ spectrumText_bl('grxh') ? '有' : "无" }}
                                    </span>
                                </el-col>
                                <el-col :span="12">
                                    <span class="lable_bg">信号功率</span>
                                    <span class="content_bg">
                                        {{ spectrumText_bl('grxh') ? '有' : "无" }}
                                    </span>
                                </el-col>
                                <el-col :span="12">
                                    <span class="lable_bg">抗干扰手段</span>
                                    <span class="content_bg">
                                        {{ spectrumText_bl('grxh') ? '有' : "无" }}
                                    </span>
                                </el-col>
                                <el-col :span="12">
                                    <span class="lable_bg">当前接入节点</span>
                                    <span class="content_bg">
                                        {{ spectrumText_bl('grxh') ? '有' : "无" }}
                                    </span>
                                </el-col>
                            </el-row> -->
                    </el-col>
                    <el-col :span="12" style="height: 100%;padding-left: 5px;">
                        <div style="border: 1px solid #1d436d; height: calc(100% );margin-bottom: 5px;">
                            <div class="subTitle">
                                频谱感知结果
                            </div>
                            <div style="height: calc(70%);width: 100%;">
                                <!-- 频谱图 -->
                                <!-- <div class="subTitle">
                                        频谱图
                                        <span class="sub_text">{{ props.terminalDate?.spectrumFrame?.typeValue || '--频段'
                                            }}</span>
                                    </div> -->
                                <div style="height: 92%;width: 100%;" id="spectrumChart">
                                </div>
                            </div>
                            <!-- <el-row style="width: 100%;height: 20%;">
                                <el-col :span="8">
                                    <span class="lable_bg">干扰有无</span>
                                    <span class="content_bg">
                                        {{ spectrumText_bl('grxh') ? '有' : "无" }}
                                    </span>
                                </el-col>
                                <el-col :span="8">
                                    <span class="lable_bg">干扰频点</span>
                                    <span class="content_bg">{{ spectrumText('grpd') / 1000 || '--' }}MHz</span>
                                </el-col>
                                <el-col :span="8">
                                    <span class="lable_bg">干扰带宽</span>
                                    <span class="content_bg">

                                        {{ spectrumText('grdk') / 1000 || '--' }}MHz

                                    </span>
                                </el-col>
                                <el-col :span="8">
                                    <span class="lable_bg">干扰强度</span>
                                    <span class="content_bg">{{ spectrumText('grqd') || '--' }}dB</span>
                                </el-col>
                                <el-col :span="8">
                                    <span class="lable_bg">干扰类型</span>
                                    <span class="content_bg">{{ spectrumText('grlx') ?
                                        grlxList[spectrumText('grlx')] : '--'
                                        }}</span>
                                </el-col>
                            </el-row> -->
                            <el-descriptions class="margin-top" title="" :column="3" size="small" :border="true"
                                style="margin-bottom: 30px;">
                                <el-descriptions-item width="150px">
                                    <template #label>
                                        <div class="cell-item">干扰有无 </div>
                                    </template>
                                    <span class="cell-text"> {{ spectrumText_bl('grxh') ? '有' : "无" }}</span>
                                </el-descriptions-item>
                                <el-descriptions-item width="150px">
                                    <template #label>
                                        <div class="cell-item">干扰频点 </div>
                                    </template>
                                    <span class="cell-text"> {{ spectrumText('grpd') / 1000 || '--' }}MHz</span>
                                </el-descriptions-item>
                                <el-descriptions-item width="150px">
                                    <template #label>
                                        <div class="cell-item">干扰带宽 </div>
                                    </template>
                                    <span class="cell-text"> {{ spectrumText('grdk') / 1000 || '--' }}MHz</span>
                                </el-descriptions-item>
                                <el-descriptions-item width="150px">
                                    <template #label>
                                        <div class="cell-item">干扰强度 </div>
                                    </template>
                                    <span class="cell-text"> {{ spectrumText('grqd') || '--' }}dB</span>
                                </el-descriptions-item>
                                <el-descriptions-item width="150px">
                                    <template #label>
                                        <div class="cell-item">干扰类型 </div>
                                    </template>
                                    <span class="cell-text">{{ spectrumText('grlx') ?
                                        grlxList[spectrumText('grlx')] : '--'
                                    }}</span>
                                </el-descriptions-item>

                            </el-descriptions>
                        </div>
                        <!-- <div style="border: 1px solid #1d436d; height: calc(30%);margin-bottom: 5px;">
                            <div class="subTitle">
                                频谱感知
                            </div>
                            <div class="spectrum_perception"  style="height: calc(100% - 50px);width: 100%;align-items: flex-start;">
                                <el-row style="width: 100%;">
                                    <el-col :span="8">
                                        <span class="lable_bg">干扰有无</span>
                                        <span class="content_bg">
                                            {{ spectrumText_bl('grxh') ? '有' : "无" }}
                                        </span>
                                    </el-col>
                                    <el-col :span="8">
                                        <span class="lable_bg">干扰频点</span>
                                        <span class="content_bg">{{ spectrumText('grpd') / 1000 || '--' }}MHz</span>
                                    </el-col>
                                    <el-col :span="8">
                                        <span class="lable_bg">干扰带宽</span>
                                        <span class="content_bg">
                                            {{ spectrumText('grdk') / 1000 || '--' }}MHz

                                        </span>
                                    </el-col>
                                    <el-col :span="8">
                                        <span class="lable_bg">干扰强度</span>
                                        <span class="content_bg">{{ spectrumText('grqd') || '--' }}dB</span>
                                    </el-col>
                                    <el-col :span="8">
                                        <span class="lable_bg">干扰类型</span>
                                        <span class="content_bg">{{ spectrumText('grlx') ?
                                            grlxList[spectrumText('grlx')] : '--'
                                            }}</span>
                                    </el-col>
                                </el-row>
                            </div>
                        </div>
                        <div style="border: 1px solid #1d436d; height: calc(30%);margin-bottom: 5px;">
                            <div class="subTitle">
                                系统弹性抗干扰决策结果
                            </div>
                            <div class="spectrum_perception"  style="height: calc(100% - 50px);width: 100%;align-items: flex-start;">
                                <el-row style="width: 100%;">
                                    <el-col :span="8">
                                        <span class="lable_bg">开启状态</span>
                                        <span class="content_bg">{{ props.terminalDate.sysAntJResult?.sysAntj ? '开启' :
                                            '关闭'
                                            }}</span>
                                    </el-col>
                                    <el-col :span="8">
                                        <span class="lable_bg">测控体制</span>
                                        <span class="content_bg">{{
                                            modeList[props.terminalDate?.terminalConfigResult?.mode] || '--' }}</span>
                                    </el-col>
                                    <el-col :span="8">
                                        <span class="lable_bg">上行频率</span>
                                        <span class="content_bg">{{
                                            TCRText('txFreq') || '--' }}MHz</span>
                                    </el-col>
                                    <el-col :span="8">
                                        <span class="lable_bg">下行频率</span>
                                        <span class="content_bg">{{
                                            TCRText('rxFreq') || '--' }}MHz</span>
                                    </el-col>

                                </el-row>
                            </div>
                        </div> -->

                    </el-col>
                </el-row>




                <!-- <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                    <el-descriptions-item v-for="(textKey) in antiInterFrame || []" :key="textKey + 'antiInterFrame'"
                        width="150px">
                        <template #label>
                            <div class="cell-item">
                                {{ labelText(textKey) }}
                            </div>
                        </template>
                        <span class="cell-text" v-if="isBoolean(textKey)">
                            <el-icon v-if="props.terminalDate?.antiJammingFrame?.[textKey]" size="16px"
                                class="sucess_icon">
                                <CircleCheckFilled />
                            </el-icon>
                            <el-icon v-else size="16px" class="close_icon">
                                <CircleCloseFilled />
                            </el-icon>
                        </span>
                        <span class="cell-text" v-else>{{ props.terminalDate?.antiJammingFrame?.[textKey] || 0 }}</span>
                    </el-descriptions-item>
                </el-descriptions> -->
            </el-tab-pane>
            <!-- <el-tab-pane label="频谱数据帧" name="4" class="tab-pane">
                <el-form :model="props.terminalDate?.spectrumFrame" label-width="130px"
                    style="width: 100%;height: 50px">
                    <el-row style="width: 100%;">
                        <el-col :span="8" v-for="(textKey) in spectrumFrame || []" :key="textKey + 'spectrumFrame'">
                            <el-form-item :label="labelText(textKey)">
                                {{ props.terminalDate?.spectrumFrame?.[textKey] || 0 }}
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div style="height: calc(100% - 50px);width: 100%;" id="spectrumChart"></div>

            </el-tab-pane> -->
            <!-- <el-tab-pane label="建链申请数据帧" name="5" class="tab-pane">
                <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                    <el-descriptions-item v-for="(textKey) in createLinkFrame || []" :key="textKey + 'createLinkFrame'"
                        width="150px">
                        <template #label>
                            <div class="cell-item">
                                {{ labelText(textKey) }}
                            </div>
                        </template>
                        <span class="cell-text" v-if="isBoolean(textKey)">
                            <div class="sucess_icon" v-if="props.terminalDate?.createLinkDataFrame?.[textKey]"
                                style="height: 24px;width: 24px;"></div>
                            <div class="close_icon" v-else style="height: 24px;width: 24px;"></div>
                        </span>
                        <span class="cell-text" v-else>{{ props.terminalDate?.createLinkDataFrame?.[textKey] || 0 }}
                            {{ unitText(textKey)
                            }}</span>
                    </el-descriptions-item>
                </el-descriptions>
            </el-tab-pane>
            <el-tab-pane label="决策模块结果" name="6" class="tab-pane">
                <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                    <el-descriptions-item width="25%">
                        <template #label>
                            <div class="cell-item">
                                是否启用接入模块
                            </div>
                        </template>
                        <el-switch v-model="isEnableModel" @change="changeEnableModel" inline-prompt active-text="启用"
                            inactive-text="禁用" />
                    </el-descriptions-item>
                    <el-descriptions-item v-for="(textKey) in decisionModuleResult || []" :key="textKey" width="25%">
                        <template #label>
                            <div class="cell-item">
                                {{ labelText(textKey) }}
                            </div>
                        </template>
                        <span class="cell-text" v-if="isBoolean(textKey)">
                            <div class="sucess_icon" v-if="props.terminalDate?.sysAntJResult?.[textKey]"
                                style="height: 24px;width: 24px;"></div>
                            <div class="close_icon" v-else style="height: 24px;width: 24px;"></div>
                        </span>
                        <span class="cell-text" v-else>{{ props.terminalDate?.sysAntJResult?.[textKey] || 0 }}{{
                            unitText(textKey) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item width="25%">
                        <template #label>
                            <div class="cell-item">
                                需切换的体制
                            </div>
                        </template>
                        <span class="cell-text">{{ modeCtrlObj[props.terminalDate?.sysAntJResult?.terMode] || ''
                            }}</span>
                    </el-descriptions-item>
                </el-descriptions>
            </el-tab-pane> -->
        </el-tabs></div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, reactive, watch } from 'vue'
import * as terminalLable from "./terminalLable.js"
import * as controlApi from "@/service/API/system/control.js";
import { ElMessage } from 'element-plus';



const terminalList = terminalLable.failedList
const wholeMachineFrame = terminalLable.wholeMachineFrame// 整机状态帧
const UAVsystemFrame = terminalLable.UAVsystemFrame// 无人机测控体制状态帧
const spaceFlightSystemFrame = terminalLable.spaceFlightSystemFrame// 航天测控体制状态帧
const missileFrame = terminalLable.missileFrame// DD测控体制状态帧-灯
const missileFrameKey = terminalLable.missileFrameKey// DD测控体制状态帧keyvalue
const antiInterFrame = terminalLable.antiInterFrame// 抗干扰状态帧
const createLinkFrame = terminalLable.createLinkFrame// 建链申请数据帧
const spectrumFrame = terminalLable.spectrumFrame// 频谱数据帧
const decisionModuleResult = terminalLable.decisionModuleResult// 频谱数据帧
var terminalBriefList = terminalLable.showBriefList //终端字段（简略显示列表）
const grlxList = ["无干扰", '单音', '多音', '部分带', '脉冲', '扫频']
const kgrsdList = ["关闭抗干扰", ' ', '体制级抗干扰', ' ', '信号级抗干扰', '系统级抗干扰']
const modeList = ['不切换体制', '航天测控体制', '无人机测控体制', '导弹测控体制', '频谱感知']
const modeCtrlList = {
    '0': "不切换体制",
    '1': "扩频测控模式二-S",
    '2': "扩频测控模式二-Ka",
    '3': "扩频四合一综合测控体制",
    '4': "常规导弹测控体制",
}
const isEnableModel = ref(false) // 是否启用接入模块
// 体制
const modeCtrlObj = reactive({
    "0": "不切换体制",
    "1": "扩频测控模式二-S1",
    "2": "扩频测控模式二-S2",
    "3": "扩频测控模式二-S3",
    "4": "扩频测控模式二-S4",
    "5": "扩频测控模式二-Ka1",
    "6": "扩频测控模式二-Ka2",
    "7": "扩频测控模式二-Ka3",
    "8": "扩频测控模式二-Ka4",
    "9": "扩频四合一综合测控体制",
    "10:": "常规导弹测控体制",
})
const props = defineProps({
    terminalDate: {
        type: Object,

        default: () => {
            return {}
        },
    },
	logList: {
		type: Array,
		default: () => []
	}
})

watch(() => props.terminalDate, (val) => {
    console.log('terminalDate', val);
    upDateChart()
}
)
const state = reactive({
    activeName: "1",
    spectrumData: [],
    sendForm: {
        decisionMethod: undefined,
        counterInterferenceMeasures: undefined,
        targetNode: undefined,
        switchLink: 0,
        switchLinkCode: 0
    },
    chooseTargetForm: {}
})
const targetDataObj = ref({})
var myChart
onMounted(() => {
    chartInit()
})
// 获取是否启用接入模块
function getEnableModel() {
    controlApi.getTerminalNode().then((res) => {
        isEnableModel.value = res.data.data == 1
    })
}
function changeEnableModel() {
    controlApi.setTerminalNode().then((res) => {
        if (res.data.code == 200) {
            ElMessage.success('修改成功')
        } else {
            ElMessage.error(res.data.message)
        }
        getEnableModel()
    })
}
function chartInit() {
    var chartDom = document.getElementById('spectrumChart');
    myChart = echarts.init(chartDom);
    var option;
    option = {
        locale: "zh_CN",
        // title: {
        //     // text: '频谱数据帧',
        //     subtext: '--频段',
        //     // textStyle: {
        //     //     color: "#fff"
        //     // },
        // },
        tooltip: {
            trigger: 'axis'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        // toolbox: {
        //     right: 10,
        //     feature: {
        //         dataZoom: {
        //             yAxisIndex: 'none'
        //         },
        //         restore: {},
        //         saveAsImage: {}
        //     }
        // },
        dataZoom: [{
            type: 'inside'
        }],
        xAxis: {
            type: 'category',
            // splitLine: {
            //     lineStyle: {
            //         color: ['#d3d3d32b', '#ddd']
            //     }
            // },
            axisLabel: {
                formatter: '{value} MHz',
                color: "#fff"
            },

            data: []
        },
        yAxis: {
            type: 'value',
            splitLine: {
                // show:false,//取消间隔线
                lineStyle: {
                    // 间隔线颜色
                    color: ['#d3d3d32b']
                }
            },
            axisLabel: {
                formatter: '{value} dB',
                color: "#fff"
            },
        },
        series: [
            {
                type: 'line',
                data: [],
                lineStyle: {
                    // 1bd1c8
                    //频谱线的颜色
                    color: "#1bd1c8"
                }
            },
        ]
    };

    myChart.setOption(option);
}
function upDateChart() {
    if (!myChart || !props.terminalDate.spectrumFrame) {
        return
    }
    let spectrumData = [...props.terminalDate.spectrumFrame.spectrumList]
    let interval = props.terminalDate.spectrumFrame.interval
    let startFrequency = props.terminalDate.spectrumFrame.startFrequency
    // let subTitle = props.terminalDate.spectrumFrame.subtext

    let option = {

        xAxis: {
            data: []
        },
        series: [
            {
                data: []
            }
        ]
    }
    spectrumData.forEach((dataItem, index) => {
        option.xAxis.data.push(startFrequency + index * interval)
        option.series[0].data.push(dataItem)
    });
    myChart.setOption(option)

}
// key转文字
function labelText(key) {
    let row = terminalList.find(item => item.key == key)
    return row ? row.label : "未定义"
}
//单位文字
function unitText(key) {
    let row = terminalList.find(item => item.key == key)
    return row?.unit ? ' ' + row.unit : " "
}


function isBoolean(key) {
    let row = terminalList.find(item => item.key == key)
    return row.isBoolean || false
}
function tabChange() {
    setTimeout(() => {
        myChart.resize()
    }, 200);
    if (state.activeName == 6) {
        getEnableModel()
    }
}
function spectrumText(key) {
    let t = props.terminalDate?.antiJammingFrame?.[key]
    if (t === 0) {
        t = "0"
    }
    return t
}
function TCRText(key) {
    let t = props.terminalDate?.terminalConfigResult?.[key]
    if (t === 0) {
        t = "0"
    }
    return t
}
function spectrumText_bl(key) {
    return props.terminalDate?.machineFrame?.[key]
}
function spectrumText_gl(key) {
    return String(props.terminalDate?.antiJammingFrame?.[key])
}
function spectrumText_jr(key) {
    return props.terminalDate&&props.terminalDate?.[key] ? String(props.terminalDate?.[key]) : '未接入'
}
function sendAntiJamming() {
    state.sendForm.targetNode = props.terminalDate.nodeId
    controlApi.antiJamming(state.sendForm).then(res => {
        if (res.data.code == 200) {
            ElMessage.success("发送成功")
        } else {
            ElMessage.error("发送失败")
        }
    })
}
</script>

<style scoped lang="less">
.tab-pane {
    height: 57vh;
    overflow: auto;
    margin-top: 10px;

    :deep(.el-form-item) {
        margin-bottom: 0px;
    }

    :deep(div) {
        box-sizing: border-box;
    }
}

.sucess_icon {
    width: 40px;
    height: 40px;
    background: url(/public/images/icon/green_light.png) 0 0/100% 100% no-repeat;
    // :deep(path) {
    //     fill: rgb(5, 199, 5);
    // }
}

.close_icon {
    width: 20px;
    height: 20px;
    background: url(/public/images/icon/red_light.png) 0 0/100% 100% no-repeat;
    // :deep(svg) {
    //     font-size: 30px;
    // }

    // :deep(path) {
    //     fill: rgb(226, 1, 1);
    // }
}

.icon_box {
    height: 25%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    box-sizing: border-box;
    padding: 5px;
    flex-direction: column;
    width: 33%;
}

.subTitle {
    width: 100%;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 18px;
    font-weight: bold;
    position: relative;

    .sub_text {
        font-size: 12px;
        font-weight: 100;
        position: absolute;
        right: 20px;
    }
}

.spectrum_perception {
    height: calc(100% - 50px);
    width: 100%;
    padding: 0 20px 30px 20px;
    display: flex;
    align-items: flex-end;
    overflow-y: auto;
}

.spectrum_decision {
    height: calc(100% - 50px);
    width: 100%;
    padding: 0 20px 40px 20px;
    display: flex;
    align-items: flex-end;
    overflow-y: auto;
    flex-wrap: wrap;

    :deep(.el-select) {
        height: 25px;
        vertical-align: bottom;
        width: 100%;
    }

    :deep(.el-select__wrapper) {
        height: 100%;
        min-height: unset !important;
        padding: 0 !important;
    }
}

.lable_bg {
    background: rgba(0, 174, 255, 0.4);
    border: 2px solid #003a66;
    display: inline-block;
    width: 48%;
    line-height: 25px;

    text-align: center;

}

.content_bg {
    // background: rgba(0, 174, 255, 0);
    border: 2px solid #003a66;
    display: inline-block;
    width: 48%;
    line-height: 25px;

    padding: 0 5px;
    box-sizing: border-box;

}

.row_content {
    border: 3px solid #003a66;
    width: 100%;
}

.light_box {
    width: 100%;
    height: 150px;
    display: flex;
    // flex-direction: column;
    flex-wrap: wrap;
}
</style>
