# Panorama4.vue 性能优化指南

## 问题分析

经过代码分析，发现以下导致页面卡顿的主要问题：

### 1. WebSocket 数据处理问题
- **问题**：频繁的 WebSocket 消息导致大量 DOM 更新
- **影响**：每次数据更新都会触发响应式系统重新渲染
- **解决方案**：实现批量更新和防抖机制

### 2. 滚动监听性能问题
- **问题**：复杂的滚动监听逻辑，多个 watch 监听同一数据源
- **影响**：频繁的 DOM 查询和操作
- **解决方案**：简化滚动逻辑，添加防抖处理

### 3. 内存泄漏问题
- **问题**：定时器未正确清理，大量数据累积
- **影响**：内存持续增长，最终导致页面卡顿
- **解决方案**：完善清理机制，限制数据量

### 4. 大数据渲染问题
- **问题**：表格和日志容器渲染大量数据
- **影响**：DOM 节点过多，渲染性能下降
- **解决方案**：实现虚拟滚动或分页显示

## 优化措施

### 1. WebSocket 数据处理优化

```javascript
// 批量更新机制
let dataUpdateTimer = null
let pendingUpdates = {
    targets: new Map(),
    resources: new Map(),
    terminals: new Map()
}

// 防抖的数据更新
const debouncedDataUpdate = () => {
    if (dataUpdateTimer) {
        clearTimeout(dataUpdateTimer)
    }
    dataUpdateTimer = setTimeout(() => {
        flushPendingUpdates()
        dataUpdateTimer = null
    }, 50) // 50ms防抖
}
```

### 2. 滚动监听优化

```javascript
// 简化的滚动监听
let wasAtBottom = true
let scrollUpdateTimer = null

const debouncedScrollUpdate = () => {
    if (scrollUpdateTimer) {
        clearTimeout(scrollUpdateTimer)
    }
    scrollUpdateTimer = setTimeout(() => {
        if (logContainer.value && wasAtBottom) {
            logContainer.value.scrollTop = logContainer.value.scrollHeight
        }
        scrollUpdateTimer = null
    }, 16) // 约60fps的更新频率
}
```

### 3. 内存管理优化

```javascript
// 数据量限制
if (state.targetList.length > 1000) {
    state.targetList = state.targetList.slice(-800) // 保留最新的800个
}

// 航迹点数量限制
const targetTrack = filteredTrack
    .slice(-50) // 最多保留50个航迹点
    .concat([{longitude, latitude, altitude, time: now}])

// 组件卸载时的完整清理
onUnmounted(() => {
    // 清理所有定时器
    if (dataUpdateTimer) clearTimeout(dataUpdateTimer)
    if (scrollUpdateTimer) clearTimeout(scrollUpdateTimer)
    if (terminalStatusTimer) clearTimeout(terminalStatusTimer)
    if (flowLineUpdateTimer) clearTimeout(flowLineUpdateTimer)
    
    // 清理数据结构
    pendingUpdates.targets.clear()
    pendingUpdates.resources.clear()
    pendingUpdates.terminals.clear()
    
    // 清理图表实例
    if (Chart2.value) echarts.getInstanceByDom(Chart2.value)?.dispose()
    if (Chart4.value) echarts.getInstanceByDom(Chart4.value)?.dispose()
})
```

### 4. 大数据处理优化

```javascript
// 表格数据分页显示
:data="state.targetList.slice(0, 100)"

// 日志数据限制
const filteredLogs = computed(() => {
    // 限制显示的日志数量，只显示最新的200条
    return logs.slice(-200)
})
```

## 性能监控建议

### 1. 添加性能监控
```javascript
// 监控数据更新频率
let updateCount = 0
const monitorUpdates = () => {
    updateCount++
    if (updateCount % 100 === 0) {
        console.log(`数据更新次数: ${updateCount}`)
    }
}
```

### 2. 内存使用监控
```javascript
// 定期检查内存使用
setInterval(() => {
    if (performance.memory) {
        console.log('内存使用:', {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
        })
    }
}, 30000) // 每30秒检查一次
```

## 使用建议

### 1. 开发环境
- 使用 Vue DevTools 监控组件性能
- 定期检查内存使用情况
- 监控 WebSocket 消息频率

### 2. 生产环境
- 启用生产模式构建
- 考虑使用 Web Workers 处理大量数据
- 实现错误边界和降级策略

### 3. 长期维护
- 定期清理无用代码
- 监控页面性能指标
- 根据实际使用情况调整数据限制参数

## 预期效果

通过以上优化措施，预期可以达到：

1. **减少内存使用**：限制数据量，防止内存无限增长
2. **提高响应速度**：批量更新减少频繁的 DOM 操作
3. **改善用户体验**：页面不再出现长时间卡顿
4. **增强稳定性**：完善的清理机制防止内存泄漏

## 注意事项

1. 数据限制参数需要根据实际业务需求调整
2. 批量更新的延迟时间需要平衡性能和实时性
3. 定期监控优化效果，必要时进一步调整
