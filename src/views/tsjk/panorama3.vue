<template>
  <CesiumBox></CesiumBox>
  <!-- <el-radio-group v-model="state.pageRadio" class="pageRadio" @change="pageRadioChange">
        <el-radio-button label="1">全景态势</el-radio-button>
        <el-radio-button label="2">任务态势 </el-radio-button>
        <el-radio-button label="3">资源态势</el-radio-button>
        <el-radio-button label="4">目标态势</el-radio-button>
    </el-radio-group> -->
  <div class="showOrHidden-box" :style="{ right: state.pageRadio == 1 ? '500px' : '16%' }">
    <template v-if="state.pageRadio != 1">
      <!-- <div>
                <label>航迹显隐：</label>
                <el-switch
                    v-model="state.showTrack"
                    inline-prompt
                    active-text="显示"
                    inactive-text="隐藏"
                />
            </div> -->
      <div>
        <label>范围显隐：</label>
        <el-switch v-model="state.showArea" inline-prompt active-text="显示" inactive-text="隐藏" />
      </div>
    </template>
    <div>
      <label>名称显隐：</label>
      <el-switch v-model="state.showLabel" inline-prompt active-text="显示" inactive-text="隐藏" />
    </div>
  </div>
  <!-- 全景态势 -->

  <customPopup left="0.5%" top="7%" width="25%" height="92%" headType="2" v-if="state.pageRadio == 1">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">任务执行情况</span>
    </template>
    <template #content>
      <div class="scheduleClass">
        <div class="task_schedule" v-for="task in state.taskList" :key="task.id">
          <div style="
              width: calc(100% - 20px);
              padding-right: 20px;
              overflow-wrap: break-word;
            ">
            {{ task.taskName }}
          </div>
          <el-progress :percentage="task.schedule || 0" :color="customColor" />
        </div>
      </div>
    </template>
  </customPopup>
  <customPopup right="0.5%" top="7%" width="25%" height="35%" headType="2" v-if="state.pageRadio == 1">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">目标接入情况</span>
    </template>
    <template #content>
      <div class="chartClass" ref="Chart2"></div>
    </template>
  </customPopup>
  <customPopup right="0.5%" bottom="1%" width="25%" height="35%" headType="2" v-if="state.pageRadio == 1">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">资源接入情况</span>
    </template>
    <template #content>
      <div class="chartClass" ref="Chart4"></div>
    </template>
  </customPopup>
  <!-- 任务态势 -->
  <customPopup left="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 2">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">任务列表</span>
    </template>
    <template #content>
      <div style="height: 100%; width: 100%">
        <el-table ref="taskTableRef" class="custom-table" :data="state.taskList" highlight-current-row
          style="height: 100%" @row-click="taskTableClick">
          <el-table-column align="center" prop="taskName" label="任务名称" min-width="3"></el-table-column>
          <el-table-column align="center" prop="status" label="任务类型" min-width="2">
            <template v-slot="scope">
              <span>{{
                scope.row.taskType[0]
                  ? dictValue('businessType', scope.row.taskType[0])
                  : ''
              }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="schedule" label="任务进度" min-width="2">
            <template v-slot="scope">
              <span>{{ scope.row.schedule }} %</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </template>
  </customPopup>
  <customPopup right="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 2">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">任务详情</span>
    </template>
    <template #content>
      <div style="height: 100%; width: 100%; overflow-y: auto">
        <el-form :model="state.taskForm" label-width="80px">
          <el-form-item label="任务名称" prop="taskName">{{
            state.taskForm.taskName
          }}</el-form-item>
          <!-- <el-form-item label="所属需求" prop="endTime">{{ state.taskForm.requirementName }}</el-form-item> -->
          <!-- dictValue("taskStatus", state.taskForm.status) -->
          <el-form-item label="业务类型" prop="taskType">{{
            state.taskForm.taskType && state.taskForm.taskType.length > 0
              ? dictValue('businessType', state.taskForm.taskType[0])
              : ''
          }}</el-form-item>
          <el-form-item label="任务状态" prop="taskStatus">{{
            state.taskForm.taskStatus
          }}</el-form-item>
          <el-form-item label="任务进度" prop="schedule">{{ state.taskForm.schedule }}%
          </el-form-item>

          <el-form-item label="任务周期" prop="endTime">{{
            dictValue('repetitionType', state.taskForm.repeatType)
          }}</el-form-item>
          <el-form-item label="开始时间" prop="startTime">{{
            state.taskForm.startTime
          }}</el-form-item>
          <el-form-item label="结束时间" prop="endTime">{{
            state.taskForm.endTime
          }}</el-form-item>
          <el-form-item label="任务描述" prop="taskComment">{{
            state.taskForm.taskComment
          }}</el-form-item>
        </el-form>
      </div>
    </template>
  </customPopup>
  <customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 2">
    <template #header>
      <span class="timeLine_icon"></span>
      <span class="title-font">任务执行情况</span>
      <span class="title-font" style="position: absolute; right: 3%">当前时间：{{ timeSpan(state.currentTime) }}</span>
    </template>
    <template #content>
      <div class="chartClass" id="taskTimeLine"></div>
    </template>
  </customPopup>
  <!-- 资源态势 -->
  <!-- <customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 3">
        <template #header>
            <span class="timeLine_icon"></span>
            <span class="title-font">资源使用情况</span>
            <span class="title-font" style="position: absolute; right: 3%;">当前时间：{{ timeSpan(state.currentTime)
                }}</span>

        </template>
        <template #content>
            <div class="chartClass" id="resourceTimeLine"></div>
        </template>
    </customPopup> -->
  <customPopup left="0.5%" top="7%" width="15%" height="90%" v-if="state.pageRadio == 3">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">资源列表</span>
     <!--  <el-button type="primary" size="small" @click="showResourceStats"
                 style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);">
        资源统计
      </el-button> -->
    </template>
    <template #content>
      <div style="height: 100%; width: 100%">
        <el-table ref="resourceTableRef" class="custom-table" :data="state.resourceList" style="height: 100%"
          :row-class-name="chooseZY" highlight-current-row @row-click="taskTableClickZY">
          <el-table-column align="center" prop="name" label="设备名称" min-width="3"></el-table-column>
          <el-table-column align="center" prop="typeValue" label="设备类型" min-width="2">
          </el-table-column>
        </el-table>
      </div>
    </template>
  </customPopup>
  <customPopup right="0.5%" top="7%" width="15%" height="45%" v-if="state.pageRadio == 3">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">资源详情</span>
    </template>
    <template #content>
      <div style="height: 100%; width: 100%; display: flex; flex-direction: column">
        <div style="flex: 1; overflow-y: auto">
          <el-form :model="state.ZYForm" label-width="120px" class="targetForm_class" label-position="left">
            <el-form-item label="名称" prop="name">{{
              state.ZYForm.name
            }}</el-form-item>
            <el-form-item label="节点代号" prop="code">{{
              state.ZYForm.code
            }}</el-form-item>
            <el-form-item label="节点类型" prop="code">{{
              state.ZYForm.dataTypeValue
            }}</el-form-item>
            <el-form-item label="频段" prop="frequency">{{
              state.ZYForm.frequency
            }}</el-form-item>
            <el-form-item label="多目标能力" prop="targetCount">{{
              state.ZYForm.targetCount
            }}</el-form-item>
            <el-form-item label="业务类型" prop="businessType">{{
              dictValue('businessType', state.ZYForm.businessType)
            }}</el-form-item>
            <el-form-item label="EIRP（dB）" prop="eirp">{{
              state.ZYForm.eirp
            }}</el-form-item>
            <el-form-item label="G/T（dB）" prop="gt">{{
              state.ZYForm.gt
            }}</el-form-item>
            <el-form-item label="天线类型" prop="antennaType">{{
              dictValue('antennaType', state.ZYForm.antennaType)
            }}</el-form-item>
            <el-form-item label="工作体制" prop="workSystem">{{
              dictValue('workSystem', state.ZYForm.workSystem)
            }}</el-form-item>
            <el-form-item label="经度（°）" prop="longitude">{{
              state.ZYForm.longitude
            }}</el-form-item>
            <el-form-item label="纬度（°）" prop="latitude">{{
              state.ZYForm.latitude
            }}</el-form-item>
            <el-form-item label="高度（米）" prop="altitude">{{
              state.ZYForm.altitude
            }}</el-form-item>
            <el-form-item label="测控距离（米）" prop="radius">{{
              state.ZYForm.bottomRadius
            }}</el-form-item>
          </el-form>
        </div>
        <div style="
            text-align: center;
            padding: 10px 0;
            border-top: 1px solid #003A66;
          ">
          <!-- <el-button
            type="primary"
            @click="clickStationRow()"
            v-if="state.ZYForm.id && state.ZYForm.id !== '00000000'"
          >
            资源状态
          </el-button> -->
          <el-button type="primary" @click="clickGplot()" v-if="state.ZYForm.id === '00000000'">
            详情
          </el-button>
        </div>
      </div>
    </template>
  </customPopup>

  <!-- 资源状态弹窗 -->
  <customPopup right="0.5%" top="53%" width="15%" height="45%" v-if="state.pageRadio == 3">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">资源状态</span>
    </template>
    <template #content>
      <div style="height: 100%; width: 100%; overflow-y: auto">
        <el-form :model="state.ZYForm" label-width="120px" class="targetForm_class" label-position="left">
          <el-form-item v-for="(item, i) in state.stationData" :key="'target' + i" :label="'测控目标' + (i * 1 + 1) + '：'"
            prop="targetStatus">
            <div class="target-status-container">
              <div class="sucess_icon" style="height: 24px;width: 24px;" v-if="getTargetStatus(item)"></div>
              <div class="close_icon" style="height: 24px;width: 24px;" v-else></div>
            </div>
          </el-form-item>
        </el-form>
        <div style="
            text-align: center;
            padding: 10px 0;
            border-top: 1px solid #003A66;
          ">
          <el-button type="primary" @click="clickStationRow()" v-if="state.ZYForm.id && state.ZYForm.id !== '00000000'">
            资源状态
          </el-button>

        </div>
      </div>
    </template>
  </customPopup>

  <customPopup left="16%" bottom="3%" width="68%" height="20%" v-if="state.pageRadio == 3">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">实时日志</span>
      <div style="position: absolute; right: 2%; display: flex; align-items: center;">
        <span style="color: white; margin-right: 8px; font-size: 12px;">日志类型：</span>
        <el-select v-model="state.logTypeFilter" placeholder="全部" size="small" style="width: 120px;" clearable
          @change="handleLogTypeChange">
          <el-option label="站点状态" value="stationState"></el-option>
          <el-option label="终端状态" value="terminalState"></el-option>
          <el-option label="操控端交互日志" value="controlLog"></el-option>
          <el-option label="接入决策模块交互日志" value="selectionLog"></el-option>
        </el-select>
      </div>
    </template>
    <template #content>
      <div ref="logContainer" style="height: 130px; overflow: auto; ">
        <p style="font-size: 16px; line-height: 25px" v-for="(item, index) in filteredLogs" :key="index">
          {{ `${item.time} ` }}
          【
          <span :style="{ color: item.type == 'ERROR' ? '#f00' : '#008000' }">{{
            `${item.type}`
          }}</span>
          】：
          {{ `${item.message}` }}
        </p>
      </div>
    </template>
  </customPopup>

  <!-- 目标态势 -->
  <customPopup left="0.5%" top="7%" width="15%" height="60%" v-if="state.pageRadio == 4">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">目标列表</span>
      <div style="position: absolute; right: 2%">
        <el-button @click="terminalControl">终端控制</el-button>
      </div>
    </template>
    <template #content>
      <div style="height: 100%; width: 100%">
        <el-table ref="targetTableRef" class="custom-table" :data="state.targetList" style="height: 100%"
          highlight-current-row :row-class-name="chooseMB" @row-click="targetTableClick">
          <el-table-column align="center" prop="name" label="目标名称"></el-table-column>
          <el-table-column align="center" prop="targetTypeValue" label="目标类型">
            <!-- <template v-slot="{ row }">
                            <span>{{ state.targetTypeList[row.targetTypeValue] }}</span>
                        </template> -->
          </el-table-column>
          <!-- <el-table-column align="center" prop="name" label=" " width="50">
                        <template v-slot="{ row }">
                            <span @click="clickDetileRow(row)">
                                <el-icon style="cursor: pointer;">
                                    <View />
                                </el-icon>
                            </span>
                        </template>
                    </el-table-column> -->
        </el-table>
      </div>
    </template>
  </customPopup>
  <customPopup right="0.5%" top="7%" width="15%" height="30%" v-if="state.pageRadio == 4">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">目标详情</span>
    </template>
    <template #content>
      <el-form :model="state.chooseTargetForm" label-width="80px"
        style="height: calc(100% - 30px); overflow: auto; padding-left: 0px" class="targetForm_class"
        v-show="state.chooseTargetForm?.id" label-position="left">
        <el-form-item label="目标名称">{{
          state.chooseTargetForm.name
        }}</el-form-item>
        <el-form-item label="目标类型">{{
          state.chooseTargetForm.targetTypeValue
        }}</el-form-item>
        <el-form-item label="工作体制" prop="workSystem">{{
          dictValue('workSystem', state.chooseTargetForm.workSystem)
        }}</el-form-item>
        <el-form-item label="业务类型" prop="businessType">{{
          dictValue('businessType', state.chooseTargetForm.businessType)
        }}</el-form-item>
        <el-form-item label="EIRP" prop="eirp">{{ state.chooseTargetForm.eirp }}dB</el-form-item>
        <el-form-item label="天线类型" prop="antennaType">{{
          dictValue('antennaType', state.chooseTargetForm.antennaType)
        }}</el-form-item>
        <!-- <el-form-item v-for="item in formLabelList" :label="item.label + ' :'" :prop="item.key" :key="item.key"
                    v-show="targetShowLabelList.includes(item.key)">
                    <span v-if="!item.multiple">{{ state.chooseTargetForm[item.key] }}</span>
                    <span v-else-if="item.multiple">
                        <span v-for="(textKey, value) in state.chooseTargetForm[item.key]" :key="textKey">{{
                            value }}：{{ textKey }}<br /></span>
                    </span>
                </el-form-item> -->
      </el-form>
      <!-- v-show="state.chooseTargetForm?.id" -->
      <el-link style="text-align: left; float: right" @click="clickDetileRow()">更多...</el-link>
    </template>
  </customPopup>
  <customPopup right="0.5%" top="38%" width="15%" height="30%" v-if="state.pageRadio == 4">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">终端状态</span>
    </template>
    <template #content>
      <el-form :model="targetDataObj[state.chooseTargetForm?.id] || {}" label-width="144px"
        style="height: calc(100% - 30px); overflow: auto; padding-left: 0px" class="targetForm_class"
        v-show="state.chooseTargetForm?.id" label-position="left">
        <el-form-item label="终端ID :" label-width="80px">
          <span>
            {{ state.chooseTargetForm.nodeId }}
          </span>
        </el-form-item>
        <el-form-item label="终端体制 :" label-width="80px">
          <span>
            {{ modeCtrlList[state.chooseTargetForm.terCurrentMode] || '暂无' }}
          </span>
        </el-form-item>
        <el-form-item v-for="item in terminalList" :label="item.label + ' :'" :prop="item.key" :key="item.key"
          v-show="terminalBriefList.includes(item.key)">
          <span>{{
            (targetDataObj[state.chooseTargetForm?.id]?.machineFrame?.[
              item.key
            ] || '-') + (item.unit || '')
          }}</span>
          <!-- <span>
                        <span v-for="(value, textKey ) in state.terminalDate[item.key]" :key="textKey">{{
                            textKey }}：{{ value }}<br /></span>
                    </span> -->
        </el-form-item>
      </el-form>
      <!-- v-show="state.targetForm?.id" -->
      <el-link style="text-align: left; float: right" @click="clickTerminalRow()">更多...</el-link>
    </template>
  </customPopup>
  <customPopup left="33%" top="20%" width="35%" height="50%" v-if="state.showPic && state.pageRadio == 4">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">目标素材</span>
      <div style="position: absolute; right: 2%">
        <el-button @click="state.showPic = false">关闭</el-button>
      </div>
    </template>
    <template #content>
      <div style="height: 100%; width: 100%; overflow-y: auto">
        <div style="width: 100%; height: 48%">
          <img style="height: 100%; width: 100%" :src="hostLink + '/file/test.png'" />
        </div>
        <div style="width: 100%; height: 48%">
          <video style="height: 100%; width: 100%" controls type="video/wmv"
            :src="hostLink + '/file/video.wmv'"></video>
        </div>
      </div>
    </template>
  </customPopup>
  <customPopup left="0.5%" top="70%" width="99%" height="27%" v-if="state.pageRadio == 4">
    <template #header>
      <span class="timeLine_icon"></span>
      <span class="title-font">目标接入情况</span>
      <span class="title-font" style="position: absolute; right: 3%">当前时间：{{ timeSpan(state.currentTime) }}</span>
    </template>
    <template #content>
      <div class="chartClass" id="targerTimeLine"></div>
    </template>
  </customPopup>
  <!-- 目标详情弹窗 -->
  <customPopup left="25%" top="15%" width="50%" height="60%" v-if="state.pageRadio == 4 && state.detailShow">
    <template #header>
      <span class="timeLine_icon"></span>
      <span class="title-font">目标详情</span>
      <span class="title-font" style="position: absolute; right: 3%; cursor: pointer"
        @click="state.detailShow = false">X
      </span>
    </template>
    <template #content>
      <tsDetail :tsData="state.targetForm" v-if="state.pageRadio == 4 && state.detailShow"></tsDetail>
    </template>
  </customPopup>

  <!-- 终端更多弹窗 -->
  <customPopup left="calc(50% - 575px)" top="15%" width="1150px" height="60%"
    v-if="state.pageRadio == 4 && state.terminalStateShow">
    <template #header>
      <span class="timeLine_icon"></span>
      <span class="title-font">终端状态-{{ targetDataObj[state.chooseTargetForm.id]?.tTime }}</span>
      <span class="title-font" style="position: absolute; right: 3%; cursor: pointer"
        @click="state.terminalStateShow = false">X
      </span>
    </template>
    <template #content>
      <!-- <terminalState :terminalDate="state.terminalDate" v-if="state.pageRadio == 4 && state.terminalStateShow"> -->
      <terminalState :terminalDate="targetDataObj[state.chooseTargetForm.id] || {}"
        v-if="state.pageRadio == 4 && state.terminalStateShow">
      </terminalState>
    </template>
  </customPopup>
  <!-- 拓扑图弹窗 -->
  <customPopup left="25%" top="15%" width="56%" height="60%" v-if="state.pageRadio == 3 && state.gplotShow">
    <template #header>
      <span class="timeLine_icon"></span>
      <span class="title-font">随遇接入节点</span>
      <span class="title-font" style="position: absolute; right: 3%; cursor: pointer" @click="state.gplotShow = false">X
      </span>
    </template>
    <template #content>
      <gplot :gplotData="state.gplotData" :terminalDate="targetDataObj[0] || {}"
        v-if="state.pageRadio == 3 && state.gplotShow">
      </gplot>
    </template>
  </customPopup>
  <!-- 终端控制弹窗 -->
  <customPopup left="37.5%" top="15%" width="25%" height="50%" v-if="state.pageRadio == 4 && state.terminalControlShow">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">终端控制</span>
      <div style="position: absolute; right: 2%">
        <el-button @click="state.terminalControlShow = false">关闭</el-button>
      </div>
    </template>
    <template #content>
      <!-- <el-tabs v-model="state.activeName" class="tabsClass" @tab-change="tabChange"> -->
      <!-- <el-tab-pane label="整机参数设置" name="1"> -->
      <el-tabs v-model="state.contorlActive" class="tabsClass">
        <el-tab-pane label="整机参数设置" name="1" style="padding-top: 15px">
          <el-form :model="state.terminalControlForm.machineParam" label-width="124px"
            style="height: calc(100% - 30px); overflow: auto; padding-left: 0px" label-position="left">
            <el-form-item label="目标终端序号">
              <el-input-number v-model="state.terminalControlForm.machineParam.targetNode" :controls="false"
                style="width: 70%; text-align: left"></el-input-number>
            </el-form-item>
            <el-form-item label="站点选择">
              <el-select v-model="state.terminalControlForm.machineParam.nodeId" style="width: 70%">
                <el-option value="1834120494834802689" label="航天站_S"></el-option>
                <el-option value="1834055782248001538" label="航天站_Ka"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="体制选择">
              <el-select v-model="state.terminalControlForm.machineParam.modeCtrl" style="width: 70%">
                <el-option :value="0" label="不切换体制"></el-option>
                <el-option :value="1" label="航天测控体制-S1"></el-option>
                <el-option :value="2" label="航天测控体制-S2"></el-option>
                <el-option :value="3" label="航天测控体制-S3"></el-option>
                <el-option :value="4" label="航天测控体制-S4"></el-option>
                <el-option :value="5" label="航天测控体制-Ka1"></el-option>
                <el-option :value="6" label="航天测控体制-Ka2"></el-option>
                <el-option :value="7" label="航天测控体制-Ka3"></el-option>
                <el-option :value="8" label="航天测控体制-Ka4"></el-option>
                <el-option :value="9" label="无人机测控体制"></el-option>
                <el-option :value="10" label="DD测控体制"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="接收频率(MHz)">
              <el-input-number v-model="state.terminalControlForm.machineParam.rxFreq" style="width: 70%"
                :controls="false">
              </el-input-number>
            </el-form-item>
            <el-form-item label="发射频率(MHz)">
              <el-input-number v-model="state.terminalControlForm.machineParam.txFreq" style="width: 70%"
                :controls="false"></el-input-number>
            </el-form-item>
            <el-form-item label="发射衰减(dB)">
              <el-input-number v-model="state.terminalControlForm.machineParam.txAtte" style="width: 70%"
                :controls="false"></el-input-number>
            </el-form-item>
            <el-form-item label="">
              <el-button @click="setMachineParam">发送</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="数据传输控制" name="2" style="padding-top: 15px; height: 100%">
          <el-form :model="state.terminalControlForm.dataControl" label-width="124px"
            style="height: calc(100% - 30px); overflow: auto; padding-left: 0px" label-position="left">
            <el-form-item label="终端选择">
              <el-select v-model="state.terminalControlForm.dataControl.targetNode" style="width: 70%"
                @change="targetNodeChange">
                <el-option value="1" label="终端1"></el-option>
                <el-option value="2" label="终端2"></el-option>
                <el-option value="3" label="终端3"></el-option>
                <el-option value="4" label="终端4"></el-option>
                <el-option value="5" label="终端5"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="终端体制">
              {{
                state.terminalControlForm.dataControl.modeCtrl
                  ? modeCtrlList[state.terminalControlForm.dataControl.modeCtrl]
                  : '暂无'
              }}
            </el-form-item>
            <el-form-item label="下行发射">
              <el-select v-model="state.terminalControlForm.dataControl.transModState" style="width: 70%">
                <el-option :value="0xb3d2" label="遥测任务"></el-option>
                <el-option :value="0xc72f" label="数传任务" v-show="!['1', '2', '3'].includes(
                  state.terminalControlForm.dataControl.modeCtrl
                )
                  "></el-option>
                <el-option :value="0x4c2d" label="遥测测试" v-show="!['1', '2', '3'].includes(
                  state.terminalControlForm.dataControl.modeCtrl
                )
                  "></el-option>
                <el-option :value="0x3968" label="数传测试" v-show="!['1', '2', '3'].includes(
                  state.terminalControlForm.dataControl.modeCtrl
                )
                  "></el-option>
                <el-option :value="0x235a" label="单音测试"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="">
              <el-button @click="setDataControlParam">发送</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </template>
  </customPopup>
  <!-- 站点信息弹窗 -->
  <customPopup left="calc(50% - 575px)" top="15%" width="1150px" height="60%"
    v-if="state.pageRadio == 3 && state.stationMoreShow">
    <template #header>
      <span class="timeLine_icon"></span>
      <span class="title-font">资源状态 -- {{ state.ZYForm.name }}</span>
      <span class="title-font" style="position: absolute; right: 3%; cursor: pointer"
        @click="state.stationMoreShow = false">X
      </span>
    </template>
    <template #content>
      <stationDataState :stationData="state" v-if="state.pageRadio == 3 && state.stationMoreShow">
      </stationDataState>
    </template>
  </customPopup>

  <!-- 资源统计模态框 -->
  <customPopup left="20%" top="10%" width="60%" height="80%" v-if="state.resourceStatsShow">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">资源统计分析</span>
      <span class="title-font" style="position: absolute; right: 3%; cursor: pointer"
        @click="state.resourceStatsShow = false">X
      </span>
    </template>
    <template #content>
      <div style="height: 100%; width: 100%; display: flex; flex-direction: column;">
        <!-- 散点图 -->
        <div style="height: 50%; width: 100%; border-bottom: 1px solid #2e5175;">
          <div style="padding: 10px; color: #fff; font-size: 14px; font-weight: bold;">
            资源地理分布（经纬度散点图）
          </div>
          <div style="height: calc(100% - 40px); width: 100%;" ref="scatterChart"></div>
        </div>
        <!-- 雷达图 -->
        <div style="height: 50%; width: 100%;">
          <div style="padding: 10px; color: #fff; font-size: 14px; font-weight: bold;">
            资源能力雷达图
          </div>
          <div style="height: calc(100% - 40px); width: 100%;" ref="radarChart"></div>
        </div>
      </div>
    </template>
  </customPopup>
</template>

<script setup>
import { onMounted, onUnmounted, ref, reactive, watch, nextTick, computed } from 'vue'
import CesiumBox from '@/components/evmap/CesiumBox.vue'
import customPopup from '@/components/customPopup.vue'
import * as situationApi from '@/service/API/system/situation.js'
import * as stationDataLable from "./stationDataLable.js"

import * as areaApi from '@/service/API/system/areaManage.js'
import * as dataApi from '@/service/API/home/<USER>'
import * as controlApi from '@/service/API/system/control.js'
import * as resourceApi from '@/service/API/system/resourceDispatch.js'
import * as formLabel from './formLabel.js'
import * as formLabel2 from './formLabel2.js'
import * as terminalLable from './terminalLable.js'
import tsDetail from './tsDetail.vue'
import terminalState from './terminalState.vue'
import stationDataState from './stationDataState.vue'
import { ElMessage } from 'element-plus'
import gplot from './gplot.vue'
import { useSockeMessStore } from '@/stores/index'
const sockeMessStore = useSockeMessStore()

const dictValue = window.dictValue
const timeSpan = window.timeSpan
let hostLink = AdminServerApi

const taskTableRef = ref(null)
const resourceTableRef = ref(null)
const targetTableRef = ref(null)
const logContainer = ref(null)

const customColor = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#1989fa', percentage: 60 },
  { color: '#5cb87a', percentage: 80 },
  { color: '#03a2a7', percentage: 100 },
]
const modeCtrlList = {
  0: '不切换体制',
  1: '航天测控体制-S',
  2: '航天测控体制-ka',
  3: '无人机测控体制',
  4: 'DD测控体制',
}
var formLabelList = formLabel2.failedList
var terminalList = terminalLable.failedList //终端字段（全）
var terminalBriefList = terminalLable.showBriefList //终端字段（简略显示列表）
var targetShowLabelList = formLabel2.targetShowList //目标详情展示列表

const state = reactive({
  pageRadio: '3',
  taskList: [], //任务列表
  taskForm: {}, //任务详情
  // ZYList: [],// sdw 资源列表
  ZYForm: [], // sdw 资源详情
  chooseTask: undefined,
  chooseResource: undefined,
  chooseTarget: undefined,
  showTargetList: [], //当前要展示的目标列表
  showResourceList: [], //当前要展示的资源列表
  targetList: [],
  targetFiled: [], //目标的字段
  showList: [],
  targetForm: {}, //目标详情
  showPic: false, //素材展示
  getting: false, //正在获取中
  playStatus: 'pause', //播放状态
  resourceList: [
    // {
    //   id: '00000000',
    //   name: '地基航天综合测控节点',
    //   typeValue: '固定站',
    //   dataTypeValue: '地基节点',
    //   code: '00000000',
    //   longitude: 118.8911,
    //   latitude: 23.5312,
    //   altitude: 2000,
    //   updateTime: 1735887373000,
    //   bottomRadius: 100000,
    //   targetCount: 1,
    //   eirp: 100,
    //   gt: 1,
    //   antennaType: 1,
    //   frequency: '0,1',
    //   pitchAngle: 0,
    //   workSystem: '1',
    //   areaId: '1838030102630240258',
    //   createTime: 1726107948000,
    //   generalId: '7',
    //   bearingAngle: 0,
    //   businessType: '1',
    // },
  ], //展示的资源列表
  currentTime: '2000-01-01 00:00:01', //当前时间
  showTrack: true, // 显隐航迹
  showArea: true, // 显隐范围
  showLabel: true, //名称显隐
  targetTypeList: ['无人车', '无人艇', '无人机'],
  detailShow: false, //目标详情展示
  detailActiveName: '1', //目标详情展示页面的tab
  contorlActive: '1', //控制帧的tab
  terminalStateShow: false, //终端状态弹窗控制
  terminalDate: {}, //终端数据
  gplotShow: false, //拓扑图显示控制
  gplotData: {}, //拓扑图数据
  chooseTargetForm: {}, //在列表中选中的目标
  terminalControlShow: false, //
  terminalControlForm: {
    // 定义 整机参数设置 初始值
    machineParam: {
      // targetNode: 1,
      // modeCtrl: 0,
      // rxFreq: 2070,
      // txFreq: 2260,
      // txAtte: 30
    },
    dataControl: {
      // targetNode: undefined,
      // modeCtrl: '',
      // transModState: undefined
    },
  },
  stationMoreShow: false, //站点的更多显示控制
  stationData: [], //站点信息
  logTypeFilter: '', //实时日志类型过滤器
  resourceStatsShow: false, //资源统计模态框显示控制
})
const targetDataObj = ref({})
const Chart2 = ref(null)
const Chart4 = ref(null)
const scatterChart = ref(null)
const radarChart = ref(null)

let fontColor = window.$fontColor
let TSinterval
let timePoint = 0
let uuid = parseInt(Math.random() * 100000000000)
let panoramaId
onMounted(() => {
  panoramaId = localStorage.getItem('panoramaId')
  pageRadioChange()
  window.addEventListener('onmessageTSWS', getSocketData)
})
onUnmounted(() => {
  window.removeEventListener('onmessageTSWS', getSocketData)
  mapTool.removeAll()
  clearInterval(TSinterval)
})
// 显隐航迹
watch(
  () => state.showTrack,
  (val) => {
    mapTool.showEffByAll('line', val)
  }
)
// 显隐雷达
watch(
  () => state.showArea,
  (val) => {
    state.resourceList.forEach((ele) => {
      let type = ele.dataType == 2 || ele.dataType == 3 ? 'cylinder' : 'radar'
      // 兼容不同的ID字段名称：id, equipmentId, equipmentDetail.id
      let resourceId = ele.id || ele.equipmentId || ele.equipmentDetail?.id
      if (resourceId) {
        mapTool.showEffById(resourceId, type, val)
      } else {
        console.warn('资源ID未找到:', ele)
      }
    }, { deep: true })
    //   mapTool.showEffByAll('radar', val)
    //   mapTool.showEffByAll('cylinder', val)
  }
)
// 显隐名称
watch(
  () => state.showLabel,
  (val) => {
    mapTool.showEffByAll('label', val)
  }
)

// 记录滚动状态
let wasAtBottom = true
let previousScrollHeight = 0

// 监听实时日志数据变化前，记录当前状态
watch(
  () => sockeMessStore.tsSocket.length,
  () => {
    if (logContainer.value) {
      const container = logContainer.value
      // 记录新数据添加前的状态
      const scrollTop = container.scrollTop
      const clientHeight = container.clientHeight
      const scrollHeight = container.scrollHeight

      // 更宽松的底部检测，容差增加到10px
      wasAtBottom = scrollTop + clientHeight >= scrollHeight - 10
      previousScrollHeight = scrollHeight

      // 调试信息
      console.log('滚动状态检测:', {
        scrollTop,
        clientHeight,
        scrollHeight,
        isAtBottom: wasAtBottom,
        diff: scrollHeight - (scrollTop + clientHeight)
      })
    }
  },
  { flush: 'pre' } // 在DOM更新前执行
)

// 监听实时日志数据变化后，智能滚动（类似VSCode终端效果）
watch(
  () => sockeMessStore.tsSocket.length,
  () => {
    nextTick(() => {
      if (logContainer.value) {
        const container = logContainer.value

        if (wasAtBottom) {
          // 如果之前在底部，自动滚动到新的底部
          console.log('执行自动滚动到底部')

          const scrollToBottom = () => {
            const newScrollHeight = container.scrollHeight
            container.scrollTop = newScrollHeight
            console.log('滚动执行:', {
              scrollTop: container.scrollTop,
              scrollHeight: newScrollHeight,
              clientHeight: container.clientHeight
            })
          }

          // 立即滚动
          scrollToBottom()

          // 延迟滚动，确保DOM完全渲染
          setTimeout(scrollToBottom, 0)
          setTimeout(scrollToBottom, 50) // 增加延迟时间
        } else {
          console.log('滚动条不在底部，保持当前位置')
          // 如果之前不在底部，保持相对位置不变
          // 由于新内容添加到底部，滚动位置不需要调整，内容自然保持不变
        }
      }
    })
  },
  { flush: 'post' } // 在DOM更新后执行
)

// 额外的滚动确保机制
watch(
  () => sockeMessStore.tsSocket,
  () => {
    if (logContainer.value && wasAtBottom) {
      // 使用 requestAnimationFrame 确保在浏览器下一次重绘时滚动
      requestAnimationFrame(() => {
        if (logContainer.value) {
          logContainer.value.scrollTop = logContainer.value.scrollHeight
          console.log('requestAnimationFrame 滚动执行')
        }
      })
    }
  },
  { deep: true, flush: 'post' }
)

var currentPage, currentIds

//切换场景
function pageRadioChange() {
  // mapTool.removeAll()
  // state.playStatus = "pause"
  if (state.pageRadio != 1) {
    state.showArea = true
  }
  state.showLabel = true
  state.chooseTask = undefined
  state.chooseResource = undefined
  state.chooseTarget = undefined
  state.showResourceList = []
  state.showTargetList = []
  state.targetForm = {}
  state.detailShow = false
  mapTool.removeAllFlowLine()

  switch (state.pageRadio) {
    case '1':
      // drawChart2()

      // drawChart4()
      setTimeout(() => {
        // mapTool.showEffByAll("cylinder", false)
        const height = FlyToHeightConfig[panoramaId] || 1000000
        const position = FlyToHeightConfig[panoramaId + '-position']
        position &&
          Viewer.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(
              position[0],
              position[1],
              height
            ),
          })
      }, 1000)
      mapTool.showEntityByAll(true, false)
      state.chooseTask = undefined
      state.chooseTarget = undefined
      state.chooseResource = undefined
      state.showTargetList = [...state.targetList]
      state.showResourceList = [...state.resourceList]
      break
    case '2':
      setTimeout(() => {
        initTimeLine('taskTimeLine')
        drawTimeLine('taskTimeLine')
        mapTool.showEntityByAll(true, true)
      }, 0)
      break
    case '3':
      setTimeout(() => {
        initTimeLine('resourceTimeLine')
        drawTimeLine('resourceTimeLine')
        mapTool.showEntityByAll(true, true)
      }, 0)
      // 设置资源列表，确保范围显隐功能正常工作
      // 深拷贝资源列表，避免引用问题
      state.showResourceList = JSON.parse(JSON.stringify(state.resourceList))
      console.log('资源列表初始化:', state.showResourceList)
      break
    case '4':
      setTimeout(() => {
        initTimeLine('targerTimeLine')
        drawTimeLine('targerTimeLine')

        mapTool.showEntityByAll(true, true)
      }, 0)
      break
    default:
      break
  }
}
//获取

//右上统计图 //目标执行情况
function drawChart2() {
  let chart = echarts.init(Chart2.value)
  let option = {
    color: chartColors,

    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: ['遥控', '遥测', '测量', '数传'],
      textStyle: {
        color: fontColor,
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      // feature: {
      //     mark: {show: true},
      //     dataView: {show: true, readOnly: false},
      //     magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
      //     restore: {show: true},
      //     saveAsImage: {show: true}
      // }
    },
    xAxis: [
      {
        type: 'category',
        axisTick: { show: false },
        data: ['无人机', '无人艇', '无人车', '弹'],
        axisLine: window.axisLine,
        axisLabel: {
          color: fontColor,
          rotate: 45,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        splitLine: window.splitLine,
        axisLine: window.axisLine,
        axisLabel: {
          color: fontColor,
        },
      },
    ],
    series: [
      {
        name: '遥控',
        type: 'bar',
        barGap: 0,
        // label: "遥控",
        emphasis: {
          focus: 'series',
        },
        data: [0, 0, 0, 0],
      },
      {
        name: '遥测',
        type: 'bar',
        // label: labelOption,
        emphasis: {
          focus: 'series',
        },
        data: [0, 0, 0, 0],
      },
      {
        name: '测量',
        type: 'bar',
        // label: labelOption,
        emphasis: {
          focus: 'series',
        },
        data: [0, 0, 0, 0],
      },
      {
        name: '数传',
        type: 'bar',
        // label: labelOption,
        emphasis: {
          focus: 'series',
        },
        data: [0, 0, 0, 0],
      },
    ],
  }
  situationApi.getTargetBz({ requirementId: panoramaId }).then((res) => {
    if (res.data.code == 200) {
      let data = res.data.data
      let targetType = ['无人机', '无人艇', '无人车', '弹']
      data.forEach((item) => {
        let businessIndex = item.taskType[0] - 1
        let targetTypeIndex = targetType.indexOf(item.target.dataTypeValue)
        if (targetTypeIndex > -1 && businessIndex > -1) {
          option.series[businessIndex].data[targetTypeIndex] += 1
        }
      })
      chart.setOption(option, true)
    } else {
      ElMessage.error(res.data.message)
    }
  })

  // chart.setOption(option, true)
}

//右下统计图 //资源接入情况 第一版-废弃
function drawChart4() {
  let chart = echarts.init(Chart4.value)
  // 来源 1 陆 2 海 3 空 4 天
  let typeList = ['地基', '海基', '空基', '天基']
  let option = {
    color: chartColors,

    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: [],
      type: 'scroll',
      orient: 'horizontal',
      textStyle: {
        color: fontColor,
      },
    },
    toolbox: {
      show: false,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      // feature: {
      //     mark: {show: true},
      //     dataView: {show: true, readOnly: false},
      //     magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
      //     restore: {show: true},
      //     saveAsImage: {show: true}
      // }
    },
    yAxis: [
      {
        type: 'category',
        axisTick: { show: false },
        data: typeList,
        axisLine: window.axisLine,
        axisLabel: {
          color: fontColor,
          rotate: 45,
        },
      },
    ],
    xAxis: [
      {
        type: 'value',
        splitLine: window.splitLine,
        axisLine: window.axisLine,
        axisLabel: {
          color: fontColor,
        },
      },
    ],
    series: [],
  }

  // typeList.forEach(type => {
  //     let seriesItem = {
  //         name: type,
  //         type: 'bar',
  //         emphasis: {
  //             focus: 'series'
  //         },
  //         data: []
  //     }
  //     option.series.push({ ...seriesItem })
  // })
  situationApi
    .getRealTimeAreaType({ requirementId: panoramaId })
    .then((res) => {
      if (res.data.code == 200) {
        let resData = res.data.data
        // 兵种
        for (const key in resData) {
          let data = resData[key]
          // 域
          data.forEach((item) => {
            option.legend.data.push(item.areaName)
            let seriesItem = {
              name: item.areaName,
              type: 'bar',
              stack: 'total',
              emphasis: {
                focus: 'series',
              },
              data: [0, 0, 0, 0],
            }
            let count = 0

            item.dataGenerals?.forEach((general) => {
              count = count + general.count
            })
            seriesItem.data[key - 1] = count
            option.series.push({ ...seriesItem })
          })
        }
        chart.setOption(option, true)
      } else {
        ElMessage.error(res.data.message)
      }
    })
}
window.historyTrack = {}
function getSocketData(evt) {
  //
  // 目标
  let detailData = evt.detail.data

	// // 用临时截图
	// if(detailData.data.stationInfo.id == 1834121686784696321){
	// 	detailData.data.stationInfo.dataType = 1
	// 	detailData.data.stationInfo.typeValue = '移动站'
	// }
  if (detailData.type == 5) {
    let _data = evt.detail.data.data
    _data.forEach((item) => {
      item.id = item.mbId
      item.direction = item.rollAngle
      // item.name = item.name + item.id
      // item.longitude = 110 + clacNum*0.1;
      // item.latitude = 29;
      let _type = 4
      if (item.targetType == 0) {
        _type = 6
      } else if (item.targetType == 1) {
        _type = 5
      }
      console.log('items', item)
      mapTool.drawMap({
        ...item,
	    color: 'red',
        dataType: _type,
        name: state.showLabel ? item.name : '',
        customInfo: `<img class='popImg' src="${item.bindingTarget.imgUrl}" width="110" height="110" />`,
      })
    })
    let targetList = _data
    console.log('targetList', targetList)
    targetTableRef.value?.doLayout()
    state.targetForm =
      targetList.find(
        (item) =>
          item.id == state.chooseTargetForm.id ||
          item.bindingTarget.id == state.chooseTargetForm.id
      ) || {}
    targetList.forEach((item) => {
      let index = state.targetList.findIndex(
        (item2) => item2.id == item.bindingTarget.id
      )
      if (index == -1) {
        state.targetList.push({ ...item.bindingTarget })
      } else {
        state.targetList.splice(index, 1, { ...item.bindingTarget })
      }
    })

    return
  }
  // 终端
  if (detailData.type == 6) {
    let nodeList = Object.keys(detailData.data)
    let nodeId = nodeList[0]
    const { terCurrentMode, terCurrentNode } = detailData.data[nodeId]
    if (detailData.data[nodeId].bindingTarget) {
      let bindingTarget = detailData.data[nodeId].bindingTarget
      let index = state.targetList.findIndex(
        (item) => item.id == bindingTarget.id
      )
      const params = {
        ...bindingTarget,
        nodeId: nodeId,
        terCurrentMode,
        terCurrentNode,
      }
      if (index == -1) {
        state.targetList.push(params)
      } else {
        state.targetList.splice(index, 1, params)
      }
      if (state.chooseTargetForm.id === bindingTarget.id) {
        Object.assign(state.chooseTargetForm, params)
      }
    }

    state.gplotData = {
      nodeRelation: detailData.data[nodeId].nodeRelation,
      nodeId: nodeId,
    }
    if (nodeId == 0) {
      targetDataObj.value[0] = {
        ...JSON.parse(
          JSON.stringify({
            ...detailData.data[nodeId],
            nodeId: nodeId,
            terCurrentMode,
            terCurrentNode,
          })
        ),
      }
      return
    }
    // add 2025-4-10 start  待验证 wlj

    let bindingTarget = detailData.data[nodeId]?.bindingTarget
    if (!bindingTarget || !bindingTarget.id) {
      return
    }
    // if(state.chooseTargetForm.id != bindingTarget?.id){
    //     return
    // }
    // add 2025-4-10 end

    let baseTxt = detailData.data[nodeId]?.spectrumFrame?.spectrumValues
    if (!baseTxt) {
      let terminalDate = JSON.parse(
        JSON.stringify({
          ...detailData.data[nodeId],
          nodeId: nodeId,
          terCurrentMode,
          terCurrentNode,
        })
      )
      terminalDate.tTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      targetDataObj.value[bindingTarget.id] = {
        ...terminalDate,
        sysAntJResult: getSysAntJResult(
          targetDataObj.value[bindingTarget.id],
          terminalDate
        ),
      }
    } else {
      let aData = window.atob(baseTxt)
      let arr = new Int8Array(aData.length)
      // let arr2 = new Int8Array(a.length)x`
      let arr2 = []
      for (let i = 0; i < aData.length; i++) {
        arr[i] = aData.charCodeAt(i)
        arr2.push(arr[i])
      }
      detailData.data[nodeId].spectrumFrame.spectrumList = arr2
      // state.terminalDate = JSON.parse(JSON.stringify({ ...detailData.data[nodeId], nodeId: nodeId }))
      let terminalDate = JSON.parse(
        JSON.stringify({
          ...detailData.data[nodeId],
          nodeId: nodeId,
          terCurrentMode,
          terCurrentNode,
        })
      )
      terminalDate.tTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      targetDataObj.value[bindingTarget.id] = {
        ...terminalDate,
        sysAntJResult: getSysAntJResult(
          targetDataObj.value[bindingTarget.id],
          terminalDate
        ),
      }
    }
    return
  }
  if (detailData.type == 7) {
    // state.resourceList =
    let stationInfo = detailData.data[0]
    let stationInfoList = detailData.data
    if (!stationInfo) {
      return
    }
    stationInfo = {
      ...stationInfo,
      ...stationInfo.stationInfo,
      pageStationType: 'station',
    }
    stationInfoList.forEach((item) => (item.pageStationType = 'station'))
    let index = state.resourceList.findIndex(
      (item) => item.id == stationInfo.id
    )
    if (index == -1) {
      state.resourceList.push(stationInfo)
      console.log('stationInfo', stationInfo)
      mapTool.drawMap({ ...stationInfo, dataType: stationInfo.dataType, name: state.showLabel ? stationInfo.name : '', customInfo: `<img  class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />` })
    } else {
      state.resourceList.splice(index, 1, stationInfo)
      if (state.ZYForm.id == stationInfo.id) {
        state.ZYForm = stationInfo
        state.stationData = stationInfoList
        console.log('stationInfoList', stationInfoList)
      }
    }
    resourceTableRef.value?.doLayout()
  }
  if (detailData.type == 8) {
    let stationInfo = detailData.data
    // let stationInfoList = detailData.data
    if (!stationInfo) {
      return
    }
    stationInfo = {
      ...stationInfo,
      ...stationInfo.stationInfo,
      pageStationType: 'DD',
    }
    let index = state.resourceList.findIndex(
      (item) => item.id == stationInfo.id
    )
    if (index == -1) {
      state.resourceList.push(stationInfo)
		// mapTool.drawMap({ ...stationInfo, dataType: 1, name: state.showLabel ? stationInfo.name : '', customInfo: `<img class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />` })
		mapTool.drawMap({ ...stationInfo, name: state.showLabel ? stationInfo.name : '', customInfo: `<img class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />` })
    } else {
      state.resourceList.splice(index, 1, stationInfo)
      if (state.ZYForm.id == stationInfo.id) {
        state.ZYForm = stationInfo
        state.stationData = [stationInfo]
      }
    }
    resourceTableRef.value?.doLayout()
  }
  if (detailData.type == 9) {
    let stationInfo = detailData.data
    // let stationInfoList = detailData.data
    if (!stationInfo) {
      return
    }
    stationInfo = {
      ...stationInfo,
      ...stationInfo.stationInfo,
      pageStationType: 'WRJ',
    }
    let index = state.resourceList.findIndex(
      (item) => item.id == stationInfo.id
    )
    if (index == -1) {
      state.resourceList.push(stationInfo)
		// mapTool.drawMap({ ...stationInfo, dataType: 1, name: state.showLabel ? stationInfo.name : '', customInfo: `<img class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />` })
		mapTool.drawMap({ ...stationInfo, name: state.showLabel ? stationInfo.name : '', customInfo: `<img class='popImg' src="${stationInfo.imgUrl}" width="110" height="110" />` })
    } else {
      state.resourceList.splice(index, 1, stationInfo)
      if (state.ZYForm.id == stationInfo.id) {
        state.ZYForm = stationInfo
        state.stationData = [stationInfo]
      }
    }
    resourceTableRef.value?.doLayout()
  }
  // tsDetailData.value =
}
// 绘制终端
watch(state.targetList, (list) => {
  list.forEach((e) => {
    const { longitude, latitude, altitude } =
      targetDataObj.value[e.id]?.createLinkDataFrame ?? {}
    const position = [longitude, latitude, altitude]
    mapTool.drawMap({
      id: e.id,
	  color: 'red',
      name: state.showLabel ? e.name : '',
      position,
      dataType: e.dataType || 9,
      customInfo: `<img class='popImg' src="${e.imgUrl}" width="110" height="110" />`
    })
    const data = window.flowLineList.find((item) => item.targetId === e.id)
    if (data && data.localId !== e.terCurrentNode) {
      mapTool.removeFlowLine(data.localId, data.targetId)
      mapTool.removeFlowLine(data.targetId, data.localId)
    }
    const lineId = e.terCurrentNode + '_' + e.id + '_flowLine'
    // if (window.flowLineList.find((item) => item.id === lineId)) return
    mapTool.drawflowLine(e.terCurrentNode, e.id, '#00ff00')
    mapTool.drawflowLine(e.id, e.terCurrentNode, '#00ff00')
  })
})
// 校验决策模块结果， accHandEn的值1 -> 0,或者0 -> 0,变化时保留1时的值
function getSysAntJResult(data, newData) {
  if (!data || !data.sysAntJResult) return newData.sysAntJResult
  const oldFlag =
    data.sysAntJResult.accHandEn === 1 || data.sysAntJResult.accHandEn === 0
  const newFlag = newData.sysAntJResult?.accHandEn === 0
  data.sysAntJResult.accHandEn = 0
  return oldFlag && newFlag ? data.sysAntJResult : newData.sysAntJResult
}
var visGroups = new vis.DataSet([]) //时间轴分组
var visItems = new vis.DataSet([]) //时间轴项目
var timeline //时间轴管理器
/**
 * @description 绘制时间轴
 */
function initTimeLine(domID) {
  var container = document.getElementById(domID)
  timeline = new vis.Timeline(container)
  visGroups = new vis.DataSet([])
  visItems = new vis.DataSet([])
  // let start = moment(timelineStart).format("YYYY-MM-DD HH:mm:ss");
  // let min = moment(timelineStart).subtract(1, "h").format("YYYY-MM-DD HH:mm:ss")
  // let end = moment(timelineEnd).format("YYYY-MM-DD HH:mm:ss");
  let options = {
    autoResize: true,
    height: '90%', //高度
    width: '100%', //宽度
    min: '2000-01-01 00:00:00', //设置最小时间范围
    max: '2099-12-31 23:59:59', //设置最大时间范围
    // start: "2024-08-06 15:03:18", //设置开始时间
    // end: "2024-08-07 15:03:18", //设置结束时间
    stack: true, // ture则不重叠
    limitSize: true,
    verticalScroll: true,
    // cluster: true, //数据量过大时使用。
    locale: 'zh-cn',
    xss: {
      disabled: true,
    },
    // groupOrder: function (a, b) {
    //     return a.value - b.value;
    // },
    editable: false,
    showCurrentTime: false,
    moment: function (date) {
      // return moment(date).format("YYYY-MM-DD HH:mm:ss");
      return moment(date)
    },
    locale: moment.locale('zh-cn'),
  }
  timeline.setOptions(options)
  timeline.setGroups(visGroups)
  timeline.setItems(visItems)
  timeline.addCustomTime('2000-01-01 00:00:01')
}
//绘制时间轴项目
function drawTimeLine(domID) {
  // if (domID == "taskTimeLine") {
  // state.taskList.forEach(item => {
  //     visGroups.add({ id: "111" + item.id, content: '规划-' + item.taskName, value: "111" + item.id });
  //     visGroups.add({ id: "222" + item.id, content: '实际-' + item.taskName, value: "222" + item.id });
  //     visItems.add({
  //         id: item.id + "guihua",
  //         group: "111" + item.id,
  //         content: item.taskName,
  //         start: item.startTime,
  //         end: item.endTime,
  //     })
  //     visItems.add({
  //         id: item.id + "shiji",
  //         group: "222" + item.id,
  //         content: item.taskName,
  //         start: timeSpan(new Date(item.startTime).getTime() - Math.random() * 3000),
  //         end: timeSpan(new Date(item.endTime).getTime() - Math.random() * 2000),
  //     })
  // })
  visItems.clear()
  visGroups.clear()

  timeline.setItems(visItems)
  timeline.setGroups(visGroups)

  // }
}
/**
 * @description 任务表格点击事件
 */
const taskTableClick = (row) => {
  clearData()
  state.chooseTask = row.id
  const height = FlyToHeightConfig[panoramaId] || 1000000
  const position = FlyToHeightConfig[panoramaId + '-position']
  position &&
    Viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        position[0],
        position[1],
        height
      ),
    })
  dataApi.getTaskByTaskId({ id: row.id }).then((res) => {
    if (res.data.code == 200) {
      state.taskForm = res.data.data
      state.taskForm.schedule = row.schedule
      // state.playStatus = "play"
      getResourceByTask(row.id)
      visGroups.add({ id: '222' + row.id, content: '', value: '222' + row.id })
      timeline.setGroups(visGroups)

      timeline.setOptions({
        min: timeSpan(new Date(state.taskForm.startTime).getTime() - 86400),
        start: timeSpan(new Date(state.taskForm.startTime).getTime() - 86400),
        max: timeSpan(new Date(state.taskForm.endTime).getTime() + 86400),
        end: timeSpan(new Date(state.taskForm.endTime).getTime() + 86400),
      })
      state.showTargetList = state.targetList.filter((item) => {
        return item.targetId == state.taskForm.target.targetId
      })
      state.showTargetList.forEach((item) => {
        mapTool.showEntityById(item.targetId, true)
      })
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
//根据任务ID获取资源
function getResourceByTask(taskId) {
  resourceApi
    .getEquipmentByTask({ taskId: taskId, size: 999, page: 1 })
    .then((res) => {
      if (res.data.code == 200) {
        state.showResourceList = res.data.data.records
        state.showResourceList.forEach((item) => {
          mapTool.showEntityById(item.equipmentId, true)
        })
        state.playStatus = 'play'
      } else {
        ElMessage.error(res.data.message)
      }
    })
}
//资源行点击事件
const taskTableClickZY = (row) => {
  // state.ZYForm = row
  // return

  // clearData()
  // state.chooseResource = row.equipmentId
  // // window.EVGISMAP("flyTo", {
  // //     center: [row.equipmentDetail.longitude, row.equipmentDetail.latitude],
  // // });
  // const height = FlyToHeightConfig[panoramaId] || 1000000
  // Viewer.camera.flyTo({ destination: Cesium.Cartesian3.fromDegrees(row.equipmentDetail.longitude, row.equipmentDetail.latitude, height) })
  // dataApi.getDetailById({ dataId: row.equipmentId, dataTypeId: row.generalId }).then(res => {
  //     if (res.data.code == 200) {
  //         state.ZYForm = res.data.data;
  //         getDataByResource(row)
  //     } else {
  //         ElMessage.error(res.data.message);
  //     }
  // })
  //

  state.ZYForm = row
  if (row.id === '00000000') {
    if (state.stationMoreShow === true) {
      clickGplot()
    }
    return
  }
  if (state.gplotShow === true) {
    clickStationRow()
  }
  window.EVGISMAP('flyTo', {
    center: [row.longitude, row.latitude, row.altitude + 100 || 1000000],
  })
}
function chooseMB(scope) {
  if (state.chooseTargetForm.id && scope.row.id == state.chooseTargetForm.id) {
    return 'chooseRow'
  } else {
    return ''
  }
}
function chooseZY(scope) {
  if (state.ZYForm.id && scope.row.id == state.ZYForm.id) {
    return 'chooseRow'
  } else {
    return ''
  }
}
//获取最大最小时间
function getMaxMinTime(list) {
  let max, min
  list.forEach((item) => {
    if (item.startTime < min || !min) {
      min = item.startTime
    }
    if (item.endTime > max || !max) {
      max = item.endTime
    }
  })
  return { min, max }
}
var taskByEquipment
//获取资源对应的需要显示的目标
function getDataByResource(row) {
  mapTool.showEntityById(row.id, true)
  situationApi
    .getDataByEquipment({ equipmentId: row.id, requirementId: panoramaId })
    .then((res) => {
      if (res.data.code == 200) {
        let tasks = res.data.data.tasks || []
        let targets = res.data.data.targets || []
        taskByEquipment = res.data.data
        let max, min
        window.aaa = []
        tasks.forEach((item) => {
          visGroups.add({ id: item.id, content: '', value: item.id })
          aaa.push(item.id)
          item.occupancies.forEach((occ) => {
            if (occ.startTime < min || !min) {
              min = occ.startTime
            }
            if (occ.endTime > max || !max) {
              max = occ.endTime
            }
          })
        })

        timeline.setGroups(visGroups)
        // timeline.setItems(visItems);

        timeline.setOptions({
          min: timeSpan(new Date(min).getTime() - 8640 * 10),
          start: timeSpan(new Date(min).getTime() - 8640 * 10),
          max: timeSpan(new Date(max).getTime() + 8640 * 10),
          end: timeSpan(new Date(max).getTime() + 8640 * 10),
        })
        let ids = targets.map((item) => {
          return item.targetId
        })
        //控制显示
        state.showTargetList = state.targetList.filter((item) => {
          return ids.includes(item.id)
        })
        targets.forEach((item) => {
          mapTool.showEntityById(item.targetId, false)
        })
        state.showResourceList = state.resourceList.filter((item) => {
          return row.id == item.id
        })
        mapTool.showEntityById(row.id, true)

        state.playStatus = 'play'
      } else {
        ElMessage.error(res.data.message)
      }
    })
}
function getRowData(row) {
  areaApi
    .getAreaGeneralDataDetail({
      dataTypeId: row.generalId,
      dataId: row.targetId,
    })
    .then((res) => {
      if (res.data.code == 200) {
        state.targetForm = { ...state.targetForm, ...res.data.data }
      } else {
        ElMessage.error(res.data.message)
      }
    })
}
function targetTableClick(row) {
  state.chooseTargetForm = row

  // 清除详情页面数据
  state.targetForm = {}
  state.detailShow = false
  state.terminalStateShow = false

  // controlApi.getTerminalMode({ terminalSeq: row.nodeId }).then(res => {
  //     if (res.data.code == 200) {
  //         state.chooseTargetForm._modeCtrl = res.data.data
  //     } else {
  //         ElMessage.error(res.data.message)
  //     }
  // })
}
//初始化数据
function clearData() {
  visGroups?.clear()
  visItems?.clear()
  state.chooseTask = undefined
  state.chooseTarget = undefined
  state.chooseResource = undefined
  mapTool.showEntityByAll(false)
  // 关闭标牌框
  window.EVGISMAP('closeDiv', { className: '.infoDiv' })
  // 删除聚焦框
  window.selectedId &&
    window.EVGISMAP('removeGroupEntityById', {
      id: window.selectedId + 'makerGroupOnlyOne',
      group: 'makerGroup',
    })

  state.showTargetList = []
  state.showResourceList = []
}
//目标更多点击事件
function clickDetileRow() {
  state.terminalStateShow = false
  state.detailShow = true
}

// 终端更多点击事件
function clickTerminalRow() {
  state.detailShow = false
  state.terminalStateShow = true
}
//拓扑图点击事件
function clickGplot() {
  state.gplotShow = true
  state.stationMoreShow = false
}
//站点更多点击事件
function clickStationRow() {
  state.stationMoreShow = true
  state.gplotShow = false
}
function terminalControl() {
  state.terminalControlShow = true
  state.terminalControlForm = {
    // 定义 整机参数设置 初始值
    machineParam: {
      targetNode: 1,
      modeCtrl: 0,
      rxFreq: 2070,
      txFreq: 2260,
      txAtte: 30,
      // nodeId: undefined,
      nodeId: '1834120494834802689',
    },
    dataControl: {
      targetNode: undefined,
      modeCtrl: '',
      transModState: undefined,
    },
  }
}
//整机控制帧发送
function setMachineParam() {
  controlApi
    .setMachineParam(state.terminalControlForm.machineParam)
    .then((res) => {
      if (res.data.code == 200) {
        ElMessage.success('发送成功')
      } else {
        ElMessage.error('发送失败')
      }
    })
}
//查询当前选中的终端的体制-用于显示
function targetNodeChange(val) {
  controlApi.getTerminalMode({ terminalSeq: val }).then((res) => {
    if (res.data.code == 200) {
      state.terminalControlForm.dataControl.modeCtrl = res.data.data
    } else {
      ElMessage.error(res.data.message)
    }
  })
}
//数据传输控制
function setDataControlParam() {
  controlApi
    .setDataControlParam(state.terminalControlForm.dataControl)
    .then((res) => {
      if (res.data.code == 200) {
        ElMessage.success('发送成功')
      } else {
        ElMessage.error('发送失败')
      }
    })
}

// 日志类型过滤处理函数
function handleLogTypeChange(value) {
  console.log('日志类型过滤器变更:', value)
}

// 过滤后的日志数据计算属性
const filteredLogs = computed(() => {
  if (!state.logTypeFilter) {
    // 如果没有选择过滤类型，显示所有日志
    return sockeMessStore.tsSocket
  }

  // 根据选择的类型过滤日志
  return sockeMessStore.tsSocket.filter(log => {
    return log.source === state.logTypeFilter
  })
})

// 获取测控目标状态（与运算）
function getTargetStatus(target) {
  // 从 state.stationData 中筛选出 pageStationType == 'station' 的测控目标
  const stationTargets = state.stationData.filter(item =>
    item.stationInfo && item.pageStationType === 'station' || item.stationInfo && item.pageStationType === 'DD' || item.stationInfo && item.pageStationType === 'WRJ'
  )
  // 获取对应索引的测控目标
  const targetStation = target // targetIndex 从1开始，数组从0开始
  if (!targetStation || !targetStation.stationInfo) {
    return false // 如果没有找到对应的测控目标，返回false（红灯）
  }

  // 参考 stationDatastate 页面的红绿灯判断逻辑
  // 需要检查的布尔状态字段列表（参考 booleanList）
  let booleanFields = [], value = 1
  switch(targetStation.pageStationType){
	case 'station':
		value = 1
		booleanFields = stationDataLable.booleanList
		break
	case 'DD':
		value = 'aa'
		booleanFields = stationDataLable.DDKeyListBoolean
		break
	case 'WRJ':
		value = '49'
		booleanFields = stationDataLable.WRJKeyListBoolean
		break
  }
  // const booleanFields = [
  //   "yczbsd",    // 遥测载波锁定
  //   "ycwmsd",    // 遥测伪码锁定
  //   "clzbsd",    // 测量载波锁定
  //   "clwmsd",    // 测量伪码锁定
  //   "ycwtbsd",   // 遥测伪码同步锁定
  //   "ycztbsd",   // 遥测载波同步锁定
  // ]

  // 与运算：所有状态字段都为 1 才返回 true（绿灯）
  return booleanFields.every(field => {
    const fieldValue = targetStation[field]
    return fieldValue == value // 参考 stationDatastate 的判断逻辑：== 1 为绿灯
  })
}

// 显示资源统计模态框
function showResourceStats() {
  state.resourceStatsShow = true
  nextTick(() => {
    initScatterChart()
    initRadarChart()
  })
}

// 初始化散点图（经纬度分布）
function initScatterChart() {
  if (!scatterChart.value) return

  let chart = echarts.init(scatterChart.value)

  // 模拟资源地理位置数据
  const resourceLocationData = [
    [116.4074, 39.9042, '北京站点', '地基测控'],
    [121.4737, 31.2304, '上海站点', '海基测控'],
    [113.2644, 23.1291, '广州站点', '空基测控'],
    [114.0579, 22.5431, '深圳站点', '天基测控'],
    [120.1551, 30.2741, '杭州站点', '地基测控'],
    [118.7969, 32.0603, '南京站点', '海基测控'],
    [117.2008, 39.0842, '天津站点', '空基测控'],
    [106.5516, 29.5630, '重庆站点', '天基测控'],
  ]

  const scatterData = resourceLocationData.map(item => ({
    value: [item[0], item[1]],
    name: item[2],
    type: item[3]
  }))

  let option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#37A0EA',
      textStyle: {
        color: '#fff'
      },
      formatter: function(params) {
        return `${params.data.name}<br/>
                类型: ${params.data.type}<br/>
                经度: ${params.value[0]}<br/>
                纬度: ${params.value[1]}`
      }
    },
    xAxis: {
      type: 'value',
      name: '经度',
      nameTextStyle: {
        color: fontColor
      },
      axisLine: {
        lineStyle: {
          color: fontColor
        }
      },
      axisLabel: {
        color: fontColor,
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '纬度',
      nameTextStyle: {
        color: fontColor
      },
      axisLine: {
        lineStyle: {
          color: fontColor
        }
      },
      axisLabel: {
        color: fontColor,
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    series: [{
      name: '资源分布',
      type: 'scatter',
      data: scatterData,
      symbolSize: 12,
      itemStyle: {
        color: '#37A0EA',
        borderColor: '#fff',
        borderWidth: 2
      },
      emphasis: {
        itemStyle: {
          color: '#4FB3F1',
          borderColor: '#fff',
          borderWidth: 3
        }
      }
    }]
  }

  chart.setOption(option)
}

// 初始化雷达图（资源能力）
function initRadarChart() {
  if (!radarChart.value) return

  let chart = echarts.init(radarChart.value)

  let option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#37A0EA',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      data: ['地基测控', '海基测控', '空基测控', '天基测控'],
      textStyle: {
        color: fontColor,
        fontSize: 10
      },
      bottom: 5,
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 8
    },
    radar: {
      indicator: [
        { name: '测控目标', max: 100 },
        { name: '业务类型', max: 100 },
        { name: '工作体制', max: 100 },
        { name: '支持频段', max: 100 },
        { name: '测控距离', max: 100 }
      ],
      nameGap: 15,
      radius: '65%',
      center: ['50%', '45%'],
      name: {
        textStyle: {
          color: fontColor,
          fontSize: 11
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      splitArea: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      }
    },
    series: [{
      name: '资源能力',
      type: 'radar',
      data: [
        {
          value: [90, 85, 80, 75, 85],
          name: '地基测控',
          itemStyle: {
            color: '#37A0EA'
          },
          areaStyle: {
            color: 'rgba(55, 160, 234, 0.3)'
          }
        },
        {
          value: [80, 75, 85, 90, 70],
          name: '海基测控',
          itemStyle: {
            color: '#00D7FF'
          },
          areaStyle: {
            color: 'rgba(0, 215, 255, 0.3)'
          }
        },
        {
          value: [70, 90, 75, 85, 95],
          name: '空基测控',
          itemStyle: {
            color: '#4FB3F1'
          },
          areaStyle: {
            color: 'rgba(79, 179, 241, 0.3)'
          }
        },
        {
          value: [95, 80, 90, 70, 80],
          name: '天基测控',
          itemStyle: {
            color: '#1E5A8A'
          },
          areaStyle: {
            color: 'rgba(30, 90, 138, 0.3)'
          }
        }
      ]
    }]
  }

  chart.setOption(option)
}
</script>

<style lang="less" scoped>
.showOrHidden-box {
  position: absolute;
  right: 500px;
  top: 90px;
  // z-index: 999;
}

.scheduleClass {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  box-sizing: border-box;

  .task_schedule {
    height: auto;
    min-height: 45px;
    width: 100%;
    padding: 0 10px;
    margin-bottom: 10px;
    box-sizing: border-box;
  }
}

.chartClass {
  width: 100%;
  height: 100%;
  background: #022141c0;

  :deep(.vis-content > .vis-labelset .vis-inner) {
    font-weight: bolder;
    font-size: 16px;
    margin: 0 5px;
  }

  :deep(.vis-group) {
    >.vis-item.vis-selected {
      background: #2f557b !important;
    }

    .vis-item {
      background: rgb(29, 94, 142);
    }
  }
}

.pageRadio {
  position: absolute;
  top: 10%;
  left: 50%;
  transform: translate(-50%, 0);
  background: #022141c0;
  border: 1px solid #2e5175;
}

.targetForm_class {
  height: calc(100% - 55px);

  :deep(.el-form-item) {
    margin-bottom: 0;

    .el-form-item__label {
      padding-right: 8px;
    }
  }
}

.custom-table {
  :deep(.chooseRow) {
    background: rgba(55, 160, 234, 0.5) !important;
  }
}

.customInfo {
  position: absolute;
  bottom: 10px;
  cursor: pointer;
  color: #007cb4;
  right: calc(50% - 55px) !important;
}

/* 资源状态红绿灯样式 */
.target-status-container {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-light {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: inline-block;
  border: 2px solid #ccc;
}

.status-light.success {
  background-color: #67C23A;
  border-color: #67C23A;
  box-shadow: 0 0 8px rgba(103, 194, 58, 0.6);
}

.status-light.error {
  background-color: #F56C6C;
  border-color: #F56C6C;
  box-shadow: 0 0 8px rgba(245, 108, 108, 0.6);
}

.sucess_icon {
  width: 40px;
  height: 40px;
  background: url(/public/images/icon/green_light.png) 0 0/100% 100% no-repeat;
  // :deep(path) {
  //     fill: rgb(5, 199, 5);
  // }
}

.close_icon {
  width: 20px;
  height: 20px;
  background: url(/public/images/icon/red_light.png) 0 0/100% 100% no-repeat;
  // :deep(svg) {
  //     font-size: 30px;
  // }

  // :deep(path) {
  //     fill: rgb(226, 1, 1);
  // }
}

.popImg {
  position: absolute;
  left: 50%
}
</style>
