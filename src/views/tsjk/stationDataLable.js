export const failedList = [
    // {
    //     "key": "temp",//键名
    //     "label": "整机温度值",//显示名称
    //     "unit": "℃",//单位
    //     isBoolean: true,//是否是小灯
    //     factor: 1000,//单位显示倍数
    // },
    {
        "key": "yczbsd",
        "label": "遥测载波锁定",
        isBoolean: true,
    },
    {
        "key": "ycwmsd",
        "label": "遥测伪码锁定",
        isBoolean: true,
    },
    {
        "key": "clzbsd",
        "label": "测量载波锁定",
        isBoolean: true,
    },
    {
        "key": "clwmsd",
        "label": "测量伪码锁定",
        isBoolean: true,
    },
    {
        "key": "wxycsd",
        "label": "卫星遥测锁定指示",
        isBoolean: true,
    },
    {
        "key": "wxclsd",
        "label": "星锁",
        isBoolean: true,
    },
    {
        "key": "dqgztd",
        "label": "当前跟踪通道",
    },
    {
        "key": "jsykzts",
        "label": "接收遥控总条数",
    },
    {
        "key": "ykfnts",
        "label": "遥控发令条数",
    },
    {
        "key": "sxgzpl",
        "label": "上行工作频率",
        "unit": "MHz",
        factor: 1000000,
    },
    {
        "key": "xxgzpl",
        "label": "下行工作频率",
        "unit": "MHz",
        factor: 1000000,
    },
    {
        "key": "agc",
        "label": "遥测AGC电压",
        "unit": "V",
        factor: 1000,
    },
    {
        "key": "ycc",
        "label": "遥测C/N0",
        "unit": "dBHz",
        factor: 10,
    },
    {
        "key": "scdplpl",
        "label": "遥测多普勒",
        "unit": "KHz",
        factor: 100000,
    },
    {
        "key": "clagcdy",
        "label": "测量AGC电压",
        "unit": "V",
        factor: 1000,
    },
    {
        "key": "clcn",
        "label": "测量C/N0",
        "unit": "dBHz",
        factor: 10,
    },
    {
        "key": "clzpdplpl",
        "label": "测量多普勒",
        "unit": "KHz",
        factor: 100000,
    },
    {
        "key": "scsd",
        "label": "实测速度值",
        "unit": "m/s",
        factor: 10000,
    },
    {
        "key": "ycwtbsd",
        "label": "遥测位同步",
        isBoolean: true,
    },
    {
        "key": "ycztbsd",
        "label": "遥测帧同步",
        isBoolean: true,
    },
    {
        "key": "ebno",
        "label": "Eb/N0",
        "unit": "dB",
        factor: 10,
    },
    {
        "key": "cjwtbsdzs",
        "label": "测距位同步锁定指示",
        isBoolean: true,
    },
    {
        "key": "cjztbsdzs",
        "label": "测距帧同步锁定指示",
        isBoolean: true,
    },
    {
        "key": "scjlz",
        "label": "实测距离值",
        "unit": "km",
        factor: 10000,
    },
    { key: "remoteControlStatus", label: "远控/本控状态" },
    { key: "selfCheckStatus", label: "当前自检状态" },
    { key: "workModel", label: "工作模式" },
    { key: "selfCheckResult", label: "自检测试结果" },
    { key: "carrierLock", isBoolean: true, label: "载波锁定指示" },
    { key: "frameSyncLock", isBoolean: true, label: "帧同步锁定指示" },
    { key: "bitSyncLock", isBoolean: true, label: "位同步锁定指示" },
    { key: "channelStatus", isBoolean: true, label: "信道状态" },
    { key: "fireControlLaunchStatus", label: "安控发射状态" },
    { key: "outPutPower", "unit": "dBW", label: "输出功率" },
    { key: "k1SendCount", label: "K1发送计数" },
    { key: "k2SendCount", label: "K2发送计数" },
    { key: "k4SendCount", label: "K4发送计数" },
    { key: "k1RecvCount", label: "K1接收计数" },
    { key: "k2RecvCount", label: "K2接收计数" },
    { key: "k4RecvCount", label: "K4接收计数" },
    { key: "signalTelemetryOnlineStatus", label: "遥测 信号处理在线状态" },
    { key: "signalControlOnlineStatus", label: "安控 信号处理在线状态" },
    { key: "controlPowerAmplifierStatus", label: "安控功放状态" },
    { key: "feedPowerStatus", label: "馈电状态" },
    { key: "sxsdzt", label: "上行锁定状态" },
    { key: "xxsdzt", label: "下行锁定状态" },
    { key: "sxfszt", label: "上行发射状态" },
    { key: "xxfszt", label: "下行发射状态" },
    { key: "sxykpl", label: "上行遥控频率", unit: "MHz", },
    { key: "xxycpl", label: "下行遥测频率", unit: "MHz" },
    { key: "sxyksl", label: "上行遥控速率", unit: "kbps" },
    { key: "xxycsl", label: "下行遥测速率", unit: "kbps" },
    { key: "sxxhqd", label: "上行信号强度", unit: "V" },
    { key: "xxxhqd", label: "下行信号强度", unit: "V" },
]
// 站点信息，锁定/失锁列表
export const booleanList =
    [
        "yczbsd",
        "ycwmsd",
        "clzbsd",
        "clwmsd",
        // "wxycsd",
        "ycwtbsd",
        "ycztbsd",
        // "cjwtbsdzs",
        // "cjztbsdzs",
        // "wxclsd",
    ]
// 站点信息列表
export const infoList =
    [
        "dqgztd",
        "jsykzts",
        "sxgzpl",
        "ykfnts",
        "xxgzpl",
        "agc",
        "ycc",
        "scdplpl",
        "clagcdy",
        "clcn",
        "clzpdplpl",
        "scsd",
        "ebno",
        "scjlz"
    ]
// 导弹列表
export const DDKeyList = [
    "remoteControlStatus",
    "selfCheckStatus",
    "workModel",
    "selfCheckResult",
    "fireControlLaunchStatus",
    "outPutPower",
    "k1SendCount",
    "k2SendCount",
    "k4SendCount",
    "k1RecvCount",
    "k2RecvCount",
    "k4RecvCount",
    "signalTelemetryOnlineStatus",
    "signalControlOnlineStatus",
    "controlPowerAmplifierStatus",
    "feedPowerStatus"
]
// 导弹列表-锁
export const DDKeyListBoolean = [
    "carrierLock",
    // "frameSyncLock",
    "bitSyncLock",
    "channelStatus",
]
// 无人机列表
export const WRJKeyList = [
    // "sxsdzt",
    // "xxsdzt"
    'sxfszt', 'xxfszt', 'sxykpl', 'xxycpl', 'sxyksl', 'xxycsl', 'sxxhqd', 'xxxhqd'

]
//无人机列表-锁
export const WRJKeyListBoolean = [
    "sxsdzt",
    "xxsdzt"
]
