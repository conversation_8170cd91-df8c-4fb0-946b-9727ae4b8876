<template>
    <div> <el-tabs v-model="state.activeName" class="tabsClass" @tab-change="tabChange">
            <el-tab-pane label="整机状态帧" name="1" class="tab-pane">
                <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                    <el-descriptions-item v-for="(textKey) in wholeMachineFrame || []" :key="textKey" width="150px">
                        <template #label>
                            <div class="cell-item">
                                {{ labelText(textKey) }}
                            </div>
                        </template>
                        <span class="cell-text" v-if="isBoolean(textKey)">
                            <div class="sucess_icon" v-if="props.terminalDate?.machineFrame?.[textKey]"
                                style="height: 24px;width: 24px;"></div>
                            <div class="close_icon" v-else style="height: 24px;width: 24px;"></div>
                        </span>
                        <span class="cell-text" v-else>{{ props.terminalDate?.machineFrame?.[textKey] || 0 }}{{
                            unitText(textKey) }}</span>
                    </el-descriptions-item>
                </el-descriptions>
            </el-tab-pane>
            <el-tab-pane label="测控体制状态帧" name="2" class="tab-pane">
                <!-- 根据数据的不同，调用不同的状态帧字段（TODO） v-if="props.terminalDate?.droneFrame" -->
                <div v-if="props.terminalDate?.droneFrame" class="light_box">
                    <div :span="6" v-for="textKey in UAVsystemFrame" :key="textKey" class="icon_box"
                        >
                        <el-row style="width: 100%;">
                            <el-col :span="5"></el-col>
                            <el-col :span="5" style="line-height: 40px;"> {{ labelText(textKey) }}
                            </el-col>
                            <el-col :span="4"></el-col>
                            <el-col :span="10">
                                <span class="cell-text" v-if="isBoolean(textKey)">
                                    <div class="sucess_icon" v-if="props.terminalDate?.droneFrame?.[textKey]"
                                        style="height: 40px;width: 40px;"></div>
                                    <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>
                                </span>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                <!-- 航空数据帧 -->
                <!--  -->
                <div class="light_box" v-if="props.terminalDate?.spaceFrame">
                    <div v-for="textKey in spaceFlightSystemFrame" :key="textKey" class="icon_box">
                        <el-row style="width: 100%;">
                            <el-col :span="5"></el-col>
                            <el-col :span="5" style="line-height: 40px;"> {{ labelText(textKey) }}
                            </el-col>
                            <el-col :span="4"></el-col>
                            <el-col :span="10">
                                <span class="cell-text" v-if="isBoolean(textKey)">
                                    <div class="sucess_icon" v-if="props.terminalDate?.spaceFrame?.[textKey]"
                                        style="height: 40px;width: 40px;"></div>
                                    <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>

                                </span>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                <!-- 导弹测控数据帧-文字 -->
                <div v-if="props.terminalDate?.missileFrame" style="height: 50px;margin-top: 20px;">
                    <el-descriptions class="margin-top" title="" :column="3" size="small" :border="true">
                        <el-descriptions-item v-for="(textKey) in missileFrameKey || []" :key="textKey" width="150px">
                            <template #label>
                                <div class="cell-item">
                                    {{ labelText(textKey) }}
                                </div>
                            </template>
                            <span class="cell-text">{{ props.terminalDate?.missileFrame?.[textKey] || 0 }}{{
                                unitText(textKey) }}</span>
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
                <!-- 导弹测控数据帧-灯 -->
                <div class="light_box" v-if="props.terminalDate?.missileFrame" style="height: calc(100% - 70px);">
                    <div v-for="textKey in missileFrame" :key="textKey" class="icon_box" style="height: 30%;">
                        <el-row style="width: 100%;">
                            <el-col :span="5"></el-col>
                            <el-col :span="5" style="line-height: 40px;"> {{ labelText(textKey) }}
                            </el-col>
                            <el-col :span="4"></el-col>
                            <el-col :span="10">
                                <span class="cell-text" v-if="isBoolean(textKey)">
                                    <div class="sucess_icon" v-if="props.terminalDate?.missileFrame?.[textKey]"
                                        style="height: 40px;width: 40px;"></div>
                                    <div class="close_icon" style="height: 40px;width: 40px;" v-else></div>

                                </span>
                            </el-col>
                        </el-row>
                    </div>
                </div>

            </el-tab-pane>
            <el-tab-pane label="跨域中心抗干扰处理界面" name="3" class="tab-pane">
                <el-row style="height: 99%;">
                    <el-col :span="12" style="border: 1px solid #1d436d;height: 100%;">
                        <!-- 频谱图 -->
                        <div class="subTitle">
                            频谱图
                            <span class="sub_text">{{ props.terminalDate?.spectrumFrame?.typeValue || '--频段' }}</span>
                        </div>
                        <div style="height: calc(100% - 50px);width: 100%;" id="spectrumChart">
                        </div>
                    </el-col>
                    <el-col :span="12" style="height: 100%;padding-left: 5px;">
                        <div style="border: 1px solid #1d436d; height: calc(40% - 5px);margin-bottom: 5px;">
                            <div class="subTitle">
                                频谱感知
                            </div>
                            <div class="spectrum_perception">
                                <el-row style="width: 100%;">
                                    <el-col :span="12">
                                        <span class="lable_bg">干扰有无</span>
                                        <span class="content_bg">
                                            {{ spectrumText_bl('grxh') ? '有' : "无" }}
                                        </span>
                                    </el-col>
                                    <el-col :span="12">
                                    </el-col>
                                    <el-col :span="12">
                                        <span class="lable_bg">干扰频点</span>
                                        <span class="content_bg">{{ spectrumText('grpd') / 1000 || '--' }}MHz</span>
                                    </el-col>
                                    <el-col :span="12">
                                        <span class="lable_bg">干扰带宽</span>
                                        <span class="content_bg">
                                            <!-- {{ spectrumText('grdk') ? spectrumText('grdk') / 1000
                                            + 'MHz' : 0
                                            }} -->
                                            {{ spectrumText('grdk') / 1000 || '--' }}MHz

                                        </span>
                                    </el-col>
                                    <el-col :span="12">
                                        <span class="lable_bg">干扰强度</span>
                                        <span class="content_bg">{{ spectrumText('grqd') || '--' }}dB</span>
                                    </el-col>
                                    <el-col :span="12">
                                        <span class="lable_bg">干扰类型</span>
                                        <span class="content_bg">{{ spectrumText('grlx') ?
                                            grlxList[spectrumText('grlx')] : '--'
                                            }}</span>
                                    </el-col>
                                </el-row>
                            </div>
                        </div>
                        <div style="border: 1px solid #1d436d;height: 60%;">
                            <div class="subTitle">
                                抗干扰决策
                            </div>
                            <div class="spectrum_decision">
                                <el-row style="width: 100%;">
                                    <el-col :span="12">
                                        <span class="lable_bg">决策方式</span>
                                        <span class="content_bg" style="padding: 0;">
                                            <el-select size="mini" v-model="state.sendForm.decisionMethod" clearable>
                                                <el-option :value="0" label="无效"></el-option>
                                                <el-option :value="1" label="手动"></el-option>
                                                <el-option :value="2" label="自动"></el-option>
                                            </el-select></span>
                                    </el-col>
                                    <el-col :span="12">
                                        <span class="lable_bg">抗干扰手段选择</span>
                                        <span class="content_bg"><el-select size="mini" clearable
                                                v-model="state.sendForm.counterInterferenceMeasures">
                                                <el-option :value="0" label="关闭抗干扰"></el-option>
                                                <!-- <el-option :value="1">(不显示)</el-option> -->
                                                <el-option :value="2" label="体制级抗干扰"></el-option>
                                                <!-- <el-option :value="3">(不显示)</el-option> -->
                                                <el-option :value="4" label="信号级抗干扰"></el-option>
                                                <el-option :value="5" label="系统级抗干扰"></el-option>
                                                <!-- <el-option :value="6">自主决策</el-option> -->

                                            </el-select></span>
                                    </el-col>
                                    <el-col :span="24">
                                        <span class="lable_bg">抗干扰手段决策结果</span>
                                        <span class="content_bg" style="width: calc(48% + 10px);">{{
                                            spectrumText('kgrsdjcjg') ? kgrsdList[
                                                spectrumText('kgrsdjcjg')] : '--'
                                        }}</span>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-button size="mini"
                                            style="height: 30px;width: 60px; padding: 0 5px;float: right;"
                                            @click="sendAntiJamming">发送</el-button>
                                    </el-col>
                                </el-row>
                                <el-row class="row_content">
                                    <el-col :span="24"
                                        style="text-align: center;height: 25px;line-height: 25px;">系统弹性抗干扰决策结果</el-col>
                                    <el-col :span="12">
                                        <span class="lable_bg">开启状态</span>
                                        <span class="content_bg">{{ props.terminalDate.sysAntJResult?.sysAntj ? '开启' :
                                            '关闭'
                                            }}</span>
                                    </el-col>
                                    <!-- <el-col :span="12"></el-col> -->
                                    <!-- <el-col :span="12">
                                        <span class="lable_bg">链路代号</span>
                                        <span class="content_bg">{{ spectrumText('kgrsdjcjg') ? '开启' : '关闭' }}</span>
                                    </el-col> -->
                                    <el-col :span="12">
                                        <span class="lable_bg">测控体制</span>
                                        <span class="content_bg" v-if="props.terminalDate?.sysAntJResult?.sysAntj">{{
                                            modeList[props.terminalDate?.terminalConfigResult?.mode] || '--' }}</span>
                                        <span class="content_bg" v-else>{{ '--' }}</span>
                                    </el-col>
                                    <el-col :span="12">
                                        <span class="lable_bg">上行频率</span>
                                        <span class="content_bg" v-if="props.terminalDate?.sysAntJResult?.sysAntj">{{
                                            TCRText('txFreq') || '--' }}MHz</span>
                                        <span class="content_bg" v-else>{{ '--' }}MHz</span>
                                    </el-col>
                                    <el-col :span="12">
                                        <span class="lable_bg">下行频率</span>
                                        <span class="content_bg" v-if="props.terminalDate?.sysAntJResult?.sysAntj">{{
                                            TCRText('rxFreq') || '--' }}MHz</span>
                                        <span class="content_bg" v-else>{{ '--' }}MHz</span>
                                    </el-col>
                                </el-row>
                            </div>
                        </div>
                    </el-col>
                </el-row>




                <!-- <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                    <el-descriptions-item v-for="(textKey) in antiInterFrame || []" :key="textKey + 'antiInterFrame'"
                        width="150px">
                        <template #label>
                            <div class="cell-item">
                                {{ labelText(textKey) }}
                            </div>
                        </template>
                        <span class="cell-text" v-if="isBoolean(textKey)">
                            <el-icon v-if="props.terminalDate?.antiJammingFrame?.[textKey]" size="16px"
                                class="sucess_icon">
                                <CircleCheckFilled />
                            </el-icon>
                            <el-icon v-else size="16px" class="close_icon">
                                <CircleCloseFilled />
                            </el-icon>
                        </span>
                        <span class="cell-text" v-else>{{ props.terminalDate?.antiJammingFrame?.[textKey] || 0 }}</span>
                    </el-descriptions-item>
                </el-descriptions> -->
            </el-tab-pane>
            <!-- <el-tab-pane label="频谱数据帧" name="4" class="tab-pane">
                <el-form :model="props.terminalDate?.spectrumFrame" label-width="130px"
                    style="width: 100%;height: 50px">
                    <el-row style="width: 100%;">
                        <el-col :span="8" v-for="(textKey) in spectrumFrame || []" :key="textKey + 'spectrumFrame'">
                            <el-form-item :label="labelText(textKey)">
                                {{ props.terminalDate?.spectrumFrame?.[textKey] || 0 }}
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form> 
                <div style="height: calc(100% - 50px);width: 100%;" id="spectrumChart"></div>

            </el-tab-pane> -->
            <el-tab-pane label="建链申请数据帧" name="5" class="tab-pane">
                <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                    <el-descriptions-item v-for="(textKey) in createLinkFrame || []" :key="textKey + 'createLinkFrame'"
                        width="150px">
                        <template #label>
                            <div class="cell-item">
                                {{ labelText(textKey) }}
                            </div>
                        </template>
                        <span class="cell-text" v-if="isBoolean(textKey)">
                            <div class="sucess_icon" v-if="props.terminalDate?.createLinkDataFrame?.[textKey]"
                                style="height: 24px;width: 24px;"></div>
                            <div class="close_icon" v-else style="height: 24px;width: 24px;"></div>
                        </span>
                        <span class="cell-text" v-else>{{ props.terminalDate?.createLinkDataFrame?.[textKey] || 0 }}
                            {{ unitText(textKey)
                            }}</span>
                    </el-descriptions-item>
                </el-descriptions>
            </el-tab-pane>
            <el-tab-pane label="决策模块结果" name="6" class="tab-pane">
                <el-descriptions class="margin-top" title="" :column="2" size="small" :border="true">
                    <el-descriptions-item v-for="(textKey) in decisionModuleResult || []" :key="textKey" width="150px">
                        <template #label>
                            <div class="cell-item">
                                {{ labelText(textKey) }}
                            </div>
                        </template>
                        <span class="cell-text" v-if="isBoolean(textKey)">
                            <div class="sucess_icon" v-if="props.terminalDate?.sysAntJResult?.[textKey]"
                                style="height: 24px;width: 24px;"></div>
                            <div class="close_icon" v-else style="height: 24px;width: 24px;"></div>
                        </span>
                        <span class="cell-text" v-else>{{ props.terminalDate?.sysAntJResult?.[textKey] || 0 }}{{
                            unitText(textKey) }}</span>
                    </el-descriptions-item>
                </el-descriptions>
            </el-tab-pane>
        </el-tabs></div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, reactive, watch } from 'vue'
import * as terminalLable from "./terminalLable.js"
import * as controlApi from "@/service/API/system/control.js";
import { ElMessage } from 'element-plus';



const terminalList = terminalLable.failedList
const wholeMachineFrame = terminalLable.wholeMachineFrame// 整机状态帧
const UAVsystemFrame = terminalLable.UAVsystemFrame// 无人机测控体制状态帧
const spaceFlightSystemFrame = terminalLable.spaceFlightSystemFrame// 航天测控体制状态帧
const missileFrame = terminalLable.missileFrame// DD测控体制状态帧-灯
const missileFrameKey = terminalLable.missileFrameKey// DD测控体制状态帧keyvalue
const antiInterFrame = terminalLable.antiInterFrame// 抗干扰状态帧
const createLinkFrame = terminalLable.createLinkFrame// 建链申请数据帧
const spectrumFrame = terminalLable.spectrumFrame// 频谱数据帧
const decisionModuleResult = terminalLable.decisionModuleResult// 频谱数据帧
const grlxList = ["无干扰", '单音', '多音', '部分带', '脉冲', '扫频']
const kgrsdList = ["关闭抗干扰", ' ', '体制级抗干扰', ' ', '信号级抗干扰', '系统级抗干扰']
const modeList = ['不切换体制', '航天测控体制', '无人机测控体制', '导弹测控体制', '频谱感知']
const props = defineProps({
    terminalDate: {
        type: Object,

        default: () => {
            return {}
        },
    },
})

watch(() => props.terminalDate, () => {
    upDateChart()
}
)
const state = reactive({
    activeName: "1", spectrumData: [], sendForm: {
        decisionMethod: undefined,
        counterInterferenceMeasures: undefined,
        targetNode: undefined,
        switchLink: 0,
        switchLinkCode: 0
    }
})
var myChart
onMounted(() => {
    chartInit()

})
function chartInit() {
    var chartDom = document.getElementById('spectrumChart');
    myChart = echarts.init(chartDom);
    var option;
    option = {
        locale: "zh_CN",
        // title: {
        //     // text: '频谱数据帧',
        //     subtext: '--频段',
        //     // textStyle: {
        //     //     color: "#fff"
        //     // },
        // },
        tooltip: {
            trigger: 'axis'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        // toolbox: {
        //     right: 10,
        //     feature: {
        //         dataZoom: {
        //             yAxisIndex: 'none'
        //         },
        //         restore: {},
        //         saveAsImage: {}
        //     }
        // },
        dataZoom: [{
            type: 'inside'
        }],
        xAxis: {
            type: 'category',
            // splitLine: {
            //     lineStyle: {
            //         color: ['#d3d3d32b', '#ddd']
            //     }
            // },
            axisLabel: {
                formatter: '{value} MHz',
                color: "#fff"
            },

            data: []
        },
        yAxis: {
            type: 'value',
            splitLine: {
                // show:false,//取消间隔线
                lineStyle: {
                    // 间隔线颜色
                    color: ['#d3d3d32b']
                }
            },
            axisLabel: {
                formatter: '{value} dB',
                color: "#fff"
            },
        },
        series: [
            {
                type: 'line',
                data: [],
                lineStyle: {
                    // 1bd1c8
                    //频谱线的颜色
                    color: "#1bd1c8"
                }
            },
        ]
    };

    myChart.setOption(option);
}
function upDateChart() {
    if (!myChart || !props.terminalDate.spectrumFrame) {
        return
    }
    let spectrumData = [...props.terminalDate.spectrumFrame.spectrumList]
    let interval = props.terminalDate.spectrumFrame.interval
    let startFrequency = props.terminalDate.spectrumFrame.startFrequency
    // let subTitle = props.terminalDate.spectrumFrame.subtext

    let option = {

        xAxis: {
            data: []
        },
        series: [
            {
                data: []
            }
        ]
    }
    spectrumData.forEach((dataItem, index) => {
        option.xAxis.data.push(startFrequency + index * interval)
        option.series[0].data.push(dataItem)
    });
    myChart.setOption(option)

}
// key转文字
function labelText(key) {
    let row = terminalList.find(item => item.key == key)
    return row ? row.label : "未定义"
}
//单位文字
function unitText(key) {
    let row = terminalList.find(item => item.key == key)
    return row?.unit ? ' ' + row.unit : " "
}


function isBoolean(key) {
    let row = terminalList.find(item => item.key == key)
    return row.isBoolean || false
}
function tabChange() {
    setTimeout(() => {
        myChart.resize()
    }, 200);
}
function spectrumText(key) {
    let t = props.terminalDate?.antiJammingFrame?.[key]
    if (t === 0) {
        t = "0"
    }
    return t
}
function TCRText(key) {
    let t = props.terminalDate?.terminalConfigResult?.[key]
    if (t === 0) {
        t = "0"
    }
    return t
}
function spectrumText_bl(key) {
    return props.terminalDate?.antiJammingFrame?.[key]
}
function sendAntiJamming() {
    state.sendForm.targetNode = props.terminalDate.nodeId
    controlApi.antiJamming(state.sendForm).then(res => {
        if (res.data.code == 200) {
            ElMessage.success("发送成功")
        } else {
            ElMessage.error("发送失败")
        }
    })
}
</script>

<style scoped lang="less">
.tab-pane {
    height: 48vh;
    overflow: auto;
    margin-top: 10px;

    :deep(.el-form-item) {
        margin-bottom: 0px;
    }

    :deep(div) {
        box-sizing: border-box;
    }
}

.sucess_icon {
    width: 40px;
    height: 40px;
    background: url(/public/images/icon/green_light.png) 0 0/100% 100% no-repeat;
    // :deep(path) {
    //     fill: rgb(5, 199, 5);
    // }
}

.close_icon {
    width: 20px;
    height: 20px;
    background: url(/public/images/icon/red_light.png) 0 0/100% 100% no-repeat;
    // :deep(svg) {
    //     font-size: 30px;
    // }

    // :deep(path) {
    //     fill: rgb(226, 1, 1);
    // }
}

.icon_box {
    height: 25%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    box-sizing: border-box;
    padding: 5px;
    flex-direction: column;
    width: 50%;
}

.subTitle {
    width: 100%;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 18px;
    font-weight: bold;
    position: relative;

    .sub_text {
        font-size: 12px;
        font-weight: 100;
        position: absolute;
        right: 20px;
    }
}

.spectrum_perception {
    height: calc(100% - 50px);
    width: 100%;
    padding: 0 20px 30px 20px;
    display: flex;
    align-items: flex-end;
    overflow-y: auto;
}

.spectrum_decision {
    height: calc(100% - 50px);
    width: 100%;
    padding: 0 20px 40px 20px;
    display: flex;
    align-items: flex-end;
    overflow-y: auto;
    flex-wrap: wrap;

    :deep(.el-select) {
        height: 25px;
        vertical-align: bottom;
        width: 100%;
    }

    :deep(.el-select__wrapper) {
        height: 100%;
        min-height: unset !important;
        padding: 0 !important;
    }
}

.lable_bg {
    background: rgba(0, 174, 255, 0.4);
    border: 2px solid #003a66;
    display: inline-block;
    width: 48%;
    line-height: 25px;

    text-align: center;

}

.content_bg {
    // background: rgba(0, 174, 255, 0);
    border: 2px solid #003a66;
    display: inline-block;
    width: 48%;
    line-height: 25px;

    padding: 0 5px;
    box-sizing: border-box;

}

.row_content {
    border: 3px solid #003a66;
    width: 100%;
}

.light_box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
}
</style>