<!--
 * @Author: JRX <EMAIL>
 * @Date: 2024-06-04 13:20:09
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-06-27 09:01:07
 * @FilePath: \wrxtzhglrj\src\views\system\components\userManage\userManage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <customPopup left="10px" top="80px" width="24%" height="90%">
    <template #header>
      <span class="list_icon"></span>
      <span class="title-font">用户列表</span>
      <el-button style="position: absolute; right: 10px" type="primary" icon="CirclePlus"
        @click="state.currentUser = {}">
        创建用户
      </el-button>
    </template>
    <template #content>
      <div style="display: flex; margin-bottom: 5px; height: 30px">
        <el-input placeholder="请输入关键字" v-model="state.userKeyword"></el-input>
        <el-button type="primary" icon="Search" @click="getUserList()" class="transparentBtn" style="margin-left: 15px">
          搜索
        </el-button>
      </div>
      <div style="height: calc(100% - 30px)" class="userList">
        <el-row v-for="(item, index) in state.userList" :key="index" class="user_row"
          :class="{ isChecked: item.id == state.currentUser.id }" @click="getUserById(item.id)">
          <el-col :span="5">{{ item.username }}</el-col>
          <el-col :span="5">{{ item.name }}</el-col>
        </el-row>
      </div>
    </template>
  </customPopup>
  <customPopup left="25%" top="80px" width="75%" height="90%">
    <template #header>
      <span class="user_icon"></span>
      <span class="title-font">{{ !state.currentUser.id ? "创建用户" : state.currentUser.name }}</span>
      <!-- <el-button style="margin-left: 1%" type="primary" icon="CirclePlus" @click="state.addUserDialogShow = true">
        创建用户
      </el-button> -->
    </template>
    <template #content>
      <el-form ref="formRef" :model="state.currentUser" :rules="rules" label-width="25%" class="user_form">
        <el-form-item label="用户名" prop="name">
          <el-input placeholder="请填写用户名" v-model="state.currentUser.name" style="width: 50%"></el-input>
        </el-form-item>
        <el-form-item label="登录账号" prop="username">
          <el-input placeholder="请填写登录账号" v-model="state.currentUser.username" style="width: 50%"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-if="!state.currentUser.id" placeholder="请填写密码" type="password" show-password
            v-model="state.currentUser.password" style="width: 50%"></el-input>
          <el-button v-else @click="() => {
              password.show = true;
              password.userId = state.currentUser.id;
            }
            ">
            修改密码
          </el-button>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="state.currentUser.gender">
            <el-radio :label="1">男</el-radio>
            <el-radio :label="0">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input placeholder="请填写邮箱" v-model="state.currentUser.email" style="width: 50%"></el-input>
        </el-form-item>
        <el-form-item label="电话号码" prop="phone">
          <el-input placeholder="请填写电话号码" v-model="state.currentUser.phone" style="width: 50%"></el-input>
        </el-form-item>
        <!-- <el-form-item label="启用账号" prop="status" v-if="state.currentUser.id">
          <el-radio-group v-model="state.currentUser.status" @change="statusChange">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label=" ">
          <el-button @click="changeInfo(formRef)">{{ state.currentUser.id ? "修改" : "确认" }}</el-button>
          <el-button @click="getUserById(state.currentUser.id)">重置</el-button>
          <el-button type="primary" v-if="state.currentUser.id" @click="deleteUser" :loading="password.loading">
            删 除
          </el-button>
        </el-form-item>
      </el-form>
    </template>
  </customPopup>
  <el-dialog title="修改密码" width="600px" v-model="password.show" @close="passwordClose">
    <el-form label-width="100px">
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input placeholder="请填写旧密码" v-model="password.oldPassword" type="password" show-password
          style="width: 80%"></el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input placeholder="请填写新密码" v-model="password.newPassword" type="password" show-password
          style="width: 80%"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button size="small" @click="passwordClose">取 消</el-button>
        <el-button size="small" type="primary" @click="passwordHandle" :loading="password.loading">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, ref, onMounted, onUnmounted, nextTick } from "vue";
import customPopup from "@/components/customPopup.vue";
import * as dataApi from "@/service/API/system/userManage.js";
import { ElMessage, ElMessageBox } from "element-plus";

const formRef = ref();
const state = reactive({
  addUserDialogShow: false,
  userList: [],
  userKeyword: "",
  currentUser: {},
});
const password = reactive({
  oldPassword: "",
  newPassword: "",
  userId: "",
  show: false,
  loading: false,
});
const rules = {
  name: [{ required: true, message: "请输入用户名称（中文）" }],
  username: [{ required: true, message: "请输入登账号（英文）" }],
  password: [{ required: true, message: "请输入登账密码 " }],
  gender: [{ required: true, message: "请选择性别" }],
};
onMounted(() => {
  getUserList();
});
//获取用户列表
function getUserList(id) {
  let obj = {};
  state.userKeyword && (obj.keyword = state.userKeyword);
  dataApi.getUserList(obj).then((res) => {
    if (res.data.code == 200) {
      state.userList = res.data.data.records;
      if (state.userList.length > 0) {
        if (id) {
          getUserById(id);
        } else {
          getUserById(state.userList[0].id);
        }
      }
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
//根据id获取用户信息
function getUserById(id) {
  dataApi.getUserInfo({ id: id }).then((res) => {
    if (res.data.code == 200) {
      state.currentUser = res.data.data;
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
// 关闭密码修改弹窗
function passwordClose() {
  password.oldPassword = "";
  password.newPassword = "";
  password.userId = "";
  password.loading = false;
  password.show = false;
}
function changeInfo(formRef) {
  formRef.validate((valid, fields) => {
    if (valid) {
      changeInfoHandle();
    } else {
      console.log("error submit!", fields);
    }
  });
}
//新增用户信息
function addUserInfo() {
  state.currentUser.status = 1;
  dataApi.addUserInfo(state.currentUser).then((res) => {
    if (res.data.code == 200) {
      ElMessage.success("信息新增成功");
      getUserList();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
//更新用户信息（除密码）
function changeInfoHandle() {
  if (!state.currentUser.id) {
    addUserInfo();
    return;
  }
  let obj = { ...state.currentUser };
  delete obj.password;
  dataApi.putUserInfo(obj).then((res) => {
    if (res.data.code == 200) {
      ElMessage.success("信息修改成功");
      // getUserById(obj.id);
      getUserList(obj.id);
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
//修改密码处理
function passwordHandle() {
  if (!password.oldPassword) {
    ElMessage.error("请输入旧密码");
    return;
  } else if (!password.newPassword) {
    ElMessage.error("请输入新密码");
    return;
  }
  let obj = {
    oldPassword: password.oldPassword,
    newPassword: password.newPassword,
    userId: password.userId,
  };
  password.loading = true;
  dataApi.changePassword(obj).then((res) => {
    password.loading = false;
    if (res.data.code == 200) {
      ElMessage.success("密码修改成功");
      getUserById(obj.userId);
      passwordClose();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
//修改账号状态
function statusChange() {
  dataApi.updateUserStatus({ id: state.currentUser.id }).then((res) => {
    if (res.data.code == 200) {
      ElMessage.success("状态编辑成功");
      getUserById(state.currentUser.id);
      // passwordClose();
    } else {
      ElMessage.error(res.data.message);
    }
  });
}
//删除用户
function deleteUser() {
  ElMessageBox.confirm("继续将删除用户，是否继续?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    dataApi.delUser({ id: state.currentUser.id }).then((res) => {
      if (res.data.code == 200) {
        ElMessage.success("删除用户成功");
        getUserList();
        // passwordClose();
      } else {
        ElMessage.error(res.data.message);
      }
    });
  });
}
</script>

<style scoped lang="less">
.title-font {
  font-size: 18px;
  font-weight: bolder;
  color: #c6cdd6;
}

.userList {
  width: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  padding: 20px 0;
  box-sizing: border-box;
}

.user_row {
  line-height: 30px;
  cursor: pointer;
  padding: 0px 10px;
}

.isChecked {
  background: #285472;
}

.user_row:hover {
  background: #285472;
}

.user_form .el-form-item {
  margin-top: 4%;
}
</style>
