<template>
  <div class="userDispatchDesk">
    <el-container>
      <el-header class="dispatch_header">模拟单域管控中心</el-header>
      <el-main class="dispatch_body">
        <div class="custom-table-box">
          <el-tabs class="tabsClass" v-model="tabName">
            <el-tab-pane
              :label="key"
              :name="key"
              v-for="(item, key) in areaNames"
              :key="key"
              style=""
			  :disabled="loading"
            >
              <el-table
                ref="dispatchTableRef"
                :className="key"
                :data="item || []"
                class="custom-table"
                style="width: 100%"
                height="700"
                @selection-change="handleSelectionChange"
				v-loading="loading"
				element-loading-background="rgba(122, 122, 122, 0.1)"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column
                  width="30"
                  type="index"
                  align="center"
                ></el-table-column>
                <el-table-column
                  label="任务名"
                  prop="task.taskName"
                  align="center"
                >
                </el-table-column>
                <el-table-column label="资源域" prop="areaName" align="center">
                  <template v-slot="{ row }">
                    <span>{{ areaName(row.areaId) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="设备名称" prop="name" align="center" />
                <el-table-column
                  label="设备占用开始时间"
                  prop="startTime"
                  align="center"
                />
                <el-table-column
                  label="设备占用结束时间"
                  prop="endTime"
                  align="center"
                />

                <el-table-column
                  label="综合覆盖率"
                  prop="totalCover"
                  align="center"
                  width="100"
                >
                  <template v-slot="{ row }">
                    {{
                      row.totalCover
                        ? parseInt(row.totalCover * 10000) / 100 + '%'
                        : 0
                    }}
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-main>
      <el-footer class="dispatch_footer">
        <!-- <el-button class="dispatch_button" @click="getData">接收</el-button> -->
        <el-button
          class="dispatch_button"
          @click="dispatchClick"
          :loading="loading"
          >二级调度</el-button
        >
      </el-footer>
    </el-container>
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus'
import { onMounted, onUnmounted, ref, reactive, watch } from 'vue'
// import {getResourceList} from "@/api"
import { postDispatch } from '@/service/API/system/userDispatchDesk.js'
import { sendResult } from '@/service/API/system/resourceDispatch.js'
import * as areaApi from '@/service/API/system/areaManage.js'
// import { useSockeMessStore } from '@/stores/index'
// const sockeMessStore = useSockeMessStore()

//可调度资源列表
const dispatchTableData = ref([])
const dispatchTableRef = ref(null)
const loading = ref(false)
const tabName = ref(null)
const areaList = ref([])
let progress = 0
var storageData = {}
let intervalId = 0
let endAreaNames = []
onMounted(() => {
  getAreaList()
  intervalId = setInterval(() => {
    let zyddList = localStorage.getItem('zydd_equipments')
    zyddList = JSON.parse(zyddList)
    let data = zyddList.equipments || zyddList.areas
    if (dispatchTableData.value.length != data.length) {
      // 不再清空areaNames，保持tab栏结构
      updateTableData()
    }
  }, 1000)
})
onUnmounted(() => {
  clearInterval(intervalId)
})
const handleSelectionChange = () => {
  // debugger
}
// 发送选中数据到后端，
function dispatchClick() {
  loading.value = true
  let sec = dispatchTableRef.value.find(
    (item) => item.className == tabName.value
  )

  let list = sec.getSelectionRows()

  if (!list || list.length == 0) {
    // ElMessage.error('请选择进行调度的资源')
    loading.value = false
    ElMessage({
      message: '请选择进行调度的资源',
      type: 'error',
    })
    return
  }
  // debugger
  let pr = { ...storageData }
  pr.equipments = storageData.equipments.filter(
    (item) => areaName(item.areaId) == tabName.value
  )

  // // 计算所有tab下表格的总条数之和
  // let totalCount = 0
  // Object.keys(areaNames.value).forEach((areaKey) => {
  //   totalCount += areaNames.value[areaKey].length
  // })
  // console.log(
  //   '二级调度 - 所有tab总条数:',
  //   totalCount,
  //   'tab详情:',
  //   areaNames.value
  // )

  let _parma = {
    ...pr,
    isRandom: 0,
    total: count,
    progress,
  }
  if (areaNames.value[tabName.value] && areaNames.value[tabName.value].length) {
    areaNames.value[tabName.value] = []
  }
  sec.data.forEach((item) => {
    let has = list.find((jtem) => jtem.onlyIndex == item.onlyIndex)
    if (has) {
      item.schedule = 1
    } else {
      item.schedule = 0
    }
  })

  // type 1：一级调度 2：二次调度
  if (_parma.type == 1) {
    _parma.equipments = sec.data
    _parma.areas = undefined
  } else if (_parma.type == 2) {
    _parma.equipments = undefined
    _parma.areas = sec.data
  }
  sendResult(_parma).then((res) => {
    loading.value = false
    if (res.data.code == 200) {
      ElMessage({
        message: '调度成功',
        type: 'success',
      })

      // dispatchTableRef.value.clearSelection()
      // localStorage.setItem('zydd_equipments', '')
      storageData.equipments = storageData.equipments.filter(
        (item) => areaName(item.areaId) != tabName.value
      )
      localStorage.setItem('zydd_equipments', JSON.stringify(storageData))
      dispatchTableData.value = []
      progress = res.data.data
      // 移除错误的push操作，tabName.value是字符串不是数组
      if (progress >= 100) {
        progress = 0
      }

      // 修复：使用updateTableData()而不是getData()，避免数据重复
      // getData()会重新初始化，而updateTableData()会智能更新现有数据
      updateTableData()
    } else {
      ElMessage({
        message: res.data.message,
        type: 'error',
      })
    }
  })
}
let areaNames = ref({})
let count = 0
let isUpdating = false // 防止数据更新时的重复调用

// 新增：更新表格数据函数，智能处理tab栏
function updateTableData() {
  if (isUpdating) {
    console.log('updateTableData: 正在更新中，跳过重复调用')
    return
  }

  isUpdating = true
  console.log('updateTableData: 智能更新数据和tab栏')
  let zyddList = localStorage.getItem('zydd_equipments')

  if (zyddList) {
    zyddList = JSON.parse(zyddList)
    dispatchTableData.value = zyddList.equipments || zyddList.areas
    count = count == 0 ? dispatchTableData.value.length : count

    // 检查是否已有tab栏
    const existingAreaNames = Object.keys(areaNames.value)
    const hasExistingTabs = existingAreaNames.length > 0

    if (hasExistingTabs) {
      console.log('已有tab栏，保持结构并更新数据')
      // 重置所有区域的数据为空数组，保持tab结构
      existingAreaNames.forEach((areaNameKey) => {
        areaNames.value[areaNameKey] = []
      })

      // 重新分配新数据到对应的区域
      dispatchTableData.value.forEach((item, index) => {
        item.onlyIndex = index
        const _areaName = areaName(item.areaId)

        // 如果这个区域在现有tab中存在，就添加数据
        if (areaNames.value.hasOwnProperty(_areaName)) {
          areaNames.value[_areaName].push(item)
        }
      })
    } else {
      console.log('没有tab栏，创建新的tab栏和数据')
      // 没有tab栏时，创建新的tab栏（修复：正确加载所有数据）
      endAreaNames = []
      dispatchTableData.value.forEach((item, index) => {
        item.onlyIndex = index
        const _areaName = areaName(item.areaId)
        if (index == 0) {
          tabName.value = _areaName
        }
        // 修复：分离区域初始化和数据添加的逻辑
        if (!endAreaNames.includes(_areaName)) {
          // 第一次遇到这个区域，初始化数组
          areaNames.value[_areaName] = []
          endAreaNames.push(_areaName)
        }
        // 无论是否第一次遇到，都要添加数据到对应区域
        areaNames.value[_areaName].push(item)
      })
    }

    storageData = zyddList
    isUpdating = false // 重置更新标志
  } else {
    // 如果没有数据
    if (Object.keys(areaNames.value).length > 0) {
      // 有tab栏时，将所有区域的数据设为空数组
      Object.keys(areaNames.value).forEach((areaNameKey) => {
        areaNames.value[areaNameKey] = []
      })
    }
    ElMessage({
      message: '无数据',
      type: 'warning',
    })
  }

  isUpdating = false // 重置更新标志
}

// 获取列表数据 需要在资源调度时进行一级调度（初始化时使用）
function getData() {
  console.log('getData: 初始化数据和tab栏', areaNames.value)
  // dispatchTableRef.value && dispatchTableRef.value.clearSelection()
  let zyddList = localStorage.getItem('zydd_equipments')

  if (zyddList) {
    zyddList = JSON.parse(zyddList)
    dispatchTableData.value = zyddList.equipments || zyddList.areas
    count = count == 0 ? dispatchTableData.value.length : count
    dispatchTableData.value.forEach((item, index) => {
      item.onlyIndex = index
      const _areaName = areaName(item.areaId)
      if (index == 0) {
        tabName.value = _areaName
      }
      // 修复：分离区域初始化和数据添加的逻辑
      if (!endAreaNames.includes(_areaName)) {
        // 第一次遇到这个区域，初始化数组
        areaNames.value[_areaName] = []
        endAreaNames.push(_areaName)
      }
      // 无论是否第一次遇到，都要添加数据到对应区域
      areaNames.value[_areaName].push(item)
    })
    storageData = zyddList
  } else {
    ElMessage({
      message: '无数据',
      type: 'warning',
    })
  }
}

function areaName(id) {
  let area = areaList.value.find((item) => {
    return item.id == id
  })
  return area.areaName || ''
}
//获取资源域列表
function getAreaList() {
  areaApi.getAreaList({ page: 1, size: 999, areaType: 1 }).then((res) => {
    if (res.data.code == 200) {
      areaList.value = res.data.data.records
      getData()
    } else {
      ElMessage({
        message: res.data.message,
        type: 'error',
      })
    }
  })
}
</script>
<style lang="less" scoped>
* {
  color: wheat;
  box-sizing: border-box;
}

.userDispatchDesk {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: url('/public/images/bgBorder1/模型库-bg.jpg') 0 0/100% 100%;
  z-index: 3000;
}

.dispatch_header {
  text-align: left;
  padding-left: 150px;
  font-size: 25px;
  font-weight: bold;
  height: 70px;
  line-height: 50px;
  background: url('/public/images/header1/模型库-top-bg.png') 0 0 / 100% 100%;
}

.dispatch_body {
  height: calc(100vh - 70px - 70px);
}

.dispatch_footer {
  height: 70px;
  text-align: center;
}

.custom-table-box {
  width: 100%;
  height: 100%;
  border: 1px solid #59c5cd !important;
  border-radius: 10px;
  padding: 20px;
}

.dispatch_button {
  background: #31be9b30 !important;

  &:hover {
    background: #31be9b !important;
  }
}
</style>
