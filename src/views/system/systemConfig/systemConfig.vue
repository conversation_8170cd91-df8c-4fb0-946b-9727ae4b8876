<template>
    <div class="systemSet_box">
        <customPopup width="100%" height="100%" left="0" top="0">
            <template #header>
                <span class="system_config_icon"></span>
                <span class="title-font">系统设置</span>
                <!-- <div style="position: absolute; right: 2%">
                    <el-button plain @click="close">关闭</el-button>
                </div> -->
            </template>
            <template #content>
                <div style="height: 100%;width: 100%;">
                    <el-row :gutter="10" style="height: 100%;">
                        <el-col :span="6" style="height: 100%;border-right: 2px solid #22568b">
                            <div class="configItem" :class="state.chooseSet == index ? 'chooseBox' : ''"
                                v-for="(item, index) in state.configList" @click="configItem(item, index)">{{ item.name
                                }}
                            </div>
                        </el-col>
                        <el-col :span="18" style="padding: 20px 20px;box-sizing: border-box;">
                            <!-- time_range,dropdown,boolean,integer -->
                            <div style="height: calc(100% - 40px);width: 100%">
                                <el-form label-width="150px" label-position="left">
                                    <el-form-item :label="state.configForm.name + '：'">
                                        <el-select v-model="state.configForm.showValue" style="width: 60%;"
                                            v-if="state.configForm.configType == 'integer'">
                                            
                                            <el-option v-for="(value, key) in dictTypeList['timeSliceUnit']" :key="key"
                                                :label="value" :value="key" />
                                        </el-select>
                                        <div v-if="state.configForm.configType == 'time_range'">
                                            <el-date-picker v-model="state.configForm.showValue[0]" type="date"
                                                :clearable="false" style="width: 40%;" value-format="YYYY-MM-DD" />
                                            -
                                            <el-date-picker v-model="state.configForm.showValue[1]" type="date"
                                                :clearable="false" style="width: 40%;" value-format="YYYY-MM-DD" />
                                        </div>
                                        <div v-if="state.configForm.configType == 'postions'">
                                            <el-row> <el-col :span="12">
                                                    经度：<el-input-number v-model="state.configForm.showValue[0]"
                                                        :controls="false" style="width: 60%;"></el-input-number>
                                                </el-col>
                                                <el-col :span="12">
                                                    纬度：<el-input-number v-model="state.configForm.showValue[1]"
                                                        :controls="false" style="width: 60%;"></el-input-number>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </el-form-item>
                                </el-form>
                            </div>
                            <div style="height: 40px;line-height: 40px;width: 100%;text-align: center;">
                                <el-button @click="close">关 闭</el-button>
                                <el-button @click="saveUserConfig">保 存</el-button>
                            </div>
                        </el-col>
                    </el-row>

                </div>

            </template>

        </customPopup>
    </div>
</template>

<script setup>
import { onMounted, reactive, ref, defineEmits } from 'vue'
import customPopup from "@/components/customPopup.vue";
import * as dataApi from "@/service/API/system/userManage.js";
import { ElMessage } from 'element-plus';

const dictTypeList = window.dictTypeList;

const emits = defineEmits(["close"]);

const state = reactive({
    showSystemSet: false,
    timeSliceUnit: undefined,
    chooseSet: 1,
    configList: [],
    configForm: {},
})
onMounted(() => {
    getUserConfig()
})
//获取用户设置
function getUserConfig() {
    dataApi.getUserConfig().then(res => {
        if (res.data.code == 200) {
            state.configList = res.data.data
            configItem(state.configList[0], 0)
        } else {
            ElMessage.error(res.data.message)

        }
    })
}
// 
function configItem(item, index) {
    state.chooseSet = index
    state.configForm = item
    if (item.configType == "time_range") {
        state.configForm.showValue = item.value.split("|")
    }else if (item.configType == "postions") {
        state.configForm.showValue = item.value.split(",")
    } else {
        state.configForm.showValue = item.value
    }
}
//保存用户设置
function saveUserConfig() {
    if (state.configForm.configType == "time_range") {
        if (!state.configForm.showValue[0] || !state.configForm.showValue[1]) {
            ElMessage.error("开始时间或结束时间不能为空")
            return
        }
        if (state.configForm.showValue[0] > state.configForm.showValue[1]) {
            ElMessage.error("开始时间不能大于结束时间")
            return
        }
        state.configForm.value = state.configForm.showValue.join("|")
    }else if (state.configForm.configType == "postions") {
        if ((!state.configForm.showValue[0] && state.configForm.showValue[0] !== 0) || (!state.configForm.showValue[1] && state.configForm.showValue[1] !== 0)) {
            ElMessage.error("经度或纬度不能为空")
            return
        }
        if ((state.configForm.showValue[0] > 180 || state.configForm.showValue[0] < -180) ||
            (state.configForm.showValue[1] > 90 || state.configForm.showValue[1] < -90)
        ) {
            ElMessage.error("请在正确的范围填写经纬度")
            return
        }
        state.configForm.value = state.configForm.showValue.join(",")
    }else {
        state.configForm.value = state.configForm.showValue
    }

    dataApi.saveUserConfig(state.configForm).then(res => {
        if (res.data.code == 200) {
            ElMessage.success("修改成功")
            if(state.configForm.configType == "postions"){
                window.controlCenterPos = state.configForm.value ? state.configForm.value.split(",") : []  
                localStorage.setItem("controlCenterPos", JSON.stringify(window.controlCenterPos))
            }
        } else {
            ElMessage.error(res.data.message)
        }
    })
}
function close() {
    emits("close")
}
</script>

<style lang='less' scoped>
.systemSet_box {
    position: fixed;
    top: 20%;
    left: 25%;
    z-index: 1999;
    height: 50%;
    width: 50%;
}

.chooseBox {
    background: #22578b8a;
}

.configItem {
    height: 30px;
    width: 100%;
    line-height: 30px;
    box-sizing: border-box;
    padding: 0 3px;
    border-radius: 2px;
    cursor: pointer;

    &:hover {
        background: rgba(34, 87, 139, 0.267);
    }
}
</style>