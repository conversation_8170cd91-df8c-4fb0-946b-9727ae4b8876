
<template>
  <customWindow heardeState="false">
    <template #content>
      <el-container style="height: 830px; width: 100%; margin-top: 10px">
        <el-aside>
          <el-menu :default-active="activeIndex" router @select="routerTo" text-color="#f7feff" class="menu">
            <el-menu-item :index="item.path" v-for="item of defaultMenuList" :key="item.path" style="font-size: 18px">
              <template #title>
                <!-- <img :src="item.imgUrl" alt="" class="img" /> -->
                <span class="img" :class="item.imgUrlName"></span>
                <span>{{ item.title }}</span>
              </template>
            </el-menu-item>
          </el-menu>
        </el-aside>
        <el-main style="position: relative;">
          <!-- 三级路由 -->
          <router-view></router-view>
        </el-main>
      </el-container>
    </template>
  </customWindow>
</template>
<script setup>
import { reactive, onMounted, onUnmounted, ref, nextTick, watch } from "vue";
import customWindow from "@/components/customWindow.vue";

//参数
let activeIndex = ref("");
activeIndex.value = location.hash.slice(1);
// 菜单
let defaultMenuList = [
  {
    title: "数据接入设置",
    name: "dataAccess",
    path: "/home/<USER>/dataAccess",
    // imgUrl: "../../public/images/header/current-time.png",
    imgUrlName: "current_time"
  },
  // {
  //   title: "数据分发与订阅配置",
  //   name: "dataDistribution",
  //   path: "/home/<USER>/dataDistribution",
  //   imgUrl: "../../public/images/header/data-reduction.png",
  // },
  {
    title: "区域管理",
    name: "areaManage",
    path: "/home/<USER>/areaManage",
    // imgUrl: "../../public/images/header/play-back.png",
    imgUrlName: "play_back"
  },
  {
    title: "日志管理",
    name: "dataLog",
    path: "/home/<USER>/dataLog",
    // imgUrl: "../../public/images/header/system-settings.png",
    imgUrlName: "system_settings"
  },
  {
    title: "数据字典管理",
    name: "dataDict",
    path: "/home/<USER>/dataDict",
    // imgUrl: "../../public/images/header/system-settings.png",
    imgUrlName: "system_settings"
  },
  {
    title: "地图可视化配置",
    name: "qbDistribution",
    path: "/home/<USER>/qbDistribution",
    // imgUrl: "../../public/images/header/system-settings.png",
    imgUrlName: "system_settings"
  },
    {
    title: "服务运行状态",
    name: "systemState",
    path: "/home/<USER>/systemState",
    // imgUrl: "../../public/images/header/system-settings.png",
    imgUrlName: "system_settings"
  },
];
/**
 * @description 页面加载完成
 */
onMounted(() => {
  nextTick(() => {});
});
//description
const routerTo = async (path) => {
  return;
};
/**
 * @description 组件销毁
 */
onUnmounted(() => {});
</script>
<style scoped lang="less">
@import "../../assets/style/theme/style.less"; 
.el-main{
  border: 1px solid rgba(@themeColor,.5);
}
.el-aside{
  width:300px;
  background-color: rgba(@themeColor,0.1);
  margin-right: 10px;
}
.el-menu{
  height: 100%;
  overflow: auto;
  border-right: solid 1px rgba(@themeColor,0.5);
}
.el-menu-item {
  padding: 10px 10px;
}
.img {
  width: 25px;
  height: 25px;
  padding: 5px;
  background-size: 100% 100%;
  box-sizing: border-box;

}
.current_time{
  background-image: @current_time;
}
.data_reduction{
  background-image: @data_reduction;
}
.play_back{
  background-image: @play_back;
}
.system_settings{
  background-image: @system_settings;
}
</style>
