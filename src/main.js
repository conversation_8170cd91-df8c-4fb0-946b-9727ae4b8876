/*
 * @Author: PXL
 * @Date: 2024-03-20 09:23:30
 * @LastEditors: JRX <EMAIL>
 * @LastEditTime: 2024-07-16 11:30:18
 * @Description: file content
 */
import { createApp } from "vue";
import { createPinia } from "pinia";

// import "./style.css";
import App from "./App.vue";
import router from "./router/index";
import ElementPlus from "element-plus";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import * as echarts from "echarts";
import "./style.less";
import * as mapTool from "@/utils/mapTool.js";
window.echarts = echarts;
window.mapTool = mapTool;
window.$fontColor = "#d9dfe8d9";
// window.chartColors = ["#1b5579", "#054d7a", "#64594a", "#1a5459", "#165a5b"];
window.chartColors = [
  // "#23C1E8B2",
  // "#D000B1B2",
  // "#F54D26B2",
  // "#5D5AE1B2",
  // "#2A2AAEB2",
  // "#2F74CCB2",
     "#1d7b83B2",
     "#7c623eB2",
     "#1b4080B2",
     "#0a5b8fB2",
     "#a5a453B2",
     "#bcaa96B2",
     "#73291fB2",
];
window.axisLine = {
  show: true,
  lineStyle: {
    color: "#6E707947",
    width: 1,
    type: "solid",
  },
};
window.splitLine = {
  show: false,
  lineStyle: {
    color: "#2b3b47",
    width: 1,
    type: "solid",
  },
};
window.timeSpan = (val) => {
  if (val) {
    return moment(val).format("YYYY-MM-DD HH:mm:ss")
    // echarts.format.formatTime("yyyy-MM-dd hh:mm:ss", val);
  } else {
    return "";
  }
};
import { ElMessage } from "element-plus";

let msgType = {
  error: "errorMessage",
  info: "infoMessage",
  success: "successMessage",
  warning: "warningMessage",
}
let msgTitle = {
  error: "错误提示",
  info: "消息提示",
  success: "成功提示",
  warning: "警告提示",
}
let messageBox = document.createElement("div")
document.body.appendChild(messageBox)

const msgFun = (type, msg) => {
  let div = document.createElement("div")
  div.className = `${msgType[type]} customMessage`
  div.innerHTML = `
  <div class="msg-iconBox">
    <div class="msg-icon msg-icon-icon"></div>
  </div>
  <div class="msg-text">
    <div class="msg-title">${msgTitle[type]}</div>
    <div class="msg-body">${msg}</div>
  </div>
`
  messageBox.appendChild(div)
  setTimeout(() => {
    div.remove()
  }, 3000);
}
// ElMessage = (type, msg) => {
//   msgFun(type, msg)
// }
ElMessage.error = (msg) => {
  msgFun("error", msg)
}
ElMessage.info = (msg) => {
  msgFun("info", msg)
}
ElMessage.success = (msg) => {
  msgFun("success", msg)
}
ElMessage.warning = (msg) => {
  msgFun("warning", msg)
}
// function dispatchEventStroage(){
//   const signSetItem = localStorage.setItem
//   localStorage.setItem = function(key,val){
//     debugger
//     let setEvent = new Event('setItemEvent')
//     setEvent.key = key
//     setEvent.newValue = val
//     window.dispatchEvent(setEvent)
//     signSetItem.apply(this,arguments)
//   }
// }
// dispatchEventStroage()
const app = createApp(App);
const pinia = createPinia();
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.use(pinia).use(ElementPlus, { locale: zhCn,zIndex:3000 }).use(router).mount("#app");
